let packageInfo = require("./package.json");
const config = {
  name: packageInfo.name,
  version: packageInfo.currentVersion,
  env: packageInfo.env,
  appUrl: "https://front.leyoujia.com/leyoujiaIm/electron_im/",
  fcw: "fcwhx",//房产网
  ai: "^ai[0-9]",// ai数字人
  jm: "^aa[0-9]",// 加盟
  subscribe: "subscribe",//订阅号
  serveNumber: "servenumber",//服务号
  systemMessage: "systemMessage",//系统通知
  helperAccount: "helperAccount",//群助手
  customerAccount: "customerAccount",//客户咨询
  imgBase64Reg: new RegExp("^data:image\\/\\w+;base64,"),
  imgTypeReg: new RegExp("png|jpg|jpeg|bmp|gif|heic", "i"),
  videoTypeReg: new RegExp("mp4", "i"),
  easePicUrlReg: new RegExp("https:\/\/nim.nosdn.127.net\/|https:\/\/nim-nosdn.netease.im\/"), // 云信图片url正则（用于替换图片地址）
  easePicUrlOtherReg: new RegExp("(https:\/\/nim.nosdn.127.net\/|https:\/\/nim-nosdn.netease.im\/).*\/"),
  aiFileReg: new RegExp("^(xlsx|docx|pdf|txt|json|md|markdown|csv)$"),// ai支持上传的文件
  teamAiFileReg: new RegExp("^(xlsx|docx|pdf)$"),// 群ai支持上传的文件
  jjsPicUrl1: "https://nim.leyoujia.com/", //公司内部备用图片地址方案1
  jjsPicUrl2: "https://hknim.leyoujia.com/", //公司内部备用图片地址方案2
  jjsPicUrl3: "https://xjpnim.leyoujia.com/", //公司内部备用图片地址方案3
  jjsPicUrlDefault: "https://nim.leyoujia.com/", //备用方案默认用方案1,其他地方判断如果是国外用户可以切换弄成线路2/线路3
  jjsPicUrlOther: "https://nim-his-storage.obs.cn-sz1.ctyun.cn/", //天翼云地址
  proxyInfo: packageInfo.proxyInfo, //代理信息
  settings: {
    type0: "session_shield_remind",//接收且提醒
    type1: "session_shield_no_remind",//接收不提醒
    type2: "session_helper_list",//群助手
    type3: "session_top_setting",//置顶
    type4: "msg_send_setting",//发送快捷键
    type5: "screenshot_tool_setting",//截图类型
    type6: "screenshot_key_setting",//截图快捷键
    type7: "msg_bubble_setting",//气泡设置
    type8: "shake_shield",//抖动设置
    type9: "session_accid_remark",//客户会话昵称设置
    type10: "video_type", //视频设置
    type11: "style_type", //风格设置
    type12: "window_size_type",//窗口大小和分组模式设置
    type13: "classify_type",//分组模式排序
    type14: "group_in_type",//客户讨论组静音开关
    type15: "session_hait_all_no_remind",//@全员消息不提醒
    type16: "classify_type_new",//分组模式排序（新）
    type17: "classify_tips",//分组模式显示拖拽提示
  },// 服务端配置信息
  shortcut: {
    jt1: "Alt+Shift+A",
    jt2: "Alt+Ctrl+A",
    lockIm: "Alt+L",
    toggleIm: "Alt+Shift+Z",
    devtools: "Alt+Ctrl+Shift+P+.",
    netCheck: "Ctrl+O"
  },
  teamNotifyTypes: ["teamInvite", "superTeamInvite", "applyTeam", "applySuperTeam",
    "rejectTeamInvite", "rejectSuperTeamInvite", "rejectTeamApply", "rejectSuperTeamApply"],// 触发获取本地数据的消息通知类型
  jjrDutyNumbers: ["3300143", "3300141", "3300142", "3300144", "7701125",
    "7701126", "7701127", "3300149", "3300150", "3300151",
    "7701128", "7701129", "7701130", "7700096", "3300143",
    "7700186", "7700184", "7700183", "7700190", "0100059",
    "0100073", "4000013", "5500095", "7700115", "7700088",
    "7700089", "7700095", "5500138", "3300141", "3300145",
    "3300146", "3300142", "3300147", "7700185", "3300148",
    "3300144", "7701121", "7701125", "7701126", "7701119",
    "7701122", "7701127", "7701124", "7700187", "7700139",
    "3300138", "3300139", "3300140", "3300149", "3300152",
    "3300153", "3300150", "7700188", "7700189", "3300151",
    "3300154", "7701123", "7701128", "7701129", "7701130",
    "7701120"].join(","),//用于创建讨论组时toast的数据
  gkfwDutyNumbers: ["3300137", "7700152", "7700153", "7700162", "7700163",
    "3300156", "7701137", "7701138", "7701139", "7701140",
    "7701141", "7701142"].join(","),//用于创建讨论组时toast的数据
  local: {
    robotEmpNo: "000385",// 小乐账号
    scheduleNumber: "093482",// 智能日程账号
    instructionNumber: "273131",// 指令账号
    msgCenterNumber: "servenumber000011",// 消息平台账号
    noTipNumber: "servenumber000011",// 消息不提醒账号
    nimAppKey: "8f3f8d1d4f6f3d08439853012ad439d4",
    jjsImApi: "http://127.0.0.1/im_api/aicpMainEncrypt",
    aicpUrl: "http://127.0.0.1/aicp/mainremote",
    jjsImHome: "http://127.0.0.1/im",
    jjsHome: "http://127.0.0.1",
    coaHome: "http://127.0.0.1",
    jjsResUrl: "https://images-tests.leyoujia.com",
    difyUrl: "https://itest-dify.leyoujia.com",
  },
  dev: {
    robotEmpNo: "000385",// 小乐账号
    scheduleNumber: "093482",// 智能日程账号
    instructionNumber: "273131",// 指令账号
    msgCenterNumber: "servenumber000011",// 消息平台账号
    noTipNumber: "servenumber000011",// 消息不提醒账号
    nimAppKey: "8f3f8d1d4f6f3d08439853012ad439d4",
    jjsImApi: "http://************/im_api/aicpMainEncrypt",
    aicpUrl: "http://************/aicp/mainremote",
    jjsImHome: "http://************/im",
    jjsHome: "http://************",
    coaHome: "http://************",
    jjsResUrl: "https://images-tests.leyoujia.com",
    difyUrl: "https://itest-dify.leyoujia.com",
    host: "************",
  },
  localtest: {
    robotEmpNo: "000385",// 小乐账号
    scheduleNumber: "093482",// 智能日程账号
    instructionNumber: "273131",// 指令账号
    msgCenterNumber: "servenumber000011",// 消息平台账号
    noTipNumber: "servenumber000011",// 消息不提醒账号
    nimAppKey: "8f3f8d1d4f6f3d08439853012ad439d4",
    jjsImApi: "https://itest.leyoujia.com/im_api/aicpMainEncrypt",
    aicpUrl: "https://itest.leyoujia.com/aicp/mainremote",
    jjsImHome: "https://itest.leyoujia.com/im",
    jjsHome: "https://itest.leyoujia.com",
    jjsResUrl: "https://images-tests.leyoujia.com",
    fcwUrl: "http://shenzhen.jjs.com",
    difyUrl: "https://itest-dify.leyoujia.com",
    host: "itest.leyoujia.com",
  },
  tyy: {
    robotEmpNo: "275365",// 小乐账号
    scheduleNumber: "093482",// 智能日程账号
    instructionNumber: "273131",// 指令账号
    msgCenterNumber: "servenumber000011",// 消息平台账号
    noTipNumber: "servenumber000011",// 消息不提醒账号
    nimAppKey: "8f3f8d1d4f6f3d08439853012ad439d4",
    jjsImApi: "https://tyyitest.leyoujia.com/im_api/aicpMainEncrypt",
    aicpUrl: "https://tyyitest.leyoujia.com/aicp/mainremote",
    jjsImHome: "https://tyyitest.leyoujia.com/im",
    jjsHome: "https://tyyitest.leyoujia.com",
    jjsResUrl: "https://images-tests.leyoujia.com",
    fcwUrl: "http://shenzhen.jjs.com",
    difyUrl: "https://itest-dify.leyoujia.com",
    host: "tyyitest.leyoujia.com",
  },
  onlinetest: {
    robotEmpNo: "275365",// 小乐账号
    scheduleNumber: "093482",// 智能日程账号
    instructionNumber: "273131",// 指令账号
    msgCenterNumber: "servenumber000011",// 消息平台账号
    noTipNumber: "servenumber000011",// 消息不提醒账号
    nimAppKey: "84501ef2c72807b426237cf043c6d383",
    jjsImApi: "https://onlinetest.leyoujia.com/im_api/aicpMainEncrypt",
    aicpUrl: "https://onlinetest.leyoujia.com/aicp/mainremote",
    jjsImHome: "https://onlinetest.leyoujia.com/im",
    jjsHome: "https://onlinetest.leyoujia.com",
    jjsResUrl: "https://imgcloud.leyoujia.com",
    fcwUrl: "http://testshenzhen.leyoujia.com",
    difyUrl: "https://itest-dify.leyoujia.com",
    host: "onlinetest.leyoujia.com",
  },
  online: {
    robotEmpNo: "275365",// 小乐账号
    scheduleNumber: "087347",// 智能日程账号
    msgCenterNumber: "servenumber000044",// 消息平台账号
    noTipNumber: "servenumber000045",// 消息不提醒账号
    instructionNumber: "instruction_account",// 指令账号
    nimAppKey: "638aae803525df4d733c7703e0c3323f",
    jjsImApi: "https://i.leyoujia.com/im_api/aicpMainEncrypt",
    aicpUrl: "https://i.leyoujia.com/aicp/mainremote",
    jjsImHome: "https://i.leyoujia.com/im",
    jjsHome: "https://i.leyoujia.com",
    jjsResUrl: "https://imgcloud.leyoujia.com",
    fcwUrl: "http://www.leyoujia.com",
    difyUrl: "https://lyj-ai.leyoujia.com",
    host: "i.leyoujia.com",
  }
}

module.exports = config;
