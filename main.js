const { app, BrowserWindow, ipcMain, shell } = require('electron');
const path = require('path');
const os = require('os');

// 保持对窗口对象的全局引用
let mainWindow = null;

// 设置 IPC 处理程序
function setupIpcHandlers(window) {
  // 应用退出
  ipcMain.on('app-quit', () => {
    app.quit();
  });

  // 打开外部链接
  ipcMain.on('shell-open-external', (event, url) => {
    shell.openExternal(url);
  });

  // 窗口操作
  ipcMain.on('window-show', () => {
    if (window) window.show();
  });

  ipcMain.on('window-hide', () => {
    if (window) window.hide();
  });

  ipcMain.on('window-close', () => {
    if (window) window.close();
  });

  ipcMain.on('window-minimize', () => {
    if (window) window.minimize();
  });

  ipcMain.on('window-maximize', () => {
    if (window) {
      if (window.isMaximized()) {
        window.unmaximize();
      } else {
        window.maximize();
      }
    }
  });

  ipcMain.on('window-set-title', (event, title) => {
    if (window) window.setTitle(title);
  });
}

// 检测是否为 Apple Silicon
const isAppleSilicon = process.platform === 'darwin' && process.arch === 'arm64';

function createWindow() {
  console.log('创建主窗口...');
  console.log(`运行平台: ${process.platform}, 架构: ${process.arch}`);
  if (isAppleSilicon) {
    console.log('检测到 Apple Silicon 设备');
  }

  mainWindow = new BrowserWindow({
    width: 455,
    height: 350,
    minWidth: 455,
    minHeight: 350,
    maxWidth: 455,
    maxHeight: 350,
    center: true,
    title: '乐聊',
    icon: path.join(__dirname, 'jjs_im_icon.png'),
    show: false,
    frame: false,
    transparent: false,
    backgroundColor: '#ffffff',
    webPreferences: {
      nodeIntegration: true,
      nodeIntegrationInWorker: true,
      contextIsolation: false,
      webSecurity: false,
      devTools: true,
      enableRemoteModule: true,
      webviewTag: true,
      allowRunningInsecureContent: true,
      preload: path.join(__dirname, 'preload.js'),
      // Apple Silicon 优化
      experimentalFeatures: true,
      // 启用硬件加速（对 Apple Silicon 很重要）
      offscreen: false,
      // 禁用沙盒模式以确保兼容性
      sandbox: false,
      // 允许不安全的内联脚本和 eval
      additionalArguments: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-site-isolation-trials',
        '--disable-features=BlockInsecurePrivateNetworkRequests'
      ]
    }
  });

  // 处理跨域请求
  mainWindow.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
    callback({
      requestHeaders: { 
        ...details.requestHeaders,
        'Access-Control-Allow-Origin': '*'
      }
    });
  });

  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };

    // 完全移除所有 CSP 头
    delete responseHeaders['content-security-policy'];
    delete responseHeaders['Content-Security-Policy'];
    delete responseHeaders['x-content-security-policy'];
    delete responseHeaders['X-Content-Security-Policy'];
    delete responseHeaders['x-webkit-csp'];
    delete responseHeaders['X-WebKit-CSP'];

    // 设置 CORS
    responseHeaders['Access-Control-Allow-Origin'] = ['*'];
    responseHeaders['Access-Control-Allow-Headers'] = ['*'];
    responseHeaders['Access-Control-Allow-Methods'] = ['*'];

    // 为 heic2any 模块添加特殊的 CSP 头
    responseHeaders['Content-Security-Policy'] = ['script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' blob: data:; worker-src \'self\' blob: data:; child-src \'self\' blob: data:;'];

    callback({ responseHeaders });
  });

  // 完全禁用 CSP 的额外措施
  mainWindow.webContents.session.webRequest.onBeforeRequest((details, callback) => {
    callback({});
  });

  // 在页面加载时注入脚本来移除 CSP
  mainWindow.webContents.on('dom-ready', () => {
    // 简化的脚本注入，避免复杂对象传递
    mainWindow.webContents.executeJavaScript(`
      try {
        // 移除所有 CSP meta 标签
        const cspMetas = document.querySelectorAll('meta[http-equiv*="Content-Security-Policy"]');
        cspMetas.forEach(meta => meta.remove());
        console.log('CSP meta 标签已移除');
      } catch (e) {
        console.log('移除 CSP meta 标签失败:', e);
      }
    `).catch(err => {
      console.log('注入 CSP 修复脚本失败:', err);
    });
  });

  // 加载应用
  const isDev = process.env.NODE_ENV === 'development' || process.env.WEBPACK_DEV_SERVER_URL || !app.isPackaged;
  console.log('环境检测:', { NODE_ENV: process.env.NODE_ENV, WEBPACK_DEV_SERVER_URL: process.env.WEBPACK_DEV_SERVER_URL, isPackaged: app.isPackaged, isDev });

  if (isDev) {
    console.log('正在加载开发环境 URL...');
    mainWindow.loadURL('http://localhost:8888').catch(err => {
      console.error('加载开发环境 URL 失败:', err);
      // 如果加载失败，尝试加载本地文件
      mainWindow.loadFile('dist/index.html').catch(err => {
        console.error('加载本地文件失败:', err);
      });
    });
    mainWindow.webContents.openDevTools();
  } else {
    console.log('正在加载生产环境文件...');
    mainWindow.loadFile('dist/index.html').catch(err => {
      console.error('加载生产环境文件失败:', err);
    });
  }

  // 设置 IPC 处理程序
  setupIpcHandlers(mainWindow);

  // 监听窗口准备就绪事件
  mainWindow.once('ready-to-show', () => {
    console.log('窗口准备就绪');
    mainWindow.show();
  });

  // 监听页面加载完成事件
  mainWindow.webContents.on('did-finish-load', () => {
    console.log('页面加载完成');
    // 发送消息到渲染进程
    mainWindow.webContents.send('main-process-message', '主进程已启动');
  });

  // 监听页面加载失败事件
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('页面加载失败:', errorCode, errorDescription);
  });

  // 监听渲染进程错误
  mainWindow.webContents.on('render-process-gone', (event, details) => {
    console.error('渲染进程崩溃:', details);
  });

  // 监听崩溃事件
  mainWindow.webContents.on('crashed', () => {
    console.error('渲染进程崩溃');
  });

  // 监听窗口关闭事件
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Apple Silicon 特定的应用配置
if (isAppleSilicon) {
  // 启用 GPU 加速
  app.commandLine.appendSwitch('enable-gpu-rasterization');
  app.commandLine.appendSwitch('enable-zero-copy');
  // 优化内存使用
  app.commandLine.appendSwitch('max_old_space_size', '4096');
}

// 当 Electron 完成初始化时创建窗口
app.whenReady().then(() => {
  console.log('应用程序准备就绪');
  console.log(`系统信息: ${os.type()} ${os.release()}`);
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// 当所有窗口关闭时退出应用
app.on('window-all-closed', () => {
  console.log('所有窗口已关闭');
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

// 处理未处理的 Promise 拒绝
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', promise, '原因:', reason);
});

// IPC 通信示例
ipcMain.on('renderer-message', (event, message) => {
  console.log('收到渲染进程消息:', message);
  event.reply('main-process-reply', '主进程已收到消息');
});

// 处理窗口控制事件
ipcMain.on('window-show', () => {
  if (mainWindow) mainWindow.show();
});

ipcMain.on('window-hide', () => {
  if (mainWindow) mainWindow.hide();
});

ipcMain.on('window-close', () => {
  if (mainWindow) mainWindow.close();
});

ipcMain.on('window-minimize', () => {
  if (mainWindow) mainWindow.minimize();
});

ipcMain.on('window-maximize', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.restore();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.on('window-restore', () => {
  if (mainWindow) mainWindow.restore();
});

ipcMain.on('window-focus', () => {
  if (mainWindow) mainWindow.focus();
});

ipcMain.on('window-blur', () => {
  if (mainWindow) mainWindow.blur();
});

ipcMain.on('window-set-title', (event, title) => {
  if (mainWindow) mainWindow.setTitle(title);
});

ipcMain.on('window-move-to', (event, { x, y }) => {
  if (mainWindow) mainWindow.setPosition(x, y);
});

ipcMain.on('window-resize-to', (event, { width, height }) => {
  if (mainWindow) mainWindow.setSize(width, height);
});

ipcMain.on('window-show-dev-tools', () => {
  if (mainWindow) mainWindow.webContents.openDevTools();
});

ipcMain.on('window-close-dev-tools', () => {
  if (mainWindow) mainWindow.webContents.closeDevTools();
});

ipcMain.on('window-set-always-on-top', (event, flag) => {
  if (mainWindow) mainWindow.setAlwaysOnTop(flag);
});

ipcMain.on('window-set-skip-taskbar', (event, skip) => {
  if (mainWindow) mainWindow.setSkipTaskbar(skip);
});

ipcMain.on('window-set-progress-bar', (event, progress) => {
  if (mainWindow) mainWindow.setProgressBar(progress);
});

ipcMain.on('window-flash-frame', (event, flag) => {
  if (mainWindow) mainWindow.flashFrame(flag);
});

// 处理 store 相关的 IPC 事件
ipcMain.on('store-commit', (event, { mutation, payload }) => {
  console.log(`主进程收到 store commit: ${mutation}`, payload);
  // 这里可以处理状态更新逻辑
});

ipcMain.on('store-dispatch', (event, { action, payload }) => {
  console.log(`主进程收到 store dispatch: ${action}`, payload);
  // 这里可以处理异步操作逻辑
  event.reply('store-dispatch-reply', { success: true, data: payload });
});