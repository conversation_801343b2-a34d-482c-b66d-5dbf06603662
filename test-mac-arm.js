#!/usr/bin/env node

/**
 * Mac ARM64 兼容性测试脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Mac ARM64 兼容性检查');
console.log('========================');

// 检查系统信息
console.log('\n📊 系统信息:');
console.log(`平台: ${process.platform}`);
console.log(`架构: ${process.arch}`);
console.log(`Node.js 版本: ${process.version}`);

// 检查关键文件
console.log('\n📁 关键文件检查:');
const criticalFiles = [
  'package.json',
  'main.js',
  'build/entitlements.mac.plist',
  'build-mac-arm.js'
];

criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} (缺失)`);
  }
});

// 检查 package.json 配置
console.log('\n⚙️  构建配置检查:');
try {
  const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // 检查 Electron 版本
  const electronVersion = pkg.devDependencies?.electron;
  console.log(`Electron 版本: ${electronVersion}`);
  
  if (electronVersion && parseFloat(electronVersion.replace(/[^\d.]/g, '')) >= 30) {
    console.log('✅ Electron 版本支持 Apple Silicon');
  } else {
    console.log('⚠️  建议升级 Electron 到 30.x 或更高版本');
  }
  
  // 检查构建配置
  const macConfig = pkg.build?.mac;
  if (macConfig) {
    console.log('✅ Mac 构建配置存在');
    
    const targets = macConfig.target;
    if (targets && targets.some(t => t.arch?.includes('arm64'))) {
      console.log('✅ ARM64 架构已配置');
    } else {
      console.log('❌ ARM64 架构未配置');
    }
    
    if (macConfig.hardenedRuntime) {
      console.log('✅ 强化运行时已启用');
    }
    
    if (macConfig.entitlements) {
      console.log('✅ 权限配置已设置');
    }
  } else {
    console.log('❌ Mac 构建配置缺失');
  }
  
  // 检查脚本
  const scripts = pkg.scripts;
  if (scripts) {
    const armScript = scripts['electron:build:arm64'];
    if (armScript) {
      console.log('✅ ARM64 构建脚本已配置');
    } else {
      console.log('❌ ARM64 构建脚本缺失');
    }
  }
  
} catch (error) {
  console.log('❌ package.json 解析失败:', error.message);
}

// 检查已存在的构建文件
console.log('\n📦 现有构建文件:');
if (fs.existsSync('release')) {
  const files = fs.readdirSync('release');
  const armFiles = files.filter(f => f.includes('arm64'));
  
  if (armFiles.length > 0) {
    console.log('✅ 找到 ARM64 构建文件:');
    armFiles.forEach(file => {
      const filePath = path.join('release', file);
      const stats = fs.statSync(filePath);
      console.log(`  📄 ${file} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    });
  } else {
    console.log('ℹ️  未找到 ARM64 构建文件（需要先构建）');
  }
} else {
  console.log('ℹ️  release 目录不存在（需要先构建）');
}

// 提供建议
console.log('\n💡 建议:');
console.log('1. 运行 `npm run electron:build:arm64` 构建 ARM64 版本');
console.log('2. 或运行 `node build-mac-arm.js` 使用专用构建脚本');
console.log('3. 在 Mac ARM 设备上测试安装和运行');
console.log('4. 查看 README-MAC-ARM.md 了解详细说明');

console.log('\n🎉 检查完成！');
