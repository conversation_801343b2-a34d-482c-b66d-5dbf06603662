#!/usr/bin/env node

/**
 * Mac ARM64 专用构建脚本
 * 针对 Apple Silicon 设备进行优化
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始构建 Mac ARM64 版本...');

// 检查当前平台
console.log(`当前平台: ${process.platform}`);
console.log(`当前架构: ${process.arch}`);

// 清理之前的构建
console.log('🧹 清理之前的构建文件...');
try {
  if (fs.existsSync('dist')) {
    execSync('rm -rf dist', { stdio: 'inherit' });
  }
  if (fs.existsSync('release/mac-arm64')) {
    execSync('rm -rf release/mac-arm64', { stdio: 'inherit' });
  }
} catch (error) {
  console.warn('清理文件时出现警告:', error.message);
}

// 构建前端代码
console.log('📦 构建前端代码...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ 前端代码构建完成');
} catch (error) {
  console.error('❌ 前端代码构建失败:', error.message);
  process.exit(1);
}

// 构建 Electron 应用 (ARM64)
console.log('🔨 构建 Mac ARM64 应用...');
try {
  execSync('npm run electron:build:arm64', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      // 确保使用 ARM64 架构
      npm_config_target_arch: 'arm64',
      npm_config_target_platform: 'darwin',
      // 启用 Apple Silicon 优化
      ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES: 'true'
    }
  });
  console.log('✅ Mac ARM64 应用构建完成');
} catch (error) {
  console.error('❌ Mac ARM64 应用构建失败:', error.message);
  process.exit(1);
}

// 检查构建结果
console.log('🔍 检查构建结果...');
const releaseDir = 'release';
if (fs.existsSync(releaseDir)) {
  const files = fs.readdirSync(releaseDir);
  const arm64Files = files.filter(file => file.includes('arm64'));
  
  if (arm64Files.length > 0) {
    console.log('✅ 找到 ARM64 构建文件:');
    arm64Files.forEach(file => {
      const filePath = path.join(releaseDir, file);
      const stats = fs.statSync(filePath);
      console.log(`  📄 ${file} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
    });
  } else {
    console.warn('⚠️  未找到 ARM64 构建文件');
  }
} else {
  console.error('❌ 构建目录不存在');
}

console.log('🎉 Mac ARM64 构建完成！');
console.log('');
console.log('📋 使用说明:');
console.log('1. 在 Mac ARM 设备上安装生成的 .dmg 文件');
console.log('2. 如果遇到安全提示，请在系统偏好设置 > 安全性与隐私中允许应用运行');
console.log('3. 首次运行可能需要在终端中执行: xattr -cr /Applications/乐聊.app');
