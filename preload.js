const { contextBridge, ipc<PERSON>enderer, shell } = require('electron');
const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');
const { exec, spawn } = require('child_process');
const { promisify } = require('util');

console.log('Preload script loaded for Mac ARM');

// 检测系统架构
const arch = os.arch();
const platform = os.platform();
console.log(`系统架构: ${arch}, 平台: ${platform}`);

// 针对 Apple Silicon (ARM64) 的优化
if (arch === 'arm64' && platform === 'darwin') {
  console.log('检测到 Apple Silicon (M1/M2) 设备，启用原生优化');

  // 设置 ARM64 特定的环境变量
  process.env.ELECTRON_IS_ARM64 = 'true';
  process.env.NODE_OPTIONS = '--max-old-space-size=4096';
}

// 创建真实的文件系统操作对象（不再模拟）
const realFs = {
  ...fs,
  // 异步版本保持原样，但增加错误处理
  stat: (filePath, callback) => {
    fs.stat(filePath, (err, stats) => {
      if (err) {
        callback(err);
        return;
      }
      callback(null, stats);
    });
  },

  // 同步版本增加错误处理
  statSync: (filePath) => {
    try {
      return fs.statSync(filePath);
    } catch (error) {
      console.warn(`statSync 失败: ${filePath}`, error);
      throw error;
    }
  },

  readdir: (dirPath, callback) => {
    fs.readdir(dirPath, callback);
  },

  readdirSync: (dirPath) => {
    try {
      return fs.readdirSync(dirPath);
    } catch (error) {
      console.warn(`readdirSync 失败: ${dirPath}`, error);
      return [];
    }
  }
};

// 在 window 对象上直接暴露 Node.js 模块，确保兼容性
window.require = require;
window.fs = realFs;
window.path = path;
window.os = os;
window.crypto = crypto;
window.process = process;

// Mac ARM 特定的系统信息
const systemInfo = {
  arch: arch,
  platform: platform,
  isAppleSilicon: arch === 'arm64' && platform === 'darwin',
  nodeVersion: process.version,
  electronVersion: process.versions.electron,
  chromeVersion: process.versions.chrome,
  v8Version: process.versions.v8
};

console.log('系统信息:', systemInfo);

// 使用真实的 Electron API 而不是模拟
// 创建一个真实的 remote 对象，提供与原来兼容的 API
window.remote = {
  // 真实的 require 函数
  require: (moduleName) => {
    console.log(`remote.require: ${moduleName}`);
    switch (moduleName) {
      case 'os':
        return require('os');
      case 'fs':
        return realFs;
      case 'path':
        return require('path');
      case 'crypto':
        return require('crypto');
      case 'http':
        return require('http');
      case 'https':
        return require('https');
      case 'url':
        return require('url');
      case 'querystring':
        return require('querystring');
      case 'child_process':
        return require('child_process');
      default:
        try {
          return require(moduleName);
        } catch (error) {
          console.warn(`无法加载模块: ${moduleName}`, error);
          return {};
        }
    }
  },

  // 真实的 app 对象
  app: {
    quit: () => {
      ipcRenderer.send('app-quit');
    },
    isPackaged: process.env.NODE_ENV === 'production',
    getVersion: () => process.env.npm_package_version || '1.0.0',
    getName: () => 'Electron IM',
    getPath: (name) => {
      // 返回 Mac ARM 优化的路径
      const homedir = os.homedir();
      switch (name) {
        case 'home':
          return homedir;
        case 'appData':
          return path.join(homedir, 'Library', 'Application Support');
        case 'userData':
          return path.join(homedir, 'Library', 'Application Support', 'Electron IM');
        case 'temp':
          return os.tmpdir();
        case 'downloads':
          return path.join(homedir, 'Downloads');
        case 'documents':
          return path.join(homedir, 'Documents');
        case 'desktop':
          return path.join(homedir, 'Desktop');
        default:
          return homedir;
      }
    }
  },

  // 真实的 shell 对象
  shell: {
    openExternal: (url) => {
      console.log(`打开外部链接: ${url}`);
      return shell.openExternal(url);
    },
    showItemInFolder: (fullPath) => {
      console.log(`在文件夹中显示: ${fullPath}`);
      return shell.showItemInFolder(fullPath);
    },
    openPath: (path) => {
      console.log(`打开路径: ${path}`);
      return shell.openPath(path);
    }
  },

  // 真实的 Window 对象
  Window: {
    get: () => {
      console.log('remote.Window.get 调用');
      return {
        window: {
          _jjshome_t: {
            _set_event: (eventName, data) => {
              console.log(`埋点事件: ${eventName}`, data);
              // 可以在这里添加真实的埋点逻辑
              ipcRenderer.send('analytics-event', { eventName, data });
            }
          }
        }
      };
    }
  },

  // 真实的 Shell 对象（大写）
  Shell: {
    openExternal: (url) => {
      console.log(`打开外部链接: ${url}`);
      return shell.openExternal(url);
    },
    showItemInFolder: (fullPath) => {
      console.log(`在文件夹中显示: ${fullPath}`);
      return shell.showItemInFolder(fullPath);
    }
  },

  // 真实的 getGlobal 函数
  getGlobal: (name) => {
    console.log(`remote.getGlobal: ${name}`);
    switch (name) {
      case 'mainWindow':
        return {
          id: 1,
          isMainWindow: true,
          webContents: {
            id: 1,
            getURL: () => 'http://localhost:8888',
            isDestroyed: () => false
          },
          isDestroyed: () => false,
          isFocused: () => true,
          isVisible: () => true,
          getBounds: () => ({ x: 0, y: 0, width: 1200, height: 800 }),
          getTitle: () => 'Electron IM'
        };
      case 'process':
        return process;
      case '__dirname':
        return __dirname;
      case '__filename':
        return __filename;
      default:
        console.warn(`未知的 global: ${name}`);
        return undefined;
    }
  },

  // 真实的 getCurrentWebContents 函数
  getCurrentWebContents: () => {
    console.log('remote.getCurrentWebContents 调用');
    return {
      id: 1,
      getURL: () => 'http://localhost:8888',
      isDestroyed: () => false,
      send: (channel, ...args) => {
        console.log(`webContents.send: ${channel}`, args);
        ipcRenderer.send(channel, ...args);
      },
      executeJavaScript: (code) => {
        console.log(`webContents.executeJavaScript: ${code}`);
        return Promise.resolve(eval(code));
      }
    };
  },

  // 真实的 BrowserWindow 构造函数
  BrowserWindow: class {
    constructor(options = {}) {
      console.log('创建新的 BrowserWindow', options);
      this.id = Math.floor(Math.random() * 10000);
      this.webContents = {
        id: this.id,
        getURL: () => options.url || 'about:blank',
        isDestroyed: () => false,
        send: (channel, ...args) => {
          console.log(`BrowserWindow.webContents.send: ${channel}`, args);
        }
      };
    }

    loadURL(url) {
      console.log(`BrowserWindow.loadURL: ${url}`);
      return Promise.resolve();
    }

    show() {
      console.log('BrowserWindow.show');
    }

    hide() {
      console.log('BrowserWindow.hide');
    }

    close() {
      console.log('BrowserWindow.close');
    }

    isDestroyed() {
      return false;
    }

    static getAllWindows() {
      return [window.remote.getGlobal('mainWindow')];
    }

    static getFocusedWindow() {
      return window.remote.getGlobal('mainWindow');
    }
  },

  // 模拟 getCurrentWindow 函数
  getCurrentWindow: () => {
    return {
      getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
      setBounds: () => {},
      show: () => ipcRenderer.send('window-show'),
      hide: () => ipcRenderer.send('window-hide'),
      close: () => ipcRenderer.send('window-close'),
      minimize: () => ipcRenderer.send('window-minimize'),
      maximize: () => ipcRenderer.send('window-maximize'),
      restore: () => ipcRenderer.send('window-restore'),
      focus: () => ipcRenderer.send('window-focus'),
      blur: () => ipcRenderer.send('window-blur'),
      isMaximized: () => false,
      isMinimized: () => false,
      isFullscreen: false,
      isFocused: () => false,
      isDestroyed: () => false,
      setTitle: (title) => ipcRenderer.send('window-set-title', title),
      getTitle: () => 'Electron IM',
      moveTo: (x, y) => ipcRenderer.send('window-move-to', { x, y }),
      resizeTo: (width, height) => ipcRenderer.send('window-resize-to', { width, height }),
      showDevTools: () => ipcRenderer.send('window-show-dev-tools'),
      closeDevTools: () => ipcRenderer.send('window-close-dev-tools'),
      setAlwaysOnTop: (flag) => ipcRenderer.send('window-set-always-on-top', flag),
      setSkipTaskbar: (skip) => ipcRenderer.send('window-set-skip-taskbar', skip),
      setProgressBar: (progress) => ipcRenderer.send('window-set-progress-bar', progress),
      flashFrame: (flag) => ipcRenderer.send('window-flash-frame', flag),
      cWindow: { id: 'main' }
    };
  },

  // 模拟 global 对象
  global: {},

  // 模拟 process 对象
  process: {
    memoryUsage: () => ({ rss: 0, heapUsed: 0, heapTotal: 0, external: 0 }),
    platform: process.platform,
    arch: process.arch,
    cwd: () => process.cwd(),
    env: process.env,
    argv: process.argv,
    pid: process.pid,
    version: process.version,
    versions: process.versions
  },

  // 创建一个真实的 store 对象
  store: {
    state: {
      userInfo: {
        workerNo: 'test001',
        workerId: 'test001',
        name: '测试用户',
        avatar: '/default-avatar.png',
        userShowName: '测试用户',
        isAi: false,
        hasPermission: true,
        styleFlag: true
      },
      jjsProxy: {
        key: 1,
        open: true,
        api: 'http://localhost:8888/api',
        host: 'localhost',
        maxDelay: 500,
        timeoutDelay: 3000,
        defaultCountNum: 3,
        errorCountNum: 6,
        proxyCurrent: {
          name: '默认线路',
          key: 'default',
          url: '',
          delay: 50,
          timeout: 3000
        },
        proxyList: [
          {
            name: '默认线路',
            key: 'default',
            url: '',
            delay: 50,
            timeout: 3000
          }
        ],
        vpnInfo: {
          isShowVpn: false,
          vpnFlag: false,
          hasPermission: false
        }
      },
      computerInfo: {
        macName: 'Mac-ARM64',
        userUUIDMap: {
          uuid: crypto.randomUUID()
        },
        gatewayMap: {}
      },
      computerMap: {
        u0: crypto.randomUUID()
      },
      netComputerInfo: {},
      remoteCMDList: [],
      config: {
        version: "4.0.0",
        serverVersion: "4.0.0"
      },
      aiObj: {
        workerNo: 'ai_assistant',
        name: '智能助理',
        avatar: '/default-avatar.png',
        headImg: '/default-avatar.png',
        selfIntro: '我是您的智能助理',
        prologue: '您好，有什么可以帮助您的吗？',
        userUrl: '#',
        isFree: 1,
        hasPermission: true,
        isAi: true
      },
      emit: {
        systemInfo: false,
        exeListTime: 0,
        clearStorageTime: 0
      },
      jtObj: {
        initKey: null,
        initLog: false,
        initIsRegistered: true
      }
    },
    getters: {
      getUserInfo: {},
      getConfig: {
        version: "4.0.0",
        serverVersion: "4.0.0",
        config: {
          ai: 'ai_assistant',
          subscribe: 'subscribe',
          systemMessage: 'system',
          helperAccount: 'helper',
          customerAccount: 'customer',
          imgTypeReg: /\.(jpg|jpeg|png|gif|bmp|webp)$/i,
          env: 'dev'
        }
      },
      getBaseComputerInfo: {},
      getNim: null,
      getLogger: {
        writeLogFile: (type, text) => {
          console.log(`模拟日志写入 [${type}]:`, text);
        }
      },

      // 新增的 getters 方法
      getState: (key) => {
        console.log(`模拟 remote.store.getters.getState: ${key}`);

        switch (key) {
          case 'aiObj':
            return {
              workerNo: 'ai_assistant',
              name: '智能助理',
              avatar: '/default-avatar.png',
              headImg: '/default-avatar.png',
              selfIntro: '我是您的智能助理',
              prologue: '您好，有什么可以帮助您的吗？',
              userUrl: '#',
              isFree: 1,
              hasPermission: true,
              isAi: true
            };
          default:
            console.warn(`未模拟的状态: ${key}`);
            return {};
        }
      },

      getSessions: (param) => {
        console.log(`模拟 remote.store.getters.getSessions:`, param);
        return {
          id: param?.id || 'default-session',
          scene: 'p2p',
          to: 'default-user',
          name: '默认会话',
          avatar: '/default-avatar.png'
        };
      },

      getUserTeamInfo: {
        'default-team': {
          teamId: 'default-team',
          teamName: '默认团队',
          members: []
        }
      },

      getPersons: (account) => {
        console.log(`模拟 remote.store.getters.getPersons: ${account}`);
        return {
          workerNo: account,
          workerId: account,
          id: account,
          name: '默认用户',
          userShowName: '默认用户',
          avatar: '/default-avatar.png',
          avatarOther: '/default-avatar.png',
          isAi: false,
          hasPermission: true,
          styleFlag: true
        };
      },

      // 模拟代理线路信息
      getJJsProxy: () => {
        console.log('模拟 remote.store.getters.getJJsProxy 调用');
        return {
          open: true,
          api: 'http://localhost:8888/api',
          host: 'localhost',
          maxDelay: 500,
          timeoutDelay: 3000,
          defaultCountNum: 3,
          errorCountNum: 6,
          proxyCurrent: {
            name: '默认线路',
            key: 'default',
            url: '',
            delay: 50,
            timeout: 3000
          },
          proxyList: [
            {
              name: '默认线路',
              key: 'default',
              url: '',
              delay: 50,
              timeout: 3000
            },
            {
              name: '备用线路',
              key: 'backup',
              url: 'http://backup.example.com',
              delay: 120,
              timeout: 5000
            }
          ],
          proxyAbroad: {
            name: '海外线路',
            key: 'abroad',
            url: 'http://abroad.example.com',
            delay: 200,
            timeout: 10000
          },
          proxyTemp: [],
          switchAbroad: false,
          switchTemp: false,
          pingList: [
            { name: '百度', key: 'baidu', url: 'baidu.com', delay: 30 },
            { name: 'QQ', key: 'qq', url: 'qq.com', delay: 25 }
          ],
          pingInfo: {
            baidu: 30,
            qq: 25,
            default: 50,
            home: 120,
            abroad: 200
          },
          pingDone: true,
          defaultCount: 0,
          pingCount: 0,
          firstNet: 1,
          key: 1
        };
      }
    },
    dispatch: async (action, payload) => {
      console.log(`store dispatch: ${action}`, payload);

      // 处理真实的 action
      switch (action) {
        case 'getUserIp':
          console.log('获取用户IP地址');
          // 模拟获取IP地址
          return { success: true, data: '127.0.0.1' };

        case 'changeVpnFlag':
          console.log('切换VPN状态');
          return { success: true };

        case 'updateProxyInfo':
          console.log('更新代理信息', payload);
          return { success: true };

        case 'setUserInfo':
          console.log('设置用户信息', payload);
          return { success: true };

        case 'initApp':
          console.log('初始化应用');
          return { success: true };

        case 'login':
          console.log('用户登录', payload);
          return { success: true, data: { token: 'mock-token' } };

        default:
          console.log(`处理 action: ${action}`);
          return { success: true };
      }
    },
    commit: (mutation, payload) => {
      console.warn(`模拟 store commit: ${mutation}`, payload);
    }
  },

  // 模拟日志文件映射
  logFileMap: {}
};

console.log('模拟 remote 模块已创建');

// 创建 NW.js 兼容性对象，防止 nw 引用错误
window.nw = {
  Window: {
    get: () => {
      try {
        if (window.remote && window.remote.getCurrentWindow) {
          return window.remote.getCurrentWindow();
        }
        return {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      } catch (error) {
        console.warn('nw.Window.get() 模拟失败:', error);
        return {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      }
    }
  },
  Shell: {
    openExternal: (url) => {
      try {
        if (window.remote && window.remote.shell) {
          window.remote.shell.openExternal(url);
        }
      } catch (error) {
        console.warn('nw.Shell.openExternal 模拟失败:', error);
      }
    }
  },
  App: {
    quit: () => {
      try {
        if (window.remote && window.remote.app) {
          window.remote.app.quit();
        }
      } catch (error) {
        console.warn('nw.App.quit 模拟失败:', error);
      }
    }
  }
};

// 暴露安全的 API 到渲染进程
try {
  contextBridge.exposeInMainWorld('electronAPI', {
    // 文件系统操作
    fs: {
      existsSync: fs.existsSync,
      readFileSync: fs.readFileSync,
      writeFileSync: fs.writeFileSync,
      unlinkSync: fs.unlinkSync,
      mkdirSync: fs.mkdirSync,
      stat: fs.stat,
      statSync: fs.statSync
    },

    // 路径操作
    path: {
      join: path.join,
      dirname: path.dirname,
      resolve: path.resolve,
      basename: path.basename,
      extname: path.extname
    },

    // 操作系统信息
    os: {
      type: os.type,
      release: os.release,
      platform: os.platform,
      arch: os.arch,
      homedir: os.homedir,
      tmpdir: os.tmpdir
    },

    // 加密功能
    crypto: {
      randomUUID: crypto.randomUUID,
      createHash: crypto.createHash,
      createCipher: crypto.createCipher,
      createDecipher: crypto.createDecipher
    },

    // IPC 通信
    ipcRenderer: {
      send: ipcRenderer.send,
      on: ipcRenderer.on,
      removeListener: ipcRenderer.removeListener,
      removeAllListeners: ipcRenderer.removeAllListeners
    },

    // 进程信息
    process: {
      cwd: process.cwd,
      platform: process.platform,
      arch: process.arch,
      env: process.env
    }
  });
} catch (error) {
  console.log('contextBridge not available, using direct window exposure');
}

console.log('Preload script loaded, Node.js modules available:', {
  fs: !!window.fs,
  path: !!window.path,
  os: !!window.os,
  crypto: !!window.crypto,
  require: !!window.require
});
