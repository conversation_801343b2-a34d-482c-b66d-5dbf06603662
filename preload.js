const { contextBridge, ipc<PERSON>enderer } = require('electron');
const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

// 在 window 对象上直接暴露 Node.js 模块，确保兼容性
window.require = require;
window.fs = fs;
window.path = path;
window.os = os;
window.crypto = crypto;
window.process = process;

// 使用 IPC 而不是 remote 模块
// ipcRenderer 已经在第1行声明了

// 创建一个模拟的 remote 对象，提供与原来兼容的 API
window.remote = {
  // 模拟 require 函数
  require: (moduleName) => {
    console.warn(`模拟 remote.require: ${moduleName}`);
    switch (moduleName) {
      case 'os':
        return require('os');
      case 'fs':
        return require('fs');
      case 'path':
        return require('path');
      case 'crypto':
        return require('crypto');
      default:
        return {};
    }
  },

  // 模拟 app 对象
  app: {
    quit: () => {
      ipcRenderer.send('app-quit');
    },
    isPackaged: false
  },

  // 模拟 shell 对象
  shell: {
    openExternal: (url) => {
      ipcRenderer.send('shell-open-external', url);
    }
  },

  // 模拟 getCurrentWindow 函数
  getCurrentWindow: () => {
    return {
      getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
      setBounds: () => {},
      show: () => ipcRenderer.send('window-show'),
      hide: () => ipcRenderer.send('window-hide'),
      close: () => ipcRenderer.send('window-close'),
      minimize: () => ipcRenderer.send('window-minimize'),
      maximize: () => ipcRenderer.send('window-maximize'),
      isMaximized: () => false,
      setTitle: (title) => ipcRenderer.send('window-set-title', title)
    };
  },

  // 模拟 global 对象
  global: {},

  // 模拟 process 对象
  process: {
    memoryUsage: () => ({ rss: 0, heapUsed: 0, heapTotal: 0, external: 0 }),
    platform: process.platform,
    arch: process.arch,
    cwd: () => process.cwd(),
    env: process.env,
    argv: process.argv,
    pid: process.pid,
    version: process.version,
    versions: process.versions
  },

  // 创建一个模拟的 store 对象
  store: {
    state: {
      computerInfo: {},
      netComputerInfo: {},
      remoteCMDList: [],
      config: {
        version: "4.0.0",
        serverVersion: "4.0.0"
      }
    },
    getters: {
      getUserInfo: {},
      getConfig: {
        version: "4.0.0",
        serverVersion: "4.0.0"
      },
      getBaseComputerInfo: {},
      getNim: null,
      getLogger: {
        writeLogFile: (type, text) => {
          console.log(`模拟日志写入 [${type}]:`, text);
        }
      }
    },
    dispatch: async (action, payload) => {
      console.warn(`模拟 store dispatch: ${action}`, payload);
      return { success: false, errorMsg: "Store 不可用", errorCode: 9999 };
    },
    commit: (mutation, payload) => {
      console.warn(`模拟 store commit: ${mutation}`, payload);
    }
  },

  // 模拟日志文件映射
  logFileMap: {}
};

console.log('模拟 remote 模块已创建');

// 创建 NW.js 兼容性对象，防止 nw 引用错误
window.nw = {
  Window: {
    get: () => {
      try {
        if (window.remote && window.remote.getCurrentWindow) {
          return window.remote.getCurrentWindow();
        }
        return {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      } catch (error) {
        console.warn('nw.Window.get() 模拟失败:', error);
        return {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      }
    }
  },
  Shell: {
    openExternal: (url) => {
      try {
        if (window.remote && window.remote.shell) {
          window.remote.shell.openExternal(url);
        }
      } catch (error) {
        console.warn('nw.Shell.openExternal 模拟失败:', error);
      }
    }
  },
  App: {
    quit: () => {
      try {
        if (window.remote && window.remote.app) {
          window.remote.app.quit();
        }
      } catch (error) {
        console.warn('nw.App.quit 模拟失败:', error);
      }
    }
  }
};

// 暴露安全的 API 到渲染进程
try {
  contextBridge.exposeInMainWorld('electronAPI', {
    // 文件系统操作
    fs: {
      existsSync: fs.existsSync,
      readFileSync: fs.readFileSync,
      writeFileSync: fs.writeFileSync,
      unlinkSync: fs.unlinkSync,
      mkdirSync: fs.mkdirSync,
      stat: fs.stat,
      statSync: fs.statSync
    },

    // 路径操作
    path: {
      join: path.join,
      dirname: path.dirname,
      resolve: path.resolve,
      basename: path.basename,
      extname: path.extname
    },

    // 操作系统信息
    os: {
      type: os.type,
      release: os.release,
      platform: os.platform,
      arch: os.arch,
      homedir: os.homedir,
      tmpdir: os.tmpdir
    },

    // 加密功能
    crypto: {
      randomUUID: crypto.randomUUID,
      createHash: crypto.createHash,
      createCipher: crypto.createCipher,
      createDecipher: crypto.createDecipher
    },

    // IPC 通信
    ipcRenderer: {
      send: ipcRenderer.send,
      on: ipcRenderer.on,
      removeListener: ipcRenderer.removeListener,
      removeAllListeners: ipcRenderer.removeAllListeners
    },

    // 进程信息
    process: {
      cwd: process.cwd,
      platform: process.platform,
      arch: process.arch,
      env: process.env
    }
  });
} catch (error) {
  console.log('contextBridge not available, using direct window exposure');
}

console.log('Preload script loaded, Node.js modules available:', {
  fs: !!window.fs,
  path: !!window.path,
  os: !!window.os,
  crypto: !!window.crypto,
  require: !!window.require
});
