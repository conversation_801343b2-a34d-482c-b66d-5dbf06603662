const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const fs = require('fs');
const path = require('path');
const os = require('os');
const crypto = require('crypto');

// 创建增强的 fs 模拟对象
const enhancedFs = {
  ...fs,
  stat: (filePath, callback) => {
    try {
      const stats = fs.statSync(filePath);
      // 确保 stats 对象有 isDirectory 方法
      if (!stats.isDirectory) {
        stats.isDirectory = () => false;
      }
      if (!stats.isFile) {
        stats.isFile = () => true;
      }
      callback(null, stats);
    } catch (error) {
      callback(error);
    }
  },
  statSync: (filePath) => {
    try {
      const stats = fs.statSync(filePath);
      // 确保 stats 对象有 isDirectory 方法
      if (!stats.isDirectory) {
        stats.isDirectory = () => false;
      }
      if (!stats.isFile) {
        stats.isFile = () => true;
      }
      return stats;
    } catch (error) {
      // 返回一个模拟的 stats 对象
      return {
        isDirectory: () => false,
        isFile: () => true,
        size: 0,
        mtime: new Date(),
        ctime: new Date(),
        atime: new Date()
      };
    }
  },
  readdir: (dirPath, callback) => {
    try {
      fs.readdir(dirPath, callback);
    } catch (error) {
      callback(error);
    }
  },
  readdirSync: (dirPath) => {
    try {
      return fs.readdirSync(dirPath);
    } catch (error) {
      console.warn(`readdirSync 失败: ${dirPath}`, error);
      return [];
    }
  }
};

// 在 window 对象上直接暴露 Node.js 模块，确保兼容性
window.require = require;
window.fs = enhancedFs;
window.path = path;
window.os = os;
window.crypto = crypto;
window.process = process;

// 使用 IPC 而不是 remote 模块
// ipcRenderer 已经在第1行声明了

// 创建一个模拟的 remote 对象，提供与原来兼容的 API
window.remote = {
  // 模拟 require 函数
  require: (moduleName) => {
    console.warn(`模拟 remote.require: ${moduleName}`);
    switch (moduleName) {
      case 'os':
        return require('os');
      case 'fs':
        return enhancedFs;
      case 'path':
        return require('path');
      case 'crypto':
        return require('crypto');
      case 'http':
        return {
          request: () => ({
            on: () => {},
            write: () => {},
            end: () => {}
          })
        };
      default:
        return {};
    }
  },

  // 模拟 app 对象
  app: {
    quit: () => {
      ipcRenderer.send('app-quit');
    },
    isPackaged: false
  },

  // 模拟 shell 对象
  shell: {
    openExternal: (url) => {
      ipcRenderer.send('shell-open-external', url);
    }
  },

  // 模拟 getCurrentWindow 函数
  getCurrentWindow: () => {
    return {
      getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
      setBounds: () => {},
      show: () => ipcRenderer.send('window-show'),
      hide: () => ipcRenderer.send('window-hide'),
      close: () => ipcRenderer.send('window-close'),
      minimize: () => ipcRenderer.send('window-minimize'),
      maximize: () => ipcRenderer.send('window-maximize'),
      restore: () => ipcRenderer.send('window-restore'),
      focus: () => ipcRenderer.send('window-focus'),
      blur: () => ipcRenderer.send('window-blur'),
      isMaximized: () => false,
      isMinimized: () => false,
      isFullscreen: false,
      isFocused: () => false,
      isDestroyed: () => false,
      setTitle: (title) => ipcRenderer.send('window-set-title', title),
      getTitle: () => 'Electron IM',
      moveTo: (x, y) => ipcRenderer.send('window-move-to', { x, y }),
      resizeTo: (width, height) => ipcRenderer.send('window-resize-to', { width, height }),
      showDevTools: () => ipcRenderer.send('window-show-dev-tools'),
      closeDevTools: () => ipcRenderer.send('window-close-dev-tools'),
      setAlwaysOnTop: (flag) => ipcRenderer.send('window-set-always-on-top', flag),
      setSkipTaskbar: (skip) => ipcRenderer.send('window-set-skip-taskbar', skip),
      setProgressBar: (progress) => ipcRenderer.send('window-set-progress-bar', progress),
      flashFrame: (flag) => ipcRenderer.send('window-flash-frame', flag),
      cWindow: { id: 'main' }
    };
  },

  // 模拟 global 对象
  global: {},

  // 模拟 process 对象
  process: {
    memoryUsage: () => ({ rss: 0, heapUsed: 0, heapTotal: 0, external: 0 }),
    platform: process.platform,
    arch: process.arch,
    cwd: () => process.cwd(),
    env: process.env,
    argv: process.argv,
    pid: process.pid,
    version: process.version,
    versions: process.versions
  },

  // 创建一个模拟的 store 对象
  store: {
    state: {
      computerInfo: {},
      netComputerInfo: {},
      remoteCMDList: [],
      config: {
        version: "4.0.0",
        serverVersion: "4.0.0"
      },
      aiObj: {
        workerNo: 'ai_assistant',
        name: '智能助理',
        avatar: '/default-avatar.png',
        headImg: '/default-avatar.png',
        selfIntro: '我是您的智能助理',
        prologue: '您好，有什么可以帮助您的吗？',
        userUrl: '#',
        isFree: 1,
        hasPermission: true,
        isAi: true
      }
    },
    getters: {
      getUserInfo: {},
      getConfig: {
        version: "4.0.0",
        serverVersion: "4.0.0",
        config: {
          ai: 'ai_assistant',
          subscribe: 'subscribe',
          systemMessage: 'system',
          helperAccount: 'helper',
          customerAccount: 'customer',
          imgTypeReg: /\.(jpg|jpeg|png|gif|bmp|webp)$/i,
          env: 'dev'
        }
      },
      getBaseComputerInfo: {},
      getNim: null,
      getLogger: {
        writeLogFile: (type, text) => {
          console.log(`模拟日志写入 [${type}]:`, text);
        }
      },

      // 新增的 getters 方法
      getState: (key) => {
        console.log(`模拟 remote.store.getters.getState: ${key}`);

        switch (key) {
          case 'aiObj':
            return {
              workerNo: 'ai_assistant',
              name: '智能助理',
              avatar: '/default-avatar.png',
              headImg: '/default-avatar.png',
              selfIntro: '我是您的智能助理',
              prologue: '您好，有什么可以帮助您的吗？',
              userUrl: '#',
              isFree: 1,
              hasPermission: true,
              isAi: true
            };
          default:
            console.warn(`未模拟的状态: ${key}`);
            return {};
        }
      },

      getSessions: (param) => {
        console.log(`模拟 remote.store.getters.getSessions:`, param);
        return {
          id: param?.id || 'default-session',
          scene: 'p2p',
          to: 'default-user',
          name: '默认会话',
          avatar: '/default-avatar.png'
        };
      },

      getUserTeamInfo: {
        'default-team': {
          teamId: 'default-team',
          teamName: '默认团队',
          members: []
        }
      },

      getPersons: (account) => {
        console.log(`模拟 remote.store.getters.getPersons: ${account}`);
        return {
          workerNo: account,
          workerId: account,
          id: account,
          name: '默认用户',
          userShowName: '默认用户',
          avatar: '/default-avatar.png',
          avatarOther: '/default-avatar.png',
          isAi: false,
          hasPermission: true,
          styleFlag: true
        };
      }
    },
    dispatch: async (action, payload) => {
      console.warn(`模拟 store dispatch: ${action}`, payload);
      return { success: false, errorMsg: "Store 不可用", errorCode: 9999 };
    },
    commit: (mutation, payload) => {
      console.warn(`模拟 store commit: ${mutation}`, payload);
    }
  },

  // 模拟日志文件映射
  logFileMap: {}
};

console.log('模拟 remote 模块已创建');

// 创建 NW.js 兼容性对象，防止 nw 引用错误
window.nw = {
  Window: {
    get: () => {
      try {
        if (window.remote && window.remote.getCurrentWindow) {
          return window.remote.getCurrentWindow();
        }
        return {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      } catch (error) {
        console.warn('nw.Window.get() 模拟失败:', error);
        return {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      }
    }
  },
  Shell: {
    openExternal: (url) => {
      try {
        if (window.remote && window.remote.shell) {
          window.remote.shell.openExternal(url);
        }
      } catch (error) {
        console.warn('nw.Shell.openExternal 模拟失败:', error);
      }
    }
  },
  App: {
    quit: () => {
      try {
        if (window.remote && window.remote.app) {
          window.remote.app.quit();
        }
      } catch (error) {
        console.warn('nw.App.quit 模拟失败:', error);
      }
    }
  }
};

// 暴露安全的 API 到渲染进程
try {
  contextBridge.exposeInMainWorld('electronAPI', {
    // 文件系统操作
    fs: {
      existsSync: fs.existsSync,
      readFileSync: fs.readFileSync,
      writeFileSync: fs.writeFileSync,
      unlinkSync: fs.unlinkSync,
      mkdirSync: fs.mkdirSync,
      stat: fs.stat,
      statSync: fs.statSync
    },

    // 路径操作
    path: {
      join: path.join,
      dirname: path.dirname,
      resolve: path.resolve,
      basename: path.basename,
      extname: path.extname
    },

    // 操作系统信息
    os: {
      type: os.type,
      release: os.release,
      platform: os.platform,
      arch: os.arch,
      homedir: os.homedir,
      tmpdir: os.tmpdir
    },

    // 加密功能
    crypto: {
      randomUUID: crypto.randomUUID,
      createHash: crypto.createHash,
      createCipher: crypto.createCipher,
      createDecipher: crypto.createDecipher
    },

    // IPC 通信
    ipcRenderer: {
      send: ipcRenderer.send,
      on: ipcRenderer.on,
      removeListener: ipcRenderer.removeListener,
      removeAllListeners: ipcRenderer.removeAllListeners
    },

    // 进程信息
    process: {
      cwd: process.cwd,
      platform: process.platform,
      arch: process.arch,
      env: process.env
    }
  });
} catch (error) {
  console.log('contextBridge not available, using direct window exposure');
}

console.log('Preload script loaded, Node.js modules available:', {
  fs: !!window.fs,
  path: !!window.path,
  os: !!window.os,
  crypto: !!window.crypto,
  require: !!window.require
});
