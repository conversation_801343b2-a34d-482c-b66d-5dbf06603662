<template>
  <div class="shortcut-conflict">
    <Alert ref="alertRef" :opacity="0" :noDrag="false" title="快捷键冲突" okText="前往设置" :content="content" :triggerClick="triggerClick" :done="selDone">
    </Alert>
  </div>
</template>
<script>

import {nextTick, onUnmounted, ref} from 'vue'
import Alert from "@comp/ui/comps/Alert";

export default {
  name: "shortcut-conflict",
  components: {Alert},
  setup(props, ctx) {
    let remote = window.global.mainWin.window.nw;
    let currentWindow = window.nw.Window.get();
    window.global.childWin["shortcutConflict"] = currentWindow;
    currentWindow.setAlwaysOnTop(true);
    let sel = ref(false);
    let content = ref(getContent());
    let alertRef = ref();
    // 全局鼠标按下监听
    document.addEventListener("mousedown", mousedownEvent);
    // 全局鼠标释放监听
    document.addEventListener("mouseup", mouseupEvent);
    // 全局鼠标移动监听
    document.addEventListener("mousemove", mousemoveEvent);
    // 卸载去除监听
    onUnmounted(() => {
      // 全局鼠标按下监听
      document.removeEventListener("mousedown", mousedownEvent);
      // 全局鼠标释放监听
      document.removeEventListener("mouseup", mouseupEvent);
      // 全局鼠标移动监听
      document.removeEventListener("mousemove", mousemoveEvent);
    })

    // 全局鼠标按下事件
    function mousedownEvent(e) {
      if (e.path.some(elm => /win-drag/.test(elm.className)) && !e.path.some(elm => /win-no-drag/.test(elm.className))) {
        remote.global.dragObj = {x: e.x, y: e.y, width: currentWindow.width, height: currentWindow.height};
      }
    }

    // 全局鼠标释放事件
    function mouseupEvent(e) {
      remote.global.dragObj = "";
    }

    // 全局鼠标移动事件
    function mousemoveEvent(e) {
      // 移动窗口
      if (remote.global.dragObj) {
        if (!e.which) {
          remote.global.dragObj = "";
        }
        currentWindow.moveTo(e.screenX - remote.global.dragObj.x, e.screenY - remote.global.dragObj.y)
      }
    }

    // 弹窗内容点击事件
    function triggerClick(e) {
      if (/sel-box-content/.test(e.target.className) || /sel-box-content/.test(e.target.parentElement.className)) {
        sel.value = !sel.value;
        alertRef.value.changeContent(getContent());
      }
    }

    // 弹窗内容
    function getContent() {
      return `<div>
                <div>检测到你的快捷键被占用：截取屏幕</div>
                <div class='sel-box'>
                <div class='sel-box-content selNone'>
                  <i class='sel-box-i ${sel.value ? 'sel' : ''}'></i>
                  <span>不再提示</span>
                  </div>
                </div>
              </div>`
    }

    function selDone(type) {
      if (type == 1) {
        remote.utils.emitMsg("msg", {type: "window", newWin: 1, name: "setting", width: 550, height: 412, path: "child/setting?tabIndex=3"});
      }
      remote.utils.userLocalStorage({key: "shortcutConflict", value: {show: sel.value, version: remote.store.getters.getConfig.version}}, 1)
      currentWindow.close();
    }

    return {
      sel,
      content,
      alertRef,

      triggerClick,
      selDone,
    }
  }
}
</script>
<style>
#app {
  width: 100%;
  height: 100%;
}
</style>
<style scoped lang="scss">
.shortcut-conflict {
  :deep(.default-alert-content) {
    box-shadow: 0px 8px 16px 0px rgba(17, 20, 21, 0.1);
    border-radius: 4px;
    border: 1px solid #E0E0E0;
    top: 0;
    transform: translate(-50%, 0);

    .sel-box {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      margin-top: 12px;
      flex-shrink: 0;

      .sel-box-content {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .sel-box-i {
          width: 14px;
          height: 14px;
          position: relative;
          border: 1px solid #DDDDDD;
          border-radius: 2px;
          margin-right: 6px;

          &.sel {
            border: 1px solid $styleColor;
            background: $styleColor;

            &:before {
              content: "";
              width: 8px;
              height: 3px;
              border: 2px solid #FFFFFF;
              border-top: transparent;
              border-right: transparent;
              position: absolute;
              top: 2px;
              left: 1px;
              transform: rotate(-45deg);
            }
          }
        }
      }
    }
  }
}
</style>