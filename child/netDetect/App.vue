<template>
  <div class="net-detect ">
    <!-- 顶部操作 -->
    <div class="operate-box">
      <div class="drag-area win-drag"></div>
      <div class="operate-nav win-no-drag">
        <i class="min" @click="winMin"></i>
        <i class="max" :class="isMax?'show-max':''" @click="winMax"></i>
        <i class="close" @click="winClose()"></i>
      </div>
    </div>
    <!-- 窗口名 -->
    <div class="child-win-nav win-drag">网络检测</div>
    <div class="detect-box">
      <ul class="detect-ul selAll">
        <!--系统信息-->
        <li class="detect-li">
          <div class="detect-title-box" @click="toggleShowStep(1)">
            <div class="detect-title">
              <i :class="['show-arrow', netObj.stepObj.showObj[1]?'arrow-bottom':'arrow-right']"></i>
              <span>系统信息</span>
            </div>
            <div :class="['detect-status', netObj.stepObj.resultObj[1].status]"></div>
          </div>
          <div class="detect-detail-box" v-show="netObj.stepObj.showObj[1]">
            <div v-if="netObj.stepObj.resultObj[1].status=='loading'">正在检测...</div>
            <ul v-else class="detect-detail-ul">
              <li class="detect-detail-li w50">
                <span class="detect-li-intr">系统版本：</span>
                <span class="detect-li-detail textEls">{{ netObj.computerInfo.VER }}</span>
              </li>
              <li class="detect-detail-li w50">
                <span class="detect-li-intr">乐聊版本：</span>
                <span class="detect-li-detail">{{ netObj.version }}</span>
              </li>
              <li class="detect-detail-li">
                <span class="detect-li-intr">HOST配置：</span>
                <span class="detect-li-detail">{{ netObj.hostsList.join(",") }}</span>
              </li>
            </ul>
          </div>
        </li>
        <!--ping结果-->
        <li class="detect-li">
          <div class="detect-title-box" @click="toggleShowStep(3)">
            <div class="detect-title">
              <i :class="['show-arrow', netObj.stepObj.showObj[3]?'arrow-bottom':'arrow-right']"></i>
              <span>ping结果</span>
            </div>
            <div :class="['detect-status', netObj.stepObj.resultObj[3].status]"></div>
          </div>
          <div class="detect-detail-box" v-show="netObj.stepObj.showObj[3]">
            <div v-if="netObj.stepObj.resultObj[3].status=='loading'">正在检测...</div>
            <ul v-else class="detect-detail-ul">
              <li class="detect-detail-li detect-detail-li-1" v-for="(item,key) in netObj.stepObj.resultObj[3].list" :key="key">
                <div class="detect-detail-li-title">{{ key + 1 }}、{{ item.key }}</div>
                <div class="detect-detail-li-detail">{{ item.str }}</div>
              </li>
            </ul>
          </div>
        </li>
        <!--tracert结果-->
        <li class="detect-li">
          <div class="detect-title-box" @click="toggleShowStep(4)">
            <div class="detect-title">
              <i :class="['show-arrow', netObj.stepObj.showObj[4]?'arrow-bottom':'arrow-right']"></i>
              <span>tracert结果</span>
            </div>
            <div :class="['detect-status', netObj.stepObj.resultObj[4].status]"></div>
          </div>
          <div class="detect-detail-box" v-show="netObj.stepObj.showObj[4]">
            <div v-if="netObj.stepObj.resultObj[4].status=='loading'">正在检测...</div>
            <ul v-else class="detect-detail-ul">
              <li class="detect-detail-li detect-detail-li-1" v-for="(item,key) in netObj.stepObj.resultObj[4].list" :key="key">
                <div class="detect-detail-li-title">{{ key + 1 }}、{{ item.key }}</div>
                <div class="detect-detail-li-detail" v-html="setSubSerHtml(item.str)"></div>
              </li>
            </ul>
          </div>
        </li>
        <!--nslookup结果-->
        <li class="detect-li">
          <div class="detect-title-box" @click="toggleShowStep(5)">
            <div class="detect-title">
              <i :class="['show-arrow', netObj.stepObj.showObj[5]?'arrow-bottom':'arrow-right']"></i>
              <span>nslookup结果</span>
            </div>
            <div :class="['detect-status', netObj.stepObj.resultObj[5].status]"></div>
          </div>
          <div class="detect-detail-box" v-show="netObj.stepObj.showObj[5]">
            <div v-if="netObj.stepObj.resultObj[5].status=='loading'">正在检测...</div>
            <ul v-else class="detect-detail-ul">
              <li class="detect-detail-li detect-detail-li-1" v-for="(item,key) in netObj.stepObj.resultObj[5].list" :key="key">
                <div class="detect-detail-li-title">{{ key + 1 }}、{{ item.key }}</div>
                <div class="detect-detail-li-detail" v-html="setSubSerHtml(item.str)"></div>
              </li>
            </ul>
          </div>
        </li>
        <!--公网ip-->
        <li class="detect-li">
          <div class="detect-title-box" @click="toggleShowStep(6)">
            <div class="detect-title">
              <i :class="['show-arrow', netObj.stepObj.showObj[6]?'arrow-bottom':'arrow-right']"></i>
              <span>公网ip</span>
            </div>
            <div :class="['detect-status', netObj.stepObj.resultObj[6].status]"></div>
          </div>
          <div class="detect-detail-box" v-show="netObj.stepObj.showObj[6]">
            <div v-if="netObj.stepObj.resultObj[6].status=='loading'">正在检测...</div>
            <ul v-else class="detect-detail-ul">
              <li class="detect-detail-li w50">
                <span class="detect-li-intr">当前 IP：</span>
                <span class="detect-li-detail">{{ netObj.stepObj.resultObj[6].ip }}</span>
              </li>
              <li class="detect-detail-li w50">
                <span class="detect-li-intr">来自于：</span>
                <span class="detect-li-detail">{{ netObj.stepObj.resultObj[6].address }}</span>
              </li>
            </ul>
          </div>
        </li>
        <!--主站访问检测-->
        <li class="detect-li">
          <div class="detect-title-box" @click="toggleShowStep(2)">
            <div class="detect-title">
              <i :class="['show-arrow', netObj.stepObj.showObj[2]?'arrow-bottom':'arrow-right']"></i>
              <span>主站访问检测</span>
            </div>
            <div :class="['detect-status', netObj.stepObj.resultObj[2].status]"></div>
          </div>
          <div class="detect-detail-box" v-show="netObj.stepObj.showObj[2]">
            <div v-if="netObj.stepObj.resultObj[2].status=='loading'">正在检测...</div>
            <ul v-else class="detect-detail-ul">
              <li class="detect-detail-li detect-detail-li-2" v-for="(item,key) in netObj.stepObj.resultObj[2].list" :key="key">
                <span class="detect-detail-li-title textEls">{{ item.key }}</span>
                <span class="detect-detail-li-detail">成功率：{{ parseInt(item.success / item.total * 100) }}%</span>
                <span class="detect-detail-li-detail">平均耗时：{{ item.time }}ms</span>
              </li>
            </ul>
          </div>
        </li>
      </ul>
      <div class="btn-box">
        <div :class="['btn', netObj.allResult?'':'disabled']" @click="commit()">提交检测记录</div>
      </div>
    </div>
  </div>
</template>
<script>

const fs = remote.require("fs");
const cp = remote.require("child_process");
import {nextTick, onUnmounted, ref, watch} from 'vue'
import {trimArray, getAllComputerInfo, getIconvDecode, setSubSerHtml, deepClone} from "@utils";
import {postApi, networkCheckApi} from "@utils/net/api";
import {alert, loading, toast} from "@comp/ui";

export default {
  name: "shortcut-conflict",
  setup(props, ctx) {
    let currentWindow = window.nw.Window.get();
    window.global.childWin["netDetect"] = currentWindow;

    let isMax = ref(false);
    let netObj = ref({
      stepObj: {
        showObj: {},
        resultObj: {
          1: {num: 0, total: 2, status: "loading"},
          2: {
            num: 0, total: 4, status: "loading",
            list: [],
            defaultList: [
              {url: "https://i.leyoujia.com/heart.gif", key: "i.leyoujia.com", time: 0, success: 0, total: 10},
              {url: "https://coa.leyoujia.com/heart.gif", key: "coa.leyoujia.com", time: 0, success: 0, total: 10},
              {url: "https://hr.leyoujia.com/heart.gif", key: "hr.leyoujia.com", time: 0, success: 0, total: 10},
              {url: "https://data.leyoujia.com/heart.gif", key: "data.leyoujia.com", time: 0, success: 0, total: 10}
            ]
          },
          3: {
            num: 0, total: 3, status: "loading",
            list: [
              {url: "i.leyoujia.com", key: "公司", str: ""},
              {url: "baidu.com", key: "百度", str: ""},
              {url: "qq.com", key: "qq", str: ""},
            ]
          },
          4: {
            num: 0, total: 4, status: "loading",
            list: [
              {url: "i.leyoujia.com", key: "i.leyoujia.com", str: ""},
              {url: "coa.leyoujia.com", key: "coa.leyoujia.com", str: ""},
              {url: "hr.leyoujia.com", key: "hr.leyoujia.com", str: ""},
              {url: "data.leyoujia.com", key: "data.leyoujia.com", str: ""}
            ]
          },
          5: {
            num: 0, total: 4, status: "loading",
            list: [
              {url: "i.leyoujia.com", key: "i.leyoujia.com", str: ""},
              {url: "coa.leyoujia.com", key: "coa.leyoujia.com", str: ""},
              {url: "hr.leyoujia.com", key: "hr.leyoujia.com", str: ""},
              {url: "data.leyoujia.com", key: "data.leyoujia.com", str: ""}
            ]
          },
          6: {num: 0, total: 1, status: "loading"},
        },
      },// 显示的步骤
      allResult: false,// 是否全部不步骤加载完毕
      hostsList: [],// hosts配置
      version: remote.store.getters.getConfig.version,// 乐聊版本
      computerInfo: {},// 基础电脑信息
    });

    initNetDetect();

    // 全局鼠标按下监听
    document.addEventListener("mousedown", mousedownEvent);
    // 全局鼠标释放监听
    document.addEventListener("mouseup", mouseupEvent);
    // 全局鼠标移动监听
    document.addEventListener("mousemove", mousemoveEvent);
    // 卸载去除监听
    onUnmounted(() => {
      // 全局鼠标按下监听
      document.removeEventListener("mousedown", mousedownEvent);
      // 全局鼠标释放监听
      document.removeEventListener("mouseup", mouseupEvent);
      // 全局鼠标移动监听
      document.removeEventListener("mousemove", mousemoveEvent);
    })

    // 全局鼠标按下事件
    function mousedownEvent(e) {
      if (e.path.some(elm => /win-drag/.test(elm.className)) && !e.path.some(elm => /win-no-drag/.test(elm.className))) {
        remote.global.dragObj = {x: e.x, y: e.y, width: currentWindow.width, height: currentWindow.height};
      }
    }

    // 全局鼠标释放事件
    function mouseupEvent(e) {
      remote.global.dragObj = "";
    }

    // 全局鼠标移动事件
    function mousemoveEvent(e) {
      // 移动窗口
      if (remote.global.dragObj) {
        if (!e.which) {
          remote.global.dragObj = "";
        }
        currentWindow.moveTo(e.screenX - remote.global.dragObj.x, e.screenY - remote.global.dragObj.y)
      }
    }

    // 监听全局关闭窗口事件
    watch(() => remote.store.state.emit.closeWindow,
      (newValue, oldValue) => {
        // closeWindow空时调用关闭窗口
        if (!newValue) {
          winClose();
        }
      }, {
        deep: true
      }
    );

    // 最小化
    function winMin() {
      remote.store.commit("setWindowMin", currentWindow.cWindow.id);
    }

    // 最大化
    function winMax() {
      if (currentWindow.isFullscreen) {
        isMax.value = false;
        remote.store.commit("setWindowCancelMax", currentWindow.cWindow.id);
      } else {
        isMax.value = true;
        remote.store.commit("setWindowMax", currentWindow.cWindow.id);
      }
    }

    // 关闭窗口-emit.closeWindow不存在的时候关闭当前窗口
    function winClose() {
      remote.store.commit("setWindowClose", currentWindow.cWindow.id);
    }

    // 初始化网络检测
    function initNetDetect() {
      // 初始化检测进度
      for (let key in netObj.value.stepObj.resultObj) {
        netObj.value.stepObj.resultObj[key].num = 0;
        netObj.value.stepObj.resultObj[key].status = "loading";
        if (key == 2) {
          netObj.value.stepObj.resultObj[2].list = deepClone(netObj.value.stepObj.resultObj[2].defaultList);
        }
      }
      // 获取电脑信息
      getAllComputerInfo().then(function (systemInfoData) {
        netObj.value.computerInfo = systemInfoData;
        getResultStatus(1);
      });
      getHost();
      getFetch();
      getPing();
      getTracert();
      getNslookup();
      getIp();
    };

    // 切换显示步骤
    function toggleShowStep(key) {
      netObj.value.stepObj.showObj[key] = !netObj.value.stepObj.showObj[key];
    }

    // 设置进度状态
    function getResultStatus(key, flag) {
      if (flag == undefined) {
        if (netObj.value.stepObj.resultObj[key].status == "loading") {
          // 加载中的才设置状态
          netObj.value.stepObj.resultObj[key].num++;
          if (netObj.value.stepObj.resultObj[key].num >= netObj.value.stepObj.resultObj[key].total) {
            netObj.value.stepObj.resultObj[key].status = "success";
          }
        }
      } else {
        // 获取成功/失败设置状态
        netObj.value.stepObj.resultObj[key].status = flag;
      }
      // 判断是否全部加载完毕
      let allResult = true;
      for (let key in netObj.value.stepObj.resultObj) {
        if (netObj.value.stepObj.resultObj[key].status == "loading") {
          allResult = false;
        }
      }
      netObj.value.allResult = allResult;
    }

    // 获取host配置
    function getHost() {
      let hostsPath = remote.process.env.SystemRoot + "\\System32\\drivers\\etc\\hosts";
      if (fs.statSync(hostsPath)) {
        let hosts = fs.readFileSync(hostsPath, "utf-8");
        let hostsList = trimArray(hosts.trim().replace(/\n/g, "~~").split("~~"));
        let showHostsList = [];
        hostsList.map(item => {
          let showItem = item.trim();
          if (showItem.indexOf("#") != 0) {
            showHostsList.push(showItem);
          }
        });
        netObj.value.hostsList = showHostsList;
      }
      getResultStatus(1);
    }

    // fetch获取i.coa.hr.data时间
    function getFetch() {
      let startTime = Date.now();
      netObj.value.stepObj.resultObj[2].list.map(async item => {
        // 获取多次平均值
        for (let i = 0; i < item.total; i++) {
          let res = await postApi({url: item.url, method: "GET", headers: {"Cache-Control": "no-cache"}});
          if (res?.status != -1) {
            item.success++;
            item.time += Date.now() - startTime;
          }
          if (i == item.total - 1) {
            getResultStatus(2);
            item.time = item.success == 0 ? "-" : parseInt(item.time / item.success);
          }
        }
      });
    }

    // ping获取i.百度.qq
    function getPing() {
      let encoding = "gbk";
      let binaryEncoding = "binary";
      netObj.value.stepObj.resultObj[3].list.map(item => {
        cp.exec(`ping ${item.url} -n 10`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          let pingStr = (getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding) || "").replace(/\n/g, "");
          let pingIndex = pingStr.indexOf("数据包") > -1 ? pingStr.indexOf("数据包") : 0;
          item.str = pingStr.slice(pingIndex);
          getResultStatus(3);
        });
      });
    }

    // tracert获取i.coa.hr.data
    function getTracert() {
      let encoding = "gbk";
      let binaryEncoding = "binary";
      netObj.value.stepObj.resultObj[4].list.map(item => {
        cp.exec(`tracert -d -h 15 -w 2000 ${item.url}`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          item.str = (getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding) || "");
          getResultStatus(4);
        });
      });
    }

    // nslookup获取i.coa.hr.data
    function getNslookup() {
      let encoding = "gbk";
      let binaryEncoding = "binary";
      netObj.value.stepObj.resultObj[5].list.map(item => {
        cp.exec(`nslookup ${item.url}`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          item.str = (getIconvDecode(new Buffer(stdout, binaryEncoding), encoding) || "");
          getResultStatus(5);
        });
      });
    }

    // 获取ip
    function getIp() {
      remote.store.dispatch("getUserIp").then(async res => {
        if (res.promise) {
          res = await res.promise;
        }
        netObj.value.stepObj.resultObj[6].ip = res.ip;
        netObj.value.stepObj.resultObj[6].address = res.address;
        getResultStatus(6);
      });
    }

    // 提交
    async function commit() {
      if (!netObj.value.allResult) {
        return;
      }
      loading();
      let res = await networkCheckApi({
        msgBody: JSON.stringify({
          ip: netObj.value.stepObj.resultObj[6].ip,
          sta: 1,
          content: JSON.stringify(netObj.value.stepObj),
          emp: remote.store.state.userInfo.workerNo,
        })
      });
      loading().hide();
      if (!res?.success) {
        alert({
          content: res?.errorMsg || "报告提交失败，请检查您的网络",
          showCancel: false,
          okText: "关闭"
        });
        return;
      }
      toast({title: "报告提交成功", modal: true, done: res => {winClose();}});
    }


    return {
      isMax,
      netObj,

      setSubSerHtml,

      winMin,
      winMax,
      winClose,
      initNetDetect,
      toggleShowStep,
      getResultStatus,
      commit,
    }
  }
}
</script>
<style>
#app {
  width: 100%;
  height: 100%;
}
</style>
<style scoped lang="scss">
.net-detect {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  border: 1px solid #D8D8D8;

  .operate-box {
    width: 100%;
    z-index: 101;

    &.child-operate-box {
      .operate-nav {
        top: 4px !important;
      }
    }

    &.child-collect-router {
      .operate-nav {
        z-index: 79;
      }
    }

    .drag-area {
      position: fixed;
      top: 2px;
      left: 2px;
      width: calc(100% - 4px);
      height: 30px;
    }

    .operate-nav {
      position: fixed;
      top: 0px;
      right: 6px;
      display: flex;
      z-index: 351;

      i {
        width: 30px;
        height: 30px;
        cursor: pointer;
        background-image: url(/img/icon_operate.png);
        background-repeat: no-repeat;
        background-size: 240px 30px;
      }

      .min {
        &:hover {
          background-position: -30px 0;
        }
      }

      .max {
        background-position: -60px 0;

        &:hover {
          background-position: -90px 0;
        }

        &.show-max {
          background-position: -120px 0;

          &:hover {
            background-position: -150px 0;
          }
        }
      }

      .close {
        background-position: -180px 0;

        &:hover {
          background-position: -210px 0;
        }
      }
    }
  }

  .detect-box {
    width: 100%;
    height: calc(100% - 30px);
    padding-bottom: 54px;

    .detect-ul {
      height: 100%;
      padding: 10px 0 20px;
      overflow-y: auto;

      .detect-li {
        position: relative;
        padding-bottom: 10px;

        &:hover {
          background: #F9F9F9;
        }

        .detect-title-box {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 10px 16px 0;

          &:after {
            position: absolute;
            content: "";
            width: calc(100% - 32px);
            height: 1px;
            left: 16px;
            bottom: 0;
            background: $styleBg1Sel;
          }

          .detect-title {
            flex: 1;
            display: flex;
            align-items: center;
            font-weight: bold;
            font-size: 13px;
            color: #000000;

            .show-arrow {
              width: 12px;
              height: 12px;
              margin-right: 4px;

              &.arrow-right {
                &:before {
                  border-color: transparent transparent transparent #999999;
                }

                &:before,
                &:after {
                  border-width: 5px 0 5px 5px;
                }
              }

              &.arrow-bottom {
                &:before {
                  border-color: #999999 transparent transparent transparent;
                }

                &:before,
                &:after {
                  border-width: 5px 5px 0 5px;
                }
              }
            }
          }

          .detect-status {
            flex-shrink: 0;
            width: 16px;
            height: 16px;
            margin-left: 10px;
            background-repeat: no-repeat;
            background-size: 100%;

            &.loading {
              background-image: url("/img/icon_loading.png");
              animation: myLoading 800ms linear infinite;
            }

            &.success {
              background-image: url("/img/icon_success.png");
            }

            &.error {
              background-image: url("/img/icon_error.png");
            }
          }
        }

        .detect-detail-box {
          margin-top: 4px;
          padding: 0 16px 0 32px;
          color: #666666;

          .detect-detail-ul {
            display: flex;
            flex-wrap: wrap;

            .detect-detail-li {
              width: 100%;
              flex-shrink: 0;
              margin-top: 4px;
              display: flex;

              &.w50 {
                width: 50%;
              }

              &.detect-detail-li-1 {
                flex-direction: column;
                margin-bottom: 10px;

                &:last-child {
                  margin-bottom: 0;
                }

                .detect-detail-li-detail {
                  margin-top: 4px;
                }
              }

              &.detect-detail-li-2 {
                .detect-detail-li-title {
                  width: 139px;
                }

                .detect-detail-li-detail {
                  margin-right: 40px;

                  &:last-child {
                    margin-right: 0;
                  }
                }
              }

              .detect-li-intr {
                flex-shrink: 0;
              }

              .detect-li-detail {
                flex: 1;
                word-break: break-all;

                &.textEls {
                  padding-right: 10px;
                }
              }

              .detect-detail-li-title {
                font-weight: bold;
                color: #000000
              }
            }
          }
        }
      }
    }

    .btn-box {
      position: absolute;
      bottom: 1px;
      left: 1px;
      width: calc(100% - 2px);
      height: 54px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05), inset 0px 1px 0px 0px #DDDDDD;
      background: #FFFFFF;

      .btn {
        padding: 7px 16px;
        background: #E03236;
        border-radius: 4px;
        color: #FFFFFF;
        cursor: pointer;

        &.disabled {
          opacity: 0.3;
        }
      }
    }
  }

}
</style>