<template>
  <div class="child">
    <!--  顶部操作  -->
    <div class="operate-box">
      <div :class="['drag-area','win-drag','win-no-resize']"></div>
      <div class="operate-nav win-no-drag">
        <i class="min" @click="winMin"></i>
        <i class="close" @click="winClose()"></i>
      </div>
    </div>
    <div class="forward">
      <h5 class="title">转发</h5>
      <!--选择人员转发-->
      <ul class="forward-ul" v-if="!isForwardStatus">
        <li class="forward-li forward-list-box">
          <!--搜索-->
          <div class="search-input-box" v-show="levelObj.level==1">
            <input class="search-input" type="text" placeholder="搜索" @input="searchInput($event)" v-model.trim="searchVal">
            <button class="search-btn"></button>
          </div>
          <!--分类-->
          <ul class="label-person-nav" v-show="levelObj.level==1&!searchObj.flag">
            <li :class="{'curr':labelObj.type==1}" @click="changeLabelList(1)">同事</li>
            <li :class="{'curr':labelObj.type==2}" @click="changeLabelList(2)">客户</li>
          </ul>
          <!--会话-->
          <div class="forward-person-box" v-show="levelObj.level==1&!searchObj.flag">
            <!--标签-->
            <div class="label-person-box">
              <div class="label-title-box">
                <span>标签</span>
                <span v-show="labelObj.more" :class="['label-toggle',labelObj.show?'show':'']" @click="labelObj.show=!labelObj.show">{{ labelObj.show ? "收起" : "展开" }}</span>
              </div>
              <ul :class="['forward-label-ul',labelObj.show?'label-show':'']" ref="forwardLabelUlRef">
                <li :class="['forward-label-li','textEls',selSessionMap[item.id]?'check':'']" v-for="(item,key) in labelObj.list" :key="item.id" @click="selItem(item)">{{ item.name }}</li>
              </ul>
            </div>
            <!--最近聊天-->
            <div class="forward-person-title">最近聊天</div>
            <ul class="forward-person-ul">
              <!--最近聊天-同事-->
              <li v-show="labelObj.type==1" class="forward-person-list" v-for="(item,key) in sessionList" :key="item.id" @click="selItem(item)">
                <div class="check-box" :class="{'check':selSessionMap[item.id]}"></div>
                <div class="user-info-box">
                  <div class="user-avatar-box">
                    <div class="avatar-box">
                      <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                    </div>
                  </div>
                  <div class="content-box textEls">{{ item.detailInfo.name }}</div>
                  <div class="info-btn" v-if="(item.scene=='team'||item.scene=='superTeam')&&isForwardTeamMember" @click.stop="changeLevel(2,1,item)">转私聊</div>
                </div>
              </li>
              <!--最近聊天-客户-->
              <li v-show="labelObj.type==2" class="forward-person-list" v-for="(item,key) in customerList" :key="item.id" @click="selItem(item)">
                <div class="check-box" :class="{'check':selSessionMap[item.id]}"></div>
                <div class="user-info-box">
                  <div class="user-avatar-box">
                    <div class="avatar-box">
                      <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                    </div>
                  </div>
                  <div class="content-box textEls">{{ item.detailInfo.name }}</div>
                </div>
              </li>
            </ul>
          </div>
          <!--搜索会话-->
          <ul class="forward-person-box forward-person-box-search" v-show="levelObj.level==1&searchObj.flag" ref="searchListRef">
            <li class="forward-person-list" v-for="(item,key) in searchObj.list" :key="item.id" @click="selItem(item)">
              <div class="check-box" :class="{'check':selSessionMap[item.id]}"></div>
              <div class="user-info-box">
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                  </div>
                </div>
                <div class="content-box textEls">{{ item.detailInfo.name }}</div>
                <div class="info-btn" v-if="(item.scene=='team'||item.scene=='superTeam')&&isForwardTeamMember" @click.stop="changeLevel(2,1,item)">转私聊</div>
              </div>
            </li>
          </ul>
          <!--客户咨询/转私聊-->
          <div class="level-box" v-show="levelObj.level==2">
            <div class="back" @click="changeLevel(1)">返回</div>
            <!--转私聊-->
            <div class="forward-person-box forward-person-box-team" v-if="levelObj.type==1">
              <div class="search-input-box">
                <input class="search-input" type="text" placeholder="搜索" @input="searchInput($event,1)" v-model.trim="levelObj.key">
                <button class="search-btn"></button>
              </div>
              <div class="forward-team-box">
                <div class="forward-team-name-box">
                  <div class="forward-team-name">
                    <span class="forward-team-name-text textEls">{{ levelObj.item.detailInfo.name }}</span>
                    <span class="forward-team-num">({{ levelObj.item.detailInfo.memberNum }})</span>
                  </div>
                  <div class="forward-team-all" @click="selAllTeamMember()">{{ isSelAllTeamMember().flag ? "取消" : "" }}全选</div>
                </div>
              </div>
              <div v-if="levelObj.loading" class="loading-img-box">
                <img class="loading-img" src="/img/waitting.gif" alt="">
              </div>
              <ul v-else class="forward-team-ul">
                <li class="forward-person-list" v-for="(item,key) in levelObj.list" :key="item.id" @click="selItem(item)">
                  <div class="check-box" :class="{'check':selSessionMap[item.id]}"></div>
                  <div class="user-info-box">
                    <div class="user-avatar-box">
                      <div class="avatar-box">
                        <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                      </div>
                    </div>
                    <div class="content-box textEls">{{ item.detailInfo.name }}</div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </li>
        <li class="forward-li forward-sel-list-box">
          <div class="pr20">
            <!--消息简介-->
            <div class="forward-msg">
              <div class="msg-file" v-if="forwardMsg.type=='file'||forwardMsg.type=='video'">
                <div class="file-info-box">
                  <img class="file-icon notCopy selNone" :src="getFileIcon(forwardMsg.file.ext)" alt="">
                  <div class="file-content">
                    <div class="file-name" :title="forwardMsg.file.name">
                      <span class="name textEls">{{ forwardMsg.file.showName || forwardMsg.file.name }}</span>
                      <span class="ext textEls">{{ "." + forwardMsg.file.ext }}</span>
                    </div>
                    <div class="file-size">{{ dealMem(forwardMsg.file.size) }}</div>
                  </div>
                </div>
              </div>
              <div class="msg-intr textEls2" v-else v-html="getPrimaryMsg()"></div>
            </div>
            <!--新增发送内容-->
            <input class="forward-text" type="text" placeholder="这个文档不错哦，转发给您~" v-model="otherText" maxlength="140">
            <div class="forward-intr">还能输入{{ 140 - otherText.length }}个字</div>
            <div class="forward-tips">当前发送消息总数(人*消息数)：{{ forwardAllNum }} 条，>{{ forwardMsgMax }}条则不允许发送，请精简发送人或消息数</div>
            <div class="forward-detail">请勾选需要添加的联系人({{ getForwardInfo() }}/{{ forwardMax }})</div>
          </div>
          <!--选中的人员列表-->
          <ul class="forward-person-sel-box pr20" v-show="getSelSessionList().length>0">
            <li class="forward-person-sel-list" v-for="(item,key) in getSelSessionList()" :key="item.id">
              <div :class="['user-info-box',item.uuid?'user-info-label':'']">
                <div class="user-info-content" @click="toggleShowList(item)">
                  <div class="user-info-content-box">
                    <div class="user-avatar-box">
                      <div class="avatar-box">
                        <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                      </div>
                    </div>
                    <div class="content-box textEls">{{ item.detailInfo.name }}</div>
                    <!--标签显示切换显示列表-->
                    <div :class="['label-toggle',item.show?'show':'']" v-if="item.uuid"></div>
                  </div>
                  <div class="delete-box" @click="selItem(item)"></div>
                </div>
                <!--标签人员列表列表-->
                <ul class="user-info-ul" v-if="item.uuid" v-show="item.show">
                  <li class="user-info-li" v-for="(item1,key1) in item.list">
                    <div class="user-avatar-box">
                      <div class="avatar-box">
                        <img class="avatar" :src="item1.detailInfo.avatar" :onerror="avatarError.bind(this, item1.scene, item1.to , item.detailInfo.detailType||'')">
                      </div>
                    </div>
                    <div class="textEls">{{ item1.detailInfo.name }}</div>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
          <ul class="forward-person-sel-none" v-show="getSelSessionList().length==0"></ul>
          <div class="forward-btn-box pr20">
            <button class="btn btn-confirm" :disabled="getForwardInfo()==0" @click="forwardBtn(1)">确定</button>
            <button class="btn btn-cancel" @click="forwardBtn(2)">取消</button>
          </div>
        </li>
      </ul>

      <!--转发状态-->
      <div class="forward-status-box" v-else>
        <ul class="forward-status-header">
          <li>名称</li>
          <li>进度</li>
          <li>状态</li>
        </ul>
        <div class="forward-status-body">
          <ul class="forward-status-body-box" v-for="(item,key) in forwardStatusList" :key="item.id">
            <li>
              <div class="user-info-box">
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                  </div>
                </div>
                <div class="content-box textEls">{{ item.detailInfo.name }}</div>
              </div>
            </li>
            <li>
              <progress max="100" :value="getProgress(item)"></progress>
            </li>
            <li>
              <span class="status" :class="getStatus(2, item)" @click="reForward(item)">{{ getStatus(1, item) }}</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {jQueryMethod} from "@comp/schedule/jquery";
import {nextTick, ref, watch, onUnmounted} from "vue";
import {
  avatarError, isSubOrSer, deepClone, debounce, setUserBaseInfo, getFileIcon, dealMem, getGlobal, dealMsgField, regReplace, dateFormat, sortTeamMembers, getUserTel, MD5,
} from "@utils";
import {searchUsersApi, addTagPowerApi, smartTransmitApi, getPushForwardersApi, queryPurviewApi} from "@utils/net/api";
import {alert, toast} from "@comp/ui";

export default {
  name: "Forward",
  setup(props, ctx) {
    jQueryMethod();
    // 配置文件
    let config = remote.store.getters.getConfig.config;
    let currentWindow = remote.store.getters.getCurrentWindow("forward");
    let userInfo = remote.store.getters.getUserInfo;
    // 搜索内容
    let searchVal = ref("");
    // 搜索对象
    let searchObj = ref({
      list: [],//搜索列表
      flag: false,//显示搜索内容
    });
    // 层级对象
    let levelObj = ref({
      level: 1,//1一级列表，2二级列表客户/标签列表
      type: -1,//1转私聊
      list: [],//显示二级列表
      item: {},//选择的层级对象数据
      memberList: [],//群成员列表
      key: "",//搜索内容
      loading: false,//加载状态
    });
    // 标签对象
    let labelObj = ref({
      person: [],
      customer: [],
      type: 1,
      list: [],
      show: false,//是否是展开状态
      more: false,//是否需要展开
    });
    // 选择的发送类型-1同事-2客户
    let selType = -1;
    // 搜索元素
    let searchListRef = ref();
    // 标签元素
    let forwardLabelUlRef = ref();
    // 转发的消息，forwardType-item逐条转发-merge合并转发-scheduleInvite日程邀约 forwardContent-显示转发消息简要
    let forwardMsg = ref(getGlobal("forwardMsg"));
    // 转发判断字段
    let forwardObj = {
      forwardType: forwardMsg.value.forwardType,//转发类型
      forwardTypeNew: forwardMsg.value.forwardType,//转发类型变动-逐条转发和合并转发
      forwardContent: forwardMsg.value.forwardContent,//转发简介内容
      multipleList: forwardMsg.value.multipleList || [],//逐条转发消息列表
      detailType: forwardMsg.value.detailType,//转发具体类型-暂时只有收藏
    };
    // 转发消息条数
    let forwardAllNum = ref(0);
    // 额外发送的消息
    let otherText = ref("");
    // 会话列表
    let sessionList = ref([]);
    // 客户列表
    let customerList = ref([]);
    // 选中的会话对象
    let selSessionMap = ref({});
    // 转发状态
    let isForwardStatus = ref(false);
    // 转发状态列表
    let forwardStatusList = ref([]);
    // 权限字段
    let isForward = ref(false);
    // 转私聊权限
    let isForwardTeamMember = ref(false);
    // 转发人数
    let forwardMax = ref(10);
    // 转发总消息数(人*消息数)
    let forwardMsgMax = ref(280);
    // 发送数量计数标识
    let uniqueSignCount = 0;
    getForwardPur();
    // 全局鼠标按下监听
    document.addEventListener("mousedown", mousedownEvent);
    // 全局鼠标释放监听
    document.addEventListener("mouseup", mouseupEvent);
    // 全局鼠标移动监听
    document.addEventListener("mousemove", mousemoveEvent);
    // 卸载去除监听
    onUnmounted(() => {
      // 全局鼠标按下监听
      document.removeEventListener("mousedown", mousedownEvent);
      // 全局鼠标释放监听
      document.removeEventListener("mouseup", mouseupEvent);
      // 全局鼠标移动监听
      document.removeEventListener("mousemove", mousemoveEvent);
    })
    // 加载水印
    remote.store.commit("setWaterMark", {window: window});
    // 加载埋点
    remote.store.commit("loadTjs", {window: window});
    // 获取会话列表
    let remoteSessionList = remote.store.getters.getSessions({sort: -1, child: true})["-1"];
    remoteSessionList.map(item => {
      if (!isSubOrSer(item.id) && item.to != config.helperAccount && item.to != config.customerAccount && item.detailInfo.empStatus != 4) {
        sessionList.value.push(item);
      }
    });
    // 获取客户列表
    let remoteCustomerList = ref(remote.store.getters.getSessions({sort: 5})["5"]);
    remoteCustomerList.value.map(info => {
      customerList.value.push({
        id: info.id,
        scene: info.scene,
        to: info.to,
        selType: 2,
        detailInfo: info.detailInfo
      });
    });

    // 获取服务器最新标签
    remote.store.dispatch("setGroupSetting");
    getGroupSetting();

    function getGroupSetting() {
      let groupSetting = remote.store.getters.getGroupSetting;
      let thisPersonList = [];
      let thisCustomerList = [];
      let workerMap = [];
      groupSetting.map(item => {
        if (item.type == 3) {
          item.value.split(",").map(item => {
            if (item) {
              workerMap[item] = item;
            }
          })
          // 同事
          thisPersonList.push({
            ...item,
            id: item.uuid,
            selType: 1,
            detailInfo: {
              name: item.name,
              avatar: "/img/forward/label.png",
            }
          });
        } else if (item.type == 4) {
          let customerMap = {};
          item.value.split(",").map(item => {
            if (item && new RegExp(config.fcw).test(item)) {
              customerMap[item] = {
                id: "p2p-" + item,
                scene: "p2p",
                to: item,
                detailInfo: remote.store.getters.getPersons(item)
              }
            }
          })
          // 客户
          thisCustomerList.push({
            ...item,
            id: item.uuid,
            list: Object.values(customerMap),
            selType: 2,
            detailInfo: {
              name: item.name,
              avatar: "/img/forward/label_customer.png",
            }
          });
        }
      });
      labelObj.value.person = thisPersonList;
      labelObj.value.customer = thisCustomerList;
      // 获取标签人员信息
      let workerNos = Object.values(workerMap);
      remote.store.dispatch("getPersons", {account: workerNos, doneFlag: true}).then(res => {
        labelObj.value.person.map(item => {
          let person = [];
          item.value.split(",").map(item1 => {
            if (res[item1] && res[item1].empStatus != 4) {
              person.push({
                id: "p2p-" + item1,
                scene: "p2p",
                to: item1,
                detailInfo: res[item1]
              });
            }
          });
          item.list = person;
        });
      });
      // 显示同事标签
      changeLabelList(1);
    }

    // 转发消息回调
    function sendMsgDone(res) {
      remote.store.dispatch("sendMsgDone", res);
      res.obj.forwardStatus = res.obj.status;
      let index = forwardStatusList.value.findIndex(item => item.id == res.obj.sessionId);
      if (forwardObj.forwardTypeNew == "item") {
        // 逐条转发
        forwardStatusList.value[index].forwardNum++;
        if (res.obj.status == "success") {
          forwardStatusList.value[index].statusNum++;
        }
        // 判断转发总数是否成功
        if (forwardStatusList.value[index].forwardNum == getForwardNum()) {
          forwardStatusList.value[index].status = forwardStatusList.value[index].statusNum == getForwardNum() ? "success" : "fail";
        }
      } else {
        // 普通/合并转发
        forwardStatusList.value[index].status = res.obj.status;
      }
      // 发送完替换发送列表
      if (!forwardStatusList.value[index].forwardMsgList) {
        forwardStatusList.value[index].forwardMsgList = [];
      }
      let forwardIndex = forwardStatusList.value[index].forwardMsgList.findIndex(item => item.uniqueSign == res.obj.uniqueSign);
      if (forwardIndex == -1) {
        forwardStatusList.value[index].forwardMsgList.push(res.obj);
      } else {
        forwardStatusList.value[index].forwardMsgList[forwardIndex] = res.obj;
      }

      // 发送成功存在额外发送文字则发送
      if (forwardStatusList.value[index].status == "success" && otherText.value) {
        forwardTextApi(forwardStatusList.value[index]);
      }
      if (res.obj.status == "success") {
        // 判断是否关闭窗口
        setWindowClose();
      }
    }

    // 转发额外文字回调
    function forwardMsgText(textRes) {
      remote.store.dispatch("sendMsgDone", textRes);
      let textIndex = forwardStatusList.value.findIndex(item => item.id == textRes.obj.sessionId);
      forwardStatusList.value[textIndex].textStatus = textRes.obj.status;
      forwardStatusList.value[textIndex].forwardStatus = textRes.obj.status;
      // 记录额外文字消息
      forwardStatusList.value[textIndex].forwardOtherMsg = textRes.obj;
      if (textRes.obj.status == "success") {
        // 判断是否关闭窗口
        for (let i = 0; i < forwardStatusList.value.length; i++) {
          if (forwardStatusList.value[i].textStatus != "success") {
            break;
          }
          if (i == forwardStatusList.value.length - 1) {
            setTimeout(() => {
              // 关闭窗口
              winClose();
            }, 1000);
          }
        }
      }
    }

    // 监听窗口关闭事件
    watch(() => remote.store.state.emit.winCloseClick,
      (newValue, oldValue) => {
        winClose();
      }, {
        deep: true
      }
    );

    // 初始化转发消息
    function initForwardMsg(msg) {
      let thisMsg = deepClone(msg);
      dealMsgField(thisMsg);
      // 逐条/合并转发消息列表
      delete thisMsg.multipleList;

      if (!thisMsg.forwardType) {
        // 修改原消息的pushContent
        delete thisMsg.pushContent;
      }
      return thisMsg;
    }

    // 选择转发的人,存在则删除，不存在添加
    async function selItem(item) {
      let num = getForwardInfo();
      // 选择会话
      if (!selSessionMap.value[item.id]) {
        // 转发给群需要判断是否禁言
        if (item.scene == "team" || item.scene == "superTeam") {
          // 判断是否被单独禁言
          let teamMuteObj = await remote.store.getters.getNim.getMutedTeamMembers({teamId: item.to});
          if (teamMuteObj.err) {
            toast({title: "获取群数据失败，请重试。" + teamMuteObj.err, type: 2});
            return;
          }
          let teamMuteList = teamMuteObj.obj.members;
          for (let i = 0; i < teamMuteList.length; i++) {
            if (teamMuteList[i].account == userInfo.workerNo) {
              toast({title: "您已被该群禁言，请联系群管理员", type: 2});
              return;
            }
          }
          // 判断是否被全体禁言
          let teamInfo = remote.store.getters.getTeams({id: item.to});
          if (teamInfo && (teamInfo.custom && teamInfo.custom.isMute == 1) || teamInfo.mute) {
            let teamMembersObj = await remote.store.dispatch("getTeamMembers", {id: item.to});
            if (teamMembersObj.err) {
              toast({title: "获取群数据失败，请重试。" + teamMuteObj.err, type: 2});
              return;
            }
            let teamMembers = sortTeamMembers(teamMembersObj.obj);
            // 全体禁言管理员可以发言
            let isManager = false;
            for (let i = 0; i < teamMembers.length; i++) {
              if (teamMembers[i].type == "normal") {
                break;
              } else {
                if (teamMembers[i].account == userInfo.workerNo) {
                  isManager = true;
                  break;
                }
              }
            }
            if (!isManager) {
              toast({title: "您已被该群禁言，请联系群管理员", type: 2});
              return;
            }
          }
        }
        let isSelLabel = getSelSessionList().findIndex(item => item.uuid) > 0;
        let selNum = 1;
        // 获取标签人数
        if (item.uuid) {
          selNum = item.list.length;
          isSelLabel = true;
        }
        let isFcw = item.selType == 2;
        if ((selType == 1 && isFcw) || (selType == 2 && !isFcw)) {
          toast({title: "不能同时转发给，客户和同事", type: 2});
          return;
        }
        if (selType == -1) {
          forwardMax.value = isForward.value ? 50 : 10;
          // 客户标签
          if (isFcw && !isForward.value) {
            forwardMax.value = 50;
          }
        }
        if (num + selNum > forwardMax.value && !(item.uuid == -1 && num == 0)) {
          alert({
            content: `转发消息最多只能选择${forwardMax.value}个联系人！当前全部选中人数为${num + selNum}人！`,
            showCancel: false,
          });
          return;
        }
        if (selType == -1) {
          selType = 1;
          // 客户标签
          if (isFcw) {
            selType = 2;
          }
        }
        selSessionMap.value[item.id] = item;
      } else {
        delete selSessionMap.value[item.id];
      }
      // 没选中重置选择类型
      if (getForwardInfo() == 0 && getSelSessionList().length == 0) {
        selType = -1;
      }
    }

    // 获取转发人数-type-1获取转发列表
    function getForwardInfo(type) {
      let personMap = {};
      for (let key in selSessionMap.value) {
        let item = selSessionMap.value[key];
        if (item.uuid) {
          item.list.map(item1 => {
            personMap[item1.id] = item1;
          });
        } else {
          personMap[item.id] = item
        }
      }
      let personList = Object.values(personMap);
      if (type == 1) {
        return personList;
      } else {
        forwardAllNum.value = personList.length * (getForwardNum() + (otherText.value.length > 0 ? 1 : 0));
        return personList.length;
      }
    }

    // 获取转发消息数
    function getForwardNum() {
      return forwardObj.forwardTypeNew == "merge" ? 1 : (forwardObj.multipleList.length || 1);
    }

    // 获取选中列表
    function getSelSessionList() {
      return Object.values(selSessionMap.value);
    }

    // 转发按钮
    function forwardBtn(type) {
      if (type == 1) {
        if (forwardAllNum.value > forwardMsgMax.value) {
          alert({
            content: `1分钟内转发人数*转发消息数不能超过${forwardMsgMax.value}条，请减少转发人数或转发消息数`,
            showCancel: false,
            okText: "我知道了"
          });
          return;
        }
        isForwardStatus.value = true;
        forwardStatusList.value = getForwardInfo(1);
        setTimeout(() => {
          forwardStatusList.value.map(item => {
            doForward(item);
          });
        }, 100);
      } else if (type == 2) {
        // 取消关闭窗口
        winClose();
      }
    }

    // 转发消息
    function doForward(item) {
      switch (forwardObj.forwardTypeNew) {
        case "sendMsg":
          // 调用sendMsg方法
          remote.store.dispatch("sendMsg", {sessionId: item.id, messages: forwardMsg.value.msgBody}).then(res => {
            sendMsgDone(res)
          });
          break;
        case "scheduleInvite":
          //日程邀约
          smartTransmitApi({
            id: forwardMsg.value.id,
            toIds: item.to,
            type: item.scene == "p2p" ? 2 : 3
          }).then(res => {
            if (res && res.data && res.data[item.to] == "成功") {
              item.status = "success";
              forwardTextApi(item);
              // 判断是否关闭窗口
              setWindowClose();
            } else {
              item.status = "fail";
            }
          });
          break;
        case "item":
          // 逐条转发
          item.forwardNum = 0;
          item.statusNum = 0;
          let forwardMsgList = forwardObj.multipleList;
          // 存在转发失败消息重发
          if (item.forwardMsgList) {
            forwardMsgList = item.forwardMsgList;
          }
          forwardMsgList.map(msg => {
            (function (item, msg) {
              if (!msg.uniqueSignForward) {
                uniqueSignCount++;
                msg.uniqueSignForward = MD5(`sign_${msg.scene}_${msg.to}_${Date.now()}_${uniqueSignCount}`);
              }
              forwardMethods(item, deepClone(msg))
            })(item, msg);
          });
          break;
        default:
          // 普通/合并转发
          if (!forwardMsg.value.uniqueSignForward) {
            uniqueSignCount++;
            forwardMsg.value.uniqueSignForward = MD5(`sign_${forwardMsg.value.scene}_${forwardMsg.value.to}_${Date.now()}_${uniqueSignCount}`);
          }
          forwardMethods(item, deepClone(forwardMsg.value));
          break;
      }
    }

    // 转发集中销售
    function forwardMethods(item, msg) {
      if (msg.content && msg.content.type == "imJZSell") {
        // 集中销售转发
        getPushForwardersApi({
          pushId: msg.content.data.jsonData.sellId,
          workerId: userInfo.workerId,
        }).then(res => {
          if (res.success && res.data) {
            remote.utils.setJJSEvent("P92831744", JSON.stringify({
              workerId: userInfo.workerId,
              sellid: msg.content.data.jsonData.sellId,
              fhID: msg.content.data.jsonData.fhId,
              time: dateFormat(Date.now(), "yyyy-MM-dd"),
              channel: item.scene == "p2p" ? 1 : 2
            }));
            msg.content.data.jsonData.shareList = res.data;
            msg.content.data.jsonData = JSON.stringify(msg.content.data.jsonData);
            msg.content = JSON.stringify(msg.content);
            forwardMsgApi(item, msg);
          } else {
            item.status = "fail";
          }
        })
      } else if (msg.content && (msg.content.type == "imSomeHouseRecommend" || msg.content.type == "imHouseBatchShare")) {
        // 批量房源将名字改为当前用户
        msg.content.data.name = userInfo.workerName;
        msg.content.data.phone = getUserTel(userInfo)[0];
        item.msg = msg;
        forwardMsgApi(item, msg);
      } else {
        // 转发房源卡片
        if (msg.content) {
          // 还原内容
          if (msg.defaultContent) {
            msg.content = msg.defaultContent;
          }
          if (msg.content.type == "5" || msg.content.type == "6" || msg.content.type == "7") {
            remote.utils.setJJSEvent("P11952128", JSON.stringify({
              workerId: userInfo.workerId,
              workerNo: userInfo.workerNo,
              houseType: msg.content.data.houseType || (msg.content.data.other ? msg.content.data.other.houseType : ""),
              fhId: msg.content.data.fhId || msg.content.data.houseId || (msg.content.data.other ? msg.content.data.other.houseId : ""),
            }));
          } else if (msg.content.type == "imNewHouseJZSell" && msg.content.data?.jsonData) {
            remote.utils.setJJSEvent("P01372160", JSON.stringify({
              workerId: msg.content.data.jsonData.workerId,
              shareId: userInfo.workerId,
              sellId: msg.content.data.jsonData.sellId,
              projectId: msg.content.data.jsonData.projectId,
            }));
          }
        }
        // 普通转发
        item.msg = msg;
        forwardMsgApi(item, msg);
      }
    }

    // 重新发送
    function reForward(item) {
      if (item.status == "fail") {
        item.forwardNum = 0;
        item.statusNum = 0;
        doForward(item);
      } else {
        forwardTextApi(item);
      }
    }

    // 转发消息
    function forwardMsgApi(item, msg) {
      let thisMsg = deepClone(msg);
      thisMsg.autoResendFlag = true;
      // 发送成功重发不继续
      if (thisMsg.forwardStatus == "success") {
        item.forwardNum++;
        item.statusNum++;
        return;
      }
      // 发送失败替换原消息发送
      if (thisMsg.forwardStatus != "fail") {
        delete thisMsg.idServer;
        delete thisMsg.idClient;
        delete thisMsg.uniqueSign;
      }
      // 个人助理需要判断文件/单乐文档/合并消息转发追加提问
      if ((forwardObj.multipleList.length == 0 || forwardObj.multipleList.length == 1 || forwardObj.forwardType == "merge") && otherText.value && item.to == remote.store.state.aiObj.workerNo) {
        let thisForwardMsg = forwardObj.multipleList.length == 1 && forwardObj.multipleList[0].content ? forwardObj.multipleList[0] : forwardMsg.value;
        let thisForwardMsgContent = thisForwardMsg.content;
        try {
          thisForwardMsgContent = JSON.parse(thisForwardMsgContent);
        } catch (e) {}
        // 文件/单乐文档/合并消息转发增加标识
        if (thisMsg.type == "file" || thisForwardMsgContent.type == 9 || (thisForwardMsgContent.type == "multi" && thisForwardMsgContent?.msgs?.[0]?.type == "document")) {
          item.fileMsgFlag = thisMsg.fileMsgFlag = MD5(`sign_${item.scene}_${item.to}_${Date.now()}`);
          remote.store.state.fileMsgFlagMap[item.fileMsgFlag] = {
            type: thisMsg.type == "file" ? "file" : thisForwardMsgContent.type == 9 ? "multi" : "document",
            count: 1,
            msgs: [],
          }
        }
      }
      if (thisMsg.forwardStatus == "fail") {
        // 重发调用重发接口
        remote.store.getters.getNim.resendMsg(initForwardMsg(thisMsg)).then(res => {
          sendMsgDone(res)
        });
      } else {
        remote.store.getters.getNim.forwardMsg({scene: item.scene, to: item.to, msg: initForwardMsg(thisMsg)}).then(res => {
          sendMsgDone(res)
        });
      }
    }

    // 转发额外文字
    function forwardTextApi(item) {
      item.autoResendFlag = true;
      if (item.forwardOtherMsg) {
        // 重发调用重发接口
        remote.store.getters.getNim.resendMsg(initForwardMsg(item.forwardOtherMsg)).then(res => {
          forwardMsgText(res);
        });
      } else {
        remote.store.getters.getNim.forwardMsgText({scene: item.scene, to: item.to, otherText: otherText.value, fileMsgFlag: item.fileMsgFlag}).then(res => {
          forwardMsgText(res);
        });
      }
    }

    // 获取消息简介
    function getPrimaryMsg() {
      let text = "[转发]";
      if (forwardObj.forwardTypeNew == "item" || forwardObj.forwardTypeNew == "item") {
        if (forwardObj.detailType == "collectMsg") {
          text = forwardMsg.value.forwardTitle;
        } else {
          text = `${forwardObj.forwardTypeNew == "item" ? "[逐条转发]" : "[合并转发]"}${forwardMsg.value.forwardTitle}`;
        }
      } else if (forwardMsg.value.forwardContent) {
        text = forwardMsg.value.forwardContent;
      } else {
        text = remote.store.getters.getPrimaryMsg({msg: forwardMsg.value, primaryType: 1});
      }
      return text;
    }

    // 获取转发进度
    function getProgress(item) {
      let progress = 0;
      if (forwardObj.forwardTypeNew == "item") {
        // 多选逐条转发进度
        let statusNum = item.statusNum;
        let forwardNum = item.forwardNum;
        if (otherText.value) {
          forwardNum++;
          if (item.textStatus) {
            statusNum++;
          }
        }
        progress = statusNum / forwardNum * 100;
      } else {
        // 普通转发/合并转发进度
        progress = item.status ? 100 : 0;
        if (otherText.value) {
          progress /= 2;
        }
        if (item.textStatus) {
          progress = 100;
        }
      }
      return progress || 0;
    }

    // 获取发送状态 type-1文字状态-2类名状态
    function getStatus(type, item) {
      if (type == 1) {
        let text = item.status == "success" ? "转发成功" : item.status == "fail" ? "重新发送" : "正在转发";
        if (item.status == "success" && otherText.value) {
          text = item.textStatus == "success" ? "转发成功" : item.textStatus == "fail" ? "重新发送" : "正在转发";
        }
        return text;
      } else if (type == 2) {
        let className = item.status;
        if (className == "success" && otherText.value) {
          className = item.textStatus;
        }
        return className;
      }
    }

    // 搜索,type-1搜索群内成员
    function searchInput(e, type) {
      debounce({
        timerName: "forwardDoSearch",
        time: 500,
        e: e,
        fnName: function () {
          if (type == 1) {
            let text = levelObj.value.key;
            let list = [];
            if (text) {
              text = regReplace(text);
              levelObj.value.memberList.map(item => {
                if (text && new RegExp(text, "i").test(item.detailInfo.workerSpell) || new RegExp(text, "i").test(item.detailInfo.name)) {
                  list.push(item);
                }
              });
            } else {
              list = levelObj.value.memberList;
            }
            levelObj.value.list = list;
          } else {
            doSearch();
          }
        }
      });
    }

    // 搜索api
    async function doSearch() {
      let val = searchVal.value || '';
      if (!val) {
        searchObj.value.flag = false;
        return;
      }
      searchListRef.value.scrollTop = 0;
      let searchRes = await searchUsersApi({
        msgBody: JSON.stringify({
          workerId: userInfo.workerId,
          name: val,
          status: "1",//默认为全部1在职2为离职
          page: 1,
          rows: 50,
          hasAi: "1",
          aiInclude: "1",
        })
      });
      if (!searchRes.success) {
        toast({title: searchRes.errorMsg || "系统错误", type: 2});
      }
      let list = [];
      // 客户
      let friends = remote.store.getters.getNimFriend({val: val});
      friends.map(item => {
        list.push({
          id: "p2p-" + item.account,
          scene: "p2p",
          to: item.account,
          selType: 2,
          detailInfo: item
        });
      });
      // 同事
      if (searchRes.success && searchRes && searchRes.data) {
        if (searchRes.data.empList) {
          // 修改智能助理且匹配到本地智能助理数据则添加
          let aiObj = deepClone(remote.store.getters.getState("aiObj"));
          if (aiObj.workerNo) {
            let aiIndex = searchRes.data.empList.findIndex(item => {return item.workerNo == aiObj.workerNo});
            let localAiItem = remote.store.getters.getPersons(aiObj.workerNo);
            if (aiIndex != -1) {
              searchRes.data.empList[aiIndex] = localAiItem;
            } else if (new RegExp(regReplace(val), "i").test(aiObj.name)) {
              searchRes.data.empList.unshift(localAiItem);
            }
          }
          let workerMap = {};
          for (let i = 0; i < searchRes.data.empList.length; i++) {
            let item = searchRes.data.empList[i];
            // 设置搜索出的非离职人员数据
            if (item && item.empStatus != 4) {
              workerMap[item.workerNo] = item.workerNo;
              list.push({
                id: "p2p-" + item.workerNo,
                scene: "p2p",
                to: item.workerNo,
                detailInfo: setUserBaseInfo(item)
              });
            }
          }
          // 获取人员接口缓存数据
          let workerNos = Object.values(workerMap);
          remote.store.dispatch("getPersons", {account: workerNos, doneFlag: true}).then(res => {
            searchObj.value.list.map(item => {
              if (res[item.to] && res[item.to].empStatus != 4) {
                item.detailInfo = res[item.to];
              }
            });
          });
        }
      }
      // 群
      // 讨论组
      let teamInfo = remote.store.getters.getTeams({val: val});
      teamInfo.group.map(item => {
        list.push({
          id: "team-" + item.teamId,
          scene: "team",
          to: item.teamId,
          detailInfo: item
        });
      });
      teamInfo.team.map(item => {
        list.push({
          id: item.detailType + "-" + item.teamId,
          scene: item.detailType,
          to: item.teamId,
          detailInfo: item
        });
      });
      // 标签
      let labelList = labelObj.value.person.concat(labelObj.value.customer);
      list = list.concat(labelList.filter(item => {return new RegExp(regReplace(val), "i").test(item.name)}));
      searchObj.value.list = list;
      searchObj.value.flag = true;
    }

    // 改变层级
    function changeLevel(level, type, item) {
      levelObj.value.level = level;
      levelObj.value.type = type;
      levelObj.value.item = item;
      if (type == 1 && isForwardTeamMember.value) {
        // 转私聊
        levelObj.value.list = [];
        levelObj.value.loading = true;
        remote.store.dispatch("getTeamMembers", {id: item.to}).then(teamMembersObj => {
          let list = [];
          if (teamMembersObj.err) {
            toast({title: "获取群数据失败，请重试。" + teamMembersObj.err, type: 2});
            levelObj.value.list = list;
            return;
          }
          let workerNos = [];
          let teamMembers = sortTeamMembers(teamMembersObj.obj);
          teamMembers.map(item => {workerNos.push(item.account)})
          // 查询群成员数据
          remote.store.dispatch("getPersons", {account: workerNos, doneFlag: true}).then(res => {
            teamMembers.map(item => {
              if (res[item.account] && res[item.account].empStatus != 4) {
                list.push({
                  id: "p2p-" + item.account,
                  scene: "p2p",
                  to: item.account,
                  detailInfo: res[item.account]
                });
              }
            });
            levelObj.value.memberList = list;
            levelObj.value.list = list;
            levelObj.value.loading = false;
          });
        });
      }
    }

    // 同事、客户分类切换
    function changeLabelList(type) {
      labelObj.value.type = type;
      labelObj.value.show = false;
      labelObj.value.more = false;
      if (type == 1) {
        labelObj.value.list = labelObj.value.person;
      } else if (type == 2) {
        labelObj.value.list = labelObj.value.customer;
      }
      nextTick(() => {
        // 存在滚动范围显示展开按钮
        if (forwardLabelUlRef.value.scrollHeight > forwardLabelUlRef.value.clientHeight) {
          labelObj.value.more = true;
        }
      });
    }

    // 切换显示标签列表
    function toggleShowList(item) {
      if (item.uuid) {
        item.show = !item.show;
      }
    }

    // 选择整个群成员
    function selAllTeamMember() {
      let {flag, list} = isSelAllTeamMember();
      if (!flag) {
        // 非全选状态-选择全部群成员
        let num = getForwardInfo();
        if (list.length + num > forwardMax.value) {
          alert({
            content: `转发消息最多只能选择${forwardMax.value}个联系人！当前全部选中人数为${list.length + num}人！`,
            showCancel: false,
          });
          return;
        }
      } else {
        // 全选状态-取消选择全部群成员
        list = levelObj.value.memberList;
      }
      // 选中全部群成员
      list.map(item => {
        selItem(item);
      });
    }

    // 判断是否全选
    function isSelAllTeamMember() {
      let list = [];
      let flag = false;
      levelObj.value.memberList.map(item => {
        if (!selSessionMap.value["p2p-" + item.to]) {
          list.push(item);
        }
      });
      if (list.length == 0) {
        flag = true;
      }
      return {flag: flag, list: list};
    }

    // 权限
    async function getForwardPur() {
      if (userInfo.workerId == "88888888" || userInfo.managerId == "88888888") {
        // 林董和直属不判断
        isForward.value = true;
        forwardMax.value = isForward.value ? 50 : 10;
        isForwardTeamMember.value = true;
      } else {
        let res = await addTagPowerApi({
          msgBody: JSON.stringify({workerId: userInfo.workerId}),
        });
        if (res.data && res.data.data) {
          isForward.value = true;
          forwardMax.value = isForward.value ? 50 : 10;
        }
        let queryPurviewRes = await queryPurviewApi({
          msgBody: JSON.stringify({
            linkUrl: "/aicpMain/query_purview/zhuansl"
          }),
        });
        if (queryPurviewRes.data && queryPurviewRes.data.data) {
          isForwardTeamMember.value = true;
        }
      }
    }

    // 判断是否关闭窗口
    function setWindowClose() {
      for (let i = 0; i < forwardStatusList.value.length; i++) {
        if (forwardStatusList.value[i].status != "success") {
          break;
        }
        if (i == forwardStatusList.value.length - 1 && !otherText.value) {
          setTimeout(() => {
            // 关闭窗口
            winClose();
          }, 1000);
        }
      }
    }

    // 最小化
    function winMin() {
      remote.store.commit("setWindowMin", currentWindow.cWindow.id);
    }

    // 关闭窗口
    function winClose() {
      remote.store.commit("setWindowClose", currentWindow.cWindow.id);
    }

    // 全局鼠标按下事件
    function mousedownEvent(e) {
      if (e.path.some(elm => /win-drag/.test(elm.className)) && !e.path.some(elm => /win-no-drag/.test(elm.className))) {
        let bounds = currentWindow.getBounds();
        remote.global.dragObj = {x: e.x, y: e.y, width: bounds.width, height: bounds.height};
      }
    }

    // 全局鼠标释放事件
    function mouseupEvent(e) {
      remote.global.dragObj = "";
    }

    // 全局鼠标移动事件
    function mousemoveEvent(e) {
      // 移动窗口
      if (remote.global.dragObj) {
        if (!e.which) {
          remote.global.dragObj = "";
        }
        if (currentWindow.isFullscreen) {
          remote.store.commit("setWindowCancelMax", currentWindow.cWindow.id);
        }
        currentWindow.moveTo(e.screenX - remote.global.dragObj.x, e.screenY - remote.global.dragObj.y)
      }
    }

    return {
      sessionList,
      customerList,
      forwardStatusList,
      selSessionMap,
      isForwardStatus,
      otherText,
      searchVal,
      searchObj,
      levelObj,
      labelObj,
      searchListRef,
      isForward,
      forwardMax,
      forwardMsgMax,
      forwardMsg,
      forwardLabelUlRef,
      forwardObj,
      forwardAllNum,
      isForwardTeamMember,

      avatarError,
      selItem,
      forwardBtn,
      reForward,
      getPrimaryMsg,
      getProgress,
      getStatus,
      searchInput,
      getSelSessionList,
      getForwardInfo,
      changeLevel,
      changeLabelList,
      toggleShowList,
      getFileIcon,
      dealMem,
      selAllTeamMember,
      isSelAllTeamMember,
      winMin,
      winClose,
    }
  }
}
</script>
<style scoped lang="scss">
.child {
  width: 100%;
  height: 100%;
  background: #FFFFFF;

  .operate-box {
    width: 100%;
    z-index: 101;

    &.child-operate-box {
      .operate-nav {
        top: 4px !important;
      }
    }

    &.child-collect-router {
      .operate-nav {
        z-index: 79;
      }
    }

    .drag-area {
      position: fixed;
      top: 2px;
      left: 2px;
      width: calc(100% - 4px);
      height: 30px;
    }

    .operate-nav {
      position: fixed;
      top: 0px;
      right: 6px;
      display: flex;
      z-index: 351;

      i {
        width: 30px;
        height: 30px;
        cursor: pointer;
        background-image: url(/img/icon_operate.png);
        background-repeat: no-repeat;
        background-size: 240px 30px;
      }

      .min {
        &:hover {
          background-position: -30px 0;
        }
      }

      .max {
        background-position: -60px 0;

        &:hover {
          background-position: -90px 0;
        }

        &.show-max {
          background-position: -120px 0;

          &:hover {
            background-position: -150px 0;
          }
        }
      }

      .close {
        background-position: -180px 0;

        &:hover {
          background-position: -210px 0;
        }
      }
    }
  }
}

.forward {
  width: 100%;
  height: 100%;
  border: 1px solid #D8D8D8;

  .title {
    font-size: 14px;
    color: #999999;
    padding: 8px 10px;
    font-weight: normal;
  }

  .msg-file {
    overflow: hidden;

    .file-info-box {
      display: flex;
      padding: 10px;
      border-bottom: 1px solid #e8e8e8;
      min-width: 240px;
      max-width: 240px;
      line-height: 20px;
      font-size: 12px;

      .file-icon {
        width: 42px;
        height: 42px;
        border-radius: 2px;
        margin-right: 12px;
      }

      .file-content {
        max-width: calc(100% - 54px);

        span {
          display: inline-block;
        }

        .file-name {
          display: flex;
          align-items: center;
          color: #252525;

          .name {
            max-width: 130px;
          }
        }

        .file-size {
          color: #999;
        }
      }
    }

    progress {
      display: block;
      width: 100%;
      height: 3px;
      border: none;

      &::-webkit-progress-bar {
        background-color: #FFFFFF;
      }

      &::-webkit-progress-value {
        background-color: #B2E281;
      }
    }

    .file-status-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 30px;
      padding: 0 10px;

      .file-status {
        color: #808080;
      }

      .file-operate-box {
        display: flex;
        align-items: center;

        .file-operate {
          color: #2D92E6;
          margin-left: 15px;
          cursor: pointer;
        }
      }
    }
  }

  .forward-ul {
    width: 100%;
    height: calc(100% - 35px);
    display: flex;

    .forward-li {
      width: 50%;
      height: 100%;
      flex-shrink: 0;
    }

    .forward-list-box {
      border-right: 1px solid #F2F2F2;
      padding-left: 20px;

      .search-input-box {
        width: calc(100% - 20px);
        height: 25px;
        background: #EDEDED;
        position: relative;
        margin-bottom: 10px;

        .search-input {
          padding: 0 30px 0 10px;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0);
          border: 1px solid #E1E1E1;

          &:focus {
            background-color: #FFFFFF;
            border-color: rgba(82, 168, 236, 0.8);
            outline: 0;
            box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
          }
        }

        .search-btn {
          position: absolute;
          top: 1px;
          right: 1px;
          width: 30px;
          height: calc(100% - 2px);
          background: rgba(0, 0, 0, 0) url("/img/forward/search.png") no-repeat center center;
          background-size: 36%;
          cursor: pointer;
        }
      }

      .label-person-nav {
        display: flex;
        justify-content: space-between;
        padding: 10px 45px 0;
        border-bottom: 1px solid #F2F2F2;
        margin-right: 20px;

        li {
          position: relative;
          padding-bottom: 6px;
          font-size: 13px;
          color: #666666;
          line-height: 14px;
          cursor: pointer;

          &.curr {
            color: $styleColor;

            &:after {
              content: "";
              width: 40px;
              height: 1px;
              background: $styleColor;
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }

      .level-box {
        width: 100%;
        height: 100%;

        .back {
          display: inline-flex;
          align-items: center;
          padding: 20px 10px 20px 0;
          cursor: pointer;
          font-size: 13px;
          color: #666666;

          &:before {
            width: 12px;
            height: 12px;
            content: "";
            background: url("/img/forward/back.png") no-repeat center;
            background-size: 12px 12px;
            margin-right: 3px;
          }
        }
      }

      .forward-person-box {
        width: 100%;
        height: calc(100% - 25px - 10px - 31px);
        overflow-y: scroll;

        &.forward-person-box-search {
          height: calc(100% - 25px - 10px);
        }

        &.forward-person-box-team {
          height: calc(100% - 57px);
        }

        .loading-img-box {
          height: calc(100% - 25px - 10px - 19px);
          display: flex;
          justify-content: center;
          align-items: center;

          .loading-img {
            width: 32px;
          }
        }

        .forward-person-list {
          width: calc(100% - 10px);
          display: flex;
          justify-content: space-between;
          align-items: center;
          cursor: pointer;

          &:hover {
            background-color: rgb(245, 245, 245);

            .user-info-box {
              .info-btn {
                display: block;
              }
            }
          }

          .user-info-box {
            width: calc(100% - 32px);
            height: 48px;
            display: flex;
            align-items: center;

            .user-avatar-box {
              width: 34px !important;
              height: 34px !important;
              margin-right: 8px;
            }

            .content-box {
              font-size: 14px;
              flex: 1;
            }

            .info-btn {
              display: none;
              margin: 0 4px 0 16px;
              flex-shrink: 0;
              line-height: 17px;
              color: $styleColor;
              border-radius: 2px;
              border: 1px solid $styleColor;
              padding: 1px 3px;
            }
          }

          .check-box {
            width: 20px;
            height: 40px;
            margin-right: 12px;
            background-image: url("/img/forward/check.png");
            background-repeat: no-repeat;
            background-size: 80px 40px;
            background-position: -10px center;
            flex-shrink: 0;

            &.check {
              background-position: -50px center;
            }
          }

          .arrow {
            width: 40px;
            height: 40px;
            background-image: url("/img/forward/enter.png");
            background-repeat: no-repeat;
            background-size: 15px 15px;
            background-position: center center;
            flex-shrink: 0;
          }
        }

        .label-person-box {
          margin-right: 20px;

          .label-title-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #333333;
            line-height: 17px;
            margin-top: 12px;

            .label-toggle {
              position: relative;
              color: $styleColor;
              padding-right: 10px;
              cursor: pointer;

              &.show {
                &:after {
                  border-width: 0 4px 4px 4px;
                  border-color: transparent transparent $styleColor transparent;
                }
              }

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 4px 0 4px;
                border-style: solid;
                border-color: $styleColor transparent transparent transparent;
              }
            }
          }

          .forward-label-ul {
            display: flex;
            flex-wrap: wrap;
            max-height: 62px;
            overflow: hidden;

            &.label-show {
              max-height: none;
            }

            .forward-label-li {
              line-height: 17px;
              padding: 2px 8px;
              color: #58595B;
              background: #F4F4F4;
              border-radius: 27px;
              border: 1px solid #D8D8D8;
              margin: 8px 8px 0 0;
              cursor: pointer;

              &.check {
                color: $styleColor;
                background: #FFF4F4;
                border: 1px solid $styleColor;
              }
            }
          }
        }

        .forward-person-title {
          color: #333333;
          line-height: 17px;
          margin: 20px 0 2px;
        }

        .forward-team-box {
          padding-right: 20px;

          .forward-team-name-box {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 12px 0 2px;
            line-height: 17px;
            color: #333333;

            .forward-team-name {
              width: calc(100% - 50px);
              display: flex;
              align-items: center;

              .forward-team-num {
                flex-shrink: 0;
              }
            }

            .forward-team-all {
              width: 50px;
              color: $styleColor;
              cursor: pointer;
              flex-shrink: 0;
              text-align: right;
            }
          }
        }
      }
    }

    .forward-sel-list-box {
      padding-left: 20px;

      .pr20 {
        padding-right: 20px;
      }

      .forward-msg {
        width: 100%;
        height: 62px;
        display: flex;
        align-items: center;
        padding: 10px;
        background: rgb(244, 244, 244);
        border-radius: 2px;
        margin-bottom: 10px;
        font-size: 13px;
        overflow-y: hidden;

        .msg-intr {
          width: 100%;
        }
      }

      .forward-text {
        width: 100%;
        height: 28px;
        line-height: 28px;
        padding-left: 10px;
        border: 1px solid rgb(198, 197, 199);
      }

      .forward-intr {
        padding-top: 5px;
        text-align: right;
        color: rgb(165, 165, 164);
      }

      .forward-tips {
        color: #666666;
        line-height: 17px;
        margin-top: 8px;
      }

      .forward-detail {
        font-size: 13px;
        color: rgb(165, 165, 164);
        margin: 8px 0 10px 0;
      }

      .forward-person-sel-box {
        height: 262px;
        overflow-y: scroll;
        margin-bottom: 10px;
        padding-right: 20px;

        .forward-person-sel-list {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          cursor: default;

          .user-info-box {
            width: 100%;

            &.user-info-label {
              .user-info-content {
                &:hover {
                  color: $styleColor;

                  .label-toggle {
                    &.show {
                      &:after {
                        border-color: transparent transparent $styleColor transparent;
                      }
                    }

                    &:after {
                      border-color: $styleColor transparent transparent transparent;
                    }
                  }
                }
              }
            }

            .user-info-content {
              width: 100%;
              height: 24px;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .user-info-content-box {
                width: calc(100% - 32px);
                display: flex;
                align-items: center;

                .user-avatar-box {
                  width: 24px !important;
                  height: 24px !important;
                  cursor: default !important;
                  margin-right: 8px;
                }
              }

              .label-toggle {
                margin-left: 2px;
                position: relative;
                width: 10px;
                height: 100%;
                flex-shrink: 0;

                &.show {
                  &:after {
                    border-width: 0 4px 4px 4px;
                    border-color: transparent transparent #000000 transparent;
                  }
                }

                &:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  width: 0;
                  height: 0;
                  border-width: 4px 4px 0 4px;
                  border-style: solid;
                  border-color: #000000 transparent transparent transparent;
                }
              }

              .delete-box {
                width: 16px;
                height: 16px;
                background-image: url("/img/forward/delete.png");
                background-repeat: no-repeat;
                background-size: 32px 16px;
                background-position: 0 0;
                cursor: pointer;
                flex-shrink: 0;

                &:hover {
                  background-position: -16px 0;
                }
              }
            }

            .user-info-ul {
              padding: 0 32px;

              .user-info-li {
                display: flex;
                align-items: center;
                margin-top: 8px;

                &:first-child {
                  margin-top: 6px;
                }

                &:last-child {
                  margin-bottom: 4px;
                }

                .user-avatar-box {
                  width: 16px !important;
                  height: 16px !important;
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }

      .forward-person-sel-none {
        height: 262px;
        overflow-y: scroll;
        margin: 0 20px 10px 0;
        background: url("/img/forward/tips.png") no-repeat 0 100px;
        background-size: 100%;
      }

      .forward-btn-box {
        text-align: right;

        .btn {
          width: 68px;
          height: 26px;
          line-height: 22px;
          cursor: pointer;
          border-radius: 1px;
          color: rgb(255, 255, 255);
          background: $styleColor;
          border: 1px solid $styleColor;
          margin-left: 10px;
          border-radius: 4px;

          &:disabled {
            opacity: .6;
            cursor: default;
          }

          &.btn-cancel {
            color: rgb(81, 81, 81);
            background: rgb(255, 255, 255);
            border: 1px solid rgb(229, 229, 229);
            cursor: default;
          }
        }
      }
    }
  }

  .forward-status-box {
    width: 100%;
    height: calc(100% - 35px);

    li:nth-child(1) {
      width: calc(100% - 200px - 150px);
    }

    li:nth-child(2) {
      width: 200px;
      flex-shrink: 0;
    }

    li:nth-child(3) {
      width: 150px;
      flex-shrink: 0;
    }

    .forward-status-header {
      display: flex;
      line-height: 24px;
      background: #FAFAFA;
      font-weight: bold;

      li {
        padding-left: 27px;
      }
    }

    .forward-status-body {
      height: calc(100% - 24px);
      overflow-y: auto;

      .forward-status-body-box {
        display: flex;

        &:nth-child(odd) {
          background: #FFFFFF;
        }

        li {
          padding: 13px 0 13px 27px;

          .user-info-box {
            display: flex;
            align-items: center;

            .user-avatar-box {
              width: 24px !important;
              height: 24px !important;
              cursor: default !important;
              margin-right: 16px;
            }
          }

          progress {
            position: relative;
            width: 80%;
            height: 4px;
            border: none;
            border-radius: 4px;

            &::-webkit-progress-bar {
              background: #EBEBEB;
            }

            &::-webkit-progress-value {
              background-color: #3888FF;
            }
          }

          .status {
            color: #C6C5C7;
            display: flex;
            align-items: center;

            &.fail {
              color: #1AAAF0;
              cursor: pointer;

              &:before {
                background-image: url("/img/forward/status.png");
                background-position: -14px 0;
              }
            }

            &.success {
              &:before {
                background-image: url("/img/forward/status.png");
                background-position: 0 0;
              }
            }

            &:before {
              display: inline-block;
              content: "";
              width: 14px;
              height: 14px;
              margin-right: 5px;
              background-repeat: no-repeat;
              background-position: 0 0;
              background-size: 28px 14px;
            }
          }
        }
      }
    }

  }
}
</style>
