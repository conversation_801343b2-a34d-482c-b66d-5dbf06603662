<template>
  <div class="notify">
    <div class="notify-box" ref="notifyRef">
      <div class="notify-details" v-for="(item,key) in showNotifyList" :key="item.id" @click="openChat(item.id)">
        <div class="notify-header">
          <img class="app-icon" src="/img/tray.png" alt="">
          <div>乐聊</div>
          <div class="point"></div>
          <div>{{ item.showTime }}</div>
          <i v-if="showNotifyMap[item.id]&&showNotifyMap[item.id].toggle" @click.stop="toggleShowText(item.id,$event)"
             class="icon toggle-icon" :class="{'hide':!showNotifyMap[item.id].show }" :title="(showNotifyMap[item.id].show?'收起':'展开')+'通知'"></i>
          <i class="icon close-icon" @click.stop="closeNotify(item.id)" title="关闭"></i>
        </div>
        <div class="notify-content">
          <div class="notify-title">{{ item.detailInfo.name }}</div>
          <div class="notify-text" :class="showNotifyMap[item.id]&&!showNotifyMap[item.id].show?'textEls':'textEls4'" :title="item.showText"
               :data-id="item.id">{{ item.scene == "p2p" ? "" : item.userInfo.name + "：" }}{{ item.showText }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>

import {nextTick, ref} from 'vue'

export default {
  name: "notify",
  setup(props, ctx) {
    let notifyRef = ref();
    let remote = window.global.mainWin.window.nw;
    let currentWindow = window.nw.Window.get();
    window.global.childWin["notify"] = currentWindow;
    setWin({width: 0, height: 0, x: 99999, y: 99999});
    // 显示通知列表-最多显示3个
    let showNotifyList = ref([]);
    // 显示通知列表的时间
    let showNotifyMap = ref({});
    // 通知总列表
    let notifyMap = ref({});
    // 是否启动隐藏窗口定时器
    let timerFlag = true;
    // 悬浮定时器
    let mouseTimer = "";

    currentWindow.setShowInTaskbar(false)
    setInterval(() => {
      currentWindow.setShowInTaskbar(false);
    }, 10 * 1000);

    // 鼠标移入当前窗口
    document.addEventListener("mousemove", function (e) {
      if (currentWindow.width > 0) {
        resetTimer(false);
        if (mouseTimer) {
          clearTimeout(mouseTimer);
        }
        mouseTimer = setTimeout(() => {
          // 25s无操作重置定时器
          resetTimer(true);
        }, 25 * 1000);
      }
    });

    // 鼠标点击后重置定时器
    document.addEventListener("mouseup", function (e) {
      resetTimer(true);
    });

    // 鼠标移出当前窗口重置定时器
    document.addEventListener("mouseleave", function (e) {
      resetTimer(true);
    });

    // 主窗口调用方法
    window.emitMsg = function (arg) {
      switch (arg.type) {
        case "notifyMap":
          // 通知列表
          notifyMap.value = arg.value;
          setNotifyList();
          break;
        case "delNotifyMap":
          // 删除对应通知列表
          closeTimer(arg.value);
          break;
      }
      // 没有通知窗口重置定时器
      if (Object.keys(notifyMap.value).length == 0) {
        timerFlag = true;
      }
      currentWindow.setShowInTaskbar(false);
    }

    // 切换显示文字高度
    function toggleShowText(id, e) {
      showNotifyMap.value[id].show = !showNotifyMap.value[id].show;
      nextTick(() => {
        setNotifyPos(e.screenY);
      })
    }

    // 设置窗口显示通知列表-最多3个
    function setNotifyList() {
      let list = Object.values(notifyMap.value).sort((a, b) => {return a.showCount - b.showCount});
      let showList = [];
      for (let i = 0; i < 3; i++) {
        // 插入显示列表
        if (list[i]) {
          showList[i] = list[i];
          // 设置会话显示的时间
          if (!showNotifyMap.value[list[i].id]) {
            showNotifyMap.value[list[i].id] = {
              show: true
            };
          }
          // 默认不显示切换文字高度
          showNotifyMap.value[list[i].id].toggle = false;
          // 显示时间和消息最后一条时间不一致，重置定时器
          if (showNotifyMap.value[list[i].id].showTime != list[i].lastMsg.time) {
            showNotifyMap.value[list[i].id].showTime = list[i].lastMsg.time;
            // 启动窗口关闭定时器
            startTimer(list[i].id);
          }
        }
      }
      showNotifyList.value = showList.reverse();
      nextTick(() => {
        setNotifyPos();
      });
    }

    // 重置窗户隐藏定时器
    function resetTimer(flag) {
      timerFlag = flag;
      for (let i = 0; i < showNotifyList.value.length; i++) {
        startTimer(showNotifyList.value[i].id);
      }
    }

    // 启动窗口关闭定时器
    function startTimer(id) {
      closeTimer(id);
      // 鼠标不悬浮在通知内才启动定时器
      if (timerFlag) {
        // 25s后移除
        ((id) => {
          showNotifyMap.value[id].timer = setTimeout(() => {
            closeNotify(id);
          }, 25 * 1000);
        })(id);
      }
    }

    // 关闭窗口关闭定时器
    function closeTimer(id) {
      clearTimeout(showNotifyMap.value[id].timer);
      showNotifyMap.value[id].timer = "";
    }

    // 设置窗口位置
    function setNotifyPos(screenY) {
      let screenInfo = currentWindow.window.nw.Screen.screens[0];
      let width = notifyRef.value.clientWidth;
      let height = notifyRef.value.clientHeight;
      let padding = 32;
      let x = screenInfo.work_area.width + screenInfo.work_area.x - width - padding;
      let y = screenInfo.work_area.height + screenInfo.work_area.y - height - padding;

      width = showNotifyList.value.length == 0 ? 0 : width + 16;
      height = showNotifyList.value.length == 0 ? 0 : height + 16;

      if (showNotifyList.value.length == 0) {
        setWin({width: 0, height: 0, x: 99999, y: 99999});
      } else {
        currentWindow.setAlwaysOnTop(true);
        if (screenY < y) {
          // 鼠标移出当前窗口
          setTimeout(() => {
            setWin({width: width, height: height, x: x, y: y});
          }, 100);
        } else {
          setWin({width: width, height: height, x: x, y: y});
        }
      }

      // 计算窗口是否显示切换文字高度按钮
      let elms = notifyRef.value.querySelectorAll(".notify-text");
      for (let i = 0; i < elms.length; i++) {
        let computeStyle = window.getComputedStyle(elms[i]);
        let lineHeight = parseInt(computeStyle["line-height"].replace(/px/g, ""));
        // 超过一行就显示
        if (lineHeight < elms[i].scrollHeight || elms[i].clientWidth < elms[i].scrollWidth) {
          let id = elms[i].getAttribute("data-id");
          showNotifyMap.value[id].toggle = true;
        }
      }
    }

    // 关闭通知窗口
    function closeNotify(id) {
      // 恢复显示全部文字状态
      showNotifyMap.value[id].show = true;
      try {
        remote.Window.get().window.store.dispatch("setNotification", {type: "del", session: {id: id}});
      } catch (e) {
        remote.require("nw.gui").Window.getAll(function (remotes) {
          remotes[0].Window.get().window.store.dispatch("setNotification", {type: "del", session: {id: id}});
        });
      }
    }

    // 打开会话
    function openChat(id) {
      closeNotify(id);
      try {
        remote.Window.get().window.store.dispatch("setOpenNotify", {id: id});
      } catch (e) {
        remote.require("nw.gui").Window.getAll(function (remotes) {
          remotes[0].window.store.dispatch("setOpenNotify", {id: id});
        });
      }
    }

    // 移动窗口位置
    function setWin(param) {
      currentWindow.setResizable(true);
      try {
        currentWindow.x = parseInt(param.x);
      } catch (e) {}
      try {
        currentWindow.y = parseInt(param.y);
      } catch (e) {}
      try {
        currentWindow.width = parseInt(param.width);
      } catch (e) {}
      try {
        currentWindow.height = parseInt(param.height);
      } catch (e) {}
      currentWindow.setResizable(false);
    }

    return {
      notifyRef,
      showNotifyList,
      showNotifyMap,

      toggleShowText,
      closeNotify,
      openChat,
    }
  }
}
</script>
<style>
#app {
  width: 100%;
  height: 100%;
}
</style>
<style scoped lang="scss">
.notify {
  width: 100%;
  height: 100%;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 8px;

  .notify-box {
    width: 370px;
    padding: 2px;
    flex-shrink: 0;

    .notify-details {
      width: 100%;
      padding: 12px 16px;
      background: #FFFFFF;
      border: 1px solid #CCCCCC;
      box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.3);
      border-radius: 2px;
      margin-bottom: 16px;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .notify-header {
        display: flex;
        align-items: center;
        color: #666666;
        line-height: 17px;
        margin-bottom: 10px;

        .app-icon {
          width: 16px;
          margin-right: 8px;
        }

        .icon {
          width: 16px;
          height: 16px;
          background-image: url("/img/notify/icon.png");
          background-size: 96px 16px;
          background-position: 0 0;
          cursor: pointer;

          &.toggle-icon {
            margin-left: 8px;

            &:hover {
              background-position: -16px 0;
            }

            &.hide {
              background-position: -32px 0;

              &:hover {
                background-position: -48px 0;
              }
            }
          }

          &.close-icon {
            position: absolute;
            top: 10px;
            right: 10px;
            background-position: -64px 0;

            &:hover {
              background-position: -80px 0;
            }
          }
        }

        .point {
          margin: 0 8px;
          width: 3px;
          height: 3px;
          border-radius: 50%;
          background: #D8D8D8;
        }
      }

      .notify-content {
        .notify-title {
          font-size: 14px;
          font-weight: bold;
          line-height: 20px;
          margin-bottom: 1px;
        }

        .notify-text {
          font-size: 14px;
          color: #666666;
          line-height: 20px;
        }
      }
    }
  }
}
</style>