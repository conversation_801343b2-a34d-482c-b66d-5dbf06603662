<template>
  <div class="viewer win-drag">
    <!--选择文件/文件夹区域-->
    <input id="fileInput" ref="fileInputRef" type="file" @change="changeFileInput">
    <!--拖拽窗口-->
    <div class="viewer-move">
      <div class="viewer-move-text">按住可拖动</div>
    </div>
    <!--分辨率过高提示-->
    <div class="viewer-tips" v-show="thisImg.thum">
      <span>该图片分辨率过大，已进行压缩处理，建议可用</span>
      <span class="highlight" @click="openLocalFile(thisImg.src)">本地图片</span>
      <span>形式打开</span>
    </div>
    <!--关闭窗口-->
    <i class="viewer-close" @click="winClose()"></i>
    <!--缩放提示-->
    <span class="radio-tips" v-show="imgChangeObj.showRadio">{{ parseInt(imgChangeObj.radio * 100) }}%</span>
    <!--上下张图片切换-->
    <div class="viewer-page viewer-page-pre">
      <i class="page-icon page-pre" :class="{'disabled':imgObj.index==0}" @click="changeShowImg(-1)">
        <span class="page-tips">{{ imgObj.index == 0 ? '已经是第一张' : ((imgObj.index + 1) + '/' + imgObj.list.length) }}</span>
      </i>
    </div>
    <div class="viewer-page viewer-page-next">
      <i class="page-icon page-next" :class="{'disabled':imgObj.index==imgObj.list.length-1}" @click="changeShowImg(1)">
        <span class="page-tips">{{ imgObj.index == imgObj.list.length - 1 ? '已经是最后一张' : ((imgObj.index + 1) + '/' + imgObj.list.length) }}</span>
      </i>
    </div>
    <!--当前图片-->
    <img v-if="imgObj.type!='video'" src="/img/image_default.png" @mousedown="picMove($event,1)" @mousemove="picMove($event,2)"
         @mouseup="picMove($event,3)" @contextmenu="setMenu($event)" @load="loadImage" :onerror="errorImage" @click="clickImage" ref="imgRef"
         :style="'transform:scale('+imgChangeObj.radio+') rotate('+imgChangeObj.rotate+'deg);margin-top:'+imgChangeObj.marginTop+'px;margin-left:'+imgChangeObj.marginLeft+'px'">
    <!--图片操作-->
    <div v-if="imgObj.type!='video'" class="viewer-footer">
      <ul class="viewer-toolbar" :class="{'show':isMax}">
        <li class="viewer-full-page" title="全屏" v-show="!isMax" @click="winMax()"></li>
        <li class="viewer-off-page" title="退出全屏" v-show="isMax" @click="winMax()"></li>
        <li class="viewer-zoom-in" title="放大" @click="changeRadio(1)"></li>
        <li class="viewer-zoom-out" title="缩小" @click="changeRadio(-1)"></li>
        <li class="viewer-one-to-one" title="1:1" @click="changeRadio(2)"></li>
        <li class="viewer-reset" title="重置" @click="resetAll()"></li>
        <li class="viewer-rotate-right" title="顺时针旋转" @click="changeRotate()"></li>
        <li class="viewer-save" title="保存" @click="openFolder(1)"></li>
      </ul>
      <div class="viewer-pic-list" v-show="isMax" ref="viewerPicRef">
        <i class="viewer-icon left" v-show="isPicPre" @click="showPicPage(-1)"></i>
        <i class="viewer-icon right" v-show="isPicNext" @click="showPicPage(1)"></i>
        <ul class="pic-ul" v-if="isShowPicList">
          <li class="pic-li" v-for="(item,key) in imgObj.list" :key="key" :class="{'curr':key==imgObj.index}" @click="changeShowImg(key-imgObj.index)">
            <img :src="item.src||getImageQualityUrl(item.dataSrc,'',1)">
          </li>
        </ul>
      </div>
    </div>
    <!--视频-->
    <video v-if="imgObj.type=='video'" :src="thisImg.src" autoplay="autoplay" type="video/mp4" controls="controls" id="video"></video>
  </div>
</template>
<script>
import {jQueryMethod} from "@comp/schedule/jquery";
import {ref, watch, nextTick, inject, onUnmounted} from "vue";
import {
  showMenu, selElm, openLocalFile, addMenu, getGlobal, openForward, getBase64Image, getLocalFile, emitMsg, getImageQualityUrl, loadCache, getFileExt, getBase64Ext,
  MD5, saveImageLocal, getFileCachedPath, heicToJpg,
} from "@utils"
import {dataURLToImage} from "@utils/imgCompress";
import {alert, toast, loading} from "@comp/ui";

const fs = remote.require("fs");
const path = remote.require("path");

export default {
  name: "Template",
  setup(props, ctx) {
    jQueryMethod();
    let config = remote.store.getters.getConfig.config;
    let userInfo = remote.store.getters.getUserInfo;
    // 当前窗口
    let currentWindow = remote.store.getters.getCurrentWindow("viewer");
    // 是否最大化
    let isMax = ref(false);
    // 是否显示图片列表-最大化一次后显示
    let isShowPicList = ref(false);
    // 图片列表元素
    let viewerPicRef = ref();
    // 图片元素
    let imgRef = ref();
    // 是否显示左滚动
    let isPicPre = ref(false);
    // 是否显示右滚动
    let isPicNext = ref(false);
    // 拖拽窗口移动信息
    let moveObj = ref({});
    // 图标被旋转缩放信息
    let imgChangeObj = ref({});
    // 图片列表
    let imgObj = ref(getGlobal("viewerImgObj"));
    // 当前图片信息
    let thisImg = ref({});
    let loadImgKey = "";
    let loadImgNum = 0;
    let winCenter = false;
    let globalEmit = inject("globalEmit");
    // 打开文件元素
    let fileInputRef = ref();
    // 全局按键监听
    document.addEventListener("keydown", keydownEvent);
    // 全局鼠标按下监听
    document.addEventListener("mousedown", mousedownEvent);
    // 全局鼠标释放监听
    document.addEventListener("mouseup", mouseupEvent);
    // 全局鼠标移动监听
    document.addEventListener("mousemove", mousemoveEvent);
    // 全局鼠标滚轮监听
    document.addEventListener("mousewheel", mousewheelEvent);
    // 卸载去除监听
    onUnmounted(() => {
      // 全局按键监听
      document.removeEventListener("keydown", keydownEvent);
      // 全局鼠标按下监听
      document.removeEventListener("mousedown", mousedownEvent);
      // 全局鼠标释放监听
      document.removeEventListener("mouseup", mouseupEvent);
      // 全局鼠标移动监听
      document.removeEventListener("mousemove", mousemoveEvent);
      // 全局鼠标滚轮监听
      document.removeEventListener("mousewheel", mousewheelEvent);
    })
    // 重新加载图片
    initImage();
    // 加载水印
    remote.store.commit("setWaterMark", {window: window});
    // 加载埋点
    remote.store.commit("loadTjs", {window: window});

    // 初始化图片
    function initImage() {
      winCenter = true;
      // 设置隐藏窗口
      emitMsg("msg", {type: "global", setGlobal: 1, info: {winImgHide: false}});
      isMax.value = false;
      isShowPicList.value = false;
      isPicPre.value = false;
      isPicNext.value = false;
      moveObj.value = {
        winW: 0,
        winH: 0,
        flag: false,//是否移动窗口
        startX: 0,// 开始坐标
        startY: 0,
        endX: 0,// 结束坐标
        endY: 0,
        picFlag: false,//是否移动窗口---图片
        picStartX: 0,// 开始坐标
        picStartY: 0,
        picEndX: 0,// 结束坐标
        picEndY: 0,
        maxW: 0,// 窗口最小宽度
        maxH: 0,// 窗口最大宽度
      };
      imgChangeObj.value = {
        naturalRadio: 1,// 原始比例
        radio: 1,//缩放比例100%
        showRadio: false,// 显示当前缩放比例提示
        showRadioTimer: "",// 显示提示定时器
        naturalWidth: 0,// 图片原始宽度
        naturalHeight: 0,// 图片原始高度
        marginTop: 0,// 图片滑动上边距
        marginLeft: 0,// 图片滑动左边距
        tempMarginTop: 0,// 临时图片滑动上边距
        tempMarginLeft: 0,// 临时图片滑动左边距
        rotate: 0,// 图片旋转
      }
      imgObj.value = getGlobal("viewerImgObj");
      if (imgObj.value.type == "video") {
        winInit(imgObj.value.width, imgObj.value.height);
      } else {
        let windW = imgObj.value.width > 600 ? imgObj.value.width : 600;
        let windH = imgObj.value.height > 400 ? imgObj.value.height : 400;
        remote.store.commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, width: windW, height: windH, minW: 600, minH: 400, center: true});
      }
      // 当前图片信息
      thisImg.value = imgObj.value.list[imgObj.value.index];
      if (imgRef.value) {
        imgRef.value.src = "/img/image_default.png";
      }
    }

    // 重新加载图片
    watch(() => globalEmit.value.reloadImg,
      (newValue, oldValue) => {
        initImage();
        currentWindow.blur();
        nextTick(() => {
          currentWindow.focus();
        });
      }, {
        deep: true
      }
    );
    // 当前窗口拖拽缩放
    watch(() => globalEmit.value.resize,
      (newValue, oldValue) => {
        // 重新获取窗口大小
        let bounds = currentWindow.getBounds();
        moveObj.value.winW = bounds.width;
        moveObj.value.winH = bounds.height;
        // 全屏被手动缩小改变状态
        if (isMax.value && !currentWindow.isMaximized) {
          isMax.value = false;
        }
      }, {
        deep: true
      }
    );
    // 打开文件元素
    watch(() => globalEmit.value.fileInput,
      (newValue, oldValue) => {
        // 另存为
        fileInputRef.value.setAttribute("nwsaveas", newValue.nwsaveas);
        nextTick(() => {
          fileInputRef.value.click();
        });
      }, {
        deep: true
      }
    );

    // 初始化窗口-isOneToOne1:1场景不改变图片信息
    function winInit(w, h, isOneToOne) {
      if (!isOneToOne) {
        imgChangeObj.value.radio = 1;
        imgChangeObj.value.naturalRadio = 1;
        imgChangeObj.value.rotate = 0;
        imgChangeObj.value.naturalWidth = w;
        imgChangeObj.value.naturalHeight = h;
      }
      let bounds = currentWindow.getBounds();
      // 当前窗口大小
      let winW = bounds.width;
      let winH = bounds.height;
      // 图片宽高大于窗口宽高则修改窗口大小
      if ((w > winW || h > winH) && !isMax.value) {
        winW = w;
        winH = h;
      }
      let radioWH = winW / winH;
      // 屏幕大小
      let screenW = screen.availWidth;
      let screenH = screen.availHeight;
      let radioW = winW / screenW;
      let radioH = winH / screenH;
      // 超出屏幕大小重新设置窗口大小
      if (radioW > 1 || radioH > 1) {
        if (radioW > radioH) {
          if (!isOneToOne) {
            imgChangeObj.value.radio = screenW / winW;
            imgChangeObj.value.naturalRadio = screenW / winW;
          }
          winW = screenW;
          if (radioH >= 1) {
            winH = screenW / radioWH;
          }
        } else {
          if (!isOneToOne) {
            imgChangeObj.value.radio = screenH / winH;
            imgChangeObj.value.naturalRadio = screenH / winH;
          }
          winH = screenH;
          if (radioW >= 1) {
            winW = screenH * radioWH;
          }
        }
      }
      // 窗口最小大小600*400
      if (winW < 600) {
        winW = 600;
      }
      if (winH < 400) {
        winH = 400;
      }
      if (moveObj.value.winW < winW || moveObj.value.winH < winH) {
        winCenter = true;
      }
      moveObj.value.winW = winW;
      moveObj.value.winH = winH;
      remote.store.commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, width: winW, height: winH, minW: 600, minH: 400, center: winCenter});
    }

    // 关闭窗口
    function winClose() {
      remote.store.commit("setWindowClose", currentWindow.cWindow.id);
      if (imgObj.value.type == "video") {
        thisImg.value = {};
      }
      // 设置隐藏窗口
      emitMsg("msg", {type: "global", setGlobal: 1, info: {winImgHide: true}});
    }

    // 移动图片
    function picMove(e, type) {
      if (type == 1) {
        moveObj.value.picFlag = true;
        moveObj.value.picStartX = e.x;
        moveObj.value.picStartY = e.y;
        imgChangeObj.value.tempMarginLeft = imgChangeObj.value.marginLeft;
        imgChangeObj.value.tempMarginTop = imgChangeObj.value.marginTop;
      } else if (type == 3) {
        moveObj.value.picFlag = false;
        imgChangeObj.value.tempMarginLeft = 0;
        imgChangeObj.value.tempMarginTop = 0;
      }
      // 不是拖拽状态
      if (!moveObj.value.picFlag) {
        return;
      }
      moveObj.value.picEndX = e.x;
      moveObj.value.picEndY = e.y;
      let bounds = currentWindow.getBounds();
      // 当前图片大小
      let picW = parseInt(imgChangeObj.value.naturalWidth * imgChangeObj.value.radio);
      let picH = parseInt(imgChangeObj.value.naturalHeight * imgChangeObj.value.radio);
      if (imgChangeObj.value.rotate / 90 % 2 != 0) {
        // 被旋转
        let tempW = picW;
        picW = picH;
        picH = tempW;
      }
      // 图片超出窗口大小则移动图片
      if (picW > bounds.width || picH > bounds.height) {
        e.stopPropagation();
        // 最大移动的宽高
        let maxMarinW = picW - bounds.width;
        let maxMarinH = picH - bounds.height;
        // 鼠标滑动的间距
        let diffX = (moveObj.value.picEndX - moveObj.value.picStartX) * 2 + imgChangeObj.value.tempMarginLeft;
        let diffY = (moveObj.value.picEndY - moveObj.value.picStartY) * 2 + imgChangeObj.value.tempMarginTop;
        if (maxMarinW < 0) {
          // 不可移动
          diffX = 0;
        } else if (diffX != 0 && Math.abs(diffX) > maxMarinW) {
          // 大于可滑动距离
          diffX = diffX / Math.abs(diffX) * maxMarinW;
        }
        if (maxMarinH < 0) {
          diffY = 0;
        } else if (diffY != 0 && Math.abs(diffY) > maxMarinH) {
          // 大于可滑动距离
          diffY = diffY / Math.abs(diffY) * maxMarinH;
        }
        imgChangeObj.value.marginLeft = diffX;
        imgChangeObj.value.marginTop = diffY;
      }
    }

    // 改变图片缩放比例 1放大 -1缩小 2为1:1 3为重置
    function changeRadio(key, e) {
      if (key == 2) {
        imgChangeObj.value.radio = 1;
        let w = imgChangeObj.value.naturalWidth;
        let h = imgChangeObj.value.naturalHeight;
        if (imgChangeObj.value.rotate / 90 % 2 != 0) {
          // 被旋转
          let temp = w;
          w = h;
          h = temp;
        }
        imgChangeObj.value.marginTop = 0;
        imgChangeObj.value.marginLeft = 0;
        winInit(w, h, true);
        return;
      } else if (key == -1 || key == 1) {
        // imgChangeObj.value.marginTop = 0;
        // imgChangeObj.value.marginLeft = 0;
      }
      let oldRadio = imgChangeObj.value.radio;
      let radio = oldRadio + key / 10;
      if (radio >= 5) {
        radio = 5;
      }
      if (radio <= 0.1) {
        radio = 0.1
      }
      imgChangeObj.value.radio = radio;
      if (imgChangeObj.value.showRadioTimer) {
        clearTimeout(imgChangeObj.value.showRadioTimer);
      }
      imgChangeObj.value.showRadio = true;
      imgChangeObj.value.showRadioTimer = setTimeout(() => {
        imgChangeObj.value.showRadio = false;
        imgChangeObj.value.showRadioTimer = "";
      }, 2000);
    }

    // 旋转图片
    function changeRotate() {
      imgChangeObj.value.rotate += 90;
      imgChangeObj.value.marginTop = 0;
      imgChangeObj.value.marginLeft = 0;
    }

    // 切换上/下一张图片
    function changeShowImg(key) {
      winCenter = false;
      let index = imgObj.value.index + key;
      if (index < 0 || index > imgObj.value.list.length - 1) {
        return;
      }
      imgObj.value.index = index;
      thisImg.value = imgObj.value.list[imgObj.value.index];
      imgChangeObj.value.marginTop = 0;
      imgChangeObj.value.marginLeft = 0;
      scrollLi(viewerPicRef.value.querySelector(".pic-ul"), ".pic-li");
      imgRef.value.setAttribute("src", "/img/image_default.png");
      imgRef.value.setAttribute("pre-src", "");
      remote.store.commit("setImageLoadTimer", {type: "del", key: "viewer"});
    }

    // 设置图片原图
    function setImageSrc(src, thumFlag) {
      if (src && thumFlag) {
        // 显示缩略图
        return path.join(getFileCachedPath({type: 2, account: userInfo.workerNo}), src.slice(src.lastIndexOf("\\") + 1));
      } else {
        return (src || "").replace(`\\cached\\${userInfo.workerNo}\\images\\thum\\`, `\\cached\\${userInfo.workerNo}\\images\\`).replace("file:\/\/\/", "");
      }
    }

    // 重置图片状态
    function resetAll() {
      imgChangeObj.value.rotate = 0;
      imgChangeObj.value.radio = imgChangeObj.value.naturalRadio;
      imgChangeObj.value.marginTop = 0;
      imgChangeObj.value.marginLeft = 0;
    }

    // 加载图片
    async function loadImage(e) {
      let thisElm = e.target;// 初始化窗口
      loadImgKey = "" + Date.now() + loadImgNum;
      let thisLoadImgKey = loadImgKey;
      loadImgNum++;
      thisElm.dataset.src = thisImg.value.dataSrc;
      let defaultSrc = location.origin + "/img/image_default.png";
      if (thisElm.src != defaultSrc) {
        winInit(thisElm.naturalWidth, thisElm.naturalHeight);
      }
      if (/data:image\//.test(thisElm.src) || /file:\/\/\//.test(thisElm.src)) {
        return;
      }
      let thisElmSrc = thisImg.value.dataSrc;
      // 不存在图片的实际宽高获取
      if (!thisImg.value.w || !thisImg.value.h) {
        let img = await dataURLToImage(thisElmSrc);
        thisImg.value.w = img.naturalWidth;
        thisImg.value.h = img.naturalHeight;
      }
      let localSrc = setImageSrc(thisImg.value.src);
      let isExistImage = fs.existsSync(localSrc);
      if (isExistImage) {
        thisElmSrc = localSrc;
      }
      thisImg.value.thum = false;
      // 超过1w*1w分辨率显示压缩图
      let isShowThum = thisImg.value.w * thisImg.value.h > 10000 * 10000;
      // 查看大图加载错误后 缓存到本地判断是否heic文件
      if (isShowThum || (thisImg.value.saveFlag && thisElm.getAttribute("pre-src"))) {
        let localThumSrc = setImageSrc(thisImg.value.src, true);
        let thisMd5 = MD5(thisImg.value.dataSrc);
        // 非本地文件查询记录
        if (!/file:\/\/\//.test(thisImg.value.src)) {
          // 查询缓存记录
          let fileCache = await remote.store.getters.getFileDB.query("md5", thisMd5);
          // 本地缓存记录
          let fileInfo = fileCache && fileCache.length > 0 ? fileCache[0] : {};
          if (fileInfo.path) {
            thisImg.value.src = fileInfo.path + fileInfo.name;
            localSrc = setImageSrc(thisImg.value.src);
            localThumSrc = setImageSrc(thisImg.value.src, true);
            isExistImage = fs.existsSync(localSrc);
          }
        }
        let isExistThumImage = fs.existsSync(localThumSrc);
        if (isExistImage && isExistThumImage) {
          if (isShowThum) {
            thisElmSrc = localThumSrc;
            thisImg.value.thum = true;
          } else {
            thisElmSrc = thisImg.value.src;
          }
        } else {
          if (isExistImage) {
            // 存在原图不存在压缩图
            let fileIndex = thisImg.value.src.lastIndexOf("\\") + 1;
            let thumRes = await saveImageLocal({path: thisImg.value.src.slice(0, fileIndex), name: thisImg.value.src.slice(fileIndex)});
            if (thumRes && isShowThum) {
              thisElmSrc = localThumSrc;
              thisImg.value.thum = true;
            } else {
              thisElmSrc = thisImg.value.src;
            }
          } else {
            // 不存在原图重新下载
            let res = await loadCache({url: thisImg.value.dataSrc, ext: getExt(), fileDB: remote.store.state.fileDB, isDownLoad: true, getSuccess: true});
            if (res.downloadFileObj) {
              let resObj = await res.downloadFileObj;
              if (resObj.state == "success") {
                isExistImage = true;
                thisImg.value.src = resObj.path + resObj.name;
                if (isShowThum) {
                  thisImg.value.thum = true;
                  thisElmSrc = localThumSrc;
                } else {
                  thisElmSrc = thisImg.value.src;
                }
              }
            }
          }
        }
      }
      // 不是同个图片加载不执行后续
      if (thisLoadImgKey != loadImgKey) {
        return;
      }
      if (isExistImage) {
        remote.store.commit("setImageLoadTimer", {type: "del", key: "viewer"});
        if (new RegExp(location.origin + "/img/image_default.png").test(thisElmSrc)) {
          thisElm.setAttribute("src", thisElmSrc);
        } else {
          // 判断是否heic图片，是则转为jpg
          await heicToJpg({localSrc: thisElmSrc});
          thisElm.setAttribute("src", thisElmSrc);
        }
      } else {
        // 加载图片
        remote.store.dispatch("setReloadImage", {thisElm: thisElm, thisElmSrc: thisElmSrc, timerKey: "viewer", time: 12 * 1000, type: 2, notChange: true});
      }
    }

    // 图片加载失败
    function errorImage(e) {
      let thisElm = e.target;
      // 当前图片是base64不继续加载
      if (/data:image\//.test(thisElm.src)) {
        return;
      }
      // 加载失败如果存在定时器继续加载
      if (remote.store.getters.getImageLoadTimer["viewer"]) {
        thisElm.setAttribute("pre-src", thisElm.src);
        thisElm.setAttribute("pre-time", Date.now());
        thisElm.setAttribute("src", `/img/image_default.png?reloadFlag=true`);
      } else {
        // 停止加载图片
        thisElm.setAttribute("src", `/img/image_reload.png`);
      }
    }

    // 点击图片
    function clickImage(e) {
      let thisElm = e.target;
      if (thisElm.src == location.origin + "/img/image_reload.png") {
        // 重新加载失败图片
        thisElm.setAttribute("src", "/img/image_default.png?reload=true");
      }
    }

    // 最大化
    function winMax() {
      if (currentWindow.isFullscreen) {
        isMax.value = false;
        remote.store.commit("setWindowCancelMax", currentWindow.cWindow.id);
      } else {
        isMax.value = true;
        remote.store.commit("setWindowMax", currentWindow.cWindow.id);
        // 全屏显示图片列表
        isShowPicList.value = true;
        nextTick(() => {
          scrollLi(viewerPicRef.value.querySelector(".pic-ul"), ".pic-li");
        });
      }
    }

    // 打开文件夹/另存为，openType-1另存为
    function openFolder(openType) {
      let thisElm = "";
      if (new RegExp(location.origin).test(imgRef.value.src) || /^data:image\/\w+;base64,/.test(thisImg.value.dataSrc)) {
        thisElm = imgRef.value;
      }
      let thisExt = getExt();
      remote.store.dispatch("openFolder", {
        item: {file: {name: `乐聊图片${new Date().getTime()}.${thisExt}`, url: thisImg.value.dataSrc, ext: thisExt}},
        openType: openType,
        thisElm: thisElm,
        globalEmit: globalEmit.value,
        done: res => {
          if (res.state == "success") {
            toast({title: "下载成功", type: 1});
          } else if (res.state == "close" || res.state == "error") {
            toast({title: "下载失败,请重试", type: 2});
          }
        }
      })
    }

    function getExt() {
      if (thisImg.value.ext) {
        return thisImg.value.ext;
      } else {
        return config.imgBase64Reg.test(thisImg.value.dataSrc) ? getBase64Ext(thisImg.value.dataSrc) : (getFileExt(thisImg.value.dataSrc, 1) || "png");
      }
    }

    // 右键菜单
    let menu = {};

    async function setMenu(e) {
      selElm(e.target);

      let imgSize = thisImg.value.size ? thisImg.value.size / 1024 / 1024 : 0;

      let menuList = [];

      if (imgSize < 5) {
        menuList.push({
          label: "复制", click: function () {
            document.execCommand("copy");
          }
        });
        menuList.push({
          label: "另存为", click: function () {
            openFolder(1);
          }
        });
      }

      let localPath = setImageSrc((thisImg.value.src || "").indexOf("file:///") == 0 ? thisImg.value.src.replace("file:///", "") : thisImg.value.src);
      if (fs.existsSync(localPath)) {
        menuList.push({
          label: "本地打开", click: function () {
            openLocalFile(localPath);
          }
        });
      }

      if (imgSize && imgSize < 2) {
        if (!/data:image\//.test(thisImg.value.dataSrc)) {
          menuList.push({
            label: "收藏", click: async function () {
              let param = {};
              if (thisImg.value.isImg) {
                param = {
                  sourceName: thisImg.value.sessionInfo.detailInfo.name,
                  sourceIcon: thisImg.value.sessionInfo.detailInfo.avatar,
                  sourceId: thisImg.value.sessionInfo.to,
                  sessionInfo: thisImg.value.sessionInfo,
                  msg: thisImg.value.msg,
                }
              } else {
                param = {
                  sourceName: userInfo.workerName,
                  sourceIcon: userInfo.headPic,
                  sourceId: userInfo.workerNo,
                  sessionInfo: {scene: "p2p", to: userInfo.workerNo},
                  msg: {scene: "p2p", to: userInfo.workerNo, type: "custom", content: {type: "multi", msgs: [{type: "image", file: {url: thisImg.value.dataSrc}}]}},
                };
              }
              let res = await remote.store.dispatch("collectMsg", param);
              if (res.success) {
                toast({title: "收藏成功！", type: 1});
              } else {
                toast({title: "收藏失败。" + res.errorMsg, type: 2});
              }
            }
          });
          menuList.push({
            label: "转发", click: async function () {
              let thisImg = await getBase64Image(e.target);
              if (fs.existsSync(localPath)) {
                thisImg = await getLocalFile({filePath: localPath, base64: true});
              }
              let thisMsg = {
                msgBody: [{
                  type: "image",
                  url: thisImg,
                  height: e.target.naturalWidth,
                  width: e.target.naturalHeight,
                  file: {
                    url: thisImg
                  }
                }],
                forwardType: "sendMsg",
                forwardContent: `<div style="display: flex;align-items: center"><img src="${thisImg}" style="max-width: 36px;margin-right: 10px;">图片</div>`,
              }
              openForward(thisMsg);
            }
          });
        }
      }

      if (imgObj.value.index > 0) {
        menuList.push({
          label: "上一张", click: function () {
            changeShowImg(-1);
          }
        });
      }
      if (imgObj.value.index < imgObj.value.list.length - 1) {
        menuList.push({
          label: "下一张", click: function () {
            changeShowImg(1);
          }
        });
      }

      if (imgSize && imgSize < 2) {
        remote.store.dispatch("getQrcode", e.target).then(res => {
          if (res && res.data) {
            addMenu(menu, 0, {
              label: "识别二维码", click: function () {
                console.log("大图qrcode:", res.data);
                res.alert = alert;
                res.loading = loading;
                remote.store.dispatch("setQrcode", res);
              }
            }).popup(e.x, e.y);
          }
        });
      }

      menu = showMenu(menuList);
      menu.popup(e.x, e.y);
      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    // 滚动到可视区域
    function scrollLi(scrollBox, selector) {
      nextTick(() => {
        if (scrollBox) {
          let currLiElm = Array.prototype.find.call(scrollBox.querySelectorAll(selector), item => {return /curr/.test(item.className)});
          let firstLiElm = scrollBox.querySelector(selector);
          // 判断可视区域
          if (currLiElm) {
            if (currLiElm.offsetLeft + currLiElm.offsetWidth - firstLiElm.offsetLeft > scrollBox.scrollLeft + scrollBox.clientWidth) {
              scrollBox.scrollLeft = currLiElm.offsetLeft + currLiElm.offsetWidth - scrollBox.clientWidth - firstLiElm.offsetLeft;
            } else if (currLiElm.offsetLeft - firstLiElm.offsetLeft < scrollBox.scrollLeft) {
              scrollBox.scrollLeft = currLiElm.offsetLeft - firstLiElm.offsetLeft;
            }
          }
          isShowPicPage(scrollBox);
        }
      });
    }

    // 上下滑动图片列表
    function showPicPage(key) {
      let scrollBox = viewerPicRef.value.querySelector(".pic-ul");
      scrollBox.scrollLeft += key * scrollBox.clientWidth;
      isShowPicPage(scrollBox);
    }

    // 判断是否显示左右滚动
    function isShowPicPage(scrollBox) {
      // 判断是否显示左右滚动
      isPicPre.value = scrollBox.scrollLeft != 0;
      isPicNext.value = scrollBox.scrollLeft + scrollBox.clientWidth < scrollBox.scrollWidth;
    }

    // 全局按键事件
    function keydownEvent(e) {
      switch (e.key) {
        case "ArrowUp":
          // 放大图片
          changeRadio(1);
          break;
        case "ArrowDown":
          // 缩小图片
          changeRadio(-1);
          break;
        case "ArrowLeft":
          // 上一张图
          changeShowImg(-1);
          break;
        case "ArrowRight":
          // 下一张图
          changeShowImg(1);
          break;
        case "Escape":
          // 关闭窗口
          winClose();
          break
      }
    }

    // 全局鼠标按下事件
    function mousedownEvent(e) {
      if (e.path.some(elm => /win-drag/.test(elm.className)) && !e.path.some(elm => /win-no-drag/.test(elm.className))) {
        let bounds = currentWindow.getBounds();
        remote.global.dragObj = {x: e.x, y: e.y, width: bounds.width, height: bounds.height};
      }
    }

    // 全局鼠标释放事件
    function mouseupEvent(e) {
      remote.global.dragObj = "";
      moveObj.value.flag = false;
      moveObj.value.picFlag = false;
      currentWindow.setMinimumSize(600, 400);
    }

    // 全局鼠标移动事件
    function mousemoveEvent(e) {
      // 移动窗口
      if (remote.global.dragObj) {
        if (!e.which) {
          remote.global.dragObj = "";
        }
        if (currentWindow.isFullscreen) {
          remote.store.commit("setWindowCancelMax", currentWindow.cWindow.id);
        }
        currentWindow.moveTo(e.screenX - remote.global.dragObj.x, e.screenY - remote.global.dragObj.y)
      }
    }

    // 全局鼠标滚轮事件
    function mousewheelEvent(e) {
      changeRadio(e.wheelDelta > 0 ? 1 : -1, e);
    }

    // 选择文件/文件夹
    function changeFileInput(e) {
      if (globalEmit.value.fileInput && globalEmit.value.fileInput.done) {
        globalEmit.value.fileInput.done(e.target.files)
      }
      e.target.value = "";
    }

    return {
      winClose,
      picMove,
      loadImage,
      errorImage,
      clickImage,
      winMax,
      changeRadio,
      changeRotate,
      resetAll,
      openFolder,
      setMenu,
      changeShowImg,
      showPicPage,
      getImageQualityUrl,
      changeFileInput,
      openLocalFile,

      imgObj,
      moveObj,
      thisImg,
      imgChangeObj,
      isMax,
      isPicPre,
      isPicNext,
      isShowPicList,
      viewerPicRef,
      imgRef,
      fileInputRef,
    }
  }
}
</script>
<style scoped lang="scss">
#copyDivElm {
  z-index: -1;
  position: fixed;
  left: -10000;
  top: -10000;
}

.viewer {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.40);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move;

  &:hover {
    .viewer-footer {
      .viewer-toolbar {
        height: 46px;
      }

      .viewer-pic-list {
        height: 50px;
      }
    }
  }

  .viewer-move {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    line-height: 50px;
    text-align: center;
    z-index: 1;

    &:hover {
      .viewer-move-text {
        display: block;
      }
    }

    .viewer-move-text {
      display: none;
      width: 100%;
      height: 100%;
      font-size: 16px;
      color: #FFFFFF;
      background: rgba(0, 0, 0, 0.5);
    }
  }

  .viewer-tips {
    position: fixed;
    top: 60px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.5);
    padding: 6px 10px;
    color: #FFFFFF;
    line-height: 22px;
    border-radius: 4px;
    z-index: 10;
    cursor: default;

    .highlight {
      color: $styleLink;
      padding: 0 2px;
      cursor: pointer;
      font-weight: bold;
    }
  }

  .viewer-close {
    position: fixed;
    right: 5px;
    top: 5px;
    width: 38px;
    height: 38px;
    z-index: 99;
    border-radius: 50%;
    background: rgb(57, 58, 60);
    cursor: pointer;
    transition: all .15s ease-in-out;

    &:hover {
      background: rgb(212, 63, 39);
    }

    &:before {
      content: "";
      width: 2px;
      height: 20px;
      position: absolute;
      top: 50%;
      left: 50%;
      background: #FFFFFF;
      margin: -10px 0 0 -1px;
      border-radius: 1px;
      transform: rotate(-45deg);
    }

    &:after {
      content: "";
      width: 2px;
      height: 20px;
      position: absolute;
      top: 50%;
      left: 50%;
      background: #FFFFFF;
      margin: -10px 0 0 -1px;
      border-radius: 1px;
      transform: rotate(45deg);
    }
  }

  .radio-tips {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    color: #FFFFFF;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, .8);
    z-index: 1;
  }

  .viewer-footer {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -2px;
    z-index: 1;

    .viewer-toolbar {
      display: inline-flex;
      height: 0;
      position: relative;
      left: 50%;
      transform: translateX(-50%);
      padding: 0 10px;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 3px 3px 0 0;
      transition: all .15s ease-in-out;
      cursor: default;
      overflow: hidden;

      &.show {
        height: 46px;
      }

      li {
        width: 36px;
        height: 100%;
        background-image: url("/img/viewer/icon.png");
        background-repeat: no-repeat;
        background-size: 736px 56px;

        &.viewer-full-page {
          background-size: 572px 43px;
          background-position: 0 center;

          &:hover {
            background-position: -286px center;
          }
        }

        &.viewer-off-page {
          background-size: 572px 43px;
          background-position: -36px center;

          &:hover {
            background-position: -322px center;
          }
        }

        &.viewer-zoom-in {
          background-position: -96px -8px;

          &:hover {
            background-position: -464px -8px;
          }
        }

        &.viewer-zoom-out {
          background-position: -143px -8px;

          &:hover {
            background-position: -511px -8px;
          }
        }

        &.viewer-one-to-one {
          background-position: -189px -8px;

          &:hover {
            background-position: -557px -8px
          }
        }

        &.viewer-reset {
          background-position: -235px -8px;

          &:hover {
            background-position: -603px -8px
          }
        }

        &.viewer-rotate-right {
          background-position: -280px -8px;

          &:hover {
            background-position: -648px -8px
          }
        }

        &.viewer-save {
          background-position: -326px -8px;

          &:hover {
            background-position: -694px -8px
          }
        }
      }
    }

    .viewer-pic-list {
      width: 100%;
      height: 0;
      position: relative;
      overflow: hidden;

      .viewer-icon {
        width: 18px;
        height: 36px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-image: url("/img/viewer/icon_page1.png");
        background-color: rgba(0, 0, 0, 0.5);
        background-repeat: no-repeat;
        background-size: 36px 18px;
        background-position: 0 center;
        cursor: pointer;
        z-index: 1;

        &:hover {
          background-color: rgba(0, 0, 0, 1);
        }

        &.left {
          left: 0;
        }

        &.right {
          right: 0;
          background-position: -18px center;
        }
      }

      .pic-ul {
        width: 100%;
        height: 50px;
        display: flex;
        overflow: hidden;
        flex-shrink: 0;
        transition: all .15s ease-in-out;
        border-top: 1px solid #FFFFFF;

        .pic-li {
          width: 100px;
          height: 100%;
          opacity: .5;
          cursor: pointer;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;
          border: 2px solid transparent;
          flex-shrink: 0;
          cursor: pointer;

          &.curr {
            border: 2px solid #FFFFFF;
            opacity: 1;
          }

          img {
            max-width: 100%;
            max-height: 100%;
          }
        }
      }
    }
  }

  .viewer-page {
    position: fixed;
    top: 0;
    width: 70px;
    height: 100%;
    z-index: 1;

    &:hover {
      .page-icon {
        display: block;

        &:hover {
          .page-tips {
            display: block;
          }
        }
      }
    }

    &.viewer-page-pre {
      left: 0;

      .page-pre {
        &.disabled {
          background-position: -63px 0;
        }

        .page-tips {
          left: 70px;
        }
      }
    }

    &.viewer-page-next {
      right: 0;

      .page-next {
        background-position: -126px 0;

        &.disabled {
          background-position: -189px 0;
        }

        .page-tips {
          right: 70px;
        }
      }
    }

    .page-icon {
      display: none;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      background-image: url("/img/viewer/icon_page.png");
      background-repeat: no-repeat;
      background-size: 250px 60px;
      cursor: pointer;
      transition: all .15s ease-in-out;

      .page-tips {
        display: none;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        font-size: 12px;
        padding: 5px;
        border-radius: 2px;
        white-space: nowrap;
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
      }
    }
  }

  img {
    cursor: grab;
  }

  video {
    padding: 0px 70px;
    height: 100%;
    width: 100%;
    color: #000;
    background: #000000;
    cursor: default;
  }
}
</style>