# Mac ARM (Apple Silicon) 支持说明

本项目已针对 Mac ARM 系统（Apple Silicon，如 M1、M2、M3 芯片）进行了优化。

## 🚀 快速开始

### 方法一：使用预构建的 ARM64 版本
1. 下载 `release` 目录中的 `乐聊-4.0.0-arm64.dmg` 文件
2. 双击安装 DMG 文件
3. 将应用拖拽到 Applications 文件夹

### 方法二：从源码构建
```bash
# 安装依赖
npm install

# 构建 ARM64 版本
npm run electron:build:arm64

# 或使用专用构建脚本
node build-mac-arm.js
```

## 🔧 系统要求

- **操作系统**: macOS 10.15 (Catalina) 或更高版本
- **处理器**: Apple Silicon (M1/M2/M3) 或 Intel x64
- **内存**: 至少 4GB RAM
- **存储**: 至少 200MB 可用空间

## 🛠️ 构建选项

项目提供了多种构建选项：

```bash
# 构建通用版本（同时支持 ARM64 和 x64）
npm run electron:build:universal

# 仅构建 ARM64 版本
npm run electron:build:arm64

# 仅构建 x64 版本
npm run electron:build:x64

# 构建所有 Mac 版本
npm run electron:build
```

## 🔒 安全设置

由于 macOS 的安全机制，首次运行可能遇到以下问题：

### 问题 1: "无法打开应用，因为它来自身份不明的开发者"
**解决方案:**
1. 打开 `系统偏好设置` > `安全性与隐私`
2. 在 `通用` 选项卡中，点击 `仍要打开`
3. 或者在终端中执行：
   ```bash
   sudo spctl --master-disable
   ```

### 问题 2: 应用被隔离
**解决方案:**
在终端中执行以下命令移除隔离属性：
```bash
xattr -cr /Applications/乐聊.app
```

### 问题 3: 权限问题
**解决方案:**
确保应用具有必要的权限：
```bash
# 给予执行权限
chmod +x /Applications/乐聊.app/Contents/MacOS/乐聊

# 如果需要，重新签名应用
codesign --force --deep --sign - /Applications/乐聊.app
```

## ⚡ Apple Silicon 优化

项目已针对 Apple Silicon 进行了以下优化：

1. **原生 ARM64 支持**: 应用以原生 ARM64 代码运行，性能更佳
2. **GPU 加速**: 启用了 Apple Silicon 的 GPU 加速功能
3. **内存优化**: 针对 Apple Silicon 的内存架构进行了优化
4. **电池续航**: 优化了电源管理，延长电池续航时间

## 🐛 故障排除

### 应用无法启动
1. 检查系统版本是否满足要求
2. 确保已安装最新版本的 macOS
3. 尝试重新安装应用

### 性能问题
1. 确保使用的是 ARM64 版本而非 x64 版本
2. 检查活动监视器中应用的架构类型
3. 重启应用或系统

### 网络连接问题
1. 检查防火墙设置
2. 确保应用具有网络访问权限
3. 尝试重置网络设置

## 📞 技术支持

如果遇到问题，请提供以下信息：

1. **系统信息**:
   ```bash
   system_profiler SPHardwareDataType | grep "Chip\|Model"
   sw_vers
   ```

2. **应用信息**:
   ```bash
   file /Applications/乐聊.app/Contents/MacOS/乐聊
   ```

3. **错误日志**: 在 `控制台.app` 中查看相关错误信息

## 🔄 更新说明

- **v4.0.0**: 首次支持 Apple Silicon
- 后续版本将继续优化 ARM64 性能

## 📝 开发说明

如果您是开发者，以下信息可能有用：

### 依赖版本
- Electron: 30.x (支持 Apple Silicon)
- electron-builder: 25.x (支持 ARM64 构建)
- Node.js: 建议使用 18.x 或更高版本

### 构建环境
```bash
# 检查当前架构
arch
uname -m

# 安装 ARM64 版本的 Node.js（如果需要）
# 从 https://nodejs.org 下载对应版本
```

### 调试
```bash
# 启用详细日志
DEBUG=electron-builder npm run electron:build:arm64

# 检查构建产物
file release/*.dmg
```
