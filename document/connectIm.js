// 乐聊浏览器通讯源码,不暴露被外部访问-使用工具加密http://toolin.cn/obfuscator
/**
 * 3.100 http://172.16.3.100/front_cdn/leyoujiaIm/connectIm.js
 * itest https://itest.leyoujia.com/front_cdn/leyoujiaIm/connectIm.js
 * uat https://onlinetest.leyoujia.com/front_cdn/leyoujiaIm/connectIm.js
 * 生产 https://front.leyoujia.com/front_cdn/leyoujiaIm/connectIm.js
 *
 * 参数 param.type 通讯类型（必传） param.timeout 请求超时时间，默认为10*1000ms（非必传）
 * 返回 success 调用接口是否成功 errorMsg 请求失败原因 data 响应数据
 * 请求 getConnectIm(param).then(function(res){});
 *
 * 目前已有通讯类型type
 * open：获取电脑设备加密信息串
 * attendCheck：获取是否正在使用远程工具
 * version：获取当前乐聊版本号
 * openChat：打开指定会话 参数value：type:"lyj-ai"//打开类型目前仅支持ai，scene:"p2p"//会话类型目前仅支持p2p，to:"ai00001"//打开会话id，appid:""// 打开会话应用id
 * */
function getConnectIm(data) {
  let dataStr = JSON.stringify(data);
  if (!window.getConnectImPromiseMap) {
    window.getConnectImPromiseMap = {};
  }
  // 存在请求的数据返回promise对象
  if (window.getConnectImPromiseMap[dataStr]) {
    return window.getConnectImPromiseMap[dataStr];
  }

  window.getConnectImPromiseMap[dataStr] = new Promise(function (resolve) {
    getImInfo(resolve);
  });

  // 连接Im方法
  function getImInfo(resolve, param) {
    if (!param) {
      param = {
        count: 1,
        maxCount: 5,// 最大重试次数
        timer: "",// 超时定时器
        timeout: data.timeout || 10 * 1000,// 请求超时时间
        resData: {},// 响应数据
      }
    }
    var isTest = location.host != "i.leyoujia.com";
    // 获取设备
    var deviceWs = new WebSocket("wss://localhost.leyoujia.com:" + (isTest ? "29287" : "29286"));
    deviceWs.onopen = function () {
      deviceWs.send(dataStr);
      getImTimer(deviceWs, param);
    }
    deviceWs.onmessage = function (msg) {
      let resData = msg.data;
      try {
        resData = JSON.parse(resData)
      } catch (e) {}
      // 不存在该方法
      if (resData.status == -1) {
        param.count = -1;
      } else {
        // 返回响应数据
        param.count = -200;
        param.resData = resData;
      }
      deviceWs.close();
    }
    // 连接关闭事件
    deviceWs.onclose = function () {
      deviceWs = "";
      if (param.count > param.maxCount) {
        // 默认重试maxCount次
        resolve({
          success: false,
          errorMsg: "请打开乐聊后重试",
          errorCode: "pc-im-0",
        });
        delete getConnectImPromiseMap[dataStr];
      } else if (param.count == -1) {
        // 乐聊老版本没响应
        resolve({
          success: false,
          errorMsg: "请升级乐聊后重试",
          errorCode: "pc-im-2",
        });
        delete getConnectImPromiseMap[dataStr];
      } else if (param.count == -200) {
        // 响应成功
        if (typeof param.resData == "string") {
          // 兼容历史返回字符串数据
          resolve({
            success: true,
            errorMsg: "",
            errorCode: "",
            data: param.resData
          });
        } else {
          resolve({
            success: param.resData.success,
            errorMsg: param.resData.errorMsg,
            errorCode: param.resData.errorCode,
            data: param.resData.data
          });
        }
        delete getConnectImPromiseMap[dataStr];
      } else {
        // 重试继续连接
        if (param.count == 1) {
          // 打开乐聊
          if (isTest) {
            location.href = "jjsimlocaltest://login";
          } else {
            location.href = "jjsim://login";
          }
        }
        param.count++;
        getImInfo(resolve, param);
      }
    }
    //窗口关闭时，关闭连接
    window.unload = function () {
      deviceWs.close();
    }
  }

  // 超时没响应关闭连接
  function getImTimer(deviceWs, param) {
    if (param.timer) {
      clearTimeout(param.timer);
      param.timer = "";
    }
    param.timer = setTimeout(function () {
      param.count = -1;
      deviceWs && deviceWs.close();
    }, param.timeout);
  }

  // 返回请求对象
  return window.getConnectImPromiseMap[dataStr];
}