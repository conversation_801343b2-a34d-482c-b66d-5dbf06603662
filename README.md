# 禁止项目用jquery，store禁止外部直接调用state，必须用get和set

# 禁止新增utils/index.js公用方法 需要新增联系詹熏培确认是否有该方法

# 内核说明

使用nwjs内核，已知 nw0.83.0收消息异常，websocket通讯慢 nw0.64.0发送消息概率闪退 nw0.52.2概率断网，并且粘贴大量代码到输入框会闪退

# 启动说明

node版本14.18.0 npm版本6.14.15

npm i安装依赖，npm源https://registry.npmjs.org/

改变运行环境在package的env,otherEnv为3个环境的变量名，慎用生产环境

打开控制台ctrl+alt+shift+p快捷键

# 发版说明

发版地址：https://i.leyoujia.com/im/upload/index

json文件配置：name固定为JJS_IM，target固定为installer，version为发版版本号，incVersion为最低热更新版本号，env为发版环境，enforce=2为强更，yxUrl为安装包云信地址，yxIncUrl为热更新包云信地址

热更新包在dist/win-ia32-unpacked.zip，可根据最低热更新版本号后新增的img文件手动变更热更新包大小，包括node_modules/js/sdk/tools等文件

# 运行方式

nwjs用idea启动环境变量为NODE_ENV=development

npm run serve

# 打包方式

## 升级包

npm run build env=dev version=*******

## 备注

config.json为参数配置文件

# z-index

toast-400 loading-350 textmenu-300 alert组件-100 输入框提示-99 最大化最小化-99 win-drag拖拽-101 锁定乐聊-100 编辑弹窗-90 搜索收藏弹窗89 弹窗组件-80-多个弹窗会依次+1 日程弹窗-51 霸屏消息-52

# 规范

###### 1.全部function参数都为param的json参数，方便后期拓展

###### 2.图片存放在public/img格式为a_b.png下划线格式，class格式为a-b,id格式为小驼峰为idName,变量格式为小驼峰userName

###### 3.setup的使用方法分层从上往下 生命周期=>ref/reactive变量=>watch/computed=>function=>return

###### 4.层级z-index关系在readme里标记

###### 6.拖拽在class上加上win-drag，不需要拖拽的加上win-no-drag

###### 7.通用方法名在文件最顶部加入描述

###### 8.全局通讯用store的emit

# 云信sdk说明

###### 1、变更云信sdk后，由于需要ws日志，需要在 websocket.onmessage方法中加入try{remote.store.state.logger.writeLogFile(5,e.data)}catch(e){};