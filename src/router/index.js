import {createRouter, createWebHashHistory} from "vue-router"

// 登录页
const Login = () => import(/* webpackChunkName: "chunk-index" */ "@view/Login.vue")
// 首页
const Index = () => import(/* webpackChunkName: "chunk-index" */ "@view/Index.vue")
// 消息
const Chat = () => import(/* webpackChunkName: "chunk-index" */ "@comp/chat/ChatBox.vue")
// 标签
const MailList = () => import(/* webpackChunkName: "chunk-index" */ "@comp/mail/index.vue")
// 智能日程
const SmartSchedule = () => import(/* webpackChunkName: "chunk-index" */ "@comp/schedule/SmartSchedule.vue")
// 更多
const More = () => import(/* webpackChunkName: "chunk-index" */ "@comp/more/MoreBox.vue")


/**
 * 以下为子窗口
 * **/
  // 子窗口
const Child = () => import(/* webpackChunkName: "chunk-child" */ "@view/Child.vue")
// 搜索聊天记录
const SearchMsg = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/SearchMsg.vue")
// 合并转发页面
const MergeForward = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/MergeForward.vue")
// 聊天子窗口
const ChildChat = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/ChildChat.vue")
// 网络连接
const NetConnect = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/NetConnect.vue")
// 网络连接-新
const NetConnectNew = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/NetConnectNew.vue")
// 临时电脑授权
const NetConnectTemp = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/NetConnectTemp.vue")
// 我的收藏
const Collect = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/Collect.vue")
// 设置
const Setting = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/setting.vue")
// 提取消息记录
const codeRecord = () => import(/* webpackChunkName: "chunk-child" */ "@comp/child/codeRecord.vue")

const routes = [
  {
    path: "/",
    name: "Login",
    component: Login
  }, {
    path: "/index",
    name: "Index",
    children: [{
      path: "",
      name: "IndexDefault",
      redirect: "/index/chat"
    }, {
      path: "chat",
      name: "Chat",
      component: Chat
    }, {
      path: "mailList",
      name: "MailList",
      component: MailList
    }, {
      path: "smartSchedule",
      name: "SmartSchedule",
      component: SmartSchedule
    }, {
      path: "more",
      name: "More",
      component: More
    }],
    component: Index
  }, {
    path: "/child",
    name: "Child",
    children: [{
      path: "collect",
      name: "collect",
      component: Collect,
      meta: {name: "收藏"}
    }, {
      path: "setting",
      name: "setting",
      component: Setting,
      meta: {name: "设置"}
    }, {
      path: "searchMsg",
      name: "searchMsg",
      component: SearchMsg,
      meta: {name: "消息记录"}
    }, {
      path: "mergeForward",
      name: "mergeForward",
      component: MergeForward,
      meta: {name: "合并转发"}
    }, {
      path: "childChat",
      name: "childChat",
      component: ChildChat,
      meta: {name: "子会话"}
    }, {
      path: "netConnect",
      name: "netConnect",
      component: NetConnect,
      meta: {name: "网络连接"}
    }, {
      path: "netConnectNew",
      name: "netConnectNew",
      component: NetConnectNew,
      meta: {name: "公司电脑认证"}
    }, {
      path: "netConnectTemp",
      name: "netConnectTemp",
      component: NetConnectTemp,
      meta: {name: "临时电脑授权"}
    }, {
      path: "codeRecord",
      name: "codeRecord",
      component: codeRecord,
      meta: {name: "消息记录"}
    }],
    component: Child
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
