<template>
  <div class="child">
    <router-view class="router" :class="{'child-chat-router':currRouter=='/child/childChat','child-collect-router':currRouter=='/child/collect'}"></router-view>
    <!--  顶部操作  -->
    <div class="operate-box" :class="{'child-operate-box':currRouter=='/child/childChat','child-collect-router':currRouter=='/child/collect'}" v-if="isShowOperate()">
      <div :class="['drag-area','win-drag',getResizable()?'':'win-no-resize']"></div>
      <div class="operate-nav win-no-drag">
        <i class="min" @click="winMin"></i>
        <i class="max" :class="isMax?'show-max':''" @click="winMax" v-if="isShowOperate(2)"></i>
        <i class="close" @click="winClose()"></i>
      </div>
    </div>
  </div>
</template>

<script>

import {useStore} from 'vuex'
import {watch, ref, onMounted, nextTick} from 'vue'
import {useRouter} from 'vue-router'

export default {
  name: "Child",
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();
    let userInfo = store.getters.getUserInfo;
    // 设置子窗口名字
    if (router.currentRoute.value.meta && router.currentRoute.value.meta.name) {
      document.title = router.currentRoute.value.meta.name;
    }
    // 初始化文件记录数据库
    store.commit("setFileDB", userInfo.workerNo);
    // 初始化路由
    store.commit("setRouter", router);
    // 初始化本地缓存数据-未读数、特别关心
    store.commit("setInitLocalStorage");
    // 是否最大化
    let isMax = ref(false);

    // 当前页面路径
    let currRouter = ref(router.currentRoute.value.path);
    watch(() => router.currentRoute.value.path,
      (newValue, oldValue) => {
        currRouter.value = newValue;
      }, {
        deep: true
      }
    )

    onMounted(() => {
      let bounds = currentWindow.getBounds();
      store.commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: bounds.x + 1, y: bounds.y + 1, width: bounds.width, height: bounds.height, minW: bounds.width, minH: bounds.height, resizable: getResizable()});
    });

    // 监听全局关闭窗口事件
    watch(() => store.state.emit.closeWindow,
      (newValue, oldValue) => {
        // closeWindow空时调用关闭窗口
        if (!newValue) {
          winClose();
        }
      }, {
        deep: true
      }
    );

    // 最小化
    function winMin() {
      store.commit("setWindowMin", currentWindow.cWindow.id);
    }

    // 最大化
    function winMax() {
      if (currentWindow.isFullscreen) {
        isMax.value = false;
        store.commit("setWindowCancelMax", currentWindow.cWindow.id);
      } else {
        isMax.value = true;
        store.commit("setWindowMax", currentWindow.cWindow.id);
      }
    }

    // 关闭窗口-emit.closeWindow不存在的时候关闭当前窗口
    function winClose() {
      if (!store.state.emit.closeWindow) {
        store.commit("setWindowClose", currentWindow.cWindow.id);
      } else {
        store.commit("setEmit", {type: "winCloseClick", value: Date.now()});
      }
    }

    // 是否显示顶部操作栏 type-2是否显示最大化、默认为是否显示整个导航
    function isShowOperate(type) {
      let flag = true;
      let currPath = router.currentRoute.value.path;
      if (!type) {
        if (currPath == "/child/viewer") {
          flag = false;
        }
      } else if (type == 2) {
        if (currPath == "/child/setting" || currPath == "/child/forward" || currPath == "/child/netConnect" || currPath == "/child/netConnectNew"|| currPath == "/child/netConnectTemp") {
          flag = false;
        }
      }
      return flag;
    }

    // 获取窗口是否可缩放
    function getResizable() {
      let resizable = false;
      let currPath = router.currentRoute.value.path;
      if (currPath == "/child/childChat" || currPath == "/child/viewer" || currPath == "/child/collect" || currPath == "/child/searchMsg" || currPath == "/child/mergeForward" || currPath == "/child/codeRecord") {
        resizable = true;
      }
      return resizable;
    }

    // 全局定时器
    watch(() => store.state.emit.globalTimer,
      (newValue, oldValue) => {
        store.commit("setJjsProxy", remote.store.getters.getJJsProxy);
      }, {
        deep: true
      }
    )

    return {
      isMax,
      currRouter,

      winMin,
      winMax,
      winClose,
      isShowOperate,
      getResizable,
    }
  }
}
</script>

<style scoped lang="scss">
.child {
  width: 100%;
  height: 100%;

  .router {
    background: #FFFFFF;
    border: 1px solid #D8D8D8;

    &.child-chat-router {
      padding-top: 30px;
      background: $styleBg1Hover;
    }

    &.viewer {
      background: rgba(0, 0, 0, 0.4);
    }
  }

  .operate-box {
    width: 100%;
    z-index: 101;

    &.child-operate-box {
      .operate-nav {
        top: 4px !important;
      }
    }

    &.child-collect-router {
      .operate-nav {
        z-index: 79;
      }
    }

    .drag-area {
      position: fixed;
      top: 2px;
      left: 2px;
      width: calc(100% - 4px);
      height: 30px;
    }

    .operate-nav {
      position: fixed;
      top: 0px;
      right: 6px;
      display: flex;
      z-index: 351;

      i {
        width: 30px;
        height: 30px;
        cursor: pointer;
        background-image: url(/img/icon_operate.png);
        background-repeat: no-repeat;
        background-size: 240px 30px;
      }

      .min {
        &:hover {
          background-position: -30px 0;
        }
      }

      .max {
        background-position: -60px 0;

        &:hover {
          background-position: -90px 0;
        }

        &.show-max {
          background-position: -120px 0;

          &:hover {
            background-position: -150px 0;
          }
        }
      }

      .close {
        background-position: -180px 0;

        &:hover {
          background-position: -210px 0;
        }
      }
    }
  }
}
</style>