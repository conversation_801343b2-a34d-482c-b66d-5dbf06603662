<template>
  <div class="login" @click="hideInputTips">
    <!--图形验证码弹窗-->
    <div class="captcha-modal-box win-drag win-no-resize" v-show="captchaObj.show">
      <div id="captcha-box" class="win-no-drag"></div>
    </div>

    <div class="header win-drag win-no-resize">
      <img class="bg" :src="'/img/login/login_act.png'"/>
      <div class="version">v{{ version }}</div>
      <ul class="btn-box win-no-drag">
        <li class="btn-setting" @click="showProxy"></li>
        <li class="btn btn-min" @click="winMin"></li>
        <li class="btn btn-close" @click="winClose"></li>
      </ul>
      <div class="env-box" v-show="env!='online'">
        <div class="env-title">注意，你正在使用的是</div>
        <div class="env-intr textEls">{{ env == "dev" ? "3.100环境" : env == "localtest" ? "itest环境" : env == "onlinetest" ? "uat环境" : env }}</div>
      </div>
      <!-- 网络提示 -->
      <NetTips :type="1"></NetTips>
    </div>

    <div class="content">
      <div class="content-box">
        <div class="content-left">
          <div class="content-left-title">乐办公扫码登录</div>
          <!--乐聊登录/新系统登录二维码-->
          <div class="content-qrcode-box">
            <div class="content-qrcode" v-if="loginScanObj.loginSrc&&!loginScanObj.qrcodeLoadingFlag">
              <img :style="loginScanObj.expireFlag?'opacity:0.2':''" :src="loginScanObj.loginSrc">
              <img class="refresh" v-show="loginScanObj.expireFlag" src="/img/login/refresh.png" @click="showScan(loginType==1?1:4)">
            </div>
            <div v-else class="content-qrcode-loading">
              <i class="icon-loading"></i>
              <span>{{ isAuth === null ? loginScanObj.qrcodeLoadingFlag ? "正在获取设备状态" : "系统响应超时" : "" }}</span>
            </div>
          </div>
          <div class="content-left-tips">{{ loginScanObj.tips }}</div>
        </div>
        <div class="content-right">
          <div class="content-right-title">账号密码登录</div>
          <div class="name-box">
            <div class="input-box">
              <input type="text" @keyup.enter="doNext(1)" ref="nameRef" autocomplete="off" v-model.trim="userInfo.name" placeholder="请输入手机号/人员编号" maxlength="20"
                     @input="inputNumber(7)"/>
            </div>
          </div>
          <div class="pwd-box">
            <input class="input-pwd" @keyup.enter="doNext(2)" ref="pwdRef" :type="showPwdFlag?'text':'password'" v-model="pwd" placeholder="请输入密码" maxlength="16"/>
            <div class="show-pwd icon-pwd" :class="showPwdFlag?'show':''" @click="toggleShowPwd(1)"></div>
          </div>
          <button class="login-btn" :disabled="loginFlag||loginScanObj.enforce||(userInfo.name.length==0||pwd.length==0)" @click.stop="doLogin">登录</button>
        </div>
      </div>
      <div class="check-sel-box">
        <span class="check-box" @click="protocol=!protocol">
          <i class="icon-check" :class="{'show': protocol}"></i>
          <span>我已阅读并同意</span>
          <span class="check-open" @click.stop="openUrl(1)">用户服务协议</span>
          <span>、</span>
          <span class="check-open" @click.stop="openUrl(2)">隐私政策</span>
        </span>
        <span class="check-box" @click="selLoginType(loginType==2?1:2)">
          <i class="icon-check" :class="{'show': loginType==2}"></i>
          <span>仅登录系统</span>
        </span>
        <span class="check-box" @click="setAccountFlag()">
          <i class="icon-check" :class="{'show': accountFlag}"></i>
          <span>记住账号</span>
        </span>
      </div>
    </div>

    <div :class="['footer',codeType==3?'footer-tips':'']">
      <div class="footer-btn-box" v-show="codeType!=3">
        <div class="btn" @click="showScan(2)">APP下载</div>
        <div class="line"></div>
        <div class="btn" @click="openUrl(4)">帮助</div>
        <div v-if="isAuth||isAuth==null" class="line"></div>
        <div v-if="isAuth||isAuth==null" class="btn" @click="openNetDetect()">网络检测</div>
        <div v-if="isAuth||isAuth==null" class="line"></div>
        <div v-if="isAuth||isAuth==null" class="btn" @click.stop="showScan(3)">忘记密码</div>
      </div>
      <div v-show="tipsText?.length>0" :class="['wrong-tips',tipsType?'success':'']">
        <div>
          <span>{{ tipsText }}</span>
          <span class="enforce" v-if="loginScanObj.enforce" @click="checkUpdate()">马上更新</span>
          <span class="enforce" v-if="diskObj.clear" @click="clearFreeSpace()">点击清理</span>
        </div>
        <i v-show="tipsCloseFlag" class="close icon-close" @click="hideTips"></i>
      </div>
    </div>

    <div class="modal-scan" v-show="codeType==1||codeType==2||codeType==3">
      <!--扫码登录/APP下载-->
      <div class="scan-code-box" v-show="codeType==1||codeType==2">
        <div v-if="isCompany||isAuth||isAuth===null||codeType==2" class="back" @click="showScan(0)">返回</div>
        <!--扫码提示-->
        <div :class="['modal-tips','textEls2',codeType==1&&loginType==2&&((!isAuth&&isCompany)||(isAuth&&!isNewSysAuth&&!isCompany))?'modal-auth-tips':'']">
          {{ codeType == 1 ? loginType == 2 && isAuth == false && isCompany ? "电脑未认证，请去乐聊工作台-乐聊工具-网络连接认证后再试" :
          loginType == 2 && isAuth && !isNewSysAuth && !isCompany ? "私人电脑无权限访问新系统" :
            loginType == 2 && (isAuth == null || !loginScanObj.loginFlag) ? "网络异常，请重试" : loginScanObj.tips : "扫码下载乐办公APP" }}
        </div>
        <!--乐聊登录/新系统登录二维码-->
        <div class="login-scan-box" v-show="codeType==1&&(((isNewSysAuth||(isAuth===false&&!isCompany))&&loginType==2&&loginScanObj.loginFlag)||loginType==1)">
          <div class="login-qrcode scan-shadow" v-if="loginScanObj.loginSrc&&!loginScanObj.qrcodeLoadingFlag">
            <img :style="loginScanObj.expireFlag?'opacity:0.2':''" :src="loginScanObj.loginSrc">
            <img class="refresh" v-show="loginScanObj.expireFlag" src="/img/login/refresh.png" @click="showScan(loginType==1?1:4)">
          </div>
          <div class="login-qrcode" v-else>
            <i class="icon-loading"></i>
          </div>
        </div>
        <!--新系统登录电脑未认证-->
        <div class="login-none-box" v-show="codeType==1&&loginType==2&&((isAuth===false&&isCompany)||(isAuth&&!isNewSysAuth&&!isCompany)||!loginScanObj.loginFlag)"></div>
        <!--app下载-->
        <div class="login-scan-box scan-shadow app-download" v-show="codeType==2">
          <div class="login-qrcode">
            <img src="/img/login/app.png">
            <i class="app-icon"></i>
          </div>
        </div>
      </div>
      <!--忘记密码-->
      <div class="forgot-pwd-box win-no-drag">
        <div class="updated-box start-update" v-show="forgetPwdObj.status==1">
          <div class="back" @click="showScan(0)">返回</div>
          <div class="updated-text">人员编号</div>
          <div class="forgot-content-box">
            <div class="input-box">
              <input ref="forgetKeyRef" :class="{'error':forgetPwdObj.errorType==1}" type="text" placeholder="请输入手机号/人员编号" maxlength="20"
                     v-model="forgetPwdObj.key" @input="inputNumber(3)" @keyup.enter="editPassword(1)">
            </div>
          </div>
          <button class="updated-btn" :disabled="forgetPwdObj.key.length==0" @click="editPassword(1)">下一步</button>
        </div>
        <div class="updating-box" v-show="forgetPwdObj.status==2">
          <div class="back" @click="showScan(5)">返回</div>
          <div class="forgot-tips">{{ forgetPwdObj.showTips ? "为了您的账号安全，请在确认身份后修改默认密码！" : "" }}</div>
          <div class="forgot-content-box">
            <div class="input-box" v-show="forgetPwdObj.accountShowType==1">
              <input disabled="disabled" type="text" placeholder="请输入手机号/人员编号" maxlength="11" v-model="forgetPwdObj.workerNo">
            </div>
            <div class="input-box">
              <input disabled="disabled" :class="{'error':forgetPwdObj.errorType==1}" type="text" placeholder="请输入手机号码" maxlength="20"
                     v-model="forgetPwdObj.phone" @input="inputNumber(1)" @blur="blurInput(1)" v-show="forgetPwdObj.accountShowType==2">
            </div>
            <div class="input-box code-box">
              <input :class="{'error':forgetPwdObj.errorType==2}" type="text" placeholder="请输入短信验证码" maxlength="4" v-model="forgetPwdObj.code"
                     @input="inputNumber(2)" @blur="blurInput(2)">
              <span class="show-btn code-btn" :class="{'gray':forgetPwdObj.codeTime}" @click="setCaptcha(1)">
              {{ forgetPwdObj.codeTime ? "(" + forgetPwdObj.codeTime + "s)重新获取" : forgetPwdObj.sendCodeCount > 1 ? "重新获取" : "发送验证码" }}
              </span>
              <span v-show="forgetPwdObj.sendCodeCount>1" class="code-tips red" @click="sendAudioCode()">没有收到验证码？</span>
            </div>
            <div class="input-box">
              <input :class="{'error':forgetPwdObj.errorType==3}" :type="forgetPwdObj.showPwd?'text':'password'" placeholder="6-16位，须由大小写字母、数字组成" maxlength="16"
                     v-model="forgetPwdObj.pwd" @blur="blurInput(3)" @input="inputNumber(5)">
              <span class="show-btn icon-pwd" :class="{'show': forgetPwdObj.showPwd}" @click="toggleShowPwd(2)"></span>
            </div>
            <div class="input-box">
              <input :class="{'error':forgetPwdObj.errorType==4}" :type="forgetPwdObj.showConfirmPwd?'text':'password'" placeholder="请再输入一次新密码" maxlength="16"
                     v-model="forgetPwdObj.confirmPwd" @blur="blurInput(4)" @keyup.enter="editPassword" @input="inputNumber(6)">
              <span class="show-btn icon-pwd" :class="{'show': forgetPwdObj.showConfirmPwd}" @click="toggleShowPwd(3)"></span>
            </div>
            <div class="code-tips-box">
              <span class="code-tips" @click="openUrl(3)">其他问题</span>
            </div>
            <button :disabled="!forgetPwdObj.btnStatus" class="forgot-btn" @click="editPassword">确定</button>
          </div>
        </div>
        <div class="updated-box" v-show="forgetPwdObj.status==3">
          <img class="updated-img" src="/img/login/pwd_done.png">
          <div class="updated-text">密码设置成功，请重新登录!</div>
          <div class="updated-btn" @click="showScan(0)">重新登录</div>
        </div>
      </div>
    </div>

    <div class="protocol-modal win-drag win-no-resize" v-show="protocolShow">
      <div class="protocol-modal-box win-no-drag">
        <div class="protocol-modal-title">欢迎使用</div>
        <div class="protocol-modal-content">
          <span>为更好地保障您的个人权益，请您在使用本应用前，仔细阅读并同意以下协议</span>
          <span class="protocol-link" @click.stop="openUrl(1)">《用户服务协议》</span>
          <span>及</span>
          <span class="protocol-link" @click.stop="openUrl(2)">《隐私政策》</span>
        </div>
        <div class="protocol-modal-btn-box">
          <div class="protocol-modal-btn" @click="toggleProtocol(1)">不同意</div>
          <div class="protocol-modal-btn" @click="toggleProtocol(2)">同意</div>
        </div>
      </div>
    </div>

    <Update type="1" ref="updateRef"></Update>
  </div>
</template>

<script>
import {ref, reactive, watch, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {
  loginApi, sendEditPassCodeApi, editPasswordApi, getPolicyVersionApi, doLoginNewSysApi, doCheckAuthApi, uploadPcKeyApi, checkAccountApi,
} from "@utils/net/api.js";
import {
  debounce, enCodeJJS, createQrCode, MD5, emitMsg, getBaseComputerInfo, getAllComputerInfo, scrollLi, encrypt, getFileCachedPath, deleteFile, getNetConnectInfo,
  renderRsa, signRsa, encryptRsa, openLocalFile, openChildWin, calcStorageSize,
} from "@utils";
import Update from "@comp/update/Update.vue";
import {alert, loading} from "@comp/ui";
import NetTips from "@comp/ui/comps/NetTips";

export default {
  name: "Login",
  components: {Update, NetTips},
  props: {
    msg: String,
  },
  setup(props) {
    const store = useStore();
    const router = useRouter();
    emitMsg("msg", {type: "tray", tray: 3});
    // 注册快捷键
    emitMsg("msg", {type: "shortcut", unregisterAll: true});
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();
    // 获取vpn权限
    store.dispatch("getProxyTool");

    // 打开页面聚焦窗口到密码输入框
    onMounted(() => {
      currentWindow.setAlwaysOnTop(true);
      currentWindow.blur();
      store.commit("setWindowSizeInfo", {type: 1, currentWindow: currentWindow.cWindow.id});
      nextTick(() => {
        currentWindow.focus();
        doNext(3);
        getIsAuth(1);
        // 获取远程指令列表
        store.dispatch("setCheckRemote");
        // 初始化历史@人员和特别关心缓存数据
        let haitMsgMap = localStorage.getItem("haitMsgMap");
        if (haitMsgMap) {
          localStorage.removeItem("haitMsgMap");
          store.state.cacheDB.update("haitMsgMap", haitMsgMap);
        }
        let concernMsgMap = localStorage.getItem("concernMsgMap");
        if (concernMsgMap) {
          localStorage.removeItem("concernMsgMap");
          store.state.cacheDB.update("concernMsgMap", concernMsgMap);
        }
        // 判断用户是否需要记住账号
        accountFlag.value = localStorage.getItem("accountFlag") == 0 ? false : true;
      })
      if (store.getters.getUserInfo.workerNo) {
        // 登录页存在用户信息重新启动
        emitMsg("msg", {type: "global", setGlobal: 1, info: {userInfo: ""}});
        emitMsg("msg", {type: "logout", logout: 1});
      }
      calcStorageSize();
    });

    // 设置登录窗口大小
    store.commit("setWindowSizeInfo", {type: 1, currentWindow: currentWindow.cWindow.id});

    // 电脑信息
    let computerInfo = ref(store.getters.getComputerInfo);
    let baseComputerInfo = store.getters.getBaseComputerInfo;
    let netComputerInfo = ref(store.getters.getNetComputerInfo);
    watch(() => store.state.computerInfo,
      (newValue, oldValue) => {
        computerInfo.value = newValue;
      }, {
        deep: true
      }
    );
    watch(() => store.state.netComputerInfo,
      (newValue, oldValue) => {
        netComputerInfo.value = newValue;
      }, {
        deep: true
      }
    );

    // 登录方式-1乐聊-2新系统
    let loginType = ref(1);
    // 登录websocket
    let loginWs = ref();
    // 电脑是否认证
    let isAuth = ref(null);
    // 电脑是否能登录新系统
    let isNewSysAuth = ref(null);
    // 是否公司电脑
    let isCompany = ref(null);
    // 是否记住账号
    let accountFlag = ref(false);
    // 是否需碍你事协议弹窗
    let protocolShow = ref(false);
    // 协议
    let protocol = ref(false);
    // 服务端版本号
    let protocolS = ref("");
    // 获取协议版本号
    getPolicyVersionApi().then(res => {
      protocolS.value = res.version;
      if (protocolS.value && userInfo.name && userInfo.protocolV == protocolS.value) {
        protocol.value = true;
      }
    });

    // 版本号
    let config = ref(store.getters.getConfig);
    let version = ref(config.value.version);
    let env = ref(config.value.env);
    let jjsHome = config.value.config[config.value.config.env].jjsHome;
    // 名字输入框元素
    let nameRef = ref();
    // 用户名
    let userInfo = reactive({
      name: "",
      telCode: "",
      accountNo: "",
    });
    // 密码
    let pwd = ref("");
    // 搜索的用户信息
    let searchList = ref([]);
    // 搜索选中下标
    let searchIndex = ref(-1);
    // 搜索框元素
    let searchBoxRef = ref();
    // 搜索标记
    let searchFlag = ref(false);
    // 是否发起搜索
    let searchReg = ref(false);
    // 是否显示历史记录
    let showHistoryFlag = ref(false);
    // 历史记录列表
    let historyMap = ref({});
    // 历史记录数据
    let historyList = ref(getHistoryList());
    // 是否显示密码
    let showPwdFlag = ref(false);
    // 是否登录
    let loginFlag = ref(false);
    // 是否显示关闭提示
    let tipsCloseFlag = ref(false);
    // 提示内容
    let tipsText = ref("");
    let tipsType = ref("");
    // 密码框元素
    const pwdRef = ref();
    // 输入框提示内容
    let inputTipsText = ref("");
    // 提示内容高度
    let tipsTop = ref(78);
    // 提示定时器
    let inputTimer = "";
    // 0关闭1登录2app下载
    let codeType = ref(0);
    // 二维码对象
    let loginScanObj = reactive({
      expireFlag: false, // 二维码是否失效
      tips: "",
      enforce: false,// 是否强制更新
      loginFlag: true,// 是否显示二维码，网络异常不显示
      loginSrc: "",// 登录二维码
      loginTimer: "",// 登录二维码定时器
      loginWsTimer: "",// 定时器轮询ws状态
      loginReloadFlag: false,// 登录二维码重新加载状态
      qrcodeLoadingFlag: true,// 二维码获取状态
      loginCode: "",// ws通讯code
      clear: "",// 清理磁盘空间类型
    });
    // 忘记密码对象
    let forgetPwdObj = ref({
      status: 1,// 修改密码状态,1:输入手机号/工号,2:修改中,3:修改完成
      workerNo: "",// 工号
      telCode: "",// 手机区号
      accountNo: "",// 手机号去除区号
      phone: "",// 手机号
      code: "",// 验证码
      pwd: "",// 新密码
      showPwd: false,// 显示新密码文字
      confirmPwd: "",// 确认新密码
      showConfirmPwd: false,// 显示确认密码文字
      codeTime: 0,// 获取验证码倒计时
      codeTimer: "",// 验证码倒计时定时器
      errorType: -1,// 错误类型
      showTips: true,// 是否显示忘记密码提示文案
      key: "",// 忘记密码输入手机号/工号
      btnStatus: false,// 是否可以点击按钮状态
      accountType: "",// 1直营 2加盟
      realName: 0,// 0 未实名 1已实名
      cardType: 1,// 1身份证 3港澳通行证（2军官、4护照、5其他、6港澳身份证）
      cardNo: "",// 证件号码
      showCard: false,// 显示选择证件类型
      loading: false,// 请求接口状态
      accountShowType: 1,// 1工号 2手机号
      sendCodeType: 1,// 1短信 2语音
      sendCodeCount: 0,// 进入到忘记密码发送验证码次数
    });
    // 硬盘空间清理对象
    let diskObj = ref({
      clear: false,// 是否需要清理
      deviceID: "",// 需要清理的盘符
      SystemDrive: "",// 系统盘
      exeDrive: "",// 乐聊安装盘
    });
    // 上传公钥标识
    let uploadPcKeyP = "";

    // 更新组件
    let updateRef = ref();

    // 图形验证码对象
    let captchaObj = ref({
      show: false,
    });

    // 忘记密码手机/人员对象
    let forgetKeyRef = ref();

    // 如果存在历史记录 默认选中第一个
    if (historyList.value.length > 0) {
      userInfo.name = historyList.value[0].loginNumber || historyList.value[0].workerNo;
      userInfo.protocolV = historyList.value[0].protocolV;
    }
    delInputImage();

    // 获取历史登录列表
    function getHistoryList() {
      try {
        historyMap.value = JSON.parse(localStorage.getItem("loginDataNew") || "{}");
        // 不存在新的登录记录获取历史的
        if (Object.keys(historyMap.value).length == 0) {
          historyMap.value = JSON.parse(localStorage.getItem("loginData") || "{}");
        }
        return Object.values(historyMap.value).sort((a, b) => {return b.time - a.time});
      } catch (e) {
        // 移除历史版本缓存
        localStorage.removeItem("loginData");
        return [];
      }
    }

    // 显示二维码 type-0关闭-1登录-2app下载-3忘记密码-4登录新系统-5返回忘记密码一级页面
    async function showScan(type, param) {
      loginScanObj.expireFlag = false;
      clearInteralTimer(loginScanObj.loginTimer);
      try {
        if (loginWs.value) {
          loginWs.value.send(`{"type":"remove","rCodeToken":"${loginScanObj.loginCode}"}`);
          loginWs.value.close();
        }
      } catch (e) {}
      switch (type) {
        case 0:
          // 重置忘记密码状态
          resetForwardPwd();
          codeType.value = 0;
          // 非认证状态返回乐聊登录
          if (!isNewSysAuth.value) {
            loginType.value = 1;
          }
          if (!isCompany.value && !isAuth.value) {
            showScan(1);
          } else {
            setLoginCode(loginType.value);
          }
          break;
        case 1:
          // 扫码登录乐聊
          if (!isCompany.value && !isAuth.value) {
            getIsAuth(1);
            return;
          }
          if (codeType.value == 0) {
            type = 0;
          }
          await getMac();
          // 上传公钥失败提示
          let uploadPcKeyFlag = await uploadPcKey(2);
          if (!uploadPcKeyFlag) {
            return;
          }
          loginType.value = 1;
          loginScanObj.tips = "";
          setLoginCode(1);
          break;
        case 2:
          break;
        case 3:
          forgetPwdObj.value.showTips = false;
          if (param?.enter) {
            editPassword(1);
          }
          nextTick(() => {
            forgetKeyRef.value.focus();
          });
          break;
        case 4:
          // 扫码登录新系统
          type = 0;
          loginScanObj.tips = "";
          setLoginCode(2);
          break;
        case 5:
          // 返回忘记密码一级页面
          forgetPwdObj.value.status = 1;
          resetForwardPwd();
          break;
      }
      if (type == 5 && !forgetPwdObj.value.showTips) {
        type = 3;
      }
      codeType.value = type;
    }

    // 重置忘记密码输入内容
    function resetForwardPwd() {
      forgetPwdObj.value.status = 1;
      forgetPwdObj.value.accountNo = "";
      forgetPwdObj.value.workerNo = "";
      forgetPwdObj.value.phone = "";
      forgetPwdObj.value.code = "";
      forgetPwdObj.value.pwd = "";
      forgetPwdObj.value.showPwd = false;
      forgetPwdObj.value.confirmPwd = "";
      forgetPwdObj.value.showConfirmPwd = false;
      forgetPwdObj.value.key = "";
      forgetPwdObj.value.errorType = -1;
      forgetPwdObj.value.btnStatus = false;
      forgetPwdObj.value.cardType = 1;
      forgetPwdObj.value.cardNo = "";
      forgetPwdObj.value.showCard = false;
      forgetPwdObj.value.loading = false;
      // 清除错误提示
      hideTips();
    }

    // 进入忘记密码页面 type:1进入忘记密码账号页，2忘记密码设置页
    function enterForwardPwd(type, data) {
      if (type == 1) {
        hideTips();
        forgetPwdObj.value.key = userInfo.name;
        forgetPwdObj.value.telCode = userInfo.telCode;
        forgetPwdObj.value.accountNo = userInfo.accountNo;
        showScan(3, {enter: true});
        forgetPwdObj.value.showTips = true;
      } else if (type == 2) {
        forgetPwdObj.value.status = 2;
        forgetPwdObj.value.accountType = data.accountType;
        forgetPwdObj.value.realName = data.realName;
        // 设置忘记密码类型
        if (data.empNo == forgetPwdObj.value.key) {
          forgetPwdObj.value.accountShowType = 1;
          forgetPwdObj.value.workerNo = forgetPwdObj.value.key;
        } else {
          forgetPwdObj.value.accountShowType = 2;
          forgetPwdObj.value.phone = forgetPwdObj.value.key;
          forgetPwdObj.value.workerNo = data.empNo;
        }
        forgetPwdObj.value.key = "";
      }
    }

    // 清理定时器
    function clearInteralTimer(timer) {
      if (timer) {
        clearInterval(timer);
        timer = "";
      }
    }

    // 显示input输入框提示,5s取消提示,type-1只校验姓名-2只校验协议
    function checkInput(type) {
      hideInputTips();
      let inputFlag = true;
      tipsTop.value = 78;
      if (!userInfo.name && type != 2) {
        showTips({type: 1, text: "请输入手机号/人员编号"});
        inputFlag = false;
      } else if (!pwd.value && type != 1 && type != 2) {
        showTips({type: 1, text: "请输入密码"});
        inputFlag = false;
        tipsTop.value = 109;
      } else if (!protocol.value && type != 1) {
        // 校验协议
        protocolShow.value = true;
        inputFlag = false;
        tipsTop.value = 183;
      }
      if (!inputFlag) {
        inputTimer = setTimeout(() => {
          inputTipsText.value = "";
        }, 5000);
      }
      return inputFlag;
    }

    // 显示input输入框提示,5s取消提示
    function hideInputTips() {
      toggleHistoryFlag(2);
      changeShowCard(2);
      if (inputTimer) {
        clearTimeout(inputTimer);
        inputTimer = "";
        inputTipsText.value = "";
      }
    }

    // 下一步1-聚焦密码框，2执行登录,3-不验证聚焦密码框
    function doNext(type) {
      if (type == 2 && !checkInput()) {
        return;
      }
      if (type == 1) {
        // 不存在用户名校验
        if (!userInfo.name) {
          checkInput();
          return;
        }
        pwdRef.value.focus();
      } else if (type == 3) {
        if (!userInfo.name) {
          nameRef.value.focus();
        } else {
          pwdRef.value.focus();
        }
      } else {
        doLogin();
      }
    }

    // 隐藏提示
    function hideTips() {
      tipsCloseFlag.value = false;
      tipsText.value = "";
      tipsType.value = "";
      loginScanObj.enforce = false;
    }

    // 显示提示 type-1支持关闭,text-显示的文本内容 status:1成功默认错误
    function showTips(param) {
      tipsText.value = param.text;
      tipsType.value = param.status;
      if (param.type == 1) {
        tipsCloseFlag.value = true;
      } else {
        tipsCloseFlag.value = false;
      }
    }

    // 登录
    async function doLogin() {
      console.time("登录点击");
      if (!checkInput() || loginFlag.value) {
        return;
      }
      hideTips();
      loginFlag.value = true;
      console.timeEnd("登录点击");
      // 判断C盘和乐聊安装盘内存
      if (diskObj.value.clear) {
        // 点击登录磁盘空间不足后重新获取
        diskObj.value.clear = false;
        computerInfo.value = {};
        store.commit("setComputerInfo", {});
      }
      await getMac();
      let diskFlag = true;
      diskObj.value.SystemDrive = remote.process.env.SystemDrive;
      // 最小100M
      let minFreeSpaceNum = 100 * 1024 * 1024;
      if (computerInfo.value?.diskInfo?.length > 0) {
        diskObj.value.exeDrive = remote.process.execPath.split(":")[0] + ":";
        // 判断系统盘和乐聊所在盘是否小于100M
        for (let i = 0; i < computerInfo.value.diskInfo.length; i++) {
          let computerItem = computerInfo.value.diskInfo[i];
          if (computerItem.FreeSpaceNum < minFreeSpaceNum && computerItem.DeviceID == diskObj.value.exeDrive) {
            diskObj.value.deviceID = diskObj.value.exeDrive;
            diskFlag = false;
            break;
          } else if (computerItem.FreeSpaceNum < minFreeSpaceNum && computerItem.DeviceID == diskObj.value.SystemDrive) {
            diskObj.value.deviceID = diskObj.value.SystemDrive;
            diskFlag = false;
          }
        }
      }
      if (!diskFlag) {
        diskObj.value.clear = true;
        showTips({type: 1, text: `${diskObj.value.deviceID == diskObj.value.exeDrive ? "乐聊" : "系统"}所在盘空间少于100M，为确保能正常使用，请保证空间大于100M，`});
        loginFlag.value = false;
        return;
      } else {
        diskObj.value.clear = false;
      }
      let jjsProxy = store.getters.getJJsProxy;
      if (jjsProxy.loginSwitch) {
        // 备灾登录云信
        doLoginYxApi();
      } else {
        // 上传公钥失败提示
        let uploadPcKeyFlag = await uploadPcKey(2);
        if (!uploadPcKeyFlag) {
          loginFlag.value = false;
          return;
        }
        // 默认登录公司
        showTips({text: "正在登录中..."});
        doLoginApi();
      }
    }

    // 切换显示密码-type-1登录密码-2新密码-3确认新密码
    function toggleShowPwd(type) {
      if (type == 1) {
        showPwdFlag.value = !showPwdFlag.value;
      } else if (type == 2) {
        forgetPwdObj.value.showPwd = !forgetPwdObj.value.showPwd;
      } else if (type == 3) {
        forgetPwdObj.value.showConfirmPwd = !forgetPwdObj.value.showConfirmPwd;
      }
    }

    // 切换显示历史记录 type1显示2隐藏
    function toggleHistoryFlag(type) {
      if (type == 1) {
        showHistoryFlag.value = true;
      } else if (type == 2) {
        showHistoryFlag.value = false;
      } else {
        showHistoryFlag.value = !showHistoryFlag.value;
      }
    }

    // 登录api
    async function doLoginApi() {
      loginFlag.value = true;
      await getMac();
      console.time("登录api");
      let msg = {};
      getPhoneObj(userInfo.name, userInfo);
      if (loginType.value == 1) {
        // 登录乐聊
        let secret = await getEncrypt();
        msg = await loginApi({
          msgBody: JSON.stringify({
            imei: baseComputerInfo.hostName,
            mac: computerInfo.value.macAddress,
            lat: 0,
            lng: 0,
            ipStr: baseComputerInfo.address,
            loginAddr: "",
            username: enCodeJJS(userInfo.accountNo),
            password: MD5(pwd.value.toLowerCase()),
            passwordv2: MD5(pwd.value),
            empNo: userInfo.accountNo,
            telCode: userInfo.telCode,
            appVer: version.value,
            sysVer: baseComputerInfo.sysVer,
            devMd5: secret,
            type: "2",
          }),
          empNo: userInfo.accountNo,
        });
        loginFlag.value = false;
        console.timeEnd("登录api");
      } else {
        let secret = await getEncrypt(1);
        // 登录新系统
        let res = await doLoginNewSysApi({
          secret: secret,
        });
        loginFlag.value = false;
        console.timeEnd("登录api");
        if (res.code) {
          showTips({text: "登录成功，正在跳转..."});
          toNewSysBack(res.code);
        } else if (res.infoNo == 10018) {
          enterForwardPwd(1);
        } else {
          showTips({type: 1, text: res.infoStr || res.errorMsg || "系统错误"});
        }
        return;
      }
      if (msg.success) {
        if (msg.data && msg.data.data) {
          showTips({text: "正在拼命登录中..."});
          // 加盟登录兼容后台没返回手机号场景
          if (msg.data.data.accountType == 2) {
            msg.data.data.phone = userInfo.accountNo;
          }
          doLoginSuccess(msg.data.data);
        }
      } else {
        let errMsg = "";
        if (msg.errorCode == 10009) {
          // 强更
          errMsg = msg.errorMsg
          loginScanObj.enforce = true;
        } else if (msg.errorCode == 99994) {
          // 未认证
          setAuthStatus(1);
        } else {
          if (msg.errorCode == 10018) {
            // 重置默认密码
            enterForwardPwd(1, msg.data);
            return;
          } else if (msg.errorCode == 76666) {
            // 用户无法登陆乐聊帮助登录新系统
            loginType.value = 2;
            doLoginApi();
          } else {
            errMsg = msg.addErrorMsg || msg.errorMsg || "系统错误";
          }
        }
        showTips({type: 1, text: errMsg});
        console.log("doLoginApi", msg);
      }
    }

    // 登录云信api
    async function doLoginYxApi() {
      showTips({text: "正在登录中..."});
      loginFlag.value = true;
      await getMac();
      console.time("登录云信api");
      let loginNim = NIM.getInstance({
        transports: ["websocket"],
        appKey: config.value.config[config.value.config.env].nimAppKey,
        needReconnect: false,
        account: userInfo.name,
        token: MD5(pwd.value.toLowerCase()),
        onconnect: function () {
          loginNim.getUser({
            account: userInfo.name,
            sync: true,
            done: (err, res) => {
              if (!err) {
                res.sign = JSON.parse(res.sign);
                showTips({text: "正在拼命登录中..."});
                doLoginSuccess({
                  name: res.nick,
                  headPic: res.avatar,
                  deptName: res.sign.deptName,
                  deptNumber: res.sign.deptNumber,
                  workerNo: userInfo.name,
                  imPwd: MD5(pwd.value.toLowerCase())
                });
              } else {
                console.log("loginYxGetUserErr", err);
                doLoginYxRes(err);
              }
              loginNim.disconnect();
            }
          });
        },
        ondisconnect: function (err) {
          console.log("loginYxDisconnect", err);
          switch (err?.code) {
            case 302:
              err.message = "账号/密码错误请重试";
              break;
          }
          doLoginYxRes(err);
        },
        onerror: function (err) {
          console.log("loginYxErr", err);
          doLoginYxRes(err);
        }
      });
    }

    // 登录云信异常结果
    function doLoginYxRes(res) {
      showTips({type: 1, text: res?.message || "登录异常，请重试"});
      console.timeEnd("登录云信api");
      loginFlag.value = false;
    }

    // 获取mac地址
    function getMac(notTips) {
      return new Promise(resolve => {
        if (!computerInfo.value.macAddress || !baseComputerInfo.hostName || !netComputerInfo.value.ComputerId) {
          let baseHostName = baseComputerInfo.hostName
          // 设置电脑基础信息
          store.commit("setBaseComputerInfo", getBaseComputerInfo());
          baseComputerInfo = store.getters.getBaseComputerInfo;
          // 没联网状态到联网状态，重新获取电脑信息
          if (!baseHostName && baseComputerInfo.hostName) {
            store.commit("setComputerInfo", {});
            store.commit("setNetComputerInfo", {});
            computerInfo.value = {};
            netComputerInfo.value = {};
          }
          let promiseCount = 0;
          if (!computerInfo.value.macAddress) {
            console.time("获取电脑信息");
            getAllComputerInfo().then(function (systemInfoData) {
              // 设置电脑基础信息
              store.commit("setComputerInfo", systemInfoData);
              console.timeEnd("获取电脑信息");
              promiseCount++;
              if (promiseCount == 2) {
                hideTips();
                resolve();
              }
            });
          } else {
            promiseCount++;
          }
          if (!netComputerInfo.value.ComputerId) {
            console.time("获取电脑网络连接信息");
            getNetConnectInfo().then(function (netComputerInfo) {
              // 获取电脑网络连接信息
              store.commit("setNetComputerInfo", netComputerInfo);
              console.timeEnd("获取电脑网络连接信息");
              promiseCount++;
              if (promiseCount == 2) {
                hideTips();
                resolve();
              }
            });
          } else {
            promiseCount++;
          }
          if (!notTips) {
            if (!baseComputerInfo.hostName) {
              showTips({type: 1, text: "当前网络异常，请连接后重试"});
              loginFlag.value = false;
            } else {
              showTips({text: "正在获取数据..."});
            }
          }
        } else {
          resolve();
        }
      });
    }

    // 登录成功判断是否存在协议版本号
    async function doLoginSuccess(data, type) {
      if (protocolS.value) {
        toIndex(data, type);
      } else {
        let res = await getPolicyVersionApi();
        // 第二次获取不到不阻碍登录
        if (res.version) {
          protocolS.value = res.version;
        }
        toIndex(data, type);
      }
    }

    // 登录成功跳转-type1扫码登录
    async function toIndex(data, type) {
      data.protocolS = protocolS.value;
      data.doLoginkey = userInfo.telCode && userInfo.telCode != 86 ? "+" + userInfo.telCode + "-" + userInfo.accountNo : userInfo.accountNo;
      store.commit("setUserInfo", data);
      await router.push("/index");
      if (type == 1) {
        showScan(0);
      }
      try {
        window?._jjshome_t?._set_event_track_wid(data.workerId);
      } catch (e) {
        console.log("_set_event_track_widErr", e);
      }
      // 加载水印
      store.commit("setWaterMark", {window: window});
    }

    // 最小化
    function winMin() {
      store.commit("setWindowMin", currentWindow.cWindow.id);
    }

    // 退出
    function winClose() {
      store.commit("setWindowExit");
    }

    // 检查更新
    function checkUpdate() {
      updateRef.value.getUpdate(true);
    }

    // 正则限制
    function inputNumber(type) {
      switch (type) {
        case 1:
          // 忘记密码手机号
          forgetPwdObj.value.phone = forgetPwdObj.value.phone.replace(/[^0-9\+\-]/ig, "");
          break;
        case 2:
          // 忘记密码验证码
          forgetPwdObj.value.code = forgetPwdObj.value.code.replace(/[^0-9]/ig, "");
          break;
        case 3:
          // 忘记密码账号
          forgetPwdObj.value.key = forgetPwdObj.value.key.replace(/[^0-9\+\-]/ig, "");
          break;
        case 4:
          // 忘记密码证件
          forgetPwdObj.value.cardNo = forgetPwdObj.value.cardNo.replace(/[^a-zA-Z0-9]/ig, "");
          break;
        case 5:
          // 忘记密码密码
          forgetPwdObj.value.pwd = forgetPwdObj.value.pwd.replace(/[\u4E00-\u9FA5]/g, "");
          break;
        case 6:
          // 忘记密码确认密码
          forgetPwdObj.value.confirmPwd = forgetPwdObj.value.confirmPwd.replace(/[\u4E00-\u9FA5]/g, "");
          break;
        case 7:
          // 登录账号
          userInfo.name = userInfo.name.replace(/[^0-9\+\-]/ig, "");
          // 设置协议打钩状态
          if (protocolS.value && historyMap.value?.[userInfo.name]?.protocolV == protocolS.value) {
            protocol.value = true;
          } else {
            protocol.value = false;
          }
          break;
      }
      if (type < 7) {
        checkForgetPwd(3);
      }
    }

    // 失去焦点
    function blurInput(type) {
      if (forgetPwdObj.value.errorType == type) {
        forgetPwdObj.value.errorType = -1;
      }
    }

    // 发送验证码
    async function sendEditPassCode(type, id) {
      getPhoneObj(forgetPwdObj.value.phone, forgetPwdObj.value);
      let secret = await getEncrypt();
      let res = await sendEditPassCodeApi({
        msgBody: JSON.stringify({
          telCode: forgetPwdObj.value.accountShowType == 2 ? forgetPwdObj.value.telCode : "",
          phoneNumber: forgetPwdObj.value.accountShowType == 2 ? forgetPwdObj.value.accountNo : "",
          empNo: forgetPwdObj.value.workerNo,
          type: type,
          token: id,
          devMd5: secret,
        })
      });
      forgetPwdObj.value.sendCodeCount++;
      if (res.success) {
        let sendCodeText = forgetPwdObj.value.sendCodeType == 1 ? "短信" : forgetPwdObj.value.sendCodeType == 2 ? "语音" : "";
        let sendTips = sendCodeText + "验证码已发送";
        // 直营工号发送验证码回填手机号
        if (res.data?.phone && !forgetPwdObj.value.phone) {
          if (!forgetPwdObj.value.accountNo) {
            sendTips = sendCodeText + "验证码已发送到手机号" + res.data.phone.slice(0, 3) + "****" + res.data.phone.slice(-4);
          }
          forgetPwdObj.value.accountNo = res.data.phone;
          forgetPwdObj.value.phone = res.data.phone;
        }
        showTips({type: 1, text: sendTips, status: 1});
        if (type == 1) {
          // 发送成功显示倒计时(语音验证码不需要)
          forgetPwdObj.value.codeTime = 60;
          forgetPwdObj.value.codeTimer = setInterval(() => {
            forgetPwdObj.value.codeTime--;
            // 验证码低于50则显示语音验证码
            if (forgetPwdObj.value.sendCodeCount < 2 && forgetPwdObj.value.codeTime <= 50) {
              forgetPwdObj.value.sendCodeCount = 2;
            }
            if (forgetPwdObj.value.codeTime <= 0) {
              clearInteralTimer(forgetPwdObj.value.codeTimer);
              forgetPwdObj.value.codeTimer = "";
              forgetPwdObj.value.codeTime = 0;
            }
          }, 1000);
        }
      } else {
        if (res.errorMsg) {
          if (/验证码/.test(res.errorMsg)) {
            forgetPwdObj.value.errorType = 2;
          } else {
            forgetPwdObj.value.errorType = 1;
          }
        }
        showTips({type: 1, text: res.errorMsg || "系统错误"});
      }
    }

    // 修改登录密码 type:1忘记密码下一步
    async function editPassword(type) {
      if (forgetPwdObj.value.loading) {
        showTips({type: 1, text: "正在请求服务器..."});
        return;
      }
      // 忘记密码
      let text = checkForgetPwd(type == 1 ? 2 : "");
      if (text) {
        showTips({type: 1, text: text});
      } else if (type == 1) {
        // 切割区号+手机号
        getPhoneObj(forgetPwdObj.value.key, forgetPwdObj.value);
        // 获取账号类型和实名状态
        showTips({type: 1, text: "正在请求服务器..."});
        forgetPwdObj.value.loading = true;
        let secret = await getEncrypt();
        let res = await checkAccountApi({
          msgBody: JSON.stringify({
            telCode: forgetPwdObj.value.telCode,
            accountNo: forgetPwdObj.value.accountNo,
            devMd5: secret,
          })
        });
        forgetPwdObj.value.loading = false;
        if (!res?.success) {
          showTips({type: 1, text: res.errorMsg || "系统错误"});
          return;
        }
        forgetPwdObj.value.sendCodeCount = 0;
        // 进入忘记密码页面
        enterForwardPwd(2, res.data);
        hideTips();
      } else {
        // 确定修改密码
        forgetPwdObj.value.loading = true;
        let editParam = {
          phoneNumber: forgetPwdObj.value.accountNo,
          empNo: forgetPwdObj.value.workerNo,
          code: forgetPwdObj.value.code,
          password: forgetPwdObj.value.pwd,
        };
        // 加盟类型账号
        if (forgetPwdObj.value.accountType == 2) {
          editParam.telCode = forgetPwdObj.value.telCode;
        }
        showTips({type: 1, text: "正在请求服务器..."});
        let res = await editPasswordApi({
          msgBody: JSON.stringify(editParam)
        });
        forgetPwdObj.value.loading = false;
        if (res.success) {
          forgetPwdObj.value.status = 3;
          pwd.value = "";
          hideTips();
        } else {
          if (/密码/.test(res.errorMsg)) {
            forgetPwdObj.value.errorType = 3;
          } else if (/验证码/.test(res.errorMsg)) {
            forgetPwdObj.value.errorType = 2;
          } else if (/证件|身份/.test(res.errorMsg)) {
            forgetPwdObj.value.errorType = 5;
          } else if (/超时/) {
            forgetPwdObj.value.errorType = -1;
          }
          showTips({type: 1, text: res.errorMsg || "系统错误"});
        }
      }
    }

    // 校验忘记密码-type:1只校验手机号,2只校验账号,3判断按钮状态
    function checkForgetPwd(type) {
      let text = "";
      forgetPwdObj.value.errorType = -1;
      hideTips();
      if (type == 3) {
        if (forgetPwdObj.value.phone && forgetPwdObj.value.code && forgetPwdObj.value.pwd && forgetPwdObj.value.confirmPwd) {
          forgetPwdObj.value.btnStatus = true;
        } else {
          forgetPwdObj.value.btnStatus = false;
        }
      } else if (type == 2) {
        if (!forgetPwdObj.value.key) {
          text = "请输入人员编号/手机号";
        }
      } else if (!type) {
        if (!forgetPwdObj.value.code.length) {
          text = "请输入短信验证码";
          forgetPwdObj.value.errorType = 2;
        } else if (forgetPwdObj.value.pwd.length < 6) {
          text = "密码过于简单，密码至少6位";
          forgetPwdObj.value.errorType = 3;
        } else if (forgetPwdObj.value.confirmPwd != forgetPwdObj.value.pwd) {
          text = forgetPwdObj.value.confirmPwd ? "两次密码输入的不一致" : "请再输入一次新密码";
          forgetPwdObj.value.errorType = 4;
        }
      }
      return text;
    }

    // 跳转到url
    function openUrl(type) {
      let url = jjsHome;
      switch (type) {
        case 1:
          url += '/jjslogin/policy/servicePolicy';
          break;
        case 2:
          url += '/jjslogin/policy/privacyPolicy';
          break;
        case 3:
          url += '/jjslogin/policy/codeTips';
          break;
        case 4:
          url += '/jjslogin/policy/imTips';
          break;
      }
      store.dispatch("setOpenWindow", [url]);
    }

    // 发送语音验证码
    function sendAudioCode() {
      let text = checkForgetPwd(1);
      if (text) {
        showTips({type: 1, text: text});
      } else {
        alert({
          title: "获取语音验证码",
          content: "验证码将以电话的形式通知到您，请注意接听电话",
          done: (type) => {
            if (type == 1) {
              setCaptcha(2);
            }
          }
        });
      }
    }

    // 是否开启代理功能
    let isProxy = ref(store.getters.getJJsProxy.open);
    watch(() => store.state.jjsProxy.open,
      (newValue, oldValue) => {
        isProxy.value = newValue;
      }, {
        deep: true
      }
    );

    // 显示代理线路
    function showProxy() {
      store.commit("setEmit", {type: "showComponents", value: {type: 3, key: 1}});
    }

    // 选择登录方式
    async function selLoginType(type) {
      if (isAuth.value === null) {
        // 初始化获取认证失败，重新获取
        let res = await getIsAuth(1, "", true);
        if (res.success) {
          // 获取成功后切换新系统
          selLoginType(type);
        }
        return;
      }
      await getIsAuth(2);
      if (!getIsNewSysAuth()) {
        return;
      }

      // 扫码登录
      showScan(type == 1 ? 1 : 4);
      loginType.value = type;
    }

    // 判断是否能登录新系统
    function getIsNewSysAuth() {
      if (!isNewSysAuth.value) {
        if (isNewSysAuth.value === false) {
          if (codeType.value != 0) {
            // 非扫码页面才提示
            return true;
          }
          alert({
            content: isAuth.value === null ? "系统响应超时" : isCompany.value ? "电脑未认证，请去乐聊工作台-乐聊工具-公司电脑认证提交后再试" : "私人电脑无权限访问新系统",
            showCancel: false,
            okText: "我知道了"
          });
        }
        return false;
      }
      return true;
    }

    // 获取新系统权限 type-1初始化2判断是否能登录新系统3登录二维码 scanType-pcIm/newSys
    function getIsAuth(type, scanType, isTips) {
      return new Promise(async resolve => {
        await getMac(type == 1 ? true : false);
        // 上传公钥失败提示
        let uploadPcKeyFlag = await uploadPcKey(type, isTips);
        if (!uploadPcKeyFlag) {
          resolve();
          return;
        }
        if (type == 2 && isNewSysAuth.value !== null) {
          // 存在判断权限了不在请求
          resolve();
          return;
        }
        loginScanObj.qrcodeLoadingFlag = true;
        let res = await store.dispatch("doCheckAuth", {scanType: scanType});
        loginScanObj.qrcodeLoadingFlag = false;
        hideTips();
        if (res.success) {
          // 服务端认证状态变化
          if (isAuth.value != null) {
            let isCompanyFlag = res.type != 4;
            if (isAuth.value != res.isAuth || isNewSysAuth.value != res.isSystemAuth || isCompany.value != isCompanyFlag) {
              type = 1;
            }
          }
          setAuthStatus(type, res);
        } else {
          if (isTips == true) {
            showTips({type: 1, text: res.errorMsg || "系统异常，请重试"});
          }
          if (type == 1) {
            loginScanObj.expireFlag = true;
            loginScanObj.qrcodeLoadingFlag = false;
            loginScanObj.loginSrc = await createQrCode({text: "error", width: 155});
            loginScanObj.tips = "系统响应超时";
          }
        }
        resolve(res);
      });
    }

    // 设置认证状态交互
    function setAuthStatus(type, res) {
      isAuth.value = res.isAuth;
      isNewSysAuth.value = res.isSystemAuth;
      isCompany.value = res.type != 4;

      // 跳转认证二维码
      if (type == 1 && !isCompany.value && !isAuth.value) {
        codeType.value = 1;
        setLoginCode(3, res);
      } else if (type == 1) {
        setLoginCode(1);
      }
    }

    // 长链接二维码 type1乐聊2新系统3认证
    async function setLoginCode(type, res) {
      hideTips();
      if (isAuth.value && !isNewSysAuth.value && type == 2) {
        loginScanObj.loginFlag = false;
        loginScanObj.loginReloadFlag = true;
        return;
      }
      loginScanObj.expireFlag = true;
      loginScanObj.loginFlag = true;
      loginScanObj.loginReloadFlag = true;
      loginScanObj.qrcodeLoadingFlag = true;
      let codeRes = {};
      if (type == 3) {
        codeRes = res;
      } else {
        codeRes = await getIsAuth(3, type == 1 ? "pcIm" : type == 2 ? "newSys" : "");
      }
      loginScanObj.loginCode = codeRes.authCode;
      let loginTips = codeRes.errorMsg || "";
      loginScanObj.tips = loginTips;
      if (!loginScanObj.loginCode) {
        let loginError = codeRes.errorMsg || "系统异常，请重试";
        loginScanObj.tips = loginError;
        loginScanObj.loginSrc = await createQrCode({text: "error", width: 155});
        loginScanObj.qrcodeLoadingFlag = false;
        return;
      }
      loginScanObj.false = true;
      loginScanObj.expireFlag = false;
      loginScanObj.qrcodeLoadingFlag = false;
      loginScanObj.loginSrc = await createQrCode({text: loginScanObj.loginCode, width: 155});
      let urlObj = new URL(jjsHome);
      let ws = urlObj.protocol == "https:" ? "wss:" : "ws:";
      loginWs.value = new WebSocket(`${ws + urlObj.host}/jjslogin/WebSocketServer`);
      loginWs.value.onopen = function () {
        console.log("ws已打开");
        loginWs.value.send(`{"type":"open","rCodeToken":"${loginScanObj.loginCode}"}`);
        loginScanObj.expireFlag = false;
        loginScanObj.tips = loginTips;
        clearInteralTimer(loginScanObj.loginWsTimer);
        loginScanObj.loginWsTimer = setInterval(() => {
          if (!loginWs.value) {
            clearInteralTimer(loginScanObj.loginWsTimer);
            return;
          }
          loginWs.value.send(`{"type":"open","rCodeToken":"${loginScanObj.loginCode}"}`);
        }, 10 * 1000);
      }
      // 收到消息事件
      loginWs.value.onmessage = function (msg) {
        loginScanObj.expireFlag = true;
        try {
          console.log("loginWs", encrypt(msg.data));
          let resData = JSON.parse(msg.data);
          if (resData.type == "scanCheck") {
            loginScanObj.tips = `${resData.data.empName}-${resData.data.deptName}，请在手机上确认登录！`
          } else if (resData.type == "scanLogin") {
            // 登录成功跳转新系统
            loginScanObj.tips = "登录成功，正在跳转...";
            toNewSysBack(resData.data.code);
          } else if (resData.type == "scanAuth" || resData.type == "scanReset") {
            // 手机扫码认证后 或服务端主动断开标识 断开连接
            loginWs.value.close();
          } else if (resData.type == "scanImLogin") {
            // 登录乐聊
            console.log("扫码登录");
            doLoginSuccess(resData.data, 1);
          }
          // 扫码返回的提示
          if (resData.data?.tips) {
            loginScanObj.tips = resData.data?.tips;
          }
        } catch (e) {
          console.log("loginWsErr", e);
          loginScanObj.tips = "系统错误";
        }
      }
      // 连接关闭事件
      loginWs.value.onclose = function (e) {
        console.log("ws已关闭", e);
        loginWs.value = "";
        clearInteralTimer(loginScanObj.loginTimer);
        loginScanObj.expireFlag = true;
        if (!loginScanObj.loginReloadFlag) {
          loginScanObj.tips = "系统响应超时";
        }
        loginScanObj.loginReloadFlag = true;
      }
      // 发生了错误事件
      loginWs.value.onerror = function () {
        console.log("ws发生了错误");
        loginWs.value = "";
        loginScanObj.expireFlag = true;
        loginScanObj.tips = "系统错误";
        clearInteralTimer(loginScanObj.loginTimer);
      }
      clearInteralTimer(loginScanObj.loginTimer);
      loginScanObj.loginTimer = setInterval(() => {
        //  二维码失效时间 2分钟
        loginScanObj.expireFlag = true;
        loginScanObj.tips = "二维码失效, 请刷新后再操作";
        clearInteralTimer(loginScanObj.loginTimer);
        loginScanObj.loginReloadFlag = true;
        loginWs.value.send(`{"type":"remove","rCodeToken":"${loginScanObj.loginCode}"}`);
        loginWs.value.close();
      }, 2 * 60 * 1000);
    }

    // 跳转新系统返回乐聊登录页
    function toNewSysBack(code) {
      store.dispatch("setOpenWindow", [`${jjsHome}/jjslogin/forward?id=${code}&jumpType=99`]);
      setTimeout(() => {
        showScan(0);
        hideTips();
        pwd.value = "";
      }, 1000);
    }

    // 返回登录页删除输入框图片
    function delInputImage() {
      try {
        deleteFile(getFileCachedPath({type: 6}));
      } catch (e) {}
    }

    // 获取加密串
    async function getEncrypt(type) {
      return new Promise(async resolve => {
        let param = {};
        if (type == 1) {
          param.workerNo = userInfo.accountNo;
          param.wpassword = pwd.value.toLowerCase();
          param.passwordv2 = MD5(pwd.value);
          param.telCode = userInfo.telCode;
        }
        let secret = await store.dispatch("getSecretEnCrypt", {param: param});
        resolve(secret);
      });
    }

    // 上传公钥 type-1初始化2判断是否能登录新系统
    async function uploadPcKey(type, isTips) {
      return true;
      await getMac(type == 1 ? true : false);
      let rsaObj = renderRsa();
      if (rsaObj.publicKey) {
        // 本地不存在私钥则上传公钥
        if (!uploadPcKeyP) {
          uploadPcKeyP = new Promise(async resolve => {
            console.time("获取上传信息");
            if (type != 1) {
              showTips({text: "正在获取设备数据..."});
            }
            // 公钥去除前缀和换行
            let publicKey = rsaObj.publicKey.replace(/(\n|-----BEGIN PUBLIC KEY-----|-----END PUBLIC KEY-----)/g, "");
            let secret = await store.dispatch("getSecretEnCrypt", {
              notRsa: true
            });
            // 上传公钥和对时间戳签名
            let encryptRsaParam = {
              computerInfo: secret,
              clientPublicKey: publicKey,
              signData: String(secret.t),
            }
            encryptRsaParam.sign = signRsa(encryptRsaParam.signData, rsaObj.privateKey);
            console.timeEnd("获取上传信息");
            let res = await uploadPcKeyApi({
              contexts: encryptRsa(JSON.stringify(encryptRsaParam))
            });
            uploadPcKeyP = "";
            if (res.success) {
              if (type != 1) {
                showTips({type: 1, text: ""});
              }
              resolve(true);
              renderRsa(rsaObj.privateKey);
            } else {
              if (type != 1 || isTips) {
                showTips({type: 1, text: res.errorMsg || "系统信息异常，请重试"});
                resolve(false);
              } else {
                resolve(true);
              }
            }
          });
        }
        return uploadPcKeyP;
      } else {
        return true;
      }
    }

    // 清理磁盘空间
    function clearFreeSpace() {
      if (diskObj.value.deviceID == diskObj.value.exeDrive) {
        // 乐聊所在盘小于100M，弹窗选择清理
        alert({
          title: "清理提示",
          content: "乐聊所在盘空间少于100M，为确保能正常使用，请保证空间大于100M，您可选择让乐聊直接清理，也可跳转至文件夹中，自己手动清理。",
          cancelText: "手动清理",
          okText: "直接清理",
          done: type => {
            if (type == 1) {
              store.commit("setEmit", {type: "clearBox", value: Date.now()});
              hideTips();
            } else if (type == 2) {
              openLocalFile(diskObj.value.deviceID);
              hideTips();
            }
          },
        });
      } else {
        // 非乐聊所在盘小于100M，打开文件管理器清理
        openLocalFile(diskObj.value.deviceID);
      }
    }

    // 打开网络检测
    function openNetDetect() {
      // 打开网络检测窗口
      openChildWin("netDetect", {always_on_top: true});
    }

    // 图形验证码
    function setCaptcha(type) {
      if (forgetPwdObj.value.codeTimer && type == 1) {
        return;
      }
      forgetPwdObj.value.sendCodeType = type;
      let text = checkForgetPwd(1);
      if (text) {
        showTips({type: 1, text: text});
      } else {
        captchaObj.value.show = true;
        initRandomCaptcha("#captcha-box",
          {
            // 生成验证码的接口
            genCaptchaUrl: jjsHome + "/main/captcha/gen",
            // 验证码校验接口
            validUrl: jjsHome + "/main/captcha/checkForm",
            validSuccessCallback: (res, c, tac) => {
              // 校验成功后关闭调用获取验证码
              c.destoryWindow();
              sendEditPassCode(type, res.data.id);
            },
            validFailCallback: (res, c, tac) => {
              // 校验失败
            },
            hideCallback: () => {
              captchaObj.value.show = false;
            },
            genCaptFail: () => {
              showTips({type: 1, text: "验证码生成失败，请重试"});
              captchaObj.value.show = false;
            },
          }, {}).loadCaptcha();
      }
    }

    // 切换显示证件类型
    function changeShowCard(key, type) {
      if (key == 1) {
        forgetPwdObj.value.showCard = !forgetPwdObj.value.showCard
      } else if (key == 2) {
        forgetPwdObj.value.showCard = false;
        if (type) {
          forgetPwdObj.value.cardType = type;
        }
      }
    }

    // 是否同意协议
    function toggleProtocol(type) {
      if (type == 1) {
        protocol.value = false;
      } else if (type == 2) {
        protocol.value = true;
        console.log("同意协议");
        doLogin();
      }
      protocolShow.value = false;
    }

    // 获取手机号码
    function getPhoneObj(number, obj) {
      let telCode = "";
      let accountNo = number.replace(/\+/, "");
      if (/\-/.test(accountNo)) {
        telCode = accountNo.split("-")[0];
        accountNo = accountNo.split("-")[1];
      }
      obj.telCode = telCode;
      obj.accountNo = accountNo;
    }

    // 切换记住账号
    function setAccountFlag() {
      accountFlag.value = !accountFlag.value;
      localStorage.setItem("accountFlag", accountFlag.value ? 1 : 0);
    }

    return {
      version,
      env,
      userInfo,
      pwd,
      searchList,
      searchIndex,
      searchBoxRef,
      searchFlag,
      searchReg,
      showHistoryFlag,
      historyList,
      showPwdFlag,
      loginFlag,
      tipsCloseFlag,
      tipsText,
      tipsType,
      pwdRef,
      inputTipsText,
      tipsTop,
      codeType,
      loginScanObj,
      forgetPwdObj,
      diskObj,
      inputNumber,
      blurInput,
      protocolShow,
      protocol,
      accountFlag,
      updateRef,
      isProxy,
      loginType,
      isAuth,
      isNewSysAuth,
      isCompany,
      captchaObj,
      forgetKeyRef,

      nameRef,
      winMin,
      winClose,
      toggleShowPwd,
      toggleHistoryFlag,
      doLogin,
      hideTips,
      doNext,
      hideInputTips,
      showScan,
      checkUpdate,
      sendEditPassCode,
      editPassword,
      openUrl,
      sendAudioCode,
      showProxy,
      selLoginType,
      clearFreeSpace,
      openNetDetect,
      openChildWin,
      setCaptcha,
      changeShowCard,
      toggleProtocol,
      setAccountFlag,
    };
  },
};
</script>

<style scoped lang="scss">
@import '@static/css/public.scss';

.login {
  width: calc(100% - 1px);
  height: calc(100% - 1px);
  background: #FFFFFF;
  position: relative;
  border: 1px solid #D8D8D8;

  button {
    &:disabled {
      opacity: 0.3;
      cursor: default;
    }
  }

  input {
    font-size: 14px;

    &:disabled {
      background: #F3F3F3;
    }
  }

  .captcha-modal-box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;

    #captcha-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 318px;
      height: 278px;

      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        content: "";
        width: 32px;
        height: 32px;
        background: url("/img/icon_loading.png") no-repeat center;
        animation: captchaLoading 800ms linear infinite;
      }

      @keyframes captchaLoading {
        from {
          transform: translate(-50%, -50%) rotate(0deg);
        }

        to {
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
    }
  }

  .icon-pwd {
    width: 20px;
    height: 20px;
    background-image: url("/img/login/pwd_toggle.png");
    background-repeat: no-repeat;
    background-size: 80px 20px;
    cursor: pointer;

    &:hover {
      background-position: -20px 0;
    }

    &.show {
      background-position: -40px 0;

      &:hover {
        background-position: -60px 0;
      }
    }
  }

  .icon-close {
    width: 18px;
    height: 18px;
    background: url("/img/close.png") no-repeat center;
    background-size: 9px 9px;
    cursor: pointer;
  }

  .wrong-tips {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    min-height: 28px;
    background: #f9f5d5;
    color: #252525;
    display: flex;
    align-items: center;
    padding: 6px 32px;
    z-index: 1;
    word-break: break-all;

    &.success {
      &::before {
        background-image: url("/img/login/tips_success.png");
      }
    }

    &::before {
      content: "";
      position: absolute;
      top: 50%;
      left: 10px;
      transform: translateY(-50%);
      width: 14px;
      height: 14px;
      background-image: url("/img/login/tips.png");
      background-repeat: no-repeat;
      background-size: 100%;
    }

    .enforce {
      color: rgb(0, 13, 249);
      cursor: pointer;
    }

    .close {
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
    }
  }

  .header {
    height: 106px;
    overflow-y: hidden;
    position: relative;

    .bg {
      width: 100%;
    }

    .version {
      position: absolute;
      top: 13px;
      left: 56px;
      color: #fff;
      opacity: 0.5;
    }

    .btn-box {
      display: flex;
      position: absolute;
      top: 0;
      right: 0;

      .btn {
        width: 12px;
        height: 12px;
        background-image: url(/img/allicon.png);
        background-repeat: no-repeat;
        cursor: pointer;
      }

      .btn-min {
        background-position: -32px -23px;
        margin: 10px 10px 0 0;
      }

      .btn-close {
        background-position: -92px -23px;
        margin: 12px 10px 0 0;
      }

      .btn-setting {
        width: 16px;
        height: 16px;
        background: url(/img/more/setting.png) no-repeat;
        background-size: 16px;
        cursor: pointer;
        margin: 8px 10px 0 0;
      }
    }

    .env-box {
      position: absolute;
      top: 19px;
      left: 380px;
      color: #FFFFFF;
      width: calc(100% - 380px);

      .env-title {
        font-size: 16px;
        line-height: 25px;
      }

      .env-intr {
        font-size: 48px;
        line-height: 67px;
        font-weight: bold;
      }
    }
  }

  .content {
    position: relative;
    width: 100%;
    padding: 32px 48px 0;

    .content-box {
      display: flex;
      height: 257px;
      margin-bottom: 16px;

      .content-left {
        position: relative;
        width: 215px;
        display: flex;
        flex-direction: column;

        &:after {
          content: "";
          position: absolute;
          top: 48px;
          right: 0;
          width: 1px;
          height: 184px;
          background: #F1F1F1;
        }

        .content-left-title {
          font-weight: bold;
          font-size: 18px;
          color: #333333;
          line-height: 25px;
          padding-left: 17px;
        }

        .content-qrcode-box {
          position: relative;
          width: 170px;
          height: 170px;
          margin: 24px 0 8px;

          .content-qrcode {
            width: 100%;
            height: 100%;
            padding: 8px;
            box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.16);
            border-radius: 8px;

            .refresh {
              width: 55px;
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              cursor: pointer;
            }
          }

          .content-qrcode-loading {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            color: #666666;

            .icon-loading {
              width: 36px;
              height: 36px;
              background: url("/img/login/icon_loaing.png") no-repeat;
              background-size: 100%;
              animation: myLoading 800ms linear infinite;
              margin-bottom: 12px;
            }
          }
        }

        .content-left-tips {
          width: 160px;
          text-align: center;
          color: #333333;
          line-height: 20px;
          font-weight: bold;
        }
      }

      .content-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-left: 45px;

        .content-right-title {
          font-weight: bold;
          font-size: 18px;
          color: #333333;
          line-height: 25px;
          margin-bottom: 24px;
        }

        input,
        button {
          width: 100%;
          height: 48px;
        }

        input {
          padding: 5px 10px;
          border: 1px solid #E3E4E6;
          font-size: 16px;
          border-radius: 8px;

          &:focus {
            border: 1px solid #000000;
          }
        }

        .name-box,
        .pwd-box {
          width: 100%;
          margin-bottom: 20px;
        }

        .pwd-box {
          position: relative;

          .show-pwd {
            position: absolute;
            top: 50%;
            right: 8px;
            transform: translateY(-50%);
            cursor: pointer;
          }
        }

        .login-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: $styleColor;
          color: #FFFFFF;
          border-radius: 8px;
          cursor: pointer;
          font-size: 16px;

          &:hover {
            background-color: $styleColorHover;
          }
        }
      }
    }

    .check-sel-box {
      display: flex;
      justify-content: center;
      align-items: center;

      .check-box {
        display: flex;
        align-items: center;
        margin-left: 28px;
        cursor: pointer;

        &:first-child {
          margin-left: 0;
        }

        .icon-check {
          width: 12px;
          height: 12px;
          background: url("/img/login/check_toggle.png") no-repeat;
          background-size: 24px 12px;
          margin-right: 4px;

          &.show {
            background-position: -12px 0;
          }
        }

        .check-open {
          color: $styleColor;
          margin-left: 2px;
          cursor: pointer;

          &:last-child {
            margin-left: 0;
          }
        }
      }
    }
  }

  .footer {
    position: absolute;
    left: 0;
    top: calc(100% - 50px);
    width: 100%;
    height: 50px;
    z-index: 1;

    &.footer-tips {
      top: calc(100% - 28px);
      height: 28px;
    }

    .footer-btn-box {
      width: calc(100% - 24px);
      position: absolute;
      top: calc(100% - 50px);
      left: 12px;
      display: flex;
      justify-content: center;
      align-items: center;

      .btn {
        display: flex;
        align-items: center;
        color: #999999;
        cursor: pointer;
        position: relative;

        &:hover {
          color: $styleColor;

          .toggle-arrow {
            &:after {
              border-color: $styleColor transparent transparent transparent;
            }
          }
        }

        &.hover {
          .toggle-arrow {
            &:after {
              border-width: 0 4px 4px 4px;
              border-color: transparent transparent $styleColor transparent;
            }
          }
        }

        .toggle-arrow {
          position: relative;
          width: 15px;
          height: 15px;

          &:after {
            content: "";
            width: 0;
            height: 0;
            border-width: 4px 4px 0 4px;
            border-style: solid;
            border-color: #999999 transparent transparent transparent;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }

        }
      }

      .line {
        width: 1px;
        height: 9px;
        background: #CFCECB;
        margin: 0 6px;
      }
    }
  }

  .modal-scan {
    position: fixed;
    top: 107px;
    bottom: 0;
    width: calc(100% - 3px);
    height: calc(100% - 109px);
    background: #ffffff;

    .back {
      position: absolute;
      top: 16px;
      left: 12px;
      font-size: 14px;
      color: #666666;
      line-height: 17px;
      padding-left: 12px;
      background: url("/img/login/back.png") no-repeat left center;
      background-size: 12px;
      cursor: pointer;
    }

    .scan-code-box {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .scan-shadow {
        box-shadow: 0px 2px 10px 0px rgb(0 0 0 / 16%);
      }

      .login-scan-box {
        width: 170px;
        height: 170px;
        position: relative;

        &.app-download {
          width: 160px;
          height: 160px;
        }

        .login-qrcode {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          padding: 8px;
          border-radius: 8px;
        }

        .icon-loading {
          width: 36px;
          height: 36px;
          background: url("/img/login/icon_loaing.png") no-repeat;
          background-size: 100%;
          animation: myLoading 800ms linear infinite;
          margin-bottom: 12px;
        }

        .refresh {
          width: 55px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          cursor: pointer;
        }

        .app-icon {
          width: 40px;
          height: 40px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: #FFFFFF url("/img/login/app_icon.png") no-repeat center;
          background-size: 32px;
          border-radius: 8px;
        }
      }

      .login-none-box {
        width: 216px;
        height: 144px;
        background: url("/img/login/none.png") no-repeat center;
        background-size: 100%;
      }
    }

    .forgot-pwd-box {
      width: 100%;
      height: 100%;
      position: relative;

      .forgot-content-box {
        width: 320px;
        margin-top: 12px;

        .input-box {
          margin-bottom: 12px;
          position: relative;

          &.flex-box {
            display: flex;
            align-items: center;
            border: 1px solid #CFCECB;
            border-radius: 4px;

            &.hover,
            &:focus-within {
              border: 1px solid #000000;
            }

            input {
              border: none;

              &:focus {
                border: none;
              }
            }
          }

          &.code-box {
            .code-tips {
              position: absolute;
              top: 50%;
              left: 332px;
              transform: translateY(-50%);
              white-space: nowrap;
              font-size: 14px;
              color: $styleColor;
              cursor: pointer;
            }
          }

          input {
            width: 100%;
            height: 40px;
            border: 1px solid #CFCECB;
            padding: 0 10px;
            border-radius: 4px;

            &:focus {
              border: 1px solid #000000;
            }

            &.error {
              border: 1px solid $styleColor;
            }
          }

          .show-btn {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);

            &.code-btn {
              font-size: 14px;
              cursor: pointer;

              &:hover {
                color: $styleColor;
              }

              &.gray {
                color: #999999;
                cursor: default;
              }
            }
          }

          .input-option {
            height: 38px;
            font-size: 14px;
            display: flex;
            align-items: center;
            position: relative;
            flex-shrink: 0;
            padding-left: 10px;

            .input-sel {
              width: 70px;
            }

            .show-arrow {
              &.arrow-top {
                &:before {
                  border-width: 0 4px 4px 4px;
                }
              }

              &.arrow-bottom {
                &:before {
                  border-width: 4px 4px 0 4px;
                }
              }

              &:after {
                display: none;
              }
            }
          }

          .input-option-ul {
            position: absolute;
            width: 100%;
            top: 42px;
            left: 0;
            box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
            border-radius: 4px;
            border: 1px solid #CCCCCC;
            background: #FFFFFF;
            z-index: 1;
            font-size: 14px;

            li {
              line-height: 40px;
              padding: 0 10px;

              &:hover {
                background: $styleBg1Hover;
                cursor: pointer;
              }
            }
          }
        }

        .forgot-btn {
          width: 100%;
          height: 40px;
          line-height: 40px;
          text-align: center;
          background: $styleColor;
          font-size: 14px;
          color: #FFFFFF;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: $styleColorHover;
          }
        }

        .code-tips-box {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          margin-bottom: 12px;

          .code-tips {
            color: #333333;
            cursor: pointer;
            margin-left: 12px;

            &:hover {
              color: $styleColor;
            }

            &.gray {
              color: #999999;
              cursor: default;
            }

            &.red {
              color: $styleColor;
              font-size: 14px;
            }
          }
        }
      }

      .updating-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        .forgot-tips {
          color: #333333;
          height: 20px;
          line-height: 20px;
          margin-top: 16px;
          font-size: 14px;
        }
      }

      .updated-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        &.start-update {
          .updated-text {
            margin-top: 84px;
          }

          .updated-btn {
            margin-top: 20px;
          }
        }

        .updated-img {
          width: 60px;
          margin-top: 64px;
        }

        .updated-text {
          font-size: 16px;
          font-weight: bold;
          color: #333333;
          line-height: 21px;
          margin-top: 16px;
        }

        .updated-btn {
          width: 320px;
          height: 40px;
          line-height: 40px;
          background: $styleColor;
          font-size: 14px;
          color: #FFFFFF;
          text-align: center;
          margin-top: 40px;
          cursor: pointer;
          border-radius: 4px;

          &:hover {
            background: $styleColorHover;
          }
        }
      }
    }

    .modal-tips {
      font-size: 18px;
      height: 94px;
      line-height: 22px;
      padding: 48px 12px 24px;
      color: #333333;
      font-weight: bold;
      text-align: center;

      &.modal-auth-tips {
        font-size: 13px;
        color: #666666;
      }
    }

    .modal-close {
      width: 200px;
      font-size: 13px;
      background-color: $styleColor;
      height: 31px;
      line-height: 31px;
      text-align: center;
      color: #fff;
      margin-top: 29px;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        background-color: $styleColorHover;
      }
    }
  }

  .protocol-modal {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    background: rgba(0, 0, 0, 0.7);

    .protocol-modal-box {
      width: 360px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 16px;
      background: #FFFFFF;
      border-radius: 4px;
      color: #333333;
      line-height: 17px;

      .protocol-modal-title {
        font-size: 14px;
        font-weight: bold;
        color: #2A2A2A;
        line-height: 20px;
        margin-bottom: 10px;
        text-align: center;
      }

      .protocol-link {
        color: #3D5688;
        cursor: pointer;
      }

      .protocol-modal-btn-box {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 16px;

        .protocol-modal-btn {
          width: 56px;
          height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          cursor: pointer;

          &:last-child {
            background: $styleColor;
            color: #FFFFFF;
            border: 1px solid $styleColor;
            margin-left: 16px;
          }
        }
      }
    }
  }
}
</style>
