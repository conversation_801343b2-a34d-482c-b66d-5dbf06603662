<template>
  <div class="index" :class="'style-'+styleConfig">
    <div class="content-box" :class="{'border': ratio > 1}">
      <div class="left">
        <div class="user-avatar-box">
          <div class="avatar-box" @click="showUserInfo(userInfo.workerNo, $event)">
            <img v-if="userInfo" class="avatar" :src="getPerson(userInfo.workerNo).avatar" :onerror="avatarError.bind(this, 'p2p', userInfo.workerNo, '')">
          </div>
          <img v-if="userInfo&&userWearPic(getPerson(userInfo.workerNo))" class="user-label" :src="userWearPic(getPerson(userInfo.workerNo))" :onerror="hideElm">
          <div v-if="userInfo&&getPerson(userInfo.workerNo).wearType==2" class="user-label-text">{{ getPerson(userInfo.workerNo).wearName }}</div>
        </div>
        <ul class="tab-ul">
          <li class="tab-li" :class="(currRouter==item.path?'curr':'')+' tab-li-'+item.type" v-for="(item,key) in tabList" :attr="item.name" :key="item.sort"
              @click="toRouter(item)" @dblclick="scrollList()">
            <i>
              <span class="badge" v-show="item.badge">{{ item.badge > 99 ? "99+" : item.badge }}</span>
            </i>
            <span class="tab-tips textEls" :title="item.name.length>5?item.name:''">{{ item.name.slice(0, 5) }}</span>
          </li>
        </ul>
        <div class="setting" @click.stop="imStatusFun">
          <i :class="{'point':isUpdate}"></i>
        </div>
        <ul :class="['setting-ul',imStatus?'show':'']">
          <li class="setting-li" @click="lockIm">锁定乐聊</li>
          <li class="setting-li" @click="settingIm">设置</li>
        </ul>
      </div>
      <div class="main-content">
        <!--  顶部操作  -->
        <div class="operate-box win-drag">
          <!--顶部搜索-->
          <div class="search-box">
            <chatSearch ref="chatSearchRef"></chatSearch>
          </div>
        </div>
        <div class="operate-nav win-no-drag">
          <i class="min" @click="winMin(1)"></i>
          <i class="max" :class="isMax?'show-max':''" @click="winMax"></i>
          <i class="close" @click="winMin(2)"></i>
        </div>
        <!--路由内容-->
        <router-view class="router" v-slot="{ Component }">
          <!--缓存日程页面-->
          <keep-alive include="SmartSchedule">
            <component :is="Component" v-show="showPage()"></component>
          </keep-alive>
        </router-view>
      </div>
      <Update type="2"></Update>
    </div>
    <!--分组修改弹窗-->
    <ClassifyEditor ref="classifyEditorRef"></ClassifyEditor>
    <!--锁定乐聊-->
    <div :class="['bound-locking',lockStatus?'show':'']">
      <div class="locking-cont win-drag win-no-resize">
        <div class="locking-cont-img">
          <span>
            <img :src="userHeadImg" :onerror="avatarError.bind(this, 'p2p', userInfo.workerNo, '')">
          </span>
          <p>{{ userName }}</p>
        </div>
        <div class="locking-cont-txt win-no-drag">
          <div class="lock-input" v-show="lockContStatus">
            <div class="lock-input-box">
              <input :type="showPwdFlag?'text':'password'" v-model="lockPassword" placeholder="请输入您的密码" @keyup.enter="saveLock" ref="lockPwdRef">
              <div class="show-pwd icon-pwd" :class="showPwdFlag?'show':''" @click="toggleShowPwd()"></div>
            </div>
            <div class="lock-btn-box">
              <button class="locking-btn-ok" type="button" @click="saveLock">确定</button>
              <button class="locking-btn-cancel" type="button" @click="changeLock">取消</button>
            </div>
          </div>
          <div class="lock-button" v-show="!lockContStatus">
            <button class="locking-btn-lock" type="button" @click="changeLock">
              <i></i>
              <span>解锁</span>
            </button>
            <p>乐聊正在锁定中...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {ref, watch, onMounted, nextTick, onUnmounted} from "vue";
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {initNim} from "@utils/sdk/nimSDK";
import {avatarError, emitMsg, hideElm, MD5, compareVersion, debounce, deepClone, openAppUrl, userWearPic, setJJSEvent, getNewSysUrl} from "@utils";
import {alert, loading, toast} from "@comp/ui";
import {checkWorkApi, quickActingGetTaskStatusBy1} from "@utils/net/api"
import Update from "@comp/update/Update.vue";
import chatSearch from "@comp/search/chatSearch";
import ClassifyEditor from "@comp/ui/comps/ClassifyEditor";

export default {
  name: "Index",
  props: {
    // 选择查看详情工号
    selUserWorkerNo: {
      type: String,
      default: "",
    },
  },
  components: {Update, chatSearch, ClassifyEditor},
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    let ratio = ref(window.devicePixelRatio);
    let showPwdFlag = ref(false);
    emitMsg("msg", {type: "tray", tray: 1});
    // 注册快捷键
    emitMsg("msg", {type: "shortcut", unregisterAll: true});
    // 设置水印
    onMounted(() => {
      // 注册显示隐藏快捷键
      emitMsg("msg", {type: "shortcut", register: config.shortcut.toggleIm});
      // 注册锁定乐聊快捷键
      emitMsg("msg", {type: "shortcut", register: config.shortcut.lockIm});
      // 窗口聚焦
      document.addEventListener("focus", windowFocus);
      // 不存在用户则退出
      if (!userInfo?.workerNo) {
        store.commit("setLogout", {type: 1});
        emitMsg("msg", {type: "logout", logout: 1});
      }
    });
    // 卸载去除监听
    onUnmounted(() => {
      // 窗口聚焦
      document.removeEventListener("focus", windowFocus);
    });

    // 当前页面路径
    let currRouter = ref(router.currentRoute.value.path);
    // 用户信息
    let userInfo = store.getters.getUserInfo || {};
    // 不存在用户则退出
    if (!userInfo?.workerNo) {
      store.commit("setLogout", {type: 1});
      emitMsg("msg", {type: "logout", logout: 1});
      return;
    }
    // 搜索元素
    let chatSearchRef = ref();
    // 分组修改弹窗
    let classifyEditorRef = ref(null);
    // 配置环境
    let config = store.getters.getConfig.config;
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();
    // 是否最大化
    let isMax = ref(false);
    // 是否有更新
    let isUpdate = ref(false);
    // 电脑信息
    let computerInfo = ref(store.getters.getComputerInfo);
    // 日程点点上一次更新时间
    let scheduleStudyingTime = ref(new Date().getTime())
    // 设置全局定时器
    store.commit("setGlobalTimer");
    // 左侧tab列表
    let tabList = ref([]);
    // 左侧tab数据
    let tabMap = ref({});
    // 页面列表
    tabMap.value = {
      msg: {name: "消息", badge: 0, path: "/index/chat", sort: 1, type: 1},
      friend: {name: "通讯录", badge: 0, path: "/index/mailList", sort: 2, type: 2},
      document: {name: "乐文档", badge: 0, path: "toDocSys", sort: 3, type: 3},
      schedule: {name: "日程", badge: 0, path: "/index/smartSchedule", sort: 4, type: 4},
      collect: {name: "收藏", badge: 0, path: "toCollect", sort: 5, type: 5},
      system: {name: "新系统", badge: 0, path: "toNewSys", sort: 8, type: 8},
      download: {name: "APP下载", badge: 0, path: "toAppSys", sort: 9, type: 9},
      dataReport: {name: "数据报表", badge: 0, path: "toDataReport", sort: 10, type: 10},
      more: {name: "工作台", path: "/index/more", badge: 0, sort: 11, type: 11},
    };
    tabList.value = Object.values(tabMap.value).sort((a, b) => {return a.sort - b.sort});
    // 显示设置乐聊
    let imStatus = ref(false);
    // 乐聊风格
    let styleConfig = ref(store.getters.getSettings[config.settings.type11]);
    // 初始化基础信息接口
    store.dispatch("setServerConfig");
    // 初始化本地缓存数据-未读数、特别关心
    store.commit("setInitLocalStorage");
    // 初始化云信对象
    store.commit("setNim", initNim(userInfo));
    // 初始化图片缓存定时
    store.commit("setImgQueue");
    // 初始化文件记录数据库
    store.commit("setFileDB", userInfo.workerNo);
    // 初始化路由
    store.commit("setRouter", router);
    // 初始化窗口大小
    store.commit("setWindowSizeInfo", {type: 2, currentWindow: currentWindow.cWindow.id, initMin: true});
    // 初始化截图
    store.commit("setInitJt");
    // 初始化日程弹窗
    store.dispatch("setScheduleModal");
    // 初始化乐聊通知徽标
    store.commit("setNoticeUnread");
    // 初始化敏感词库
    store.dispatch("setKeyText");
    // 获取闪退日志数量
    store.dispatch("getCrashLogNum");
    // 获取收藏话术权限
    store.dispatch("getCollectStoryPur");
    // 获取转发至特定人群
    store.dispatch("getForwardGroupType");
    // 获取vpn权限
    store.dispatch("getProxyTool");
    // 获取乐聊更新
    getUpdate();

    // 切换页面
    watch(() => router.currentRoute.value.path,
      (newValue, oldValue) => {
        ratio.value = window.devicePixelRatio;
        currRouter.value = newValue;
        if (newValue != "/index/chat") {
          store.dispatch("setImEditor", "");
        }
      }, {
        deep: true
      }
    )
    // 最大化变动
    watch(() => store.state.emit.isFullscreen,
      (newValue, oldValue) => {
        isMax.value = newValue;
      }, {
        deep: true
      }
    );
    // 电脑信息变动
    watch(() => store.state.computerInfo,
      (newValue, oldValue) => {
        computerInfo.value = newValue;
      }, {
        deep: true
      }
    );
    // 左侧tab列表监听
    watch(() => store.state.tabMap.leftList,
      (newValue, oldValue) => {
        let leftList = deepClone(newValue);
        let list = Object.values(tabMap.value);
        for (let i = 0; i < leftList.length; i++) {
          list.push({name: leftList[i].name, badge: 0, path: "toAppUrl", sort: 6 + i, type: "tab-left-" + leftList[i].img, url: leftList[i].requestUrl});
        }
        tabList.value = list.sort((a, b) => {return a.sort - b.sort});
      }, {
        deep: true
      }
    );
    // 监听会话变化获取未读数
    watch(() => store.state.updateSessionsTime,
      (newValue, oldValue) => {
        // 延迟渲染视图
        let sessionsUnread = store.getters.getSessions({count: -1});
        tabMap.value.msg.badge = sessionsUnread;
        if (sessionsUnread == 0) {
          // 未读数为0清除闪烁
          emitMsg("msg", {type: "tray", tray: 5});
        } else {
          // 存在未读数闪烁
          emitMsg("msg", {type: "tray", tray: 4});
        }
      }, {
        deep: true
      }
    );

    // 全局定时器
    watch(() => store.state.emit.globalTimer,
    (newValue, oldValue) => {
      // 登录的状态下
      if (store.state.userInfo.workerNo) {
        // 1分钟刷新一次
        if(Date.now() + store.getters.getDiffTime - scheduleStudyingTime.value > 60000){
          handleScheduleBadge();
        }
      }
    })
    // 聚焦主窗口
    watch(()=> store.state.emit.focus,()=>{
      handleScheduleBadge();
    })

    // 处理日程红点
    function handleScheduleBadge(){

      // 处理日程红点点
      quickActingGetTaskStatusBy1().then(res=>{
        tabMap.value.schedule.badge = res?.data || 0;
        scheduleStudyingTime.value = new Date().getTime()
      })
    }


    // 乐聊风格切换
    watch(() => store.state.settings,
      (newValue, oldValue) => {
        styleConfig.value = newValue[config.settings.type11];
      }, {
        deep: true
      }
    );
    // 窗口失去焦点
    watch(() => store.state.emit.blur,
      (newValue, oldValue) => {
        // 重置当前会话
        let currentSession = store.getters.getCurrentSession;
        if (currentSession.timer) {
          store.commit("setCancelCurrentSession");
        }
      }, {
        deep: true
      }
    );
    // 跳转新系统监听
    watch(() => store.state.emit.toNewSys,
      (newValue, oldValue) => {
        // 非乐聊锁定状态
        if (!store.getters.getLockIm) {
          toNewSys();
        }
      }, {
        deep: true
      }
    );
    // 分组修改回调
    watch(() => store.state.emit.initClassify,
      (newValue, oldValue) => {
        classifyEditorRef.value.initClassify(newValue);
      }, {
        deep: true
      }
    );
    // 全局点击
    watch(() => store.state.emit.click,
      (newValue, oldValue) => {
        imStatus.value = false;
      }, {
        deep: true
      }
    );
    // 锁定乐聊
    watch(() => store.state.emit.lockIm,
      (newValue, oldValue) => {
        lockIm();
      }, {
        deep: true
      }
    );
    // 跳转设置
    watch(() => store.state.emit.toSetting,
      (newValue, oldValue) => {
        settingIm();
      }, {
        deep: true
      }
    );
    // 获取乐聊更新
    watch(() => store.state.config.serverVersion,
      (newValue, oldValue) => {
        getUpdate();
      }, {
        deep: true
      }
    );

    // 点击跳转
    function toRouter(item) {
      let path = item.path;
      switch (path) {
        case "toDocSys":
          // 乐文档
          store.dispatch("setOpenWindow", [config[config.env].jjsHome + "/lyj-front/docs", "login"]);
          break;
        case "toRemind":
          // 备忘
          store.dispatch("setOpenWindow", [config[config.env].jjsHome + "/attend/main/index", "login"]);
          break;
        case "toWait":
          // 待办
          store.dispatch("setOpenWindow", [config[config.env].jjsHome + "/workflowplatform/toOldTodo", "login"]);
          break;
        case "toAppUrl":
          openAppUrl(item.url);
          break;
        case "toNewSys":
          // 新系统
          toNewSys();
          break;
        case "toCollect":
          // 收藏
          emitMsg('msg', {type: 'window', name: 'collect', newWin: 1, width: 780, height: 524, path: 'child/collect'});
          break;
        case "toAppSys":
          // app下载
          store.dispatch("setOpenWindow", [config[config.env].jjsHome + "/app_subject/index.html"]);
          break;
        case "toDataReport":
          store.dispatch("setOpenWindow", [config[config.env].jjsHome + "/lyj-front/bd-analysis-front/#/index", "login"]);
          setJJSEvent("P49303552", JSON.stringify({workerId: userInfo.workerId}));
          break;
        default:
          console.log('路由跳转', path)
          let routerParam = {path: path};
          if (currRouter.value == "/index/mailList" && store.getters.getEmit.mailLoading) {
            alert({
              content: `<span>当前正在执行退群/解散群操作，大概需要<span class="highlight">1-3</span>分钟，离开后，仍会在后台继续执行</span>`,
              zIndex: 300,
              done: type => {
                if (type == 1) {
                  routerChange(routerParam);
                  store.commit("setEmit", {type: "mailLoading", value: false});
                }
              }
            })
            return;
          }
          if (path == "/index/more") {
            routerParam.time = Date.now();
          } else if (currRouter.value != path && path == "/index/chat") {
            nextTick(() => {
              let id = store.getters.getCurrentSession.id;
              if (id) {
                store.dispatch("activeImEditor", {id: id});
              }
            });
          }
          routerChange(routerParam);
          break;
      }
    }

    // 路由跳转
    function routerChange(routerParam) {
      router.push(routerParam);
      // 路由跳转清空图片加载定时器
      store.commit("setImageLoadTimer", {type: "clear"});
      console.time("切换tab");
    }

    // 双击滚动到对应列表未读数位置，默认为顶部
    function scrollList() {
      store.commit("setEmit", {type: "scrollSessionList", value: new Date().getTime()});
    }

    // 跳转新系统
    async function toNewSys() {
      let url = await getNewSysUrl();
      if (url) {
        store.dispatch("setOpenWindow", [url]);
      }
    }

    // 最小化
    function winMin(type) {
      if (type == 1) {
        currentWindow.minimize();
      } else {
        store.commit("setWindowMin", currentWindow.cWindow.id);
      }
    }

    // 最大化
    function winMax() {
      if (currentWindow.isFullscreen) {
        isMax.value = false;
        store.commit("setWindowCancelMax", currentWindow.cWindow.id);
      } else {
        isMax.value = true;
        store.commit("setWindowMax", currentWindow.cWindow.id);
      }
    }


    // 显示用户详情弹窗
    function showUserInfo(account, e) {
      store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: account, selUserElm: e}});
    }

    // 切换乐聊状态
    function imStatusFun() {
      imStatus.value = !imStatus.value
    }

    //乐聊设置
    function settingIm() {
      if (!store.getters.getLockIm) {
        emitMsg("msg", {type: "window", newWin: 1, name: "setting", width: 550, height: 412, path: "child/setting"});
      }
    }

    //锁定乐聊
    let userHeadImg = ref(userInfo.headPic) //头像
    let userName = ref(userInfo.name) //名称
    let lockEmpNumber = ref(userInfo.workerNo) //锁定取工号
    let lockStatus = ref(false)  //锁定乐聊弹窗状态
    let lockContStatus = ref(false) //解锁还是输入密码状态
    let lockPassword = ref('') //锁定密码
    let lockPwdRef = ref();

    // 锁定乐聊
    function lockIm() {
      store.dispatch("setLockIm", true);
      lockStatus.value = true;
    }

    // 改变乐聊锁定状态
    function changeLock() {
      lockContStatus.value = !lockContStatus.value;
      lockPassword.value = '';
      showPwdFlag.value = false;
      if (lockContStatus.value) {
        nextTick(() => {
          lockPwdRef.value.focus();
        });
      }
    }

    // 解除乐聊锁定
    async function saveLock() {
      let res = await checkWorkApi({
        msgBody: JSON.stringify({
          username: lockEmpNumber.value,
          password: MD5(lockPassword.value.toLowerCase()),
          passwordv2: MD5(lockPassword.value),
        })
      })
      if (res.success) {
        lockStatus.value = false;
        showPwdFlag.value = false;
        changeLock();
        store.dispatch("setLockIm", false);
      } else {
        toast({title: res.addErrorMsg || res.errorMsg, type: 2});
      }
    }

    // 切换锁定乐聊密码显示
    function toggleShowPwd() {
      showPwdFlag.value = !showPwdFlag.value;
    }

    // 获取用户信息
    function getPerson(account) {
      return store.getters.getPersons(account);
    }

    // 是否有更新
    function getUpdate() {
      let {version, serverVersion} = store.getters.getConfig;
      isUpdate.value = compareVersion(version, serverVersion);
    }

    // 切换显示页面
    function showPage() {
      console.time("切换tab");
      nextTick(() => {
        nextTick(() => {
          console.timeEnd("切换tab", {path: currRouter.value});
        });
      })
      return true;
    }

    // 窗口聚焦
    function windowFocus(e) {
      let id = store.getters.getCurrentSession.id;
      store.commit("setEmit", {type: "windowFocus", value: Date.now()});
      if (id && currRouter.value == "/index/chat") {
        if (e.target.nodeName != "INPUT") {
          store.dispatch("activeImEditor", {id: id, active: true});
        }
      }
      // 设置当前会话
      debounce({
        timerName: "windowFocus",
        time: 300,
        fnName: function () {
          let id = store.getters.getCurrentSession.id;
          if (id && currRouter.value == "/index/chat") {
            store.dispatch("setCurrentSession", {id: id, type: "focus"});
          }
        }
      });
    }

    return {
      showPwdFlag,
      showPage,
      ratio,
      currRouter,
      userInfo,
      tabList,
      isMax,
      classifyEditorRef,
      chatSearchRef,
      styleConfig,
      isUpdate,

      winMin,
      winMax,
      avatarError,
      toRouter,
      scrollList,
      showUserInfo,
      imStatusFun,
      imStatus,
      settingIm,
      lockIm,
      userHeadImg,
      userName,
      lockStatus,
      lockContStatus,
      changeLock,
      toggleShowPwd,
      saveLock,
      lockPassword,
      lockPwdRef,
      getPerson,
      hideElm,
      userWearPic,
    }
  }
}
</script>
<style scoped lang="scss">
.bound-locking {
  display: none;
  position: fixed;
  left: 5px;
  top: 5px;
  right: 5px;
  bottom: 5px;
  z-index: 10;
  border-radius: 3px;
  background: rgb(240,240,240) url(/img/locking_bg.png) no-repeat;
  background-size: cover;

  &.show {
    display: block;
    z-index: 100;
  }

  .locking-cont {
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .locking-cont-img {
      font-size: 14px;
      width: 120px;
      margin: 0px auto 30px;
      overflow: hidden;
      color: #000;
      text-align: center;

      span {
        display: block;
        width: 120px;
        height: 120px;
        margin: 0 auto 15px;
        border-radius: 2px;
        overflow: hidden;

        img {
          display: block;
          width: 100%;
        }
      }

      p {
        text-align: center;
      }
    }

    .locking-cont-txt {
      font-size: 12px;
      height: 150px;
      overflow: hidden;

      .lock-input {
        position: relative;
        width: 100%;
        height: 150px;
        font-size: 12px;
        text-align: center;

        .lock-input-box {
          position: relative;
          margin-bottom: 35px;
          overflow: hidden;

          input {
            font-size: 12px;
            width: 238px;
            height: 30px;
            color: #000;
            text-align: left;
            padding: 0px 15px;
            border-radius: 2px;
            box-shadow: none;
            box-sizing: border-box;
          }

          .icon-pwd {
            width: 20px;
            height: 20px;
            background-image: url("/img/login/pwd_toggle.png");
            background-repeat: no-repeat;
            background-size: 80px 20px;
            cursor: pointer;

            &:hover {
              background-position: -20px 0;
            }

            &.show {
              background-position: -40px 0;

              &:hover {
                background-position: -60px 0;
              }
            }
          }

          .show-pwd {
            position: absolute;
            top: 50%;
            right: 8px;
            transform: translateY(-50%);
            cursor: pointer;
          }
        }

        .lock-btn-box {
          .locking-btn-ok {
            font-size: 12px;
            width: 68px;
            height: 24px;
            line-height: 22px;
            color: #000;
            margin: 0 5px;
            text-align: center;
            border: 1px solid #666;
            border-radius: 4px;
            cursor: pointer;
          }

          .locking-btn-cancel {
            font-size: 12px;
            width: 68px;
            height: 24px;
            line-height: 22px;
            color: #000;
            margin: 0 5px;
            text-align: center;
            border: 1px solid #666;
            border-radius: 4px;
            cursor: pointer;
          }
        }
      }

      .lock-button {
        height: 150px;

        .locking-btn-lock {
          font-size: 0;
          width: 300px;
          height: 44px;
          line-height: 42px;
          color: #fff;
          text-align: center;
          box-shadow: inset 0 0 3px $styleColor;
          border: 1px solid $styleColor;
          border-radius: 5px;
          box-sizing: border-box;
          background-color: $styleColor;
          cursor: pointer;

          i {
            display: inline-block;
            width: 15px;
            height: 19px;
            vertical-align: middle;
            margin-right: 10px;
            background: url(/img/lock_ico.png) no-repeat;
            background-size: 100%;
          }

          span {
            font-size: 18px;
            display: inline-block;
            vertical-align: middle;
          }
        }

        p {
          font-size: 14px;
          color: #000;
          margin: 80px 0;
          text-align: center;
        }
      }
    }
  }
}

.index {
  width: 100%;
  height: 100%;
  background: #E7E7EA;

  &.style-1 {
    .left {
      background: #2D91E6 !important;

      .tab-li {

        &.curr,
        &:hover {
          .tab-tips {
            opacity: 1 !important;
            color: #FFFFFF !important;
          }
        }

        @for $i from 1 to 13 {
          &.tab-li-#{$i} {
            &.curr,
            &:hover {
              i {
                background-image: url("/img/index/tab/style1/tab#{$i}_sel.png") !important;
              }
            }

            i {
              background-image: url("/img/index/tab/style1/tab#{$i}.png") !important;
            }
          }
        }

        @for $i from 1 to 5 {
          &.tab-li-tab-left-#{$i} {
            &.curr,
            &:hover {
              i {
                background-image: url("/img/index/tab/style1/tab_left#{$i}_sel.png") !important;
              }
            }

            i {
              background-image: url("/img/index/tab/style1/tab_left#{$i}.png") !important;
            }
          }
        }

        .tab-tips {
          opacity: 0.6 !important;
          color: #FFFFFF !important;
        }
      }

      .setting {
        i {
          background-image: url("/img/index/tab/style1/tab12.png") !important;

          &:hover {
            background-image: url("/img/index/tab/style1/tab12_sel.png") !important;
          }
        }
      }

      .setting-ul {
        background: #2782D0 !important;
        border: none !important;

        .setting-li {
          opacity: 0.6 !important;
          color: #FFFFFF !important;

          &:hover {
            background: #2272B7 !important;
          }
        }
      }
    }

    .search-box {
      width: calc(100% - 100px) !important;
    }
  }

  .operate-box {
    width: 100%;
    height: 52px;
    position: relative;
    z-index: 19;

    .search-box {
      width: calc(100% - 90px);
      height: 100%;
      position: relative;
    }
  }

  .operate-nav {
    position: fixed;
    top: 12px;
    right: 6px;
    display: flex;
    z-index: 351;

    i {
      width: 30px;
      height: 30px;
      cursor: pointer;
      background-image: url(/img/icon_operate.png);
      background-repeat: no-repeat;
      background-size: 240px 30px;
    }

    .min {
      &:hover {
        background-position: -30px 0;
      }
    }

    .max {
      background-position: -60px 0;

      &:hover {
        background-position: -90px 0;
      }

      &.show-max {
        background-position: -120px 0;

        &:hover {
          background-position: -150px 0;
        }
      }
    }

    .close {
      background-position: -180px 0;

      &:hover {
        background-position: -210px 0;
      }
    }
  }

  .content-box {
    width: 100%;
    height: 100%;
    display: flex;
    border: 1px solid #d8d8d8;

    &.border {
      border: 2px solid #d8d8d8;
    }

    .left {
      position: relative;
      width: 60px;
      height: 100%;
      z-index: 11;
      flex-shrink: 0;

      .user-avatar-box {
        height: 32px !important;
        width: 32px !important;
        margin: 11px auto 15px;
      }

      .tab-ul {
        width: 100%;

        .tab-li {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          padding: 7px 0 8px;

          i {
            display: block;
            width: 20px;
            height: 20px;
            margin: 0 auto;
            position: relative;
            background-repeat: no-repeat;
            background-size: 20px 20px;
          }

          .tab-tips {
            font-size: 11px;
          }

          &.curr {
            .tab-tips {
              font-weight: bold;
            }
          }

          &.curr,
          &:hover {
            .tab-tips {
              color: $styleColor;
            }
          }

          @for $i from 1 to 12 {
            &.tab-li-#{$i} {
              &.curr,
              &:hover {
                i {
                  background-image: url("/img/index/tab/tab#{$i}_sel.png");
                }
              }

              i {
                background-image: url("/img/index/tab/tab#{$i}.png");
              }
            }
          }

          @for $i from 1 to 5 {
            &.tab-li-tab-left-#{$i} {
              &.curr,
              &:hover {
                i {
                  background-image: url("/img/index/tab/tab_left#{$i}_sel.png");
                }
              }

              i {
                background-image: url("/img/index/tab/tab_left#{$i}.png");
              }
            }
          }

          .badge {
            position: absolute;
            top: -5px;
            left: 15px;
          }

          .tab-tips {
            color: #333333;
            font-size: 11px;
            line-height: 14px;
            margin-top: 4px;
          }
        }
      }

      .setting {
        position: absolute;
        bottom: 24px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 20px;
        cursor: pointer;

        i {
          display: block;
          width: 20px;
          height: 20px;
          background-image: url("/img/index/tab/tab12.png");
          background-repeat: no-repeat;
          background-size: 20px 20px;
          position: relative;

          &:hover {
            background-image: url("/img/index/tab/tab12_sel.png");
          }

          &.point {
            &:after {
              content: "";
              position: absolute;
              top: 0px;
              right: 0px;
              width: 8px;
              height: 8px;
              background: #f74b32;
              border-radius: 50%;
            }
          }
        }
      }

      .setting-ul {
        display: none;
        position: absolute;
        left: 48px;
        bottom: 22px;
        width: 116px;
        background: #FFFFFF;
        border-radius: 4px;
        box-shadow: 0px 2px 8px 0px rgba(165, 165, 165, 0.6);
        border: 1px solid #E5E5E5;
        overflow: hidden;

        &.show {
          display: block;
        }

        .setting-li {
          font-size: 14px;
          height: 43px;
          line-height: 43px;
          padding-left: 15px;
          color: #000000;
          overflow: hidden;

          &:hover {
            background: $styleBg1Hover;
          }
        }
      }
    }

    .main-content {
      width: calc(100% - 60px);
      height: 100%;
      border-left: none;

      .router {
        width: 100%;
        height: calc(100% - 52px) !important;
        background: $styleBg1;
        border-top-left-radius: 4px;
      }
    }
  }
}
</style>
