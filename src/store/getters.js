import {
  getSessionType, deepClone, strToHtml, regReplace, getFcwInfo, getPersons, getInputMessage, sortSessions, getChildWin, setWinMethod, isDefaultSessions, hasItemChildWin,
  isFcwList, getTeamType, getDefaultSessionUnread, setSessionField,
} from "@utils"
import config from "/config.js"

export default {
  // 返回对应对象
  getState: state => info => {
    return state[info];
  },
  // 获取对应窗口状态
  getWinState: state => info => {
    let chatChild = getChildWin("chat-" + info.id);
    if (chatChild) {
      return chatChild.window.store.state[info.key];
    } else {
      return state[info.key];
    }
  },
  // 获取日志
  getLogger: (state) => {
    return state.logger;
  },
  // 获取package配置信息
  getConfig: (state) => {
    return state.config;
  },
  // 获取窗口大小
  getWindowSizeType: (state) => {
    return state.windowSizeType;
  },
  // 获取当前窗口
  getCurrentWindow: (state) => (info) => {
    let win;
    try {
      // 在 Electron 中使用 remote 获取当前窗口
      if (window.remote && window.remote.getCurrentWindow) {
        win = window.remote.getCurrentWindow();
      } else if (remote && remote.getCurrentWindow) {
        win = remote.getCurrentWindow();
      } else {
        // 回退方案：创建一个模拟的窗口对象
        win = {
          getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
          setBounds: () => {},
          show: () => {},
          hide: () => {},
          close: () => {}
        };
      }

      if (info && getChildWin(info)) {
        win = getChildWin(info);
      }
      setWinMethod(win);
    } catch (error) {
      console.warn('获取当前窗口失败:', error);
      // 创建一个模拟的窗口对象
      win = {
        getBounds: () => ({ x: 0, y: 0, width: 800, height: 600 }),
        setBounds: () => {},
        show: () => {},
        hide: () => {},
        close: () => {}
      };
    }
    return win;
  },
  // 获取电脑信息
  getComputerInfo: (state) => {
    return state.computerInfo
  },
  // 获取电脑信息map
  getComputerMap: (state) => {
    return state.computerMap
  },
  // 获取电脑基础信息
  getBaseComputerInfo: (state) => {
    return state.baseComputerInfo
  },
  // 获取电脑网络连接信息
  getNetComputerInfo: (state) => {
    return state.netComputerInfo
  },
  // 获取登录用户信息
  getUserInfo: (state) => {
    return state.userInfo
  },
  // 获取云信对象
  getNim: (state) => {
    return state.nimSDK
  },
  // 获取文件数据库
  getFileDB: (state) => {
    return state.fileDB;
  },
  // 获取人员数据库
  getUserDB: (state) => {
    return state.userDB;
  },
  // 获取im编辑器对象
  getImEditor: (state) => {
    return state.imEditor;
  },
  // 获取当前会话信息
  getCurrentSession: (state) => {
    return state.currentSession
  },
  // 获取消息
  getMsgs: (state) => (info) => {
    if (info && info.id) {
      // 返回指定会话消息
      let msgs = state.msgs[info.id] || [];
      if (info.idServer) {
        return msgs.find(item => {return item.idServer == info.idServer});
      } else if (info.uniqueSign) {
        return msgs.find(item => {return item.uniqueSign == info.uniqueSign});
      } else {
        return msgs.sort((a, b) => {return (a.time || 0) - (b.time || 0)});
      }
    } else {
      // 返回全部会话
      return state.msgs;
    }
  },
  // 获取@我消息
  getHaitMsgMap: (state) => (info) => {
    if (info && info.id) {
      // 返回指定@自己消息
      if (info.sort) {
        let haitMsgMap = deepClone(state.haitMsgMap[info.id] || {});
        delete haitMsgMap.isShowTips;
        delete haitMsgMap.isHaitAll;
        return Object.values(haitMsgMap).sort((a, b) => {return a.time - b.time});
      } else {
        return state.haitMsgMap[info.id];
      }
    } else {
      // 返回所有@自己消息
      return state.haitMsgMap;
    }
  },
  // 获取特别关心消息
  getConcernMsgMap: (state) => (info) => {
    if (info && info.id) {
      // 返回指定特别关心消息
      if (info.sort) {
        let concernMsgMap = deepClone(state.concernMsgMap[info.id] || {});
        delete concernMsgMap.isShowTips;
        return Object.values(concernMsgMap).sort((a, b) => {return a.time - b.time});
      } else {
        return state.concernMsgMap[info.id];
      }
    } else {
      // 返回所有特别关心消息
      return state.concernMsgMap;
    }
  },
  // 获取云信好友列表
  getNimFriend: (state) => (info) => {
    if (info && info.account != null) {
      // 返回指定云信好友
      return getFcwInfo({account: info.account, nimFriend: state.nimFriend});
    } else if (info && info.val != null) {
      // 关键词匹配搜索好友列表
      let nimFriend = Object.values(state.nimFriend);
      let valReg = new RegExp(regReplace(info.val), "i");
      return nimFriend.filter(item => {return valReg.test(item.account) || valReg.test(item.name)});
    } else if (info && info.sort != null) {
      // 返回好友列表排序
      let nimFriend = Object.values(state.nimFriend);
      return nimFriend.sort((a, b) => {return a.createTime - b.createTime});
    } else {
      // 返回好友列表
      return state.nimFriend;
    }
  },
  // 获取会话-跟getSessionType方法的type一致 sort -1-默认会话 1-讨论组 2-高级群 3超大群 4-p2p 5-房产网 6-群通知 7-群助手 8-订阅号 9-服务号 10-全部群 11-服务号+订阅号 12-ai
  getSessions: function (state) {
    return function (info) {
      // 生产环境去除指令账号、未知服务号service000002
      let instructionNumber = `p2p-${config[config.env].instructionNumber}`;
      let service000002 = `p2p-service000002`;
      if (config.env == "online" && (state.sessions[instructionNumber] || state.sessions[service000002])) {
        delete state.sessions[instructionNumber];
        delete state.sessions[service000002];
      }
      if (info && info.sort) {
        if (info.sort == 11) {
          // 全部公众号数据
          let sessions = [];
          Object.values(state.allSubAndSer).map(item => {
            let thisSession = state.sessions["p2p-" + item.aNumber];
            sessions.push({
              scene: "p2p",
              to: item.aNumber,
              id: "p2p-" + item.aNumber,
              detailInfo: item,
              unread: thisSession ? thisSession.unread : 0,
            })
          });
          return {"11": sessions};
        }
        // 总会话
        let sessions = Object.values(state.sessions);
        // 是否有收入群助手会话
        let hasHelper = false;
        // 群助手对象
        let helpObj = {
          index: -1,// 在"-1"列表的排序
          unread: 0,// 未读数
          num: 0,// 未读会话数
          lastMsg: "",// 更新时间
        }
        // 订阅号对象
        let dyhObj = {
          index: -1,// 在"-1"列表的排序
          unread: 0,// 未读数
          updateTime: 0,// 更新时间
          lastMsg: "",// 最后一条消息
        };
        // 客户咨询对象
        let khzxObj = {
          index: -1,// 在"-1"列表的排序
          unread: 0,// 未读数
          updateTime: 0,// 更新时间
          lastMsg: "",// 最后一条消息
        }
        // 不同会话类型列表
        let sessionInfo = {
          "-1": [],// 默认会话
          "2": [],// 高级群
          "4": [],// p2p
          "5": [],// 房产网
          "7": [],// 群助手
          "8": [],// 订阅号
          "10": [],// 全部群
          "12": [],// ai
          hait: [],// @我
          remind: [],// 稍后处理
          mark: [],// 标记
          groupAndTeam: [],// 群和讨论组
          subordinate: [],// 直接下属
        };
        // 不同会话类型未读数
        let sessionUnreadInfo = {};
        for (let key in sessionInfo) {
          sessionUnreadInfo[key] = 0;
        }
        // 同事分组对象
        let colleagueChildMap = {};
        // 同事会话分组对象
        let colleagueClassifyMap = {};
        // 客户分组对象
        let customerChildMap = {};
        // 客户会话分组对象
        let customerClassifyMap = {};
        // 讨论组分组对象
        let groupChildMap = {};
        // 讨论组会话分组对象
        let groupClassifyMap = {};
        // 业务讨论组类型对象
        let groupTypeMap = deepClone(state.groupTypeMap);
        try {
          // 设置分组信息
          state.groupSettings.map((item, key) => {
            if (item.type == 1 || item.type == 2) {
              // 讨论组/群
              groupChildMap[item.uuid] = {...item, sortIndex: key, list: [], num: 0, key: `groupClassify-${item.uuid}`};
              if (item.value) {
                item.value.split(",").map(tItem => {
                  if (!groupClassifyMap[tItem]) {
                    groupClassifyMap[tItem] = {};
                  }
                  groupClassifyMap[tItem][item.uuid] = item.uuid;
                });
              }
            } else if (item.type == 3 && item.uuid != -1) {
              // 同事
              colleagueChildMap[item.uuid] = {...item, sortIndex: key, list: [], num: 0, key: `colleagueClassify-${item.uuid}`};
              if (item.value) {
                item.value.split(",").map(tItem => {
                  if (!colleagueClassifyMap[tItem]) {
                    colleagueClassifyMap[tItem] = {};
                  }
                  colleagueClassifyMap[tItem][item.uuid] = item.uuid;
                });
              }
            } else if (item.type == 4) {
              // 客户
              customerChildMap[item.uuid] = {...item, sortIndex: key, list: [], num: 0, key: `customerClassify-${item.uuid}`};
              if (item.value) {
                item.value.split(",").map(tItem => {
                  if (!customerClassifyMap[tItem]) {
                    customerClassifyMap[tItem] = {};
                  }
                  customerClassifyMap[tItem][item.uuid] = item.uuid;
                });
              }
            }
          });
        } catch (e) {
          console.log("getClassifyMapErr", e)
        }
        // 可以被折叠的置顶会话数量
        let foldTopNum = 0;
        // 总置顶数
        let topNum = 0;
        // 最后一个置顶会话信息
        let lastSessionTopInfo = {
          updateTime: 0,
          id: ""
        };
        state.lastSessionTopId = "";
        // 特别关心数量
        let concernCount = 0;
        for (let i = 0; i < sessions.length; i++) {
          let item = sessions[i];
          // 云信updateTime不准确
          if (item.lastMsg) {
            if (item.lastMsg.time > item.updateTime) {
              item.updateTime = item.lastMsg.time;
            }
          }
          let sessionType = getSessionType(item).type;
          if (item.to == config.systemMessage && item.localCustom) {
            item.updateTime = item.localCustom.updateTime;
          }
          // 判断是否存在子窗口
          let hasChildWin = hasItemChildWin(info, item);
          // 全部群
          if (!(sessionType != 1 && sessionType != 2 && sessionType != 3)) {
            sessionInfo["10"].push(item);
            sessionUnreadInfo["10"] += item.unread || 0;
          }
          // 客户分组插入对应会话
          if (customerClassifyMap[item.to]) {
            for (let key in customerClassifyMap[item.to]) {
              let customerChildMapItem = customerChildMap[customerClassifyMap[item.to][key]];
              customerChildMapItem.num += getDefaultSessionUnread(item);
              customerChildMapItem.list.push(item);
            }
          }
          let isDefault = isDefaultSessions(item, sessionType, hasChildWin);
          setSessionField(item);
          if (isDefault || item.isHelper) {
            // 获取@我列表
            if (item.unread > 0 && state.haitMsgMap[item.id] && (state.haitMsgMap[item.id].isShowTips || state.haitMsgMap[item.id].isHaitAll)) {
              item.haitInfo = state.haitMsgMap[item.id];
              sessionInfo["hait"].push(item);
              sessionUnreadInfo["hait"] += item.unread || 0;
            } else {
              item.haitInfo = {};
            }
            // 获取稍后处理列表
            if (state.localUnreadMap[item.id]) {
              sessionInfo["remind"].push(item);
              sessionUnreadInfo["remind"] += item.unread || 0;
            }
            // 获取标记会话
            if (state.importantMarkMap[item.id]) {
              sessionInfo["mark"].push(item);
              sessionUnreadInfo["mark"] += item.unread || 0;
            }
            // 获取特别关心会话
            if (item.unread > 0 && state.concernMsgMap[item.id]?.isShowTips) {
              item.isConcern = true;
              concernCount++;
            } else {
              item.isConcern = false;
            }
            // 直接下属
            if (state.subordinateMap[item.to]) {
              sessionInfo["subordinate"].push(item);
              sessionUnreadInfo["subordinate"] += item.unread || 0;
            }
            if (isDefault) {
              // 默认会话不返回房产网、进线客户讨论组、群助手、订阅号、售后讨论组
              sessionInfo["-1"].push(item);
              sessionUnreadInfo["-1"] += getDefaultSessionUnread(item);
              if (sessionType == 1 || sessionType == 2) {
                // 群和讨论组
                sessionInfo["groupAndTeam"].push(item);
                sessionUnreadInfo["groupAndTeam"] += getDefaultSessionUnread(item);
                // 群
                if (sessionType == 2) {
                  sessionInfo["2"].push(item);
                  sessionUnreadInfo["2"] += getDefaultSessionUnread(item);
                }
              } else if (sessionType == 4) {
                // 私聊会话
                if (new RegExp(config.ai).test(item.to)) {
                  // ai
                  sessionInfo["12"].push(item);
                  sessionUnreadInfo["12"] += getDefaultSessionUnread(item);
                } else {
                  sessionInfo["4"].push(item);
                  sessionUnreadInfo["4"] += getDefaultSessionUnread(item);
                }
              }
              if (item.isTop) {
                if ((item.to != config.subscribe && item.to != config.systemMessage && item.to != config.helperAccount && item.to != config.customerAccount)
                  && (!lastSessionTopInfo.updateTime || item.updateTime == 1 || item.updateTime < lastSessionTopInfo.updateTime)) {
                  // 设置最后一个置顶会话信息-去除会话盒子
                  lastSessionTopInfo.updateTime = item.updateTime;
                  lastSessionTopInfo.id = item.id;
                }
                // 计算可折叠的会话数量
                if (!item.unread && !item.alwaysTop && state.currentSession.id != item.id) {
                  foldTopNum++;
                }
                topNum++;
              }
              // 设置群助手、订阅号、客户咨询下标
              switch (String(item.to)) {
                case config.helperAccount:
                  helpObj.index = sessionInfo["-1"].length - 1;
                  break
                case config.subscribe:
                  dyhObj.index = sessionInfo["-1"].length - 1;
                  break
                case config.customerAccount:
                  khzxObj.index = sessionInfo["-1"].length - 1;
                  break
              }
              if (item.scene == "team") {
                // 判断是否存在分组
                if (groupClassifyMap[item.to]) {
                  for (let key in groupClassifyMap[item.to]) {
                    // 讨论组分组插入对应群聊会话
                    let groupChildMapItem = groupChildMap[groupClassifyMap[item.to][key]];
                    groupChildMapItem.num += getDefaultSessionUnread(item);
                    groupChildMapItem.list.push(item);
                  }
                }
                // 设置业务类型讨论组
                if (item.detailInfo?.groupType && groupTypeMap[item.detailInfo.groupType] && state.groupTypeShowMap[item.detailInfo.groupType]) {
                  // 获取最新接口讨论组名
                  groupTypeMap[item.detailInfo.groupType].name = state.groupTypeShowMap[item.detailInfo.groupType].label;
                  groupTypeMap[item.detailInfo.groupType].list.push(item);
                  groupTypeMap[item.detailInfo.groupType].num += getDefaultSessionUnread(item);
                }
              } else if (item.scene == "p2p") {
                if (colleagueClassifyMap[item.to]) {
                  for (let key in colleagueClassifyMap[item.to]) {
                    // 同事分组插入对应会话
                    let colleagueChildMapItem = colleagueChildMap[colleagueClassifyMap[item.to][key]];
                    colleagueChildMapItem.num += getDefaultSessionUnread(item);
                    colleagueChildMapItem.list.push(item);
                  }
                }
              }
            } else {
              // 群助手
              hasHelper = true;
              if (item.isHelper && !(hasChildWin || sessionType == 14)) {
                sessionInfo["7"].push(item);
                sessionUnreadInfo["7"] += item.unread || 0;
              }
              if (item.unread) {
                helpObj.unread += item.unread || 0;
                helpObj.num++;
              }
              if (item.lastMsg && !hasChildWin) {
                // 群助手设置最后一个会话消息
                if (!helpObj.lastMsg || (helpObj.lastMsg && helpObj.lastMsg.time < item.lastMsg.time)) {
                  helpObj.lastMsg = item.lastMsg;
                }
              }
            }
          } else {
            if (sessionType == 8 && item.to != config.subscribe) {
              dyhObj.unread += item.unread || 0;
              // 订阅号设置更新时间
              if (dyhObj.updateTime < item.updateTime) {
                dyhObj.updateTime = item.updateTime;
                if (item.lastMsg) {
                  dyhObj.lastMsg = item.lastMsg;
                }
              }
            } else if (isFcwList(item, "", "", true)) {
              // 房产网设置客户咨询会话时间
              if (!item.isNoTip) {
                khzxObj.unread += item.unread || 0;
              }
              // 订阅号设置更新时间
              if (khzxObj.updateTime < item.updateTime) {
                khzxObj.updateTime = item.updateTime;
                if (item.lastMsg) {
                  khzxObj.lastMsg = item.lastMsg;
                }
              }
            }

            // 客户列表 进线客户讨论组(只有群主显示)、售后讨论组不显示
            if (isFcwList(item, "", "", true)) {
              sessionInfo["5"].push(item);
              sessionUnreadInfo["5"] += item.unread || 0;
            }
            // 房产网备注
            if (sessionType == 5) {
              item.detailInfo = getFcwInfo({account: item.to, nimFriend: state.nimFriend});
            }
            // 订阅号
            if (sessionType == 8 && item.to != config.subscribe) {
              sessionInfo["8"].push(item);
              sessionUnreadInfo["8"] += item.unread || 0;
            }
          }
          let showLastMsg = this.getPrimaryMsg({msg: item.lastMsg || {sessionId: item.id}, primaryType: 3, msgFlag: true});
          item.showLastMsg = showLastMsg.text;
          item.showLastTime = showLastMsg.msg?.time;
          item.showLastFrom = showLastMsg.msg?.from;
          item.showLastStatus = showLastMsg.msg?.status;
          setSessionField(item);
        }
        // 设置可折叠置顶数量
        state.topObj = {
          foldTopNum: foldTopNum,
          topNum: topNum
        }
        // 设置最后一个置顶会话id
        state.lastSessionTopId = lastSessionTopInfo.id;
        if (sessionInfo["-1"].length > 0) {
          // 设置群助手消息
          if (helpObj.index > -1 && sessionInfo["-1"][helpObj.index]?.to == config.helperAccount) {
            let thisSession = sessionInfo["-1"][helpObj.index];
            if (!hasHelper) {
              // 去除群助手最后一个会话消息
              delete thisSession.lastMsg;
            } else {
              thisSession.lastMsg = helpObj.lastMsg;
              thisSession.updateTime = helpObj.lastMsg.time;
            }
            // 设置群助手存在多少个未读会话
            thisSession.helperNum = helpObj.num;
            thisSession.showLastMsg = this.getPrimaryMsg({msg: thisSession.lastMsg, primaryType: 3});
            thisSession.showLastTime = thisSession.updateTime;
          }
          // 设置订阅号
          if (dyhObj.index > -1 && sessionInfo["-1"][dyhObj.index]?.to == config.subscribe) {
            let thisSession = sessionInfo["-1"][dyhObj.index];
            // 设置订阅号未读数
            thisSession.unread = dyhObj.unread;
            thisSession.updateTime = dyhObj.updateTime;
            // 设置订阅号最后一条消息提示
            if (thisSession.unread > 0) {
              dyhObj.lastMsg = {scene: "p2p", type: "text", text: `[有${thisSession.unread < 100 ? thisSession.unread : "99+"}条订阅号消息]`};
            }
            thisSession.lastMsg = dyhObj.lastMsg;
            thisSession.showLastMsg = this.getPrimaryMsg({msg: thisSession.lastMsg, primaryType: 3});
            thisSession.showLastTime = thisSession.updateTime;
          }
          // 设置客户咨询
          if (khzxObj.index > -1 && sessionInfo["-1"][khzxObj.index]?.to == config.customerAccount) {
            let thisSession = sessionInfo["-1"][khzxObj.index];
            thisSession.unread = khzxObj.unread;
            thisSession.updateTime = khzxObj.updateTime;
            // 设置客户咨询最后一条消息提示
            if (thisSession.unread > 0) {
              khzxObj.lastMsg = {scene: "p2p", type: "text", isTop: true, text: `[有${thisSession.unread < 100 ? thisSession.unread : "99+"}条客户咨询消息]`};
            }
            thisSession.lastMsg = khzxObj.lastMsg;
            thisSession.showLastMsg = this.getPrimaryMsg({msg: thisSession.lastMsg, primaryType: 3});
            if (thisSession.updateTime > Date.now() - 365 * 24 * 60 * 60 * 1000) {
              thisSession.showLastTime = thisSession.updateTime;
            }
          }
        }

        for (let key in sessionInfo) {
          sessionInfo[key] = sortSessions(sessionInfo[key]);
        }

        // 设置客户咨询未读数
        sessionInfo.khzxUnread = khzxObj.unread;
        // 设置特别关心数量
        sessionInfo.concernCount = concernCount;
        // 设置所有分类会话未读数
        sessionInfo.unreadMap = sessionUnreadInfo;
        // 设置讨论组分组列表
        sessionInfo.groupChildList = Object.values(groupChildMap).sort((a, b) => {return a.sortIndex - b.sortIndex});
        sessionInfo.groupChildList.map(groupChildItem => {
          groupChildItem.list = sortSessions(groupChildItem.list);
        });
        // 设置业务讨论组列表
        for (let key in groupTypeMap) {
          // 删除不存在可创建的业务讨论组
          if (!groupTypeMap[key].name || groupTypeMap[key].list.length == 0) {
            delete groupTypeMap[key];
          }
        }
        sessionInfo.groupTypeList = Object.values(groupTypeMap).sort((a, b) => {return a.sortIndex - b.sortIndex});
        sessionInfo.groupTypeList.map(groupTypeItem => {
          groupTypeItem.list = sortSessions(groupTypeItem.list);
        });
        // 设置同事分组列表
        sessionInfo.colleagueChildList = Object.values(colleagueChildMap).sort((a, b) => {return a.sortIndex - b.sortIndex});
        sessionInfo.colleagueChildList.map(colleagueChildItem => {
          colleagueChildItem.list = sortSessions(colleagueChildItem.list);
        });
        // 设置客户分组列表
        sessionInfo.customerChildList = Object.values(customerChildMap).sort((a, b) => {return a.sortIndex - b.sortIndex});
        sessionInfo.customerChildList.map(customerChildItem => {
          customerChildItem.list = sortSessions(customerChildItem.list);
        });

        return sessionInfo;
      } else if (info && info.id) {
        // 返回指定会话
        let thisSession = {};
        if (info.temp) {
          // 返回临时会话
          thisSession = state.sessionsTemp[info.id] || {};
          if (state.sessions[info.id]?.to) {
            thisSession.isNoTip = state.sessions[info.id].isNoTip;
            thisSession.isHelper = state.sessions[info.id].isHelper;
          }
        } else {
          thisSession = state.sessions[info.id] || state.sessionsTemp[info.id] || {};
        }
        return deepClone(thisSession);
      } else if (info && info.count) {
        // 统计未读数
        let sessionsUnread = 0;
        let sessionList = Object.values(state.sessions);
        for (let i = 0; i < sessionList.length; i++) {
          let item = sessionList[i];
          // 判断是否存在子窗口
          let sessionType = getSessionType(item).type;
          let hasChildWin = hasItemChildWin(info, item);
          // 默认会话不返回房产网、进线客户讨论组、群助手、订阅号、售后讨论组
          if (isDefaultSessions(item, sessionType, hasChildWin)) {
            sessionsUnread += getDefaultSessionUnread(item);
          }
          if (sessionsUnread > 99) {
            break;
          }
        }
        return sessionsUnread;
      } else {
        // 返回全部会话
        return state.sessions;
      }
    }
  },
  // 获取更新会话id
  getUpdateSessionId: function (state) {
    return state.updateSessionId;
  },
  // 获取更新群成员数据
  getUpdateMemberTeamInfo: function (state) {
    return state.updateMemberTeamInfo;
  },
  // 获取群
  getTeams: (state) => (info) => {
    if (info && info.sort) {
      // 返回会话排序
      let teams = Object.values(state.teams).sort((a, b) => {return b.createTime - a.createTime});
      return teams;
    } else if (info && info.id) {
      // 返回指定会话
      return state.teams[info.id] || {};
    } else if (info && info.ids && Array.isArray(info.ids)) {
      return info.ids.map(i => state.teams[i])
    } else if (info && info.val) {
      let teamsInfo = {
        group: [],
        team: []
      }
      // 返回模糊搜索群和讨论组
      for (let key in state.teams) {
        let item = state.teams[key];
        // 可以根据群名、群号搜索
        if (!new RegExp(regReplace(info.val), "i").test(item.name) && !new RegExp(regReplace(info.val), "i").test(item.teamId)) {
          continue;
        }
        // 进线客户讨论组(只有群主显示)、售后讨论组不显示
        if (isFcwList("", "", item, true, true)) {
          continue;
        }
        if (item.detailType == "group") {
          teamsInfo.group.push(item);
        } else {
          teamsInfo.team.push(item);
        }
      }
      return teamsInfo;
    } else if (info && info.type) {
      // 返回全部群类型 type-1讨论组-2群-3超大群-4群和超大群-5非客户讨论组和群
      let list = [];
      for (let key in state.teams) {
        let item = state.teams[key];
        if (info.type == 1 && item.detailType == "group") {
          // 进线客户讨论组(只有群主显示)、售后讨论组不显示
          if (!isFcwList("", "", item, true, true)) {
            list.push(item);
          }
        } else if ((info.type == 2 || info.type == 4) && item.detailType == "team") {
          list.push(item);
        } else if ((info.type == 3 || info.type == 4) && item.detailType == "superTeam") {
          list.push(item);
        } else if (info.type == 5) {
          // 过滤客户讨论组
          if (!isFcwList("", item.teamId, item)) {
            list.push(item);
          }
        } else if (info.type == 6) {
          // 群和进线客户讨论组(只有群主显示)、售后讨论组不显示
          if (!isFcwList("", item.teamId, item, true, true)) {
            list.push(item);
          }
        }
      }
      return list;
    } else {
      // 返回全部会话
      return state.teams;
    }
  },
  // 返回本地群成员信息
  getTeamMembersLocal: (state) => (info) => {
    let {account, err, teamMembers, id} = info;
    if (!teamMembers && id) {
      teamMembers = state.teamMembers[id] || [];
    }
    if (account) {
      // 返回指定成员
      return {err: err, obj: teamMembers.find(item => {return account == item.account}) || {}};
    } else {
      // 返回整个群成员列表
      return {err: err, obj: teamMembers};
    }
  },
  // 获取人员 info为6位数工号/数组
  getPersons: (state) => (info) => {
    return getPersons(state, info);
  },
  // 获取订阅号和服务号
  getSubAndSer: (state) => (info) => {
    if (info && info.val) {
      let subAndSerInfo = {
        sub: [],
        ser: []
      }
      // 返回模糊搜索群和讨论组
      for (let key in state.allSubAndSer) {
        let item = state.allSubAndSer[key];
        if (!new RegExp(regReplace(info.val), "i").test(item.name)) {
          continue;
        }
        if (new RegExp(config.subscribe, "i").test(key) && key != config.subscribe) {
          subAndSerInfo.sub.push(item);
        } else if (new RegExp(config.serveNumber, "i").test(key)) {
          subAndSerInfo.ser.push(item);
        }
      }
      return subAndSerInfo;
    } else if (info && info.type) {
      let list = [];
      // type-1订阅号2服务号
      for (let key in state.allSubAndSer) {
        let item = state.allSubAndSer[key];
        if (info.type == 1 && new RegExp(config.subscribe, "i").test(key) && key != config.subscribe) {
          list.push(item);
        } else if (info.type == 2 && new RegExp(config.serveNumber, "i").test(key)) {
          list.push(item);
        }
      }
      return list;
    } else {
      // 返回全部
      return state.allSubAndSer;
    }
  },
  // 获取所有分组信息
  getGroupSetting: function (state) {
    return state.groupSettings;
  },
  // 获取所有分组信息
  getFindPersons: function (state) {
    return function (info) {
      if (info && info.val) {
        // 返回模糊搜索人员列表
        let personList = [];
        // 数组下标
        let index = 0;
        // 搜索到的人数
        let searchNum = 0;
        // 是否有搜索到的人
        let hasPerson = false;
        let information = state.findPersons.information;
        if (information && information.length > 0) {
          for (let i = 0; i < information.length; i++) {
            let groupList = information[i].groupList;
            if (groupList && groupList.length > 0) {
              for (let j = 0; j < groupList.length; j++) {
                let spawVoList = groupList[j].spawVoList;
                if (spawVoList && spawVoList.length > 0) {
                  hasPerson = false;
                  personList[index] = [];
                  for (let k = 0; k < spawVoList.length; k++) {
                    let valReg = new RegExp(regReplace(info.val), "i");
                    // 服务端的人员数据
                    let personInfoServer = deepClone(getPersons(state, spawVoList[k].workerNo));
                    // 去除空数据
                    for (let key in personInfoServer) {
                      if (!personInfoServer[key]) {
                        delete personInfoServer[key];
                      }
                    }
                    let personInfo = Object.assign(spawVoList[k], personInfoServer);
                    // spawVoList[k]公司名多了个分号
                    personInfo.companyName = information[i].compayName;
                    personInfo.deptName = groupList[j].deptName;
                    if (valReg.test(personInfo.nickname) || valReg.test(personInfo.obligation) || valReg.test(personInfo.selfIntro)) {
                      // 加入符合模糊搜索条件的人员信息
                      personInfo.searchNum = searchNum;
                      personList[index].push(personInfo);
                      searchNum++;
                      hasPerson = true;
                    }
                  }
                  // 判断是否有搜索结果
                  if (hasPerson) {
                    index++;
                  } else {
                    personList.splice(index, 1);
                  }
                }
              }
            }
          }
        }
        // 设置搜索的总数量
        if (personList[0] && personList[0][0]) {
          personList[0][0].allNumber = searchNum;
        }
        return personList;
      } else {
        // 默认返回全部数据
        return state.findPersons;
      }
    }
  },
  // 搜索本地应用
  getAppList: function (state, info) {
    return function (info) {
      let list = [];
      for (let i = 0; i < state.allAppList.length; i++) {
        let item = state.allAppList[i];
        if (new RegExp(regReplace(info.value), "i").test(item.name)) {
          list.push(item);
        }
        if (list.length >= 50) {
          break;
        }
      }
      return deepClone(list);
    }
  },
  // 获取简单消息解析 type-默认为查询最后一条消息,1为不显示发送人信息,2为不查询,3为返回查询的最后一条消息,4为不查询且不显示发送人
  getPrimaryMsg: function (state) {
    return function (info) {
      let {msg, primaryType, nameFlag, msgFlag} = info;
      let text = "";
      if (!msg) {
        return text;
      }
      if (!primaryType || primaryType == 2 || primaryType == 3) {
        if (primaryType == 3) {
          // 查询会话最后消息最后一条
          let thisMsgs = state.msgs[msg.sessionId];
          if (thisMsgs && thisMsgs.length > 0) {
            // 消息列表偶尔比会话列表的最后一条消息延迟
            let thisMsg = thisMsgs[thisMsgs.length - 1];
            if (thisMsg.time >= msg?.time) {
              msg = thisMsgs[thisMsgs.length - 1];
            }
          }
        }
        if (msg.scene) {
          let personName = state.persons[msg.from] ? state.persons[msg.from].name : msg.from;
          text = (msg.scene != "p2p" || nameFlag ? (msg.from == state.userInfo.workerNo ? "我" : personName) + ":" : "");
        }
      }
      let msgType = msg.type;
      if (msgType) {
        switch (msgType) {
          case "text":
            text += msg.text;
            break;
          case "image":
            text += "[图片]";
            break;
          case "file":
            if (msg.forbidFileMsg) {
              text += "[非法文件，已被本站拦截]";
            } else {
              text += "[文件]";
            }
            break;
          case "audio":
            text += "[语音]";
            break;
          case "video":
            text += "[视频]";
            break;
          case "geo":
            text += "[位置]";
            break;
          case "tip":
            let tips = msg.tip;
            if (msg.custom) {
              let thisCustom = deepClone(msg.custom);
              try {
                if (typeof thisCustom == "string") {
                  thisCustom = JSON.parse(thisCustom)
                }
              } catch (e) {
              }
              if (thisCustom.type == "audio") {
                tips = "[提醒消息]";
              }
            }
            let personName = state.persons[msg.from] ? state.persons[msg.from].name : msg.from;
            text = msg.tip == "撤回了一条消息" ? (msg.from == state.userInfo.workerNo ? "您" : personName) + "撤回了一条消息" : tips;
            break;
          case "custom":
            let content = msg.content;
            if (content) {
              content = deepClone(content);
              try {
                if (typeof content == "string") {
                  content = JSON.parse(content);
                }
              } catch (e) {
              }
              if (content?.type) {
                switch (String(content.type)) {
                  case "1":
                    text += "[猜拳]";
                    break;
                  case "2":
                    text += "[阅后即焚]";
                    break;
                  case "3":
                    text += "[贴图]";
                    break;
                  case "4":
                    text += "[白板]";
                    break;
                  case "5":
                  case "6":
                  case "7":
                    if (content.data?.sourceType == "minApp-ycya") {
                      text += content.data.title;
                    } else {
                      text += "[房源]";
                    }
                    break;
                  case "8":
                    let sourceType = content.data?.sourceType || "";
                    if (content && sourceType == "app-lxt-zb") {
                      text += "暂不支持的消息,请往乐办公查看";
                    } else {
                      text += content.data?.title || "";
                    }
                    break;
                  case "9":
                    text += "[聊天记录]";
                    break;
                  case "10":
                  case "11":
                    text += "您有一条不支持的消息,可下载房源网APP在手机端查看";
                    break;
                  case "multi":
                    for (let i = 0; i < content?.msgs?.length; i++) {
                      let thisItem = content.msgs[i];
                      switch (thisItem.type) {
                        case "text":
                          text += thisItem.text;
                          break;
                        case "image":
                          text += "[图片]";
                          break;
                        case "document":
                          text += "[乐文档]";
                          break;
                        case "file":
                          text += "[文件]";
                          break;
                      }
                    }
                    break;
                  case "shake":
                    text += "[抖一抖]";
                    break;
                  case "collect":
                    text += "[收藏]";
                    break;
                  case "minApp":
                    text += "您有一条" + (content.data?.minAppTitle || "") + "信息，请到乐办公APP查看。";
                    break;
                  case "msg-center":
                  case "msg-center-link":
                    text += content.data?.title || "[消息平台]";
                    break;
                  case "msg-report":
                    text += content.data?.title || "[消息播报]";
                    break;
                  case "imJZSell":
                    text += "[集中销售]";
                    break;
                  case "imNewHouseJZSell":
                    text += "[新房集中销售]";
                    break;
                  case "imSomeHouseRecommend":
                  case "imHouseBatchShare":
                    text += "[批量分享房源]";
                    break;
                  case "imShortVideo":
                    text += "[乐学堂短视频分享]";
                    break;
                  case "card":
                    // 新增卡片消息简介字段
                    if (content.labText) {
                      text += content.labText;
                    } else if (content.data?.cardType == 4) {
                      text += "[审批] " + content.data.title || "";
                    } else {
                      text += "[系统消息]";
                    }
                    break;
                  case "jzxs-approval-notice":
                    text += content.data?.title || "[集中销售通知]";
                    break;
                  case "minApp_YLYK_Detail":
                    text += "[有料有客]";
                    break;
                  case "imCourseShare":
                    text += "[乐学堂课程]";
                    break;
                  case "imNews":
                    text += content.data?.title ? `[${content.data.title}]` : "[资讯卡片]";
                    break;
                  case "cooperate":
                    text += content.data?.title ? `[${content.data.title}]` : "[合作邀请]";
                    break;
                  case "ai-card":
                    text += content.data.title + " " + content.data.intr;
                    break;
                  case "ai-msg":
                    for (let i = 0; i < content?.msgs?.length; i++) {
                      let thisItem = content.msgs[i];
                      switch (thisItem.type) {
                        case "text":
                        case "intr":
                        case "link":
                        case "column":
                          text += thisItem.text;
                          break;
                        case "image":
                          text += "[图片]";
                          break;
                      }
                    }
                    text = text || "[ai卡片]";
                    break;
                  case "ai-app-card":
                    text += content.data?.title ? `[${content.data.title}]` : "[自定义消息]";
                    break;
                  case "lxt_signup":
                    text += content.data?.title ? `[${content.data.title}]` : "[培训报名]";
                    break;
                  case "monad-card":
                    text += content.data?.title ? `[${content.data.title}]` : "[面施工单]";
                    break;
                  default:
                    text += "[自定义消息]";
                    break;
                }
              } else {
                text += "[自定义消息]";
              }
            }
            break;
          case "notification":
            // 通知消息解析
            text = "[通知消息]";
            if (msg.announcement) {
              text = "[群公告]";
            }
            break;
          default:
            text += "[未知消息类型]";
            break;
        }
      }
      // 是否被反垃圾
      if (state.antispamMap[msg.sessionId] && state.antispamMap[msg.sessionId][msg.idServer]) {
        text = "[消息涉嫌违规，不支持查看]";
      }
      let formatText = (text || "").replace(/\n/g, " ");
      formatText = buildEmoji(strToHtml(formatText, true));
      if (msgFlag) {
        return {text: formatText, msg: msg};
      } else if (info.notChange) {
        // 转发的时候不转义pushContent内容
        return text;
      } else {
        return formatText;
      }
    }
  },
  // 获取emit数据
  getEmit: function (state, info) {
    return state.emit;
  },
  // 获取云信文件上传/下载进程
  getNimFileUpload: (state) => (info) => {
    return state.nimFileUpload[info];
  },
  // 获取本地时间和服务器时间差
  getDiffTime: function (state, info) {
    return state.diffTime;
  },
  // 获取本地未读会话
  getLocalUnreadMap: (state) => {
    return state.localUnreadMap;
  },
  // 获取黑名单列表
  getBlacklist: (state) => (info) => {
    if (info && info.sort) {
      // 返回黑名单排序
      return Object.values(state.blackMap).sort((a, b) => {return b.updateTime - a.updateTime});
    } else if (info && info.id) {
      // 返回指定黑名单
      return state.blackMap[info.id];
    } else {
      return state.blackMap;
    }
  },
  // 获取特别关心/重要标记列表
  getConcernOrImportantMarkMap: (state) => (info) => {
    let objMap = state.concernMap;
    if (info.keyType == 2) {
      objMap = state.importantMarkMap;
    }
    if (info && info.sort) {
      // 返回列表
      return Object.values(objMap);
    } else if (info && info.id) {
      // 返回指定对象
      return objMap[info.id];
    } else {
      return objMap;
    }
  },
  // 获取引用消息被撤回记录
  getQuoteDeleteMap: (state) => (info) => {
    return state.quoteDeleteMap[info] || {};
  },
  // 获取最近联系人
  getRecentContactsMap: (state) => {
    return state.recentContactsMap;
  },
  // 获取引用消息
  getQuoteMsg: (state) => {
    return state.quoteMsg;
  },
  // 获取@信息
  getHaitInfo: (state) => {
    return state.haitInfo;
  },
  // 获取好友数据
  getFriends: (state) => {
    return state.friends
  },
  // 返回通讯录数据结构
  getMailList: (state) => {
    return state.mailList
  },
  // 获取聚焦元素-消息高亮
  getFocusMsg: (state) => {
    return state.focusMsg
  },
  // 获取经纪人合作信息
  getCooperateInfo: (state) => (info) => {
    return state.cooperateInfo[info.account];
  },
  // 获取对应人的人事档案权限
  getHrAuthority: (state) => (info) => {
    return state.hrAuthority[info.account];
  },
  // 获取服务器配置信息
  getSettings: (state) => {
    return state.settings;
  },
  // 获取窗口抖动时间
  getShakeTimeMap: (state) => (info) => {
    return state.shakeTimeMap[info];
  },
  // 获取本地入群通知
  getLocalMsgs: (state) => {
    return state.teamLocalMsgs;
  },
  // 获取日程弹窗
  getScheduleModal: (state) => {
    return state.scheduleModal;
  },
  // 获取强提醒消息
  getRemindMsg: (state) => {
    return state.remindMsg;
  },
  // 获取小乐信息
  getXlInfo: (state) => (info) => {
    if (info.custom && info.custom.hait && info.custom.hait.length > 0) {
      if (info.isAll) {
        // 去除@全员
        info.text = info.text.replace(`@ 全员 `, "");
      }
      for (let i = 0; i < info.custom.hait.length; i++) {
        let item1 = info.custom.hait[i];
        let item1Name = info.custom.atName[i];
        if (!info.isAll && info.xlNo != item1) {
          // 统计@的人
          info.tempAts.push(item1);
        }
        if (item1Name) {
          info.text = info.text.replace(item1Name, "");
        } else if (state.persons[item1]) {
          info.text = info.text.replace(`@${state.persons[item1].name} `, "");
        }
      }
    }
    return info;
  },
  // 获取语音已读标识
  getAudioTipMsg: (state) => (info) => {
    if (info && info.idServer) {
      return state.audioTipMsg.find(item => {return item.custom && item.custom.msgServerId == info.idServer});
    } else {
      return state.audioTipMsg;
    }
  },
  // 获取语音播放对象
  getAudioObj: (state) => {
    return state.audioObj;
  },
  // 获取锁定乐聊状态
  getLockIm: (state) => {
    return state.lockIm;
  },
  // 获取编辑器高度
  getEditorHeightMap: (state) => {
    return state.editorHeightMap;
  },
  // 获取编辑器内容
  getEditorContentMap: (state) => {
    return state.editorContentMap;
  },
  // 获取编辑器消息内容
  getEditorContentMsg: (state) => (info) => {
    let contents = state.editorContentMap[info.id];
    let msg = [];
    if (contents && contents.ops && contents.ops.length > 0) {
      msg = getInputMessage(contents.ops).messages;
      if (info.del) {
        delete state.editorContentMap[info.id];
        state.imEditor.clear(info.id);
      }
    }
    return msg;
  },
  // 获取群未读通知
  getNoticeUnread: (state) => {
    return state.noticeUnread;
  },
  // 获取群通知数据
  getNoticeMap: (state) => {
    return state.noticeMap;
  },
  // 获取通知消息内容
  getNotification: function (state) {
    return function (info) {
      let item = deepClone(info);
      let userInfo = state.userInfo;
      let text = "";
      try {
        let type = item.attach.type;
        if (type) {
          type = type.replace("SuperTeam", "Team");
        }
        let teamName = "群";
        // 通知群类型
        let sessionType = getSessionType(state.sessions[item.sessionId]);
        if (sessionType.type == 1) {
          teamName = "讨论组";
        }
        // 通知的用户信息
        let usersMap = {};
        if (item.attach.users) {
          item.attach.users.map(item1 => {
            try {
              if (typeof item1.sign == "string") {
                item1.sign = JSON.parse(item1.sign)
              }
            } catch (e) {
            }
            if (!item1.sign) {
              item1.sign = {};
            }
            // 本地存在当前人信息从本地获取
            let thisPersonInfo = getPersons(state, item1.account);
            if (thisPersonInfo.deptName) {
              item1.sign = thisPersonInfo;
            }
            usersMap[item1.account] = item1;
          });
        }
        // 通知的成员
        let members = [];
        if (item.attach.account) {
          item.attach.accounts = [item.attach.account];
        }
        if (item.attach.accounts) {
          for (let i = 0; i < item.attach.accounts.length; i++) {
            let item1 = item.attach.accounts[i];
            if (item1 == userInfo.workerNo) {
              members.push("您");
              continue;
            } else if (item1 == config[config.env].robotEmpNo) {
              // 不显示小乐进出群
              continue;
            }
            let thisName = `${usersMap[item1].nick}${usersMap[item1].sign.deptName ? "(" + usersMap[item1].sign.deptName + ")" : ""}`
            // 获取本地数据
            let thisPerson = getPersons(state, item1);
            if (!usersMap[item1].nick) {
              thisName = thisPerson.name;
            }
            members.push(thisName);
          }
          members = members.join(",");
        }
        // 操作用户名
        let thisUser = "您";
        if (item.from != userInfo.workerNo) {
          thisUser = `${usersMap[item.from].nick}${usersMap[item.from].sign.deptName ? "(" + usersMap[item.from].sign.deptName + ")" : ""}`;
          // 获取本地数据
          let thisPerson = getPersons(state, item.from);
          if (!usersMap[item.from].nick) {
            thisUser = thisPerson.name;
          }
        }
        // 群自定义消息
        let custom = item?.attach?.custom || "";
        if (custom) {
          try {
            if (typeof custom == "string") {
              custom = JSON.parse(custom);
            }
          } catch (e) {
          }
        }
        // 匹配通知类型
        switch (type) {
          case "addTeamMembers":
            text = `${thisUser}邀请${members}加入${teamName}`;
            if (custom) {
              if (custom.type == "active") {
                text = `${members}通过主动入群加入${teamName}`;
              }
            }
            break;
          case "removeTeamMembers":
            if (sessionType.type == 3) {
              text = members + "已退群";
            } else {
              if (custom) {
                if (custom.type == "move") {
                  text = `${members}因异动不满足入群条件,退出${teamName}(本消息普通成员不可看)`;
                } else if (custom.type == "dimission") {
                  text = `${members}因离职退出${teamName}(本消息普通成员不可看)`;
                } else if (custom.type == "initiative") {
                  text = `${members}自主申请退出该${teamName}(本消息普通成员不可看)`;
                } else {
                  text = `${members}因不满足条件,退出${teamName}(本消息普通成员不可看)`;
                }
              } else {
                text = `${thisUser}已将${members}移出${teamName}(需再次邀请方可入群)`;
              }
            }
            break;
          case "leaveTeam":
            text = `${thisUser}已退出${teamName}`;
            break;
          case "updateTeam":
            if (item.attach.team.joinMode) {
              switch (item.attach.team.joinMode) {
                case "noVerify":
                  text = `${teamName}身份验证模式更新为允许任何人加入`;
                  break;
                case "needVerify":
                  text = `${teamName}身份验证模式更新为需要验证消息`;
                  break;
                case "rejectAll":
                  text = `${teamName}身份验证模式更新为不允许任何人申请加入`;
                  break;
                default:
                  // text = `更新${teamName}消息`;
                  break;
              }
            } else if (item.attach.team.name) {
              text = `${thisUser}更新${teamName}名称为${item.attach.team.name}`;
            } else if (item.attach.team.intro) {
              text = `${thisUser}更新${teamName}介绍为${item.attach.team.intro}`;
            } else if (item.attach.team.inviteMode) {
              text = item.attach.team.inviteMode === "manager" ? "邀请他人权限为管理员" : "邀请他人权限为所有人";
            } else if (item.attach.team.updateTeamMode) {
              text = teamName + item.attach.team.updateTeamMode === "manager" ? "资料修改权限为管理员" : "资料修改权限为所有人";
            } else if (item.attach.team.avatar) {
              text = `更改了${teamName}头像`;
            } else if (item.attach.team.announcement) {
              text = `${teamName}公告`;
            } else if (item.attach.team.mute != null) {
              if (item.attach.team.mute) {
                text = `${thisUser}已开启全员禁言 ，当前仅群主及管理员可发言`;
              } else {
                text = `${thisUser}关闭了全体禁言`;
              }
            } else if (item.attach.team.serverCustom) {
              let serverCustom = item.attach.team.serverCustom;
              try {
                serverCustom = JSON.parse(serverCustom);
              } catch (e) {}
              text = getPersons(state, serverCustom.operAccid).name || "";
              switch (String(serverCustom.operType)) {
                case "-1":
                  text = "";
                  break;
                case "1":
                  text += `更新${teamName}信息为：${serverCustom.typeName || ""}  ${serverCustom.typeStatus || ""}`;
                  break;
                case "2":
                  text += `更新${teamName}名称为${serverCustom.alias}`;
                  break;
                default:
                  text += `更新${teamName}信息`;
                  break;
              }
            }
            break;
          case "acceptTeamInvite":
            if (item.from === item.attach.account) {
              text = `${thisUser}加入了${teamName}`;
            } else {
              text = `${thisUser}接受了${members}的入${teamName}邀请`;
            }
            break;
          case "passTeamApply":
            if (item.from === item.attach.account) {
              if (item.from == userInfo.workerNo) {
                text = `${thisUser}加入了${teamName}`;
              } else {
                text = `${thisUser}通过扫码加入${teamName}`;
              }
            } else {
              text = `${thisUser}通过了${members}的入${teamName}申请`;
            }
            break;
          case "dismissTeam":
            text = `${thisUser}解散了${teamName}`;
            break;
          case "updateTeamMute":
          case "updateTeamMembersMute":
            let account = item.attach.account;
            if (item.attach.accounts && item.attach.accounts[0]) {
              account = item.attach.accounts[0];
            }
            let thisName = "您";
            if (account != userInfo.workerNo) {
              thisName = `${usersMap[account].nick}(${usersMap[account].sign.deptName})`;
              // 获取本地数据
              let thisPerson = getPersons(state, account);
              if (!usersMap[account].nick) {
                thisName = thisPerson.name;
              }
            }
            let otherName = "您";
            if (item.from != userInfo.workerNo) {
              otherName = `管理员`;
            }
            text = `${thisName}被${otherName}${item.attach.mute ? "" : "解除"}禁言`;
            break;
          case "addTeamManagers":
            text = `${members}成为了管理员`;
            break;
          case "removeTeamManagers":
            text = `${members}被解除管理员`;
            break;
          case "transferTeam":
            let transferName = "您";
            if (item.attach.account != userInfo.workerNo) {
              transferName = `${usersMap[item.attach.account].nick}(${usersMap[item.attach.account].sign.deptName})`;
            }
            text = `${transferName}成为了群主`;
            break;
          default:
            text = "通知消息";
            break;
        }
      } catch (e) {
        console.log("getNotificationErr", e)
      }
      return text;
    }
  },
  // 替换敏感词为*
  getKeyText: (state) => (text = "") => {
    if (state.userInfo.workerNo == "000001") {
      return text;
    }
    try {
      if (text && state.keyText) {
        text = text.replace(new RegExp(state.keyText, "g"), function (text) {
          console.log("keyText", text);
          return '*';
        });
      }
    } catch (e) {
      console.log("keyTextMsgErr", e);
    }
    return text;
  },
  // 房产网客户敏感词对象
  getFcwKeyMap: (state) => (info) => {
    let fcwKeyMap = {
      textMap: {},// 敏感词匹配词
      keyMap: {},// 敏感词匹配规则
    };
    try {
      if (info.scene == "p2p" && new RegExp(config.fcw).test(info.to) && info.text) {
        state.fcwKeyTextList.map(item => {
          // 获取敏感词匹配数据
          info.text.replace(new RegExp(item.keyword, "g"), function (text) {
            fcwKeyMap.textMap[text] = true;
            fcwKeyMap.keyMap[item.ruleName] = true;
            return text;
          });
        });
      }
    } catch (e) {
      console.log("getFcwKeyMapErr", e);
    }
    return fcwKeyMap;
  },
  // 房产网客户敏感词返回错误提示
  getFcwKeyText: (state) => (info) => {
    let text = "";
    let textList = Object.keys(info.textMap || {});
    let keyList = Object.keys(info.keyMap || {});
    if (textList.length > 0) {
      text = `消息中包含"${textList.toString()}"，涉嫌"${keyList.toString()}"，不展示给客户，请调整措辞后重发`;
    }
    return text;
  },
  // 房产网客户敏感词修改发送消息体
  getFcwKeyMsg: (state) => (msg, allFcwKeyMap) => {
    try {
      // 房产网敏感词命中词
      let fcwKeyMap = allFcwKeyMap || {};
      // 房产网客户消息判断是否命中敏感词
      if (!allFcwKeyMap && new RegExp(config.fcw).test(msg.to)) {
        fcwKeyMap = remote.store.getters.getFcwKeyMap({scene: msg.scene, to: msg.to, text: msg.text});
      }
      if (new RegExp(config.fcw).test(msg.to)) {
        let fcwKeyText = remote.store.getters.getFcwKeyText(fcwKeyMap);
        if (fcwKeyText) {
          try {
            msg.custom = JSON.parse(msg.custom || "{}");
          } catch (e) {}
          try {
            msg.custom.hideMessage = 1;
            msg.custom = JSON.stringify(msg.custom);
          } catch (e) {}
        }
      }
    } catch (e) {
      console.log("getFcwKeyMsgErr", e);
    }
  },
  // 获取图片加载和线路切换定时器
  getImageLoadTimer: (state) => {
    return state.imageLoadTimer;
  },
  // 获取子窗口
  getChildWin: (state) => {
    return state.childWin;
  },
  // 获取跳转链接
  getLinkUrlJson: (state) => (info) => {
    let url = "";
    let frameName = "";
    let param = "";
    switch (String(info.type)) {
      case "5":
      case "6":
      case "7":
      case "imJZSell":
      case "imNewHouseJZSell":
      case "jzxs-approval-notice":
      case "imSomeHouseRecommend":
      case "imHouseBatchShare":
        // 兼容集中销售
        let houseId = info.fhId || info.content.other.houseId || info.content.other.fhId || info.content.other.projectId;
        let isXf = (info.type == 7 && info.content.other.houseType == 1) || ((info.type == 5 || info.type == 6) && info.content.other.houseType == 4) || info.type == "imNewHouseJZSell";
        let isXq = (info.type == 5 || info.type == 6) && info.content.other.houseType == 3;
        let isXx = (info.type == 7 && info.content.other.houseType == 5);
        let urlParam = `empNo=${state.userInfo.workerNo}&shareNo=${info.shareNo}&way=le&cardType=${info.type}`;
        // 房源类型url
        if (isXf) {
          // 新房
          url = `${config[config.env].jjsHome}/jjsxm/proxy/detail?projectId=${houseId}&${urlParam}`;
        } else if (isXq) {
          // 小区
          url = `${config[config.env].jjsHome}/community/communityDic/communityDic-detail?comId=${houseId}&${urlParam}`;
        } else if (isXx) {
          // 学校
          url = `${config[config.env].jjsHome}/school/schoolInfo/school-detail?schoolId=${houseId}&${urlParam}`;
        } else if (info.content.sourceType == "minApp-ycya") {
          url = info.content.sourceUrl;
        } else {
          // 其他场景跳租房/二手房
          url = `${config[config.env].jjsHome}/hsl/house/house-detail?fhId=${houseId}&${urlParam}`;
        }
        // 跳转大数据需要的字段
        if (info.type == 7) {
          url += `&source=lbg`;
        } else if (info.type == "imJZSell") {
          url += `&source=jzxs&sellId=${info.content.other.sellId}`;
        }
        frameName = "login";
        break;
      case "8":
      case "9":
        // 8卡片类型、9合并转发url
        frameName = "jjsHome";
        url = info.content.url;
        // 合并转发改为原生待测试 TODO
        // param = {type: "window", newWin: 1, width: 680, height: 680, path: "child/mergeForward?" + info.content.url.split("?")[1]};
        break;
      case "collect":
        // 收藏url
        frameName = "jjsHome";
        url = `${config[config.env].jjsHome}/im/collec/detail?id=${info.content.id}`;
        break;
      case "geo":
        // 地理位置
        url = `https://api.map.baidu.com/marker?location=${info.geo.lat},${info.geo.lng}&title=我在这里&content=${info.geo.title}&output=html&zoom=16&src=im.leyoujia.com`;
        break;
      case "doc":
        // 乐文档
        frameName = "login";
        if (info.doc.property == 2) {
          url = `${config[config.env].jjsHome}/lyj-front/sheet/index.html?id=${info.doc.padId}&appName=pc-im${info.doc.docId ? "&docId=" + info.doc.docId : ""}${info.doc.clearId ? "&clearcall=" + info.doc.clearId : ""}${info.doc.location ? "&location=" + info.doc.location : ""}`
        } else {
          url = `${config[config.env].jjsHome}/lyjEtherpad/p/${info.doc.padId}?appName=pc-im${info.doc.docId ? "&docId=" + info.doc.docId : ""}${info.doc.clearId ? "&clearcall=" + info.doc.clearId : ""}${info.doc.location ? "&location=" + info.doc.location : ""}`
        }
        break;
      case "minApp_YLYK_Detail":
        // 有料有客文章卡片
        frameName = "login";
        url = `${config[config.env].fcwUrl}/zx/detail/${info.content.other.minAppPage.replace(/[^\d]/g, "")}.html`;
        break;
    }
    // 外部浏览器打开
    if (info.content && info.content.isOuterOpen) {
      frameName = "login";
    }
    return {frameName: frameName, url: url, param: param};
  },
  // 获取反垃圾列表
  getAntispamMap: (state) => {
    return state.antispamMap;
  },
  // 获取本地搜索数据
  getLocalSearch: (state) => {
    return state.localSearch;
  },
  // 获取是否折叠置顶状态
  getIsFoldTop: (state) => {
    return state.isFoldTop;
  },
  // 获取会话列表tab
  getSessionTab: (state) => {
    return state.sessionTab;
  },
  // 获取列表分组列表对象
  getSessionTabClassifyItem: (state) => {
    return state.sessionTabClassifyItem;
  },
  // 获取列表讨论组分组是否展开
  getSessionTabClassifyShow: (state) => {
    return state.sessionTabClassifyShow;
  },
  // 获取可折叠会话数量
  getTopObj: (state) => {
    return state.topObj;
  },
  // 获取最后一个折叠置顶会话
  getLastSessionTopId: (state) => {
    return state.lastSessionTopId;
  },
  // 是否触发click事件
  getIsClick: (state) => {
    return state.isClick;
  },
  // 获取左侧、顶部tab数据
  getTabMap: (state) => {
    return state.tabMap;
  },
  // 获取收藏话术权限
  getCollectStoryPur: (state) => {
    return state.collectStoryPur;
  },
  // 获取收藏/话术列表
  getCollectMap: (state) => {
    return state.collectMap;
  },
  // 获取是否需要设置代理线路
  getJJsProxy: (state) => {
    return state.jjsProxy;
  },
  // 获取云信数据
  getNimInfo: (state) => {
    return state.nimInfo;
  },
  // 返回自己在群信息
  getUserTeamInfo: (state) => {
    return state.userTeamInfos;
  },
  // 获取讨论组搜索下拉数据
  getGroupSearchInfo: function (state, info) {
    return state.groupSearchInfo;
  },
  // 获取可创建讨论组列表
  getGroupTypeList: function (state, info) {
    return state.groupTypeList;
  },
  // 获取sse消息列表
  getSseMsgList: function (state) {
    return function (info) {
      // id-当前会话id，msgList-当前会话渲染的消息列表
      for (let i = 0; i < info.msgList.length; i++) {
        let msgItem = info.msgList[i];
        // 删除sseMsg被删除的消息
        if (msgItem.isSseMsg && !state.sseMsgMap[msgItem.idServer]) {
          info.msgList.splice(i, 1);
          i--;
        }
      }
      for (let key in state.sseMsgMap) {
        let item = deepClone(state.sseMsgMap[key]);
        if (item) {
          if (info.id == item.sessionId) {
            let msgIndex = info.msgList.findIndex(msgItem => {return msgItem.idServer == item.idServer});
            // 不存在该消息则插入排序
            if (msgIndex == -1) {
              info.msgList.push(item);
              info.msgList.sort((a, b) => {return (a.time || 0) - (b.time || 0)});
            } else {
              // 替换文本内容
              info.msgList[msgIndex] = deepClone(item);
            }
          }
        }
      }
    }
  },
  // 获取对应权限
  getPurMap: (state) => (info) => {
    let pur = state.purMap[info.key];
    switch (info.key) {
      case "forward":
        // 林董和直属默认有权限
        if (state.userInfo.workerId == "88888888" || state.userInfo.managerId == "88888888") {
          pur = true;
        }
        break;
    }
    return pur;
  },
  // 获取同事、客户、讨论组/群分组列表
  getClassifyList: (state) => (info) => {
    // 获取服务器分组配置
    let classifySettingList = deepClone(state.groupSettings || []);
    if (info?.uuid) {
      // 返回指定uuid分组
      let classifyItem = classifySettingList.find(item => {return item.uuid == info.uuid}) || {};
      if (info.valueMap) {
        // 返回分组值map
        let itemList = (classifyItem.value || "").split(",");
        let itemMap = {};
        for (let i = 0; i < itemList.length; i++) {
          let itemI = itemList[i];
          if (itemI) {
            itemMap[itemI] = itemI;
          }
        }
        return itemMap;
      } else {
        // 返回分组信息
        return classifyItem;
      }
    } else {
      // 返回对应分组列表
      let classifyList = [];
      for (let i = 0; i < classifySettingList.length; i++) {
        let item = classifySettingList[i];
        switch (Number(info.type)) {
          case 2:
            // 讨论组和群合并
            if (item.type == 1 || item.type == 2) {
              classifyList.push(item);
            }
            break;
          case 3:
            // 同事分组，去除直接下属
            if (item.type == 3 && item.uuid != -1) {
              classifyList.push(item);
            }
            break;
          default:
            if (item.type == info.type) {
              classifyList.push(item);
            }
            break;
        }
      }
      return classifyList;
    }
  },
}