import {initUserDB, initCacheDB} from "@utils/sdk/dbSDK";
import {getUserInfo} from "@utils";

let packageInfo = require("/package.json");
let config = require("/config.js");

export default {
  // 日志
  logger: {},
  // 配置信息
  config: {
    width: 980,
    height: 680,
    name: packageInfo.name,
    env: packageInfo.env,
    version: config.version,
    serverVersion: localStorage.getItem("serverVersion") || config.version,
    config: config,
  },
  // 窗口大小信息
  windowSizeType: {
    tabType: 0,// 0精简模式1分组模式
    width: 980,
    height: 680,
    classifyWidth: 150,// 分组宽度
    listWidth: 239,// 会话列表宽度
  },
  // 电脑信息
  computerInfo: {},
  // 电脑基础信息
  baseComputerInfo: {},
  // 电脑网络连接信息
  netComputerInfo: {},
  // 电脑信息map
  computerMap: {},
  // 登录用户信息
  userInfo: getUserInfo(),
  // 未登录选中的用户信息
  tempUserInfo: {},
  // 云信对象
  nimSDK: {},
  // 人员状态定时器
  empStatusTimer: "",
  // 所有会话
  sessions: {},
  // 临时会话
  sessionsTemp: {},
  // 当前会话信息
  currentSession: {},
  // 最后一个置顶的会话
  lastSessionTopId: "",
  // 可折叠置顶数量
  topObj: {
    foldTopNum: 0,
    topNum: 0,
  },
  // 更新会话时间
  updateSessionsTime: 0,
  // 群
  teams: {},
  // 群加载标识
  teamsInit: 0,
  // 群成员
  teamMembers: {},
  // 当前用户在群信息
  userTeamInfos: {},
  // 服务器的全部服务号和订阅号
  allSubAndSer: {},
  // 人员
  persons: {},
  // 消息
  msgs: {},
  // 消息加载状态
  msgsLoading: {},
  // @自己消息
  haitMsgMap: {},
  // 特别关心消息
  concernMsgMap: {},
  // 云信好友列表
  nimFriend: {},
  // 待添加云信好友列表
  addNimFriendMap: {},
  // 添加好友请求状态
  addNimFriendFlag: false,
  // 服务端配置信息
  settings: {},
  // 分组配置信息,如群/讨论组/标签分组
  groupSettings: [],
  // 找对人办对事数据
  findPersons: {},
  // 通讯
  emit: {},
  // 用户数据库
  userDB: initUserDB(),
  // 用户数据请求状态对象
  userReqMap: {},
  // 本地文件记录数据集
  fileDB: {},
  // 缓存数据库
  cacheDB: initCacheDB(),
  // 文本编辑器
  imEditor: {},
  // 路由
  router: {},
  // 引用消息内容
  quoteMsg: {},
  // 图片缓存定时器
  imgQueueTimer: "",
  // 图片缓存队列
  imgQueue: {},
  // 图片缓存下载中队列
  imgLoadQueue: {},
  // 云信上传文件进程
  nimFileUpload: {},
  // 服务器和本地时间差
  diffTime: 0,
  // 本地设置未读会话列表
  localUnreadMap: {},
  // 黑名单
  blackMap: {},
  // 特别关心
  concernMap: {},
  // 重要标记
  importantMarkMap: {},
  // 依赖服务器的配置信息-配置信息、特别关心-如果请求失败切换会话列表的时候请求
  serverFlagMap: {},
  // 输入框@状态
  haitInfo: {},
  // 好友数据-用于通讯录
  friends: [],
  // 用于通讯录的数据结构
  mailList: [],
  // 聚焦元素
  focusMsg: "",
  // 经纪人合作信息
  cooperateInfo: {},
  // 认识档案权限
  hrAuthority: {},
  // 常用表情
  oftenEmoji: "",
  // 窗口抖动定时器
  shakeInterval: "",
  // 窗口抖动时间
  shakeTimeMap: {},
  // 群消息通知
  teamLocalMsgs: [],
  // 强提醒消息列表
  remindMsg: [],
  // 霸屏消息
  importantMsg: [],
  // 日程弹窗
  scheduleModal: "",
  // 语音已读提示，用于漫游判断语音是否已读
  audioTipMsg: [],
  // 语音播放对象
  audioObj: {},
  // 锁定乐聊
  lockIm: false,
  // 编辑器高度
  editorHeightMap: {},
  // 编辑器内容
  editorContentMap: {},
  // 含有群未读通知的群id
  noticeUnread: [],
  // 群通知最新数据
  noticeMap: {},
  // 撤回重新编辑消息记录
  deleteMsgMap: {},
  // 引用消息被撤回记录
  quoteDeleteMap: {},
  // 语音转文字
  audioTextMap: {},
  // 敏感词库
  keyText: "",
  // 房产网敏感词库
  fcwKeyTextList: [],
  // 讨论组敏感词库
  groupKeyTextList: [],
  // 图片加载和线路切换定时器
  imageLoadTimer: {},
  // 最近联系人
  recentContactsMap: {},
  // 消息宽度对象
  msgWidthObj: {
    msg: 475,// 默认消息宽度
    height: 430,// 默认消息高度
    centerImage: 0,// 消息平台图片
    newsImage: 456,// 咨询图片
  },
  // 子窗口
  childWin: {},
  // 截图对象
  jtObj: {
    isJt: false, // 是否打开截图
    timer: "", // 截图定时器
    uid: "",// 打开截图的时间
    initLog: false,// 初始化埋点
    initKey: "",// 初始化埋点的快捷键
    initIsRegistered: "",// 初始化埋点是否冲突
  },
  remindLaterTimer: "",// 稍后提醒定时器
  // 子窗口输入框内容
  childEditorHtml: "",
  // 通知消息数据
  notifyMap: {},
  // 通知排序
  notifyMapCount: 0,
  // 反垃圾列表
  antispamMap: {},
  // 本地搜索数据
  localSearch: [],
  // 是否折叠置顶
  isFoldTop: false,
  // 列表tab
  sessionTab: "all",
  // 列表分组列表对象
  sessionTabClassifyItem: {},
  // 取列表讨论组分组是否展开
  sessionTabClassifyShow: {
    "groupClassify": false,// 讨论组列表默认展开
    "colleagueClassify": false,
    "customerClassify": false,
  },
  // 发送消息是否滚动聊天会话
  scrollSessionState: true,
  // 当前显示的会话列表
  currSessionList: [],
  // 是否触发点击事件
  isClick: true,
  // 全局1s定时器
  globalTimer: "",
  // 左侧和顶部tab列表
  tabMap: {
    leftList: [],
    topList: []
  },
  // 被替换的文件地址MD5数据
  removeMD5Obj: {},
  // 云信文件转移时间
  yxFileTime: 0,
  // 闪烁时间
  flashTime: 0,
  // 更新会话id
  updateSessionId: "",
  // 更新群成员数据
  updateMemberTeamInfo: {
    id: "",
    time: 0
  },
  // 收藏话术权限
  collectStoryPur: false,
  // 收藏和话术列表
  collectMap: {
    time: 0,
    storyTime: 0,
    list: [{id: 0, name: "默认分组"}],
    storyList: [{gid: 0, gname: "默认分组"}],
  },
  // 用户当前ip信息
  ipInfo: {
    //是否国内用户
    isDomestic: true,
  },
  // 当前代理线路对象
  jjsProxy: {
    // 是否第一次主线路异常
    firstNet: 1,
    // 是否启动代理功能
    open: true,
    // 获取延迟的接口地址
    api: config[config.env].jjsImApi,
    // 允许代理域名
    host: "",
    // 当前代理线路
    proxyCurrent: {},
    // ping网络状况列表
    pingList: [{name: "百度", key: "baidu", url: "baidu.com", delay: ""}, {name: "qq", key: "qq", url: "qq.com", delay: ""}],
    // 代理线路列表
    proxyList: [],
    // 国外代理线路信息
    proxyAbroad: {},
    // 临时代理线路信息
    proxyTemp: [],
    // 是否国内用户主线路和线路异常，切换到国外线路
    switchAbroad: false,
    // 是否走了临时线路
    switchTemp: false,
    // 最大延迟
    maxDelay: 500,
    // 超时延迟
    timeoutDelay: 3000,
    // 默认线路3次失败再切换别的线路
    defaultCountNum: 3,
    // 全部线路异常6次再上报微信
    errorCountNum: 6,
    // 定时获取判断的延迟信息
    pingInfo: {baidu: "", qq: "", default: "", home: "", abroad: ""},
    // 是否获取ping数据完成
    pingDone: true,
    // 主线路异常次数
    defaultCount: 0,
    // ping异常次数
    pingCount: 0,
    // 全部线路异常次数
    errorCount: 0,
    // 是否在线
    onLine: true,
    // 发送预警时间
    sendMsgTime: 0,
    // 备灾开关
    loginSwitch: false,
    // 人事备灾开关
    rsSwitch: false,
    // vpn权限
    vpnInfo: {
      isShowVpn: false,// 是否有VPN权限
      vpnFlag: false,// 是否开启了VPN
      vpnObj: {},// vpn开启关闭内容对象
    }
  },
  // 云信加载数据
  nimInfo: {
    type: "",// 云信状态err、info
    info: ["消息收取中…", "网络异常正在尝试重连，请稍等"],
    infoType: 0,// 加载状态
    err: "消息收取失败，",
    tips: "0%",// 额外提示内容，如进度
    progress: 0,// 加载进度
    defaultProgress: 0,// 默认进度
    time: "",// 初始化云信时间
  },
  // 讨论组搜索下拉数据
  groupSearchInfo: {},
  // 可创建讨论组列表
  groupTypeList: [{label: "普通", value: "0"}],
  groupTypeListShow: [],
  groupTypeMap: {},// 业务讨论组分组列表对象
  groupTypeShowMap: {},// 业务讨论组分组列表显示对象
  // 客户服务端信息
  fcwServerInfo: {},
  // 开启sse对象列表
  sseMap: {},// 流对象
  sseMsgMap: {},// 消息渲染对象
  sseTempMsgMap: {},// 防抖对象
  sseIdentMap: {},// 创建流标识对象
  sseDisableSendMap: {},// sse禁止发送消息会话对象
  // 权限列表
  purMap: {
    forward: false,// 转发权限，有权限同事50人客户50人，没权限同事10人客户50人
    forwardTime: 0,// 获取转发权限时间
    reportMsg: false,// 转发特定人权限
    reportMsgTime: 0,// 获取转发特定人权限时间
    reportMsgGroup: [],// 转发特定人群
    reportMsgGroupTime: 0,// 获取转发特定人群时间
    clearMessage: false,// 清除消息记录功能权限
  },
  // 个人助理账号信息
  aiObj: {
    workerNo: "",// 智能助理云信账号
    name: "",// 智能助理特性化名字
    briefIntr: "",// 介绍
    prologue: "",// 开场白
    headImg: "", // 头像
    intrUrl: "https://i.leyoujia.com/lyjEtherpad/p/ee7afef5-69bc-4d2b-b22a-de8f13a33b6b?docId=21581311",// 介绍说明地址
    consumeUrl: "https://i.leyoujia.com/lyjEtherpad/p/0e73017e-fbc4-4e0a-9730-433eaf7dbe02?docId=********",// 消费说明地址
    protocolUrl: "https://i.leyoujia.com/lyjEtherpad/p/2a7928bf-2570-41d2-ba48-41bf5adec880?docId=********",// ai协议地址
    appUrl: config[config.env].jjsHome + "/lyj-menu/syssetting/SYS_AI?hideSideBar=true#/application/index",// 应用首页
    appStoreUrl: config[config.env].jjsHome + "/lyj-menu/syssetting/SYS_AI?hideSideBar=true#/application/market",// 应用商城
    userUrl: config[config.env].jjsHome + "/lyj-menu/syssetting/SYS_AI?hideSideBar=true#/account/index",// 自定义头像和昵称
    knowledgeUrl: config[config.env].jjsHome + "/lyj-menu/syssetting/SYS_AI?hideSideBar=true#/knowledge/index",// 个人知识库
    moreApp: config[config.env].difyUrl + "/apps",// 更多应用
    teamAiUrl: "https://i.leyoujia.com/lyjEtherpad/p/9f0ee6e0-5e49-464b-9dbc-7802abc8b362?docId=********",// 群ai说明
    gpt4: false,// 是否开启gtp4
    gpt4Tips: false,// 是否显示gpt4提示
    currentApp: {},// 当前选中的对象
    defaultCurrentApp: {},// 默认的应用对象
    commonAppList: [],// 默认三大类应用
    showAppList: [],// 显示的应用列表
    defaultAppList: [],// 默认显示的应用列表
    refresh: false,// 新话题请求接口状态
    initCard: true,// 是否第一次初始化主界面卡片
  },
  // fcw在线状态
  fcwOnlineMap: {},
  // 远程cmd指令列表
  remoteCMDList: [
    {"type": "port", "port": "3389"},
    {"type": "port", "port": "30193"},
    {"type": "task", "taskName": "SunloginClient.exe", "count": 3},
    {"type": "task", "taskName": "ToDesk.exe", "count": 2},
    {"type": "task", "taskName": "ToDesk_Session.exe", "count": 0}
  ],
  // 发送文件标识对象
  fileMsgFlagMap: {},
  // 群ai信息
  teamAiMap: {},
  errorMap: {
    "pc-im-0": "请打开乐聊后重试",
    "pc-im-1": "请登录乐聊后重试",
    "pc-im-2": "请升级乐聊后重试",
    "pc-im-3": "系统错误",
    "pc-im-4": "代码异常",
  },
  // 调用接口时间
  apiTimeMap: {
    "getLoginSecretApi": 0,
  },
  // ai临时appId,key为会话id，value为{id:应用id,flag:是否进入初始化过,temp为临时标识}
  aiTempIdMap: {},
  // 直接下属列表
  subordinateMap: {},
  // 电脑类型
  deviceObj: {},
  // 全部应用列表
  allAppList: [],
  // 服务端分组列表
  classifyList: [],
  // 是否显示同事分组
  collDutyType: false,
  // 验证码提取消息记录对象
  codeRecordMsgObj: {
    code: "",// 验证码
    list: "",// 消息列表
    hasMore: false,// 是否还有更多消息
  },
}