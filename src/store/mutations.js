import config from "/config.js";

let fs = remote.require("fs");
import {
  getAvatar, getSessionType, setUserBaseInfo, encrypt, loadCache, deepClone, emitMsg, setFcwUser, debounce, getAppPath, isSubOrSer, getFcwInfo,
  getChildWin, isMainWin, Events, setJJSEvent, getExeList, enCodeJJS, isFcwList, userLocalStorage, MD5, removeMsgByTime, setLocalStorage,
  userCache, calcStorageSize,
} from "@utils";
import {initFileDB, removeDB} from "@utils/sdk/dbSDK";
import {initJt} from "@utils/sdk/jtSDK";
import {
  getUserBatchApi, addNimFriendApi, getEmpStatusApi, getUATPasswordApi, getWxMsgTokenApi, sendWxMsgApi, getAppCardByAccIdsApi, newTopics<PERSON><PERSON>, messageTo<PERSON>ode<PERSON><PERSON>, mergeMessageToCode<PERSON>pi,
} from "@utils/net/api"
import {alert, loading, toast} from "@comp/ui";
import {initLogger} from "@utils/logger";
import UUID from '@utils/uuid.js';

/**
 *
 * */
export default {
  // 设置状态
  setState: function (state, info) {
    state[info.key] = info.value;
    // 子窗口
    if (info.child) {
      let childWin = getChildWin(info.child);
      if (childWin && isMainWin()) {
        childWin.window.store.commit("setState", {child: true, key: info.key, value: info.value});
      }
    }
  },
  // 初始化日志
  setLogger: function (state, info) {
    if (!state.logger.writeLogFile) {
      state.logger = initLogger();
    }
  },
  // 设置配置信息
  setConfig: function (state, info) {
    state.config[info.key] = info.value;
  },
  // 设置版本号
  setConfigVersion: function (state, info) {
    let version = state.config.version;
    let serverVersion = state.config.serverVersion;
    // 存在更新
    if (info && info.serverVersion) {
      serverVersion = info.serverVersion;
      setLocalStorage("serverVersion", serverVersion);
    } else if (localStorage.getItem("serverVersion")) {
      // 本地缓存
      serverVersion = localStorage.getItem("serverVersion");
    }
    // 更新完成修改本地版本号
    if (info && info.version) {
      version = info.version;
      // 修改版本号
      let pkgPath = getAppPath(`\\package.json`);
      let pkg = JSON.parse(fs.readFileSync(pkgPath));
      pkg.currentVersion = version;
      fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2));
    }
    // 设置版本号信息
    state.config.version = version;
    state.config.serverVersion = serverVersion;

    // 安全地设置 remote store 中的 serverVersion
    try {
      if (window.remote && window.remote.store && window.remote.store.state && window.remote.store.state.config) {
        window.remote.store.state.config.serverVersion = serverVersion;
      } else if (remote && remote.store && remote.store.state && remote.store.state.config) {
        remote.store.state.config.serverVersion = serverVersion;
      }
    } catch (error) {
      console.warn('设置 remote store serverVersion 失败:', error);
    }
  },
  // 初始化本地缓存数据
  setInitLocalStorage: function (state, info) {
    let that = this;
    // 未读缓存
    state.localUnreadMap = userLocalStorage({key: "localUnreadMap", value: {}}, 2);
    // 特别关心
    state.concernMap = userLocalStorage({key: "concernMap", value: {}}, 2);
    // 重要标记
    state.importantMarkMap = userLocalStorage({key: "importantMarkMap", value: {}}, 2);
    // 引用被撤回记录
    state.quoteDeleteMap = userLocalStorage({key: "quoteDeleteMap", value: {}}, 2);
    // 最近联系人
    state.recentContactsMap = userLocalStorage({key: "recentContactsMap", value: {}}, 2);
    // 搜索记录
    let localSearch = userLocalStorage({key: "localSearchMap", value: []}, 2);
    localSearch.map(item => {
      setUserBaseInfo(item);
    });
    state.localSearch = localSearch;
    // @消息
    userCache({key: "haitMsgMap"}, 2).then(res => {
      state.haitMsgMap = res;
    });
    // state.haitMsgMap = userLocalStorage({key: "haitMsgMap"}, 2);
    // 特别关心
    userCache({key: "concernMsgMap"}, 2).then(res => {
      state.concernMsgMap = res;
    });
    // state.concernMsgMap = userLocalStorage({key: "concernMsgMap"}, 2);
    // 发送快捷键
    if (!state.settings[config.settings.type4]) {
      state.settings[config.settings.type4] = userLocalStorage({key: config.settings.type4, value: "2"}, 2);
    }
    // 截图快捷键
    if (!state.settings[config.settings.type6]) {
      state.settings[config.settings.type6] = userLocalStorage({key: config.settings.type6, value: config.shortcut.jt1}, 2);
      that.commit("setShortcut", {key: "jt", value: state.settings[config.settings.type6]});
    }
    // 气泡通知
    if (state.settings[config.settings.type7] != 2) {
      that.commit("setNotifyWin", "add");
    }
    // 乐聊风格
    if (!state.settings[config.settings.type11]) {
      state.settings[config.settings.type11] = userLocalStorage({key: config.settings.type11, value: "0"}, 2);
    }
    // 窗口大小和分组模式
    if (!state.settings[config.settings.type12]) {
      state.settings[config.settings.type12] = userLocalStorage({key: config.settings.type12, value: {}}, 2);
      for (let key in state.settings[config.settings.type12]) {
        state.windowSizeType[key] = state.settings[config.settings.type12][key];
      }
    }
    // 分组模式排序
    if (!state.settings[config.settings.type13]) {
      state.settings[config.settings.type13] = JSON.parse(localStorage.getItem(config.settings.type13) || "{}")[state.userInfo.workerNo] || "1,7,2,3,4,5,6";
    }
    // 分组排序（新）
    if (!state.settings[config.settings.type16]) {
      state.settings[config.settings.type16] = JSON.parse(localStorage.getItem(config.settings.type16) || "[]")[state.userInfo.workerNo] || [];
    }
    // 分组拖拽提示
    if (!state.settings[config.settings.type17]) {
      state.settings[config.settings.type17] = userLocalStorage({key: config.settings.type17, value: "0"}, 2);
    }
  },
  // 设置窗口大小
  setWindowSizeType: function (state, info) {
    state.windowSizeType = info;
  },
  // 退出应用
  setWindowExit: function () {
    remote.App.closeAllWindows();
    remote.App.quit();
  },
  // 关闭应用
  setWindowClose: function (state, info) {
    let thisWin = getChildWin("", info);
    // 子窗口关闭存在输入内容提示
    if (state.router.currentRoute?.path == "/child/childChat" && state.imEditor.hasContents(state.currentSession.id)) {
      alert({
        content: "输入区存在待发出的消息，确认关闭会话窗口？",
        done: (type) => {
          if (type == 1) {
            thisWin.close();
          }
        }
      });
    } else {
      thisWin.close();
    }
  },
  // 设置窗口大小 type-1登录-2默认-其他自定义
  setWindowSizeInfo: function (state, info) {
    let currentWindow = getChildWin("", info.currentWindow);
    let stateConfig = state.config;
    let resizable = info.resizable != null ? info.resizable : true;//可拖拽缩放
    let isTop = info.isTop || false;//是否在最顶层
    let minWidth = info.minW;
    let minHeight = info.minH;
    let maxWidth = info.maxW || screen.availWidth;
    let maxHeight = info.maxH || screen.availHeight;
    currentWindow.setResizable(true);
    if (!state.router.currentRoute || info.type == 1) {
      // 登录窗口
      currentWindow.unmaximize();
      info.width = 689;
      info.height = 499;
      info.center = true;
      resizable = false;
      isTop = true;
      minWidth = info.width;
      minHeight = info.height;
    } else if (info.type == 2) {
      // 默认窗口
      info.width = state.windowSizeType.width || stateConfig.width;
      info.height = state.windowSizeType.height || stateConfig.height;
      info.center = true;
    }
    if (info.initMin) {
      // 更新窗口最小尺寸
      minWidth = 980;
      // 分组模式窗口宽度最小值加150
      if (state.windowSizeType.tabType == 1) {
        minWidth += 150;
      }
      if (info.changeTabType && Math.abs(currentWindow.width - 150 - stateConfig.width) < 2 && state.windowSizeType.tabType == 0) {
        // 切换为精简模式缩小150px
        info.width = currentWindow.width - 150;
      }
      minHeight = 680;
      state.emit.initWinSize = true;
    }
    if (info.initWinSize) {
      state.emit.initWinSize = true;
    }
    // 设置窗口信息
    currentWindow.setAlwaysOnTop(isTop);
    if (minWidth && minHeight) {
      currentWindow.setMinimumSize(minWidth, minHeight);
    }
    currentWindow.setMaximumSize(maxWidth, maxHeight);
    // 设置窗口位置
    let winParam = currentWindow.getBounds();
    let setBoundsParam = {
      x: parseInt(info.x || winParam.x),
      y: parseInt(info.y || winParam.y),
      width: parseInt(info.width || winParam.width),
      height: parseInt(info.height || winParam.height),
      center: info.center
    }
    currentWindow.setBounds(setBoundsParam);
    currentWindow.setResizable(resizable);
  },
  // 最小化
  setWindowMin: function (state, info) {
    let thisWin = getChildWin("", info);
    if (isMainWin(info)) {
      // 主窗口才能隐藏
      thisWin.setShowInTaskbar(false);
    }
    thisWin.minimize();
  },
  // 取消最小化
  setWindowCancelMin: function (state, info) {
    let thisWin = getChildWin("", info);
    thisWin.setShowInTaskbar(true);
    thisWin.show();
    thisWin.focus();
  },
  // 最大化-登录窗口禁止
  setWindowMax: function (state, info) {
    if (state.router.currentRoute || !isMainWin(info)) {
      getChildWin("", info).enterFullscreen();
    }
  },
  // 取消最大化-登录窗口禁止
  setWindowCancelMax: function (state, info) {
    if (state.router.currentRoute || !isMainWin(info)) {
      getChildWin("", info).restore();
    }
  },
  // 设置电脑信息
  setComputerInfo: function (state, info) {
    state.computerInfo = info
    // 设置电脑信息map
    let computerMap = {
      s0: state.computerInfo.Mac,
      s1: state.computerInfo.baseboardSerialnumber,
      s2: state.computerInfo.cpuProcessorid,
      s3: state.computerInfo.diskdriveSerialnumber,
      s4: state.computerInfo.biosSerialnumber,
      s5: state.computerInfo.osSerialnumber,
      s6: state.computerInfo.UUID,
      s7: state.computerInfo.baseboardProduct,
      s8: state.computerInfo.diskdriveCaption,
      s9: state.computerInfo.cpuCaption,
      s10: state.computerInfo.gatewayMac,
      s11: state.computerInfo.gatewayIp,
      u0: state.computerInfo.userUUID
    };
    // computerMap.ssId = MD5(computerMap.s1 + "+" + computerMap.s2 + "+" + computerMap.s4 + "+" + computerMap.s6 + "+" + computerMap.s7 + "+" + computerMap.u0);
    state.computerMap = computerMap;
  },
  // 设置电脑基础信息
  setBaseComputerInfo: function (state, info) {
    state.baseComputerInfo = info
  },
  // 设置电脑网络连接信息
  setNetComputerInfo: function (state, info) {
    state.netComputerInfo = info
  },
  // 设置未登录当前选中用户信息
  tempUserInfo: function (state, info) {
    state.tempUserInfo = info;
  },
  // 设置登录用户信息
  setUserInfo: function (state, info) {
    console.time("登录跳转");
    info.headPic = getAvatar(info.headPic);
    info.env = state.config.config.env;
    // 设置获取人员信息接口通用字段
    info.workerName = info.name;
    info.name = `${info.name}(${info.deptName})`;
    // 设置人员类型1直营2加盟
    info.empType = info.accountType;
    if (!info.empType) {
      info.empType = new RegExp(config.jm).test(info.workerNo) ? 2 : 1;
    }
    state.userInfo = info;
    emitMsg("msg", {type: "global", setGlobal: 1, info: {userInfo: encrypt(JSON.stringify(info))}});
    emitMsg("msg", {type: "global", setGlobal: 1, info: {config: config}});
    if (localStorage.getItem("accountFlag") == 0) {
      // 不记住账号 清除记录
      localStorage.removeItem("loginData");
      localStorage.removeItem("loginDataNew");
    } else {
      // 设置登录记录
      let loginData = {
        type: 1,
        key: info.doLoginkey,
        data: {workerNo: info.doLoginkey, workerId: info.workerId, deptName: info.deptName, workerName: info.workerName, managerId: info.managerId, time: new Date().getTime(), protocolV: info.protocolS}
      };
      this.commit("setLoginData", loginData);
    }
    // 上报登录日志
    remote.store.dispatch("uploadZxp", {type: 1});
  },
  // 设置路由
  setRouter: function (state, info) {
    state.router = info;
  },
  // 退出登录 type-1退出登录-2退出登录清除全部数据-content为弹窗内容
  setLogout: function (state, info) {
    console.log("setLogout", info, !!state?.nimSDK?.disconnect, remote.logoutFlag);
    emitMsg("msg", {type: "global", setGlobal: 1, info: {userInfo: ""}});
    if (state.nimSDK && state.nimSDK.disconnect) {
      state.nimSDK.disconnect();
      if (info.content) {
        alert({
          title: "被踢提示",
          content: info.content,
          okText: "重新登录",
          showCancel: false,
          showClose: false,
          opacity: 0.9,
          done: () => {
            remote.logoutFlag = false;
            emitMsg("msg", {type: "logout", logout: info.type});
          }
        });
        remote.logoutFlag = true;
      } else {
        emitMsg("msg", {type: "logout", logout: info.type});
      }
      // 退出登录清空通知
      getChildWin("notify").window.emitMsg({type: "notifyMap", value: []});
    }
  },
  // 设置登录信息 1设置 2删除
  setLoginData: function (state, info) {
    console.log("setLoginData", info);
    // 设置登录历史
    let loginData = JSON.parse(localStorage.getItem("loginDataNew") || "{}");
    // 兼容历史登录数据
    if (Object.keys(loginData).length == 0) {
      loginData = JSON.parse(localStorage.getItem("loginData") || "{}");
    }
    if (info.type == 1) {
      loginData[info.key] = info.data;
    } else if (info.type == 2) {
      delete loginData[info.key];
    }
    setLocalStorage("loginDataNew", JSON.stringify(loginData));
  },
  // 设置云信对象
  setNim: function (state, info) {
    remote.store.state.nimSDK = state.nimSDK = info;
    if (state.empStatusTimer) {
      clearTimeout(state.empStatusTimer);
    }
    // 获取账号登录状态
    state.empStatusTimer = setInterval(() => {
      getEmpStatusApi({
        msgBody: JSON.stringify({
          empNumber: state.userInfo.workerId,
          token: state.userInfo.token,
          appName: "pc-im"
        })
      });
    }, 30 * 1000);
  },
  // 图片缓存队列定时器
  setImgQueue: function (state, info) {
    if (state.imgQueueTimer) {
      return;
    }
    state.imgQueueTimer = setInterval(async () => {
      let imgList = Object.values(state.imgQueue).sort((a, b) => {return b.time - a.time});
      let imgLoadList = Object.values(state.imgLoadQueue);
      for (let key in state.imgLoadQueue) {
        // 下载完成删除队列
        state.imgLoadQueue[key].downloadFileObj.then(res => {
          if (res.state == "success") {
            // 设置消息字段
            loadCache({url: state.imgLoadQueue[key].url, size: state.imgLoadQueue[key].size, ext: state.imgLoadQueue[key].ext, fileDB: state.fileDB, isDownLoad: false}).then(fileInfo => {
              try {
                state.imgLoadQueue[key].info.fileInfo = fileInfo.fileInfo;
              } catch (e) {}
              delete state.imgQueue[key];
              delete state.imgLoadQueue[key];
            })
          } else if (new Date().getTime() - state.imgLoadQueue[key].startTime > 1 * 60 * 1000) {
            // 1分钟未下载完成剔除
            res.clientRequest.destroy();
            delete state.imgQueue[key];
            delete state.imgLoadQueue[key];
          }
        });
      }
      // 最多本地化3个
      for (let i = 0; i < 3 - imgLoadList.length; i++) {
        if (!imgList[i]) {
          continue;
        }
        let loadCacheObj = (await loadCache({...imgList[i], isDownLoad: true, fileDB: state.fileDB}));
        if (!state.imgLoadQueue[imgList[i].md5] && loadCacheObj.downloadFileObj) {
          // 开始缓存本地图片
          state.imgLoadQueue[imgList[i].md5] = imgList[i];
          state.imgLoadQueue[imgList[i].md5].startTime = new Date().getTime();
          state.imgLoadQueue[imgList[i].md5].downloadFileObj = loadCacheObj.downloadFileObj;
        } else {
          // 删除已经缓存的记录
          delete state.imgQueue[imgList[i].md5];
        }
      }

      // 判断去除撤回重新编辑按钮
      for (let key in state.deleteMsgMap) {
        let item = state.deleteMsgMap[key];
        for (let key1 in item) {
          let item1 = item[key1];
          // 删除超过5分钟的撤回重新编辑
          if (item1 + 5 * 60 * 1000 <= new Date().getTime() + state.diffTime) {
            if (state.msgs[key]) {
              for (let i = 0; i < state.msgs[key].length; i++) {
                if (state.msgs[key][i].idServer == key1) {
                  delete state.msgs[key][i].deleteMsgDetail;
                  break;
                }
              }
            }
            delete state.deleteMsgMap[key][key1];
            if (Object.values(state.deleteMsgMap[key]).length == 0) {
              delete state.deleteMsgMap[key];
            }
          }
        }
      }
    }, 3 * 1000);
  },
  // 设置文件记录数据库对象
  setFileDB: function (state, info) {
    state.fileDB = initFileDB(info);
  },
  // 删除db
  setRemoveDB: function (state, info = {}) {
    removeDB(info.workerNo, info.type);
  },
  // 设置@我信息
  setHaitMsgMap: function (state, info) {
    let {id, account, item, type} = info;
    if (type == "remove") {
      delete state.haitMsgMap[id];
    } else {
      // 保留自己被@消息
      if (account == state.userInfo.workerNo || (account == "all" && !state.settings[config.settings.type15]?.[id])) {
        if (!state.haitMsgMap[id]) {
          state.haitMsgMap[id] = {};
        }
        if (!state.haitMsgMap[id][item.idServer]) {
          state.haitMsgMap[id][item.idServer] = item;
        }
        if (account == "all") {
          state.haitMsgMap[id].isHaitAll = true;
        }
        state.haitMsgMap[id].isShowTips = true;
      }
    }
    userCache({key: "haitMsgMap", value: state.haitMsgMap}, 1);
  },
  // 设置特别关心信息
  setConcernMsgMap: function (state, info) {
    let {id, item, type, account} = info;
    if (type == "deleteItem") {
      // 删除特别关心中的取消特别关心的人
      for (let key in state.concernMsgMap) {
        for (let key1 in state.concernMsgMap[key]) {
          if (state.concernMsgMap[key][key1].from == account) {
            delete state.concernMsgMap[key][key1];
          }
        }
        // 存在一个isShowTips
        if (Object.values(state.concernMsgMap[key]).length == 1) {
          delete state.concernMsgMap[key];
        }
      }
    } else if (type == "remove") {
      delete state.concernMsgMap[id];
    } else {
      if (state.concernMap["p2p-" + item.from]) {
        if (!state.concernMsgMap[info.id]) {
          state.concernMsgMap[info.id] = {};
        }
        if (item.scene != "p2p") {
          // 1v1不加入特别关心消息列表
          state.concernMsgMap[info.id][item.idServer] = item;
        }
        state.concernMsgMap[info.id].isShowTips = true;
      }
    }
    userCache({key: "concernMsgMap", value: state.concernMsgMap}, 1);
  },
  // 设置云信好友
  setNimFriend: function (state, info) {
    let type = info.type;
    if (type == "init") {
      // 初始化
      let nimFriendList = info.friends;
      // 设置云信好友
      nimFriendList.map(item => {
        // 去除非房产网账户
        if (!new RegExp(config.fcw).test(item.account)) {
          return;
        }
        item = setFcwUser(item);
        state.nimFriend[item.account] = item;
      });
      // 被删除的好友
      nimFriendList.invalid.map(item => {
        delete state.nimFriend[item.account];
      })
    } else if (type == "addFriend") {
      // 添加好友
      info.friend = setFcwUser(info.friend);
      state.nimFriend[info.account] = info.friend;
    } else if (type == "deleteFriend") {
      // 删除好友
      delete state.nimFriend[info.account];
    } else if (type == "updateFriend") {
      // 更新好友
      info.friend = setFcwUser(info.friend);
      if (info.friend.alias != null) {
        if (!state.nimFriend[info.friend.account]) {
          // 更新好友本地不存在好友信息则插入
          info.friend = setFcwUser(info.friend);
          state.nimFriend[info.friend.account] = info.friend;
        } else {
          state.nimFriend[info.friend.account].alias = info.friend.alias;
          state.nimFriend[info.friend.account].name = info.friend.alias;
        }
        // 更新fcw会话数据
        let sessionId = "p2p-" + info.friend.account;
        if (state.sessions[sessionId] && state.sessions[sessionId].detailInfo) {
          state.sessions[sessionId].detailInfo.alias = info.friend.alias;
          state.sessions[sessionId].detailInfo.name = setFcwUser(state.nimFriend[info.friend.account]).name;
          this.commit("setUpdateSessionId", sessionId);
        }
      }
    }
  },
  // 去除重复消息
  setMsgRepeat: function (state, info) {
    // idServer.idClient.uniqueSign去重
    let msgs = deepClone(state.msgs[info.id]);
    msgs = (msgs || []).concat(info.msgs).sort((a, b) => {return (a.time || 0) - (b.time || 0)});
    for (let i = 0; i < msgs.length; i++) {
      for (let j = i + 1; j < msgs.length; j++) {
        if ((msgs[i].idServer && msgs[i].idServer === msgs[j].idServer)
          || (msgs[i].idClient && msgs[i].idClient === msgs[j].idClient)
          || (msgs[i].uniqueSign && msgs[i].uniqueSign === msgs[j].uniqueSign)
        ) {
          console.log("msgRepeat:", info.id, msgs[i].idServer, msgs[j].idServer, msgs[i].idClient, msgs[j].idClient, msgs[i].status, msgs[j].status, msgs[i].uniqueSign, msgs[j].uniqueSign);
          // 本地生成消息为toSending，云信发送中消息为sending，依次去重
          if (msgs[i].deleteMsg || msgs[j].deleteMsg) {
            // 撤回消息
            try {
              // 删除对应@提示
              if (state.haitMsgMap[msgs[i].sessionId] && state.haitMsgMap[msgs[i].sessionId][msgs[i].idServer]) {
                delete state.haitMsgMap[msgs[i].sessionId][msgs[i].idServer];
                // 判断列表是否还有数据
                let hasList = false;
                for (let key in state.haitMsgMap[msgs[i].sessionId]) {
                  if (!isNaN(Number(key))) {
                    hasList = true;
                  }
                }
                if (!hasList) {
                  delete state.haitMsgMap[msgs[i].sessionId];
                }
              }
              // 删除对应特别关心提示
              if (state.concernMsgMap[msgs[i].sessionId] && state.concernMsgMap[msgs[i].sessionId][msgs[i].idServer]) {
                delete state.concernMsgMap[msgs[i].sessionId][msgs[i].idServer];
                // 判断列表是否还有数据
                let hasList = false;
                for (let key in state.concernMsgMap[msgs[i].sessionId]) {
                  if (!isNaN(Number(key))) {
                    hasList = true;
                  }
                }
                if (!hasList) {
                  delete state.concernMsgMap[msgs[i].sessionId];
                }
              }
              // 删除对应重要提醒
              let importantMsgIndex = state.importantMsg.findIndex(item => {return item.idServer == msgs[i].idServer});
              if (importantMsgIndex > -1) {
                state.importantMsg.splice(importantMsgIndex, 1);
                state.emit.importantMsg = state.importantMsg;
              }
              // 删除对应强提醒
              let remindMsgIndex = state.remindMsg.findIndex(item => {return item.idServer == msgs[i].idServer});
              if (remindMsgIndex > -1) {
                state.remindMsg.splice(remindMsgIndex, 1);
              }
              // 删除引用消息
              if (state.quoteMsg[msgs[i].sessionId]?.msg?.idServer == msgs[i].idServer) {
                delete state.quoteMsg[msgs[i].sessionId];
              }
            } catch (e) {
              console.log("setMsgRepeatDelErr", e);
            }
            // 自己发送的重新编辑-文字、图片、图文
            if ((/text|image/.test(msgs[i].type) || (msgs[i].type == "custom" && msgs[i].content && msgs[i].content.type == "multi"))
              && msgs[i].from == state.userInfo.workerNo) {
              msgs[j].deleteMsgDetail = msgs[i];
              if (!state.deleteMsgMap[info.id]) {
                state.deleteMsgMap[info.id] = {};
              }
              // 保存撤回消息时间，用于5分钟后取消显示重新编辑按钮
              state.deleteMsgMap[info.id][msgs[j].idServer] = Date.now() + state.diffTime;
            }
            if (msgs[i].deleteMsg) {
              msgs[i].time = msgs[j].time;
              msgs.splice(j, 1);
            } else {
              msgs[j].time = msgs[i].time;
              msgs.splice(i, 1);
            }
          } else if (msgs[i].status == "success") {
            msgs.splice(j, 1);
          } else if (msgs[j].status == "success") {
            msgs.splice(i, 1);
          } else if (msgs[i].status == "toSending") {
            msgs.splice(i, 1);
          } else if (msgs[j].status == "toSending") {
            msgs.splice(j, 1);
          } else if (msgs[i].status == "sending") {
            msgs.splice(i, 1);
          } else if (msgs[j].status == "sending") {
            msgs.splice(j, 1);
          } else if (msgs[i].status == "fail") {
            msgs.splice(i, 1);
          } else if (msgs[j].status == "fail") {
            msgs.splice(j, 1);
          } else {
            msgs.splice(i, 1);
          }
          i--;
          break;
        }
      }
    }
    // 最多显示100个消息记录，服务号和公众号不限制消息数
    if (msgs.length > 100 && !isSubOrSer(info.id)) {
      msgs = msgs.slice(-100);
    }
    state.msgs[info.id] = msgs;
  },
  // 设置会话
  setSessions: function (state, info) {
    let that = this;
    let workerMap = {};
    // 子窗口会话
    if (info.child) {
      state.sessions = info.sessions;
      return;
    }
    // 单个会话
    if (info && info.id) {
      info = [info];
    }
    // 遍历会话
    if (Array.isArray(info)) {
      for (let i = 0; i < info.length; i++) {
        let item = info[i];
        let sessionTypeInfo = getSessionType(item);
        let detailInfo = {
          name: sessionTypeInfo.name,
          avatar: sessionTypeInfo.avatar,
        };
        // 设置通知不提醒/群助手/置顶信息/@全员消息不提醒
        item.isNoTip = false;
        item.isHelper = false;
        item.isTop = false;
        item.isHaitAllNoTip = false;
        // 通知不提醒，包括用户设置和指定账号
        if (state.settings[config.settings.type1]?.[item.id] || config[config.env].noTipNumber == item.to) {
          item.isNoTip = true;
        }
        // 客户进线讨论组静音开关（需要创建超过24小时后）
        if ((state.teams[item.to]?.groupType == 2 || state.teams[item.to]?.groupType == 4) && state.settings[config.settings.type14] == 1 &&
          Date.now() + state.diffTime - state.teams[item.to]?.createTime > 24 * 60 * 60 * 1000) {
          item.isNoTip = true;
        }
        // 群助手
        if (state.settings[config.settings.type2] && state.settings[config.settings.type2][item.id]) {
          item.isHelper = true;
          item.isNoTip = false;
        }
        // @全员消息不提醒
        if (state.settings[config.settings.type15] && state.settings[config.settings.type15][item.id]) {
          item.isHaitAllNoTip = true;
        }
        // 置顶
        if ((state.settings[config.settings.type3] && state.settings[config.settings.type3][item.id]) || item.to == config.customerAccount) {
          // 改为直接取云信返回的状态，云信状态有异常
          item.isTop = true;
        }
        if (item.isHelper && !state.sessions["p2p-" + config.helperAccount]) {
          // 设置群助手
          let qzsItem = {scene: "p2p", to: config.helperAccount, id: "p2p-" + config.helperAccount, detailInfo: {name: "群助手"}};
          qzsItem.detailInfo.avatar = getSessionType(qzsItem).avatar;
          state.sessions["p2p-" + config.helperAccount] = qzsItem;
        } else if (isFcwList(item)) {
          this.commit("setCustomerBox");
          // 房产网备注
          if (sessionTypeInfo.type == 5) {
            detailInfo = getFcwInfo({account: item.to, nimFriend: state.nimFriend});
            // 房产网用户判断是否为好友-不是则请求接口添加
            if (new RegExp(config.fcw).test(item.to) && !state.nimFriend[item.to] && !state.addNimFriendMap[item.to]) {
              // 待添加客户列表
              state.addNimFriendMap[item.to] = item.to;
              debounce({
                timerName: "addFriend",
                time: 1000,
                fnName: function () {
                  let addNimFriendList = Object.values(state.addNimFriendMap);
                  // 再次处理非好友的房产网用户
                  addNimFriendList = addNimFriendList.filter(item => {return !state.nimFriend[item]});
                  if (addNimFriendList.length > 0 && !state.addNimFriendFlag) {
                    // 防止多次请求
                    state.addNimFriendFlag = true;
                    addNimFriendApi({
                      "msgBody": JSON.stringify({
                        accid: state.userInfo.workerNo,
                        faccid: addNimFriendList.toString(),
                        type: 1
                      })
                    }).then(res => {
                      state.addNimFriendFlag = false;
                      // 添加好友成功删除待添加数据
                      if (res.success) {
                        state.addNimFriendMap = {};
                      }
                    });
                  }
                }
              });
            }
          }
        } else if (sessionTypeInfo.type == 8) {
          // 设置主订阅号会话
          let dyhItem = {scene: "p2p", to: config.subscribe, id: "p2p-" + config.subscribe, detailInfo: {name: "乐聊订阅号"}};
          dyhItem.detailInfo.avatar = getSessionType(dyhItem).avatar;
          state.sessions["p2p-" + config.subscribe] = dyhItem;
        } else if (sessionTypeInfo.type == 6) {
          // 设置群通知会话
          item.unread = item.localCustom ? (item.localCustom.unread || 0) : 0;
        }
        if (item.scene == "p2p") {
          // 一对一
          if (state.persons[item.to]) {
            detailInfo = setUserBaseInfo(state.persons[item.to]);
          } else if (!isNaN(item.to) || new RegExp(config.ai).test(item.to) || new RegExp(config.jm)) {
            // 获取个人账号信息
            workerMap[item.to] = item.to;
          }
        } else {
          // 群
          if (state.teams[item.to]) {
            detailInfo = state.teams[item.to];
          }
          // 最后一条消息的人获取个人信息
          if (item.lastMsg && item.lastMsg.from && !state.persons[item.lastMsg.from]) {
            workerMap[item.lastMsg.from] = item.lastMsg.from;
          }
        }
        item.detailInfo = detailInfo;
        // 设置会话历史字段
        if (state.sessions[item.id] && state.sessions[item.id].notMore) {
          item.notMore = true;
        }
        // 设置本地未读
        !item.unread && (item.unread = state.localUnreadMap[item.id]);
        // 更新会话不存在最后一条消息，且会话列表存在则保留
        if (state.sessions[item.id] && state.sessions[item.id].lastMsg && !item.lastMsg) {
          item.lastMsg = state.sessions[item.id].lastMsg;
        }
        state.sessions[item.id] = item;
        // 子窗口
        let childWin = getChildWin("chat-" + item.id);
        if (childWin && isMainWin()) {
          childWin.window.store.commit("setSessions", {child: true, sessions: state.sessions});
        }
      }
    }
    // 更新会话
    let sessionList = Object.values(state.sessions);
    // 最多显示1000个会话
    if (sessionList.length > 1000) {
      sessionList.sort((a, b) => {return (b.updateTime || 0) - (a.updateTime || 0)});
      for (let i = sessionList.length - 1; i > 1000; i--) {
        delete state.sessions[sessionList[i].id];
      }
    }
    state.updateSessionsTime = Date.now();
    if (Object.keys(workerMap).length > 0) {
      that.commit("setPersons", {workerMap: workerMap});
    }
  },
  // 设置客户咨询盒子
  setCustomerBox: function (state, info) {
    if (!state.sessions["p2p-" + config.customerAccount]) {
      // 设置客户咨询
      let sessionInfo = this.getters.getSessions({sort: -1});
      let khzxItem = {scene: "p2p", to: config.customerAccount, id: "p2p-" + config.customerAccount, detailInfo: {name: "客户咨询"}, isTop: true, alwaysTop: 1, unread: sessionInfo.khzxUnread};
      khzxItem.detailInfo.avatar = getSessionType(khzxItem).avatar;
      state.sessions["p2p-" + config.customerAccount] = khzxItem;
    }
  },
  // 更新会话
  setUpdateSessionId: function (state, info) {
    // 同个会话更新已读状态
    if (state.updateSessionId == info) {
      state.updateSessionId = "";
    }
    let childWin = getChildWin("chat-" + info);
    if (childWin && isMainWin()) {
      childWin.window.store.commit("setUpdateSessionId", info);
    }
    state.updateSessionId = info;
  },
  // 同步完成删除不存在的群会话
  setRemoveSessions: function (state, info) {
    let sessionList = [];
    for (let key in state.sessions) {
      let item = state.sessions[key]
      if (item.scene == "team" && !state.teams[item.to]) {
        // 删除不存在的群列表
        state.nimSDK.deleteLocalSession({id: item.id, isLocal: true, initFlag: true});
        delete state.sessions[item.id];
        sessionList.push(item.id);
      }
    }
    if (sessionList.length > 0) {
      state.updateSessionsTime = Date.now();
    }
  },
  // 获取用户信息 accounts-一定为人员数组 focus-强制获取服务器数据 done-执行完成回调
  setPersons: function (state, info) {
    let that = this;
    // 查询本地数据库
    let accounts = Object.values(info.workerMap);
    state.userDB.search(accounts).then(function (dbPersons) {
      // 设置本地缓存
      that.commit("setPersonsInfo", dbPersons);
      // 数字人列表
      let arrAi = [];
      // 员工列表
      let arrUser = [];
      // 请求中的员工数据
      let userReqMap = {};
      // 处理存在缓存/正在请求/数字人数据
      for (let key in info.workerMap) {
        if (info.focus || (!state.userReqMap[key] && !state.persons[key])) {
          // 智能助理不请求
          if (key != state.aiObj.workerNo) {
            if (new RegExp(config.ai).test(key)) {
              arrAi.push(key);
            } else {
              arrUser.push(key);
            }
            if (!info.notReqFlag) {
              state.userReqMap[key] = key;
              userReqMap[key] = key;
            }
          }
        }
      }

      // 每1000个发起一次请求,并发
      let ps = [];
      let arr1 = [];
      let arr2 = arrUser.slice(0);
      while (arr2.length > 0) {
        arr1 = arr2.slice(0, 1000);
        arr2 = arr2.slice(1000);
        ps.push(getUserBatchApi({
          msgBody: JSON.stringify({
            nowWorkerNo: state.userInfo.workerNo,
            workerNo: JSON.stringify(arr1),
          })
        }));
      }
      let arrAi1 = [];
      let arrAi2 = arrAi.slice(0);
      while (arrAi2.length > 0) {
        arrAi1 = arrAi2.slice(0, 1000);
        arrAi2 = arrAi2.slice(1000);
        ps.push(getAppCardByAccIdsApi({
          accIds: arrAi
        }))
      }
      if (ps.length > 0) {
        Promise.allSettled(ps).then(function (results) {
          // 去除请求中的人员数据
          for (let key in userReqMap) {
            delete state.userReqMap[key];
          }
          let datas = [];
          // 合并所有请求结果
          if (results?.length > 0) {
            Array.prototype.concat.apply(datas, results).map(function (item) {
              let userList = item?.value?.data?.data || item?.value?.data;
              if (userList?.length > 0) {
                for (let i = 0; i < userList.length; i++) {
                  let item = userList[i];
                  // 设置基础信息
                  if (item.workerNo || item.nimAccid) {
                    datas.push(setUserBaseInfo(item));
                  }
                }
              }
            });
            if (datas.length > 0) {
              // 批量更新本地数据库
              state.userDB.bulkPut(datas);
              // 设置本地缓存
              that.commit("setPersonsInfo", datas);
            }
            // 执行回调
            if (info.done) {
              info.done();
            }
          }
        });
      } else {
        // 执行回调
        if (info.done) {
          info.done();
        }
      }
    });
  },
  // 设置人员信息到缓存
  setPersonsInfo: function (state, info) {
    // 设置本地缓存
    for (let i = 0; i < info.length; i++) {
      let item = info[i];
      let no = item.account || item.workerNo;
      // 设置会话信息
      if (state.sessions["p2p-" + no]) {
        state.sessions["p2p-" + no].detailInfo = item;
      }
      state.persons[no] = item;
    }
  },
  // 设置群
  setTeams: function (state, info) {
    let {teamList, localTeamFlag, initTeamFlag} = info;
    if (localTeamFlag) {
      // 初始化本地会话的过程如果群列表已经回调不设置群信息
      if (Object.keys(state.teams).length == 0) {
        state.teamsInit++;
      } else {
        return;
      }
    }
    if (initTeamFlag && state.teamsInit == 1) {
      // 非初始化本地会话群信息过程重置群信息
      state.teamsInit++;
      state.teams = {};
    }
    // 获取群主信息
    let workerMap = {};
    let groupTypeMap = state.groupTypeMap;
    // 遍历会话
    for (let i = 0; i < teamList.length; i++) {
      let item = teamList[i];
      try {
        if (item.custom && typeof item.custom == "string") {
          item.custom = JSON.parse(item.custom);
        }
      } catch (e) {
        console.log("customError", item, e);
      }
      try {
        if (item.serverCustom && typeof item.serverCustom == "string") {
          item.serverCustom = JSON.parse(item.serverCustom);
        }
      } catch (e) {
        console.log("serverCustomError", item, e);
      }
      if (item.serverCustom) {
        item.groupType = item.serverCustom.groupType;
        item.groupDisabled = item.serverCustom.groupDisabled;
        if (item.serverCustom.alias) {
          item.name = item.serverCustom.alias;
        }
        if (item.groupType) {
          // 设置业务讨论组对象
          if (!groupTypeMap[item.groupType]) {
            groupTypeMap[item.groupType] = {name: item.serverCustom.typeName, uuid: item.groupType, sortIndex: item.createTime, list: [], num: 0};
          } else if (item.createTime < item.createTime) {
            groupTypeMap[item.groupType].sortIndex = item.createTime;
          }
        }
      }
      item.detailType = "team";
      if (item.custom?.type == "group" || item.serverCustom?.type == "group") {
        item.detailType = "group";
        if (!item.avatar) {
          item.avatar = "/img/default/group.png";
        }
      }
      // 超大群
      if (item.diyTeamType == 2) {
        item.detailType = "superTeam";
      }
      state.teams[item.teamId] = item;
      if (!state.persons[item.owner]) {
        workerMap[item.owner] = item.owner;
      }
    }
    state.groupTypeMap = Object.assign(groupTypeMap, state.groupTypeMap);
    // 重新渲染会话
    this.commit("setSessions", Object.values(state.sessions));
    if (Object.keys(workerMap).length > 0) {
      this.commit("setPersons", {workerMap: workerMap});
    }
  },
  // 更新群
  updateTeam: function (state, info) {
    // 子窗口会话
    if (info.child) {
      state.teams[info.teamId] = info.team;
      return;
    }
    let teamInfo = state.teams[info.teamId];
    // 子窗口
    let childWin = "";
    if (teamInfo) {
      let teamId = (teamInfo.detailType == "superTeam" ? "superTeam-" : "team-") + teamInfo.teamId;
      childWin = getChildWin("chat-" + teamId);
      switch (info.updateType) {
        case "dismiss":
        // 解散群
        case "remove":
          // 被踢出群
          delete state.teams[info.teamId];
          delete state.teamMembers[info.teamId];
          if (childWin) {
            childWin.close();
            childWin = "";
          }
          break;
        case "update":
          // 更新群信息
          if (info.name != null) {
            state.teams[info.teamId].name = info.name;
            remote.store.commit("setUpdateSessionId", info.scene + "-" + info.teamId);
          }
          // 群头像
          if (info.avatar != null) {
            state.teams[info.teamId].avatar = info.avatar;
          }
          // 群主
          if (info.owner != null) {
            state.teams[info.teamId].owner = info.owner;
          }
          // 自定义群拓展字段
          if (info.custom != null) {
            state.teams[info.teamId].custom = JSON.parse(info.custom);
            remote.store.commit("setUpdateSessionId", info.scene + "-" + info.teamId);
          }
          // 服务器拓展字段
          if (info.serverCustom != null) {
            state.teams[info.teamId].serverCustom = JSON.parse(info.serverCustom);
            state.teams[info.teamId].groupType = state.teams[info.teamId].serverCustom.groupType;
            state.teams[info.teamId].groupDisabled = state.teams[info.teamId].serverCustom.groupDisabled;
            if (state.teams[info.teamId].serverCustom.alias) {
              state.teams[info.teamId].name = state.teams[info.teamId].serverCustom.alias;
            }
            remote.store.commit("setUpdateSessionId", info.scene + "-" + info.teamId);
          }
          // 禁言
          if (info.mute != null) {
            state.teams[info.teamId].mute = info.mute;
          }
          break;
      }
      if (childWin && isMainWin()) {
        childWin.window.store.commit("updateTeam", {child: true, teamId: info.teamId, team: deepClone(state.teams[info.teamId])});
      }
    }
  },
  // 设置群成员
  setTeamMembers: function (state, info) {
    // 子窗口会话
    if (info.child) {
      state.teamMembers[info.id] = info.teamMembers;
      return;
    }
    // 删除群成员数据
    if (info.del) {
      delete state.teamMembers[info.id];
    } else {
      let members = info.members || [];
      // 获取群员信息
      let workerMap = {};
      members.map(item => {
        if (!state.persons[item.account]) {
          workerMap[item.account] = item.account;
        }
      })
      if (Object.keys(workerMap).length > 0) {
        this.commit("setPersons", {workerMap: workerMap, notReqFlag: info.notReqFlag});
      }
      // 设置群员数据
      state.teamMembers[info.id] = members;
      if (members?.length > 0) {
        state.updateMemberTeamInfo = {id: info.id, time: Date.now()};
      }
    }
    let teamInfo = state.teams[info.id];
    if (teamInfo) {
      let teamId = (teamInfo.detailType == "superTeam" ? "superTeam-" : "team-") + teamInfo.teamId;
      // 子窗口
      let childWin = getChildWin("chat-" + teamId);
      if (childWin && isMainWin()) {
        childWin.window.store.commit("setTeamMembers", {child: true, id: info.id, teamMembers: state.teamMembers[info.id]})
        childWin.window.store.commit("setUpdateMemberTeamInfo", {id: info.id, time: Date.now()});
      }
    }
  },
  // 更新群成员
  updateTeamMembers: function (state, info) {
    // 获取群员信息
    let workerMap = {};
    let members = state.teamMembers[info.team.teamId];
    let userTeamInfo = state.userTeamInfos[info.team.teamId];
    let teamInfo = state.teams[info.team.teamId];
    // 更新群成员信息
    if (members && !(members instanceof Promise)) {
      switch (info.updateType) {
        case "remove":
          // 移出群成员
          info.accounts.map(item => {
            let memberIndex = members.findIndex(item1 => {return item1.account == item});
            if (memberIndex != -1) {
              members.splice(memberIndex, 1);
            }
            // 去除小乐标记
            if (item == config[config.env].robotEmpNo && state.userTeamInfos[info.team.teamId]) {
              state.userTeamInfos[info.team.teamId].hasRobot = false;
            }
          });
          break;
        case "add":
          // 加入群成员
          info.members.map(item => {
            item.type = "normal";
            let memberIndex = members.findIndex(item1 => {return item1.id == item.id});
            if (memberIndex == -1) {
              members = members.concat(item);
              if (!state.persons[item.account]) {
                workerMap[item.account] = item.account;
              }
            }
            // 加入小乐标记
            if (item.account == config[config.env].robotEmpNo && state.userTeamInfos[info.team.teamId]) {
              state.userTeamInfos[info.team.teamId].hasRobot = true;
            }
          });
          break;
        case "update":
          // 更新群成员
          info.members.map(item => {
            let memberIndex = members.findIndex(item1 => {return item1.id == item.id});
            if (memberIndex != -1) {
              // 群成员类型(群主/管理员/普通)
              if (item.type != null) {
                members[memberIndex].type = item.type;
              }
              // 群成员禁言状态
              if (item.mute != null) {
                members[memberIndex].mute = item.mute;
              }
            }
          });
          break;
      }
      if (Object.keys(workerMap).length > 0) {
        this.commit("setPersons", {workerMap: workerMap});
      }
      // 设置群成员
      state.teamMembers[info.team.teamId] = members;
      if (members?.length > 0) {
        state.updateMemberTeamInfo = {id: info.team.teamId, time: Date.now()};
      }
      // 设置子窗口群员数据
      let teamId = (teamInfo.detailType == "superTeam" ? "superTeam-" : "team-") + teamInfo.teamId;
      if (getChildWin("chat-" + teamId)) {
        getChildWin("chat-" + teamId).window.store.commit("setTeamMembers", {child: true, id: info.team.teamId, teamMembers: members});
        getChildWin("chat-" + teamId).window.store.commit("setUpdateMemberTeamInfo", {id: info.team.teamId, time: Date.now()});
      }
    }
    // 更新自己在群信息
    if (userTeamInfo && info.updateType == "update") {
      info.members.map(item => {
        if (item.id == info.team.teamId + "-" + state.userInfo.workerNo) {
          // 群成员类型(群主/管理员/普通)
          if (item.type != null) {
            userTeamInfo.type = item.type;
          }
          // 群成员禁言状态
          if (item.mute != null) {
            userTeamInfo.mute = item.mute;
          }
          // 设置子窗口群员数据
          let teamId = (teamInfo.detailType == "superTeam" ? "superTeam-" : "team-") + teamInfo.teamId;
          if (getChildWin("chat-" + teamId)) {
            getChildWin("chat-" + teamId).window.store.commit("setUserTeamInfo", {id: info.team.teamId, value: userTeamInfo});
          }
        }
      });
    }
    // 更新群成员数据
    if (teamInfo && info.team.memberNum) {
      teamInfo.memberNum = info.team.memberNum;
    }
  },
  // 设置更新自己在群信息
  setUserTeamInfo: function (state, info) {
    state.userTeamInfos[info.id] = info.value;
  },
  // 设置更新群成员数据
  setUpdateMemberTeamInfo: function (state, info) {
    state.updateMemberTeamInfo = info;
  },
  // 设置配置信息
  setSetting: function (state, info) {
    if (!info) return;
    // 更新群提醒
    if (info.key == config.settings.type0 || info.key == config.settings.type1 || info.key == config.settings.type2 || info.key == config.settings.type15) {
      remote.store.commit("setUpdateSessionId", info.value);
    }
    if (info.child) {
      // 子窗口直接设置
      state.settings = info.settings;
      return;
    }
    let key = info.key;
    let values = "";
    let oldKey = info.oldKey;
    // 会话备注
    if (key == config.settings.type9 && info.value) {
      values = JSON.parse(info.value)
    } else {
      try {
        values = info.value ? info.value.split(",") : null;
      } catch (e) {}
    }
    let i;
    if (info.operation === "update" && oldKey && key && values.length > 0) {
      for (i = 0; i < values.length; i++) {
        if (state.settings[oldKey] && state.settings[oldKey][values[i]]) delete state.settings[oldKey][values[i]];
        if (!state.settings[key]) {
          state.settings[key] = {};
        }
        if (key == config.settings.type9 && info.value) {
          state.settings[key][values[i].accid] = values[i];
        } else {
          state.settings[key][values[i]] = values[i];
        }
      }
    } else if (info.operation === "add") {
      for (i = 0; i < values.length; i++) {
        if (!state.settings[key]) {
          state.settings[key] = {};
        }
        if (key == config.settings.type9 && info.value) {
          state.settings[key][values[i].accid] = values[i];
        } else {
          state.settings[key][values[i]] = values[i];
        }
      }
    } else if (info.operation === "delete") {
      for (i = 0; i < values.length; i++) {
        if (key == config.settings.type9 && info.value) {
          delete state.settings[key][values[i].accid];
        } else {
          if (state.settings[key] && state.settings[key][values[i]]) {
            delete state.settings[key][values[i]];
          }
        }
      }
    } else if (info.operation === "change") {
      if (!state.settings[info.key]) {
        state.settings[info.key] = {};
      }
      state.settings[info.key][info.key1] = info.value1;
    } else {
      // 默认直接变更
      state.settings[info.key] = info.value;
      try {
        if (info.key == config.settings.type16) {
          state.settings[info.key] = JSON.parse(state.settings[info.key]);
        }
      } catch (e) {}
      // 切换截图快捷键
      if (info.key == config.settings.type6) {
        this.commit("setShortcut", {key: "jt", value: info.value});
      }
    }
    // 重新渲染会话
    this.commit("setSessions", Object.values(state.sessions));
    // 客户讨论组设置通知状态需要计算客户咨询盒子未读数
    if (info.key == config.settings.type0 || info.key == config.settings.type1) {
      if (isFcwList(state.sessions[info.value], "", "", true)) {
        if ((info.key == config.settings.type0 && info.type == "add") || (info.key == config.settings.type1 && info.operation == "delete")) {
          state.sessions["p2p-" + config.customerAccount].unread += state.sessions[info.value].unread || 0;
        } else {
          state.sessions["p2p-" + config.customerAccount].unread -= state.sessions[info.value].unread || 0;
        }
      }
    } else if (info.key == config.settings.type14) {
      // 客户进线静音开关重新计算客户盒子咨询未读数
      let sessionInfo = this.getters.getSessions({sort: -1});
      state.sessions["p2p-" + config.customerAccount].unread = sessionInfo.khzxUnread;
    }
  },
  // 用于组件通讯
  setEmit: function (state, info) {
    // type=="scroll" bottom-滚动到底部，msg-收到消息判断是否滚动，top-滚动到底部，具体值-滚动到具体位置
    // type=="click" 全局点击事件,type=="toNewSys" 跳转新系统,type=="scrollSessionList“滚动会话列表
    state.emit[info.type] = info.value;
  },
  // 云信文件上传进程，用于终止上传
  setNimFileUpload: function (state, info) {
    if (info.value == "del") {
      // 通知结束
      if (remote.store.state.nimFileUpload[info.key]?.event) {
        remote.store.state.nimFileUpload[info.key].event.emit("progress", {key: "del"});
      }
      delete remote.store.state.nimFileUpload[info.key];
    } else {
      if (!remote.store.state.nimFileUpload[info.key]) {
        remote.store.state.nimFileUpload[info.key] = {};
      }
      // 设置信息
      if (info.value.notChange) {
        remote.store.state.nimFileUpload[info.key] = info.value.upload;
      } else {
        for (let key in info.value) {
          remote.store.state.nimFileUpload[info.key][key] = info.value[key];
        }
      }
      // 设置发布监听模式
      if (!remote.store.state.nimFileUpload[info.key].event) {
        remote.store.state.nimFileUpload[info.key].event = new Events();
      }
      remote.store.state.nimFileUpload[info.key].event.emit("progress");
    }
  },
  // 设置本地未读会话
  setLocalUnreadMap: function (state, info) {
    if (info.type == "del") {
      delete state.localUnreadMap[info.id];
      if (isFcwList(state.sessions[info.id], "", "", true) && !state.sessions[info.id].isNoTip) {
        state.sessions["p2p-" + config.customerAccount].unread -= state.sessions[info.id].unread || 0;
      }
    } else {
      state.localUnreadMap[info.id] = 1;
      state.sessions[info.id].unread = 1;
      if (isFcwList(state.sessions[info.id], "", "", true) && !state.sessions[info.id].isNoTip) {
        this.commit("setCustomerBox");
        state.sessions["p2p-" + config.customerAccount].unread += 1;
      }
    }
    state.updateSessionsTime = Date.now();
    // 设置本地缓存
    userLocalStorage({key: "localUnreadMap", value: state.localUnreadMap}, 1);
  },
  // 设置特别关心/重要标记列表
  setConcernOrImportantMark: function (state, info) {
    if (info.keyType == 2) {
      // 重要标记
      if (info.type == "del") {
        delete state.importantMarkMap[info.id];
      } else {
        state.importantMarkMap[info.id] = true;
      }
      // 设置本地缓存
      userLocalStorage({key: "importantMark", value: state.importantMarkMap}, 1);
    } else {
      // 默认特别关心
      if (info.type == "del") {
        delete state.concernMap[info.id];
        delete state.concernMsgMap[info.id];
      } else {
        state.concernMap[info.id] = true;
      }
      // 设置本地缓存
      userLocalStorage({key: "concernMap", value: state.concernMap}, 1);
    }

    // 发送通知给自己和别人(用于多端同步)
    state.nimSDK.sendCustomSysMsg({
      scene: "p2p",
      to: state.userInfo.workerNo,
      content: JSON.stringify({
        type: "updateSpecialConcernData"
      }),
      done: function () {}
    });
  },
  // 设置引用消息被撤回记录
  setQuoteDeleteMap: function (state, info) {
    if (!state.quoteDeleteMap[info.id]) {
      state.quoteDeleteMap[info.id] = {};
    }
    state.quoteDeleteMap[info.id][info.idServer] = info.idServer;
    // 设置本地缓存
    userLocalStorage({key: "quoteDeleteMap", value: state.quoteDeleteMap}, 1);
  },
  // 设置最近联系人列表
  setRecentContactsMap: function (state, info) {
    if (info) {
      state.recentContactsMap[info] = new Date().getTime() + state.diffTime;
    } else {
      // 设置本地缓存
      userLocalStorage({key: "recentContactsMap", value: state.recentContactsMap}, 1);
    }
  },
  // 设置引用消息
  setQuoteMsg: function (state, info) {
    // 判断是否子窗口引用
    let chatChild = getChildWin("chat-" + info.id);
    if (info.type == "del") {
      // 删除引用
      delete state.quoteMsg[info.id];
      if (chatChild) {
        delete chatChild.window.store.state.quoteMsg[info.id];
      }
    } else {
      // 设置引用
      let quoteMsg = info.msg;
      state.quoteMsg[info.id] = quoteMsg;
      if (chatChild) {
        chatChild.window.store.state.quoteMsg[info.id] = quoteMsg;
      }
    }
  },
  // 设置@信息
  setHaitInfo: function (state, info) {
    if (info.changeType) {
      // 只改变状态
      state.haitInfo.type = info.type;
      if (state.haitInfo.type == 1) {
        state.haitInfo.docIndex = info.docIndex;
      } else if (state.haitInfo.type == 2) {
        state.haitInfo.appIndex = info.appIndex;
      } else if (state.haitInfo.type == 3) {
        state.haitInfo.aiIndex = info.aiIndex;
      } else {
        state.haitInfo.memberIndex = info.memberIndex;
      }
    } else {
      state.haitInfo = info;
    }
  },
  // 设置通讯录数据结构
  setMailList: function (state) {
    let baseObj = {};
    let baseList = [];
    state.friends.forEach(i => {
      //姓首字母-大写
      let xm_first = ((i.workerSpell || "").split('')[0] || "").toUpperCase();
      if (baseObj[xm_first]) {
        baseObj[xm_first].push(i)
      } else {
        baseObj[xm_first] = [i]
      }
    })
    //排序
    for (var i = 65; i <= 90; i++) {
      let xm_first = String.fromCharCode(i);
      if (Array.isArray(baseObj[xm_first])) {
        baseList.push({name: xm_first, list: baseObj[xm_first]})
      }
    }
    state.mailList = baseList
  },
  // 设置聚焦元素
  setFocusMsg: function (state, info) {
    state.focusMsg = info;
  },
  // 设置经纪人合作细腻
  setCooperateInfo: function (state, info) {
    let {account, data} = info;
    state.cooperateInfo[account] = data;
  },
  // 设置认识档案权限
  setHrAuthority: function (state, info) {
    state.hrAuthority[info.account] = info;
  },
  // 设置常用表情计数
  setOftenEmoji: function (state, info) {
    let oftenEmoji = state.oftenEmoji || [];
    let emojiIndex = oftenEmoji.findIndex(item => {return info == item.emojiName});
    if (emojiIndex != -1) {
      oftenEmoji[emojiIndex].emojiCount++;
    } else {
      oftenEmoji.push({
        emojiCount: 1,
        emojiName: info
      })
    }
    state.oftenEmoji = oftenEmoji;
  },
  // 初始化截图
  setInitJt: function (state, info) {
    initJt();
  },
  // 设置快捷键-unregister原快捷键，register新快捷键
  setShortcut: function (state, info) {
    let register = "";
    let unregister = "";
    // 快捷键列表不存在才能注册该快捷键
    if (info.key == "jt") {
      register = config.shortcut.jt1;
      unregister = config.shortcut.jt2;
      emitMsg("msg", {type: "shortcut", unregister: register});
      emitMsg("msg", {type: "shortcut", unregister: unregister});
      if (info.value != register) {
        unregister = register;
        register = info.value;
      }
    }
    if (register) {
      emitMsg("msg", {type: "shortcut", register: register, unregister: unregister});
    }
  },
  // 设置气泡通知窗口
  setNotifyWin: function (state, info) {
    if (info == "add" && !getChildWin("notify")) {
      // 初始化通知窗口
      emitMsg("msg", {
        type: "window", newWin: 1, name: "notify", width: 10, height: 10, transparent: true, blur: true, x: 0, y: 0, changePath: true, minWidth: 10, minHeight: 10, taskbar: false,
        path: (process.env.NODE_ENV != "development" ? `/notify.html` : `http://localhost:8888/notify.html`),
      });
    } else if (info == "del" && getChildWin("notify")) {
      // 关闭通知窗口
      getChildWin("notify").close(true);
    }
  },
  // 设置窗口抖动时间
  setShakeTimeMap: function (state, info) {
    state.shakeTimeMap[info.id] = info.time;
  },
  // 设置编辑器高度
  setEditorHeightMap: function (state, info) {
    state.editorHeightMap[info.id] = info.height;
  },
  // 设置编辑器内容
  setEditorContentMap: function (state, info) {
    state.editorContentMap[info.id] = info.content;
  },
  // 修改分组数据
  updateGroupSetting: function (state, info) {
    if (info.type == 2) {
      // 讨论组分组变更通知更新
      state.updateSessionsTime = Date.now();
    }
    let uuids = state.groupSettings.map(i => {return i.uuid});
    let op = info.op || info.operation;//add update delete
    if (!info.uuid || (info.upKey == "team" && op == "update" && !info.olduuid)) {
      //不存在uuid就请求接口
      remote.store.dispatch('setGroupSetting');
      return
    }
    let t_index = uuids.indexOf(info.uuid);
    if (op != 'add' && t_index == -1) return
    if (info.upKey == 'tag') {
      if (op == 'update') {
        if (info.value != null) {
          state.groupSettings[t_index].value = info.value
        }
        if (info.name) {
          state.groupSettings[t_index].name = info.name
        }
      } else if (op == 'delete') {
        state.groupSettings.splice(t_index, 1)
      } else if (op == 'add') {
        state.groupSettings.push(info)
      }
    }
    if (info.upKey == 'team') {
      let newReg = new RegExp(`(${info.value},)|(,${info.value})`)
      if (op == 'update') {
        if (info.value) {
          if (info.olduuid != 'default') {
            let ot_index = uuids.indexOf(info.olduuid);
            state.groupSettings[ot_index].value = state.groupSettings[ot_index].value.replace(newReg, '')
          }
          state.groupSettings[t_index].value += `,${info.value}`
        } else {
          state.groupSettings[t_index].name = info.name
        }
      } else if (op == 'delete') {
        if (info.value) {
          // 移动分组
          state.groupSettings[t_index].value = state.groupSettings[t_index].value.replace(newReg, '')
        } else {
          // 删除分组
          state.groupSettings.splice(t_index, 1)
        }
      } else if (op == 'add') {
        // 这个地方会返回一个新的id
        remote.store.dispatch('setGroupSetting');
      }
    }
  },
  // 新-更新分组数据
  updateLabelClassify: function (state, info) {
    let index = -1;
    if (info.uuid) {
      index = state.groupSettings.findIndex(item => {return item.uuid == info.uuid});
    }
    switch (info.op) {
      case "add":
        state.groupSettings.unshift(info);
        break;
      case "rename":
        if (index > -1) {
          state.groupSettings[index].name = info.name;
        }
        break;
      case "delete":
        if (index > -1) {
          state.groupSettings.splice(index, 1);
        }
        break;
      case "move":
        // 批量移动
        info.value.map(item => {
          let valueReg = new RegExp(`(${item.value},)|(,${item.value})|${item.value}`);
          let itemIndex = state.groupSettings.findIndex(gItem => {return gItem.uuid == item.uuid});
          if (itemIndex > -1) {
            // 原分组移出
            state.groupSettings[itemIndex].value = state.groupSettings[itemIndex].value.replace(valueReg, "");
          }
          // 添加至新分组
          if (!valueReg.test(state.groupSettings[index].value)) {
            state.groupSettings[index].value += `,${item.value}`;
          }
        });
        break;
      case "moveIn":
        // 批量移入
        info.value.map(item => {
          let itemIndex = state.groupSettings.findIndex(gItem => {return gItem.uuid == info.uuid});
          if (itemIndex > -1) {
            // 移入内容在分组内不存在才添加
            let valueReg = new RegExp(`(${item.value},)|(,${item.value})|${item.value}`);
            if (!valueReg.test(state.groupSettings[itemIndex].value)) {
              state.groupSettings[itemIndex].value += `,${item.value}`;
            }
          }
        });
        break;
      case "moveOut":
        // 批量移出
        info.value.map(item => {
          let itemIndex = state.groupSettings.findIndex(gItem => {return gItem.uuid == info.uuid});
          if (itemIndex > -1) {
            let valueReg = new RegExp(`(${item.value},)|(,${item.value})|${item.value}`);
            state.groupSettings[itemIndex].value = state.groupSettings[itemIndex].value.replace(valueReg, "");
          }
        });
        break;
    }
    if (info.type == 2) {
      // 讨论组分组变更通知更新
      state.updateSessionsTime = Date.now();
    }
  },
  // 从云信本地数据库中获取到数据
  getLocalSysMsgs: function (state, info) {
    state.nimSDK.getLocalSysMsgs().then(res => {
      if (!res.err) {
        state.teamLocalMsgs = res.obj.sysMsgs.filter(i => {return config.teamNotifyTypes.indexOf(i.type) > -1});
      }
    });
  },
  // 修改云信本地数据库超大群的通知信息
  updateLocalSysMsgs: function (state, info) {
    let t_state = info.state;
    let idServer = info.idServer;

    let db_name = 'nim-' + state.userInfo.workerNo;
    let openDB = indexedDB.open(db_name);
    let that = this
    openDB.onsuccess = function () {
      let db = openDB.result;
      console.log('数据库打开成功', db);
      var store = db.transaction("sysMsg", 'readwrite').objectStore("sysMsg");
      var openCursor = store.openCursor();
      openCursor.onsuccess = function (e) {
        var cursor = e.target.result,
          value,
          updateRequest;
        if (cursor) {//必须要检查
          console.log(cursor);
          if (cursor.value.idServer == idServer) {
            console.log('游标开始更新')
            value = cursor.value;
            value.state = t_state;
            updateRequest = cursor.update(value);
            updateRequest.onerror = function () {
              console.log('游标更新失败');
            };
            updateRequest.onsuccess = function () {
              console.log('游标更新成功');
              that.commit("getLocalSysMsgs");
            }
          } else {
            cursor.continue();
          }
        }
      }
    }
  },
  // 设置群未读通知（从localstorage设置/从参数设置）结构：{ workerNo1 :[teamId1,teamId2],workerNo2:[teamId1,teamId2,teamId3]}
  setNoticeUnread: function (state, info) {
    let noticeLocalUnread = window.localStorage.getItem("noticeUnread");
    if (!info || !info.teamId) {
      // 从本地缓存中获取
      if (noticeLocalUnread) {
        state.noticeUnread = JSON.parse(noticeLocalUnread)[state.userInfo.workerNo] || []
      } else {
        setLocalStorage("noticeUnread", JSON.stringify({}));
      }
    } else {
      // 从参数中获取
      let t_index = state.noticeUnread.indexOf(info.teamId);
      if (t_index == -1 && info.type == 1) {
        state.noticeUnread.push(info.teamId);
      } else if (t_index != -1 && info.type == 2) {
        state.noticeUnread.splice(t_index, 1);
      }
      let json_unread = {};
      // 避免开发时候清掉缓存报错，这里还是判断一次，生产应该不判断也不会报错
      if (noticeLocalUnread) {
        json_unread = JSON.parse(noticeLocalUnread);
      }
      json_unread[state.userInfo.workerNo] = state.noticeUnread;
      setLocalStorage('noticeUnread', JSON.stringify(json_unread));
    }
  },
  // 设置图片加载和线路切换定时器
  setImageLoadTimer: function (state, info) {
    if (info.type == "clear") {
      for (let key in state.imageLoadTimer) {
        clearTimeout(state.imageLoadTimer[key]);
        delete state.imageLoadTimer[key];
      }
    } else if (info.type == "del") {
      clearTimeout(state.imageLoadTimer[info.key]);
      delete state.imageLoadTimer[info.key];
    } else if (info.type == "add") {
      if (state.imageLoadTimer[info.key]) {
        // 存在原定时器则清除
        clearTimeout(state.imageLoadTimer[info.key]);
      }
      state.imageLoadTimer[info.key] = info.timer;
    }
  },
  // 设置消息宽度
  setMsgWidthObj: function (state, info) {
    state.msgWidthObj[info.key] = info.value;
  },
  // 重置当前会话
  setCancelCurrentSession: function (state, info) {
    // 重置上个定时器
    if (state.currentSession.timer) {
      clearTimeout(state.currentSession.timer);
    }
    state.currentSession.timer = "";
    remote.store.getters.getNim.setCurrSessionState({type: 2});
  },
  // 设置当前会话已读
  setMsgRead: function (state, info) {
    try {
      // 当前会话标记消息已读
      let currentSessionMsgs = state.msgs[info.id];
      if (/p2p/.test(state.currentSession.id) && state.currentSession.timer && state.currentSession.id == info.id && currentSessionMsgs?.length > 0) {
        let msg = currentSessionMsgs[currentSessionMsgs.length - 1];
        if (msg?.target) {
          remote.store.getters.getNim.sendMsgReceipt(msg);
        }
      }
    } catch (e) {
      console.log("setReadErr", e);
    }
  },
  // 子窗口输入框内容
  setChildEditorHtml: function (state, info) {
    state.childEditorHtml = info;
  },
  // 设置反垃圾列表
  setAntispamMap: function (state, info) {
    let idServerMap = {
      updateTime: Date.now()
    };
    info.data.map(item => {
      idServerMap[item] = true;
    })
    state.antispamMap[info.id] = idServerMap;
  },
  // 设置搜索本地记录
  setLocalSearch: function (state, info) {
    // 去除同样的历史记录
    for (let i = 0; i < state.localSearch.length; i++) {
      let item = state.localSearch[i];
      if (info.localValue == item.localValue) {
        state.localSearch.splice(i, 1);
        break;
      }
    }
    if (state.localSearch.length > 10) {
      state.localSearch.length = 10;
    }
    state.localSearch.unshift(info);
    userLocalStorage({key: "localSearchMap", value: state.localSearch}, 1);
  },
  // 设置是否折叠置顶
  setIsFoldTop: function (state, info) {
    state.isFoldTop = info;
  },
  // 设置会话列表tab
  setSessionTab: function (state, info) {
    state.sessionTab = info;
  },
  // 列表分组列表对象
  setSessionTabClassifyItem: function (state, info) {
    state.sessionTabClassifyItem = info;
  },
  // 列表分组显示状态
  setSessionTabClassifyShow: function (state, info) {
    state.sessionTabClassifyShow[info.key] = info.value;
  },
  // 设置滚动会话状态
  setScrollSessionState: function (state, info) {
    state.scrollSessionState = info;
  },
  // 设置当前会话列表
  setCurrSessionList: function (state, info) {
    state.currSessionList = info;
  },
  // 设置是否触发点击事件
  setIsClick: function (state, info) {
    state.isClick = info;
  },
  // 设置全局定时器
  setGlobalTimer: function (state) {
    if (state.globalTimer) {
      clearInterval(state.globalTimer);
    }
    state.globalTimer = setInterval(() => {
      state.emit.globalTimer = Date.now();
      if (isMainWin()) {
        try {
          // 定时写入日志
          state.logger.writeLogFileTimer();
        } catch (e) {}
        // 获取ip和代理信息
        remote.store.dispatch("getUserIp");
        // 设置云信加载进度
        if (state.nimInfo.type == "info" && state.nimInfo.infoType == 0 && state.nimInfo.time && Date.now() - state.nimInfo.time < 5 * 60 * 1000) {
          let progress = state.nimInfo.defaultProgress + parseInt(((Date.now() - state.nimInfo.time) / (0.5 * 60 * 1000)) * (100 - state.nimInfo.defaultProgress));
          state.nimInfo.progress = progress > 99 ? 99 : progress;
        }
        if (remote.Window.get().window?._jjshome_t?._set_event) {
          // 初始化截图埋点
          if (state.jtObj.initKey && !state.jtObj.initLog) {
            state.jtObj.initLog = true;
            setJJSEvent("P54371584", JSON.stringify({
              w: state.userInfo.workerNo,
              k: state.jtObj.initKey,
              s: state.jtObj.initIsRegistered ? "正常" : "冲突",
              t: Date.now(),
            }));
          }
          if (state.userInfo.workerNo) {
            // 初始化信息收集
            if (!state.emit.systemInfo) {
              state.emit.systemInfo = true;
              setJJSEvent("P06328320", JSON.stringify({w: state.userInfo.workerNo, ...state.computerMap, s10: state.computerInfo.macName, ...state.computerInfo.userUUIDMap}));
              // 初始化网关信息收集
              if (state.computerInfo.gatewayMap) {
                let w_gw = [];
                for (let key in state.computerInfo.gatewayMap) {
                  w_gw.push({ip: key, name: state.computerInfo.gatewayMap[key].name, mac: state.computerInfo.gatewayMap[key].mac});
                }
                setJJSEvent("P40427264", JSON.stringify({u0: state.computerMap.u0, w_gw: w_gw}));
              }
            }
            // 运行列表每次启动和每小时上传一次
            if (Date.now() - (state.emit.exeListTime || 0) > 1 * 60 * 60 * 1000) {
              state.emit.exeListTime = Date.now();
              getExeList().then(res => {
                if (res.launchInfo) {
                  state.emit.exeListTime = Date.now();
                  setJJSEvent("P31593216", JSON.stringify({
                    processName: Object.keys(res.launchInfo).toString()
                  }));
                } else {
                  state.emit.exeListTime = 0;
                }
              });
              // 每1小时判断用户缓存
              let storageSize = calcStorageSize();
              // 每24小时判断用户缓存提示，缓存数据大于1g提示清理缓存
              if (storageSize / 1024 / 1024 > 1024 && Date.now() - userLocalStorage({key: "clearStorageTime", value: 0}, 2) > 24 * 60 * 60 * 1000) {
                state.emit.clearStorageTime = Date.now();
                userLocalStorage({key: "clearStorageTime", value: state.emit.clearStorageTime}, 1);
                alert({
                  title: "提示",
                  content: `<div style="text-align: left">
                              <div>缓存内容过大，请及时清理，否则消息可能会出现接收延迟。</div>
                              <div style='color:#E03236;margin-top: 10px;font-size: 12px;line-height: 18px;'>注意：清除缓存后，大概有1-5分钟本地在更新信息。</div>
                              <div style='color:#E03236;font-size: 12px;line-height: 18px;'>若点击下方【暂不处理】，后期可以在乐聊右下角【设置】-【通用设置】中手动清理。</div>
                            </div>`,
                  cancelText: "暂不处理",
                  okText: "清除缓存",
                  done: (type) => {
                    if (type == 1) {
                      loading();
                      emitMsg("msg", {type: "clear", clear: 2});
                      loading().hide();
                    }
                  }
                });
              }
            }

          }
        }
        // 一分钟更新一次乐聊性能日志
        if (Date.now() - (state.emit.timeLogLastTime || 0) > 60 * 1000) {
          state.emit.timeLogLastTime = Date.now();
          console.time("定时性能");
          console.timeEnd("定时性能");
        }
      }
    }, 1000);
  },
  // 设置被替换文件的MD5数据
  setRemoveMD5Obj: function (state, info) {
    if (info.type == "add") {
      state.removeMD5Obj[info.md5] = info;
    } else if (info.type == "del") {
      delete state.removeMD5Obj[info.md5];
    }
  },
  // 设置代理线路
  setJjsProxy: function (state, info) {
    if (info) {
      state.jjsProxy = info;
    }
    state.jjsProxy.key = (state.jjsProxy.key || 0) + 1;
  },
  // 获取UAT密码
  getUAT: function (state, info) {
    getUATPasswordApi().then(res => {
      console.log(111111, res)
    });
  },
  // 获取消息记录code
  getRecordCode: function (state, info) {
    messageToCodeApi(info).then(res => {
      console.log(111111, res)
    });
  },
  // 获取消息记录code
  getMergeMessageCode: function (state, info) {
    mergeMessageToCodeApi(info).then(res => {
      console.log(111111, res)
    });
  },
  // 获取微信预警token
  sendWxMsg: function (state, info) {
    getWxMsgTokenApi().then(res => {
      if (res.access_token) {
        let workerNo = state.userInfo.workerNo || state.tempUserInfo.workerNo || "";
        let name = state.userInfo.name || state.tempUserInfo.tempName || "";
        sendWxMsgApi({
          "token": res.access_token,
          "toparty": "23",
          "agentid": "1",
          "safe": "0",
          "msgtype": "text",
          "text": {
            "content": `工号：${workerNo}、${name}直连线路及备用线路全部无响应，请及时关注（IP：${state.ipInfo.ip}、pc设备码：${state.computerInfo.macAddress}）`
          }
        });
      }
    });
  },
  // 设置收藏/话术列表
  setCollectMap: function (state, info) {
    if (info.type == "collect") {
      state.collectMap.list = info.list;
      state.collectMap.time = Date.now() + state.diffTime;
    } else {
      state.collectMap.storyList = info.list;
      state.collectMap.storyTime = Date.now() + state.diffTime;
    }
  },
  // 设置云信数据
  setNimInfo: function (state, info) {
    if (info.type != state.nimInfo.type) {
      state.nimInfo.type = info.type;
    }
    if (info.infoType != state.nimInfo.infoType) {
      state.nimInfo.infoType = info.infoType;
    }
    if (info.defaultProgress) {
      state.nimInfo.defaultProgress = info.defaultProgress;
    }
    if (info.progress) {
      state.nimInfo.progress = info.progress;
    }
    if (info.time != null) {
      state.nimInfo.time = info.time;
    }
  },
  // 讨论组搜索下拉数据
  setGroupSearchInfo: function (state, info) {
    state.groupSearchInfo = info;
  },
  // 设置ai对象
  setAiObj: function (state, info) {
    let aiObj = deepClone(state.aiObj);
    if (info.switchApp) {
      delete info.switchApp;
      info.currentApp = deepClone(info.currentApp);
      if (!info.notSwitchReq) {
        newTopicsApi({aiAccid: info.sendTo});
      }
      // 直接发送文件/乐文档/聊天/切换gpt4不提示
      if (!info.notTip) {
        let content = {type: "ai-msg", msgs: []};
        if (info.currentApp.prologue) {
          content.msgs = [{type: "text", text: info.currentApp.prologue}];
        }
        if (info.currentApp.id == 3) {
          // 快速总结
          content.msgs = [
            {type: "text", text: `把文档、链接、长篇文字、聊天记录发给我，\n我可以迅速为你进行总结。`},
            {type: "intr", text: `支持您从本地上传以下格式\ndocx、PDF、xlsx、Markdown、txt、json\n单个文件不得超过10m`},
            {type: "link", text: `[点击发送]`, isWrap: false, linkType: "file", name: "房地产广告发布规定.pdf", size: 90676, ext: "pdf", url: "https://nim-nosdn.netease.im/NDEzMTEyNA==/bmltYV8zMDY5MTY3Mzg0Ml8xNzA1OTIzMzcyNDcxX2NhNzRjYmQ5LWYwOTYtNDFmZS04YmIyLTgxYjAzMWFkNDBmYw==?createTime=1705923373401"},
            {type: "text", text: ` 功能示例，快速总结《房地产广告发布规定.pdf》`},
          ];
        } else if (info.currentApp.appType == 2 && info.currentApp.hasDiyJson) {
          // 写作应用
          content.msgs.push({type: "text", text: "点击下方按钮填入需求，让我创作出更精确的结果。", bold: 1, marginTop: info.currentApp.prologue ? 10 : ""});
          content.msgs.push({type: "button", text: "填写创作需求", marginTop: 10, pc: {type: "custom", detailType: "aiApp", custom: {appId: info.currentApp.id}}});
        }
        if (info.sendTo == state.aiObj.workerNo && info.currentApp.isDefault) {
          // 智能助理是默认对话应用发送消息卡片
          this.commit("sendAiMsgCard", {content: state.aiObj.cardCustom, tip: "新建话题", sendTo: info.sendTo});
        } else {
          this.commit("sendAiMsgCard", {content: content, tip: "新建话题", sendTo: info.sendTo});
        }
        state.emit.sendAiMsgCardTime = Date.now();
      }
    }
    for (let key in info) {
      aiObj[key] = info[key];
      if (aiObj?.cardCustom?.data) {
        if (key == "callName") {
          aiObj.cardCustom.data.title = "你好，" + info[key];
        } else if (key == "prologue") {
          aiObj.cardCustom.data.intr = info[key];
        }
      }
    }
    state.aiObj = aiObj;
    // 存在智能助理子窗口
    let chatChild = getChildWin("chat-p2p-" + aiObj.workerNo);
    if (chatChild) {
      chatChild.window.store.state.aiObj = aiObj;
    }
  },
  sendAiMsgCard: function (state, info) {
    // 触发设置开场白卡片
    let sessionId = "p2p-" + info.sendTo;
    let time = Date.now() + state.diffTime;
    if (info.tip) {
      // 发送提示
      remote.store.dispatch("setMsgs", {
        id: sessionId, msgs: [{
          sessionId: sessionId,
          scene: "p2p",
          to: state.userInfo.workerNo,
          from: info.sendTo,
          type: "tip",
          status: "success",
          time: time,
          tip: info.tip,
          idServer: UUID.generate(),
          forbidMsg: true
        }]
      });
    }
    if (info.content?.msgs?.length > 0 || info.content?.data) {
      remote.store.dispatch("setMsgs", {
        id: sessionId, msgs: [{
          sessionId: sessionId,
          scene: "p2p",
          to: state.userInfo.workerNo,
          from: info.sendTo,
          type: "custom",
          status: "success",
          time: info.time || time + 1,
          content: info.content,
          idServer: UUID.generate(),
          forbidMsg: true
        }]
      });
    }
    this.commit("setUpdateSessionId", sessionId);
  },
  // 设置sse消息
  setSseMsgMap: function (state, info) {
    // 如果是子窗口则通知
    if (info.sseTempMsg?.sessionId) {
      let chatChild = getChildWin("chat-" + info.sseTempMsg.sessionId);
      if (info.delete) {
        // 删除
        delete state.sseMsgMap[info.sseTempMsg.idServer];
        delete state.sseTempMsgMap[info.sseTempMsg.idServer];
        delete state.sseDisableSendMap[info.sseTempMsg.sessionId];
        if (chatChild) {
          delete chatChild.window.store.state.sseMsgMap[info.sseTempMsg.idServer];
          delete chatChild.window.store.state.sseTempMsgMap[info.sseTempMsg.idServer];
          delete chatChild.window.store.state.sseDisableSendMap[info.sseTempMsg.sessionId];
        }
      } else {
        state.sseMsgMap[info.sseTempMsg.idServer] = info.sseTempMsg;
        // 私聊禁止发送状态
        if (info.sseTempMsg.scene == "p2p") {
          state.sseDisableSendMap[info.sseTempMsg.sessionId] = true;
          if (info.sseTempMsg.stopSseFlag) {
            delete state.sseDisableSendMap[info.sseTempMsg.sessionId];
          }
        }
        if (chatChild) {
          chatChild.window.store.state.sseMsgMap[info.sseTempMsg.idServer] = info.sseTempMsg;
          // 私聊禁止发送状态
          if (info.sseTempMsg.scene == "p2p") {
            chatChild.window.store.state.sseDisableSendMap[info.sseTempMsg.sessionId] = true;
            if (info.sseTempMsg.stopSseFlag) {
              delete chatChild.window.store.state.sseDisableSendMap[info.sseTempMsg.sessionId];
            }
          }
        }
      }
    }
  },
  // 设置fcw在线状态
  setFcwOnlineMap: function (state, info) {
    if (info.type == "del") {
      delete state.fcwOnlineMap[info.key];
    } else {
      state.fcwOnlineMap[info.key] = info.value;
    }
  },
  // 初始化埋点
  loadTjs: function (state, info) {
    if (state.computerInfo?.macAddress && !info.window._jjshome_t) {
      let script = info.window.document.createElement("script");
      let url = "https://logtest.leyoujia.com/bigdata/js/t.min.js";
      if (config.env == "online") {
        url = "https://cdn.leyoujia.com/bigdata/js/t.min.js";
      }
      script.src = `${url}?worker-id=${state.userInfo.workerId || ""}&mac=${state.computerInfo.macAddress}`;
      info.window.document.getElementsByTagName("head")[0].appendChild(script);
    }
  },
  // 加载水印
  setWaterMark: function (state, info) {
    let userInfo = state.userInfo;
    if (userInfo?.workerNo) {
      let scriptSrc = 'https://front.leyoujia.com/js/waterMark.js?v=18&waterMarkType=2&waterMarkId=' + userInfo.workerNo + '&token=' + userInfo.token +
        '&empNo=' + userInfo.workerNo + '&empNumber=' + userInfo.workerId + '&appName=pc-im&OpId=' + userInfo.workerId + '&Authorization=' + userInfo.authorization;
      let script = info.window.document.createElement("script");
      script.type = "text/javascript";
      script.id = "waterData";
      script.src = scriptSrc;
      info.window.document.getElementsByTagName('head')[0].appendChild(script);
    }
  },
  // 初始化ai临时appId
  setAiTempIdMap: function (state, info) {
    state.aiTempIdMap[info.key] = info.value;
    let childWin = getChildWin("chat-" + info.key);
    if (childWin && isMainWin()) {
      childWin.window.store.commit("setAiTempIdMap", info);
    }
  },
}