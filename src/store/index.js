import {createStore} from "vuex";
import state from "./state"
import mutations from "./mutations"
import getters from "./getters"
import action from "./action"

let store = createStore({
  state: state,
  mutations: mutations,
  actions: action,
  getters: getters,
  modules: {}
})
window.store = store;

if (!remote.store) {
  remote.store = store;
  remote.utils = require("@utils");
  remote.ui = require("@comp/ui");
}

export default store;