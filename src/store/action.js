let path = remote.require("path");
let fs = remote.require("fs");
let http = remote.require("http");
let cp = remote.require("child_process");
import config from "/config.js"
import {request} from "@utils/net/request";
import {
  MD5, loadCache, selFolderDialog, emitMsg, getMsgCustom, getPushContent, dealInputMessage, deepClone, getXLMatchedStr, openLocalFile,
  openLocalFolder, hasContent, isSubOrSer, isNotificationRole, dataUrlToBlob, dateFormat, openViewer, getBase64Image, saveBase64Local, showMenu,
  getStrParam, getLink, calcWH, getLocalFile, uploadToQiNiu, getLogFileInfo, dealMsgCenterContent, linkFormat, getFileCachedPath, compareVersion,
  dealMsgField, getInputMessage, transTime, getImageData, getImageQualityUrl, downloadFile, getChildWin, getBounding, isMainWin, setJJSEvent,
  isHalfYearAgo, dispatchEvent, encrypt, decrypt, getAppPath, debounce, getMsgTrim, isFcwList, getIconvDecode, getFreeLoginUrl, encryptRsa,
  getAllComputerInfo, userLocalStorage, signRsa, setUserBaseInfo, isRegistered, enCodeJJS, throttle, unlinkDir, strToHtml, setLocalStorage,
  trimArray, userCache, strToImg, getSessionType, getFileExt, getQuoteMsg, getBase64Ext, getVpnFlag, heicToJpg,
} from "@utils"
import {alert, loading, toast} from "@comp/ui";
import {
  searchSubAndSerApi, queryAllGroupSettingApi, getAllSpecialDataApi, getFindPersonsDataApi, getUsersApi, queryWorkBenchApi, searchGroupTypeListApi,
  getAccountInfoApi, getUserBatchApi, getAppCardByAccIdsApi, leaveTeamApi, dismissGroupApi, queryOftenEmojiApi, modifySettingsApi, answerXiaoLeApi, getKeyTextApi,
  getIpApi1, getIpApi2, getIpApi3, getIpApi4, getLoginSecretApi, addPurDocListApi, detailsDocApi, queryCloudMessageApi, fcwRemarkApi,
  editCollectContentApi, queryAllSettingApi, getLoggerTokenApi, uploadFilePathApi, smartRemindApi, msgTipApi, sensitiveWordsApi, querySensitiveApi, getVersionApi,
  obsStoreEndTimeApi, zxpApi, queryPurviewApi, queryCollectClassifyApi, queryCollectStoryClassifyApi, editCollectStoryContentApi, getProxyApi, queryEsfCardFieldsApi,
  getTimeApi, searchCustomerLinkApi, fcwBindApi, fcwRulesApi, addTagPowerApi, initiateAskApi, getImSensitivesApi, closeAnswerApi, getButtonObjApi, getForwardGroupTypeApi,
  speechToTextApi, checkRemoteApi, getAppAndAccGroupInfoApi, getTeamAiInfoApi, createTeamAiInfoApi, havePermissionApi, getAppPurApi, imMonadInfoByIdsApi, getProxyToolApi,
  aiSparringApi, findSubordinateApi, doCheckAuthApi, getMenuListApi, getNhrApolloApi,
} from "@utils/net/api"
import {initEditor} from "@utils/sdk/imeditorSDK";
import {openJt} from "@utils/sdk/jtSDK";
import {initScheduleModal} from "@comp/schedule/scheduleModal";
import {dataURLToImage, fileToDataURL} from '@utils/imgCompress'
import UUID from "@utils/uuid.js";

let uniqueSignCount = 0;// 批量发送文件时间偶尔会一致，新增计数
let uniqueSignInput = 0;// 批量粘贴图片偶尔会一致，新增计数
let jtCount = 1;// 截图显示窗口偏移

export default {
  // 请求服务器数据获取依赖数据
  setServerConfig: function ({state, commit, dispatch, getters}, info) {
    // 服务号和订阅号信息
    if (!state.serverFlagMap["setSubAndSer"]) {
      dispatch("setSubAndSer");
    }
    // 配置信息
    if (!state.serverFlagMap["setAllSetting"]) {
      dispatch("setAllSetting");
    }
    // 获取服务器所有分组
    if (!state.serverFlagMap["setGroupSetting"]) {
      dispatch("setGroupSetting");
    }
    // 特别关心
    if (!state.serverFlagMap["setAllConcernMap"]) {
      dispatch("setAllConcernMap");
    }
    // 找对人
    if (!state.serverFlagMap["setFindPersons"]) {
      dispatch("setFindPersons");
    }
    // 通讯录
    if (!state.serverFlagMap["setFriends"]) {
      dispatch("setFriends");
    }
    // 左侧和顶部tab
    if (!state.serverFlagMap["setWorkBench"]) {
      dispatch("setWorkBench");
    }
    // 可创建讨论组类型
    if (!state.serverFlagMap["setGroupTypeList"]) {
      dispatch("setGroupTypeList");
    }
    // 智能助理基础信息
    if (!state.serverFlagMap["setAiObjInfo"]) {
      dispatch("setAiObjInfo", {init: true});
    }
    // 更新转发指定人/删除历史记录权限
    if (!state.serverFlagMap["getButtonObj"]) {
      dispatch("getButtonObj");
    }
    // 电脑类型
    if (!state.serverFlagMap["doCheckAuth"]) {
      dispatch("doCheckAuth", {init: true});
    }
    // 应用列表
    if (!state.serverFlagMap["getMenuList"] && state.deviceObj.isCompany != null) {
      dispatch("getMenuList");
    }
    // 直接下属
    if (!state.serverFlagMap["setFindSubordinate"]) {
      dispatch("setFindSubordinate");
    }
    // 是否显示同事分组
    if (!state.serverFlagMap["collDutyType"]) {
      dispatch("collDutyType");
    }
  },
  // 设置全部订阅号和服务号
  setSubAndSer: function ({state, commit, dispatch}, info) {
    let that = this;
    state.serverFlagMap["setSubAndSer"] = "loading";
    searchSubAndSerApi({
      msgBody: JSON.stringify({
        imei: state.baseComputerInfo.hostName,
        accountName: "",
        currPage: 1,
        pageSize: 100
      })
    }).then(res => {
      if (res && res.success) {
        state.serverFlagMap["setSubAndSer"] = true;
      } else {
        state.serverFlagMap["setSubAndSer"] = "";
      }
      if (res && res.data && res.data && res.data.data && res.data.data.length > 0) {
        // 设置全部订阅号和服务号数据
        for (let i = 0; i < res.data.data.length; i++) {
          let item = res.data.data[i];
          item.account = item.aNumber;
          item.avatar = item.headPic;
          state.allSubAndSer[item.aNumber] = item;
        }

        // 设置公众号/订阅号信息
        commit("setPersonsInfo", Object.values(state.allSubAndSer))
        // 重新渲染会话
        commit("setSessions", Object.values(state.sessions));
      }
    })
  },
  // 设置全部配置信息
  setAllSetting: function ({state, commit, dispatch}, info) {
    state.serverFlagMap["setAllSetting"] = "loading";
    queryAllSettingApi({
      msgBody: JSON.stringify({
        empNo: state.userInfo.workerNo,
      })
    }).then(res => {
      try {
        if (res?.data?.data?.length > 0) {
          // 设置全部订阅号和服务号数据
          for (let i = 0; i < res.data.data.length; i++) {
            let item = res.data.data[i];
            // 设置截图、发送快捷键、乐聊风格、气泡通知、窗口大小分组类型、分组排序到本地缓存
            switch (item.key) {
              case config.settings.type4:
              case config.settings.type6:
              case config.settings.type7:
              case config.settings.type11:
              case config.settings.type12:
              case config.settings.type13:
              case config.settings.type16:
              case config.settings.type17:
                if (item.key == config.settings.type6) {
                  // 切换截图快捷键
                  commit("setShortcut", {key: "jt", value: item.value});
                } else if (item.key == config.settings.type7) {
                  // 切换气泡通知
                  commit("setNotifyWin", state.settings[config.settings.type7] != 2 ? "add" : "del");
                } else if (item.key == config.settings.type12) {
                  // 窗口大小是json字符串
                  try {
                    item.value = JSON.parse(item.value);
                  } catch (e) {}
                  state.settings[item.key] = item.value;
                  for (let key in state.settings[item.key]) {
                    state.windowSizeType[key] = state.settings[item.key][key];
                  }
                  commit("setWindowSizeInfo", {type: 2, initWinSize: true, initMin: true, currentWindow: remote.store.getters.getCurrentWindow().cWindow.id});
                } else if (item.key == config.settings.type16) {
                  // 分组列表是字符串
                  try {
                    item.value = JSON.parse(item.value);
                  } catch (e) {}
                  state.settings[item.key] = item.value;
                }
                // 设置本地缓存
                userLocalStorage(item, 1);
                break;
            }
            let valueObj = {};
            if (item.key && item.value) {
              //type为9的数据是json字符串数据
              if (item.type === 9) {
                try {
                  let valueList = JSON.parse(item.value);
                  if (valueList) {
                    for (let j = 0; j < valueList.length; j++) {
                      if (valueList[j] && valueList[j].accid) {
                        valueObj[valueList[j].accid] = valueList[j];
                      }
                    }
                    // 设置默认配置信息
                    state.settings[item.key] = valueObj;
                  }
                } catch (err) {
                  console.log("setSettingErr:", err);
                }
              } else if (item.key == config.settings.type1 || item.key == config.settings.type2 || item.key == config.settings.type3 || item.key == config.settings.type15) {
                // 设置接收但不提醒/群助手/置顶会话/@全员消息不提醒
                let valueList = item.value.split(",");
                for (let j = 0; j < valueList.length; j++) {
                  valueObj[valueList[j]] = valueList[j];
                  state.settings[item.key] = valueObj;
                }
              } else {
                state.settings[item.key] = item.value;
              }
            }
          }
          // 重新渲染会话
          commit("setSessions", Object.values(state.sessions));
        }
      } catch (e) {
        console.log("setAllSettingErr", e);
      }
      if (res && res.success) {
        state.serverFlagMap["setAllSetting"] = true;
        // 冲突子窗口弹窗
        let localShortcutConflictInfo = userLocalStorage({key: "shortcutConflict", value: ""}, 2);
        let isRegisteredShortcut = isRegistered(state.settings[config.settings.type6]);
        if (isMainWin() && !isRegisteredShortcut && !(localShortcutConflictInfo?.show && localShortcutConflictInfo.version == state.config.version)) {
          emitMsg("msg", {
            type: "window", newWin: 1, name: "shortcutConflict", width: 356, height: 186, transparent: true, changePath: true,
            path: (process.env.NODE_ENV != "development" ? `/shortcutConflict.html` : `http://localhost:8888/shortcutConflict.html`),
          });
        }
        state.jtObj.initKey = state.settings[config.settings.type6] || config.shortcut.jt1;
        state.jtObj.initIsRegistered = isRegisteredShortcut;
      } else {
        state.serverFlagMap["setAllSetting"] = "";
      }
    });
  },
  // 更新全部配置
  updateAllSetting: async function ({state, commit, dispatch}, info) {
    let res = await queryAllSettingApi({
      msgBody: JSON.stringify({
        empNo: state.userInfo.workerNo,
      })
    });
    if (res?.data?.data?.length > 0) {
      for (let i = 0; i < res.data.data.length; i++) {
        let item = res.data.data[i];
        switch (item.key) {
          case config.settings.type16:
            // 分组列表是字符串
            try {
              item.value = JSON.parse(item.value);
            } catch (e) {}
            state.settings[item.key] = item.value;
            // 设置本地缓存
            userLocalStorage(item, 1);
            break;
        }
      }
    }
  },
  // 设置全部分组
  setGroupSetting: function ({state, commit, dispatch}, info) {
    state.serverFlagMap["setGroupSetting"] = "loading";
    queryAllGroupSettingApi({
      msgBody: JSON.stringify({
        workerNo: state.userInfo.workerNo,
        sortOrder: "asc",
      })
    }).then(res => {
      if (res && res.success) {
        state.serverFlagMap["setGroupSetting"] = true;
        state.groupSettings = res?.data?.data || [];
        state.updateSessionsTime = Date.now();
      } else {
        state.serverFlagMap["setGroupSetting"] = "";
      }
    });
  },
  // 设置全部特别关心列表
  setAllConcernMap: async function ({state, commit, dispatch}, info) {
    state.serverFlagMap["setAllConcernMap"] = "loading";
    let res = await getAllSpecialDataApi({
      msgBody: JSON.stringify({
        accid: state.userInfo.workerNo,
      }),
      serviceCode: "40002",
      methodCode: "getSpecialConcernData",
    });
    if (res && res.success) {
      state.serverFlagMap["setAllConcernMap"] = true;
      let list = res?.data?.data || [];
      // 服务器特别关心/重要标记列表
      let serverConcernMap = {};
      let serverImportantMarkMap = {};
      list.map(item => {
        let itemId = (item.type == 1 ? "p2p-" : "team-") + item.faccid;
        if (item.specialType == 1) {
          serverConcernMap[itemId] = true;
        } else if (item.specialType == 2) {
          serverImportantMarkMap[itemId] = true;
        }
      });
      // 设置本地缓存
      state.concernMap = serverConcernMap;
      state.importantMarkMap = serverImportantMarkMap;
      userLocalStorage({key: "concernMap", value: serverConcernMap}, 1);
      userLocalStorage({key: "importantMarkMap", value: serverImportantMarkMap}, 1);
      state.updateSessionsTime = Date.now();
    } else {
      state.serverFlagMap["setAllConcernMap"] = "";
    }
  },
  // 设置全部找对人办对事
  setFindPersons: async function ({state, commit, dispatch}, info) {
    let res = await getFindPersonsDataApi({
      msgBody: JSON.stringify({
        imei: state.baseComputerInfo.hostName,
        nowWorkerNo: state.userInfo.workerNo,
      })
    });
    if (res && res.success) {
      state.serverFlagMap["setFindPersons"] = true;
    } else {
      state.serverFlagMap["setFindPersons"] = "";
    }
    if (res?.data?.data) {
      state.findPersons = res.data.data;
      // 获取人员信息
      let workerMap = {};
      let information = state.findPersons.information;
      if (information && information.length > 0) {
        for (let i = 0; i < information.length; i++) {
          let groupList = information[i].groupList;
          if (groupList && groupList.length > 0) {
            for (let j = 0; j < groupList.length; j++) {
              let spawVoList = groupList[j].spawVoList;
              if (spawVoList && spawVoList.length > 0) {
                for (let k = 0; k < spawVoList.length; k++) {
                  workerMap[spawVoList[k].workerNo] = spawVoList[k].workerNo;
                }
              }
            }
          }
        }
        if (state.userInfo.empType == 2) {
          // 加盟默认当前公司
          state.findPersons.information = state.findPersons.information.sort(item => {return item.compayId == state.userInfo.companyId ? -1 : 1});
        } else {
          // 直营默认乐有家控股
          state.findPersons.information = state.findPersons.information.sort(item => {return item.compayId == "330007" ? -1 : 1});
        }
      }
      if (Object.keys(workerMap).length > 0) {
        commit("setPersons", {workerMap: workerMap});
      }
    }
  },
  // 设置通讯录好友
  setFriends: async function ({state, commit, dispatch}, info) {
    state.serverFlagMap["setFriends"] = "loading";
    let res = await getUsersApi({
      msgBody: JSON.stringify({
        workerId: enCodeJJS(state.userInfo.workerId),
      })
    });
    if (res && res.success) {
      state.serverFlagMap["setFriends"] = true;
    } else {
      state.serverFlagMap["setFriends"] = "";
    }
    if (Array.isArray(res?.data?.data)) {
      let list = res.data.data;
      // 获取人员信息
      let workerMap = {};
      for (let i = 0; i < list.length; i++) {
        workerMap[list[i].workerNo] = list[i].workerNo;
      }
      if (Object.keys(workerMap).length > 0) {
        commit("setPersons", {
          workerMap: workerMap,
          done: () => {
            list.map(item => {
              setUserBaseInfo(item);
            });
            state.friends = list;
            commit("setMailList");
          }
        });
      }
    }
  },
  // 获取左侧和顶部tab
  setWorkBench: async function ({state, commit, dispatch}, info) {
    if (info) {
      // 工作台操作更新
      state.tabMap.leftList = info.leftList;
      state.tabMap.topList = info.topList;
    } else {
      state.serverFlagMap["setWorkBench"] = "loading";
      let res = await queryWorkBenchApi({
        msgBody: JSON.stringify({
          empNumber: state.userInfo.workerId,
          positionList: [2, 3],
        })
      });
      if (res && res.success) {
        state.serverFlagMap["setWorkBench"] = true;
      } else {
        state.serverFlagMap["setWorkBench"] = "";
      }
      if (res && res.data && res.data.data) {
        let leftList = [];
        let topList = [];
        let list = res.data.data;
        list.map(item => {
          switch (String(item.position)) {
            case "2":
              topList.push(item);
              break;
            case "3":
              leftList.push(item);
              break;
          }
        });
        state.tabMap.leftList = leftList;
        state.tabMap.topList = topList;
      }
    }
  },
  // 获取可创建讨论组列表
  setGroupTypeList: async function ({state, commit, dispatch}, info) {
    state.serverFlagMap["setGroupTypeList"] = "loading";
    let res = await searchGroupTypeListApi();
    if (res?.success) {
      state.serverFlagMap["setGroupTypeList"] = true;
    } else {
      state.serverFlagMap["setGroupTypeList"] = "";
    }
    if (res?.data?.typeList) {
      state.groupTypeList = res.data.typeList;
      let groupTypeShowMap = {};
      (res.data.typeList || []).map((item, key) => {
        if (item.value != 0) {
          groupTypeShowMap[item.value] = deepClone(item);
        }
      });
      state.groupTypeShowMap = groupTypeShowMap;
      state.updateSessionsTime = Date.now();
    }
  },
  // 获取智能助理基础信息
  setAiObjInfo: function ({state, commit, dispatch}, info = {}) {
    if (info.init) {
      state.serverFlagMap["setAiObjInfo"] = "loading";
    }
    // 获取本地ai对象数据渲染
    let localAiObj = userLocalStorage({key: "aiObj", value: {}}, 2);
    // 设置ai对象
    if (localAiObj?.workerNo) {
      remote.store.commit("setAiObj", localAiObj);
    }
    return new Promise(async resolve => {
      let res = await getAccountInfoApi({
        empNumber: state.userInfo.workerId
      });
      if (info.init) {
        if (res?.success) {
          state.serverFlagMap["setAiObjInfo"] = true;
        } else {
          state.serverFlagMap["setAiObjInfo"] = "";
        }
      }
      if (res?.data?.accId) {
        let appList = [];
        let currentApp = {};
        // 显示的应用列表
        if (res.data.regularApps) {
          res.data.regularApps.map(item => {
            appList.push({
              ...item,
              title: item.name,
              intr: item.briefIntr,
            })
          });
        }
        let callName = (res.data.callName || (state.userInfo.workerId == "********" ? "林董" : state.userInfo.workerName));
        // 设置的ai对象
        let aiObj = {
          workerNo: res.data.accId,
          name: res.data.astName,
          callName: callName,
          briefIntr: res.data.briefIntr,
          prologue: res.data.prologue,
          headImg: res.data.headImg,
          commonAppList: res.data.commonApps || [],
          detailAppId: "-1",
          managerName: res.data.managerName,
          superiorName: res.data.superiorName,
          cardCustom: {
            type: "ai-card", data: {
              title: "你好，" + callName,
              intr: res.data.prologue,
              appList: appList,
            }
          }
        };
        // 问题对象
        if (res.data.guidedQuestion) {
          aiObj.cardCustom.data.questionObj = res.data.guidedQuestion;
        }
        // 新问题对象
        if (res.data.commonQA) {
          aiObj.cardCustom.data.msgList = res.data.commonQA;
        }
        // 跳转创建应用
        if (res.data.createApp) {
          aiObj.cardCustom.data.addAppObj = res.data.createApp;
        }
        // 查询设置当前选中应用
        let localDefaultAppFlag = false;
        let defaultCurrentApp = userLocalStorage({key: "aiObjDefaultCurrentApp", value: {}}, 2);
        if (aiObj.commonAppList.length > 0) {
          // 存在通用应用 1智问智答 2找对人办对事 3快速总结 4普通对话
          let appIndex = aiObj.commonAppList.findIndex(item => {return item.isDefault});
          currentApp = deepClone(aiObj.commonAppList[appIndex == -1 ? 0 : appIndex]);
          aiObj.detailAppId = currentApp.id;
          if (currentApp.knowBases?.length > 0) {
            let childIndex = currentApp.knowBases.findIndex(item => {return item.isDefault});
            currentApp.childObj = deepClone(currentApp.knowBases[childIndex == -1 ? 0 : appIndex]);
            // 本地存在选中数据库缓存则选中
            if (defaultCurrentApp?.id && defaultCurrentApp.childObj?.id) {
              let localChildIndex = currentApp.knowBases.findIndex(item => {return item.id == defaultCurrentApp.childObj.id && item.name == defaultCurrentApp.childObj.name});
              if (localChildIndex > -1) {
                currentApp.childObj = deepClone(defaultCurrentApp.childObj);
                aiObj.defaultCurrentApp = defaultCurrentApp;
                localDefaultAppFlag = true;
              }
            } else {
              aiObj.defaultCurrentApp = deepClone(currentApp);
            }
          }
        }
        // 初始化当前选中应用
        if (info.init) {
          aiObj.currentApp = currentApp;
        }
        // 本地子项缓存/服务器子项被删除变更
        if (!localDefaultAppFlag) {
          userLocalStorage({key: "aiObjDefaultCurrentApp", value: aiObj.defaultCurrentApp}, 1);
        }
        // 设置ai对象
        remote.store.commit("setAiObj", aiObj);
        // 设置本地缓存
        userLocalStorage({key: "aiObj", value: aiObj}, 1);
        let datas = [setUserBaseInfo({
          workerNo: aiObj.workerNo,
          name: aiObj.name,
          selfIntro: aiObj.selfIntro,
          prologue: aiObj.prologue,
          headImg: aiObj.headImg,
          briefIntr: aiObj.briefIntr,
          managerName: aiObj.managerName,
          superiorName: aiObj.superiorName,
        })];
        if (info.datas) {
          // 半小时更新一次本地数据库
          resolve(datas);
        } else if (info.init) {
          // 初始化更新本地数据库
          dispatch("updatePersons", {account: aiObj.workerNo, datas: datas});
        } else {
          resolve(state.aiObj);
        }
      } else {
        resolve();
      }
    })
  },
  // 获取直接下属
  setFindSubordinate: async function ({state, commit, dispatch, getters}, info) {
    // 直接下属
    if (!state.serverFlagMap["setFindSubordinate"]) {
      state.serverFlagMap["setFindSubordinate"] = "loading";
      let res = await findSubordinateApi();
      if (res?.success) {
        state.serverFlagMap["setFindSubordinate"] = true;
      } else {
        state.serverFlagMap["setFindSubordinate"] = "";
      }
      if (res?.success) {
        let subordinateMap = {};
        (res.data?.data || []).map(item => {
          subordinateMap[item.empNo] = item;
        });
        state.subordinateMap = subordinateMap;
        state.updateSessionsTime = Date.now();
      }
    }
    return state.subordinateMap;
  },
  // 获取同事分组是否显示
  collDutyType: async function ({state, commit, dispatch, getters}, info) {
    // 直接下属
    if (!state.serverFlagMap["collDutyType"]) {
      state.serverFlagMap["collDutyType"] = "loading";
      let res = await getNhrApolloApi({
        msgBody: JSON.stringify({
          key: "collDutyType"
        })
      });
      if (res?.success) {
        state.serverFlagMap["collDutyType"] = true;
      } else {
        state.serverFlagMap["collDutyType"] = "";
      }
      if (res?.success) {
        state.collDutyType = res.data?.data?.collDutyType || false;
        state.updateSessionsTime = Date.now();
      }
    }
    return state.collDutyType;
  },
  // 更新用户信息
  updatePersons: async function ({state, commit, dispatch}, info) {
    let res, datas;
    if (info.datas) {
      datas = info.datas;
    } else {
      if (new RegExp(config.ai).test(info.account)) {
        // 智能助理同时更新接口数据
        if (info.account == state.aiObj.workerNo) {
          datas = await dispatch("setAiObjInfo", {datas: true});
        } else {
          res = await getAppCardByAccIdsApi({
            accIds: [info.account],
          });
          if (res?.data?.length > 0) {
            // 设置基础信息
            datas = res.data.map((item) => {
              return setUserBaseInfo(item);
            });
          }
        }
      } else {
        res = await getUserBatchApi({
          msgBody: JSON.stringify({
            nowWorkerNo: state.userInfo.workerNo,
            workerNo: JSON.stringify([info.account]),
          })
        });
        if (res?.data?.data?.length > 0) {
          // 设置基础信息
          datas = res.data.data.map((item) => {
            return setUserBaseInfo(item);
          });
        }
      }
    }
    if (datas?.length > 0) {
      // 更新本地数据库
      state.userDB.primaryKeys([info.account]).then(list => {
        if (list.length == 0) {
          state.userDB.put(datas[0]);
        } else {
          state.userDB.update(list[0], datas[0]);
        }
        remote.store.commit("setUpdateSessionId", `p2p-${info.account}`);
      });
      // 设置本地缓存
      commit("setPersonsInfo", datas);
      // 执行回调
      if (info.done) {
        info.done();
      }
    }
  },
  // 设置会话消息
  setMsgs: async function ({state, commit, dispatch, getters}, info) {
    // 设置子窗口消息
    let childWin = getChildWin("chat-" + info.id);
    if (childWin && isMainWin()) {
      childWin.window.store.dispatch("setMsgs", {child: true, id: info.id, msgs: info.msgs, type: info.type});
    }
    // 清空历史记录
    if (info.type == "clear") {
      state.msgs[info.id] = [];
      // 删除会话最后一条消息
      if (state.sessions[info.id]) {
        delete state.sessions[info.id].lastMsg;
        delete state.sessions[info.id].showLastMsg;
      }
      return;
    }
    let workerMap = {};
    if (!state.msgs[info.id]) {
      state.msgs[info.id] = [];
    }
    // 删除消息-toDel删除发送状态-toDelLocal-删除发送成功状态
    if (info.msgs && info.msgs[0] && (info.msgs[0].toDel || info.msgs[0].toDelLocal)) {
      let delIndex = -1;
      if (info.msgs[0].toDel) {
        delIndex = state.msgs[info.id].findIndex(item => {
          return info.msgs[0].uniqueSign == item.uniqueSign
        });
      } else if (info.msgs[0].toDelLocal) {
        delIndex = state.msgs[info.id].findIndex(item => {
          return info.msgs[0].idServer == item.idServer
        });
      }
      if (delIndex != -1) {
        state.msgs[info.id].splice(delIndex, 1);
      }
      return;
    }
    let msgMap = {};
    for (let i = 0; i < info.msgs.length; i++) {
      let item = info.msgs[i];
      item.onmsg = info.onmsg;
      item.asyncCount = info.asyncCount;
      try {
        // 设置消息会话数
        if (!msgMap[item.sessionId]) {
          msgMap[item.sessionId] = [];
        }
        msgMap[item.sessionId].push(item);
        await dispatch("setMsgField", {item: item, onmsg: info.onmsg});
        if (item.content) {
          // 抖动消息-在线消息且只有1v1抖动
          if (info.onmsg == 1 && item.scene == "p2p" && item.content.type == "shake" && state.settings[config.settings.type8] != 1) {
            dispatch("setCurrentSession", {id: item.sessionId});
            commit("setEmit", {type: "scroll", value: "bottom"});
            dispatch("shakeWindow", {id: info.id});
          }
          // 在线离线漫游消息重置ai应用标识
          if (info.onmsg && item.content.reset == 1 && state.aiTempIdMap[item.sessionId]?.id) {
            delete state.aiTempIdMap[item.sessionId].id;
          }
        }
        // 收到消息统计是否被@和特别关心
        if (state.sessions[info.id] && state.sessions[info.id].unread > 0) {
          // 在线/离线消息提示日程、消息平台弹窗
          if (info.onmsg == 1 || info.onmsg == 2) {
            // 日程强提醒、消息平台弹窗提醒
            if (info.id == "p2p-" + config[config.env].scheduleNumber && item.content && (item.content.type == "schedule_invite" || item.content.type == "schedule_remind")) {
              dispatch("setRemindMsg", {type: "add", value: deepClone(item)})
            }
            // 消息平台弹窗提醒
            if (info.id == "p2p-" + config[config.env].msgCenterNumber && item.content && (item.content.type == "msg-center" || item.content.type == "msg-report" || item.content.type == "msg-center-link")) {
              let thisMsg = deepClone(item);
              // 设置底部padding,1-稍后提醒，2-按钮，3-稍后提醒和按钮
              thisMsg.msgCenterType = 0;
              if (item.content.type == "msg-center" || item.content.type == "msg-report" || item.content.type == "msg-center-link") {
                if (item.content.data) {
                  thisMsg.content.data.content = dealMsgCenterContent(thisMsg);
                  if (thisMsg.content.data.laterRemind == 2) {
                    thisMsg.msgCenterType = 1;
                  }
                  let msgContent = thisMsg.content.data.content;
                  if (msgContent && msgContent.length > 0 && msgContent[msgContent.length - 1] && msgContent[msgContent.length - 1].type == "button") {
                    if (thisMsg.msgCenterType == 1) {
                      thisMsg.msgCenterType = 3;
                    } else {
                      thisMsg.msgCenterType = 2;
                    }
                  }
                }
              }
              if (item.content.data.rank == 2) {
                // 重要弹窗
                if (item.content.type == "msg-center" || item.content.type == "msg-center-link") {
                  dispatch("setRemindMsg", {type: "add", value: thisMsg});
                }
              } else if (item.content.data.rank == 3 && !state.importantMsg.idServer) {
                // 紧急弹窗(存在则不继续弹出)
                dispatch("setImportantMsg", {type: "add", value: thisMsg});
              }
            }
          }
          // 在线/离线/漫游消息统计特别关心和被@，消息是未读的时候才统计
          if (i >= info.msgs.length - state.sessions[info.id].unread) {
            // 特别关心消息
            if (state.concernMap["p2p-" + item.from]) {
              commit("setConcernMsgMap", {id: info.id, item: item});
              if (info.onmsg == 1 || info.onmsg == 2) {
                // 在线/离线才显示特别关心右上角弹窗
                let concernMsg = deepClone(item);
                // 不是1v1加入群名
                if (concernMsg.scene != "p2p") {
                  concernMsg.teamName = state.teams[item.to].name;
                }
                state.emit.concernMsg = concernMsg;
              }
            }
            try {
              // 被@
              if (item.custom && item.custom.hait) {
                item.custom.hait.map(account => {
                  commit("setHaitMsgMap", {id: info.id, account: account, item: item});
                });
              } else if (/hait/.test(JSON.stringify(item.custom.msgs)) || /hait/.test(JSON.stringify(item.content.msgs))) {
                let thisMsgs = /hait/.test(JSON.stringify(item.custom.msgs)) ? item.custom.msgs : item.content.msgs;
                thisMsgs.map(thisItem => {
                  if (thisItem.custom && thisItem.custom.hait) {
                    thisItem.custom.hait.map(account => {
                      commit("setHaitMsgMap", {id: info.id, account: account, item: item});
                    });
                  }
                })
              }
            } catch (e) {
            }
          }
        }
        if (!state.persons[item.from]) {
          workerMap[item.from] = item.from;
        }
        if (item.from == item.to && item.type == "tip" && item.custom && item.custom.type == "audio") {
          dispatch("setAudioTipMsg", [item]);
          msgMap[item.sessionId].pop();
          info.msgs.splice(i, 1);
          i--;
          continue;
        }
      } catch (e) {
        console.log("setMsgForError", e);
      }

      // 当前消息会话详情
      let itemSession = state.sessions[item.sessionId];
      try {
        // 在线消息显示通知
        if (info.onmsg == 1 && state.settings[config.settings.type7] != 2 && (state.currentSession.id != item.sessionId || !state.currentSession.timer) &&
          itemSession && !itemSession.isNoTip && !itemSession.isHelper && (item.type != "tip" || item.deleteMsg) && item.type != "notification" &&
          item.from != state.userInfo.workerNo) {
          dispatch("setNotification", {type: "add", session: deepClone(itemSession), msg: item});
        }
      } catch (e) {
        console.log("notifyErr", e);
      }
      try {
        if (item.sessionId == `p2p-` + config[config.env].instructionNumber && item.content && item.content.type == "upload") {
          if (info.onmsg == 1) {
            // 在线消息的指令账号执行上传
            dispatch("uploadLogFile", {type: item.content.detailType, no: item.content.no, date: item.content.date});
          } else if (state.baseComputerInfo.hostName == item.content.imei && Date.now() + state.diffTime - item.time < 3 * 24 * 60 * 60 * 1000) {
            // 离线3天内和imei匹配的才上传
            dispatch("uploadLogFile", {type: item.content.detailType, no: item.content.no, date: item.content.date});
          }
        }
      } catch (e) {
        console.log("uploadLogFileErr", e);
      }
      // 窗口闪烁,3s一次
      if (info.onmsg == 1 && itemSession && !itemSession.isNoTip && !itemSession.isHelper && Date.now() - state.flashTime > 3 * 1000) {
        state.flashTime = Date.now();
        chrome.windows.update(getters.getCurrentWindow("chat-" + itemSession.id).cWindow.id, {drawAttention: true})
      }
    }
    // 首次登录离线漫游消息不同步
    if (!(info.onmsg != 1 && info.asyncCount == 0)) {
      for (let key in msgMap) {
        if (msgMap[key][0]) {
          commit("setMsgRepeat", {
            id: msgMap[key][0].sessionId,
            msgs: msgMap[key],
            history: info.history
          });
        }
      }

      if (Object.keys(workerMap).length > 0) {
        commit("setPersons", {workerMap: workerMap});
      }
    }

    if (info.history) {
      state.msgsLoading[info.id] = false;
    }
    commit("setMsgRead", {id: info.id});
  },
  // 设置消息字段
  setMsgField: function ({state, commit, dispatch, getters}, info) {
    let {item, onmsg} = info;
    // 判断通知消息转义
    if (item.attach && item.attach.team) {
      if (typeof item.attach.team.announcement == "string") {
        try {
          item.attach.team.announcement = JSON.parse(item.attach.team.announcement);
        } catch (e) {
        }
      }
      if ((item.attach.type == 'updateTeam' || item.attach.type == 'updateSuperTeam') && item.attach.team.announcement) {
        item.announcement = true;
        if (onmsg) {
          commit('setNoticeUnread', {teamId: item.target, type: 1})
          // 设置子窗口通知红点
          let childWin = getChildWin("chat-" + item.sessionId);
          if (childWin && isMainWin()) {
            childWin.window.store.commit("setNoticeUnread");
          }
        }
      }
    }
    // 判断自定义转义
    if (item.custom && typeof item.custom == "string") {
      try {
        item.custom = JSON.parse(item.custom);
      } catch (e) {
      }
    }
    if (item.content) {
      // 判断内容转义
      if (typeof item.content == "string") {
        try {
          item.content = JSON.parse(item.content);
        } catch (e) {
        }
      }
      // 判断内容消息转义
      if (item.content?.msgs) {
        if (typeof item.content.msgs == "string") {
          try {
            item.content.msgs = JSON.parse(item.content.msgs);
          } catch (e) {
          }
        }
      }
      // 判断小乐消息转义
      if (item.content?.msgs && item.content.msgs.length > 0) {
        try {
          item.content.msgs.map(msgItem => {
            if (typeof msgItem.custom == "string") {
              try {
                msgItem.custom = JSON.parse(msgItem.custom);
              } catch (e) {
              }
            } else if (typeof msgItem.file == "string") {
              try {
                msgItem.file = JSON.parse(msgItem.file);
              } catch (e) {
              }
            }
          });
        } catch (e) {
        }
      }
      // 判断小乐消息转义
      if (item.content?.robot && typeof item.content.robot == "string") {
        try {
          item.content.robot = JSON.parse(item.content.robot);
        } catch (e) {
        }
      }
      // 小乐消息存在sseId删除对应的服务
      let xlSseId = item.content.sseId || item.content.sid || item.content.ident;
      if (xlSseId) {
        try {
          // 兼容接口超时但有数据返回的场景
          if (!state.sseMsgMap[xlSseId] && state.sseMsgMap[item.content.ident]) {
            commit("setSseMsgMap", {delete: true, sseTempMsg: state.sseMsgMap[item.content.ident]});
          }
        } catch (e) {}
        try {
          // 设置该流收到消息
          if (state.sseIdentMap[item.content.ident]) {
            state.sseIdentMap[item.content.ident] = 2;
            dispatch("setSseListener", {type: "remove", id: xlSseId, ident: item.content.ident, delete: true});
          } else {
            dispatch("setSseListener", {type: "remove", id: xlSseId, delete: true});
          }
        } catch (e) {}
      }
      // 判断other字段转义
      if (item.content?.other && typeof item.content.other == "string") {
        try {
          item.content.other = JSON.parse(item.content.other);
        } catch (e) {
        }
      }
      // 判断data字段转义
      if (item.content?.data && typeof item.content.data == "string") {
        try {
          item.content.data = JSON.parse(item.content.data);
        } catch (e) {
        }
      }
      // 集中销售字段转义
      if (item.content?.data && item.content.data.jsonData && typeof item.content.data.jsonData == "string") {
        try {
          item.content.data.jsonData = JSON.parse(item.content.data.jsonData);
        } catch (e) {
        }
      }
    }
    // 设置显示文字缓存
    if (item.type == "text") {
      item.showText = buildEmoji(strToHtml(item.text, '', item));
    }
    dispatch("setMsgsLocal", {item: item, notAwait: true});
    // 设置消息本地缓存字段
    return new Promise(async resolve => {
      await dispatch("setMsgsLocal", {item: item});
      resolve();
    });
  },
  // 设置消息本地文件缓存字段
  setMsgsLocal: async function ({state, commit, dispatch}, info) {
    return new Promise(async resolve => {
      let {item, notAwait} = info
      // 自定义消息
      let multiMsg = item.content?.msgs || [];
      // 设置消息文件本地缓存
      if (item.file && item.type != "document" && /https?:\/\//.test(item.file.url)) {
        if (!item.file.fileInfo) {
          item.file.fileInfo = {};
        }
        if (item.type == "video") {
          if (!item.file.videoInfo) {
            item.file.videoInfo = {}
          }
          item.file.videoPic = item.file.url + (/\?/.test(item.file.url) ? "&" : "?") + "vframe";
        }
        // 禁止显示的文件类型
        if (/bat/i.test(item.file.ext)) {
          item.forbidMsg = true;
          item.forbidFileMsg = true;
        }
        // 文件类型图片
        if (config.imgTypeReg.test(item.file.ext)) {
          item.file.isImage = true;
        }
        // 设置图片宽度
        if (state.msgWidthObj.msg && item.file.w && item.file.h) {
          // 计算图片显示宽高
          let fileWH = calcWH(10, 10, state.msgWidthObj.msg, state.msgWidthObj.height, item.file.w, item.file.h);
          item.file.showW = fileWH.w;
          item.file.showH = fileWH.h;
        }
        if (!notAwait && item.file.url && (!item.file.fileInfo.url || (item.file.videoInfo && !item.file.videoInfo.url) || remote.store.state.removeMD5Obj[MD5(item.file.url)])) {
          // 同步设置消息本地数据
          await dispatch("setFileLocal", {file: deepClone(item.file), item: item}).then(res => {
            item.file = res;
          });
        }
      } else if (multiMsg.length > 0) {
        for (let j = 0; j < multiMsg.length; j++) {
          // 设置显示文字缓存
          if (multiMsg[j].type == "text") {
            multiMsg[j].showText = buildEmoji(strToHtml(multiMsg[j].text, '', multiMsg[j]));
          }
          // 设置图片宽度
          if (state.msgWidthObj.msg && multiMsg[j].file && multiMsg[j].file.w && multiMsg[j].file.h) {
            // 计算图片显示宽高
            let fileWH = calcWH(10, 10, state.msgWidthObj.msg * 0.7, state.msgWidthObj.height, multiMsg[j].file.w, multiMsg[j].file.h);
            multiMsg[j].file.showW = fileWH.w;
            multiMsg[j].file.showH = fileWH.h;
          }
          if (!notAwait && multiMsg[j].file && multiMsg[j].type != "document" &&
            (!multiMsg[j].file.fileInfo?.url || remote.store.state.removeMD5Obj[MD5(multiMsg[j].file.url)])) {
            multiMsg[j].file.fileInfo = {};
            // 图片消息默认缓存
            if (multiMsg[j].file.url) {
              // 同步设置消息本地数据
              await dispatch("setFileLocal", {file: deepClone(multiMsg[j].file), item: item}).then(res => {
                multiMsg[j].file = res;
              });
            }
          }
        }
      }
      // 计算消息平台和资讯消息图片大小
      switch (item.content?.type) {
        case "msg-center":
        case "msg-report":
        case "imNews":
        case "msg-center-link":
          let customImageMsg = item.content?.data?.content || [];
          for (let j = 0; j < customImageMsg.length; j++) {
            let thisItem = customImageMsg[j];
            if (thisItem.type == "img" || thisItem.type == "image") {
              let imageW = thisItem.w || thisItem?.file?.w;
              let imageH = thisItem.h || thisItem?.file?.h;
              let imageUrl = thisItem.value;
              try {
                // 对象不存在宽高则通过url判断
                if (!imageW || !imageH) {
                  let urlParam = {};
                  let urlSearchIndex = imageUrl?.indexOf("?") || -1;
                  if (urlSearchIndex > -1) {
                    urlParam = new URLSearchParams(imageUrl.slice(urlSearchIndex));
                    imageW = Number(urlParam.get("w"));
                    imageH = Number(urlParam.get("h"));
                  }
                }
              } catch (e) {
                console.log("ImageWH-URLSearchParamsErr", e)
              }
              if (imageW && imageH) {
                let msgWidth = state.msgWidthObj.centerImage;
                if (item.content?.type == "imNews") {
                  msgWidth = state.msgWidthObj.newsImage;
                }
                if (msgWidth) {
                  let fileWH = calcWH(10, 10, msgWidth, state.msgWidthObj.height, imageW, imageH);
                  if (thisItem.type == "image") {
                    thisItem.file.showW = fileWH.w;
                    thisItem.file.showH = fileWH.h;
                  } else {
                    thisItem.showW = fileWH.w;
                    thisItem.showH = fileWH.h;
                  }
                }
              }
            }
          }
          break;
      }
      resolve();
    });
  },
  // 发送消息
  sendMsg: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let {sessionId, messages, notGetLink, resendFlag, fileMsgFlag, autoResendFlag} = info;
      let {scene, account, uniqueSign, thisMsgTime} = await dispatch("getMsgInfo", info);
      let currentWindow = getters.getCurrentWindow("chat-" + sessionId);
      if (!notGetLink) {
        messages = await dispatch("getLinkResult", {sessionId, messages, thisMsgInfo: {scene, account, uniqueSign, thisMsgTime, fileMsgFlag}});
      }
      let MsgCustom = {};
      if (resendFlag) {
        MsgCustom = info.custom;
      } else {
        let quoteMsg = remote.store.state.quoteMsg[sessionId];
        // 消息custom字段
        MsgCustom = getMsgCustom({
          quoteMsg: quoteMsg,
          messages: messages
        });
      }
      // 推送提醒内容
      let contentParam = {userName: `${state.userInfo.name}`};
      if (scene != "p2p" && state.teams[account]) {
        contentParam.teamName = state.teams[account].name;
      }
      let thisMsg = {
        uniqueSign: uniqueSign,
        fileMsgFlag: fileMsgFlag,
        autoResendFlag: autoResendFlag,
        id: sessionId,
        sessionId: sessionId,
        from: state.userInfo.workerNo,
        status: "toSending",
        to: account,
        scene: scene,
        custom: typeof MsgCustom != "string" ? JSON.stringify(MsgCustom) : MsgCustom,
        messages: messages,
        time: thisMsgTime
      }
      if (messages.length == 0) {
        if (thisMsg.fileMsgFlag) {
          delete remote.store.state.fileMsgFlagMap[thisMsg.fileMsgFlag];
        }
        return;
      } else if (messages.length == 1 && messages[0].type === "text") {
        // 推送提醒内容
        contentParam.text = messages[0].text;
        thisMsg.type = "text";
        thisMsg.pushContent = getPushContent(contentParam);
        thisMsg.text = messages[0].text;
        state.nimSDK.sendText({
          uniqueSign: thisMsg.uniqueSign,
          fileMsgFlag: thisMsg.fileMsgFlag,
          autoResendFlag: thisMsg.autoResendFlag,
          scene: thisMsg.scene,
          to: thisMsg.to,
          text: thisMsg.text,
          custom: thisMsg.custom,
          pushContent: thisMsg.pushContent,
        }).then((res) => {
          resolve(res);
          dispatch("sendMsgDone", res);
        });
      } else if (messages.length == 1 && messages[0].type == "image") {
        // 推送提醒内容
        contentParam.text = "[图片]";
        thisMsg.type = "image";
        thisMsg.pushContent = getPushContent(contentParam);
        thisMsg.file = {url: messages[0].url};
        thisMsg.blob = messages[0].blob;
        if (messages[0].blob && messages[0].blob.size > 2 * 1024 * 1024) {
          alert({
            content: "图片大于2M，将以文件方式发送。",
            done: (type) => {
              if (type == 1) {
                thisMsg.type = "file";
                // 发送文件
                state.nimSDK.sendFile({
                  uniqueSign: thisMsg.uniqueSign,
                  fileMsgFlag: thisMsg.fileMsgFlag,
                  autoResendFlag: thisMsg.autoResendFlag,
                  scene: thisMsg.scene,
                  to: thisMsg.to,
                  type: thisMsg.type,
                  blob: dataUrlToBlob(thisMsg.file.url),
                  pushContent: thisMsg.pushContent,
                  thisMsg: thisMsg,
                }).then((res) => {
                  resolve(res);
                  dispatch("sendMsgDone", res);
                });
              }
              // 设置消息发送状态
              dispatch("setMsgs", {id: sessionId, msgs: [thisMsg]});
            }
          });
          return;
        } else {
          // 发送图片
          state.nimSDK.sendFile({
            uniqueSign: thisMsg.uniqueSign,
            fileMsgFlag: thisMsg.fileMsgFlag,
            autoResendFlag: thisMsg.autoResendFlag,
            scene: thisMsg.scene,
            to: thisMsg.to,
            type: thisMsg.type,
            blob: dataUrlToBlob(thisMsg.file.url),
            custom: thisMsg.custom,
            pushContent: thisMsg.pushContent,
            thisMsg: thisMsg,
          }).then((res) => {
            resolve(res);
            dispatch("sendMsgDone", res);
          });
        }
      } else if (messages.length == 1 && (messages[0].type == "file" || messages[0].type == "audio" || messages[0].type == "video")) {
        // 推送提醒内容
        contentParam.text = "[文件]";
        thisMsg.type = messages[0].type;
        thisMsg.pushContent = getPushContent(contentParam);
        thisMsg.file = messages[0].file;
        // 发送文件
        state.nimSDK.sendFile({
          uniqueSign: thisMsg.uniqueSign,
          fileMsgFlag: thisMsg.fileMsgFlag,
          autoResendFlag: thisMsg.autoResendFlag,
          scene: thisMsg.scene,
          to: thisMsg.to,
          type: thisMsg.type,
          blob: thisMsg.file,
          pushContent: thisMsg.pushContent,
          thisMsg: thisMsg,
        }).then((res) => {
          resolve(res);
          dispatch("sendMsgDone", res);
        });
      } else if (messages.length == 1 && messages[0].type == "custom") {
        // 推送提醒内容
        contentParam.text = messages[0].pushContent || "[自定义消息]";
        thisMsg.type = "custom";
        thisMsg.content = JSON.stringify(messages[0].content);
        thisMsg.pushContent = getPushContent(contentParam);
        state.nimSDK.sendCustomMsg({
          uniqueSign: thisMsg.uniqueSign,
          fileMsgFlag: thisMsg.fileMsgFlag,
          autoResendFlag: thisMsg.autoResendFlag,
          scene: thisMsg.scene,
          to: thisMsg.to,
          content: JSON.stringify(messages[0].content),
          custom: thisMsg.custom,
          pushContent: thisMsg.pushContent,
          thisMsg: thisMsg,
        }).then((res) => {
          resolve(res);
          dispatch("sendMsgDone", res);
        });
      } else if (messages.length == 1 && messages[0].type == "geo") {
        // 推送提醒内容
        contentParam.text = messages[0].pushContent || "[位置]";
        thisMsg.type = "geo";
        thisMsg.geo = messages[0].geo;
        thisMsg.pushContent = getPushContent(contentParam);
        state.nimSDK.sendGeoMsg({
          uniqueSign: thisMsg.uniqueSign,
          fileMsgFlag: thisMsg.fileMsgFlag,
          autoResendFlag: thisMsg.autoResendFlag,
          scene: thisMsg.scene,
          to: thisMsg.to,
          geo: thisMsg.geo,
          pushContent: thisMsg.pushContent,
          thisMsg: thisMsg,
        }).then((res) => {
          resolve(res);
          dispatch("sendMsgDone", res);
        });
      } else if (messages.length > 0) {
        // 图文消息
        let previewerP = [];
        let contentText = "";
        // 房产网敏感词命中词
        let fcwKeyMap = {};
        for (let i = 0; i < messages.length; i++) {
          if (messages[i].type == "text") {
            messages[i].text = getters.getKeyText(messages[i].text);
            // 房产网客户消息判断是否命中敏感词
            if (new RegExp(config.fcw).test(thisMsg.to)) {
              fcwKeyMap = Object.assign(fcwKeyMap, getters.getFcwKeyMap({scene: thisMsg.scene, to: thisMsg.to, text: messages[i].text}));
            }
            let thisCustom = {
              type: "text",
              text: messages[i].text,
              custom: {},
            };
            // @人员
            if (messages[i].hait) {
              thisCustom.custom.hait = messages[i].hait;
              thisCustom.custom.haitPosition = messages[i].haitPosition;
              thisCustom.custom.atName = messages[i].atName;
            }
            // @文档
            if (messages[i].docId) {
              thisCustom.custom.docId = messages[i].docId;
              thisCustom.custom.docName = messages[i].docName;
              thisCustom.custom.docPos = messages[i].docPos;
              thisCustom.custom.docImg = messages[i].docImg;
              thisCustom.custom.property = messages[i].property;
            }
            contentText += messages[i].text;
            previewerP.push(Promise.resolve(thisCustom));
          } else if (messages[i].type === "document") {
            previewerP.push(Promise.resolve(messages[i]));
            contentText += "[乐文档]";
          } else {
            contentText += "[图片]";
            // 上传图片
            let previewFileParam = {
              type: "image",
              name: Date.now(),
            }
            if (messages[i].file && messages[i].url) {
              previewFileParam.dataURL = messages[i].file.url;
            } else {
              previewFileParam.blob = messages[i].blob;
            }
            previewerP.push(state.nimSDK.previewFile(previewFileParam));
          }
        }
        // 房产网客户消息判断是否命中敏感词
        getters.getFcwKeyMsg(thisMsg, fcwKeyMap);
        // 推送提醒内容
        contentParam.text = contentText;
        thisMsg.type = "custom";
        thisMsg.content = JSON.stringify({
          type: "multi",
          msgs: messages
        });
        thisMsg.pushContent = getPushContent(contentParam);
        Promise.allSettled(previewerP).then((data) => {
          let msgStatus = "toSending";
          let msgContent = [];
          let msgErr = "";
          let msgErrCode = "";
          for (let i = 0; i < data.length; i++) {
            if (data[i].status != "fulfilled") {
              msgStatus = "fail";
              msgContent.push(data[i].reason);
              if (!msgErr) {
                msgErr = data[i].reason?.err?.message;
                msgErrCode = data[i].reason?.err?.code;
              }
            } else {
              msgContent.push(data[i].value);
            }
          }
          if (msgStatus == "toSending") {
            state.nimSDK.sendCustomMsg({
              uniqueSign: thisMsg.uniqueSign,
              fileMsgFlag: thisMsg.fileMsgFlag,
              autoResendFlag: thisMsg.autoResendFlag,
              scene: thisMsg.scene,
              to: thisMsg.to,
              content: JSON.stringify({
                type: "multi",
                msgs: msgContent
              }),
              custom: thisMsg.custom,
              pushContent: thisMsg.pushContent,
              text: contentText,
              thisMsg: thisMsg,
            }).then((res) => {
              resolve(res);
              dispatch("sendMsgDone", res);
            });
          } else {
            // 发送失败
            thisMsg.status = msgStatus;
            dispatch("setMsgs", {id: sessionId, msgs: [thisMsg]});
            if (msgErrCode == "Error_Timeout" && !state.nimInfo.type && !thisMsg.autoResendFlag) {
              // 超时且非重连状态重发一次
              console.log("Error_Timeout", thisMsg.uniqueSign);
              thisMsg.autoResendFlag = true;
              dispatch("resendMsg", deepClone(thisMsg));
              return;
            }
            toast({title: (!msgErr || msgErr == "超时") ? "图片上传失败，请重试" : msgErr, type: 2});
          }
        });
      }
      // 发送消息滚动到底部
      currentWindow.window.store.commit("setEmit", {type: "scroll", value: "bottom"});
      // 发送消息清空引用消息
      remote.store.commit("setQuoteMsg", {type: "del", id: sessionId});
      // 设置消息发送状态
      dispatch("setMsgs", {id: sessionId, msgs: [thisMsg]}).then(res => {
        setTimeout(() => {
          // 发送消息滚动到底部
          currentWindow.window.store.commit("setEmit", {type: "scroll", value: "bottom"});
          // 第一次打开该会话发送消息滚动到当前会话
          if (state.scrollSessionState) {
            commit("setEmit", {type: "scrollCurrentSession", value: Date.now()});
          }
          commit("setScrollSessionState", false);
        }, 100);
      });
    });
  },
  // 发送消息结束回调
  sendMsgDone: function ({state, commit, dispatch, getters}, info) {
    let msg = info.obj;
    console.log("sendMsgDone", `idServer:${msg.idServer},idClient:${msg.idClient},uniqueSign:${msg.uniqueSign},from:${msg.from},to:${msg.to},time:${msg.time}`);
    if (info.err?.code == "Error_Timeout" && !state.nimInfo.type && !msg.autoResendFlag) {
      // 超时且非重连状态重发一次
      console.log("Error_Timeout", msg.uniqueSign);
      msg.autoResendFlag = true;
      dispatch("resendMsg", deepClone(msg));
      return;
    }
    // 7101被拉黑
    if (info.err && info.err.code != 7101 && info.err.message != "notTips") {
      let thisContent = JSON.stringify(msg.content) || "";
      let thisCustom = JSON.stringify(msg.custom) || "";
      if ((thisContent?.length > 4000 || thisCustom?.length > 1000) && info.err.code == "414") {
        toast({title: "消息内容过长", type: 2});
        console.log("消息内容过长", encrypt(thisContent), encrypt(thisCustom));
      } else {
        toast({title: info.err.message, type: 2});
      }
    }
    if (info.err && info.err.code == 7101) {
      msg.detailStatus = "您已被对方拉黑，消息已被拒收";
    }
    dispatch("setMsgs", {id: info.obj.sessionId, msgs: [deepClone(msg)]});
    // 处理常用表情
    let emojiList = [];
    try {
      if (msg.type == "custom") {
        let content = msg.content;
        // 判断是否有发送表情，同一个表情同一条消息，发送两个表情算两次
        emojiList = getXLMatchedStr(JSON.stringify(content));
      } else {
        emojiList = getXLMatchedStr(msg.text);
      }
      if (emojiList) {
        emojiList.map(item => {
          commit("setOftenEmoji", item);
        });
      }
    } catch (e) {
      console.log("setOftenEmojiErr", e);
    }
    if (!info.err) {
      // 发送成功判断是否需要小乐/智能助理回答
      dispatch("setAiQuestion", {msg: msg});
      // 发送成功后判断是否文档类型消息，是则批量授权可阅读
      try {
        if (msg.from == state.userInfo.workerNo && (msg.to != state.userInfo.workerNo || msg.to == msg.from) && msg.type == "custom" && /docPadId/.test(msg.content) && /document/.test(msg.content)) {
          let msgContent = deepClone(msg.content);
          if (typeof msgContent == "string") {
            try {
              msgContent = JSON.parse(msgContent);
            } catch (e) {
            }
          }
          let docList = [];
          msgContent.msgs.map(function (item) {
            // 乐文档
            if (item.type == "document") {
              setJJSEvent("P04376576", JSON.stringify({
                docId: item.file.docId,
                workerId: state.userInfo.workerId
              }));
              // 判断历史状态，存在可阅读/可编辑不改动
              if (item.file.empNumber == state.userInfo.workerId) {
                // 获取本地本地缓存
                let localDocPur = JSON.parse(localStorage.getItem("docPur") || '{}');
                if (!localDocPur[state.userInfo.workerNo]) {
                  localDocPur[state.userInfo.workerNo] = {};
                }
                let currLocalDocPur = localDocPur[state.userInfo.workerNo][msg.to + "-" + item.file.docId];
                docList.push({
                  fileId: item.file.docId,
                  fileType: 1,
                  name: msg.scene == "p2p" ? "" : state.sessions[msg.sessionId].detailInfo.name,
                  number: msg.scene == "p2p" ? getters.getPersons(msg.to).workerId : msg.to,
                  operateLevel: currLocalDocPur ? currLocalDocPur.key : "1",
                  type: msg.scene == "p2p" ? 1 : 5
                });
              }
            }
          });
          // 请求权限接口操作本地文档卡片
          if (docList.length > 0) {
            addPurDocListApi(docList).then(function (res) {
              if (res && res.data && res.data.length > 0) {
                let docPurList = res.data;
                docPurList.map(function (item) {
                  if (item && item.success) {
                    // 发送通知给自己和别人(用于多端同步)
                    state.nimSDK.sendCustomSysMsg({
                      scene: "p2p",
                      to: state.userInfo.workerNo,
                      content: JSON.stringify({
                        type: "updateDocument",
                        account: msg.to,
                        id: item.fileId,
                        key: item.operateLevel,
                        from: 1//1-pc 2-ios 3-android
                      }),
                      done: function () {}
                    });
                  }
                });
                console.log("addPurDocList", docPurList);
              }
            });
          }
        }
      } catch (e) {
        console.log('addPurDocListErr', e);
      }
    }
  },
  // 发起ai/小乐提问
  setAiQuestion: async function ({state, commit, dispatch, getters}, info) {
    let {msg, aiJson} = info;
    let res, isAll, isXl, isAiApp, preSseTempMsg, tempAts = [], param = {}, aiAppMap = {}, aiParam = {};
    let xlNo = config[config.env].robotEmpNo;
    let aiNo = state.aiObj.workerNo;
    let aiAppReg = new RegExp(config.ai);
    try {
      if (aiJson) {
        if (!aiJson.notSetAiLoading) {
          aiJson.aiLoading = false;
        }
        delete aiJson.notSetAiLoading;
        preSseTempMsg = aiJson;
        param = aiJson.aiParam;
        aiParam = aiJson.aiParam;
        aiAppMap[aiJson.from] = aiJson.from;
        isAiApp = true;
      } else {
        param = {
          questionText: msg.text || (msg.pushContent || "").replace(state.userInfo.name + ":", ""),
          questionType: "1",// 1文本2文件3乐文档4合并转发
          empNumber: state.userInfo.workerId,
          empNo: state.userInfo.workerNo,
        }
        if (msg.scene != "p2p") {
          param.questionText = param.questionText.replace(`${state.userInfo.name}(${state.teams[msg.to].name}):`, "");
        }

        isXl = msg.to == xlNo;
        isAiApp = aiAppReg.test(msg.to);

        // 私聊
        if (isXl || isAiApp) {
          param.source = 1;
          if (isAiApp) {
            aiAppMap[msg.to] = msg.to;
          }
        } else if (msg.custom) {
          // 群聊
          let msgCustom = deepClone(msg.custom);
          if (typeof msgCustom == "string") {
            try {
              msgCustom = JSON.parse(msgCustom);
            } catch (e) {
            }
          }
          // 遍历@人员是否有小乐、智能助理、数字人
          if (msgCustom?.hait?.length > 0) {
            msgCustom.hait.map(item => {
              if (!isXl && item == xlNo) {
                isXl = true;
              }
              if (aiAppReg.test(item)) {
                isAiApp = true;
                aiAppMap[item] = item;
              }
            });
            // 存在@消息
            if (isXl || isAiApp) {
              param.groupId = msg.to;
              param.source = 2;

              isAll = JSON.stringify(msgCustom.hait).indexOf("all") != -1;
              if (isAll) {
                let teamMembers = state.teamMembers[msg.to];
                tempAts = teamMembers.map(item => {return item.account});
              }
              // 群@ai新增引用消息体
              if (isAiApp) {
                param.customFrom = JSON.stringify(getQuoteMsg(msg, 1));
              }
            }
            param.questionText = getters.getXlInfo({xlNo: xlNo, isAll: isAll, tempAts: tempAts, custom: msgCustom, text: param.questionText}).text;
          }
        }

        // 判断消息类型
        switch (msg.type) {
          case "file":
            // 文件
            param.questionType = "2";
            if (msg.file?.isImage) {
              param.questionType = "1";
              param.questionText = `\n${msg.file.url}\n`;
            } else {
              if (msg.file?.url) {
                param.questionJson = {
                  url: msg.file.url,
                  size: msg.file.size,
                  ext: msg.file.ext,
                  name: msg.file.name
                }
              }
            }
            break;
          case "image":
            // 图片
            if (msg.file?.url) {
              param.files = JSON.stringify([{type: "image", transfer_method: "remote_url", url: msg.file.url}]);
            }
            break;
          case "custom":
            if (msg.content) {
              let msgContent = deepClone(msg.content);
              if (typeof msgContent == "string") {
                try {
                  msgContent = JSON.parse(msgContent);
                } catch (e) {
                }
              }
              switch (String(msgContent.type)) {
                case "multi":
                  // 图文
                  let thisMsgs = msgContent.msgs;
                  param.questionText = "";
                  thisMsgs.map(item => {
                    if (item.type == "text") {
                      let thisText = item.text;
                      // 获取@小乐信息
                      let xlInfo = getters.getXlInfo({xlNo: xlNo, isAll: isAll, tempAts: tempAts, custom: item.custom, text: thisText});
                      tempAts = xlInfo.tempAts;
                      param.questionText += xlInfo.text;
                    } else if (item.type == 'image') {
                      if (item.file?.url) {
                        if (!param.files) {
                          param.files = [];
                        }
                        param.files.push({type: "image", transfer_method: "remote_url", url: item.file.url});
                      }
                    } else if (item.type == "document") {
                      param.questionType = "3";
                      param.questionJson = deepClone(item.file);
                    }
                  });
                  if (param.files) {
                    param.files = JSON.stringify(param.files);
                  }
                  break;
                case "9":
                  // 合并转发
                  param.questionType = "4";
                  param.questionJson = {msgidServer: msgContent.data?.other?.messageIds, name: msgContent.data?.title};
                  break;
                case "7":
                  // 房源卡片
                  param.questionText = getters.getLinkUrlJson({...msgContent, content: msgContent.data, shareNo: state.userInfo.workerNo}).url;
                  break;
              }
            }
            break;
        }

        param.atEmpNos = tempAts.join(",");
      }

      // 没有来源不回答
      if (param.source) {
        if (isXl) {
          let xlParam = deepClone(param);
          delete xlParam.questionType;
          delete xlParam.questionJson;
          // 小乐消息请求后台获取sse流式
          res = await answerXiaoLeApi({
            msgBody: JSON.stringify(xlParam),
          });
          if (res?.data?.sseId) {
            dispatch("setSseListener", {type: "add", msgType: "xl", id: res.data.sseId, sessionId: msg.sessionId, from: config[config.env].robotEmpNo});
          }
        }
        // 数字人消息遍历
        for (let key in aiAppMap) {
          let sseTempMsg = {};
          if (!preSseTempMsg) {
            aiParam = deepClone(param);
            aiParam.toTager = msg.to;
            aiParam.aiAccid = key;
            let isSummary = aiParam.questionType == 2 || aiParam.questionType == 3 || aiParam.questionType == 4;
            if (key == aiNo) {
              // 只有智能助理使用gpt4
              aiParam.modelName = state.aiObj.gpt4 && state.aiObj.currentApp?.id == 1 ? "gpt-4-32k" : "";
              // 发送文件/乐文档/聊天当前应用不是快速总结切换
              if (isSummary && state.aiObj.currentApp.id != 3) {
                remote.store.commit("setAiObj", {switchApp: true, currentApp: state.aiObj.commonAppList.find(item => {return item.id == 3}), sendTo: key, notTip: true});
              }
              aiParam.appTypeId = state.aiObj.currentApp.id;
              // 不在使用
              // aiParam.knowBaseId = state.aiObj.currentApp?.childObj?.id;
            } else {
              aiParam.appTypeId = state.persons[key]?.appTypeId;
              // 制定业务类型的id需要等待获取临时应用id
              if (state.persons[key]?.businessType && state.persons[key].businessType != "AI" && msg.scene == "p2p") {
                let aiTempMap = state.aiTempIdMap["p2p-" + key];
                aiParam.appTypeId = aiTempMap?.id;
                // 不存在业务id调用对应接口
                if (!aiParam.appTypeId && aiTempMap?.temp) {
                  if (state.persons[key].businessType == "AI_PL") {
                    if (msg.sessionId == state.currentSession.id) {
                      // 当前会话没变
                      if (aiTempMap.temp.id) {
                        // 发送开场白
                        if (aiTempMap.temp.prologue) {
                          commit("sendAiMsgCard", {content: {type: "ai-msg", msgs: [{type: "text", text: aiTempMap.temp.prologue}]}, sendTo: msg.to, time: msg.time + 1});
                        }
                      } else {
                        // ai陪练接口
                        aiSparringApi({stage: aiTempMap.temp.stage, businessId: aiTempMap.temp.businessId, fromAccid: msg.to});
                      }
                      commit("setAiTempIdMap", {key: state.currentSession.id, value: {id: aiTempMap.temp.id, flag: true, temp: aiTempMap.temp}});
                    }
                  }
                }
              }
              // 群@ai新增引用消息体
              if (aiAppReg.test(key)) {
                // 群在群数据集
                let datasetId = state.teamAiMap[msg.to]?.datasetId;
                if (datasetId && state.teamAiMap[msg.to].to == key) {
                  aiParam.datasetId = datasetId;
                  aiParam.inputs = JSON.stringify({describe: state.teamAiMap[msg.to].prologue});
                }
              }
            }
            if (!aiParam.appTypeId) {
              return;
            }
            if (isSummary) {
              if (aiParam.questionType == 3) {
                aiParam.questionJson.askContext = aiParam.questionText;
              }
              delete aiParam.questionText;
              aiParam.questionJson = JSON.stringify(aiParam.questionJson);
            } else {
              if (!aiParam.questionText) {
                return;
              }
            }
            aiParam.ident = UUID.generate();
            sseTempMsg = {
              scene: msg.scene,
              from: key,
              to: msg.from,
              sessionId: msg.sessionId,
              time: msg.time + 1,
              type: "text",
              aiLoading: true,
              idServer: aiParam.ident,
              status: "success",
              forbidMsg: true,
              aiParam: aiParam,
              isSseMsg: true,
            };
          } else {
            sseTempMsg = deepClone(preSseTempMsg);
          }
          // 存在文件+文字的问题，等待全部消息发送完成后再调用
          if (msg?.fileMsgFlag && remote.store.state.fileMsgFlagMap[msg.fileMsgFlag]) {
            if (remote.store.state.fileMsgFlagMap[msg.fileMsgFlag].count > 0) {
              remote.store.state.fileMsgFlagMap[msg.fileMsgFlag].count--;
              remote.store.state.fileMsgFlagMap[msg.fileMsgFlag].msgs.push({...sseTempMsg, aiParam: aiParam});
              return;
            } else {
              let fileMsgFlag = remote.store.state.fileMsgFlagMap[msg.fileMsgFlag].type;
              aiParam.questionType = fileMsgFlag == "file" ? "2" : fileMsgFlag == "document" ? "3" : "4";
              if (msg.type == "text") {
                // 文字后发送完
                aiParam.questionJson = JSON.parse(remote.store.state.fileMsgFlagMap[msg.fileMsgFlag].msgs[0].aiParam.questionJson);
                aiParam.questionJson.askContext = aiParam.questionText;
              } else {
                // 文件/乐文档/合并消息后发送完
                let askContext = "";
                aiParam.questionJson = JSON.parse(aiParam.questionJson);
                remote.store.state.fileMsgFlagMap[msg.fileMsgFlag].msgs.map(item => {
                  askContext += item.aiParam.questionText;
                });
                aiParam.questionJson.askContext = askContext;
              }
              aiParam.questionJson = JSON.stringify(aiParam.questionJson);
              delete aiParam.questionText;
              // 提问后删除发送文件标识
              delete remote.store.state.fileMsgFlagMap[msg.fileMsgFlag];
            }
          }
          sseTempMsg.text = "我正在思考…";
          state.sseIdentMap[aiParam.ident] = 1;
          aiParam.axiosCancel = res => {
            sseTempMsg.axiosCancel = res;
            delete sseTempMsg.stopSseFlag;
            commit("setSseMsgMap", {sessionId: sseTempMsg.sessionId, sseTempMsg: deepClone(sseTempMsg)});
          }
          res = await initiateAskApi(aiParam);
          // 弱网收到消息后不在渲染,并告知服务器停止流
          if (state.sseIdentMap[aiParam.ident] == 2) {
            return;
          }
          if (res?.success) {
            if (res?.data?.sid) {
              sseTempMsg.text = "我正在组织语言…";
              commit("setSseMsgMap", {delete: true, sseTempMsg: sseTempMsg});
              sseTempMsg.cid = res.data.cid;
              sseTempMsg.sid = res.data.sid;
              sseTempMsg.idServer = res.data.sid;
              commit("setSseMsgMap", {sessionId: sseTempMsg.sessionId, sseTempMsg: deepClone(sseTempMsg)});
              // 创建流数据
              if (res.data.sid) {
                dispatch("setSseListener", {type: "add", msgType: "ai", id: res.data.sid, ident: aiParam.ident, sessionId: sseTempMsg.sessionId, from: sseTempMsg.from});
              }
            } else {
              commit("setSseMsgMap", {delete: true, sseTempMsg: sseTempMsg});
            }
          } else {
            // 主动终止不渲染
            if (getters.getWinState({id: sseTempMsg.sessionId, key: "sseMsgMap"})[sseTempMsg.idServer]) {
              sseTempMsg.text = "抱歉，内容处理失败，请刷新重试";
              sseTempMsg.resendFlag = true;
              sseTempMsg.stopSseFlag = true;
              commit("setSseMsgMap", {sessionId: sseTempMsg.sessionId, sseTempMsg: deepClone(sseTempMsg)});
            }
          }
        }
      }
    } catch (e) {
      console.log('setAiQuestionErr', e);
    }
  },
  // 初始化im编辑器
  setImEditor: function ({state, commit, dispatch, getters}, info) {
    if (!info) {
      state.imEditor = {};
      return;
    }
    // 不是聊天页面不初始化
    if (state.router.currentRoute.path != "/index/chat" && state.router.currentRoute.path != "/index/mailList" && state.router.currentRoute.path != "/child/childChat") {
      return;
    }
    state.imEditor = initEditor({
      id: info.id,
      send: (param) => {
        // 取消@状态
        if (state.haitInfo.haitFlag || state.haitInfo.callFlag) {
          state.haitInfo.enter = true;
          return;
        }
        // 发送消息param-key键值-shiftKey是否按下shift-shortKey是否按下ctrl
        let sendType = remote.store.state.settings[config.settings.type4];
        if ((sendType == 1 && !param.shortKey) || (sendType == 2 && param.shortKey)) {
          dispatch("inputSend");
          // 聚焦当前会话
          dispatch("setCurrentSession", {id: state.currentSession.id, focus: true});
        } else {
          // 换行
          state.imEditor.insertText(state.currentSession.id, '\n');
          state.imEditor.activeEditor(state.currentSession.id);
        }
      },
      handlerKey: (e) => {
        try {
          // 存在内容去除上下切换会话功能
          let hasContentId = hasContent(state.imEditor.getContents(state.currentSession.id));
          if (hasContentId || (!hasContentId && hasContent(getters.getEditorContentMap[state.currentSession.id]))) {
            state.emit.toggleSession = false;
          }
        } catch (e) {}
        // 监听按键
        switch (e.key) {
          case "esc":
            // 取消@状态
            if (state.haitInfo.haitFlag || state.haitInfo.callFlag) {
              state.haitInfo = {};
              state.haitInfo.escFlag = true;
              return;
            }
            break;
          case "up":
            // 选中@下标上移
            if (state.haitInfo.haitFlag || state.haitInfo.callFlag) {
              if (state.haitInfo.type == 1) {
                // 搜索文档
                let docIndex = state.haitInfo.docIndex || 0;
                state.haitInfo.docIndex = --docIndex;
              } else if (state.haitInfo.type == 2) {
                // 搜索app
                let appIndex = state.haitInfo.appIndex || 0;
                state.haitInfo.appIndex = --appIndex;
              } else if (state.haitInfo.type == 3) {
                // 搜索ai
                let aiIndex = state.haitInfo.aiIndex || 0;
                state.haitInfo.aiIndex = --aiIndex;
              } else {
                // 搜索人员
                let memberIndex = state.haitInfo.memberIndex || 0;
                state.haitInfo.memberIndex = --memberIndex;
              }
              return false;
            }
            return true;
            break;
          case "down":
            // 选中@下标下移
            if (state.haitInfo.haitFlag || state.haitInfo.callFlag) {
              if (state.haitInfo.type == 1) {
                // 搜索文档
                let docIndex = state.haitInfo.docIndex || 0;
                state.haitInfo.docIndex = ++docIndex;
              } else if (state.haitInfo.type == 2) {
                // 搜索app
                let appIndex = state.haitInfo.appIndex || 0;
                state.haitInfo.appIndex = ++appIndex;
              } else if (state.haitInfo.type == 3) {
                // 搜索ai
                let aiIndex = state.haitInfo.aiIndex || 0;
                state.haitInfo.aiIndex = ++aiIndex;
              } else {
                // 搜索人员
                let memberIndex = state.haitInfo.memberIndex || 0;
                state.haitInfo.memberIndex = ++memberIndex;
              }
              return false;
            }
            return true;
            break;
        }
      },
      mouseup: (e) => {
        commit("setEmit", {type: "mouseup", value: Date.now()});
        dispatchEvent("mouseup");
      },
      mousemove: (id, e) => {
        remote.store.commit("setEmit", {type: "imActivity", value: Date.now()});
        commit("setEmit", {type: "mousemove", value: e});
      },
      click: (e) => {
        // 触发全局点击
        remote.store.commit("setEmit", {type: "imActivity", value: Date.now()});
        dispatch("setCurrentSession", {id: state.currentSession.id, type: "click", focus: true});
        commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
        e.id = state.currentSession.id;
        commit("setEmit", {type: "click", value: e});
        mousedownGlobalEvent(e);
      },
      menu: (id, contents, e) => {
        // 右键
        dispatch("activeImEditor", {id: id, active: true});
        // 子窗口不设置
        if (!getChildWin("chat-" + id)) {
          dispatch("setCurrentSession", {id: state.currentSession.id});
        }
        // 触发全局点击
        let menuList = [];
        if (contents && contents.ops && contents.ops.length > 0) {
          menuList.push({
            label: "复制", click: function () {
              e.view.document.execCommand("copy");
            }
          });
          menuList.push({
            label: "剪切", click: function () {
              e.view.document.execCommand("cut");
              setTimeout(() => {
                dispatch("activeImEditor", {id: state.currentSession.id, active: true});
              }, 100);
            }
          });
        }
        menuList.push({
          label: "粘贴", click: function () {
            e.view.document.execCommand("paste");
            setTimeout(() => {
              dispatch("activeImEditor", {id: state.currentSession.id, active: true});
            }, 100);
          }
        });
        if (contents && contents.ops && contents.ops.length === 1 && contents.ops[0].insert && contents.ops[0].insert["imImage"]) {
          let thisImg = contents.ops[0].insert["imImage"].node;
          menuList.push({
            label: "另存为", click: function () {
              dispatch("openFolder", {
                item: {file: {name: `乐聊图片${Date.now()}.png`, url: thisImg.src, ext: "png"}},
                openType: 1,
                thisElm: thisImg,
                done: res => {
                  if (res.state == "success") {
                    toast({title: "下载成功", type: 1});
                  } else if (res.state == "close" || res.state == "error") {
                    toast({title: "下载失败,请重试", type: 2});
                  }
                }
              });
            }
          });
        }
        let bounds = getBounding(document.getElementById("editor"));
        showMenu(menuList).popup(bounds.left + e.x, bounds.top + e.y);
      },
      dblImg: (id, contents) => {
        // 双击图片
        console.log("editor-dblImg", id, contents);
        if (contents && contents.imgs && contents.imgs.length > 0) {
          let thisElm = "";
          let thisIndex = -1;
          let imgList = [];
          for (let i = 0; i < contents.imgs.length; i++) {
            let item = contents.imgs[i];
            let thisSrc = item.src;
            try {
              thisSrc = decodeURIComponent(thisSrc);
            } catch (e) {}
            imgList.push({src: thisSrc, dataSrc: thisSrc, w: item.naturalWidth, h: item.naturalHeight});
            // 设置当前打开图片
            if (/ql-embed-selected/.test(item.className)) {
              thisElm = item;
              thisIndex = i;
            }
          }
          openViewer(imgList, thisIndex, thisElm.naturalWidth, thisElm.naturalHeight);
        }
      },
      focus: (id) => {
        // 获取焦点
        if (!hasContent(state.imEditor.getContents(id)) && hasContent(getters.getEditorContentMap[id])) {
          state.imEditor.setContents(id, getters.getEditorContentMap[id]);
        } else if (state.childEditorHtml) {
          // 子窗口插入消息
          state.imEditor.dangerouslyPasteHTML(id, state.childEditorHtml);
          state.childEditorHtml = "";
        }
      },
      blur: (e) => {
        // 失去焦点
      },
      hait: (id, range) => {
        // 触发@
        if (!state.haitInfo.callFlag) {
          state.haitInfo.haitFlag = true;
          state.haitInfo.haitCursor = range;
          state.haitInfo.haitText = "@";
          console.log("editor-hait", id, range);
        }
      },
      call: (id, range) => {
        // 触发/唤起应用弹窗
        if (!state.haitInfo.haitFlag) {
          state.haitInfo.callFlag = true;
          state.haitInfo.haitCursor = range;
          state.haitInfo.haitText = "#";
          console.log("editor-searchApp", id, range);
        }
      },
      change: (id) => {
        // 输入变化
        remote.store.commit("setEmit", {type: "imActivity", value: Date.now()});
        // 去除上下切换会话功能
        state.emit.toggleSession = false;
        // 发送已读回执
        if (!state.currentSession.timer) {
          dispatch("setCurrentSession", {id: state.currentSession.id});
        }
        // 显示@内容
        if (state.haitInfo.haitFlag || state.haitInfo.callFlag) {
          state.haitInfo.currCursor = state.imEditor.getSelection(id);
          if (!state.haitInfo.currCursor) {
            return;
          }
          let haitCursor = state.haitInfo.haitCursor;
          let currRange = state.haitInfo.currCursor;
          let content = state.imEditor.getContents(id, haitCursor.index, currRange.index - haitCursor.index);
          state.haitInfo.haitText = content.ops[0] ? content.ops[0].insert : "";
        }
        // 设置编辑器内容
        commit("setEditorContentMap", {id: id, content: state.imEditor.getContents(id)});
      },
      dropFile: (id, files) => {
        // 拖拽文件
        console.log("editor-dropFile", id, files);
        dispatch("dealFiles", {id: id, files: files})
      },
      pasteFile: (id, files) => {
        // 粘贴文件
        console.log("editor-pasteFile", id, files);
        if (files.length > 1 || (files.length == 1 && (files[0].path || files[0].filePath))) {
          dispatch("dealFiles", {id: id, files: files})
        }
      },
      imageError: (id, files) => {
        // 违规图片(如大于2M)
        console.log("editor-imageError", id, files);
        alert({
          content: "图片大于2M，将以文件方式发送。",
          done: (type) => {
            if (type == 1) {
              // 发送文件
              dispatch("dealFiles", {id: id, files: files})
            }
          }
        });
      },
      dropAndCopyFilter: (node, detla) => {
        // 处理拖拽/复制元素
        if (!node || !detla) {
          return detla;
        }
        let newDelta = new ImEditor.Delta;
        let className = node.attributes.getNamedItem("class");
        let isImg = node.nodeName.toLocaleLowerCase() == "img";
        let haitAccount = node.attributes.getNamedItem("data-hait-account") || node.attributes.getNamedItem("data-@-account");
        if (className && className.nodeValue) {
          // 复制文本
          if (/dataCopy/.test(className.nodeValue) && node.innerText) {
            return newDelta.insert(`${node.innerText}\n`);
          }
          if (/notCopy/.test(className.nodeValue)) {
            return newDelta;
          }
        }
        // @人员
        if (isImg && haitAccount && !/reEditor/.test(className.nodeValue)) {
          let thisUserInfo = state.persons[haitAccount.value];
          let thisUserName = thisUserInfo ? thisUserInfo.name : haitAccount.value;
          if (thisUserName == "all") {
            thisUserName = "全员";
          }
          let text = "@" + thisUserName;
          return newDelta.insert(text);
        }
        return detla;
      },
      dealImg: function (node) {
        let that = this;
        let currId = state.currentSession.id;
        // 处理图片
        return new Promise(async function (resolve) {
          let isFile = /file:\/\/\//.test(node);
          try {
            // 兼容不是file前缀的本地图片
            if (fs.statSync(node)) {
              isFile = true;
            }
          } catch (e) {}
          if (isFile) {
            // 本地压缩图片替换
            if (new RegExp(`/cached/${state.userInfo.workerNo}/images/thum/`).test(node)) {
              node = node.replace("/thum", "")
            }
            // 去除前后缀
            node = node.split("?")[0].replace("file:///", "");
            let filePath = node;
            try {
              // 中文地址需要转码
              filePath = decodeURIComponent(filePath);
            } catch (e) {}
            let base64 = await getLocalFile({filePath: filePath, base64: true});
            // 判断大于2M文件发送
            try {
              let thisFile = await getLocalFile({filePath: filePath, file: true});
              if (thisFile.size > 2 * 1024 * 1024) {
                if (state.currentSession.id == currId) {
                  that.imageError(currId, [thisFile]);
                }
                resolve("");
                return;
              }
            } catch (e) {}
            resolve({src: filePath, base64: base64});
          } else if (new RegExp(location.origin).test(node)) {
            // 处理本地origin文件,表情返回显示，其他不返回显示
            if (/img\/emoji\//.test(node)) {
              let name = getEmojiText(node.slice(node.lastIndexOf("/") + 1));
              if (name) {
                resolve({text: name, src: node});
              } else {
                resolve("");
              }
            } else {
              resolve("");
            }
          } else if (typeof node === "string") {
            if (/https?:\/\//.test(node)) {
              let urlExt = "png";
              let urlNameIndex = node.lastIndexOf("/");
              if (urlNameIndex > -1) {
                urlExt = getFileExt(node.slice(urlNameIndex + 1), 1) || "png";
              }
              uniqueSignInput++;
              // 缓存图片到本地
              downloadFile({
                url: node,
                name: Date.now() + "_" + uniqueSignInput,
                path: getFileCachedPath({type: 1, account: state.userInfo.workerNo}),
                ext: urlExt,
                done: async (res) => {
                  if (res.state == "success") {
                    let base64 = await getLocalFile({filePath: res.path + res.name, base64: true});
                    // 判断大于2M文件发送
                    try {
                      let thisFile = await getLocalFile({filePath: res.path + res.name, file: true});
                      if (thisFile.size > 2 * 1024 * 1024) {
                        if (state.currentSession.id == currId) {
                          that.imageError(currId, [thisFile]);
                        }
                        resolve("");
                        return;
                      }
                    } catch (e) {}
                    resolve({src: res.path + res.name, base64: base64});
                  } else if (res.state == "error") {
                    try {
                      let img = await dataURLToImage(node);
                      let base64 = await getBase64Image(img);
                      resolve({src: node, base64: base64});
                    } catch (e) {
                      resolve("");
                    }
                  }
                }
              });
            } else {
              let localSrc = getFileCachedPath({type: 6, account: state.userInfo.workerNo}) + Date.now() + uniqueSignInput + "." + (config.imgBase64Reg.test(node) ? getBase64Ext(node) : "png");
              uniqueSignInput++;
              // 将heic转为png
              let isHeicObj = await heicToJpg({localSrc: localSrc, base64: node, done: true});
              // 不是heic直接保存
              if (!isHeicObj.flag) {
                await saveBase64Local({path: localSrc, base64: node});
              } else if (isHeicObj.node) {
                node = isHeicObj.node;
              }
              resolve({src: localSrc, base64: node});
            }
          }
        })
      },
      error: (e) => {
        // 输入框出错
        console.log("editor-error", e);
      },
      keydown: (e) => {
        if (!state.haitInfo.escFlag) {
          store.commit("setEmit", {type: "keydown", value: e});
          if (e.ctrlKey && e.key == "f") {
            commit("setEmit", {type: "searchFocus", value: Date.now()});
          } else if (e.key == "Escape") {
            if (state.router.currentRoute.path == "/child/childChat") {
              // 聊天子窗口关闭
              commit("setWindowClose", getters.getCurrentWindow().cWindow.id);
            } else {
              commit("setWindowMin", getters.getCurrentWindow().cWindow.id);
            }
          }
        } else {
          state.haitInfo.escFlag = false;
        }
      }
    });
  },
  // 激活对应聊天会话编辑器
  activeImEditor: function ({state, commit, dispatch, getters}, info) {
    let {id, active, content} = info;
    if (id) {
      let imEditor = state.imEditor;
      try {
        // 切换路由会重置编辑器，判断是否初始化后在聚焦和渲染内容
        if (imEditor._manager && imEditor._manager.hasEditor) {
          if (!imEditor.isActive(id)) {
            imEditor.activeEditor(id);
            if (!hasContent(imEditor.getContents(id)) && hasContent(getters.getEditorContentMap[id])) {
              imEditor.setContents(id, getters.getEditorContentMap[id]);
            }
          } else if (active) {
            imEditor.activeEditor(id);
          }
          if (content) {
            imEditor.dangerouslyPasteHTML(id, content)
          }
          state.emit.activeImEditor = {id: id};
        } else {
          setTimeout(() => {
            dispatch("activeImEditor", {id: id, active: active, content: content});
          }, 100);
        }
      } catch (e) {
        console.log("activeImEditorErr", e)
      }
    }
  },
  // 获取输入框的数据发送
  inputSend: function ({state, commit, dispatch, getters}, info) {
    if (!navigator.onLine) {
      toast({title: "您已经处于离线状态，无法发送消息，请上线后再次尝试。", type: 2});
      return;
    }
    let contents = state.imEditor.getContents(state.currentSession.id);
    if (contents && contents.ops && contents.ops.length > 0) {
      // 去除最后一个回车
      let sessionId = state.currentSession.id;
      let idInfo = sessionId.split("-");
      let scene = idInfo[0];
      let account = idInfo[1];
      let msgObj = getInputMessage(contents.ops);
      let messages = msgObj.messages;
      let imgCount = msgObj.imgCount;
      let sessionDetail = state.sessions[sessionId]?.detailInfo;

      // 发送错误提示
      if (state.sseDisableSendMap[sessionId]) {
        toast({title: "别急，我正在思考，请稍后发送", type: 2});
        return;
      } else if (sessionDetail?.businessType && sessionDetail.businessType != "AI" && !state.aiTempIdMap[sessionId]?.id) {
        toast({title: "您输入的内容暂时找不到，请在页面上选择场景或能力点", type: 2});
        return;
      } else if (messages.length == 0) {
        toast({title: "不能发送空白信息", type: 2});
        return;
      } else if (messages.length == 1 && messages[0].type == "text" && messages[0].text.length > 4000) {
        toast({title: "字数不能多于4000字", type: 2});
        return;
      } else if (imgCount > 10) {
        toast({title: "单条消息图片超过10张", type: 2});
        return;
      }
      // 清空输入框
      state.imEditor.clear(state.currentSession.id);
      // 存在发送文件标识则过滤
      let fileMsgFlag = "";
      try {
        for (let i = 0; i < messages.length; i++) {
          if (messages[i].sendFileFlag) {
            if (messages.length > 1) {
              uniqueSignCount++;
              fileMsgFlag = MD5(`sign_${scene}_${account}_${Date.now()}_${uniqueSignCount}`);
              // 记录发送标识次数，用于发送ai问题请求
              remote.store.state.fileMsgFlagMap[fileMsgFlag] = {
                type: "file",
                count: 1,
                msgs: [],
              }
            }
            remote.store.dispatch("sendMsgFile", {sessionId: sessionId, filePath: messages[i].filePath, fileMsgFlag: fileMsgFlag});
            messages.splice(i, 1);
            i--
          }
        }
      } catch (e) {
        console.log("fileMsgFlagMapErr", e);
      }
      if (messages.length > 0) {
        dispatch("doSendMsg", {sessionId: sessionId, messages: messages, fileMsgFlag: fileMsgFlag});
      }
    }
  },
  // 主窗口/子窗口调用发送方法
  doSendMsg: function ({state, commit, dispatch}, info) {
    if (isMainWin()) {
      dispatch("sendMsg", info);
    } else {
      remote.store.dispatch("sendMsg", info);
    }
  },
  // 处理发送文件消息
  dealFiles: async function ({state, commit, dispatch}, info) {
    let {id, files} = info;
    let to = (id || "").split("-")[1];
    let isAi = new RegExp(config.ai).test(to);
    // 发送文件
    for (let i = 0; i < files.length; i++) {
      let file = files[i];
      let uniqueSign = file.uniqueSign;
      let fileName = file.name || "";
      let fileExt = fileName.lastIndexOf(".") != -1 ? fileName.slice(fileName.lastIndexOf(".") + 1) : "";
      if (file.size == 0) {
        alert({content: "不能发送空文件"});
        return;
      } else if (file.size / 1024 / 1024 > 500 && state.userInfo.workerNo != "346765") {
        alert({content: "抱歉,当前乐聊暂不支持发送大于500M文件"});
        return;
      } else if (isAi && (file.size / 1024 / 1024 > 10 || !config.aiFileReg.test(fileExt))) {
        alert({content: "仅支持docx、PDF、xlsx、Markdown、txt、json，单个文件不得超过10m"});
        return;
      }
      // 不存在本地的文件先保存
      let filePath = file.filePath || file.path;
      if (!filePath) {
        let base64 = await fileToDataURL(file);
        let fileExt = getFileExt(file.name);
        filePath = getFileCachedPath({account: state.userInfo.workerNo, type: 3}) + (config.imgTypeReg.test(fileExt) ? `乐聊图片` : "") + `${Date.now()}.${fileExt}`;
        await saveBase64Local({path: filePath, base64: base64});
        setTimeout(() => {
          dispatch("sendFileAndDoc", {type: "file", id: id, to: to, param: {sessionId: id, filePath: filePath, fileName: fileName, uniqueSign: uniqueSign}});
        }, 500);
      } else {
        dispatch("sendFileAndDoc", {type: "file", id: id, to: to, param: {sessionId: id, filePath: filePath, fileName: fileName, uniqueSign: uniqueSign}});
      }
    }
  },
  // 设置文件和乐文档消息
  sendFileAndDoc: async function ({state, commit, dispatch, getters}, info) {
    let {type, id, to, param} = info;
    let rangeIndex = 0;
    if (new RegExp(config.ai).test(to)) {
      // 数字人都允许输入文件
      let content = state.imEditor.getContents(id);
      // 判断输入框是否存在乐文档/文件
      let preIndex = 0;
      for (let i = 0; i < content?.ops?.length; i++) {
        if (content?.ops?.[i]?.insert?.imHaitImageNew || content?.ops?.[i]?.insert?.imDefaultImage?.key == "file") {
          // 存在则删除
          state.imEditor.deleteText(id, preIndex, 1);
          if (preIndex == 0) {
            rangeIndex--;
          }
        } else {
          preIndex += (content?.ops?.[i]?.insert?.length || 0);
        }
      }
      if (type == "doc") {
        // 删除对应@数据后插入首位
        param.range.index += rangeIndex;
        param.insertRange = {index: 0, length: 0};
        state.imEditor.insertHaitImageNew(param);
      } else if (type == "file") {
        state.imEditor.insertDefaultImageBlot({
          id: id,
          range: {index: 0, length: 0},
          insertRange: {index: 0, length: 0},
          img: strToImg("文件：" + param.fileName),
          key: "file",
          value: encodeURIComponent(JSON.stringify(param))
        });
      }
    } else {
      if (type == "doc") {
        state.imEditor.insertHaitImageNew(param);
      } else if (type == "file") {
        remote.store.dispatch("sendMsgFile", {sessionId: id, filePath: param.filePath, uniqueSign: param.uniqueSign});
      }
    }
  },
  // 发送文件-子窗口调用父窗口方法发送
  sendMsgFile: async function ({state, commit, dispatch}, info) {
    let param = {
      path: path.dirname(info.filePath) + "\\",
      name: path.basename(info.filePath)
    };
    let file = await getLocalFile(param);
    dispatch("sendMsg", {sessionId: info.sessionId, uniqueSign: info.uniqueSign, fileMsgFlag: info.fileMsgFlag, messages: dealInputMessage({type: "file", messages: [], file: file})});
  },
  // 查询设置消息记录,type-1设置-2查询
  setHistory: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      // 多次回调
      let preResolve = info.resolve;
      delete info.resolve;
      let idInfo = info.id.split("-");
      let scene = idInfo[0];
      let account = idInfo[1];
      let sessions = state.sessions[info.id] || state.sessionsTemp[info.id];
      if (info.type == 1 && sessions && sessions.notMore) {
        resolve({err: "", obj: {msgs: []}});
        return;
      }
      let res = {}
      let msgInfo = {};
      let thisLimit = info.limit || 20;
      if (scene == "superTeam" || (state.router.currentRoute.path != "/index/chat" && scene == "team" && !state.teams[account])) {
        msgInfo = await dispatch("queryCloudMessage", {text: "", scene: scene, to: account, page: info.page || 1, beginTime: info.notStart ? "" : info.beginTime, endDate: info.notEnd ? "" : info.endTime, limit: thisLimit, reverse: info.reverse});
        await Promise.all(msgInfo.p);
        res.obj = {msgs: msgInfo.list, limit: thisLimit};
      } else {
        let nimParam = {
          scene: scene,
          to: account,
          beginTime: info.beginTime,
          endTime: info.endTime,
          limit: thisLimit,
          reverse: info.reverse || false
        };
        if (info.msgTypes) {
          nimParam.msgTypes = info.msgTypes;
        }
        res = await remote.store.getters.getNim.getHistoryMsgs(nimParam);
        // 换设备未读数异常，需要获取完历史记录后，再次setCurrSession上传会话记录
        if (info.type == 1) {
          let currentId = state.currentSession.id;
          remote.store.getters.getNim.setCurrSessionState({type: 1, id: info.id});
          if (currentId != state.currentSession.id && state.currentSession.timer) {
            remote.store.getters.getNim.setCurrSessionState({type: 1, id: currentId});
          }
        }
      }

      let {err, obj} = res;
      if (err) {
        if (preResolve) {
          preResolve(res);
        } else {
          resolve(res);
        }
        toast({title: err.message, type: 2});
        if (err.code == 403) {
          dispatch("isRemoveSession", info.id);
        }
        console.log("getHistoryMsgsErr", obj);
        if (info.type == 1) {
          state.msgsLoading[info.id] = false;
        }
        return;
      }
      let msgs = deepClone(obj.msgs);
      // 记录查询消息记录的id
      try {
        console.log("getHistoryMsgsDetails", msgs.map(item => {return item.idServer}).toString());
      } catch (e) {}
      // 过滤自己消息过滤语音已读提示,过滤群通知消息
      if (account == state.userInfo.workerNo || scene != "p2p") {
        for (let i = 0; i < msgs.length; i++) {
          let thisMsg = msgs[i];
          // 过滤自己消息过滤语音已读提示
          if (account == state.userInfo.workerNo && thisMsg.custom) {
            if (typeof thisMsg.custom == "string") {
              try {
                thisMsg.custom = JSON.parse(thisMsg.custom);
              } catch (e) {
              }
            }
            if (thisMsg.custom.type == "audio" || thisMsg.custom.type == "hide") {
              msgs.splice(i, 1);
              i--;
            }
          } else if (scene != "p2p" && thisMsg.attach && thisMsg.type == "notification") {
            // 过滤群通知消息
            // 获取当前成员在群信息
            let thisUserTeamInfo = await dispatch("getTeamMembers", {id: thisMsg.to, account: state.userInfo.workerNo});
            let isThisOwner = getters.getTeams({id: thisMsg.to}).owner == state.userInfo.workerNo;
            let isTeamManager = thisUserTeamInfo.obj && /manager|owner/.test(thisUserTeamInfo.obj.type);
            // 不存在通知解析删除该消息
            let hasText = getters.getNotification(thisMsg);
            let isNotify = isNotificationRole(thisMsg, isThisOwner, isTeamManager);
            if (!isNotify || (isNotify && !hasText)) {
              msgs.splice(i, 1);
              i--;
            }
          }
        }
        // 消息被过滤完后，继续往上加载
        let localMsg = state.msgs[info.id] || [];
        if (info.type == 1) {
          if ((msgs.length + localMsg.length < 20 || msgs.length == 0) && (obj.msgs.length == obj.limit || (msgInfo.pages && msgInfo.pages > (info.page || 0)))) {
            info.endTime = obj.msgs[obj.msgs.length - 1]?.time;
            info.page = (info.page || 0) + 1;
            info.notEnd = true;
            await dispatch("setMsgs", {id: info.id, msgs: msgs});
            // 保存上一次回调
            info.resolve = preResolve || resolve;
            dispatch("setHistory", info);
            return;
          }
        } else if (info.type == 2) {
          // 查询消息记录通知被过滤完继续向上获取
          msgs = msgs.concat(info.msgs || []);
          if (msgs.length < 20 && obj.msgs.length != 0 && obj.msgs.length == obj.limit) {
            if (info.startTime || obj.beginTime) {
              info.startTime = obj.msgs[obj.msgs.length - 1]?.time;
            } else {
              info.endTime = obj.msgs[obj.msgs.length - 1]?.time;
            }
            // 超过5次后不获取通知类型消息
            if (info.count > 5) {
              info.msgTypes = ["custom", "text", "image", "audio", "video", "geo", "file", "tip", "robot", "g2"];
            }
            info.msgs = msgs;
            info.count = info.count || 0;
            info.count++;
            // 保存上一次回调
            info.resolve = preResolve || resolve;
            dispatch("setHistory", info);
            return;
          }
        }
      }
      // 设置消息记录
      if (info.type == 1) {
        if ((!msgInfo.pages && obj.msgs.length < obj.limit) || (msgInfo.pages && msgInfo.pages <= info.page)) {
          sessions.notMore = true;
          if (obj.msgs.length == 0) {
            sessions.noMsg = true;
          }
          commit("setUpdateSessionId", info.id);
        }
        dispatch("setMsgs", {id: info.id, msgs: msgs, history: true});
      }
      // 过滤消息后重新加载回调
      if (preResolve) {
        preResolve({err: "", obj: {msgs: msgs}});
      } else {
        resolve({err: "", obj: {msgs: msgs, page: info.page}});
      }
    });
  },
  // 查看前后消息-前后25条消息
  showRecord: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let {time, sessionId} = info;
      time = parseInt(time);
      // 大于一年直接提示
      if (new Date().getTime() + getters.getDiffTime - time > 365 * 24 * 60 * 60 * 1000) {
        toast({title: "抱歉，目前只能查询近一年的聊天记录", type: 2});
        resolve();
        return;
      }
      loading();
      let param = {id: sessionId, type: 2, limit: 25, endTime: time};
      let p1 = await dispatch("setHistory", param);
      loading().hide();
      if (p1.err) {
        resolve();
        return;
      }
      loading();
      delete param.endTime;
      param.beginTime = time;
      param.reverse = true;
      let p2 = await dispatch("setHistory", param);
      loading().hide();
      if (p2.err) {
        resolve();
        return;
      }
      let list = [];
      if (!p1.err && p1.obj && p1.obj.msgs) {
        list = list.concat(p1.obj.msgs);
      }
      if (!p2.err && p2.obj && p2.obj.msgs) {
        list = list.concat(p2.obj.msgs);
      }
      let p = [];
      list.map(item => {
        p.push(dispatch("setMsgField", {item: item}));
      });
      resolve({list: list, p: p});
    });
  },
  // 搜索聊天记录
  queryCloudMessage: async function ({state, commit, dispatch, getters}, info) {
    let {text, scene, to, page, limit, beginTime, endDate, reverse} = info;
    loading();
    let queryParam = {
      toTarget: to,
      fromAccount: scene == "p2p" ? (info.queryWorkerNo || state.userInfo.workerNo) : "",
      keyWord: text,
      isFromNick: true,
      startDate: beginTime || *************,
      endDate: endDate || 0,
      page: page || 1,
      rows: limit || 50,
      sortord: reverse ? "ase" : "",
    };
    if (scene != "p2p") {
      queryParam.joinTime = state.userTeamInfos[to]?.joinTime;
    }
    let res = await queryCloudMessageApi({
      msgBody: JSON.stringify(queryParam)
    })
    loading().hide();
    let list = [];
    let p = [];
    let total = 0;
    let pages = 0;
    if (!res.success) {
      toast({title: res.errorMsg, type: 2});
    }
    if (res && res.data && res.data.data) {
      res.data.data.map(item => {
        let thisMsg = item.msgJson;
        let msg = {
          time: Number(thisMsg.msgTimestamp),
          type: thisMsg.msgType.toLowerCase(),
          text: thisMsg.body,
          custom: thisMsg.attach,
          idServer: thisMsg.msgidServer,
          idClient: thisMsg.msgidClient,
          from: thisMsg.fromAccount,
          to: thisMsg.to,
          scene: thisMsg.convType.toLowerCase() == "super_team" ? "superTeam" : thisMsg.convType.toLowerCase(),
          fromNick: thisMsg.fromNick,
          page: page,
          status: "success"
        }
        msg.sessionId = `${msg.scene}-${msg.to}`;
        if (msg.type == "file") {
          msg.file = msg.custom;
        }
        if (msg.custom && (msg.custom.type == "multi" || msg.custom.type == "ai-msg")) {
          msg.content = msg.custom;
        }
        if (msg.type == "picture") {
          msg.type = "image";
          msg.file = msg.custom;
        }
        if (msg.type != "notification") {
          list.unshift(msg);
        }
      });
      list.map(item => {
        p.push(dispatch("setMsgField", {item: item}));
      });
      total = res.data.total;
      pages = res.data.pages;
    }
    return new Promise(resolve => resolve({list: list, p: p, total: total, pages: pages}));
  },
  // 初始化的时候获取自己会话的500条数据用于判断语音漫游
  initAudioTipMsg: async function ({state, commit, dispatch}, info) {
    let res = await remote.store.state.nimSDK.getHistoryMsgs({
      scene: "p2p",
      to: state.userInfo.workerNo,
      endTime: info,
      msgTypes: "tip",
    });
    if (!res.err) {
      let msgs = res.obj.msgs;
      // 未满500条且还能获取更多则继续获取
      if (state.audioTipMsg.length <= 400 && msgs.length == 100) {
        dispatch("initAudioTipMsg", msgs[msgs.length - 1].time);
      }
      dispatch("setAudioTipMsg", msgs);
    }
  },
  // 设置语音已读数据
  setAudioTipMsg: function ({state, commit, dispatch}, info) {
    return new Promise(resolve => {
      let audioTipMsg = state.audioTipMsg;
      info.map(item => {
        if (item.custom) {
          if (typeof item.custom == "string") {
            try {
              item.custom = JSON.parse(item.custom);
            } catch (e) {
            }
          }
          if (item.custom.type == "audio") {
            audioTipMsg.push(item);
          }
        }
      });
      // 最多保存500条
      if (audioTipMsg.length > 500) {
        audioTipMsg.splice(0, audioTipMsg.length - 500);
      }
      audioTipMsg.sort((a, b) => {return a.time - b.time});
      state.audioTipMsg = audioTipMsg;
      resolve();
    })
  },
  // 判断输入框内容是否删除会话
  isRemoveSession: function ({state, commit, dispatch, getters}, info) {
    if (state.imEditor.hasContents(info)) {
      alert({
        content: "输入区存在待发出的消息，确认关闭会话窗口？",
        done: (type) => {
          if (type == 1) {
            dispatch("removeSession", info);
          }
        }
      });
    } else {
      dispatch("removeSession", info);
    }
  },
  // 删除会话
  removeSession: function ({state, commit, dispatch, getters}, info) {
    state.nimSDK.deleteLocalSession({
      id: info,
      isLocal: true,
    }).then((res) => {
      let {err, obj} = res;
      if (err) {
        toast({title: "删除会话失败:" + err.message, type: 2});
        return;
      }
      if (state.router.currentRoute.path == "/index/chat") {
        dispatch("changeNextSession", info);
      } else {
        // 删除在其他页面的时候的临时会话
        if (state.currentSession.tempId == info) {
          commit("setUpdateSessionId", info);
        }
      }
      delete state.sessions[info];
      // 删除特别关心、@我弹窗
      delete state.haitMsgMap[info];
      delete state.concernMsgMap[info];
      // 删除群成员
      let idInfoList = info?.split("-");
      delete state.teamMembers[idInfoList[1]];
      // 关闭子窗口
      if (getChildWin("chat-" + info)) {
        getChildWin("chat-" + info).close();
      }
      state.updateSessionsTime = Date.now();
      // 关闭会话删除@消息和特别关心缓存
      commit("setHaitMsgMap", {type: "remove", id: info});
      commit("setConcernMsgMap", {type: "remove", id: info});
    });
  },
  // 关闭/打开子窗口切换到下个会话
  changeNextSession: function ({state, commit, dispatch, getters}, info) {
    // 判断关闭会话状态-客户列表5，其他情况-1
    let sessionList = deepClone(state.currSessionList);
    let key = sessionList.findIndex(item => {return info == item.id});
    // 会话大于1且是当前会话关闭，重置会话为下一个
    if (sessionList.length > 1 && info == state.currentSession.id) {
      let nextSession = sessionList[key + 1] || sessionList[key - 1];
      dispatch("setCurrentSession", {id: nextSession.id});
    } else if (sessionList.length == 1 || sessionList.length == 0) {
      // 关闭最后一个会话清除当前会话
      state.nimSDK.setCurrSessionState({type: 2});
      state.currentSession = {};
    }
  },
  // 设置当前会话 info-会话id
  setCurrentSession: async function ({state, commit, dispatch, getters}, info) {
    if (!info || !info.id || (isMainWin() && !state.nimSDK.disconnect)) {
      return;
    }
    // 关闭对应通知消息
    remote.store.dispatch("setNotification", {type: "del", session: {id: info.id}});
    let {id, type, focus, content} = info;
    // 不是主会话页面跳转回主会话
    let currPath = state.router.currentRoute.path;
    // 公众号切回主会话显示原会话id
    if (state.currentSession.tempId && currPath == "/index/chat") {
      id = state.currentSession.tempId;
      state.currentSession.tempId = "";
      state.sessionsTemp = {};
    }
    let idInfo = id.split("-");
    let scene = idInfo[0];
    let account = idInfo[1];
    let notInsert = false;
    let tempId = "";
    // 公众号打开会话不插入主会话
    let insertFlag = currPath == "/index/mailList" && info.type != "open";
    // 打开会话
    let isOpen = type != "click" && type != "focus";
    // 触发点击
    if (type == "open") {
      commit("setEmit", {type: "click", value: {clickOpen: true, id: id}});
    }
    if (insertFlag) {
      notInsert = true;
      if (!isSubOrSer(state.currentSession.id)) {
        tempId = state.currentSession.id;
      }
    }
    if (isOpen && ((currPath == "/child/childChat" && id != state.router.currentRoute.query.id) || currPath == "/child/netConnectNew" ||
      currPath == "/child/searchMsg")) {
      // 子窗口打开会话
      remote.store.dispatch("setCurrentSession", {id: id, type: "open", content: content});
      remote.Window.get().focus();
      return;
    } else if (!/\/child\//.test(currPath)) {
      // 非子窗口
      if (!insertFlag) {
        // 跳转回聊天页面
        if (currPath != "/index/chat") {
          state.router.push({path: "/index/chat"});
        }
        // 打开会话跳转对应列表
        let selSessionTab = "all";
        let isFcw = isFcwList("", id, state.teams?.[account]);
        if (isFcw) {
          selSessionTab = "customer";
        }
        if (id == "p2p-customerAccount") {
          dispatch("setFcwOnlineEvent");
        }
        // 客户和普通会话切换、或者通过点击跳转
        if ((isOpen && ((isFcw && state.sessionTab != "customer") || (!isFcw && state.sessionTab == "customer"))) || type == "open") {
          if (/customerClassify-/.test(state.sessionTab) && type != "open") {
            selSessionTab = state.sessionTab;
          } else if (isFcw) {
            selSessionTab = "customer";
          }
          state.sessionTab = selSessionTab;
          commit("setEmit", {type: "selSessionTab", value: {key: selSessionTab, id: id, value: Date.now()}});
        }
      }
      // 聚焦子窗口
      let childWin = getChildWin("chat-" + id);
      if (childWin && isMainWin()) {
        childWin.window.store.commit("setWindowCancelMin", childWin.cWindow.id);
        childWin.window.store.dispatch("setCurrentSession", {id: id, type: "open"});
        return;
      }
    }
    // 不存在会话则插入会话
    if (!state.sessions[id] && !notInsert && currPath != "/child/childChat") {
      let res = await state.nimSDK.insertLocalSession({
        scene: scene,
        to: account
      });
      // 云信本地数据库缓存和服务器不一致 删除会话重新加入
      if (res.err) {
        // 云信存在bug直接加入会话
        if (res.err.code == "Session_Exist") {
          res.err.event.session.insert = true;
          res.err.event.session.updateTime = Date.now();
          commit("setSessions", res.err.event.session);
        } else {
          console.log("insertLocalSessionErr", res);
          alert({
            content: `插入本地会话失败，${res.err.message}-${res.err.code}，请重试`,
            done: type => {
              if (type == 1) {
                state.nimSDK.deleteLocalSession({
                  id: id,
                  isLocal: true,
                });
              }
            }
          });
          return;
        }
      } else {
        res.obj.session.insert = true;
        commit("setSessions", res.obj.session);
        // 滚动到当前会话
        setTimeout(() => {
          commit("setEmit", {type: "scrollCurrentSession", value: Date.now()});
        }, 100);
      }
    } else if (type == "open") {
      // 滚动到当前会话
      commit("setEmit", {type: "scrollCurrentSession", value: Date.now()});
    }
    // 临时会话-如公众号页面
    if (notInsert && !state.sessionsTemp[id]) {
      state.sessionsTemp[id] = {
        scene: scene,
        to: account,
        id: id,
        notInsert: true,
        detailInfo: state.allSubAndSer[account] || state.persons[account] || state.teams[account] || info.detailInfo || {}
      }
    }
    // 打开查看不在群的消息重置消息记录
    if (info.detailInfo?.notInTeam) {
      delete state.msgs[`${scene}-${account}`];
      delete state.msgsLoading[id];
      delete state.sessionsTemp[id].notMore;
    }
    // 切换会话
    if (id != state.currentSession.id) {
      // 暂停语音
      if (state.audioObj.idServer) {
        remote.store.dispatch("setAudioObj", {});
      }
      // 回到公众号消息数量过多则删除
      if (id != state.currentSession.id && state.msgs[id]?.length > 400) {
        state.msgs[id] = state.msgs[id].slice(-100);
      }
    }
    // 不是当前会话或是当前会话非重置状态，重新选中当前会话
    if (id != state.currentSession.id || !state.currentSession.timer || currPath == "/child/childChat" || focus) {
      // 切换会话清除上个会话的@和特别关心消息
      if (id && state.currentSession.id && id != state.currentSession.id && (state.haitMsgMap[state.currentSession.id] || state.concernMsgMap[state.currentSession.id])) {
        dispatch("setMsgTipsClear", {type: "remove", id: state.currentSession.id});
      }
      state.currentSession.id = id;
      if (!state.currentSession.timer) {
        state.currentSession.timer = true;
      }
      remote.store.getters.getNim.setCurrSessionState({type: 1, id: id});
      commit("setMsgRead", {id: id});
      commit("setLocalUnreadMap", {type: "del", id: id});
      dispatch("setMsgTipsClear", {type: "clearTips", id: state.currentSession.id});
    }
    // 重置上个定时器
    if (state.currentSession.timer) {
      clearTimeout(state.currentSession.timer);
    }
    // 10s重置当前会话
    let timer = setTimeout(() => {
      commit("setCancelCurrentSession");
    }, 10 * 1000);
    state.currentSession.id = id;
    state.currentSession.timer = timer;
    state.currentSession.tempId = state.currentSession.tempId || tempId;
    // 群助手
    if (account == config.helperAccount) {
      return;
    }
    // 不存在历史记录则获取
    if ((!state.msgs[id] || (state.msgs[id].length < 20 && scene != "superTeam")) && !state.msgsLoading[id]) {
      state.msgsLoading[id] = true;
      let historyParam = {id: id, type: 1};
      // 存在最后一条消息则继续向上请求
      if (state.msgs[id] && state.msgs[id][0] && state.msgs[id][0].time) {
        historyParam.endTime = state.msgs[id][0].time;
        historyParam.page = state.msgs[id][0].page + 1;
        historyParam.notEnd = true;
      }
      remote.store.dispatch("setHistory", historyParam);
    }
    // 设置最近联系人/群
    commit("setRecentContactsMap", id);
    // 获取当前用户的最新服务器数据,切换会话半小时更新一次
    if (scene == "p2p" && (!isNaN(account) || new RegExp(config.ai).test(account)) &&
      (!state.persons[account] || !state.persons[account].updateTime ||
        (state.persons[account] && (Date.now() - state.persons[account].localAddTime > 30 * 60 * 1000)))) {
      dispatch("updatePersons", {account: account});
      // 同时更新最近联系人列表
      commit("setRecentContactsMap");
    }
    // 获取被屏蔽消息，一分钟更新一次
    if (!state.antispamMap[id] || Date.now() - state.antispamMap[id].updateTime > 1 * 60 * 1000) {
      if (!state.antispamMap[id]) {
        state.antispamMap[id] = {};
      }
      state.antispamMap[id].updateTime = Date.now();
      querySensitiveApi({
        msgBody: JSON.stringify({
          toTarget: account,
          fromAccount: scene == "p2p" ? state.userInfo.workerNo : "",
        })
      }).then(res => {
        if (res.data && res.data.data && res.data.data.length > 0) {
          commit("setAntispamMap", {id: id, data: res.data.data})
          // 通知更新消息
          commit("setEmit", {type: "reloadMsg", value: {id: id, time: Date.now()}});
        }
      });
    }
    // 请求服务器数据获取依赖数据
    if (state.router.currentRoute.path == "/index/chat") {
      debounce({
        timerName: "setServerConfig",
        time: 1000,
        fnName: function () {
          dispatch("setServerConfig");
        }
      });
    }
    // 插入输入框内容
    if (content) {
      dispatch("activeImEditor", {id: id, content: content, type: type});
    }
  },
  // 重置显示会话
  resetCurrSession: function ({state, commit, dispatch, getters}, info) {
    state.currentSession = {};
    getters.getNim.setCurrSessionState({type: 2});
  },
  // 设置云信黑名单
  setNimBlacklist: async function ({state, commit, dispatch}, info) {
    let {account, flag} = info;
    let res = await state.nimSDK.markInBlacklist({
      account: account,
      isAdd: flag,
    });
    if (res.err) {
      toast({title: (res.err.message || "操作失败请重试") + res.err.code, type: 2});
      return;
    }
    dispatch("setBlacklist", res.obj);
    remote.store.commit("setUpdateSessionId", `p2p-${account}`);
  },
  // 设置黑名单列表
  setBlacklist: async function ({state, commit, dispatch}, info) {
    // 单个黑名单对象
    if (info && info.account) {
      let sessionId = "p2p-" + info.account;
      if (!info.isAdd) {
        // 移出黑名单-加入会话
        delete state.blackMap[info.account];
        if (!getChildWin("chat-" + sessionId)) {
          dispatch("setCurrentSession", {id: sessionId});
        }
        return;
      } else {
        // 加入黑名单-移除置顶和删除会话-云信新sdk加入黑名单会默认移除置顶状态，不需要手动移除
        let param = {type: 2, key: "session_top_setting", remark: "取消置顶", operation: "delete", value: sessionId, notTop: true};
        dispatch("setModifySettings", param);
        dispatch("removeSession", sessionId);
      }
      info = [info.record];
    }
    if (Array.isArray(info)) {
      // 遍历黑名单列表
      for (let i = 0; i < info.length; i++) {
        let item = info[i];
        state.blackMap[item.account] = item;
      }
    }
  },
  // 批量获取人员信息-不可多次调用会造成卡顿 info.account可谓单个工号字符串、也可以为工号数组,doneFlag为需要回调
  getPersons: function ({state, commit}, info) {
    return new Promise(resolve => {
      setTimeout(() => {
        let workerMap = {};
        let person = {};
        let isArray = true;
        let account = info.account;
        if (!Array.isArray(info.account)) {
          isArray = false;
          account = [account];
        }
        account.map(item => {
          if (state.persons[item] || remote.store.state.nimFriend[item]) {
            person[item] = state.persons[item] || remote.store.state.nimFriend[item];
            // 云信好友数据
            if (!remote.store.state.nimFriend[item]?.detailInfo) {
              person[item].detailInfo = remote.store.state.nimFriend[item];
            }
          } else {
            workerMap[item] = item;
            person[item] = {
              account: item,
              workerNo: item,
              name: item,
              avatar: "/img/default/p2p.png"
            };
          }
        });
        // 不存在的人员信息异步请求
        if (Object.keys(workerMap).length > 0) {
          commit("setPersons", {
            workerMap: workerMap,
            done: () => {
              // 需要等待结果回调
              if (info.doneFlag) {
                let awaitPerson = {};
                account.map(item => {
                  if (state.persons[item] || remote.store.state.nimFriend[item]) {
                    awaitPerson[item] = state.persons[item] || remote.store.state.nimFriend[item];
                    // 云信好友数据
                    if (!remote.store.state.nimFriend[item]?.detailInfo) {
                      awaitPerson[item].detailInfo = deepClone(remote.store.state.nimFriend[item]);
                    }
                  } else {
                    awaitPerson[item] = {
                      account: item,
                      workerNo: item,
                      name: item,
                      avatar: "/img/default/p2p.png"
                    };
                  }
                });
                resolve(isArray ? deepClone(awaitPerson) : deepClone(awaitPerson[account]));
              }
            }
          });
          // 不需要等待请求回调
          if (!info.doneFlag) {
            resolve(isArray ? deepClone(person) : deepClone(person[account]));
          }
        } else {
          resolve(isArray ? deepClone(person) : deepClone(person[account]));
        }
      }, 0);
    });
  },
  // 获取群成员
  getTeamMembers: function ({state, commit, dispatch, getters}, info) {
    return new Promise(resolve => {
      let {id, account} = info;
      if (!id) {
        resolve({err: "", obj: []});
        return;
      }
      let teamMembers = state.teamMembers[id];
      if (Array.isArray(teamMembers)) {
        // 返回群结果
        resolve(getters.getTeamMembersLocal({id: id, account: account, err: "", teamMembers: state.teamMembers[id]}));
        return;
      }
      setTimeout(() => {
        // 不存在则请求
        if (!teamMembers) {
          // 返回请求状态
          state.teamMembers[id] = remote.store.getters.getNim.getTeamMembers({id: id});
          state.teamMembers[id].then(res => {
            if (!res.err) {
              state.teamMembers[id] = res.obj || [];
            }
            if (state.teamMembers[id]?.length > 0) {
              state.updateMemberTeamInfo = {id: id, time: Date.now()};
            }
            resolve(getters.getTeamMembersLocal({id: id, account: account, err: res.err, teamMembers: res.obj || []}));
          });
        } else if (!Array.isArray(teamMembers)) {
          // 返回请求状态
          teamMembers.then(res => {
            resolve(getters.getTeamMembersLocal({id: id, account: account, err: res.err, teamMembers: state.teamMembers[id]}));
          });
        }
      }, 0);
    });
  },
  // 获取当前用户在群信息
  getUserTeamInfo: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async (resolve, reject) => {
      if (state.userTeamInfos[info.id]) {
        resolve(state.userTeamInfos[info.id]);
      } else {
        let res = await remote.store.state.nimSDK.getTeamMemberByTeamIdAndAccount({
          teamId: info.id,
          account: state.userInfo.workerNo
        });
        if (res.obj?.[state.userInfo.workerNo]) {
          state.userTeamInfos[info.id] = res.obj[state.userInfo.workerNo];
          resolve(state.userTeamInfos[info.id]);
        } else {
          resolve({});
        }
        // 查询小乐是否在群里
        let xlRes = await remote.store.state.nimSDK.getTeamMemberByTeamIdAndAccount({
          teamId: info.id,
          account: config[config.env].robotEmpNo
        });
        if (xlRes.obj?.[config[config.env].robotEmpNo]) {
          state.userTeamInfos[info.id].hasRobot = true;
        }
      }
    });
  },
  // 设置消息本地数据回调
  setFileLocal: function ({state, commit}, info) {
    return new Promise(async (resolve, reject) => {
      let {file, item} = info;
      if (!item) {
        item = {};
      }
      let url = file.url;
      let cacheSize = file.size;
      // 缓存图片路径
      let cacheUrl = url;
      // 文件类型
      let ext = file.ext == "unknown" ? "" : file.ext;
      // 缓存图片后缀
      let cacheExt = ext;
      // 是否图片-存在ext为空type为图片png等场景
      let isImage = config.imgTypeReg.test(ext) || config.imgTypeReg.test(file.type);
      // 补上后缀
      if (isImage && !ext) {
        ext = "png";
      }
      // 是否视频类型
      let isVideo = file.videoPic;
      if (isVideo) {
        // 视频md5
        file.videoMD5 = MD5(cacheUrl);
        cacheUrl += (/\?/.test(file.url) ? "&" : "?") + "vframe";
        cacheExt = "png";
      }
      let thisMd5 = MD5(cacheUrl);
      file.fileMD5 = MD5(cacheUrl);
      // 设置图片缓存队列,5M以下的才缓存，和缓存视频封面，且非首次登录的离线漫游消息
      if (((isImage && file.size < 5 * 1024 * 1024 && !state.imgQueue[thisMd5]) || isVideo) && !isHalfYearAgo(item.item) && !(item.onmsg != 1 && item.asyncCount == 0)) {
        state.imgQueue[thisMd5] = {md5: thisMd5, url: cacheUrl, size: cacheSize, ext: cacheExt || "png", time: Date.now(), info: file};
      }
      // 名字不存在后缀补上后缀
      if (ext && !new RegExp(`.${ext}`).test(file.name)) {
        file.name += `.${ext}`;
      }
      // 文件显示名(去除后缀)
      file.showName = (file.name || "").replace(new RegExp("(.*)." + ext), "$1");
      if (isVideo) {
        // 获取视频本地信息
        let videoInfo = {};
        if (!isHalfYearAgo(item.item)) {
          videoInfo = await loadCache({url: url, size: file.size, ext: ext, fileDB: state.fileDB, isDownLoad: false});
        }
        file.videoInfo = videoInfo.fileInfo || {};
      }
      // 获取文件本地信息
      let fileInfo = {};
      if (!isHalfYearAgo(item.item)) {
        fileInfo = await loadCache({url: cacheUrl, size: file.size, ext: cacheExt, fileDB: state.fileDB, isDownLoad: false});
      }
      file.fileInfo = fileInfo.fileInfo || {};
      resolve(file);
    });
  },
  // 打开/下载文件 item消息对象 openType-1固定调用下载-2批量下载(不下载已经存在的)-3下载不提示
  openFile: function ({state, commit, dispatch}, info) {
    let {item, openType, done} = info;
    let fileInfo = item.file.fileInfo || {};
    if (item.type == "video") {
      // 视频信息
      fileInfo = item.file.videoInfo || {};
    }
    // 原始消息
    let msgItem = {};
    if (state.msgs[state.currentSession.id]) {
      msgItem = state.msgs[state.currentSession.id][state.msgs[state.currentSession.id].findIndex(item1 => item1.idServer == (item.idServer || item.msgidServer))] || {};
    }
    if (fileInfo.path) {
      // 存在文件
      let fileLocal = fileInfo.path + fileInfo.name;
      let isExistsFile = fs.existsSync(fileLocal);
      // 存在默认打开
      if (isExistsFile) {
        // 下载类型不打开
        if (!openType || openType == 3) {
          // 打开文件-文件小于50M，图片太大会闪退
          if ((item.type == "video" || (item.file?.isImage && item.file.size < 50 * 1024 * 1024)) && state.settings[config.settings.type10] != 1) {
            // 视频本地打开
            openViewer([{src: fileLocal, dataSrc: item.file.url, size: item.file.size, w: item.file.w, h: item.file.h, ext: item.file.ext}], 0, item.file.w, item.file.h, item.type);
          } else {
            openLocalFile(fileLocal);
          }
          console.log("openFile", fileLocal);
        } else if (openType == 1) {
          // 下载文件
          dispatch("downloadFile", {item: item, param: {reDownLoad: true}, msgItem: msgItem, done: done});
        }
      } else {
        // 不存在删除本地缓存
        state.fileDB.del("md5", fileInfo.md5);
        if (item.type == "video") {
          item.file.videoInfo = {};
          msgItem.file && (msgItem.file.videoInfo = {});
        } else {
          item.file.fileInfo = {};
          msgItem.file && (msgItem.file.fileInfo = {});
        }
        if (!openType) {
          alert({
            content: "该文件已被删除，是否重新下载",
            done: type => {
              if (type == 1) {
                // 下载文件
                dispatch("downloadFile", {item: item, msgItem: msgItem, done: done});
              }
            }
          });
        } else {
          // 下载文件
          dispatch("downloadFile", {item: item, msgItem: msgItem, done: done});
        }
      }
    } else {
      // 下载文件
      dispatch("downloadFile", {item: item, msgItem: msgItem, done: done});
    }
  },
  // 打开文件夹/另存为 item消息对象 openType-1固定调用另存为 thisElm当前图片元素 done回调
  openFolder: async function ({state, commit, dispatch}, info) {
    let {item, openType, thisElm, done, msgItem, globalEmit} = info;
    let fileInfo = item.file.fileInfo || {};
    if (item.type == "video") {
      // 视频信息
      fileInfo = item.file.videoInfo || {};
    }
    if (fileInfo.path && !openType) {
      // 打开文件
      openLocalFolder(fileInfo.path, fileInfo.name);
      console.log("openFolder", fileInfo.path + fileInfo.name);
    } else {
      let thisExt = item.file.ext;
      if (thisExt == "unknown" && config.imgTypeReg.test(item.file.type)) {
        thisExt = "png";
      }
      // 另存为
      let selFilePath = await selFolderDialog({defaultPath: config.imgTypeReg.test(thisExt) ? `乐聊图片${Date.now()}.${thisExt}` : item.file.name, globalEmit: globalEmit});
      let ext = path.extname(selFilePath)
      // 不存在后缀设置为文件类型后缀
      if (!ext) {
        ext = item.file.ext;
      }
      // 去除后缀的点
      if (/\./.test(ext)) {
        ext = ext.split(".")[1];
      }
      if (ext == "unknown" && config.imgTypeReg.test(item.file.type)) {
        ext = "png";
      }
      let param = {
        filePath: path.dirname(selFilePath),
        fileName: path.basename(selFilePath),
        fileExt: ext,
        replaceFile: true,
      }
      if (param.filePath[param.filePath.length - 1] != "\\") {
        param.filePath += "\\";
      }
      console.log("openFolder", selFilePath, param);
      dispatch("downloadFile", {item: item, param: param, thisElm: thisElm, done: done, msgItem: msgItem});
    }
  },
  // 下载文件 item为消息对象,param为另存为数据,done回调
  downloadFile: async function ({state, commit, dispatch}, info) {
    let {item, param, thisElm, done, msgItem} = info;
    msgItem = msgItem || {};
    let filePath = item.file.path;
    let fileName = item.file.name;
    let fileSize = item.file.size;
    let fileExt = item.file.ext;
    let fileUrl = item.file.url;
    let reDownLoad = false;
    let replaceFile = false;
    if (param) {
      if (param.replaceFile) {
        filePath = param.filePath;
        fileName = param.fileName;
        fileExt = param.fileExt;
        replaceFile = param.replaceFile;
      } else if (param.reDownLoad) {
        reDownLoad = param.reDownLoad;
      }
    }
    if (thisElm) {
      // 直接保存图片到本地
      let thisBse64 = await getBase64Image(thisElm, fileExt);
      // 不存在后缀设置为文件类型后缀
      if (!path.extname(fileName)) {
        fileName += "." + fileExt;
      }
      saveBase64Local({path: filePath + fileName, base64: thisBse64});
      if (done) {
        done({state: "success"});
      }
      return;
    } else if (/file:\/\/\//.test(fileUrl)) {
      // 本地文件直接复制
      fs.copyFileSync(fileUrl.replace("file:///", ""), filePath + fileName);
      if (done) {
        done({state: "success"});
      }
      return;
    }
    if (remote.store.state.nimFileUpload[item.file.downloadMD5]) {
      console.log("downloading", remote.store.state.nimFileUpload[item.file.downloadMD5]);
      alert({
        content: "当前下载任务正在进行中,是否重新下载",
        done: type => {
          if (type == 1) {
            try {
              remote.store.state.nimFileUpload[item.file.downloadMD5].abort();
            } catch (e) {}
            remote.store.commit("setNimFileUpload", {key: item.file.downloadMD5, value: "del"});
            dispatch("downloadFile", info);
          }
        }
      })
      return;
    }
    if (!item.file.downloadMD5) {
      item.file.downloadMD5 = item.file.videoMD5 || item.file.fileMD5;
    }
    // 设置下载进度
    item.file.progress = "0.00";
    remote.store.commit("setNimFileUpload", {key: item.file.downloadMD5, value: {file: item.file}});
    // 判断天翼云文件是否存在
    let jjsPicInfo = await dispatch("getHasJjsPicUrlOther", fileUrl);
    // 获取过程如果取消下载则不继续
    if (!remote.store.state.nimFileUpload[item.file.downloadMD5]) {
      return;
    }
    let jjsPicUrlOrigin = jjsPicInfo.jjsPicUrlOrigin;
    fileUrl = jjsPicInfo.url;
    loadCache({
      url: fileUrl,
      size: fileSize,
      ext: fileExt,
      fileDB: state.fileDB,
      filePath: filePath,
      fileName: fileName,
      isDownLoad: true,
      replaceFile: replaceFile,
      reDownLoad: reDownLoad,
      jjsPicUrlOrigin: jjsPicUrlOrigin,
      done: res => {
        if (done) {
          done(res);
        }
        item.file.progress = res.percentage;
        // 设置下载进度
        try {
          remote.store.commit("setNimFileUpload", {key: item.file.downloadMD5, value: {abort: res.clientRequest.destroy.bind(res.clientRequest), file: item.file, res: res}});
        } catch (e) {
          console.log("setNimFileUploadErr", e);
        }
        if (res.state == "success") {
          remote.store.commit("setNimFileUpload", {key: item.file.downloadMD5, value: "del"});
          delete item.file.progress;
          delete item.file.downloadMD5;
          let fileInfo = {
            md5: res.md5,
            path: res.path,
            name: res.name,
            size: res.total,
            url: res.url
          };
          if (item.type == "video") {
            // 视频信息
            item.file.videoInfo = fileInfo;
            msgItem.file && (msgItem.file.videoInfo = fileInfo);
          } else {
            // 图片/文件信息
            item.file.fileInfo = fileInfo;
            msgItem.file && (msgItem.file.fileInfo = fileInfo);
          }
          // 只有1v1下载提示
          if ((item.scene == "p2p" || item.convType == "PERSON") && (item.type == "file" || item.msgType == "FILE" || item.type == "video") && !item.showTips) {
            remote.store.state.nimSDK.sendTipMsg({
              scene: "p2p",
              to: item.to == state.userInfo.workerNo ? (item.from || item.fromAccount) : item.to,
              tip: `${state.userInfo.name}成功下载了 ${item.file.name} 文件`
            }).then(res => {
              dispatch("sendMsgDone", res);
            });
          }
        } else if (res.state == "close" || res.state == "error") {
          remote.store.commit("setNimFileUpload", {key: item.file.downloadMD5, value: "del"});
          delete item.file.progress;
          if (res.state == "error") {
            if (res.code == "EBUSY") {
              toast({title: res.msg, type: 2});
            } else {
              toast({title: "抱歉,下载失败,请稍后再试!" + (res.msg || ""), type: 2});
            }
          }
        } else if (res.state == "start") {
          if (item.type == "video") {
            // 视频信息
            msgItem.file && !msgItem.file.videoInfo && (msgItem.file.videoInfo = {});
          } else {
            // 图片/文件信息
            msgItem.file && !msgItem.file.fileInfo && (msgItem.file.fileInfo = {});
          }
        }
      }
    });
  },
  // 获取天翼云文件地址是否存在
  getHasJjsPicUrlOther: function ({state, commit, dispatch}, info) {
    return new Promise(async resolve => {
      let jjsPicUrlOrigin = "";
      if (config.easePicUrlOtherReg.test(info)) {
        let jjsPicUrlOtherUrl = info.replace(config.easePicUrlOtherReg, config.jjsPicUrlOther);
        let urlRes = {};
        try {
          urlRes = await request({url: jjsPicUrlOtherUrl, time: true, timeout: 3000});
        } catch (e) {}
        if (urlRes?.statusCode == 200) {
          try {
            jjsPicUrlOrigin = info.match(config.easePicUrlOtherReg)[0];
          } catch (e) {}
          info = jjsPicUrlOtherUrl;
        }
      }
      resolve({url: info, jjsPicUrlOrigin: jjsPicUrlOrigin});
    });
  },
  // 获取云信文件地址是否存在
  getHasJjsPicUrl: function ({state, commit, dispatch}, info) {
    return new Promise(async resolve => {
      let flag = false;
      let urlRes = {};
      try {
        urlRes = await request({url: info, time: true, timeout: 3000});
      } catch (e) {}
      if (urlRes?.statusCode == 200) {
        flag = true;
      }
      resolve(flag);
    });
  },
  // 获取常用表情
  getOftenEmoji: function ({state, commit, dispatch}, info) {
    return new Promise(async (resolve) => {
      if (state.oftenEmoji) {
        // 返回使用次数降序
        let list = deepClone(state.oftenEmoji.sort((a, b) => {return b.emojiCount - a.emojiCount}));
        if (list.length > 11) {
          list.length = 11;
        }
        resolve(list);
      } else {
        let list = [];
        let res = await queryOftenEmojiApi();
        if (res && res.data && res.data.data) {
          list = res.data.data;
          state.oftenEmoji = list;
        }
        resolve(list);
      }
    });
  },
  // 退出讨论组/群
  leaveTeam: function ({state, commit, dispatch}, info) {
    return new Promise(resolve => {
      let teamInfo = state.teams[info.id];
      let isGroup = teamInfo.detailType == "group";
      alert({
        content: `确定退出该${isGroup ? "讨论组" : "群"}?`,
        done: async type => {
          if (type == 1) {
            loading();
            let res = await dispatch("leaveTeamMethod", info);
            if (res.err) {
              alert({content: (res.err.message || "操作失败请重试") + res.err.code});
            }
            resolve(res);
            loading().hide();
          } else {
            resolve({err: "", obj: teamInfo, alertType: type});
          }
        }
      });
    });
  },
  leaveTeamMethod: function ({state, commit, dispatch}, info) {
    return new Promise(async resolve => {
      let teamInfo = state.teams[info.id];
      let isGroup = teamInfo.detailType == "group";
      try {
        // 群调用服务端api
        let res = await leaveTeamApi({
          msgBody: JSON.stringify({empNo: state.userInfo.workerNo, tid: teamInfo.teamId, teamType: isGroup ? "2" : "1"})
        })
        // 20004不存在该群，直接关闭会话
        if (!res.success && res.errorCode != 20004) {
          resolve({err: {code: res.errorCode, message: res.errorMsg}, obj: teamInfo, alertType: 1});
          return;
        }
        resolve({err: "", obj: teamInfo, alertType: 1});
        dispatch("removeSession", (teamInfo.detailType == "superTeam" ? "superTeam-" : "team-") + teamInfo.teamId);
      } catch (e) {
        console.log("leaveTeamMethodErr", e);
        resolve({err: {code: "error", message: e}, obj: teamInfo, alertType: 1});
      }
    });
  },
  // 解散讨论组/群
  dismissTeam: function ({state, commit, dispatch}, info) {
    return new Promise(resolve => {
      let teamInfo = state.teams[info.id];
      alert({
        content: "确定解散该讨论组?",
        done: async type => {
          if (type == 1) {
            loading();
            let res = await dispatch("dismissTeamMethod", info);
            if (res.err) {
              alert({content: (res.err.message || "操作失败请重试") + res.err.code});
            }
            resolve(res);
            loading().hide();
          } else {
            resolve({err: "", obj: teamInfo, alertType: type});
          }
        }
      });
    });
  },
  dismissTeamMethod: function ({state, commit, dispatch}, info) {
    return new Promise(async resolve => {
      let teamInfo = state.teams[info.id];
      try {
        if (teamInfo.detailType != "group") {
          let errorMsg = "乐聊仅支持解散讨论组，群组请到新系统后台解散";
          toast({title: errorMsg, type: 2});
          resolve({err: {message: errorMsg}, obj: teamInfo});
          return;
        }
        let res = await dismissGroupApi({
          msgBody: JSON.stringify({owner: state.userInfo.workerNo, tid: teamInfo.teamId})
        });
        // 20004不存在该群，直接关闭会话
        if (!res.success && res.errorCode != 20004) {
          resolve({err: {code: res.errorCode, message: res.errorMsg}, obj: teamInfo, alertType: 1});
          return;
        }
        resolve({err: "", obj: teamInfo, alertType: 1});
        dispatch("removeSession", (teamInfo.detailType == "superTeam" ? "superTeam-" : "team-") + teamInfo.teamId);
      } catch (e) {
        console.log("dismissTeamMethodErr", e);
        resolve({err: {code: "error", message: e}, obj: teamInfo, alertType: 1});
      }
    });
  },
  // 打开截图
  setOpenJt: async function ({state, commit, dispatch, getters}, info) {
    console.log("setOpenJt", state.jtObj.isJt);
    if (state.jtObj.isJt) {
      toast({title: "截图正在运行中，请勿重复操作", type: 2});
      // 提示后2s解除状态
      if (!state.jtObj.timer) {
        state.jtObj.timer = setTimeout(() => {
          state.jtObj.timer = "";
          state.jtObj.isJt = false;
        }, 2000);
      }
      return;
    }
    // 去除截图后再次截图存在定时器
    if (state.jtObj.timer) {
      clearTimeout(state.jtObj.timer);
      state.jtObj.timer = "";
    }
    let clip = remote.Clipboard.get();
    // 清空粘贴板
    clip.clear();
    let currentWindow = getters.getCurrentWindow();
    let jtType = 1;
    let configType = await remote.store.state.settings[config.settings.type5];
    if (configType == 1 || configType == 2) {
      jtType = 3;
    } else {
      if (10000 > state.computerInfo.BuildNumber) {
        jtType = 2;
      }
    }
    // 隐藏窗口
    let isHideWin = info.isFocus && (configType == 2 || configType == 4)
    if (isHideWin) {
      commit("setWindowMin", currentWindow.cWindow.id);
    }
    state.jtObj.isJt = true;
    state.jtObj.uid = UUID.generate();
    setJJSEvent(info.key == 1 ? "P06240256" : "P95993856", JSON.stringify({
      d: state.jtObj.uid,
      w: state.userInfo.workerNo,
      y: isHideWin ? "隐藏" : "展示",
      t: Date.now(),
      n: jtType == 3 ? "Snipaste" : jtType == 2 ? "screenshot_xp" : "screenshot",
    }));
    openJt(jtType).then(async (param) => {
      state.jtObj.isJt = false;
      if (info.isFocus) {
        commit("setWindowCancelMin", currentWindow.cWindow.id);
        currentWindow.window.store.dispatch("activeImEditor", {id: state.currentSession.id, active: true});
        let sessionId = currentWindow.window.store.state.currentSession.id;
        if (state.imEditor._iframeDocument) {
          let clipImage = clip.get("png") || clip.get("jpeg");
          // 存在图片则插入输入框。且不是公众号服务号、群助手、系统通知、客户咨询
          if (clipImage && clipImage.length > 0 && clipImage != "data:image/png;base64," && !isSubOrSer(sessionId)
            && sessionId != "p2p-" + config.helperAccount && sessionId != "p2p-" + config.systemMessage && sessionId != "p2p-" + config.customerAccount) {
            state.imEditor.dangerouslyPasteHTML(sessionId, `<img src="${clip.get("png") || clip.get("jpeg")}">`);
            state.imEditor.activeEditor(sessionId);
          }
          // 部分系统不偏移会卡住一会
          if (configType == 2 || configType == 4) {
            setTimeout(() => {
              let bounds = currentWindow.getBounds();
              commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: bounds.x + jtCount, y: bounds.y + jtCount, width: bounds.width, height: bounds.height});
              jtCount = jtCount * -1;
              commit("setWindowCancelMin", currentWindow.cWindow.id);
              setTimeout(() => {
                commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: bounds.x + jtCount, y: bounds.y + jtCount, width: bounds.width, height: bounds.height});
                jtCount = jtCount * -1;
              }, 0);
            }, 100);
          }
        }
      }
      setJJSEvent(info.key == 1 ? "P75662592" : "P72530432", JSON.stringify({
        d: state.jtObj.uid,
        w: state.userInfo.workerNo,
        y: isHideWin ? "隐藏" : "展示",
        t: Date.now(),
        n: jtType == 3 ? "Snipaste" : jtType == 2 ? "screenshot_xp" : "screenshot",
        s: param?.err ? "异常" : "正常"
      }));
    });
  },
  // 窗口抖动
  shakeWindow: function ({state, commit, dispatch, getters}, info) {
    let currentWindow = getters.getCurrentWindow();
    // 子窗口抖动
    if (getChildWin("chat-" + info.id)) {
      currentWindow = getChildWin("chat-" + info.id);
    }
    if (state.shakeInterval) {
      clearInterval(state.shakeInterval);
    }
    let bounds = currentWindow.getBounds();
    let boundsInfo = {
      x: bounds.x,
      y: bounds.y,
      width: bounds.width,
      height: bounds.height,
    };
    commit("setWindowCancelMin", currentWindow.cWindow.id);
    state.shakeInterval = setInterval(() => {
      commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: boundsInfo.x - 5, y: boundsInfo.y - 5, width: boundsInfo.width, height: boundsInfo.height, maxW: boundsInfo.width, maxH: boundsInfo.height, isTop: true});
      setTimeout(() => {
        commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: boundsInfo.x + 5, y: boundsInfo.y + 5, width: boundsInfo.width, height: boundsInfo.height, maxW: boundsInfo.width, maxH: boundsInfo.height});
        commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: boundsInfo.x, y: boundsInfo.y, width: boundsInfo.width, height: boundsInfo.height});
      }, 75);
    }, 150);
    // 清除抖动
    setTimeout(() => {
      clearInterval(state.shakeInterval);
      state.shakeInterval = "";
    }, 900);
  },
  // 设置强提醒
  setRemindMsg: function ({state, commit, dispatch, getters}, info) {
    if (info.type == "del") {
      // 关闭指定的强提醒
      let index = state.remindMsg.findIndex(item => {return item.idServer == info.idServer});
      if (index > -1) {
        state.remindMsg.splice(index, 1);
      }
    } else if (info.type == "add") {
      // 添加
      state.remindMsg.push(info.value);
    } else {
      // 默认设置
      state.remindMsg = info.value;
    }
    state.emit.remindMsg = state.remindMsg;
  },
  // 设置霸屏提醒
  setImportantMsg: function ({state, commit, dispatch, getters}, info) {
    if (info.type == "add") {
      // 添加
      state.importantMsg.unshift(info.value);
    } else if (info.type == "del") {
      // 删除
      for (let i = 0; i < state.importantMsg.length; i++) {
        if (state.importantMsg[i].idServer == info.idServer) {
          state.importantMsg.splice(i, 1);
          break;
        }
      }
    } else if (info.type == "clear") {
      state.importantMsg = [];
    }
    state.emit.importantMsg = state.importantMsg;
  },
  // 锁定乐聊
  setLockIm: function ({state, commit, dispatch, getters}, info) {
    state.lockIm = info;
    for (let key in window.global.childWin) {
      let item = getChildWin(key);
      if (item) {
        if (info == true) {
          // 锁定乐聊隐藏其他窗口
          commit("setWindowMin", item.cWindow.id);
          item.setShowInTaskbar(false);
        } else {
          // 解除锁定恢复
          commit("setWindowCancelMin", item.cWindow.id);
          if (key == "notify") {
            item.setShowInTaskbar(false);
          }
        }
      }
    }
    if (state.lockIm && state.imEditor?.blurEditor) {
      state.imEditor.blurEditor(state.currentSession.id);
    }
  },
  // 设置日程状态
  setScheduleStatus: function ({state, commit, dispatch, getters}, param) {
    let id = param.id;
    //参与状态 1-接受 2-待定 3-未回复 4-拒绝
    let status = param.status;
    let workerNo = state.userInfo.workerNo;
    // 设置本地缓存
    let localSchedule = JSON.parse(localStorage.getItem("smartSchedule") || '{}');
    if (!localSchedule[workerNo]) {
      localSchedule[workerNo] = {};
    }
    state.nimSDK.getServerTime().then(res => {
      let {err, obj} = res;
      let serverTime = obj;
      if (err) {
        console.log("getServerTimeError", err);
        serverTime = Date.now() + state.diffTime;
      }
      localSchedule[workerNo][id] = {
        status: status,
        time: serverTime
      };
      setLocalStorage("smartSchedule", JSON.stringify(localSchedule));
      if (param.value == 1 && param.idServer) {
        // 关闭对应的强提醒-多端同步
        dispatch("setRemindMsg", {type: "del", idServer: param.idServer});
      } else if (state.remindMsg.length > 0) {
        // 更新强提醒状态
        state.emit.remindMsg = [];
        state.emit.remindMsg = state.remindMsg;
      }
      // 通知更新消息
      commit("setEmit", {type: "reloadMsg", value: {id: state.currentSession.id, time: Date.now()}});
    });

    // 多端同步操作通知回调 不发送
    if (!param.customSysMsg) {
      let customParam = {
        type: "updateSchedule",
        id: id,
        status: status,
        value: 0,
        from: 1//1-pc 2-ios 3-android
      };
      // 强提醒改变状态，需要关闭对应消息提示
      if (param.idServer) {
        customParam.idServer = param.idServer;
        customParam.value = 1;
      }
      // 发送通知给自己和别人(用于多端同步)
      state.nimSDK.sendCustomSysMsg({
        scene: "p2p",
        to: workerNo,
        content: JSON.stringify(customParam),
        done: function () {}
      });
    }
  },
  // 设置文档
  setDocStatus: function ({state, commit, dispatch, getters}, param) {
    state.nimSDK.getServerTime().then(res => {
      let {err, obj} = res;
      let serverTime = obj;
      if (err) {
        console.log("getServerTimeError", err);
        serverTime = Date.now() + state.diffTime;
      }
      // 获取本地本地缓存
      let localDocPur = JSON.parse(localStorage.getItem("docPur") || '{}');
      if (!localDocPur[state.userInfo.workerNo]) {
        localDocPur[state.userInfo.workerNo] = {};
      }
      // 设置本地缓存
      if (param.key == "del") {
        delete localDocPur[state.userInfo.workerNo][param.account + "-" + param.id];
      } else if (param.idServer) {
        localDocPur[state.userInfo.workerNo][param.account + "-" + param.id + "-" + param.idServer] = {key: param.key, time: serverTime};
      } else {
        localDocPur[state.userInfo.workerNo][param.account + "-" + param.id] = {key: param.key, time: serverTime};
      }
      setLocalStorage("docPur", JSON.stringify(localDocPur));
      // 通知更新消息
      commit("setEmit", {type: "reloadMsg", value: {id: state.currentSession.id, time: Date.now()}});
    });
  },
  // 设置服务器和云信配置信息
  setModifySettings: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let param = info;
      // 不取消置顶 云信bug-加入黑名单取消置顶会报错
      let notTop = param.notTop;
      delete param.notTop;
      // 是否变更自己信息
      let modify = param.modify;
      delete param.modify;
      // 获取会话详细类型
      let detailType = param.detailType;
      delete param.detailType;
      param = {
        ...param,
        empNo: state.userInfo.workerNo,
        imei: state.baseComputerInfo.hostName,
      }
      let res = await modifySettingsApi({
        msgBody: JSON.stringify(param)
      });
      resolve(res);
      // 设置成功，发送自定义消息
      if (res?.success) {
        // 同步本地配置信息
        commit("setSetting", param);
        // 设置本地配置-快捷键、分组模式、分组排序
        if (param.key == config.settings.type6 || param.key == config.settings.type12 || param.key == config.settings.type13 || param.key == config.settings.type16) {
          userLocalStorage(param, 1);
        }
        if (modify) {
          state.nimSDK.modifyMyInfo(param);
        }
        // 同步群设置
        if (detailType) {
          state.nimSDK.updateInfoInTeam(param, detailType);
        }
        // 修改云信置顶状态
        if (param.key == config.settings.type3 && !notTop) {
          state.nimSDK.updateStickTopSession(param);
        }
      }
    });
  },
  // 设置日程弹窗
  setScheduleModal: function (store, info) {
    store.state.scheduleModal = initScheduleModal(store);
  },
  // 跳转日程提醒
  toScheduleModal: function ({state, getters}, info) {
    getters.getCurrentWindow().focus()
    state.router.push({path: "/index/smartSchedule"});
    let minStamp = 30 * 60 * 1000;
    let _timeStart = (parseInt(new Date().getTime() / minStamp) + 1) * minStamp;
    let _timeEnd = _timeStart + minStamp;
    let _time = [dateFormat(_timeStart, "yyyy年MM月dd日 HH:mm"), dateFormat(_timeEnd, "yyyy年MM月dd日 HH:mm")].join("-")
    new state.scheduleModal({
      type: 1,
      time: _time,
      title: info,
      callback: function (result) {
        if (result == "add") {
          // 新建成功，刷新页面
          setTimeout(function () {
            state.router.push({path: "/index/smartSchedule", query: {time: Date.now()}});
          }, 0)
        }
      }
    })
  },
  // 设置系统消息
  setLocalSysMsgs: async function ({state, commit, dispatch, getters}, info) {
    let scene = "p2p";
    let to = config.systemMessage;
    let sessionId = scene + "-" + to;
    if (state.currentSession.id == sessionId) return //当前是通知则不改变徽标
    if (!state.sessions[sessionId]) {
      state.sessions[sessionId] = {scene: scene, to: to, localCustom: {unread: 0}};
      state.nimSDK.insertLocalSession({scene: scene, to: to});
    }
    let unread = 0;
    if (state.sessions[sessionId]?.localCustom) {
      unread = state.sessions[sessionId].localCustom.unread = (state.sessions[sessionId].localCustom.unread || 0) + 1;
    } else {
      unread++;
    }
    let res = await state.nimSDK.updateLocalSession({
      id: sessionId,
      localCustom: {unread: unread, updateTime: info.time}
    });
    // 设置当前系统消息未读数
    if (!res.err && res.obj) {
      state.sessions[sessionId].localCustom = res.obj.localCustom;
      state.sessions[sessionId].unread = state.sessions[sessionId].localCustom.unread || 0;
    }
  },
  // 设置系统通知
  setNotification: function ({state, commit, dispatch, getters}, info) {
    let {type, session, msg} = info;
    if (type == "del") {
      // 删除
      if (state.notifyMap[session.id]) {
        delete state.notifyMap[session.id];
        try {
          getChildWin("notify").window.emitMsg({type: "notifyMap", value: state.notifyMap});
        } catch (e) {}
      }
    } else {
      // 添加
      // 生产环境去除指令账号、未知服务号service000002
      if (config.env == "online" && (session.id == `p2p-${config[config.env].instructionNumber}` || session.id == "p2p-service000002")) {
        return;
      }
      // 进线客户讨论组(只有群主显示)、售后讨论组不显示
      if (session.to && state.teams[session.to] && isFcwList("", "", state.teams[session.to], true, true)) {
        return;
      }
      // 存在通知状态计数保持原来计数
      if (!state.notifyMap[session.id]) {
        session.showCount = state.notifyMapCount;
        state.notifyMapCount++;
      } else {
        session.showCount = state.notifyMap[session.id].showCount;
      }
      // 获取人员信息
      dispatch("getPersons", {doneFlag: true, account: msg.from}).then(personInfo => {
        session.userInfo = personInfo;
        session.showText = getters.getPrimaryMsg({msg: msg, primaryType: 4, notChange: true});
        session.showTime = transTime(msg.time);
        session.lastMsg = msg;
        state.notifyMap[session.id] = session;
        try {
          getChildWin("notify").window.emitMsg({type: "notifyMap", value: state.notifyMap});
        } catch (e) {
          // 初始化通知窗口
          commit("setNotifyWin", "add");
        }
      });
    }
  },
  // 点击打开通知
  setOpenNotify: function ({state, commit, dispatch, getters}, info) {
    // 设置子窗
    let currentWindow = getters.getCurrentWindow();
    if (getChildWin("chat-" + info.id)) {
      currentWindow = getChildWin("chat-" + info.id);
    }
    dispatch("setCurrentSession", {id: info.id, type: "open"});
    commit("setWindowCancelMin", currentWindow.cWindow.id);
    currentWindow.window.store.dispatch("scrollBottom", {sessionId: info.id});
    currentWindow.setAlwaysOnTop(true);
    setTimeout(() => {
      currentWindow.setAlwaysOnTop(false);
    }, 10)
  },
  // 设置敏感词库-半小时更新一次
  setKeyText: function ({state, commit, dispatch, getters}, info) {
    // 获取讨论组敏感词-禁止修改
    getImSensitivesApi().then(res => {
      state.groupKeyTextList = res?.data?.sensitiveWord || [];
    });
    // 获取客户敏感词-整个消息禁止发送给客户
    fcwRulesApi().then(res => {
      let list = res?.data || [];
      for (let i = 0; i < list.length; i++) {
        if (!list[i]?.keyword) {
          list.splice(i, 1);
          i--;
          continue;
        }
        list[i].keyword = list[i].keyword.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&').replace(/,/g, "|");
      }
      state.fcwKeyTextList = list;
    });
    // 获取云信文件转移时间
    obsStoreEndTimeApi().then(res => {
      if (res.data && res.data.data) {
        state.yxFileTime = res.data.data;
      }
    });
    // 获取远程指令列表
    dispatch("setCheckRemote");
    // 获取敏感词-替换星号
    getKeyTextApi().then(res => {
      let keyList = (res || "").replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&').replace(/\r\n|\r|\n/g, "|").split("|");
      state.keyText = trimArray(keyList).join("|");
      setTimeout(() => {
        // 半小时更新一次
        dispatch("setKeyText");
      }, 30 * 60 * 1000)
    });
  },
  // 图片发送检测
  setSensitiveWords: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      // 暂时不接入
      if (state.userInfo.workerNo == "000001" || true) {
        resolve(true);
        return;
      }
      let res = await sensitiveWordsApi({
        type: 2,
        sourceModel: "pc-im",
        context: info
      });
      if (!res.data || res.data.level != 1) {
        resolve(true);
      } else {
        if (res.data && res.data.sensitiveText) {
          toast({title: "发送失败，图片涉及敏感信息", type: 2});
          console.log("setSensitiveWords", res.data.sensitiveText);
        }
        resolve(false);
      }
    })
  },
  // 重新获取备灾开关和代理数据
  getReProxyInfo: async function ({state, commit, dispatch, getters}, info) {
    state.jjsProxy.done = false;
    await dispatch("getUserIp");
    return state.jjsProxy;
  },
  // 获取ip地址
  getUserIp: async function ({state, commit, dispatch, getters}, info) {
    // 获取用户ip和详情
    if (!state.ipInfo.ip && !state.ipInfo.done) {
      if (state.ipInfo.promise) {
        return state.ipInfo;
      }
      state.ipInfo.promise = new Promise(async resolve => {
        state.ipInfo.done = true;
        try {
          state.ipInfo.ipRes = await getIpApi1();
          if (!state.ipInfo.ipRes.errorMsg && state.ipInfo.ipRes.data) {
            state.ipInfo.ip = state.ipInfo.ipRes.data.ip;
            state.ipInfo.address = state.ipInfo.ipRes.data.country + state.ipInfo.ipRes.data.province + state.ipInfo.ipRes.data.city;
            console.log("getTpApi1", state.ipInfo);
          }
        } catch (e) {
          console.log("getTpApi1Err", e);
        }
        if (!state.ipInfo.ip) {
          try {
            state.ipInfo.ipRes = await getIpApi2();
            if (state.ipInfo.ipRes) {
              let ipResJson = JSON.parse(state.ipInfo.ipRes.replace(/\s/g, "").replace("当前IP：", "{\"ip\":\"").replace("来自于：", "\",\"address\":\"") + "\"}");
              state.ipInfo.ip = ipResJson.ip;
              state.ipInfo.address = ipResJson.address;
            }
            console.log("getTpApi2", state.ipInfo);
          } catch (e) {
            console.log("getTpApi2Err", e);
          }
        }
        if (!state.ipInfo.ip) {
          try {
            state.ipInfo.ipRes = await getIpApi3();
            if (state.ipInfo.ipRes) {
              state.ipInfo.ip = state.ipInfo.ipRes.ip;
              if (state.ipInfo.ipRes.data) {
                state.ipInfo.address = state.ipInfo.ipRes.data.country + state.ipInfo.ipRes.data.prov + state.ipInfo.ipRes.data.city;
              }
            }
            console.log("getTpApi3", state.ipInfo);
          } catch (e) {
            console.log("getTpApi3Err", e);
          }
        }
        if (!state.ipInfo.ip) {
          try {
            state.ipInfo.ipRes = await getIpApi4();
            if (state.ipInfo.ipRes?.data) {
              state.ipInfo.ip = state.ipInfo.ipRes.data.ip;
              state.ipInfo.address = state.ipInfo.ipRes.data.address;
            }
            console.log("getTpApi4", state.ipInfo);
          } catch (e) {
            console.log("getTpApi4Err", e);
          }
        }
        if (!/中国/.test(state.ipInfo?.ipRes?.address)) {
          state.ipInfo.isDomestic = false;
        }
        state.ipInfo.done = false;
        resolve(state.ipInfo);
        delete state.ipInfo.promise;
      });
    }
    // 获取代理信息文件
    try {
      if (!state.jjsProxy.done && !state.jjsProxy.flag) {
        let proxyInfo = JSON.parse(decrypt(config.proxyInfo));
        state.jjsProxy.flag = true;
        let proxyInfoRes = await getProxyApi();
        state.jjsProxy.flag = false;
        // 存在代理信息设置本地
        if (proxyInfoRes.agent) {
          state.jjsProxy.done = true;
          proxyInfo = {
            open: proxyInfoRes.open,
            api: proxyInfoRes.interface,
            maxDelay: proxyInfoRes?.timeout?.high,
            timeoutDelay: proxyInfoRes?.timeout?.error,
            defaultCountNum: proxyInfoRes?.count?.default,
            errorCountNum: proxyInfoRes?.count?.error,
            host: proxyInfoRes.host,
            proxyHome: {name: proxyInfoRes.agent.home?.name, key: "home", url: `http://${proxyInfoRes.agent.home?.ip}:${proxyInfoRes.agent.home?.port}`, timeout: proxyInfoRes.checkRate?.proxy || 5000},
            proxyAbroad: {name: proxyInfoRes.agent.abroad?.name, key: "home", url: `http://${proxyInfoRes.agent.abroad?.ip}:${proxyInfoRes.agent.abroad?.port}`, timeout: proxyInfoRes.checkRate?.proxy || 1000},
            proxyTemp: [],
            loginSwitch: proxyInfoRes.applogin?.rzswitch,
            rsSwitch: proxyInfoRes.applogin?.rsswitch,
          };
          // 设置临时线路
          if (Array.isArray(proxyInfoRes.agent.temp) && proxyInfoRes.agent.temp.length > 0) {
            proxyInfoRes.agent.temp.map((item, index) => {
              proxyInfo.proxyTemp.push({name: item.name, key: `temp${index}`, url: `http://${item.ip}:${item.port}`});
            });
          }
          // 缓存到本地
          let proxyStr = encrypt(JSON.stringify(proxyInfo));
          config.proxyInfo = proxyStr;
          if (process.env.NODE_ENV != "development") {
            let pkgPath = getAppPath(`\\package.json`);
            let pkg = JSON.parse(fs.readFileSync(pkgPath));
            pkg.proxyInfo = proxyStr;
            fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2));
          }
        }
        // 设置本地代理信息
        state.jjsProxy.open = proxyInfo.open != null ? proxyInfo.open : true;
        state.jjsProxy.api = proxyInfo.api || config[config.env].jjsImApi;
        state.jjsProxy.maxDelay = proxyInfo.maxDelay || 500;
        state.jjsProxy.timeoutDelay = proxyInfo.timeoutDelay || 3000;
        state.jjsProxy.defaultCountNum = proxyInfo.defaultCountNum || 3;
        state.jjsProxy.errorCountNum = proxyInfo.errorCountNum || 6;
        state.jjsProxy.host = proxyInfo.host;
        state.jjsProxy.proxyList = [
          {name: "默认线路", key: "default", url: "", delay: "", timeout: proxyInfo.timeoutDelay},
          {name: proxyInfo?.proxyHome?.name, key: "home", url: proxyInfo?.proxyHome?.url, delay: "", timeout: proxyInfo?.proxyHome?.timeout || 10000}
        ];
        state.jjsProxy.proxyAbroad = {name: proxyInfo?.proxyAbroad?.name, key: "abroad", url: proxyInfo?.proxyAbroad?.url, delay: "", timeout: proxyInfo?.proxyAbroad?.timeout || 10000};
        state.jjsProxy.proxyTemp = proxyInfo.proxyTemp || [];
        state.jjsProxy.loginSwitch = proxyInfo.loginSwitch || false;
        state.jjsProxy.rsSwitch = proxyInfo.rsSwitch || false;
      }
      let proxyAbroadIndex = state.jjsProxy.proxyList.findIndex(item => {return item.key == "abroad"});
      if (state.ipInfo.isDomestic) {
        // 国内用户删除新加坡线路
        if (proxyAbroadIndex > -1) {
          state.jjsProxy.proxyList.splice(proxyAbroadIndex, 1);
        }
      } else {
        // 国外用户新增新加坡线路
        if (proxyAbroadIndex == -1) {
          state.jjsProxy.proxyList.push(state.jjsProxy.proxyAbroad);
        }
      }
    } catch (e) {
      console.log("getProxyErr", e);
    }
    // 线路只有线上测试环境和生产环境能使用
    if ((config.env == "onlinetest" || config.env == "online") && state.jjsProxy.open) {
      dispatch("getProxyDelay");
    }
    return state.ipInfo;
  },
  // 获取代理线路延迟
  getProxyDelay: async function ({state, commit, dispatch, getters}, info) {
    if (!window.navigator.onLine || (window.navigator.onLine && !state.jjsProxy.onLine)) {
      // 断网/断网恢复状态设置回主线路
      dispatch("setProxyCurrent", {key: "default", error: false});
      return;
    }
    let p = [];
    //  c-外网ip、p-代理线路、d-上次线路延迟、n-当前线路
    let requestParam = {url: state.jjsProxy.api, time: true, timeout: state.jjsProxy.timeoutDelay / 2, qs: {c: state.ipInfo.ip, p: "", d: "", n: state.jjsProxy.proxyCurrent.url}};
    // 获取线路延迟
    state.jjsProxy.proxyList.map(item => {
      // 超过频率控制才继续请求
      if (!item.flag && Date.now() - (item.time || 0) > item.timeout) {
        item.flag = true;
        item.time = Date.now();
        requestParam.proxy = item.url;
        requestParam.qs.p = item.url;
        requestParam.qs.d = item.delay;
        p.push(request(deepClone(requestParam)));
      }
    });
    // 存在请求线路延迟的场景需要判断网络是否正常
    if (p.length > 0) {
      // 获取最优线路
      Promise.allSettled(p).then(async res => {
        state.jjsProxy.switchAbroad = false;
        state.jjsProxy.switchTemp = false;
        // 设置线路延迟
        for (let i = 0; i < res.length; i++) {
          let resItem = res[i];
          let thisDelay = "";
          if (resItem.value) {
            resItem = resItem.value;
            if (resItem && resItem.timingPhases) {
              thisDelay = parseInt(resItem.timingPhases.firstByte);
              if (thisDelay > state.jjsProxy.timeoutDelay) {
                thisDelay = "";
              }
            }
          } else {
            resItem = resItem.reason;
          }
          if (resItem) {
            for (let j = 0; j < state.jjsProxy.proxyList.length; j++) {
              if (state.jjsProxy.proxyList[j].url == resItem.thisProxy) {
                if (thisDelay > state.jjsProxy.timeoutDelay) {
                  thisDelay = "";
                }
                state.jjsProxy.proxyList[j].flag = false;
                state.jjsProxy.proxyList[j].time = Date.now();
                state.jjsProxy.proxyList[j].delay = thisDelay;
                state.jjsProxy.pingInfo[state.jjsProxy.proxyList[j].key] = thisDelay;
                state.logger.writeLogFile(10, JSON.stringify(state.jjsProxy.proxyList[j]));
                break;
              }
            }
          }
        }
        // 默认线路最大延迟数，超过后判断其他线路延迟获取最优线路
        // 判断代理服务器情况
        if (state.jjsProxy.pingInfo["default"] && state.jjsProxy.pingInfo["default"] < state.jjsProxy.maxDelay) {
          // 主线路500ms内使用主线路
          state.jjsProxy.defaultCount = 0;
          dispatch("setProxyCurrent", {key: "default", error: false});
          if (state.jjsProxy.firstNet == 1) {
            state.jjsProxy.firstNet++;
          }
        } else if (state.jjsProxy.defaultCount < state.jjsProxy.defaultCountNum && state.jjsProxy.firstNet != 1) {
          // 主线路异常3次才切换线路，第1次主线路异常直接切线路
          state.jjsProxy.defaultCount++;
        } else if (state.jjsProxy.pingInfo["default"] && state.jjsProxy.pingInfo["default"] >= state.jjsProxy.maxDelay && state.jjsProxy.pingInfo["default"] <= state.jjsProxy.timeoutDelay) {
          if (state.jjsProxy.firstNet == 1) {
            state.jjsProxy.firstNet++;
          }
          // 主线路500ms-3000ms，且代理线路小于500ms，使用代理线路
          if (state.jjsProxy.pingInfo["home"] && state.jjsProxy.pingInfo["home"] < state.jjsProxy.maxDelay) {
            dispatch("setProxyCurrent", {key: "home", error: false});
          } else if (!state.ipInfo.isDomestic && state.jjsProxy.pingInfo["abroad"] && state.jjsProxy.pingInfo["abroad"] < state.jjsProxy.maxDelay) {
            // 国内用户只有默认线路和国内代理线路都故障的时候才使用国外线路
            dispatch("setProxyCurrent", {key: "abroad", error: false});
          } else {
            // 代理线路也大于500ms使用主线路
            dispatch("setProxyCurrent", {key: "default", error: false});
          }
        } else {
          // 主线路大于3000ms，且代理线路小于3000ms，使用代理线路
          if (state.jjsProxy.pingInfo["home"] && state.jjsProxy.pingInfo["home"] < state.jjsProxy.timeoutDelay) {
            dispatch("setProxyCurrent", {key: "home", error: false});
          } else if (!state.ipInfo.isDomestic && state.jjsProxy.pingInfo["abroad"] && state.jjsProxy.pingInfo["abroad"] < state.jjsProxy.timeoutDelay) {
            // 国内用户只有默认线路和国内代理线路都故障的时候才使用国外线路
            dispatch("setProxyCurrent", {key: "abroad", error: false});
          } else {
            // 代理线路异常
            let p1 = [];
            // 国内线路加入重新获取延迟
            state.jjsProxy.proxyList.map(item => {
              requestParam.proxy = item.url
              requestParam.qs.p = item.url;
              requestParam.qs.d = item.delay;
              p1.push(request(deepClone(requestParam)));
            });
            // 国内线路可以将国外线路做为备用线路
            if (state.ipInfo.isDomestic) {
              // 获取国外线路延迟
              state.jjsProxy.switchAbroad = true;
              requestParam.proxy = state.jjsProxy.proxyAbroad.url;
              requestParam.qs.p = state.jjsProxy.proxyAbroad.url;
              requestParam.qs.d = state.jjsProxy.proxyAbroad.delay;
              p1.push(request(deepClone(requestParam)));
            }
            // 存在临时线路
            if (state.jjsProxy.proxyTemp.length > 0) {
              state.jjsProxy.switchTemp = true;
              // 获取临时线路延迟
              state.jjsProxy.proxyTemp.map(item => {
                requestParam.proxy = item.url
                requestParam.qs.p = item.url;
                requestParam.qs.d = item.delay;
                p1.push(request(deepClone(requestParam)));
              });
            }
            Promise.allSettled(p1).then(async res => {
              // 最优的备用线路
              let tempInfo = {};
              for (let i = 0; i < res.length; i++) {
                let resItem = res[i];
                let thisDelay = "";
                if (resItem.value) {
                  resItem = resItem.value;
                  if (resItem && resItem.timingPhases) {
                    thisDelay = parseInt(resItem.timingPhases.firstByte);
                    if (thisDelay > state.jjsProxy.timeoutDelay) {
                      thisDelay = "";
                    }
                  }
                } else {
                  resItem = resItem.reason;
                }
                if (resItem) {
                  // 国内线路延迟
                  for (let j = 0; j < state.jjsProxy.proxyList.length; j++) {
                    if (state.jjsProxy.proxyList[j].url == resItem.thisProxy) {
                      if (thisDelay > state.jjsProxy.timeoutDelay) {
                        thisDelay = "";
                      }
                      state.jjsProxy.proxyList[j].delay = thisDelay;
                      if (thisDelay && (!tempInfo.delay || tempInfo.delay > thisDelay)) {
                        tempInfo = state.jjsProxy.proxyList[j];
                      }
                    }
                  }
                  if (state.jjsProxy.proxyAbroad.url == resItem.thisProxy) {
                    // 国外代理延迟
                    if (thisDelay > state.jjsProxy.timeoutDelay) {
                      thisDelay = "";
                    }
                    state.jjsProxy.proxyAbroad.delay = thisDelay;
                    if (thisDelay && (!tempInfo.delay || tempInfo.delay > thisDelay)) {
                      tempInfo = state.jjsProxy.proxyAbroad;
                    }
                  }
                  if (state.jjsProxy.switchTemp) {
                    // 临时代理延迟
                    for (let j = 0; j < state.jjsProxy.proxyTemp.length; j++) {
                      if (state.jjsProxy.proxyTemp[j].url == resItem.thisProxy) {
                        if (thisDelay > state.jjsProxy.timeoutDelay) {
                          thisDelay = "";
                        }
                        state.jjsProxy.proxyTemp[j].delay = thisDelay;
                        if (thisDelay && (!tempInfo.delay || tempInfo.delay > thisDelay)) {
                          tempInfo = state.jjsProxy.proxyTemp[j];
                        }
                      }
                    }
                  }
                }
              }
              // 设置当前线路
              if (tempInfo.delay) {
                // 存在可用线路
                dispatch("setProxyCurrent", {key: "temp", error: false, proxy: tempInfo});
              } else {
                // 不存在可用线路切回主线路
                dispatch("setProxyCurrent", {key: "default", error: true});
              }
            });
          }
        }
        commit("setJjsProxy");
      });
    }
  },
  // 设置当前代理线路-获取设置的代理线路，都不存在重置回默认线路
  setProxyCurrent: function ({state, commit, dispatch, getters}, info) {
    // 当前线路故障，获取当前网络情况
    let isOnLine = state.jjsProxy.onLine;
    if (window.navigator.onLine) {
      if (!isOnLine) {
        // 断网恢复重置
        state.jjsProxy.pingCount = 0;
        state.jjsProxy.onLine = true;
      }
      // 当前线路超时或者断网后当前线路有延迟获取网络状况
      if ((!state.jjsProxy.proxyCurrent.delay || (state.jjsProxy.proxyCurrent.delay && state.jjsProxy.pingCount >= 3)) && state.jjsProxy.pingDone) {
        state.jjsProxy.pingDone = false;
        let p = [];
        let encoding = "gbk";
        let binaryEncoding = "binary";
        // 获取网络延迟
        state.jjsProxy.pingList.map(item => {
          p.push(new Promise(resolve => {
            cp.exec(`ping ${item.url} -w ${state.jjsProxy.timeoutDelay} -n 1`, {encoding: binaryEncoding}, function (err, stdout, stderr) {
              let pingStr = (getIconvDecode(new Buffer(stderr || stdout, binaryEncoding), encoding) || "").replace(/=|\s/g, "");
              let pingList = [];
              if (pingStr.indexOf("Average") > -1) {
                pingList = pingStr.split("Average");
              } else {
                pingList = pingStr.split("平均");
              }
              let pingDelay = pingList[pingList.length - 1];
              if (pingList.length > 1) {
                item.delay = parseInt(pingDelay);
              } else {
                item.delay = "";
              }
              state.jjsProxy.pingInfo[item.key] = item.delay;
              state.logger.writeLogFile(10, JSON.stringify(item));
              resolve();
            });
          }));
        });
        Promise.allSettled(p).then(async res => {
          state.jjsProxy.pingDone = true;
          // 设置ping异常次数
          if (!state.jjsProxy.pingInfo["baidu"] && !state.jjsProxy.pingInfo["qq"]) {
            state.jjsProxy.pingCount++;
          } else {
            state.jjsProxy.pingCount = 0;
          }
        });
      }
    } else {
      // 直接设置失败3次提示网络异常
      state.jjsProxy.pingCount = 3;
      state.jjsProxy.onLine = false;
      state.jjsProxy.proxyCurrent.delay = "";
      state.jjsProxy.proxyAbroad.delay = "";
      state.jjsProxy.proxyList.map(item => {
        item.delay = "";
        item.time = "";
      });
      state.jjsProxy.proxyTemp.map(item => {
        item.delay = "";
      });
      commit("setJjsProxy");
    }
    let proxyCurrentIndex = state.jjsProxy.proxyList.findIndex(item => {return item.key == info.key});
    if (proxyCurrentIndex > -1) {
      state.jjsProxy.proxyCurrent = state.jjsProxy.proxyList[proxyCurrentIndex];
    } else if (info.key == "abroad") {
      state.jjsProxy.proxyCurrent = state.jjsProxy.proxyAbroad;
    } else if (info.proxy) {
      state.jjsProxy.proxyCurrent = info.proxy;
    } else {
      state.jjsProxy.proxyCurrent = state.jjsProxy.proxyList[0];
    }
    // 设置全部线路失败次数
    if (info.error) {
      // 百度、qq正常且全部线路失败才设置
      if (state.jjsProxy.pingCount == 0) {
        state.jjsProxy.errorCount++;
        if (state.jjsProxy.errorCount == state.jjsProxy.errorCountNum && Date.now() - state.jjsProxy.sendMsgTime > 2 * 60 * 60 * 1000) {
          // 设置微信预警-2小时预警一次
          state.jjsProxy.sendMsgTime = Date.now();
          commit("sendWxMsg");
        }
      }
    } else {
      state.jjsProxy.errorCount = 0;
      // 恢复网络重置
      if (!isOnLine && state.jjsProxy.onLine) {
        state.jjsProxy.pingCount = 0;
      }
    }
  },
  // 图片加载失败切换离线 thisElm-加载元素，thisElmSrc-图片原地址，timerKey-定时器名称，time-定时器时间，type-1消息列表小图-2查看图片大图
  setReloadImage: function ({state, commit, dispatch, getters}, info) {
    let {thisElm, thisElmSrc, timerKey, time, type, error, notChange} = info;
    if (/data:image\//.test(thisElmSrc)) {
      thisElm.setAttribute("src", thisElmSrc);
      return;
    }
    // 图片加载定时器
    let timer = getters.getImageLoadTimer[timerKey];
    if (timer && !(error || thisElm.src == location.origin + "/img/image_default.png?reloadFlag=true")) {
      commit("setImageLoadTimer", {type: "del", key: timerKey});
      timer = "";
    }
    if (error || thisElm.src == location.origin + "/img/image_default.png?reloadFlag=true") {
      // 30s内加载失败切换路径加载
      let reloadSrc = thisElmSrc;
      if (config.easePicUrlReg.test(thisElm.getAttribute("pre-src"))) {
        if ((localStorage.getItem("imgHostType") || 1) == 1) {
          // 线路一：切换天翼云
          reloadSrc = thisElmSrc.replace(config.easePicUrlOtherReg, config.jjsPicUrlOther).split("&")[0];
        } else {
          // 线路二：切换腾讯云
          reloadSrc = thisElmSrc.replace(config.easePicUrlReg, config.jjsPicUrlDefault);
        }
      }
      console.log(`reloadPic1-showPicTimeTimeout${time}`, reloadSrc);
      let preTime = thisElm.getAttribute("pre-time");
      // 3s后重新加载
      let delayTime = 3000;
      // 延迟时间默认为100
      let timerTime = 100;
      if (preTime && Date.now() - preTime < delayTime) {
        timerTime = delayTime - (Date.now() - preTime);
      }
      setTimeout(() => {
        if (!/\/img\/image_not_found/.test(thisElm.src)) {
          // 天翼云、腾讯云线路轮流切换
          let preloadSrc = thisElm.getAttribute("pre-src");
          if (preloadSrc) {
            reloadSrc = preloadSrc;
          }
          if (new RegExp(config.jjsPicUrlOther).test(reloadSrc)) {
            reloadSrc = thisElmSrc.replace(config.easePicUrlReg, config.jjsPicUrlDefault);
          } else if (new RegExp(config.jjsPicUrlDefault).test(reloadSrc)) {
            reloadSrc = thisElmSrc.replace(config.easePicUrlReg, config.jjsPicUrl3);
          } else {
            reloadSrc = thisElmSrc.replace(config.easePicUrlOtherReg, config.jjsPicUrlOther).split("&")[0];
          }
          thisElm.setAttribute("src", getImageQualityUrl(reloadSrc, notChange));
        }
      }, timerTime);
    } else if (thisElm.src == location.origin + "/img/image_default.png" || thisElm.src == location.origin + "/img/image_default.png?reload=true") {
      thisElm.setAttribute("src", getImageQualityUrl(thisElmSrc, notChange));
      // 默认加载云信路线
      timer = setTimeout(function () {
        // 加载time时间，然后加载腾讯线路
        let reloadSrc = "";
        if ((localStorage.getItem("imgHostType") || 1) == 1) {
          // 线路一：切换天翼云
          reloadSrc = thisElmSrc.replace(config.easePicUrlOtherReg, config.jjsPicUrlOther).split("&")[0];
        } else {
          // 线路二：切换腾讯云
          reloadSrc = thisElmSrc.replace(config.easePicUrlReg, config.jjsPicUrlDefault);
        }
        console.log(`reloadPic2-showPicTimeTimeout${time}`, reloadSrc)
        thisElm.setAttribute("src", getImageQualityUrl(reloadSrc, notChange));
        timer = setTimeout(function () {
          console.log(`reloadPic2-showPicTimeTimeout${time}-tx`, reloadSrc);
          // 停止加载图片
          thisElm.setAttribute("src", `/img/image_reload${type == 1 ? "_min" : ""}.png`);
        }, time);
        commit("setImageLoadTimer", {type: "add", key: timerKey, timer: timer});
        // 判断图片是否不存在
        try {
          let picObj = new URL(thisElmSrc);
          let options = {
            host: picObj.host,
            port: picObj.port,
            path: (picObj.pathname + picObj.search).replace(/\/\//g, "/"),//双斜杠的地址会被重定向下载失败
          };
          http.get(options, function (res) {
            console.log("picReload-statusCode:", res.statusCode);
            // 不存在替换图片
            if (res.statusCode == 404) {
              thisElm.onload = null;
              thisElm.setAttribute("src", `/img/image_not_found${type == 1 ? "_min" : ""}.png`);
              console.log("picReload404:", thisElmSrc);
              // 清除定时器
              clearTimeout(timer);
            }
          }).on("error", function (e) {
            console.log("picReloadError:", thisElmSrc, e);
          });
        } catch (e) {
        }
      }, time);
      commit("setImageLoadTimer", {type: "add", key: timerKey, timer: timer});
    }
  },
  // 获取电脑状态
  doCheckAuth: async function ({state, commit, dispatch, getters}, info) {
    // 初始化获取电脑类型
    if (info?.init) {
      state.serverFlagMap["doCheckAuth"] = "loading";
    }
    let secret = await dispatch("getSecretEnCrypt", {param: {}});
    let res = await doCheckAuthApi({
      "hdSerialNumber": state.netComputerInfo.SerialNumber,
      "hdModelNumber": state.netComputerInfo.Model,
      "computerId": state.netComputerInfo.ComputerId,
      "macAddress": state.netComputerInfo.Mac[0],
      "netIds": state.netComputerInfo.Mac[1] || state.netComputerInfo.Mac[0],
      "devMd5": secret,
      "scanType": info?.scanType
    });
    if (res?.type != null) {
      state.deviceObj = {
        type: res.type,
        isCompany: res.type != 4
      };
      if (info?.init) {
        state.serverFlagMap["doCheckAuth"] = true;
      }
    } else {
      if (info?.init) {
        state.serverFlagMap["doCheckAuth"] = "";
      }
    }
    return res;
  },
  // 查询全部应用
  getMenuList: async function ({state, commit, dispatch, getters}, info) {
    state.serverFlagMap["getMenuList"] = "loading";
    let res = await getMenuListApi({
      msgBody: JSON.stringify({
        appForm: {
          isApp: 0,
          deviceType: state.deviceObj.type,
        }
      })
    });
    if (res?.success) {
      state.serverFlagMap["getMenuList"] = true;
      state.allAppList = res?.data?.data || [];
    } else {
      state.serverFlagMap["getMenuList"] = "";
    }
  },
  // 获取新系统登录秘钥
  getLoginSecret: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let timeObj = await state.nimSDK.getServerTime();
      if (timeObj.err) {
        console.log("getServerTimeError", timeObj.err);
        timeObj.obj = Date.now() + state.diffTime;
      }
      if (!state.computerInfo.macAddress) {
        alert({content: "获取设备MAC错误"});
        return;
      }
      if (Math.abs(Date.now() + state.diffTime - state.apiTimeMap["getSecretEnCrypt"]) < 1000) {
        resolve({success: false, errorMsg: "请勿频繁操作"});
        return;
      }
      let secret = await dispatch("getSecretEnCrypt", {time: timeObj.obj});
      let res = await getLoginSecretApi({
        msgBody: JSON.stringify({
          time: dateFormat(timeObj.obj, "MMddHHmm"),
          netId: state.computerInfo.macAddress,
          ip: state.baseComputerInfo.address,
          imei: state.baseComputerInfo.hostName,
          workerId: state.userInfo.workerId,
          token: state.userInfo.token,
          devMd5: secret,
          type: "2",
          jumpUrl: info.url,
        })
      });
      if (res?.success) {
        state.apiTimeMap["getSecretEnCrypt"] = Date.now() + state.diffTime;
      }
      resolve(res);
    });
  },
  // 获取电脑信息密钥
  getSecretEnCrypt: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      if (remote.store.state.computerMap.s0 == null) {
        let systemInfoData = await getAllComputerInfo();
        // 设置电脑基础信息
        remote.store.commit("setComputerInfo", systemInfoData);
      }
      let time = Date.now() + remote.store.state.diffTime;
      if (info.time) {
        time = info.time;
      } else {
        let res = await getTimeApi();
        if (res?.data?.time) {
          time = res?.data?.time;
          state.diffTime = time - Date.now();
        }
      }
      let param = {};
      let oldParam = {t: time, ...remote.store.state.computerMap};
      // if (remote.privateKey) {
      //   oldParam.signData = String(time);
      //   oldParam.sign = signRsa(oldParam.signData, remote.privateKey);
      // }
      if (info.param) {
        param = Object.assign(oldParam, info.param);
      } else {
        param = oldParam;
      }
      if (info.notRsa) {
        resolve(param);
      } else {
        resolve(encryptRsa(JSON.stringify(param)));
      }
    });
  },
  // 获取发送消息的唯一值和时间
  getMsgInfo: function ({state, commit, dispatch, getters}, info) {
    let {sessionId} = info;
    let idInfo = sessionId.split("-");
    let scene = idInfo[0];
    let account = idInfo[1];
    uniqueSignCount++;
    let uniqueSign = MD5(`sign_${scene}_${account}_${Date.now()}_${uniqueSignCount}`);
    // 发送失败重发
    if (info.uniqueSign) {
      uniqueSign = info.uniqueSign;
    }
    // 当前时间小于最后一条消息时间
    let thisMsgTime = Date.now() + state.diffTime;
    let thisSessionMsgs = state.msgs[sessionId];
    if (thisSessionMsgs && thisSessionMsgs.length > 0 && thisSessionMsgs[thisSessionMsgs.length - 1].time > thisMsgTime) {
      thisMsgTime = thisSessionMsgs[thisSessionMsgs.length - 1].time + 100;
    }
    return {scene, account, uniqueSign, thisMsgTime};
  },
  // 发送文档连接转卡片消息
  getLinkResult: function ({state, commit, dispatch, getters}, info) {
    return new Promise(resolve => {
      let {sessionId, messages, thisMsgInfo} = info;
      let msgP = [];
      let sendMessages = [];
      let sendFlag = false;
      let docFlag = false;
      let projectFlag = false;
      // 需要判断的类型数量，乐文档+房源链接
      let promiseAllCount = 2;
      messages.map(function (msgItem) {
        let promiseCount = 0;
        // 获取文本中的链接
        if (msgItem.type == "text") {
          let linkList = msgItem.text.match(getLink("", true)) || [];
          let linkStr = msgItem.text;
          // 发送状态显示的字符串
          let sendStr = msgItem.text;
          // 乐文档类型的链接回调-需要请求接口获取详情
          let idList = [];
          let ids = [];
          // 房源链接
          let projectList = [];
          let projects = [];
          // 消息切割文档类型消息列表
          linkList.map(function (item) {
            try {
              let urlObj = new URL(item);
              let docId = getStrParam(urlObj.search, "docId");
              let fhId = getStrParam(urlObj.search, "fhId");
              // 判断链接是否有文档，有则请求数据转为卡片消息（自定义消息）
              if (config[config.env].host == urlObj.host && /(\/lyjEtherpad\/(p\/|(externalSharing\/read)))|(\/lyj-front\/sheet\/index.html)/.test(urlObj.pathname) && docId) {
                sendFlag = true;
                linkStr = linkStr.replace(item, "$documentReplaceText$documentReplaceText");
                idList.push({id: docId, $documentReplaceText: item});
                ids.push(docId);
              }
              // 判断链接是否有房源链接，有则请求数据转为卡片消息（自定义消息）
              if (config[config.env].host == urlObj.host && /\/hsl\/house\/house-detail/.test(urlObj.pathname) && fhId) {
                let replaceText = "projectReplaceText----projectReplaceText";
                linkStr = linkStr.replace(item, replaceText).replace(new RegExp(`\n?${replaceText}`), "");
                sendStr = sendStr.replace(item, replaceText).replace(new RegExp(`\n?${replaceText}`), "");
                projectList.push({type: "text", text: item, fhId: fhId});
                projects.push(fhId);
                msgItem.text = sendStr;
              }
            } catch (e) {
            }
          });
          let itemP = new Promise(function (itemResolve) {
            // 获取文档连接所有数据
            if (ids.length > 0) {
              detailsDocApi({ids: ids.join(",")}).then(function (res) {
                if (res.success && res.data && res.data.length > 0) {
                  let arr = res.data;
                  let newMessage = linkStr.split("$documentReplaceText");
                  // 文本@计数
                  let haitIndex = 0;
                  for (let i = 0; i < newMessage.length; i++) {
                    if (newMessage[i]) {
                      newMessage[i] = {type: "text", text: newMessage[i]};
                      // 当前消息@计数
                      let currHaitIndex = 0;
                      // 存在@人员数据插入对应字段
                      newMessage[i].text.replace(/@/g, function (item, key) {
                        if (msgItem.haitPosition && msgItem.haitPosition.findIndex(function (item) {return item == haitIndex}) > -1) {
                          if (!newMessage[i].haitPosition) {
                            newMessage[i].hait = [];
                            newMessage[i].haitPosition = [];
                          }
                          newMessage[i].hait.push(msgItem.hait[msgItem.haitPosition.findIndex(function (item) {return item == haitIndex})]);
                          newMessage[i].haitPosition.push(currHaitIndex);
                        }
                        haitIndex++;
                        currHaitIndex++;
                        return item;
                      });
                    }
                  }
                  for (let i = 0; i < idList.length; i++) {
                    let detailItem = idList[i];
                    let docResFlag = false;
                    for (let j = 0; j < arr.length; j++) {
                      let docObj = arr[j];
                      if (docObj && docObj.id == detailItem.id && docObj.padId && docObj.empNumber && docObj.title) {
                        docResFlag = true;
                        docFlag = true;
                        let docName = docObj.title;
                        if (docName.length > 20) {
                          docName = docName.slice(0, 20) + "...";
                        }
                        docName += (docObj.suffix || (docObj.property == 2 ? ".xlsx" : ".docx"));
                        for (let k = 0; k < newMessage.length; k++) {
                          if (!newMessage[k]) {
                            newMessage[k] = {
                              type: "document",
                              file: {
                                docPadId: docObj.padId,
                                docName: docName,
                                docId: docObj.id,
                                docImg: docObj.imgPath,
                                empNumber: docObj.empNumber,
                                property: docObj.property,
                              }
                            };
                            break;
                          }
                        }
                        break;
                      }
                    }
                    // 不存在则还原消息体
                    if (!docResFlag) {
                      for (let k = 0; k < newMessage.length; k++) {
                        if (!newMessage[k]) {
                          newMessage[k] = {type: "text", text: detailItem.$documentReplaceText};
                          break;
                        }
                      }
                    }
                  }
                  promiseCount++;
                  if (promiseCount == promiseAllCount) {
                    itemResolve(newMessage);
                  }
                } else {
                  promiseCount++;
                  if (promiseCount == promiseAllCount) {
                    itemResolve([msgItem]);
                  }
                }
              }).catch(function () {
                promiseCount++;
                if (promiseCount == promiseAllCount) {
                  itemResolve([msgItem]);
                }
              });
            } else {
              promiseCount++;
            }
            // 获取房源连接所有数据
            if (projects.length > 0) {
              // 存在房源链接发送卡片
              projectFlag = true;
              let projectMsgList = [];
              let projectP = [];
              projectList.map(projectItem => {
                projectP.push(new Promise(async resolve => {
                  let msgInfo = await dispatch("getMsgInfo", info);
                  let {scene, account, uniqueSign, thisMsgTime, fileMsgFlag} = msgInfo;
                  // 单独发送房源链接
                  let defaultMsg = {
                    uniqueSign: uniqueSign,
                    fileMsgFlag: fileMsgFlag,
                    id: sessionId,
                    sessionId: sessionId,
                    from: state.userInfo.workerNo,
                    status: "toSending",
                    to: account,
                    scene: scene,
                    messages: [projectItem],
                    time: thisMsgTime,
                    text: projectItem.text,
                    type: projectItem.type,
                    fhId: projectItem.fhId
                  }
                  // 设置消息发送状态
                  dispatch("setMsgs", {id: sessionId, msgs: [defaultMsg]}).then(res => {
                    debounce({
                      timerName: "setMsgScroll",
                      time: 100,
                      fnName: function () {
                        // 发送消息滚动到底部
                        store.commit("setEmit", {type: "scroll", value: "bottom"});
                      }
                    });
                  });
                  projectMsgList.push(defaultMsg);
                  resolve();
                }));
              });
              Promise.all(projectP).then(function (arr) {
                // 获取链接详情后发送
                queryEsfCardFieldsApi({
                  fhIds: projects.join(","),
                  workerId: state.userInfo.workerId,
                  force: true,
                }).then(function (res) {
                  let dataList = res?.data || [];
                  projectMsgList.map(projectItem => {
                    let itemFlag = false;
                    for (let i = 0; i < dataList.length; i++) {
                      let thisItem = dataList[i];
                      if (thisItem.fhId == projectItem.fhId) {
                        // 存在房源数据转为自定义消息
                        let projectContent = {
                          data: {
                            content: `${thisItem.room || 0}室${thisItem.hall || 0}厅  ${thisItem.buildArea || 0}m²  ${thisItem.orientation?.name || ""}`,
                            contentRent: thisItem.rentPrice > 0 ? `租${thisItem.rentPrice}元/月` : "",
                            contentSell: thisItem.salePrice > 0 ? `售${thisItem.salePrice || 0}万` : "",
                            image: thisItem.coverUrl,
                            source: "app-jjrj",
                            title: thisItem.comName,
                            other: {
                              houseId: thisItem.fhId,
                              houseType: 2,
                              rsType: thisItem.rentPrice > 0 ? 1 : 2
                            }
                          },
                          type: 7
                        }
                        dispatch("sendMsg", {...projectItem, messages: [{type: "custom", content: projectContent, pushContent: "[乐聊房源分享]"}], notGetLink: true});
                        itemFlag = true;
                        break;
                      }
                    }
                    // 不存在房源数据发送文本消息
                    if (!itemFlag) {
                      dispatch("sendMsg", {...projectItem, notGetLink: true});
                    }
                  });
                });
              });
              promiseCount++;
              if (promiseCount == promiseAllCount) {
                itemResolve([{...msgItem, text: linkStr}]);
              }
            } else {
              promiseCount++;
            }
            // 不存在直接返回
            if (ids.length == 0 && projects.length == 0) {
              itemResolve([msgItem]);
            }
          });
          msgP.push(itemP);
          sendMessages.push(msgItem);
        } else {
          msgP.push([msgItem]);
          sendMessages.push(msgItem);
        }
      });
      if (sendFlag) {
        // 发送链接消息的时候请求服务器有延迟，需要先显示loading
        let defaultMsg = {
          uniqueSign: thisMsgInfo.uniqueSign,
          fileMsgFlag: thisMsgInfo.fileMsgFlag,
          id: sessionId,
          sessionId: sessionId,
          custom: JSON.stringify({}),
          content: JSON.stringify({type: "multi", msgs: sendMessages}),
          from: state.userInfo.workerNo,
          status: "toSending",
          to: thisMsgInfo.account,
          scene: thisMsgInfo.scene,
          messages: sendMessages,
          time: thisMsgInfo.thisMsgTime,
          text: sendMessages.length > 1 ? "" : sendMessages[0].text,
          type: sendMessages.length > 1 ? "custom" : sendMessages[0].type
        }
        // 设置消息发送状态
        dispatch("setMsgs", {id: sessionId, msgs: [defaultMsg]}).then(res => {
          setTimeout(() => {
            // 发送消息滚动到底部
            store.commit("setEmit", {type: "scroll", value: "bottom"});
          }, 100);
        });
      }
      Promise.all(msgP).then(function (arr) {
        let msgList = [];
        arr.map(function (item) {
          if (item) {
            msgList = msgList.concat(item);
          }
        });
        if (docFlag || projectFlag) {
          // 请求成功-以自定义消息类型发送
          resolve(getMsgTrim(msgList));
        } else {
          // 请求失败-以文本类型发送
          resolve(getMsgTrim(sendMessages));
        }
      });
    });
  },
  // 设置本地时间和服务器时间差
  setDiffTime: function ({state, commit, dispatch, getters}, messages) {
    return new Promise(resolve => {
      try {
        remote.store.getters.getNim.getServerTime().then(res => {
          if (!res.err) {
            state.diffTime = res.obj - Date.now();
          }
          resolve(res);
        });
      } catch (e) {
        resolve({res: Date.now()});
      }
    })
  },
  // 设置聊天子窗口
  setChildWin: function ({state, commit, dispatch, getters}, info) {
    try {
      state.emit.childWinChange = {...info, time: Date.now()};
      if (info.type == "del") {
        // 关闭子窗口
        if (state.childWin) {
          delete state.childWin[info.name];
        }
        delete window.global.childWin[info.name];
        // 删除子窗口的@和特别关心消息和sse服务
        let sessionId = (info.name || "").split("chat-")[1];
        dispatch("setMsgTipsClear", {type: "sessionDB", id: sessionId});
        for (let key in state.sseMap) {
          if (state.sseMap[key].sessionId == sessionId) {
            dispatch("setSseListener", {type: "remove", id: key, delete: true});
          }
        }
        // 子窗口存在语音则停止播放
        if ("chat-" + remote.store.state.audioObj.id == info.name) {
          remote.store.dispatch("setAudioObj", {});
        }
      } else {
        // 打开子窗口
        if (state.childWin) {
          state.childWin[info.name] = info.win;
          // 打开聊天子窗口切换下个会话
          if (info.id) {
            dispatch("changeNextSession", info.id);
          }
        }
      }
    } catch (e) {
      console.log("setChildWinErr", e);
    }
  },
  // 重发消息
  resendMsg: function ({state, commit, dispatch, getters}, thisMsg) {
    // 对应字段转为字符串
    if (thisMsg.custom && typeof thisMsg.custom != "string") {
      thisMsg.custom = JSON.stringify(thisMsg.custom);
    }
    if (thisMsg.content && typeof thisMsg.content != "string") {
      thisMsg.content = JSON.stringify(thisMsg.content);
    }
    // 删除对应重发消息
    thisMsg.toDel = true;
    dispatch("setMsgs", {id: thisMsg.sessionId, msgs: [deepClone(thisMsg)]});
    delete thisMsg.toDel;
    if (thisMsg.type == "file" || /data:image\//.test(thisMsg.custom) || /data:image\//.test(thisMsg.content) || (thisMsg.file && (!thisMsg.file.url || /data:image\//.test(thisMsg.file.url)))) {
      console.log("resendMsg1", thisMsg.uniqueSign);
      thisMsg.resendFlag = true;
      dispatch("sendMsg", thisMsg);
    } else if (thisMsg.type == "text") {
      console.log("resendMsg3", thisMsg.uniqueSign);
      state.nimSDK.resendMsg(thisMsg).then(res => {
        dispatch("sendMsgDone", res);
      });
    } else {
      console.log("resendMsg2", thisMsg.uniqueSign);
      state.nimSDK.forwardMsg({
        scene: thisMsg.scene,
        to: thisMsg.to,
        msg: thisMsg,
      }).then(res => {
        dispatch("sendMsgDone", res);
      });
    }
  },
  // 房产网客户备注
  fcwRemark: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      loading();
      let param = {
        msgBody: JSON.stringify({
          empNo: state.userInfo.workerNo,
          key: config.settings.type9,
          value: JSON.stringify([info]),
          operation: info.remark ? "add" : "delete",
          type: 9
        })
      }
      let res = await fcwRemarkApi(param)
      loading().hide();
      if (res && res.data && res.data.data) {
        getters.getNim.modifyMyInfo(param); //同步多端
        if (info.remark) {
          // 修改客户备注
          if (!state.settings[config.settings.type9]) {
            state.settings[config.settings.type9] = {};
          }
          state.settings[config.settings.type9][info.accid] = info;
        } else {
          delete state.settings[config.settings.type9][info.account];
        }
      }
      resolve(res);
    })
  },
  // 收藏消息
  collectMsg: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let {sessionInfo, sourceName, sourceIcon, sourceId, groupId, msg} = info;
      msg = deepClone(msg);
      let sourceType = "";
      if (sessionInfo.scene == "p2p") {
        if (sessionInfo.to == state.userInfo.workerNo) {
          sourceType = 4
        } else {
          sourceType = 3
        }
      } else if (sessionInfo.scene == "team") {
        if (sessionInfo.detailInfo.detailType == "group") {
          sourceType = 1
        } else {
          sourceType = 2
        }
      } else if (sessionInfo.scene == 'superTeam') {
        sourceType = 2
      }
      let type = msg.type;
      let collectHtml = "";
      // 自定义消息字段-目前只有乐文档使用
      if (msg.type == "custom") {
        switch (String(msg.content.type)) {
          case "5":
          case "6":
          case "7":
          case "8":
          case "9":
          case "collect":
            type = "link";
            break;
          case "multi":
            // 图文消息类型
            type = "custom";
            let multiMsg = msg.content.msgs;
            for (let i = 0; i < multiMsg.length; i++) {
              if (multiMsg[i].type == "text") {
                if (multiMsg[i].text && multiMsg[i].text.trim() != "") {
                  collectHtml += `<span>${multiMsg[i].text}</span>`;
                }
              } else if (multiMsg[i].type == "image") {
                collectHtml += `<img src="${multiMsg[i].file.url}">`;
              } else if (multiMsg[i].type == "document") {
                collectHtml += "[乐文档]";
              }
            }
            break;
        }
      }
      let params = {
        "sourceClientType": "pc",
        "type": type,
        "empNo": state.userInfo.workerNo,
        "sourceType": sourceType,
        "html": collectHtml,
        "sourceName": sourceName,
        "sourceIcon": sourceIcon,
        "sourceId": sourceId,
        "msgIdServer": msg.idServer,
        "msgScene": msg.scene == 'superTeam' ? 'super_team' : msg.scene,
        "msgTo": msg.to,
        "msgIdClient": msg.idClient,
        "msgTime": msg.time,
        "groupId": groupId
      }
      if (type == "text") {
        // 文本消息
        params.text = msg.text;
      } else if (type == "custom") {
        msg = dealMsgField(msg);
        // 自定义消息
        params.custom = msg.custom;
        params.content = msg.content;
      }
      let res = await editCollectContentApi({msgBody: JSON.stringify(params)});
      resolve(res);
    });
  },
  // 窗口抖动
  windowShake: function ({state, commit, dispatch, getters}, info) {
    let currTime = new Date().getTime();
    let shakeTime = getters.getShakeTimeMap(info.id) || 0;
    if (currTime - shakeTime > 10 * 1000) {
      commit("setShakeTimeMap", {id: info.id, time: currTime});
      queryAllSettingApi({
        msgBody: JSON.stringify({
          empNo: info.to,
          key: "shake_shield"
        })
      }).then(res => {
        let heShakeStatus = 0;
        if (res && res.data && res.data.data && res.data.data[0] && res.data.data[0].value) {
          heShakeStatus = res.data.data[0].value;
        }
        getters.getNim.sendCustomMsg({
          scene: info.scene,
          to: info.to,
          pushContent: "[抖一抖]",
          content: JSON.stringify({
            type: "shake",
            data: {value: state.userInfo.name + '发送了一个窗口抖动'},
            heShakeStatus: heShakeStatus
          }),
        }).then((res) => {
          // 发送成功抖动自己窗口
          if (res.err) {
            toast({title: (res.err.message || "操作失败请重试") + res.err.code, type: 2});
          } else {
            dispatch("shakeWindow", {id: info.id});
            dispatch("sendMsgDone", res);
          }
        });
      })
    } else {
      toast({title: "您发送窗口抖动过于频繁，请稍后再发", type: 2});
    }
  },
  // 上传日志文件
  uploadLogFile: async function ({state, commit, dispatch, getters}, info) {
    let logFileInfo = getLogFileInfo(info.type, info.no, info.date);
    if (fs.existsSync(path.join(logFileInfo.path, logFileInfo.name))) {
      let tokenRes = await getLoggerTokenApi();
      let file = await getLocalFile(logFileInfo);
      let qiNiuRes = await uploadToQiNiu(file, tokenRes.data.token, `pc-im/log/${logFileInfo.name}_${dateFormat(new Date, "HH-mm-ss")}`);
      let uploadRes = await uploadFilePathApi({
        msgBody: JSON.stringify({
          logType: info.type,
          empNumber: state.userInfo.workerId,
          logLink: `${tokenRes.data.qiniuDomain}/${qiNiuRes.key}`,
        }),
      });
      console.log("uploadLogFile", uploadRes)
    }
  },
  // 稍后提醒日程
  remindLater: async function ({state, commit, dispatch, getters}, info) {
    let {item, minute} = info;
    if (state.remindLaterTimer) {
      toast({title: "请勿频繁操作", type: 2});
      return;
    }
    state.remindLaterTimer = setTimeout(() => {
      state.remindLaterTimer = "";
    }, 3000);
    if (item.content.type == "schedule_remind") {
      // 智能日程稍后提醒
      let res = await smartRemindApi({
        scheduleId: item.content.schedule.scheduleId,
        type: 2,
        time: minute
      });
      toast({title: res.errorMsg || "操作成功", type: res.success ? 1 : 2});
    } else if (item.content.type == "msg-center" || item.content.type == "msg-center-link") {
      let res = await msgTipApi({
        msgBody: JSON.stringify({
          msgIdServer: item.idServer,
          minute: minute,
          empNo: state.userInfo.workerNo,
          empNumber: state.userInfo.workerId
        })
      });
      toast({title: res.errorMsg == "success" ? "操作成功" : res.errorMsg, type: res.success ? 1 : 2});
    }
  },
  // 跳转消息平台
  toMsgCenterLink: async function ({state, commit, dispatch, getters}, info) {
    let {item, isTips} = info;
    if (item.type == "link_button" && item.url) {
      // 普通按钮跳转浏览器
      dispatch("setOpenWindow", [linkFormat(item.url), "login"]);
    } else if ((item.type == "custom_button" || item.type == "link") && (item.url || item.pcUrl || item.pcClickTips)) {
      // 自定义按钮
      if (item.urlType == 1 || item.urlType == null) {
        // 跳转浏览器
        if (item.pcClickTips) {
          toast({title: item.pcClickTips});
        } else {
          dispatch("setOpenWindow", [linkFormat(item.url || item.pcUrl), item.open ? "jjsHome" : "login", {w: item.pc?.w, h: item.pc?.h, noSize: item.pc?.noSize}]);
        }
      }
    } else {
      if (isTips) {
        toast({title: "该按钮未配置跳转地址"});
      }
    }
  },
  // 识别二维码
  setQrcode: async function ({state, commit, dispatch, getters}, res) {
    if (/jjsoaSuper:|jjsoa:/.test(res.data)) {
      let codeInfo = res.data.split(":");
      let scene = codeInfo[0] == "jjsoaSuper" ? "superTeam" : "team";
      let teamId = codeInfo[1];
      // 获取群信息
      res.loading();
      let teamInfo = await remote.store.state.nimSDK.getTeam({
        type: scene,
        account: teamId
      });
      res.loading().hide();
      if (teamInfo.err) {
        res.alert({content: teamInfo.err.code == 803 ? "群/组不存在" : teamInfo.err.message, showCancel: false});
        return;
      }
      res.alert({
        content: `<div style="display:flex;align-items:center">
                    <img style="max-width:24px;max-height:24px" src=${teamInfo.obj.avatar || "/img/default/group.png"} onerror="this.src='/img/default/group.png'">
                    <span style="margin-left:10px">${teamInfo.obj.name} (${teamInfo.obj.memberNum})</span>
                  </div>`,
        showCancel: false,
        okText: "加入群聊",
        done: async type => {
          if (type == 1) {
            // 申请入群
            res.loading();
            let applyInfo = await remote.store.state.nimSDK.applyTeam({
              scene: scene,
              teamId: teamId
            });
            res.loading().hide();
            if (applyInfo.err) {
              res.alert({
                content: applyInfo.err.code == 809 ? "您已加入该群/讨论组" : applyInfo.err.message,
                showCancel: false,
                okText: applyInfo.err.code == 809 ? "打开该群/讨论组" : "我知道了",
                done: type => {
                  if (type == 1) {
                    if (applyInfo.err.code == 809) {
                      remote.store.dispatch("setCurrentSession", {id: scene + "-" + teamId})
                    }
                  }
                }
              });
              return;
            }
            if (teamInfo.obj.joinMode == "needVerify") {
              // 群需要审核
              res.alert({content: "入群申请已发出", showCancel: false});
            } else {
              // 直接加入讨论组
              loading();
              setTimeout(() => {
                loading().hide();
                remote.store.dispatch("setCurrentSession", {id: scene + "-" + teamId})
              }, 2000)
            }
          }
        }
      });
    } else {
      res.alert({content: "请扫描公司群或者讨论组二维码", showCancel: false});
    }
  },
  // 判断是否二维码
  getQrcode: function ({state, commit, dispatch, getters}, img) {
    return new Promise(async resolve => {
      let imageData = (await getImageData(img)).imageData;
      let qrcode = jsQR(imageData.data, imageData.width, imageData.height, {inversionAttempts: "dontInvert"});
      resolve(qrcode);
    });
  },
  // 消息滚动到底部
  scrollBottom: async function ({state, commit, dispatch, getters}, info) {
    let currentWindow = getters.getCurrentWindow();
    // 判断是否有子窗口
    if (getChildWin("chat-" + info.sessionId)) {
      currentWindow = getChildWin("chat-" + info.sessionId);
    }
    currentWindow.window.store.commit("setEmit", {type: "scroll", value: "bottom"});
  },
  // 检查更新
  getUpdate: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let moreRes = {};
      let param = {
        type: config.env == "online" ? 3 : config.env == "onlinetest" ? 2 : 1,
        mac: state.computerInfo.macAddress
      }
      if (state.userInfo.workerNo) {
        param.empNo = state.userInfo.workerNo;
      }
      // 登录页面也需要判断vip更新版本号和全量更新最高版本号
      if (info.type == 2 || info.type == 3) {
        let moreParam = deepClone(param)
        moreParam.vip = 1;
        moreRes = await getVersionApi(moreParam);
      }
      let res = await getVersionApi(param);
      // 设置点击检查更新
      if (moreRes.data && moreRes.data.version && (moreRes.data.vipNo || "").indexOf(state.userInfo.workerNo) > -1) {
        if (!res.data || !res.data.version) {
          // 不存在版本号直接返回vip版本
          res = moreRes;
        } else {
          // 存在比较版本号
          if (compareVersion(res.data.version, moreRes.data.version)) {
            res = moreRes;
          }
        }
      }
      if (res.data && res.data.env == config.env && res.data.version && (info.type != 2 || (res.data.vipNo || "").indexOf(state.userInfo.workerNo) > -1)) {
        let updateFlag = compareVersion(config.version, res.data.version);
        commit("setConfigVersion", {serverVersion: updateFlag ? res.data.version : config.version});
      }
      resolve(res);
    });
  },
  // 获取闪退日志数量
  getCrashLogNum: function ({state, commit, dispatch, getters}, info = {}) {
    let crashFilePath = remote.process.env.LOCALAPPDATA + "\\" + state.config.name + "\\User Data\\Crashpad\\reports";
    if (info.type == "remove") {
      unlinkDir(crashFilePath);
      return;
    }
    fs.readdir(crashFilePath, function (err, menu) {
      if (!menu) return;
      let crashLogNum = 0;
      menu.forEach(function (ele) {
        crashLogNum++;
      });
      let localCrashLogNum = localStorage.getItem("crashLogNum");
      // 新增的闪退日志才上传
      if (localCrashLogNum && crashLogNum > Number(localCrashLogNum)) {
        // 上传所有日志
        dispatch("uploadZxp", {type: 0});
      }
      setLocalStorage("crashLogNum", crashLogNum);
    });
  },
  // 上传日志
  uploadZxp: async function ({state, commit, dispatch, getters}, info) {
    if (state.config.env == "online") {
      let zxpParam = {
        account: state.userInfo.workerNo || state.tempUserInfo.workerNo || state.ipInfo.ip,
        ver: state.config.version,
        type: info.type,
        imei: state.baseComputerInfo.hostName + "~" + state.computerInfo.userUUID
      }
      // 存在初始化和加载时间传参
      if (info.load_time) {
        zxpParam.load_time = info.load_time;
      }
      if (info.init_time) {
        zxpParam.init_time = info.init_time;
      }
      // 存在报错信息
      if (info.msg) {
        zxpParam.msg = info.msg;
      }
      zxpApi(zxpParam);
    }
  },
  // 获取是否有收藏话术权限
  getCollectStoryPur: async function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let queryPurviewRes = await queryPurviewApi({
        msgBody: JSON.stringify({
          linkUrl: "/aicpMain/query_purview/shuoci"
        }),
      });
      if (queryPurviewRes.success) {
        state.collectStoryPur = queryPurviewRes?.data?.data;
      }
      resolve(state.collectStoryPur);
    });
  },
  // 获取收藏列表
  getCollectClassify: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let res = await queryCollectClassifyApi({
        msgBody: JSON.stringify({
          empNo: state.userInfo.workerNo
        })
      });
      resolve(res);
    });
  },
  // 获取话术列表-type0全部1公共2私有
  getCollectStoryClassify: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let res = await queryCollectStoryClassifyApi({
        msgBody: JSON.stringify({
          type: info
        })
      });
      resolve(res);
    });
  },
  // 收藏到话术列表
  setCollectStoryContent: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      let param = {
        categoryId: info.parentId,
        contentJson: info.content
      }
      if (info.id != null) {
        param.id = info.id;
      }
      let res = await editCollectStoryContentApi({
        msgBody: JSON.stringify(param)
      });
      resolve(res);
    });
  },
  // 打开窗口
  setOpenWindow: async function ({state, commit, dispatch, getters}, info) {
    let url = info[0];
    if (url) {
      let frameName = info[1]
      url = await getFreeLoginUrl(url, frameName);
      if (!url) {
        return;
      }
      if (!frameName || frameName == "login") {
        remote.Shell.openExternal(url);
      } else if (/jjsHome/.test(frameName)) {
        let width = 1100;
        let height = 700;
        let resizable = true;
        if (info[2] == "demandOrBug") {
          // 转需求页面默认宽高
          width = 1440;
          height = 750;
        }
        // 自定义子窗口宽高和缩放
        if (info[2]?.w && info[2]?.h) {
          width = info[2].w;
          height = info[2].h;
        }
        if (info[2]?.noSize) {
          resizable = false;
        }
        emitMsg("msg", {
          type: "window", newWin: 1, name: "webview", width: width, height: height, minWidth: 800, minHeight: 550, changePath: true, frame: true, resizable: resizable,
          path: url,
        });
      }
      console.log("setOpenWindow", encrypt(url));
    }
  },
  // 房产网客户判断是否存在跳转链接-缓存10分钟
  setFcwLink: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      // 房产网客户判断是否存在跳转链接
      if (!state.fcwServerInfo[info]) {
        state.fcwServerInfo[info] = {};
        state.fcwServerInfo[info].linkTime = Date.now();
      }
      if (Date.now() - (state.fcwServerInfo[info]?.linkTime || 0) > 10 * 60 * 1000) {
        searchCustomerLinkApi({
          yxId: info,
          workerId: state.userInfo.workerId
        }).then(res => {
          if (res.success) {
            state.fcwServerInfo[info].url = res?.data;
            state.fcwServerInfo[info].linkTime = Date.now();
          } else {
            delete state.fcwServerInfo[info].linkTime;
          }
          resolve(state.fcwServerInfo[info]);
        });
      } else {
        resolve(state.fcwServerInfo[info]);
      }
    });
  },
  // 房产网客户判断是否是专属经纪人绑定-缓存10分钟
  setFcwZsLabel: function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      // 房产网客户判断是否是专属经纪人绑定
      if (!state.fcwServerInfo[info]) {
        state.fcwServerInfo[info] = {};
        state.fcwServerInfo[info].zsLabelTime = Date.now();
      }
      if (Date.now() - (state.fcwServerInfo.zsLabelTime || 0) > 10 * 60 * 1000) {
        fcwBindApi({
          msgBody: JSON.stringify({
            faccid: info,
            empNumber: state.userInfo.workerId
          })
        }).then(res => {
          if (res.success) {
            state.fcwServerInfo[info].zsLabel = res?.data?.data;
            state.fcwServerInfo[info].zsLabelTime = Date.now();
          } else {
            delete state.fcwServerInfo[info].zsLabelTime;
          }
          resolve(state.fcwServerInfo[info]);
        });
      } else {
        resolve(state.fcwServerInfo[info]);
      }
    });
  },
  // 插入本地会话
  insertLocalSession: async function ({state, commit, dispatch, getters}, info) {
    let id = info.scene + "-" + info.to;
    if (info.scene != "p2p" && !state.teams[info.to]) {
      // 不存在的群不添加
      return;
    }
    if (!store.getters.getSessions({id: id}).to) {
      // 不存在会话则插入本地
      let insertRes = await state.nimSDK.insertLocalSession({
        scene: info.scene,
        to: info.to,
        updateTime: info.updateTime || 1
      });
      if (insertRes.err?.code == "Session_Exist") {
        insertRes.err.event.session.insert = true;
        insertRes.err.event.session.updateTime = 1;
        commit("setSessions", insertRes.err.event.session);
      }
    }
  },
  // 发送讨论组分组多端同步自定义系统消息
  sendCustomSysMsgByGroups: function ({state, commit, dispatch, getters}, info) {
    // 发送通知给自己和别人(用于多端同步)
    let param = {
      type: "refreshGroups",
      from: 1//1-pc 2-ios 3-android
    }
    if (info?.groupList) {
      param.value = info.groupList.toString();
    }
    if (info?.personList) {
      param.person = info.personList.toString();
    }
    store.getters.getNim.sendCustomSysMsg({
      scene: "p2p",
      to: state.userInfo.workerNo,
      content: JSON.stringify(param),
      done: function () {}
    });
  },
  // 清除@我和提示
  setMsgTipsClear: async function ({state, commit, dispatch, getters}, info) {
    let {id, type, clearAll} = info;
    if (type == "sessionDB") {
      let sessionHaitMap = await dispatch("getSessionReadTime", state.haitMsgMap);
      let sessionConcernMap = await dispatch("getSessionReadTime", state.concernMsgMap);
      // 判断移除@消息
      dispatch("setHaitOrConcernMsg", {id: id, sessionMap: sessionHaitMap, clearAll: clearAll, msgMap: state.haitMsgMap});
      // 判断移除特别关心消息
      dispatch("setHaitOrConcernMsg", {id: id, sessionMap: sessionConcernMap, clearAll: clearAll, msgMap: state.concernMsgMap});
      userCache({key: "haitMsgMap", value: state.haitMsgMap}, 1);
      userCache({key: "concernMsgMap", value: state.concernMsgMap}, 1);
    } else if (type == "remove") {
      // 删除会话的@和特别关心消息
      let chatChildObj = getChildWin("chat-" + id, "", true);
      if (!chatChildObj.isExit) {
        delete state.haitMsgMap[id];
        delete state.concernMsgMap[id];
        userCache({key: "haitMsgMap", value: state.haitMsgMap}, 1);
        userCache({key: "concernMsgMap", value: state.concernMsgMap}, 1);
      }
    } else if (type == "clearTips") {
      if (state.haitMsgMap[id]) {
        state.haitMsgMap[id].isHaitAll = false;
        state.haitMsgMap[id].isShowTips = false;
      }
      if (state.concernMsgMap[id]) {
        state.concernMsgMap[id].isShowTips = false;
      }
    } else {
      // 移除@我标识
      if (state.haitMsgMap[id] && state.haitMsgMap[id].isShowTips) {
        state.haitMsgMap[id].isHaitAll = false;
        state.haitMsgMap[id].isShowTips = false;
        userCache({key: "haitMsgMap", value: state.haitMsgMap}, 1);
      }
      // 移除特别关心标识
      if (state.concernMsgMap[id] && state.concernMsgMap[id].isShowTips) {
        state.concernMsgMap[id].isShowTips = false;
        userCache({key: "concernMsgMap", value: state.concernMsgMap}, 1);
      }
    }
  },
  // 判断删除@和特别关心消息标识
  setHaitOrConcernMsg: function ({state, commit, dispatch, getters}, info) {
    let {id, sessionMap, clearAll, msgMap} = info;
    // 判断移除@消息
    for (let key in msgMap) {
      // 存在id的时候只判断对应id的数据
      if (id && id != key) {
        continue;
      }
      if (msgMap[key] && state.sessions[key] && (state.currentSession.id != key || clearAll)) {
        if (!state.sessions[key].unread) {
          // 会话已读直接清除
          delete msgMap[key];
        } else {
          // 判断会话未读时间
          if (!sessionMap[key]) {
            delete msgMap[key];
          } else if (!/p2p-/.test(key)) {
            let sessionHaitFlag = false;
            for (let key1 in msgMap[key]) {
              // 删除超过已读消息的@消息
              if (msgMap[key][key1]?.time) {
                if (sessionMap[key] - msgMap[key][key1].time > 0) {
                  delete msgMap[key][key1];
                } else {
                  sessionHaitFlag = true;
                }
              }
            }
            // 当前会话不存在未读的@消息删除会话标记
            if (!sessionHaitFlag) {
              delete msgMap[key];
            }
          }
        }
      }
    }
  },
  // 批量查询云信数据库会话会话已读时间-ack
  getSessionReadTime: function ({state, commit, dispatch, getters}, info) {
    return new Promise(resolve => {
      // 打开数据库
      let openDB = indexedDB.open(`nim-${state.userInfo.workerNo}`);
      let result = {};
      openDB.onsuccess = function () {
        // 查询会话表
        try {
          let sessionDB = openDB.result.transaction("session", "readonly").objectStore("session");
          let p = [];
          for (let key in info) {
            // 异步查询
            p.push(new Promise(resolve1 => {
              let res = sessionDB.get(key);
              res.onsuccess = function () {
                // 设置对应会话已读时间
                if (res?.result?.ack) {
                  result[key] = res.result.ack;
                }
                resolve1();
              }
              res.onerror = function () {
                resolve1();
              }
            }))
          }
          // 查询结束
          Promise.all(p).then(res => {
            resolve(result);
          });
        } catch (e) {
          resolve(result);
        }
      }
      openDB.onerror = function () {
        resolve(result);
      }
    })
  },
  // 创建sse对象接收服务器消息
  setSseListener: async function ({state, commit, dispatch, getters}, info) {
    // sse最多开启6个服务
    switch (info.type) {
      case "add":
        // 新增
        if (Object.keys(state.sseMap).length >= 6) {
          // 超过6个断开最早的一个流
          let sseTime = 0;
          let removeSseId = "";
          for (let key in state.sseMap) {
            if (!sseTime || sseTime > state.sseMap[key].time) {
              sseTime = state.sseMap[key].time;
              removeSseId = key;
            }
          }
          dispatch("setSseListener", {type: "remove", id: removeSseId, noStop: true});
        }
        if (info.msgType == "ai") {
          state.sseMap[info.id] = new EventSource(config[config.env].jjsHome + "/ai-platform-api/api/sseProxy/sse/" + info.id);
        } else if (info.msgType == "xl") {
          state.sseMap[info.id] = new EventSource(config[config.env].jjsHome + "/jjsznzl/api/sseProxy/sse/" + info.id);
        }
        state.sseMap[info.id].sessionId = info.sessionId;
        state.sseMap[info.id].time = Date.now() + state.diffTime;
        state.sseMap[info.id].onmessage = (event) => {
          try {
            if (event.data) {
              // 获取和设置当前sse的会话消息
              let data = JSON.parse(event.data);
              let msg = deepClone(state.sseTempMsgMap[info.id]) || {};
              msg.scene = data.scene;
              msg.from = info.from;
              msg.to = data.to;
              msg.sessionId = info.sessionId;
              if (!msg.time) {
                msg.time = Date.now() + state.diffTime;
              }
              msg.msgType = data.msgType || "text";
              msg.type = data.msgType || "text";
              if (data.typingType == 1 && msg.type == "text") {
                // 逐字返回
                msg.text = (msg.text || "") + (data.answer || "");
              } else if (data.typingType == 2) {
                // 全文返回
                if (msg.type == "text") {
                  msg.text = data.answer || "";
                } else {
                  msg.custom = JSON.parse(data.answer || "");
                }
              }
              if (msg.type == "text") {
                msg.type = "custom";
                msg.preIndex = msg.preIndex || 0;
                msg.content = msg.content || {type: "multi", msgs: []};
              }
              msg.idServer = info.id;
              msg.sseId = info.id;
              msg.isXLTyping = true;
              msg.forbidMsg = true;
              msg.status = "success";
              msg.isSseMsg = true;
              state.sseTempMsgMap[info.id] = msg;
              // 需要有内容才渲染-防止后台一次性发送一堆卡顿
              if (msg.text) {
                throttle({
                  timerName: "sseMsgMap",
                  time: 100,
                  fnName: function () {
                    // 流被停止后不在渲染
                    if (!state.sseMsgMap[info.id]) {
                      return;
                    }
                    // 100ms延迟渲染一长串
                    let thisMsg = state.sseTempMsgMap[info.id];
                    if (msg.preIndex != undefined && thisMsg?.content) {
                      thisMsg.content.msgs.push({type: "text", text: thisMsg.text.slice(thisMsg.preIndex)});
                      thisMsg.preIndex = thisMsg.text.length;
                    }
                    if (thisMsg?.idServer) {
                      commit("setSseMsgMap", {sessionId: msg.sessionId, sseTempMsg: thisMsg});
                    }
                  }
                });
              }
            }
          } catch (e) {
            console.log("setSseListenerAddErr", e)
          }
        };
        state.sseMap[info.id].onopen = () => {
          console.log("sseOpen", info.id);
        }
        state.sseMap[info.id].onerror = () => {
          console.log("sseErr", info.id);
          dispatch("setSseListener", {type: "remove", id: info.id});
        }
        state.sseMap[info.id].onclose = () => {
          console.log("sseClose", info.id);
          dispatch("setSseListener", {type: "remove", id: info.id});
        }
        break;
      case "remove":
        // 删除
        if ((state.sseMap[info.id] || state.sseIdentMap[info.ident] == 2) && !info.noStop) {
          // 告知服务器停止流
          closeAnswerApi({sid: info.id});
        }
        try {
          // 子窗口不存在情况
          let chatChildObj = getChildWin("chat-" + state.sseMap[info.id].sessionId, "", true);
          if (state.sseMap[info.id] && (!chatChildObj.isExit || info.delete)) {
            state.sseMap[info.id].close();
            delete state.sseMap[info.id];
          }
        } catch (e) {
          delete state.sseMap[info.id];
        }
        // 删除sse消息
        if (state.sseMsgMap[info.id]) {
          commit("setSseMsgMap", {delete: true, sseTempMsg: state.sseMsgMap[info.id]});
        }
        break;
      case "clear":
        // 清空
        for (let key in state.sseMap) {
          if (state.sseMap[info.id]) {
            // 告知服务器停止流
            closeAnswerApi({sid: info.id});
          }
          dispatch("setSseListener", {type: "remove", id: key});
        }
        break;
    }
  },
  // 获取转发权限
  getForwardPur: async function ({state, commit, dispatch, getters}, info) {
    // 间隔1分钟才触发-林董和直属拥有权限不判断
    if (state.userInfo.workerId != "********" && state.userInfo.managerId != "********" && Date.now() - state.purMap.forwardTime > 60 * 1000) {
      let res = await addTagPowerApi({
        msgBody: JSON.stringify({workerId: state.userInfo.workerId}),
      });
      if (res.success) {
        state.purMap.forwardTime = Date.now();
        if (res?.data?.data) {
          state.purMap.forward = true;
        }
      }
    }
  },
  // 设置语音播放对象
  setAudioObj: async function ({state, commit, dispatch, getters}, info) {
    try {
      let audioObj = deepClone(state.audioObj);
      // 点击播放更新当前页面
      if (info.isPlay) {
        audioObj = deepClone(info);
      }
      let nextParam = {
        type: "nextAudio",
        value: {
          idServer: audioObj.idServer,
          id: audioObj.id,
          playNext: audioObj.playNext,
          msgType: audioObj.msgType
        }
      }
      // 播放结束，存在下一条自动播放的语音
      if (audioObj.playNext && info.audioEnd) {
        nextParam.value.index = audioObj.index;
      }
      let childWin = getChildWin("chat-" + audioObj.id);
      if (childWin) {
        childWin.window.store.commit("setEmit", nextParam);
      } else {
        store.commit("setEmit", nextParam);
      }
    } catch (e) {
      console.log("setAudioObjErr", e);
    }
    delete info.isPlay;
    delete info.audioEnd;
    state.audioObj = info;
  },
  // 设置订阅客户在线会话
  setFcwOnlineEvent: function ({state, commit, dispatch, getters}, info = {}) {
    throttle({
      timerName: "subscribeEvent",
      time: 1000,
      fnName: async function () {
        let fcwMap = {};
        // 获取房产网客户列表
        for (let key in state.sessions) {
          let item = state.sessions[key];
          if (new RegExp(config.fcw).test(item.to)) {
            fcwMap[item.to] = item.to;
          }
        }
        // 设置订阅指定人在线状态
        if (info.account) {
          fcwMap[info.account] = info.account;
        }
        let fcwMapList = Object.keys(fcwMap);
        if (fcwMapList.length > 0) {
          // 过滤已订阅的账号
          let searchRes = await state.nimSDK.querySubscribeEventsByAccounts({accounts: Object.keys(fcwMap)});
          if (searchRes?.obj?.msgEventSubscribes) {
            searchRes.obj.msgEventSubscribes.map(item => {
              if (item.type == 1) {
                delete fcwMap[item.to];
              }
            });
          }
          // 去除已订阅的人员数据后订阅
          if (Object.keys(fcwMap).length > 0) {
            let res = await state.nimSDK.subscribeEvent({accounts: Object.keys(fcwMap)});
            if (res.err) {
              console.log("subscribeEventErr", res.err);
            }
          }
        }
      }
    });
  },
  // 设置语音转文字
  setSpeechToText: async function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      if (state.audioTextMap[info.idServer]) {
        info?.done && info.done({
          success: true,
          resultText: state.audioTextMap[info.idServer]
        });
        resolve();
        return;
      }
      // 不存在转文字记录则请求接口
      let res = await speechToTextApi({
        "msgBody": JSON.stringify({
          url: info.url.split("?")[0] + "?audioTrans&type=mp3",
          format: "mp3"
        }),
      });
      if (res?.data?.flashResult?.length > 0) {
        let result = res.data.flashResult;
        let text = "";
        for (let i = 0; i < result.length; i++) {
          text += result[i].text;
        }
        state.audioTextMap[info.idServer] = text;
        res.resultText = text;
      }
      info?.done && info.done(res);
      resolve();
    });
  },
  // 获取是否有转发特定人权限
  getButtonObj: async function ({state, commit, dispatch, getters}, info) {
    state.serverFlagMap["getButtonObj"] = "loading";
    // 间隔1分钟才触发
    if (Date.now() - state.purMap.reportMsgTime > 60 * 1000) {
      let res = await getButtonObjApi();
      if (res?.success) {
        state.serverFlagMap["getButtonObj"] = true;
        state.purMap.reportMsgTime = Date.now();
        state.purMap.reportMsg = res?.data?.broadcasting;
        state.purMap.clearMessage = res?.data?.clearMessage;
        if (!isMainWin()) {
          remote.store.state.purMap.reportMsg = state.purMap.reportMsg;
          remote.store.state.purMap.clearMessage = state.purMap.clearMessage;
        }
      } else {
        state.serverFlagMap["getButtonObj"] = "";
      }
    }
  },
  // 获取转发至特定人群
  getForwardGroupType: async function ({state, commit, dispatch, getters}, info) {
    return new Promise(async resolve => {
      // 间隔1分钟才触发
      if (Date.now() - state.purMap.reportMsgGroupTime > 60 * 1000) {
        let res = await getForwardGroupTypeApi();
        if (res.success) {
          state.purMap.reportMsgGroupTime = Date.now();
          state.purMap.reportMsgGroup = res?.data?.groupTypeList;
          if (!isMainWin()) {
            remote.store.state.purMap.reportMsgGroup = state.purMap.reportMsg;
          }
        }
      }
    });
  },
  // 获取远程指令列表
  setCheckRemote: async function ({state, commit, dispatch, getters}, info) {
    checkRemoteApi().then(res => {
      if (res?.rules) {
        state.remoteCMDList = res.rules;
      }
    });
  },
  // 设置和获取群ai信息
  setTeamAiMap: function ({state, commit, dispatch, getters}, info) {
    // 删除群ai信息
    if (info.type == "del") {
      delete state.teamAiMap[info.id];
      return;
    }
    if (info.type == "update") {
      for (let key in info.value) {
        state.teamAiMap[info.id][key] = info.value[key]
      }
      return;
    }
    // 请求状态直接返回
    if (state.teamAiMap[info.id] instanceof Promise) {
      return state.teamAiMap[info.id];
    }
    // 半小时更新一次
    if (state.teamAiMap[info.id]?.to && Date.now() + state.diffTime - state.teamAiMap[info.id].time < 30 * 60 * 1000) {
      return new Promise(resolve => {
        resolve(deepClone(state.teamAiMap[info.id]));
      });
    }
    // 不存在请求则获取
    state.teamAiMap[info.id] = new Promise(async (resolve) => {
      let res = {};
      if (info.type == "init") {
        // 初始化获取
        res = await createTeamAiInfoApi({
          msgBody: JSON.stringify({
            tid: info.id,
            aiType: 1,
          })
        });
        if (!res?.success) {
          toast({title: res?.errorMsg || "系统错误"});
        }
      } else {
        res = await getTeamAiInfoApi({
          msgBody: JSON.stringify({
            tid: info.id,
            aiType: 1,
          })
        });
      }
      if (res?.success && res.data?.aiInfo?.accid) {
        let teamAiInfo = res.data.aiInfo;
        let teamAiLocal = getSessionType({scene: "p2p", to: teamAiInfo.accid, detailInfo: {detailType: "teamAi"}});
        let teamAi = {
          to: teamAiInfo.accid,
          datasetId: teamAiInfo.knowSignId,
          appTypeId: teamAiInfo.appId,
          avatar: teamAiLocal.avatar,
          name: teamAiLocal.name,
          prologue: teamAiInfo.describeText || teamAiLocal.prologue,
          time: Date.now() + state.diffTime,
        }
        let aiRes = await getAppAndAccGroupInfoApi({appId: teamAi.appTypeId});
        if (aiRes.success) {
          // 设置应用介绍
          teamAi.briefIntr = aiRes.data?.appPerModel?.briefIntr;
          // 设置云信信息
          let aiYxInfo = aiRes.data?.appGroupModels?.find(item => {return item.accId == teamAi.to });
          if (aiYxInfo?.accImg) {
            teamAi.avatar = aiYxInfo.accImg;
          }
          if (aiYxInfo?.accName) {
            teamAi.name = aiYxInfo.accName;
          }
        }
        // 更新群ai本地数据库
        dispatch("updatePersons", {
          account: teamAi.to, datas: [setUserBaseInfo({
            workerNo: teamAi.to,
            name: teamAi.name,
            selfIntro: teamAi.selfIntro,
            prologue: teamAi.prologue,
            headImg: teamAi.avatar,
            id: teamAi.appTypeId,
          })]
        });
        delete state.teamAiMap[info.id];
        state.teamAiMap[info.id] = teamAi;
      } else {
        // 获取不到删除
        delete state.teamAiMap[info.id];
      }
      // 当前会话判断滚动到底部
      if (state.currentSession.to == info.id) {
        commit("setEmit", {type: "scroll", value: "bottom"});
      }
      resolve(deepClone(state.teamAiMap[info.id]));
    })
    return state.teamAiMap[info.id];
  },
  // 浏览器打开会话
  browserOpenChat: async function ({state, commit, dispatch, getters}, info) {
    try {
      if (!state.userInfo.workerNo) {
        // 需登录乐聊提示
        return {success: false, errorMsg: state.errorMap["pc-im-1"], errorCode: "pc-im-1"};
      }
      // 判断能否打开会话权限
      let res = await havePermissionApi({
        msgBody: JSON.stringify({
          type: info.type,
          scene: info.scene,
          accid: info.to,
          busId: info.appId
        })
      });
      let returnRes = {success: res?.success || false};
      if (!res?.success) {
        // 默认code/系统错误提示
        returnRes.errorCode = res?.errorCode || "pc-im-3";
        returnRes.errorMsg = res?.errorMsg || state.errorMap["pc-im-3"];
      }
      if (res?.data?.havePermission === false) {
        returnRes.success = false;
        returnRes.errorMsg = "没有打开该会话权限";
        returnRes.errorCode = 995;
      }
      // 有权限打开会话聚焦
      if (res?.data?.havePermission) {
        if (info.to == state.aiObj.workerNo && info.appId) {
          let appRes = await getAppPurApi({appId: info.appId});
          if (appRes?.success && appRes.data) {
            // 获取到应用信息打开会话和切换到指定应用
            dispatch("setOpenNotify", {id: `${info.scene}-${info.to}`});
            commit("setEmit", {type: "switchAppItem", value: appRes.data});
          } else {
            returnRes.success = false;
            returnRes.errorMsg = res?.errorCode || "获取应用信息失败";
            returnRes.errorCode = res?.errorMsg || 994;
          }
        } else {
          // 非个人助理会话或个人助理不切换应用直接打开
          dispatch("setOpenNotify", {id: `${info.scene}-${info.to}`});
        }
      }
      return returnRes;
    } catch (e) {
      return {success: false, errorMsg: e.message, errorCode: "pc-im-4"};
    }
  },
  // 获取vpn权限和内容
  getProxyTool: async function ({state, commit, dispatch, getters}, info) {
    // 判断是否开启vpn
    let vpnFlag = await getVpnFlag();
    state.jjsProxy.vpnInfo.vpnFlag = vpnFlag;
    if (state.userInfo.workerNo) {
      // 登录状态重新获取最新的vpn权限
      let res = await getProxyToolApi();
      if (res?.data?.hav) {
        state.jjsProxy.vpnInfo.isShowVpn = true;
        state.jjsProxy.vpnInfo.vpnObj = {
          open: res.data["bat-open"],
          close: res.data["bat-close"]
        }
        localStorage.setItem("vpnInfo", encrypt(JSON.stringify(state.jjsProxy.vpnInfo)));
      } else {
        state.jjsProxy.vpnInfo.isShowVpn = false;
        state.jjsProxy.vpnInfo.vpnObj = {};
        localStorage.removeItem("vpnInfo");
      }
    } else if (localStorage.getItem("vpnInfo")) {
      // 存在vpn权限缓存
      let vpnInfo = state.jjsProxy.vpnInfo;
      try {
        vpnInfo = JSON.parse(decrypt(localStorage.getItem("vpnInfo")));
      } catch (e) {}
      vpnInfo.vpnFlag = vpnFlag;
      state.jjsProxy.vpnInfo = vpnInfo;
    }
  },
  // 切换vpn开关
  changeVpnFlag: async function ({state, commit, dispatch, getters}, info) {
    // 存在vpn权限
    if (state.jjsProxy.vpnInfo.isShowVpn) {
      let binaryEncoding = "binary";
      if (state.jjsProxy.vpnInfo.vpnFlag) {
        // 创建关闭vpn应用bat
        let vpnOffPath = path.join(getFileCachedPath({type: 5}), "/vpn_off.bat");
        let vpnOnStr = state.jjsProxy.vpnInfo.vpnObj.close;
        fs.writeFileSync(vpnOffPath, vpnOnStr, "utf-8");
        cp.exec(vpnOffPath, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          fs.unlinkSync(vpnOffPath);
          getVpnFlag(1, state.jjsProxy.vpnInfo.vpnFlag, toast).then(vpnFlag => {
            state.jjsProxy.vpnInfo.vpnFlag = vpnFlag;
          });
        });
      } else {
        // 创建打开vpn应用bat
        let vpnOnPath = path.join(getFileCachedPath({type: 5}), "/vpn_on.bat");
        let vpnOnStr = state.jjsProxy.vpnInfo.vpnObj.open;
        fs.writeFileSync(vpnOnPath, vpnOnStr, "utf-8");
        cp.exec(vpnOnPath, {encoding: binaryEncoding}, function (err, stdout, stderr) {
          fs.unlinkSync(vpnOnPath);
          getVpnFlag(2, state.jjsProxy.vpnInfo.vpnFlag, toast).then(vpnFlag => {
            state.jjsProxy.vpnInfo.vpnFlag = vpnFlag;
          });
        });
      }
    }
  },
  // 设置服务端分组列表
  setClassifyList: async function ({state, commit, dispatch, getters}, info) {
    // id:0全部-1客户-2稍后处理-3@我-4乐文档-5群-6讨论组分组-7群/讨论组分组-8同事-9数字人-10单聊-11标记-12我的直属-13业务讨论组分组-14我的任务
    if (state.serverFlagMap["setFindSubordinate"] != true || state.serverFlagMap["setGroupTypeList"] != true || state.serverFlagMap["setGroupSetting"] != true) {
      // 分组/直属接口请求异常不允许操作
      toast({title: "列表正在初始化中，请稍后重试", type: 2});
      return;
    }
    let list = [];
    info.list.map(item => {
      let lItem = {id: item.id, name: item.name, uuid: item.uuid};
      if (item.id == 14) {
        lItem.pc = false;
      } else if (item.id == 0) {
        return;
      }
      list.push(lItem);
    });
    let res = await dispatch("setModifySettings", {type: 3, key: config.settings.type16, value: list})
    if (res.success) {
      // 发送通知给自己和别人(用于多端同步)
      store.getters.getNim.sendCustomSysMsg({
        scene: "p2p",
        to: state.userInfo.workerNo,
        content: JSON.stringify({
          type: 'refreshSetting',
          source: 1, //pc 2 android 3ios
          key: "classify_type_new",
        }),
        done: function () {}
      });
      toast({title: "设置成功", type: 1});
      state.settings[config.settings.type16] = list;
    } else {
      toast({title: "设置失败," + (res.errorMsg || "系统错误"), type: 2});
    }
    return state.settings[config.settings.type16];
  },
}