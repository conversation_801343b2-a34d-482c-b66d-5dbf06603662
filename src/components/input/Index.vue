<template>
  <div class="input" contenteditable="true" ref="editorRef" @click="doEditor($event, 'click')"
       @dragover.prevent @paste="doEditor($event, 'paste')" @drop="doEditor($event, 'drop')" @keydown="doEditor($event, 'keydown')">
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import {selElm, debounce, loadElmTree, elmToMsg} from "@utils";
import {fileToDataURL} from "@utils/imgCompress";
import {alert, loading, toast} from "@comp/ui";

export default {
  name: "Input",
  props: {
    changeEdit: {
      type: Function
    }
  },
  setup(props, ctx) {
    const store = useStore();
    let config = store.getters.getConfig.config;
    // 编辑器元素
    let editorRef = ref();
    // 是否是插入
    let isInit = true;

    onMounted(() => {
      editorRef.value.addEventListener("click", function (e) {
        // 选择图片
        if (e.target.nodeName == "IMG") {
          selElm(e.target);
        }
      });
    });

    // type-1粘贴2拖拽3输入
    async function doEditor(e, type) {
      if (props.changeEdit && !isInit) {
        props.changeEdit(true);
      }
      let s = window.getSelection();
      switch (type) {
        case "click":
          break;
        case "paste":
          await formatEditor(e);
          break;
        case "drop":
          // 光标移动到最后插入
          let r = document.createRange();
          r.selectNodeContents(editorRef.value);
          r.collapse(false);
          s.removeAllRanges();
          s.addRange(r);

          await formatEditor(e);
          document.execCommand("paste");
          break;
        case "keydown":
          break;
      }
      isInit = false;
    }

    // 格式化插入内容
    async function formatEditor(e) {
      let clip = remote.Clipboard.get();
      let clipType = clip.get("html") ? "html" : clip.get("png") ? "png" : "text";
      let clipContent = clip.get("html") || clip.get("png") || clip.get("text");
      // 获取粘贴板内容
      let dataTransfer = e.clipboardData || e.dataTransfer;
      // 获取粘贴板文件
      let files = dataTransfer.files;
      let fileHtml = "";
      for (let i = 0; i < files.length; i++) {
        let file = files[i];
        if (config.imgTypeReg.test(file.type) && file.size < 2 * 1024 * 1024) {
          // 图片
          let fileBse64 = await fileToDataURL(file);
          fileHtml += `<img src="${fileBse64}">`;
        }
      }
      // 获取粘贴板文本
      let node = document.createElement("span");
      let html = dataTransfer.getData("text/html");
      let text = dataTransfer.getData("text/plain");
      if (files.length > 0) {
        clip.set(fileHtml, "html");
      } else if (html) {
        html = getHtmlContent(html);
        node.innerHTML = `<span>${html}</span>`;
        clipContent = html;
        let thisHtml = "";
        if (node.childNodes.length == 0) {
          thisHtml += node.innerHTML;
        } else {
          loadElmTree(1, node, (obj, elm) => {
            switch (elm.nodeName) {
              case "IMG":
                if (elm.className == "im-emoji") {
                  thisHtml += elm.outerHTML;
                } else {
                  thisHtml += `<img data-ext="${obj.ext}" data-info=${elm.dataset.info || "''"} src="${elm.src}">`;
                }
                break;
              case "BR":
                thisHtml += `<br>`;
                break;
              default:
                thisHtml += buildEmoji(elm.innerText.replace(/\n/g, "<br>"));
                break;
            }
          });
        }
        clip.set(thisHtml, "html");
      } else if (text) {
        node.innerText = text;
        clip.set(node.innerText, "text");
      }
      if (!isInit) {
        // 恢复粘贴板
        debounce({
          timerName: "clip",
          time: 500,
          fnName: function () {
            clip.set(clipContent, clipType);
          }
        });
      }
    }

    // 插入html内容
    function insertHtml(html, init) {
      if (init) {
        isInit = true;
      }
      let clip = remote.Clipboard.get();
      let clipType = clip.get("html") ? "html" : clip.get("png") ? "png" : "text";
      let clipContent = clip.get("html") || clip.get("png") || clip.get("text");
      clip.set(html, "html");
      editorRef.value.focus();
      document.execCommand("paste");
      if (clipType == "html") {
        clipContent = getHtmlContent(clipContent);
      }
      clip.set(clipContent, clipType);
    }

    // 获取输入框html对象
    async function getHtmlObj() {
      return new Promise(async resolve => {
        loading();
        let res = await elmToMsg(3, editorRef.value);
        loading().hide();
        resolve(res);
      });
    }

    // 获取html内容
    function getHtmlContent(html) {
      let startRes = html.match(/<!--\s*StartFragment\s*-->/);
      let endRes = html.match(/<!--\s*EndFragment\s*-->/);
      if (startRes && endRes) {
        let startStr = startRes[0];
        let endStr = endRes[0];
        return `${html.slice(html.indexOf(startStr) + startStr.length, html.lastIndexOf(endStr))}`;
      } else {
        return html;
      }
    }

    return {
      editorRef,

      doEditor,
      insertHtml,
      getHtmlObj,
    }
  }
}
</script>
<style scoped lang="scss">
.input {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background: #FFFFFF;
  font-size: 13px;
  line-height: 1.4;

  &:focus {
    border: none;
    outline: none;
  }

  ::selection {
    background: #b3d4fc;
    text-shadow: none;
  }

  ::v-deep(img) {
    max-height: 70px;
    max-width: 97%;
    vertical-align: text-bottom;
  }
}
</style>