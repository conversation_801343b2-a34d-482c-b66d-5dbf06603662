<template>
  <div class="collect-modal win-drag" v-show="collectInputObj.isShow" @mousedown="showComponents({type:2})">
    <div class="collect-input-box win-no-drag win-no-resize" @mousedown.stop="">
      <!--顶部标题-->
      <div class="collect-header">
        <span class="intr">{{ sessionInfo.to ? "编辑后将直接发送给：" : (editType == 0 ? "编辑话术" : editType == 1 ? "编辑我的话术" : detailType == 1 ? "创建笔记" : "编辑收藏内容") }}</span>
        <span class="title" v-if="sessionInfo.name||(sessionInfo.detailInfo&&sessionInfo.detailInfo.name)">乐有家网客户 | {{ sessionInfo.name || sessionInfo.detailInfo.name }}</span>
        <img class="close" src="/img/close.png" alt="" @click="showComponents({type:2})">
      </div>
      <div class="collect-content">
        <!--功能栏-->
        <ul class="tool-box">
          <li class="icon-editor pic" title="选择图片" @click="selFile(1)"></li>
          <li class="icon-editor emoji" title="选择表情" @click.stop="toggleEmoji"></li>
        </ul>
        <!--编辑弹窗-->
        <div class="input-box">
          <InputComps ref="inputCompsRef" :changeEdit="changeEdit"></InputComps>
        </div>
      </div>
      <div class="collect-footer">
        <div v-if="editType==0" class="sel-box" @click="collectInputObj.isPublicSel=!collectInputObj.isPublicSel">
          <i :class="['sel-box-i', collectInputObj.isPublicSel?'sel':'']"></i>
          <span>同时保存到我的话术-默认分组</span>
        </div>
        <div v-else></div>
        <div class="btn-box">
          <span class="btn" @mousedown="showComponents({type:2})">取消</span>
          <span v-show="editType!=0" class="btn" @click="forwardItem(1)">保存并发送</span>
          <span v-show="editType!=0" class="btn" @click="saveItem">保存</span>
          <span class="btn" @click="forwardItem()">发送</span>
        </div>
      </div>
      <!--表情弹窗-->
      <Emoji :isShow="collectInputObj.emojiShow" :top="collectInputObj.emojiTop" :left="collectInputObj.emojiLeft" :selEmoji="selEmoji" type="1"></Emoji>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import LyDialog from "@comp/ui/comps/LyDialog";
import Emoji from "@comp/ui/comps/Emoji";
import InputComps from "@comp/input/Index";
import {useStore} from "vuex";
import {getLocalFile, msgToHtml, openForward, forwardCollectMsgs, htmlEscapeAll, getChildWin, elmToMsg, setJJSEvent} from "@utils";
import {fileToDataURL} from "@utils/imgCompress";
import {alert, loading, toast} from "@comp/ui";
import {editCollectContentApi} from "@utils/net/api.js";

export default {
  name: "Collect",
  components: {LyDialog, InputComps, Emoji},
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    showComponents: {
      type: Function
    },
    // 发送会话信息
    sessionInfo: {
      type: Object,
      default: {}
    },
    editItem: {
      type: Object,
      default: {}
    },
    changeShow: {
      type: Function
    },
    // 0公共-1我的-2收藏
    editType: {
      type: String,
    },
    // 1新增笔记
    detailType: {
      type: String
    }
  },
  setup(props, ctx) {
    let path = remote.require("path");
    const store = useStore();
    let config = store.getters.getConfig.config;
    let inputCompsRef = ref();
    let collectInputObj = ref({
      isShow: false,// 是否显示编辑弹窗
      isEdit: false,// 编辑内容是否有变动
      emojiShow: false,// 是否显示表情弹窗
      emojiTop: 0,// 表情位置top
      emojiLeft: 0,// 表情位置left
      isPublicSel: false,// 公共话术是否选择保存到我的话术
    });

    // 选择文件发送type-1图片-2文件
    async function selFile(type) {
      store.commit("setEmit", {
        type: "fileInput", value: {
          multiple: type == 1 ? false : true,
          accept: type == 1 ? "image/gif,image/jpeg,image/png,image/jpg,image/bmp" : "*",
          done: files => {
            let p = [];
            for (let i = 0; i < files.length; i++) {
              let item = files[i].path;
              let param = {
                path: path.dirname(item) + "\\",
                name: path.basename(item)
              };
              p.push(getLocalFile(param));
            }
            Promise.all(p).then(files => {
              if (type == 1) {
                // 发送图片
                let file = files[0];
                if (!config.imgTypeReg.test(file.name)) {
                  toast({title: "请选择图片", type: 2});
                } else if (file.size == 0) {
                  toast({title: "不能发送空文件", type: 2});
                  return;
                } else if (file.size / 1024 / 1024 > 2) {
                  toast({title: "图片大小不能大于2M", type: 2});
                  return;
                } else {
                  // 图片
                  fileToDataURL(file).then((data) => {
                    inputCompsRef.value.insertHtml(`<img src="${data}">`);
                  });
                }
              } else if (type == 2) {
                // 文件
              }
            }).catch(err => {
              toast({title: "文件加载错误请重试" + err, type: 2})
            })
          }
        }
      });
    }

    // 切换显示表情
    function toggleEmoji(e) {
      collectInputObj.value.emojiShow = !collectInputObj.value.emojiShow;
      collectInputObj.value.emojiTop = e.target.offsetTop + e.target.clientWidth + 5;
      collectInputObj.value.emojiLeft = e.target.offsetLeft + e.target.clientHeight + 5;
    }

    // 监听全局点击
    watch(() => store.state.emit.click,
      (newValue, oldValue) => {
        if (newValue && newValue.target) {
          if (collectInputObj.value.emojiShow) {
            toggleEmoji(newValue);
          }
        }
      }, {
        deep: true
      }
    );

    // 监听显示编辑框
    watch(() => props.isShow,
      (newValue, oldValue) => {
        if (newValue) {
          // 当前不是显示状态才重新赋值
          if (!collectInputObj.value.isShow) {
            nextTick(async () => {
              collectInputObj.value.isPublicSel = false;
              collectInputObj.value.isEdit = false;
              inputCompsRef.value.editorRef.innerText = "";
              loading();
              let res = await forwardCollectMsgs([props.editItem], 1);
              // 判断是否天翼云文件是否需要上传
              let node = document.createElement("div");
              node.innerHTML = msgToHtml(res.thisMsg, 1);
              let thisRes = await elmToMsg(3, node);
              loading().hide();
              inputCompsRef.value.insertHtml(thisRes.html, true);
            });
          }
          collectInputObj.value.isShow = true;
        } else {
          // 公共话术无需保存
          if (collectInputObj.value.isEdit && props.changeShow && props.editType != 0) {
            alert({
              content: `当前内容未保存,是否不保存离开?`,
              done: type => {
                if (type == 1) {
                  collectInputObj.value.isShow = false;
                } else {
                  props.changeShow(true);
                }
              }
            });
          } else {
            collectInputObj.value.isShow = false;
          }
        }
      }, {
        deep: true
      }
    );

    // 选择表情
    function selEmoji(item, emojiKey) {
      inputCompsRef.value.insertHtml(`<img class="im-emoji" data-text="${item.key}" alt="${item.key}" src="/img/emoji/${emojiKey}/${item.value}${item.ext}" width="24" height="24">`);
      collectInputObj.value.emojiShow = false;
    }

    // 改变编辑状态
    function changeEdit(flag) {
      collectInputObj.value.isEdit = flag;
    }

    // 保存内容
    function saveItem() {
      let editType = collectInputObj.value.isPublicSel ? "1" : props.editType;
      return new Promise(async resolve => {
        let res = await inputCompsRef.value.getHtmlObj();
        if (!res.success) {
          toast({title: res.errorMsg, type: 2});
          return;
        }
        if (!canSaveMsg(res.msgs)) {
          toast({title: "字数不能多于4000字", type: 2});
          return;
        }
        loading();
        let editRes = {};
        let saveItem = {};
        let collectParam = {};
        if (editType == 1) {
          // 我的话术
          if (collectInputObj.value.isPublicSel) {
            editRes = await store.dispatch("setCollectStoryContent", {id: "", parentId: "0", content: res.msgs});
          } else {
            editRes = await store.dispatch("setCollectStoryContent", {id: props.editItem.collectId, parentId: props.editItem.parentId, content: res.msgs});
          }
        } else {
          // 我的收藏
          let userInfo = store.getters.getUserInfo;
          let text = store.getters.getPrimaryMsg({msg: {from: userInfo.workerNo, type: "custom", content: {type: "multi", msgs: res.msgs}}, primaryType: 4, notChange: true}) || "";
          collectParam = {
            groupId: props.editItem.parentId || 0,
            id: props.editItem.collectId || "",
            type: props.editItem.collectType || "note",
            empNo: userInfo.workerNo,
            sourceType: props.editItem.sourceType || 3,
            sourceName: props.editItem.sourceName || userInfo.name,
            sourceIcon: props.editItem.sourceIcon || userInfo.headPic,
            sourceId: props.editItem.sourceId || userInfo.workerNo,
            sourceClientType: "pc",
            title: text.slice(0, 50),
            text: text,
            html: res.html,
          };
          if (collectParam.type != "note") {
            if (!props.editItem.idServer) {
              // 不存在消息id改为草稿
              collectParam.type = "note";
            } else if (res.msgs.length == 1 && res.msgs[0].type == "text") {
              collectParam.type = "text";
            } else {
              collectParam.type = "custom";
            }
          }
          if (collectParam.type != "note") {
            collectParam.msgScene = props.editItem.scene == 'superTeam' ? 'super_team' : props.editItem.scene;
            collectParam.msgTo = props.editItem.to;
            collectParam.msgTime = props.editItem.time;
            collectParam.msgIdClient = props.editItem.idServer;
            collectParam.msgIdServer = props.editItem.idServer;
          } else {
            collectParam.html = "";
            res.detailMsgs.map(item => {
              if (item.type == "image") {
                collectParam.html += item.html;
              } else {
                collectParam.html += htmlEscapeAll(item.html);
              }
            });
          }
          if (collectParam.type == "custom") {
            let thisMsg = {type: "multi", msgs: res.msgs};
            collectParam.custom = JSON.stringify(thisMsg);
            collectParam.content = JSON.stringify(thisMsg)
          }
          if (collectParam.type == "note" && !collectParam.html) {
            toast({title: "请输入笔记内容", type: 2});
            loading().hide();
            return;
          }
          editRes = await editCollectContentApi({
            msgBody: JSON.stringify(collectParam)
          });
        }
        loading().hide();
        resolve(editRes);
        if (!editRes.success) {
          toast({title: editRes.errorMsg || "系统错误", type: 2});
          return;
        } else {
          toast({title: "保存成功", type: 1});
        }
        if (editType == 1) {
          saveItem = {
            id: props.editItem.collectId,
            parentId: props.editItem.parentId,
            speechContent: res.msgs
          }
        } else {
          saveItem = editRes.data.data;
          // 收藏后返回字段不准确
          if (saveItem.type == "custom") {
            saveItem.oText = collectParam.text;
            saveItem.oHtml = collectParam.html;
            saveItem.custom = collectParam.custom;
            saveItem.content = collectParam.content;
          }
          if (!saveItem.updateTime) {
            saveItem.updateTime = Date.now() + store.getters.getDiffTime;
          }
        }
        collectInputObj.value.isEdit = false;
        props.changeShow(false);
        store.commit("setEmit", {type: "saveCollectItem", value: saveItem});
      })
    }

    // 转发消息-type1保存并转发
    async function forwardItem(type) {
      if (type == 1 || collectInputObj.value.isPublicSel) {
        // 保存并转发
        let saveRes = await saveItem();
        if (!saveRes.success) {
          return;
        }
      }
      let res = await inputCompsRef.value.getHtmlObj();
      if (!res.success) {
        toast({title: res.errorMsg, type: 2});
        return;
      }
      let thisMsg = {};
      let thisMsgs = res.msgs;
      if (!canSaveMsg(thisMsgs)) {
        toast({title: "字数不能多于4000字", type: 2});
        return;
      }
      if (thisMsgs.length == 1 && thisMsgs[0].type != "document") {
        let thisMsgItem = thisMsgs[0];
        thisMsg.type = thisMsgItem.type;
        if (thisMsgItem.type == "text") {
          thisMsg.text = thisMsgItem.text;
        } else if (thisMsgItem.type == "image") {
          thisMsg.file = thisMsgItem.file;
        }
      } else {
        thisMsg.type = "custom";
        thisMsg.content = JSON.stringify({type: "multi", msgs: thisMsgs});
      }
      if (props.sessionInfo.to) {
        // 直接发送给用户
        loading();
        remote.store.getters.getNim.forwardMsg({scene: props.sessionInfo.scene, to: props.sessionInfo.to, msg: thisMsg}).then(res => {
          remote.store.dispatch("sendMsgDone", res);
          store.commit("setEmit", {type: "showComponents", value: {type: 1}});
          nextTick(() => {
            store.commit("setEmit", {type: "showComponents", value: {type: 2}});
          });
          if (props.sessionInfo.close) {
            // 关闭搜索弹窗
            if (getChildWin("collect")) {
              getChildWin("collect").close();
              remote.store.dispatch("setChildWin", {type: "del", name: "collect"});
            }
          }
          loading().hide();
        });
      } else {
        openForward(thisMsg);
      }
      if (props.editType == 0) {
        setJJSEvent("P54415360", JSON.stringify({
          aid: props.editItem.collectId,
          type: props.editItem.parentId
        }));
      }
    }

    // 字数不能大于4000字
    function canSaveMsg(msg) {
      let flag = true;
      if (msg.length == 1) {
        if (msg[0].type == "text" && msg[0].text.length > 4000) {
          flag = false;
        }
      } else {
        if (JSON.stringify(msg).length > 4000) {
          flag = false;
        }
      }
      return flag;
    }

    return {
      collectInputObj,
      inputCompsRef,

      selFile,
      toggleEmoji,
      selEmoji,
      changeEdit,
      saveItem,
      forwardItem,
    }
  }
}
</script>
<style scoped lang="scss">
.collect-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 90;

  .collect-input-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 500px;
    height: 400px;
    background: #FFFFFF;
    box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.2);
    border: 1px solid #D8D8D8;
    border-radius: 6px;

    .collect-header {
      position: relative;
      display: flex;
      align-items: center;
      height: 30px;
      padding: 0 16px;
      background: #F0F0F0;
      font-weight: 700;
      border-radius: 6px 6px 0px 0px;

      .intr {
        color: #333333;
        margin-right: 5px;
      }

      .close {
        position: absolute;
        top: 50%;
        right: 12px;
        transform: translateY(-50%);
        width: 20px;
        cursor: pointer;
        padding: 4px;
      }
    }

    .collect-content {
      .tool-box {
        display: flex;
        align-items: center;
        height: 30px;
        padding: 0 16px;
        background: #FAFAFA;
        border-bottom: 1px solid #E2E2E2;

        .icon-editor {
          width: 20px;
          height: 20px;
          margin-right: 12px;
          position: relative;
          cursor: pointer;
          background-image: url("/img/index/icon_editor.png");
          background-repeat: no-repeat;
          background-size: 360px 20px;

          &.emoji {
            background-position: 0 0;

            &:hover {
              background-position: -20px 0;
            }
          }

          &.pic {
            background-position: -40px 0;

            &:hover {
              background-position: -60px 0;
            }
          }
        }
      }

      .input-box {
        height: 300px;
        padding: 8px 16px;
      }
    }

    .collect-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 38px;
      padding: 0 16px;
      border-radius: 0px 0px 6px 6px;

      .sel-box {
        display: flex;
        align-items: center;
        cursor: pointer;

        .sel-box-i {
          flex-shrink: 0;
          width: 14px;
          height: 14px;
          position: relative;
          border: 1px solid #DDDDDD;
          margin-right: 6px;
          border-radius: 2px;

          &.sel {
            border: 1px solid $styleColor;
            background: $styleColor;

            &:after {
              content: "";
              width: 8px;
              height: 3px;
              border: 2px solid #FFFFFF;
              border-top: transparent;
              border-right: transparent;
              position: absolute;
              top: 2px;
              left: 1px;
              transform: rotate(-45deg);
            }
          }
        }
      }

      .btn-box {
        display: flex;
        align-items: center;

        .btn {
          margin-left: 8px;
          padding: 3px 12px;
          line-height: 16px;
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          cursor: pointer;

          &:hover {
            border: 1px solid $styleColor;
            color: $styleColor;
          }
        }
      }
    }
  }
}
</style>