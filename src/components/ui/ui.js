import {createVNode, render} from "vue";
import AlertConstructor from "./comps/Alert.vue";
import LoadingConstructor from "./comps/Loading.vue";
import ToastConstructor from "./comps/Toast.vue";
import TextmenuConstructor from "./comps/Textmenu.vue";

export const $alert = function (options) {
  return createDom(AlertConstructor, options);
};

export const $loading = function (options) {
  return createDom(LoadingConstructor, options);
};

export const $toast = function (options) {
  return createDom(ToastConstructor, options);
};

export const $textmenu = function (options) {
  return createDom(TextmenuConstructor, options);
};

// 创建dom元素
function createDom(constructor, options) {
  let oldContainer = document.getElementsByClassName(`__default__container__comps__${constructor.name}`)[0];
  // 创建div
  let container = document.createElement("div");
  container.className = `__default__container__comps__${constructor.name}`;
  //创建虚拟节点
  const vm = createVNode(constructor, options);
  //渲染虚拟节点
  render(vm, container);
  // 存在历史元素替换
  if (oldContainer) {
    // 退出登录状态不覆盖弹窗
    if (remote.logoutFlag && constructor.name == "Alert") {
      return;
    }
    oldContainer.parentNode.replaceChild(container, oldContainer);
  } else {
    document.body.appendChild(container);
  }
  return vm.component.proxy;
}