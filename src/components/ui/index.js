/**
 * title-标题 content-内容 showClose-是否显示关闭 done-点击回调type1-确定-2取消-3关闭 opacity-背景框透明度
 * showOk-是否显示确定 okText-确定文案 okTextColor-确定颜色
 * showCancel-是否显示取消 cancelText-取消文案  cancelTextColor-取消颜色
 * 创建alert后会返回obj，可调用changeContent改变弹窗内容 hide主动关闭弹窗
 */
export function alert(params) {
  return app.__vue_app__.config.globalProperties.$alert({...params})
}

// title-标题 time-时长 done回调
export function loading(params) {
  return app.__vue_app__.config.globalProperties.$loading({...params})
}

// title-标题 time-时长 type-类型1成功2失败其他默认 done回调
export function toast(params) {
  return app.__vue_app__.config.globalProperties.$toast({...params})
}

// x、y坐标、win当前窗口
export function textmenu(params) {
  return app.__vue_app__.config.globalProperties.$textmenu({...params})
}