<template>
  <div class="default-alert win-drag win-no-resize" v-if="isShow" :style="{background: 'rgba(0,0,0,'+opacity+')',zIndex: zIndex}">
    <div :class="['default-alert-content', 'win-no-resize',noDrag?'win-no-drag':'win-drag']">
      <div class="default-alert-title">
        <span>{{ title }}</span>
        <img src="/img/close.png" alt="" v-if="showClose" @click.prevent.stop="handleDone(3)">
      </div>
      <div class="default-alert-box win-no-drag">
        <div class="default-alert-value-box" :style="contentStyle">
          <div class="default-alert-value selAll" v-html="showContent" ref="alertContentRef" @click="triggerClick"></div>
        </div>
        <div class="default-alert-btns">
          <div class="default-alert-cancel default-alert-btn" v-if="showCancel" :style="setCancelColor()"
               @click.prevent.stop="handleDone(2)">
            {{ cancelText }}
          </div>
          <div class="default-alert-submit default-alert-btn" v-if="showOk" :style="setOkColor()"
               @click.prevent.stop="handleDone(1)">
            {{ okText }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {defineComponent, nextTick, onMounted, ref, toRefs} from "vue";

export default defineComponent({
  name: "Alert",
  props: {
    // 标题
    title: {
      type: String,
      default: "提示",
    },
    // 内容
    content: {
      type: String,
      default: "",
      required: true,
    },
    // 是否显示关闭
    showClose: {
      type: Boolean,
      default: true,
    },
    // 是否显示取消
    showCancel: {
      type: Boolean,
      default: true,
    },
    // 是否显示确认
    showOk: {
      type: Boolean,
      default: true,
    },
    // 确定按钮文字
    okText: {
      type: String,
      default: "确定",
    },
    // 确定按钮文字颜色
    okTextColor: {
      type: String,
      default: "",
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: "取消",
    },
    // 取消按钮文字颜色
    cancelTextColor: {
      type: String,
      default: "",
    },
    // 背景框透明度
    opacity: {
      type: Number,
      default: 0.5,
    },
    // 回调 1确定 2取消 3关闭
    done: {
      type: Function,
    },
    // 元素加载完成回调
    on: {
      type: Function
    },
    // 是否点击确认取消不关闭弹窗
    isClose: {
      type: Boolean,
      default: true
    },
    // 内容样式
    contentStyle: {
      type: String,
    },
    noDrag: {
      type: Boolean,
      default: true
    },
    // v-html点击委托事件
    triggerClick: {
      type: Function
    },
    // 弹窗层级
    zIndex: {
      type: Number,
      default: 100,
    }
  },
  setup(props) {
    // 是否显示当前组件
    const isShow = ref(true);
    // 弹窗内容
    const {content} = toRefs(props);
    const showContent = ref("");
    showContent.value = content.value;
    // 弹窗内容元素
    let alertContentRef = ref();
    onMounted(() => {
      nextTick(() => {
        props.on && props.on(alertContentRef.value);
      });
    });

    // ok的颜色
    const setOkColor = () => {
      return `color: ${props.okTextColor}`;
    };
    // 取消的颜色
    const setCancelColor = () => {
      return `color: ${props.cancelTextColor}`;
    };

    // 移除当前组件
    function hide() {
      isShow.value = false;
    }

    // 操作回调
    const handleDone = (type) => {
      if (props.isClose) {
        hide();
      }
      props.done && props.done(type);
    };

    // 改变alert内容
    const changeContent = (content) => {
      showContent.value = content;
    };

    return {
      isShow,
      showContent,
      alertContentRef,
      setOkColor,
      setCancelColor,

      hide,
      changeContent,
      handleDone
    };
  },
});
</script>
<style scoped lang="scss">
.default-alert {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 100;

  .default-alert-content {
    width: 340px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #FFFFFF;
    border-radius: 4px;
    overflow: hidden;

    .default-alert-title {
      padding: 6px 12px 6px 16px;
      font-size: 12px;
      line-height: 17px;
      color: #000000;
      font-weight: bold;
      background: #F0F0F0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        width: 20px;
        cursor: pointer;
        padding: 4px;
      }
    }

    .default-alert-box {
      padding: 16px 0;

      .default-alert-value-box {
        max-height: 180px;
        overflow-y: auto;
        word-break: break-all;
        font-size: 14px;
        color: #2A2A2A;
        text-align: center;
        margin-bottom: 16px;
        padding: 0 16px;
      }

      .default-alert-value {
        display: flex;
        justify-content: center;
        align-items: center;
        color: #2A2A2A;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
      }

      .default-alert-btns {
        display: flex;
        justify-content: center;
        align-items: center;

        .default-alert-btn {
          padding: 7px 16px 6px 16px;
          border-radius: 4px;
          overflow: hidden;
          line-height: 17px;
          font-size: 12px;
          border: 1px solid #E0E0E0;
          cursor: pointer;

          & + .default-alert-btn {
            margin-left: 16px;
          }

          &.default-alert-cancel {
            background: #FFFFFF;
            color: #000000;
          }

          &.default-alert-submit {
            background: $styleColor;
            color: #FFFFFF;
            border: 1px solid $styleColor;
          }
        }
      }
    }
  }
}
</style>