<template>
  <div :class="['dialog-content','win-drag','win-no-resize',newClass]" :style="{'z-index': t_zIndex,}" v-if="visible" @click="clickModal">
    <div class="ly-dialog win-no-drag" :style="{width: `${dialogWidth}`,'overflow':overflowHidden?'hidden':'unset'}">
      <header>
        <span v-html="title"></span>
        <img class="close" src="/img/close.png" alt="" @click="closeAll">
      </header>
      <div class="content">
        <slot></slot>
      </div>
      <footer v-if="foot">
        <div class="toast" v-if="footerToast">{{ footerToast }}</div>
        <div class="center">
          <button @click="$emit('close','buttom')" class="cancel-btn btn">{{ cancelText }}</button>
          <button @click="$emit('confirm')" class="confirm-btn btn">{{ okText }}</button>
        </div>
      </footer>
    </div>
  </div>
</template>
<script>
import {computed, ref, watch} from "vue";

export default {
  props: {
    newClass: {
      type: String,
      default: ""
    },
    zIndex: {
      type: Number,
      default: 80,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    // 超出视图范围隐藏布局
    overflowHidden:{
      type: Boolean,
      default: true,
    },
    width: {
      type: [Number, String],
      default: '50%',
    },
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "default",
    },
    foot: {
      type: Boolean,
      default: true,
    },
    footerToast: {
      type: String,
      default: ""
    },
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },
    btnDanger: {
      type: Boolean,
      default: false,
    },
    // 取消按钮文字
    cancelText: {
      type: String,
      default: "取消",
    },
    // 确定按钮文字
    okText: {
      type: String,
      default: "确定",
    },
  },
  emits: ["close", "closeAll", "confirm"],
  setup(props, {emit}) {
    let t_zIndex = ref(80);
    const dialogWidth = computed(() => {
      let width = props.width
      if (typeof props.width == 'number') width += 'px'
      return width
    })

    //获取当前的index
    function getIndex() {
      let dom_list = document.getElementsByClassName("default-dialog");
      if (props.zIndex !== 80) return props.zIndex;
      let maximum_index = 80;
      for (var index = 0; index < dom_list.length; index++) {
        let dom = dom_list[index];
        let z_index = parseInt(dom.style.zIndex);
        if (z_index > maximum_index) maximum_index = z_index;
      }
      return maximum_index + 1;
    }

    function clickModal(e) {
      if (!props.closeOnClickModal) return;
      if (e.target.className === "default-dialog") emit("close",'top');
    }

    function closeAll() {
      emit("closeAll");
      emit("close");
    }

    //监听关闭和开启
    watch(
      () => props.visible,
      (newValue, oldValue) => {
        if (newValue) {
          t_zIndex.value = getIndex();
        } else {
        }
      }
    );
    return {
      clickModal,
      closeAll,
      dialogWidth,
      t_zIndex,
    };
  },
};
</script>
<style lang="scss" scoped>
.dialog-content {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);

  .ly-dialog {
    border-radius: 4px;
    overflow: hidden;
    position: absolute;
    background: white;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;

    header {
      flex-shrink: 0;
      box-sizing: content-box;
      padding: 7px 16px;
      background: rgba(240, 240, 240, 1);
      color: rgba(0, 0, 0, 1);
      font-weight: bold;
      line-height: 17px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 4px 4px 0 0;

      .close {
        width: 20px;
        cursor: pointer;
        padding: 4px;
      }
    }

    .content {
      flex: 1;
      padding: 24px 16px;
    }

    .center {
      text-align: center;
    }

    footer {
      padding-bottom: 16px;
      position: relative;

      .toast {
        padding-left: 15px;
        margin-bottom: 10px;
      }

      .btn {
        padding: 7px 16px 6px 16px;
        border-radius: 4px;
        overflow: hidden;
        line-height: 17px;
        font-size: 12px;
        border: 1px solid #E0E0E0;
        cursor: pointer;

        & + .btn {
          margin-left: 16px;
        }
      }

      .cancel-btn {
        background: #FFFFFF;
        color: #000000;
      }

      .confirm-btn {
        background: $styleColor;
        color: #FFFFFF;
        border: none;
      }
    }
  }

}
</style>
