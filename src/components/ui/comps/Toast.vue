<template>
  <div class="default-toast-modal" v-if="isShow&&modal"></div>
  <div class="default-toast" v-if="isShow" :style="{zIndex: zIndex,background: 'rgba(69, 72, 74,'+opacity+')'}">
    <img class="default-toast-img" v-if="type==1||type==2" :src="'/img/icon_'+(type==1?'success':'error')+'.png'"
         alt="">
    <span class="default-toast-title" v-html="strToHtml((title||'').replace('socket状态不对','网络异常，请检查您的网络状态'))"></span>
  </div>
</template>
<script>
import {defineComponent, ref} from "vue";
import {strToHtml} from "@utils";

export default defineComponent({
  name: "Toast",
  props: {
    // 标题
    title: {
      type: String,
    },
    // 类型
    type: {
      type: Number,
    },
    // 背景框透明度
    opacity: {
      type: Number,
      default: 0.95,
    },
    // toast时长
    time: {
      type: Number,
      default: 3 * 1000
    },
    // 回调,loading结束
    done: {
      type: Function,
    },
    // 层级
    zIndex: {
      type: Number,
      default: 400
    },
    // 是否遮挡用户操作
    modal: {
      type: Boolean,
      default: false,
    }
  },
  setup(props) {
    // 是否显示当前组件
    const isShow = ref(true);

    // toast定时关闭
    setTimeout(() => {
      handleDone();
    }, props.time)

    // 移除当前组件
    function hide() {
      isShow.value = false;
    }

    // 操作回调
    const handleDone = () => {
      hide();
      props.done && props.done();
    };

    return {
      isShow,

      hide,
      handleDone,
      strToHtml
    };
  },
});
</script>
<style scoped lang="scss">
.default-toast-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.default-toast {
  max-width: 80%;
  max-height: 80%;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  padding: 7px 20px;
  display: flex;
  align-items: center;
  word-break: break-all;
  z-index: 200;
  color: #FFFFFF;
  font-size: 14px;
  line-height: 22px;
  border-radius: 4px;

  .default-toast-img {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .default-toast-title {
    font-size: 14px;
    line-height: 25px;
  }
}
</style>