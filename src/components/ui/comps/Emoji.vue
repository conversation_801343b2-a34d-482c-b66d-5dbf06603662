<template>
  <div class="emoji-sel-box" ref="emojiBoxRef" v-show="isShow" :style="(bottom!=undefined?'bottom:'+bottom+'px;':'top:'+top+'px;')+'left:'+left+'px'">
    <div class="emoji-sel-content" v-if="isShow">
      <div class="emoji-often-box" v-if="emojiKey=='jjs_emoji'">
        <div class="emoji-title">最近使用</div>
        <ul class="emoji-ul-box">
          <li class="emoji-li" :class="'emoji-'+emojiKey" v-for="(item,key) in oftenEmojiList" :key="item.value" :title="item.key"
              @click.stop="selEmojiFun(item)">
            <img :src="'/img/emoji/'+emojiKey+'/'+item.value+item.ext" alt="">
          </li>
        </ul>
        <div class="emoji-title">所有表情</div>
      </div>
      <ul class="emoji-ul-box">
        <li class="emoji-li" :class="'emoji-'+emojiKey" v-for="(item,key) in emojiList[emojiKey]" :key="item.value" :title="item.key"
            @click.stop="selEmojiFun(item)">
          <img :src="'/img/emoji/'+emojiKey+'/'+item.value+item.ext" alt="">
        </li>
      </ul>
      <ul class="emoji-tab-ul">
        <li class="emoji-tab-li" :class="emojiKey==key?'curr':''" v-for="(item,key) in emojiList" :key="key" @click.stop="selEmojiKey(key)">
          <img :class="'emoji-'+key" :src="'/img/emoji/'+key+'.png'" alt="">
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";

export default {
  name: "Template",
  props: {
    type: {
      type: String
    },
    isShow: {
      type: Boolean,
      default: false,
    },
    top: {
      type: Number,
    },
    bottom: {
      type: Number,
    },
    left: {
      type: Number,
      default: 0,
    },
    // 选择表情完成回调
    selEmoji: {
      type: Function
    },
    // 加载完成回调
    showEmojiDone: {
      type: Function
    }
  },
  setup(props, ctx) {
    const store = useStore();
    let emojiBoxRef = ref();
    // 表情列表
    let emojiList = ref(getEmojiList(props.type));
    // 常用表情列表
    let oftenEmojiList = ref([]);
    // 选中的表情
    let emojiKey = ref("jjs_emoji");

    // 监听显示表情
    watch(() => props.isShow,
      (newValue, oldValue) => {
        if (newValue) {
          showEmoji()
        }
      }, {
        deep: true
      }
    );

    // 显示表情
    function showEmoji() {
      if (oftenEmojiList.value.length == 0 && props.showEmojiDone) {
        nextTick(() => {
          props.showEmojiDone();
        });
      }
      // 获取常用表情
      store.dispatch("getOftenEmoji").then(list => {
        for (let i = 0; i < list.length; i++) {
          let item = list[i];
          item.key = `[${item.emojiName}]`;
          let itemName = getEmojiName(item.key);
          if (!itemName) {
            list.splice(i, 1);
            i--;
            continue;
          }
          item.value = getEmojiName(item.key).split(".")[0];
          item.ext = ".png";
        }
        oftenEmojiList.value = list;
        selEmojiKey("jjs_emoji");
        if (props.showEmojiDone) {
          props.showEmojiDone();
        }
      });
    }

    // 切换选择表情tab
    function selEmojiKey(key) {
      emojiKey.value = key;
    }

    // 选择表情发送/插入输入框
    function selEmojiFun(item) {
      if (props.selEmoji) {
        props.selEmoji(item, emojiKey.value);
      }
    }

    return {
      emojiKey,
      emojiList,
      oftenEmojiList,
      emojiBoxRef,

      showEmoji,
      selEmojiKey,
      selEmojiFun,
    }
  }
}
</script>
<style scoped lang="scss">
.emoji-sel-box {
  position: absolute;
  background: #FFFFFF;
  width: 468px;
  border: 1px solid #ccc;
  box-shadow: 0 0 15px #A8A79F;
  z-index: 1;
  border-radius: 4px;
  overflow: hidden;

  .emoji-often-box {
    margin: 18px 5px -18px 18px;

    .emoji-ul-box {
      height: 36px;
      margin: 0;
    }
  }

  .emoji-ul-box {
    margin: 18px 5px 18px 18px;
    height: 220px;
    overflow-y: scroll;
    overflow-x: hidden;
    display: flex;
    flex-wrap: wrap;

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
    }

    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: #dddddd;
      }
    }

    .emoji-li {
      width: 36px;
      height: 36px;
      padding: 6px;

      &.emoji-le, &.emoji-jjs, &.emoji-ajmd, &.emoji-xxy, &.emoji-lt, &.emoji-xl {
        width: 87px;
        height: 87px;
      }

      &:hover {
        background: #EEEEEE;
      }

      img {
        width: 100%;
      }
    }
  }

  .emoji-tab-ul {
    display: flex;
    background: #F5F5F5;

    .emoji-tab-li {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 40px;
      width: 60px;
      cursor: pointer;

      &.curr {
        background: #FFFFFF;
      }

      img {
        width: 24px;

        &.emoji-ajmd, &.emoji-xxy, &.emoji-lt {
          width: 40px;
        }
      }
    }
  }
}
</style>