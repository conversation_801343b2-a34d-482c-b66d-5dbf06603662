<template>
  <div class="default-loading win-drag win-no-resize" v-if="isShow" v-show="isShowFlag" :style="{background: 'rgba(0,0,0,'+opacity+')',left: left+'px'}">
    <img class="default-loading-img" src="/img/loading.png" alt="">
    <span class="default-loading-title">{{ title }}</span>
  </div>
</template>
<script>
import {defineComponent, ref} from "vue";

export default defineComponent({
  name: "Loading",
  props: {
    // 标题
    title: {
      type: String,
      default: "加载中...",
    },
    // 背景框透明度
    opacity: {
      type: Number,
      default: 0.5,
    },
    // loading时长
    time: {
      type: Number,
    },
    // 回调,loading结束
    done: {
      type: Function,
    },
    // 是否显示loading
    isShowFlag: {
      type: Boolean,
      default: true,
    },
    // 左定位
    left: {
      type: Number,
      default: 0
    },
  },
  setup(props) {
    // 是否显示当前组件
    const isShow = ref(true);

    // loading关闭时间，默认不关
    if (props.time) {
      setTimeout(() => {
        handleDone();
      }, props.time)
    }

    // 移除当前组件
    function hide() {
      isShow.value = false;
    }

    // 操作回调
    const handleDone = () => {
      hide();
      props.done && props.done();
    };


    return {
      isShow,

      hide,
      handleDone
    };
  },
});
</script>
<style scoped lang="scss">
.default-loading {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 350;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  word-break: break-all;

  .default-loading-title {
    max-width: 80%;
    max-height: 80%;
  }

  .default-loading-img {
    display: block;
    width: 32px;
    margin: 0 auto 10px;
    animation: myLoading 800ms linear infinite;
  }

  @keyframes myLoading {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>