<template>
  <div
    :class="{
      'default-button': true,
      'primary-button': type === 'primary',
      'text-button': type === 'text',
      'simple-button': type === 'simple',
      'mini-button': size === 'mini',
      'small-button': size === 'small',
      'small2-button': size === 'small2',
      'medium-button': size === 'medium',
    }"
    :style="{
      width: width ? width + 'px' : '',
      height: height ? height + 'px' : '',
      fontSize: fontsize + 'px',
      borderRadius: radius,
    }"
  >
    <slot></slot>
  </div>
</template>
<script>
/**
  type:类型
    1.默认：蓝色边框  蓝色字体  白色背景
    2.primary：蓝色边框  白色字体 蓝色背景
    3.text：无边框 无padding 蓝色字体
    4.simple：灰色边框 黑色字体 白色背景
  size：大小
    1.默认/mini：左右边距15px 上下边距0
    2.small：左右边距15px 行高26px   ---较扁平
    3.small2:左右边距12px 行高28px  
 */
export default {
  name: "LyButton",
  props: {
    type: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "mini",
    },
    fontsize: {
      type: Number,
      default: 12,
    },
    width: {
      type: Number,
      default: 0,
    },
    height: {
      type: Number,
      default: 0,
    },
    radius: {
      type: String,
      default: "0px",
    },
  },
  setup() {
    return {};
  },
};
</script>

<style lang="scss" scoped>
.default-button {
  border: 1px solid $styleColor;
  padding: 0px 15px;
  font-size: 12px;
  background: #fff;
  color: $styleColor;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  // 颜色
  &.primary-button {
    background: $styleColor;
    color: #FFFFFF;
    &:hover {
      background: $styleColor;
    }
  }
  &.text-button {
    border: none;
    background: none;
    color: #333333;
    padding: 0 !important;
  }
  &.simple-button {
    color: #333333;
    border: 1px solid #E0E0E0;
    &:hover {
      color: $styleColor;
      border: 1px solid $styleColor;
    }
  }

  // 大小
  &.mini-button {
    padding: 0px 15px;
  }
  &.small-button {
    padding: 0px 15px;
    height: 26px;
    line-height: 24px;
  }
  &.small2-button {
    padding: 0 12px;
    height: 28px;
    line-height: 26px;
  }
  &.medium-button {
    padding: 0 20px;
    height: 30px;
    line-height: 28px;
  }
}
</style>