<template>
  <div class="classify-editor" ref="classifyEditorRef" @mouseup.stop="stopPropagation">
    <!--创建/重命名分组-->
    <LyDialog :title="(dialogObj.type==1?'新建':'重命名')+(dialogObj.isClassifyLabel?'标签':'讨论组/群分组')" :width="383" :height="146" :zIndex="301"
              :visible="dialogObj.type==1||dialogObj.type==2" :closeOnClickModal="true" @close="dialogOperate(1)" @confirm="dialogOperate(2)">
      <div class="main-dialog-box">
        <div class="ly-dialog-default-box">
          <label class="ly-dialog-default-label">
            <span class="ly-dialog-default-tips">*</span>
            <span>分组名称：</span>
          </label>
          <div class="ly-dialog-default-detail">
            <input class="ly-dialog-default-input" v-model.trim="dialogObj.classifyName" maxlength="20" placeholder="最多输入20字" @keyup.enter="dialogOperate(2)"/>
          </div>
        </div>
      </div>
    </LyDialog>
    <!--分组模式排序弹窗-->
    <LyDialog class="show-ul-dialog" :title="dialogObj.dialogTitle"
              :width="320" :height="271" :closeOnClickModal="false" :visible="dialogObj.type==7" @confirm="dialogSort(2)" @close="dialogSort(1)">
      <ul class="ly-dialog-show-ul" v-if="dialogObj.classifySortList">
        <li v-for="(item,key) in dialogObj.classifySortList" :key="item.id"
            :class="['ly-dialog-show-li',item.id==dialogObj.classifyMoveItem.id?'move':'',dialogObj.classifyMoveItem.id?'move-status':'']" :draggable="true"
            @dragstart="itemDrag($event,1,item)" @dragenter="itemDrag($event,2,item)" @dragover="itemDrag($event,3,item)" @dragend="itemDrag($event,4,item)">
          <i class="move-icon"></i>
          <span>{{ item.name }}</span>
        </li>
      </ul>
    </LyDialog>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import LyDialog from "@comp/ui/comps/LyDialog";
import {alert, loading, toast} from "@comp/ui";
import {classifyMoveApi, classifyMoveInApi, classifyMoveOutApi, classifyUpdateApi, deleteGroupSettingApi, groupSettingSortApi} from "@utils/net/api";

export default {
  name: "ClassifyEditor",
  components: {LyDialog},
  setup(props, ctx) {
    const store = useStore();
    let userInfo = store.getters.getUserInfo;
    // 配置文件
    let config = store.getters.getConfig.config;
    let classifyEditorRef = ref();

    let dialogObj = ref({
      type: 0,// 1创建分组2重命名分组3删除分组4移动至分组5添加至分组6移出至分组7分组排序
      done: "", // 操作完成的回调
      classifyType: 0,// 1群2讨论组3同事4客户-1模块
      classifyTypeName: "",// 分组类型名
      classifyName: "",// 分组名
      classifyUUID: "",// 分组uuid
      classifyValue: "",// 改变分组值
      notTips: false,// 不提示
      notSendCustomSysMsg: false,// 是否不发送自定义消息
      isClassifyLabel: false,// 是否同事/客户标签
      classifySortList: [],
      classifyMoveItem: {},
      itemMap: {},// 默认标签列表
      dialogTitle: "",// 显示弹窗名
    });

    // 初始化分组操作类型
    async function initClassify(param) {
      dialogObj.value.type = param.type;
      dialogObj.value.done = param.done;
      dialogObj.value.notTips = param.notTips;
      dialogObj.value.notSendCustomSysMsg = param.notSendCustomSysMsg || false;
      dialogObj.value.classifyType = param.classifyType;
      dialogObj.value.classifyName = param.classifyName;
      dialogObj.value.classifyUUID = param.classifyUUID;
      dialogObj.value.classifyValue = param.classifyValue || "";
      dialogObj.value.classifySortList = param.classifySortList || [];
      dialogObj.value.itemMap = param.itemMap || {};
      dialogObj.value.classifyTypeName = dialogObj.value.classifyType == 1 ? "群" : dialogObj.value.classifyType == 2 ? "讨论组" : dialogObj.value.classifyType == 3 ? "同事" : "客户";
      dialogObj.value.isClassifyLabel = dialogObj.value.classifyType == 3 || dialogObj.value.classifyType == 4;
      switch (Number(dialogObj.value.type)) {
        case 3:
          // 3删除分组
          alert({
            title: "删除提示",
            content: dialogObj.value.isClassifyLabel ? "确认删除标签?" : `所选分组将会被删除，分组中全部讨论组/群将移出该分组`,
            done: async type => {
              if (type == 1) {
                let res = await updateClassify();
                resDone(res);
              } else {
                resDone();
              }
            }
          });
          break;
        case 4:
        case 5:
        case 6:
          // 操作同事标签判断权限
          if (dialogObj.value.classifyType == 3 && (dialogObj.value.type == 4 || dialogObj.value.type == 5)) {
            store.dispatch("getForwardPur");
          }
          // 4移动至分组5添加至分组6移出至分组
          let res = await updateClassify();
          resDone(res);
          break;
      }
      // 设置弹窗标题
      switch (Number(dialogObj.value.classifyType)) {
        case -1:
          dialogObj.value.dialogTitle = "模块排序";
          break;
        case 1:
        case 2:
          dialogObj.value.dialogTitle = "讨论组/群分组排序";
          break;
        case 3:
          dialogObj.value.dialogTitle = "同事排序";
          break;
        case 4:
          dialogObj.value.dialogTitle = "客户排序";
          break;
        default:
          dialogObj.value.dialogTitle = "";
          break;
      }
      nextTick(() => {
        let inputElm = classifyEditorRef.value.querySelector("input");
        if (inputElm) {
          inputElm.focus();
        }
      });
    }

    // 弹窗操作 key1关闭2确认
    async function dialogOperate(key) {
      if (key == 2) {
        if (!dialogObj.value.classifyName) {
          toast({title: "请完善分组名称", type: 2, zIndex: 400});
          return;
        }
        // 创建分组api
        let res = await updateClassify();
        if (!res.success) {
          return;
        }
        resDone(res);
      } else {
        resDone();
      }
      dialogObj.value.type = 0;
      dialogObj.value.classifyName = "";
    }

    // type 1创建分组2重命名分组3删除分组4移动至分组5添加至分组6移出至分组
    async function updateClassify() {
      let res = {};
      let type = "";
      let remark = "";
      if (!dialogObj.value.notTips) {
        loading();
      }
      switch (Number(dialogObj.value.type)) {
        case 1:
          type = "add";
          remark = `创建${dialogObj.value.classifyTypeName}${dialogObj.value.isClassifyLabel ? "标签" : "分组"}`;
          res = await classifyUpdate();
          break;
        case 2:
          type = "rename";
          remark = "重命名分组名称";
          res = await classifyUpdate();
          break;
        case 3:
          type = "delete";
          remark = `删除${dialogObj.value.classifyTypeName}${dialogObj.value.isClassifyLabel ? "标签" : "分组"}`;
          res = await deleteGroupSettingApi({
            msgBody: JSON.stringify({
              type: dialogObj.value.classifyType,
              empNo: userInfo.workerNo,
              uuid: dialogObj.value.classifyUUID,
            })
          });
          break;
        case 4:
          type = "move";
          remark = `移动至${dialogObj.value.classifyName}`;
          res = await classifyMoveApi({
            msgBody: JSON.stringify({
              type: dialogObj.value.classifyType,
              uuids: dialogObj.value.classifyUUID,
              settingJson: JSON.stringify(dialogObj.value.classifyValue)
            })
          });
          break;
        case 5:
          type = "moveIn";
          remark = `添加至${dialogObj.value.classifyName}`;
          res = await classifyMoveInApi({
            msgBody: JSON.stringify({
              type: dialogObj.value.classifyType,
              uuids: dialogObj.value.classifyUUID,
              settingJson: JSON.stringify(dialogObj.value.classifyValue)
            })
          });
          break;
        case 6:
          type = "moveOut";
          remark = `移出至${dialogObj.value.classifyName}`;
          res = await classifyMoveOutApi({
            msgBody: JSON.stringify({
              type: dialogObj.value.classifyType,
              settingJson: JSON.stringify(dialogObj.value.classifyValue)
            })
          });
          break;
      }
      if (!dialogObj.value.notTips) {
        loading().hide();
      }
      if (res.success) {
        // 更新本地配置
        let updateParam = {upKey: "tag", type: dialogObj.value.classifyType, op: type, id: res?.data?.data?.id, uuid: dialogObj.value.classifyUUID || res?.data?.data?.uuid, name: dialogObj.value.classifyName, value: dialogObj.value.classifyValue};
        store.commit("updateLabelClassify", updateParam);
        if (!dialogObj.value.notTips) {
          toast({title: `操作成功`, type: 1, zIndex: 400});
        }
        if (!dialogObj.value.notSendCustomSysMsg) {
          store.dispatch("sendCustomSysMsgByGroups");
        }
      } else {
        if (!dialogObj.value.notTips) {
          toast({title: res.errorMsg || "系统错误,请重试", type: 2, zIndex: 400});
        }
      }
      return res;
    }

    // 新增/重命名分组
    async function classifyUpdate() {
      return classifyUpdateApi({
        msgBody: JSON.stringify({
          uuid: dialogObj.value.classifyUUID,
          name: dialogObj.value.classifyName,
          type: dialogObj.value.classifyType,
        })
      });
    }

    // 拖拽移动分组模式列表  type-1开始2进入3过程4结束
    function itemDrag(e, type, item) {
      switch (type) {
        case 1:
          dialogObj.value.classifyMoveItem = item;
          break;
        case 2:
          if (dialogObj.value.classifyMoveItem !== item) {
            let oldIndex = dialogObj.value.classifySortList.indexOf(dialogObj.value.classifyMoveItem);
            let newIndex = dialogObj.value.classifySortList.indexOf(item);
            let newItems = [...dialogObj.value.classifySortList];
            // 删除老节点
            newItems.splice(oldIndex, 1);
            // 添加新节点
            newItems.splice(newIndex, 0, dialogObj.value.classifyMoveItem);
            dialogObj.value.classifySortList = [...newItems];
          }
          e.preventDefault();
          break;
        case 3:
          // 去除禁止光标
          e.preventDefault();
          break;
        case 4:
          dialogObj.value.classifyMoveItem = {};
          break;
      }
    }

    // 操作框确认 1群2讨论组3同事4客户-1模块
    async function dialogSort(key) {
      if (key == 2) {
        let classifySortList = [];
        switch (String(dialogObj.value.classifyType)) {
          case "-1":
            // 分组模式排序保存修改到服务器
            dialogObj.value.classifySortList.map(item => {
              classifySortList.push(item.id);
            });
            store.dispatch("setModifySettings", {type: 3, key: config.settings.type13, value: classifySortList.join(",")}).then(res => {
              if (res.success) {
                // 发送通知给自己和别人(用于多端同步)
                store.getters.getNim.sendCustomSysMsg({
                  scene: "p2p",
                  to: userInfo.workerNo,
                  content: JSON.stringify({
                    type: 'refreshSetting',
                    source: 1, //pc 2 android 3ios
                    key: "classify_type",
                    value: classifySortList.join(",")
                  }),
                  done: function () {}
                });
                toast({title: "设置成功", type: 1});
              } else {
                toast({title: "设置失败," + (res.errorMsg || "系统错误"), type: 2});
              }
            });
            break;
          case "2":
          case "3":
          case "4":
            // 分组模式排序保存修改到服务器
            dialogObj.value.classifySortList.map((item, key) => {
              classifySortList.push({id: item.id, sort: (key + 1), type: item.type});
            });
            // 讨论组和群合并后外层type传11
            let res = await groupSettingSortApi({
              msgBody: JSON.stringify({
                empNo: userInfo.workerNo,
                type: dialogObj.value.classifyType == 1 || dialogObj.value.classifyType == 2 ? 11 : dialogObj.value.classifyType,
                sortList: classifySortList
              })
            });
            if (res?.success) {
              toast({title: "设置成功", type: 1});
              store.dispatch("sendCustomSysMsgByGroups");
            } else {
              toast({title: "设置失败", type: 2});
            }
            break;
        }
        resDone();
      } else {
        resDone();
      }
      dialogObj.value.type = 0;
    }

    function resDone(res) {
      dialogObj.value.done && dialogObj.value.done(res || {});
    }

    // 阻止点击穿透
    function stopPropagation() {}

    return {
      classifyEditorRef,
      dialogObj,

      initClassify,
      dialogOperate,
      itemDrag,
      dialogSort,
      stopPropagation,
    }
  }
}
</script>
<style scoped lang="scss">
.classify-editor {
  .ly-dialog-default-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 10px;

    &.center {
      align-items: center;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .ly-dialog-default-label {
      font-size: 13px;
      color: #666666;
      line-height: 30px;
      width: 80px;
      text-align: right;

      .ly-dialog-default-tips {
        color: #EE3939;
        margin-right: 5px;
      }
    }

    .ly-dialog-default-detail {
      width: 240px;
      position: relative;

      .ly-dialog-default-sel-text,
      .ly-dialog-default-input {
        width: 100%;
        line-height: 28px;
        padding-left: 10px;
        border: 1px solid #CCCCCC;
        border-radius: 4px;

        &:hover,
        &:focus {
          border: 1px solid #666666;
        }
      }

      .ly-dialog-default-sel-text {
        color: #545454;

        &:after {
          content: "";
          position: absolute;
          top: 50%;
          right: 10px;
          transform: translateY(-50%);
          width: 0;
          height: 0;
          border-width: 4px 4px 0;
          border-style: solid;
          border-color: #000000 transparent transparent transparent;
        }

        &.show {
          color: #000000;
        }

        &.sel {
          &:after {
            border-width: 0 4px 4px 4px;
            border-color: transparent transparent #000000 transparent;
          }
        }
      }

      .ly-dialog-default-sel-ul {
        width: 100%;
        position: absolute;
        top: 30px;
        left: 0;
        box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
        border-radius: 2px;
        border: 1px solid #CCCCCC;
        background: #FFFFFF;
        z-index: 1;

        .ly-dialog-default-sel-li {
          width: 100%;
          line-height: 30px;
          padding-left: 10px;

          &:hover {
            background: #E6F7FF;
          }
        }
      }

      .ly-dialog-default-textarea {
        width: 100%;
        height: 110px;
        background: #FFFFFF;
        border-radius: 2px;
        border: 1px solid #CCCCCC;
        padding: 10px;
      }

      .ly-dialog-default-textarea-text {
        position: absolute;
        right: 10px;
        bottom: 10px;
      }

      .red {
        color: $styleColor;
      }
    }
  }

  :deep(.show-ul-dialog .content) {
    padding: 0 0 16px 0;
  }

  .ly-dialog-show-ul {
    width: 100%;
    height: 100%;
    max-height: 240px;
    overflow-y: auto;

    .ly-dialog-show-li {
      width: 100%;
      height: 30px;
      display: flex;
      align-items: center;
      padding: 0 12px;

      &:hover {
        background: $styleBg1Hover;
      }

      &.move {
        box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.18) !important;
      }

      &.move-status {
        &:hover {
          box-shadow: none;
          background: none;
        }
      }

      .move-icon {
        width: 16px;
        height: 16px;
        background-image: url("/img/workbench/icon_move.png");
        background-repeat: no-repeat;
        background-size: 100%;
        margin-right: 8px;
        flex-shrink: 0;
      }
    }
  }
}
</style>