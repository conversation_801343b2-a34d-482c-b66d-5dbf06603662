<template>
  <div class="proxy">
    <div class="proxy-modal win-drag" v-show="isShow" @mousedown="showComponents({type:3})">
      <div class="proxy-box win-no-drag win-no-resize" @mousedown.stop="">
        <!--顶部标题-->
        <div class="proxy-header">
          <span class="title">线路切换</span>
          <img class="close" src="/img/close.png" alt="" @click="showComponents({type:3})">
        </div>
        <div v-if="proxyInfo && proxyInfo.proxyList && proxyInfo.proxyList.length>0" class="proxy-content">
          <div v-if="netVpnObj.vpnInfo.isShowVpn">
            <!--存在专属线路权限-->
            <div class="proxy-main-title">当前线路情况</div>
            <div class="proxy-name-box proxy-name-intr">
              <div class="proxy-name">线路名称：{{ proxyInfo.proxyCurrent.name || proxyInfo.proxyList[0].name }}</div>
              <div class="proxy-intr">
                <span>当前延迟：</span>
                <span :class="getDelay(1, proxyInfo.proxyCurrent.delay)">{{ getDelay(2, proxyInfo.proxyCurrent.delay) }}</span>
              </div>
            </div>
            <div class="proxy-name-box proxy-name-title"></div>
            <div class="proxy-main-title">代理线路切换</div>
            <div class="proxy-main-intr">可以通过切换不同的网络线路，保障您的使用流畅性</div>
            <div class="proxy-name-box proxy-name-intr">
              <div :class="['proxy-name','show-check','can-check', !netVpnObj.vpnInfo.vpnFlag?'sel':'']" @click.stop="changeVpnFlag(false)">正常线路</div>
            </div>
            <div class="proxy-name-box proxy-name-intr">
              <div :class="['proxy-name','show-check','can-check', netVpnObj.vpnInfo.vpnFlag?'sel':'']" @click.stop="changeVpnFlag(true)">专属线路</div>
            </div>
          </div>
          <div v-else>
            <div class="proxy-main-title">
              <span>当前延迟：</span>
              <span :class="getDelay(1, proxyInfo.proxyCurrent.delay)">{{ getDelay(2, proxyInfo.proxyCurrent.delay) }}</span>
            </div>
            <div class="proxy-main-intr">可以通过切换不同的网络线路，保障您的使用流畅性</div>
            <div class="proxy-name-box proxy-name-title">
              <div class="proxy-name">线路名称</div>
              <div class="proxy-intr">延迟/ms</div>
            </div>
            <div class="proxy-li-box">
              <div class="proxy-name-box proxy-name-intr" v-for="(item,key) in proxyInfo.proxyList" :key="key" @click="selProxy(item)">
                <div :class="['proxy-name','show-check',proxyInfo.proxyCurrent.key==item.key?'sel':'']">{{ item.name }}</div>
                <div :class="['proxy-intr',getDelay(1, item.delay)]">{{ getDelay(2, item.delay) }}</div>
              </div>
              <div v-if="proxyInfo.switchAbroad" class="proxy-name-box proxy-name-intr">
                <div :class="['proxy-name','show-check',proxyInfo.proxyCurrent.key==proxyInfo.proxyAbroad.key?'sel':'']">{{ proxyInfo.proxyAbroad.name }}</div>
                <div :class="['proxy-intr',getDelay(1, proxyInfo.proxyAbroad.delay)]">{{ getDelay(2, proxyInfo.proxyAbroad.delay) }}</div>
              </div>
              <div v-if="proxyInfo.switchTemp" class="proxy-name-box proxy-name-intr" v-for="(item,key) in proxyInfo.proxyTemp" :key="key">
                <div :class="['proxy-name','show-check',proxyInfo.proxyCurrent.key==item.key?'sel':'']">{{ item.name }}</div>
                <div :class="['proxy-intr',getDelay(1, item.delay)]">{{ getDelay(2, item.delay) }}</div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="proxy-loading">
          <img class="loading-img" src="/img/waitting.gif" alt="">
          <div class="show-text">加载中</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";

export default {
  name: "Proxy",
  props: {
    isShow: {
      type: Boolean,
      default: true
    },
    showComponents: {
      type: Function
    },
  },
  setup(props, ctx) {
    const store = useStore();

    // 初始化代理信息，确保有默认值
    let proxyInfo = ref({
      open: true,
      api: 'http://localhost:8888/api',
      host: 'localhost',
      maxDelay: 500,
      timeoutDelay: 3000,
      defaultCountNum: 3,
      errorCountNum: 6,
      proxyCurrent: {
        name: '默认线路',
        key: 'default',
        url: '',
        delay: 50,
        timeout: 3000
      },
      proxyList: [
        {
          name: '默认线路',
          key: 'default',
          url: '',
          delay: 50,
          timeout: 3000
        }
      ],
      proxyAbroad: {
        name: '海外线路',
        key: 'abroad',
        url: 'http://abroad.example.com',
        delay: 200,
        timeout: 10000
      },
      proxyTemp: [],
      switchAbroad: false,
      switchTemp: false,
      pingList: [
        { name: '百度', key: 'baidu', url: 'baidu.com', delay: 30 },
        { name: 'QQ', key: 'qq', url: 'qq.com', delay: 25 }
      ],
      pingInfo: {
        baidu: 30,
        qq: 25,
        default: 50,
        home: 120,
        abroad: 200
      },
      pingDone: true,
      defaultCount: 0,
      pingCount: 0,
      firstNet: 1,
      key: 1
    });

    // 专属线路对象
    let netVpnObj = ref({
      vpnBoxFlag: false,
      vpnInfo: {
        isShowVpn: false,
        vpnFlag: false,
        hasPermission: false
      },
      timer: "",
      showTimer: "",
    });

    // 监听代理线路
    watch(() => store.state.jjsProxy?.key,
      (newValue, oldValue) => {
        if (newValue !== oldValue) {
          // 更新代理信息
          console.log('代理线路切换:', newValue);
        }
      }, {
        deep: true
      }
    )

    // 监听vpn权限
    watch(() => store.state.jjsProxy?.vpnInfo,
      (newValue, oldValue) => {
        if (newValue) {
          netVpnObj.value.vpnInfo = {...newValue};
        }
      }, {
        deep: true
      }
    );

    // 获取延迟 type-1class名-2具体延迟数
    function getDelay(type, delay) {
      if (type == 1) {
        return isNaN(parseInt(delay)) || delay > (proxyInfo.value?.maxDelay || 500) ? "red" : delay < 500 ? "green" : "yellow";
      } else if (type == 2) {
        return isNaN(parseInt(delay)) ? "超时" : `${delay}ms`;
      }
    }

    // 选择代理线路 TODO 和自动切换线路冲突待定
    function selProxy(item) {
      // store.commit("setJjsProxy", item);
      // remote.store.commit("setJjsProxy", item);
    }

    // 切换vpn开关
    function changeVpnFlag(vpnFlag) {
      if (netVpnObj.value.vpnInfo.vpnFlag != vpnFlag) {
        console.log('切换VPN状态:', vpnFlag);
        // 更新本地状态
        netVpnObj.value.vpnInfo.vpnFlag = vpnFlag;
        // 可以在这里添加实际的VPN切换逻辑
      }
    }

    return {
      proxyInfo,
      netVpnObj,

      getDelay,
      selProxy,
      changeVpnFlag,
    }
  }
}
</script>
<style scoped lang="scss">
.proxy-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 89;
  background: rgba(0, 0, 0, 0.7);

  .red {
    color: $styleColor;
  }

  .green {
    color: #0FC85D;
  }

  .yellow {
    color: #FF9E00;
  }

  .proxy-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 360px;
    background: #FFFFFF;
    box-shadow: 0px 6px 12px 0px rgba(155, 155, 155, 0.5);
    border-radius: 4px;
    border: 1px solid #E7E7E7;

    .proxy-header {
      position: relative;
      display: flex;
      align-items: center;
      height: 30px;
      padding: 0 16px;
      background: #F0F0F0;
      font-weight: 700;
      border-radius: 6px 6px 0px 0px;

      .intr {
        color: #666666;
        margin-right: 5px;
      }

      .close {
        position: absolute;
        top: 50%;
        right: 12px;
        transform: translateY(-50%);
        width: 20px;
        cursor: pointer;
        padding: 4px;
      }
    }

    .proxy-content {
      padding: 16px;

      .proxy-main-title {
        font-size: 14px;
        font-weight: 500;
        color: #000000;
        line-height: 20px;
      }

      .proxy-main-intr {
        color: #999999;
        line-height: 17px;
        margin-top: 2px;
      }

      .proxy-name-title {
        border-top: 1px solid #E7E7E7;
        margin-top: 16px;
        padding-top: 16px;
        font-weight: 500;
        line-height: 17px;
      }

      .proxy-name-intr {
        margin-top: 10px;

        .proxy-name {
          position: relative;

          &.show-check {
            padding-left: 22px;

            &.can-check {
              &:before {
                background: #FFFFFF;
              }
            }

            &.sel {
              &:before {
                background: $styleColor;
                border: 1px solid $styleColor;
              }

              &:after {
                content: "";
                position: absolute;
                top: 5px;
                left: 4px;
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #FFFFFF;
              }
            }

            &:before {
              content: "";
              position: absolute;
              top: 1px;
              left: 0;
              width: 12px;
              height: 12px;
              border-radius: 50%;
              border: 1px solid #E0E0E0;
              background: $styleBg1Hover;
            }
          }
        }
      }

      .proxy-li-box {
        max-height: 84px;
        overflow-y: auto;
      }

      .proxy-name-box {
        display: flex;
        align-items: center;

        .proxy-name {
          width: 60%;
          flex-shrink: 0;
          cursor: pointer;
        }

        .proxy-intr {
          width: 40%;
          flex-shrink: 0;
        }
      }
    }

    .proxy-loading {
      height: calc(100% - 30px);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .loading-img {
        width: 32px;
      }

      .show-text {
        color: #666666;
        margin-top: 10px;
      }
    }
  }
}
</style>