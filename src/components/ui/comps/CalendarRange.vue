<template>
  <div class="calendar" @click.stop="stopPropagation">
    <div class="calendar-content">
      <div class="calendar-box calendar-start">
        <div class="calendar-operator">
          <div class="calendar-change-box">
            <i :class="['calendar-icon','preYear',!startObj.preYear?'disabled':'']" @click="changeDate('preYear',1,'',!startObj.preYear)"></i>
            <i :class="['calendar-icon','preMonth',!startObj.preMonth?'disabled':'']" @click="changeDate('preMonth',1,'',!startObj.preMonth)"></i>
          </div>
          <div class="calendar-title">{{ dateFormat(startObj.changeDayInfo, "yyyy年MM月") }}</div>
          <div class="calendar-change-box">
            <i :class="['calendar-icon','nextMonth',!startObj.nextMonth?'disabled':'']" @click="changeDate('nextMonth',1,'',!startObj.nextMonth)"></i>
            <i :class="['calendar-icon','nextYear',!startObj.nextYear?'disabled':'']" @click="changeDate('nextYear',1,'',!startObj.nextYear)"></i>
          </div>
        </div>
        <div class="calendar-day">
          <div class="day-header">
            <div class="day-list">一</div>
            <div class="day-list">二</div>
            <div class="day-list">三</div>
            <div class="day-list">四</div>
            <div class="day-list">五</div>
            <div class="day-list">六</div>
            <div class="day-list">日</div>
          </div>
          <div class="day-content">
            <div class="day-ul" v-for="(item,key) in startObj.currentDayList" :key="key">
              <div class="day-list" :class="{'curr':isDay(item1),'sel':isDay(item1,1),'gray':(!isMonth(item1,1)||item1.disabled),'range':isRange(item1,1)}"
                   v-for="(item1,key1) in item" :key="item1" @click="selItemDate(item1)" @mouseenter="hoverItemDate(item1)">
                <div class="day-title">{{ isDay(item1) ? "今" : item1.day }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="calendar-line"></div>
      <div class="calendar-box calendar-end">
        <div class="calendar-operator">
          <div class="calendar-change-box">
            <i :class="['calendar-icon','preYear',!endObj.preYear?'disabled':'']" @click="changeDate('preYear',2,'',!endObj.preYear)"></i>
            <i :class="['calendar-icon','preMonth',!endObj.preMonth?'disabled':'']" @click="changeDate('preMonth',2,'',!endObj.preMonth)"></i>
          </div>
          <div class="calendar-title">{{ dateFormat(endObj.changeDayInfo, "yyyy年MM月") }}</div>
          <div class="calendar-change-box">
            <i :class="['calendar-icon','nextMonth',!endObj.nextMonth?'disabled':'']" @click="changeDate('nextMonth',2,'',!endObj.nextMonth)"></i>
            <i :class="['calendar-icon','nextYear',!endObj.nextYear?'disabled':'']" @click="changeDate('nextYear',2,'',!endObj.nextYear)"></i>
          </div>
        </div>
        <div class="calendar-day">
          <div class="day-header">
            <div class="day-list">一</div>
            <div class="day-list">二</div>
            <div class="day-list">三</div>
            <div class="day-list">四</div>
            <div class="day-list">五</div>
            <div class="day-list">六</div>
            <div class="day-list">日</div>
          </div>
          <div class="day-content">
            <div class="day-ul" v-for="(item,key) in endObj.currentDayList" :key="key">
              <div class="day-list" :class="{'curr':isDay(item1),'sel':isDay(item1,1),'gray':(!isMonth(item1,2)||item1.disabled),'range':isRange(item1,1)}"
                   v-for="(item1,key1) in item" :key="item1" @click="selItemDate(item1)" @mouseenter="hoverItemDate(item1)">
                <div class="day-title">{{ isDay(item1) ? "今" : item1.day }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="calendar-footer">
      <span class="calendar-label" @click="selDay(0)">今天</span>
      <span class="calendar-label" @click="selDay(-1)">昨天</span>
      <span class="calendar-label" @click="selDay(-7)">过去7天</span>
      <span class="calendar-label" @click="selDay(-14)">过去14天</span>
      <span class="calendar-label" @click="selDay(-30)">过去30天</span>
      <span class="calendar-label" @click="selDay(-90)">过去90天</span>
      <span class="calendar-label" @click="selDay(-180)">过去180天</span>
      <span class="calendar-label" @click="selDay(-365)">过去365天</span>
    </div>
  </div>
</template>
<script>
import {ref} from "vue";
import {useStore} from "vuex";
import {dateFormat} from "@utils";

export default {
  name: "Calendar",
  props: {
    // 选择日期
    setCurrentDay: {
      type: Function,
      default: function () {}
    },
    // 改变月份
    setChangeDay: {
      type: Function,
      default: function () {}
    },
  },
  setup(props, ctx) {
    const store = useStore();
    // 系统时间信息
    let serverDayInfo = ref(new Date());
    // 开始时间对象
    let startObj = ref({
      currentDayInfo: new Date(),// 当前时间
      changeDayInfo: new Date(),// 改变显示的时间
      currentDayList: [],// 显示的日期列表
      day: "",// 传给组件的开始时间
      selDay: "",// 点击选择的开始时间
      preYear: true,// 是否可以选择上一年
      preMonth: true,// 是否可以选择上个月
      nextYear: true,// 是否可以选择下一年
      nextMonth: true,// 是否可以选择下个月
    });
    // 结束时间对象
    let endObj = ref({
      currentDayInfo: new Date(),
      changeDayInfo: new Date(),
      currentDayList: [],
      day: "",
      selDay: "",
      preYear: true,
      preMonth: true,
      nextYear: true,
      nextMonth: true,
    });
    // 鼠标悬浮的日期
    let hoverDate = ref("");
    // 限制最小日期
    let minDate = "";
    // 限制最大日期
    let maxDate = "";

    function getCurrentDate() {
      // 设置时间差
      store.dispatch("setDiffTime");
      return new Date(Date.now() + store.getters.getDiffTime);
    }

    // 获取日历 time-时间对象
    async function getCalendar(param) {
      let startCurrDate = "";
      let endCurrDate = "";
      if (param.change) {
        if (param.selType == 1) {
          startCurrDate = startObj.value.changeDayInfo;
        } else {
          endCurrDate = endObj.value.changeDayInfo;
        }
      } else {
        hoverDate.value = "";
        startObj.value.selDay = "";
        endObj.value.selDay = "";
        startCurrDate = getCurrentDate();
        endCurrDate = startCurrDate;
        serverDayInfo.value = startCurrDate;
        // 传参日期
        if (param.start) {
          startCurrDate = param.start;
          startObj.value.day = param.start;
        }
        if (param.end) {
          endCurrDate = param.end;
          endObj.value.day = param.end;
        }
        // 限制日期范围
        if (param.minDate) {
          minDate = param.minDate;
        }
        if (param.maxDate) {
          maxDate = param.maxDate;
        }
        startObj.value.currentDayInfo = startCurrDate;
        startObj.value.changeDayInfo = startCurrDate;
        // 开始月份和结束月份一致获取上月数据
        if (startCurrDate.getFullYear() == endCurrDate.getFullYear() && startCurrDate.getMonth() == endCurrDate.getMonth()) {
          startCurrDate = changeDate("preMonth", 1, true);
        }
        startObj.value.currentDayInfo = startCurrDate;
        startObj.value.changeDayInfo = startCurrDate;
        endObj.value.currentDayInfo = endCurrDate;
        endObj.value.changeDayInfo = endCurrDate;
      }

      if (param.selType == 1) {
        // 设置开始时间列表
        startObj.value.currentDayList = setCurrentMonthList(startCurrDate, minDate, maxDate);
      } else if (param.selType == 2) {
        // 设置结束时间列表
        endObj.value.currentDayList = setCurrentMonthList(endCurrDate, minDate, maxDate);
      } else {
        // 初始化开始和结束时间列表
        startObj.value.currentDayList = setCurrentMonthList(startCurrDate, minDate, maxDate);
        endObj.value.currentDayList = setCurrentMonthList(endCurrDate, minDate, maxDate);
      }

      // 设置上下月/年是否可点击
      let startListObj = {year: startObj.value.changeDayInfo.getFullYear(), month: startObj.value.changeDayInfo.getMonth() + 1};
      let endListObj = {year: endObj.value.changeDayInfo.getFullYear(), month: endObj.value.changeDayInfo.getMonth() + 1};
      // 时间范围1000-3000
      if (startListObj.year <= 1000) {
        startObj.value.preYear = false;
      } else {
        startObj.value.preYear = true;
      }
      if (startListObj.year - 1 <= 1000 && startListObj.month == 1) {
        startObj.value.preMonth = false;
      } else {
        startObj.value.preMonth = true;
      }
      if (endListObj.year >= 3000) {
        endObj.value.nextYear = false;
      } else {
        endObj.value.nextYear = true;
      }
      if (startListObj.year + 1 >= 3000 && startListObj.month == 12) {
        endObj.value.nextMonth = false;
      } else {
        endObj.value.nextMonth = true;
      }
      // 开始时间下月/年不能超过结束时间列表
      if (startListObj.year >= endListObj.year) {
        startObj.value.nextYear = false;
      } else {
        startObj.value.nextYear = true;
      }
      if (startListObj.month + 1 >= endListObj.month) {
        startObj.value.nextMonth = false;
      } else {
        startObj.value.nextMonth = true;
      }
      // 结束时间上月/年不能超过开始时间列表
      if (endListObj.year <= startListObj.year) {
        endObj.value.preYear = false;
      } else {
        endObj.value.preYear = true;
      }
      if (endListObj.month - 1 <= startListObj.month) {
        endObj.value.preMonth = false;
      } else {
        endObj.value.preMonth = true;
      }
    }

    // 设置当前月份日期列表
    function setCurrentMonthList(currDate, minDate, maxDate) {
      // 获取当月信息
      let year = currDate.getFullYear();
      let month = currDate.getMonth() + 1;
      let firstDay = new Date(year + '/' + month);
      // 上一天
      let preDay = new Date(firstDay - 24 * 60 * 60 * 1000);
      // 下个月
      let nextMonth = month + 1;
      let nextYear = year;
      if (nextMonth > 12) {
        nextYear++;
        nextMonth -= 12;
      }
      // 当月最后一天
      let lastDay = new Date(new Date(nextYear + '/' + nextMonth).getTime() - 24 * 60 * 60 * 1000);
      let monthDayList = [];
      // 周一开始计算 上个月在当前月显示的日期
      for (let i = preDay.getDay() - 1; i >= 0; i--) {
        monthDayList.push({
          year: preDay.getFullYear(),
          month: preDay.getMonth() + 1,
          day: preDay.getDate() - i
        });
      }
      // 当前月的日期
      for (let i = 1; i <= lastDay.getDate(); i++) {
        monthDayList.push({
          year: lastDay.getFullYear(),
          month: lastDay.getMonth() + 1,
          day: i
        });
      }
      let dayLength = 7;
      if (monthDayList.length + (dayLength - lastDay.getDay()) == 35) {
        dayLength = 14;
      }
      // 周日结束 下个月在当前月显示的日期
      for (let i = 1; i <= dayLength - lastDay.getDay(); i++) {
        monthDayList.push({
          year: nextYear,
          month: nextMonth,
          day: i
        });
      }
      // 当前月份每7天一个数组数据
      let monthIndex = -1;
      let monthList = [];
      monthDayList.map((item, index) => {
        if (index % 7 == 0) {
          monthIndex++;
        }
        if (!monthList[monthIndex]) {
          monthList[monthIndex] = [];
        }
        let dateTime = new Date(item.year + "/" + item.month + "/" + item.day).getTime();
        // 限制日期范围
        if (minDate && dateTime < minDate) {
          item.disabled = "minDate";
        }
        if (maxDate && dateTime > maxDate) {
          item.disabled = "maxDate";
        }
        monthList[monthIndex].push(item);
      });
      return monthList;
    }

    // 选择当前日期
    function selItemDate(item) {
      if (item.disabled) {
        props.setCurrentDay({err: item.disabled});
        return;
      }
      startObj.value.day = "";
      endObj.value.day = "";
      let selDate = new Date(item.year + "/" + item.month + "/" + item.day);
      if (!startObj.value.selDay) {
        startObj.value.selDay = selDate;
        return;
      }
      if (!endObj.value.selDay) {
        endObj.value.selDay = selDate;
      }
      let isReverse = startObj.value.selDay.getTime() - endObj.value.selDay.getTime() > 0;
      props.setCurrentDay({start: isReverse ? endObj.value.selDay : startObj.value.selDay, end: isReverse ? startObj.value.selDay : endObj.value.selDay});
    }

    // 判断是否指定时间-type-1当前选择时间-默认系统时间
    function isDay(item, type) {
      let flag = false;
      if (type == 1) {
        // 是选中的开始/结束件
        if ((startObj.value.day && isDate(startObj.value.day, item)) || (endObj.value.day && isDate(endObj.value.day, item)) ||
          (startObj.value.selDay && isDate(startObj.value.selDay, item)) || (endObj.value.selDay && isDate(endObj.value.selDay, item)) ||
          (((startObj.value.selDay && !endObj.value.selDay) || (!startObj.value.selDay && endObj.value.selDay)) && hoverDate.value && isDate(hoverDate.value, item))) {
          flag = true;
        }
      } else {
        // 是当前系统时间
        if (isDate(serverDayInfo.value, item)) {
          flag = true;
        }
      }
      return flag;
    }

    // 判断是否当前时间
    function isDate(dateInfo, item) {
      return dateInfo.getFullYear() == item.year && dateInfo.getMonth() + 1 == item.month && dateInfo.getDate() == item.day;
    }

    // 是否选中时间范围
    function isRange(item) {
      let flag = false;
      let itemTime = new Date(item.year + "/" + item.month + "/" + item.day);
      let startTime, endTime;
      // 获取四种场景的开始和结束时间范围
      if (startObj.value.day && endObj.value.day) {
        startTime = Math.min(startObj.value.day.getTime(), endObj.value.day.getTime());
        endTime = Math.max(startObj.value.day.getTime(), endObj.value.day.getTime());
      } else if (startObj.value.selDay && endObj.value.selDay) {
        startTime = Math.min(startObj.value.selDay.getTime(), endObj.value.selDay.getTime());
        endTime = Math.max(startObj.value.selDay.getTime(), endObj.value.selDay.getTime());
      } else if (startObj.value.selDay && hoverDate.value) {
        startTime = Math.min(startObj.value.selDay.getTime(), hoverDate.value.getTime());
        endTime = Math.max(startObj.value.selDay.getTime(), hoverDate.value.getTime());
      } else if (endObj.value.selDay && hoverDate.value) {
        startTime = Math.min(endObj.value.selDay.getTime(), hoverDate.value.getTime());
        endTime = Math.max(endObj.value.selDay.getTime(), hoverDate.value.getTime());
      }
      // 判断当前时间是否在时间范围内
      if (startTime && endTime && itemTime >= startTime && itemTime <= endTime) {
        flag = true;
      }
      return flag;
    }

    // 是否当前月
    function isMonth(item, selType) {
      return (selType == 1 && startObj.value.changeDayInfo.getMonth() + 1 == item.month) || (selType == 2 && endObj.value.changeDayInfo.getMonth() + 1 == item.month);
    }

    // 改变日期 type改变类型 selType1开始2结束 dateFlag只返回日期 disabledFlag是否可以操作
    function changeDate(type, selType, dateFlag, disabledFlag) {
      if (disabledFlag) {
        return;
      }
      let date = selType == 1 ? startObj.value.changeDayInfo : endObj.value.changeDayInfo;
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let dateTime = "";
      switch (type) {
        case "preYear":
          year--;
          dateTime = new Date(year + "/" + date.getMonth() + "/1") - 24 * 60 * 60 * 1000;
          break;
        case "preMonth":
          month--;
          if (month < 1) {
            year--;
            month += 12;
          }
          dateTime = new Date(year + "/" + date.getMonth() + "/1") - 24 * 60 * 60 * 1000;
          break;
        case "nextMonth":
          month++;
          if (month > 12) {
            year++;
            month -= 12;
          }
          dateTime = new Date(year + "/" + month + "/1");
          break;
        case "nextYear":
          year++;
          dateTime = new Date(year + "/" + month + "/1");
          break;
      }
      // 限制日期范围
      if (minDate && dateTime < minDate) {
        props.setChangeDay({err: "minDate"});
        return;
      }
      if (maxDate && dateTime > maxDate) {
        props.setChangeDay({err: "maxDate"});
        return;
      }
      let changeDayInfo = new Date(year + "/" + month + "/1");
      if (dateFlag) {
        // 只获取日期数据
        return changeDayInfo;
      } else {
        if (selType == 1) {
          startObj.value.changeDayInfo = changeDayInfo;
        } else {
          endObj.value.changeDayInfo = changeDayInfo;
        }
        getCalendar({change: true, selType: selType});
        props.setChangeDay({date: changeDayInfo});
      }
    }

    // 选择时间范围
    function selDay(day) {
      let currentTime = getCurrentDate();
      let currentDay = new Date(currentTime.getFullYear() + "/" + (currentTime.getMonth() + 1) + "/" + currentTime.getDate());
      let endDate = new Date(currentDay.getTime() + day * 24 * 60 * 60 * 1000);
      props.setCurrentDay({start: endDate, end: (day == 0 || day == -1) ? endDate : currentDay});
    }

    // 鼠标悬浮过的日期
    function hoverItemDate(item) {
      // 选择时间后不在判断悬浮范围
      if ((startObj.value.day && endObj.value.day) || (startObj.value.selDay && endObj.value.selDay)) {
        return;
      }
      hoverDate.value = new Date(item.year + "/" + item.month + "/" + item.day);
    }

    // 阻止点击穿透
    function stopPropagation() {}

    return {
      startObj,
      endObj,

      getCalendar,
      selItemDate,
      isDay,
      isRange,
      isMonth,
      changeDate,
      dateFormat,
      selDay,
      hoverItemDate,
      stopPropagation,
    }
  }
}
</script>
<style scoped lang="scss">
.calendar {
  width: 100%;
  height: 100%;
  padding: 22px 20px;
  color: #000000;

  &.hide .calendar-day {
    display: none;
  }

  .calendar-content {
    display: flex;
    width: 100%;
    height: 250px;

    .calendar-box {
      width: 50%;
      height: 100%;

      &.calendar-start {
        .day-list {
          &.sel.range {
            &:after {
              left: 2px !important;
            }
          }
        }
      }

      &.calendar-end {
        .day-list {
          &.sel.range {
            &:after {
              width: calc(100% + 2px) !important;
            }
          }
        }
      }

      .calendar-operator {
        display: flex;
        align-items: center;

        .calendar-title {
          font-size: 14px;
          line-height: 20px;
          font-weight: 500;
          text-align: center;
          color: #222222;
          flex: 1;
        }

        .calendar-change-box {
          display: flex;
        }

        .calendar-icon {
          position: relative;
          width: 16px;
          height: 16px;
          background-image: url("/img/icon_calendar.png");
          background-repeat: no-repeat;
          background-size: 192px 16px;
          cursor: pointer;

          &:hover:after {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            line-height: 24px;
            padding: 3px 6px;
            font-size: 12px;
            color: #FFFFFF;
            background: rgba(0, 0, 0, 0.8);
            white-space: nowrap;
            z-index: 1;
            border-radius: 2px;
          }

          &.preYear {
            background-position: 0px 0;
            margin-right: 10px;

            &:hover {
              background-position: -64px 0;

              &:after {
                content: "\4e0a\4e00\5e74";
                z-index: 1;
              }
            }

            &.disabled {
              background-position: -128px 0;
            }
          }

          &.preMonth {
            background-position: -16px 0;
            margin-right: 10px;

            &:hover {
              background-position: -80px 0;

              &:after {
                content: "\4e0a\4e2a\6708";
                z-index: 1;
              }
            }

            &.disabled {
              background-position: -144px 0;
            }
          }

          &.nextMonth {
            background-position: -32px 0;
            margin-left: 10px;

            &:hover {
              background-position: -96px 0;

              &:after {
                content: "\4e0b\4e2a\6708";
                z-index: 1;
              }
            }

            &.disabled {
              background-position: -160px 0;
            }
          }

          &.nextYear {
            background-position: -48px 0;
            margin-left: 10px;

            &:hover {
              background-position: -112px 0;

              &:after {
                content: "\4e0b\4e00\5e74";
                z-index: 1;
              }
            }

            &.disabled {
              background-position: -176px 0;
            }
          }

          &.disabled {
            cursor: not-allowed;
          }
        }
      }

      .calendar-day {
        font-size: 12px;
        line-height: 17px;
        font-weight: 500;

        .rest,
        .gray {
          color: #BFBFBF;

          .day-radio {
            background: #BFBFBF;
          }
        }

        .day-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 11px;
          color: #666666;
        }

        .day-content {
          .day-ul {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
          }

          .day-list {
            cursor: pointer;
            position: relative;

            &.curr {
              color: $styleColor;
              border-radius: 50%;
              line-height: normal;
            }

            &:hover {
              color: $styleColor;
            }

            &.sel:not(.gray) {
              color: #FFFFFF;

              &:before {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 22px;
                height: 22px;
                background: $styleColor;
                color: #FFFFFF;
                border-radius: 50%;
                line-height: normal;
                z-index: -1;
              }
            }

            &.range:not(.gray) {
              &:after {
                content: "";
                position: absolute;
                top: 0;
                left: -5px;
                width: calc(100% + 10px);
                height: 100%;
                background: #FEF4F3;
                z-index: -2;
              }
            }
          }
        }

        .day-ul {
          margin-top: 6px;
        }

        .day-list {
          width: 28px;
          height: 26px;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;

          .day-radio {
            width: 4px;
            height: 4px;
            background: #2D91E6;
            border-radius: 50%;
            position: absolute;
            bottom: -7px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
      }
    }

    .calendar-line {
      width: 1px;
      height: 100%;
      margin: 0 20px;
      background: #E0E0E0;
    }
  }

  .calendar-footer {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 8px;

    .calendar-label {
      width: 76px;
      height: 20px;
      line-height: 18px;
      text-align: center;
      border-radius: 4px;
      color: #666666;
      border: 1px solid #E0E0E0;
      margin: 8px 8px 0 0;

      &.sel,
      &:hover {
        background: #FFF3F3;
        color: $styleColor;
        border: 1px solid $styleColor;
        cursor: pointer;
      }
    }
  }
}

</style>