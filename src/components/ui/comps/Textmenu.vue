<template>
  <div class="textmenu" ref="textmenuRef" @mouseup="clickEvent" @mouseenter="mouseenter" @mouseleave="mouseleave" v-show="isShow">
    <div class="menu-box" :style="'top:'+textmenuObj.y+'px;left:'+textmenuObj.x+'px;'">
      <ul class="menu-ul" :style="'top:'+textmenuObj.top+'px;left:'+textmenuObj.left+'px;height:'+textmenuObj.height+'px;'">
        <li class="menu-li textEls" :title="item.label" :class="{'sel':item.checked,'sub':item.submenu&&item.submenu.list,'hover':submenuObj.index==key}"
            v-for="(item,key) in menuList" :key="key" @mouseup.stop="clickEvent(item.click||'show', $event,item,submenuObj)" @mouseenter="menuEnter(item, key)" @mouseleave="menuLeave()">
          <span>{{ item.label }}</span>
        </li>
      </ul>
      <ul class="menu-ul menu-sub textEls" v-if="submenuObj.list.length>0" @mouseenter="submenuEnter"
          :style="'top:'+submenuObj.top+'px;left:'+submenuObj.left+'px;height:'+submenuObj.height+'px;'">
        <li class="menu-li textEls" :title="item.label" :class="{'sel':item.checked}" v-for="(item,key) in submenuObj.list" :key="key" @mouseup.stop="clickEvent(item.click||'show',$event,item,submenuObj)">
          <span>{{ item.label }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>

import {defineComponent, nextTick, onUnmounted, ref} from "vue";
import {getGlobal} from "@utils";

export default defineComponent({
  name: "textmenu",
  props: {
    // 标题
    x: {
      type: Number,
      default: 0,
    },
    y: {
      type: Number,
      default: 0,
    },
    win: {
      type: Object
    },
    hover: {
      type: Boolean,
      default: false,
    },
    hide: {
      type: Boolean,
      default: false,
    },
    w: {
      type: Number,
      default: 0,
    },
    h: {
      type: Number,
      default: 0,
    },
  },
  setup(props, ctx) {
    // 是否显示当前组件
    const isShow = ref(true);
    global.textmenuShow = true;
    if (props.hide) {
      isShow.value = false;
      global.textmenuShow = false;
    }
    let padding = 4;
    // 模态窗元素
    let textmenuRef = ref();
    // 模态窗对象
    let textmenuObj = ref({
      x: props.w ? props.x : props.x + padding,
      y: props.h ? props.y : props.y + padding,
      w: props.w,
      h: props.h,
      top: 0,
      left: 0,
      height: "",
    });
    // 菜单列表
    let menuList = ref(getGlobal("menuList")) || [];
    // 子菜单
    let submenuObj = ref({
      index: -1,
      list: [],
      timer: "",
      top: 0,
      left: 100,
      height: "",
    });

    calcWinWH(1);

    // 全局点击监听
    document.addEventListener("mouseup", clickListenerEvent);
    // 全局点击监听
    window.addEventListener("blur", clickListenerEvent);

    // 全局点击事件
    function clickListenerEvent() {
      isShow.value = false;
      global.textmenuShow = false;
      // 全局点击监听
      document.removeEventListener("mouseup", clickListenerEvent);
      // 全局失去焦点
      window.removeEventListener("blur", clickListenerEvent);
    }

    // 点击事件
    function clickEvent(ev, e, item, submenuObj) {
      // 左键才执行
      if (e.button == 0) {
        if (ev) {
          if (ev != "show") {
            ev(item, submenuObj);
            // 点击不隐藏
            if (!item.notHide) {
              isShow.value = false;
              global.textmenuShow = false;
            }
          }
        } else {
          isShow.value = false;
          global.textmenuShow = false;
        }
      }
    }

    // 鼠标移入菜单事件
    function mouseenter() {
      global.textmenuEnter = true;
    }

    // 鼠标离开菜单事件
    function mouseleave() {
      global.textmenuEnter = false;
      if (props.hover) {
        isShow.value = false;
        global.textmenuShow = false;
      }
    }

    // 鼠标悬浮事件-菜单
    function menuEnter(item, index) {
      submenuEnter();
      if (item.submenu && item.submenu.list && item.submenu.list.length > 0) {
        submenuObj.value.index = index;
        submenuObj.value.list = item.submenu.list;
        calcWinWH(2);
      } else {
        submenuObj.value.index = -1;
        submenuObj.value.list = [];
        submenuObj.value.top = 0;
        calcWinWH(1);
      }
    }

    // 鼠标离开事件-菜单
    function menuLeave() {
      submenuObj.value.timer = setTimeout(() => {
        submenuObj.value.index = -1;
        submenuObj.value.list = [];
        submenuObj.value.top = 0;
        calcWinWH(1);
      }, 200);
    }

    // 鼠标进入二级菜单
    function submenuEnter() {
      if (submenuObj.value.timer) {
        clearTimeout(submenuObj.value.timer);
        submenuObj.value.timer = "";
      }
    }

    // 计算窗口宽高-type-1菜单-二级菜单
    function calcWinWH(type) {
      nextTick(() => {
        if (textmenuRef.value) {
          let width = 0;
          let height = 0;
          switch (type) {
            case 1:
              width = document.querySelectorAll(".menu-ul")[0].clientWidth;
              height = 26 * menuList.value.length;
              submenuObj.value.top = 0;
              break;
            case 2:
              width = document.querySelectorAll(".menu-ul")[0].clientWidth + document.querySelectorAll(".menu-ul")[1].clientWidth;
              height = 26 * submenuObj.value.list.length;
              submenuObj.value.top = document.querySelectorAll(".menu-ul .hover")[0].offsetTop;
              submenuObj.value.left = document.querySelectorAll(".menu-ul")[0].clientWidth;
              break;
          }
          setWin({width: width, height: height, top: submenuObj.value.top, type: type});
        }
      })
    }

    // 移动窗口位置
    function setWin(param) {
      nextTick(() => {
        textmenuObj.value.left = 0;
        textmenuObj.value.top = 0;
        let rightFlag = false;
        let bottomFlag = false;
        let maxHFlag = false
        let paramX = textmenuObj.value.x;
        let paramY = textmenuObj.value.y;
        let paramW = textmenuObj.value.w;
        let paramH = textmenuObj.value.h;
        if (param.type) {
          // 计算右边界
          if (paramX + param.width > props.win.width) {
            paramX = paramX - param.width - (paramW || 3 * padding);
            rightFlag = true;
          }
          if (param.height > props.win.height - 16) {
            param.height = props.win.height - 16;
            maxHFlag = true;
          }
          // 计算下边界
          if (textmenuObj.value.y + param.height + param.top + 8 > props.win.height) {
            paramY = paramY - param.height + (paramH || -3 * padding);
            bottomFlag = true;
          }
          if (param.type == 1) {
            textmenuObj.value.x = paramX;
            textmenuObj.value.y = paramY;
            if (maxHFlag) {
              textmenuObj.value.height = param.height;
            } else {
              textmenuObj.value.height = "";
            }
          } else if (param.type == 2) {
            if (rightFlag) {
              submenuObj.value.left = -document.querySelectorAll(".menu-ul")[1].clientWidth;
            }
            if (bottomFlag) {
              submenuObj.value.top = -(param.height - (props.win.height - textmenuObj.value.y - 8));
            }
            if (maxHFlag) {
              submenuObj.value.height = param.height;
            } else {
              submenuObj.value.height = "";
            }
          }
        }
      });
    }

    return {
      textmenuRef,
      textmenuObj,
      menuList,
      submenuObj,
      isShow,

      clickEvent,
      mouseenter,
      mouseleave,
      menuEnter,
      menuLeave,
      submenuEnter,
      calcWinWH,
    }
  }
})
</script>
<style scoped lang="scss">
.textmenu {
  width: 100%;
  height: 100%;

  .menu-box {
    position: fixed;
    z-index: 300;

    .menu-ul {
      color: #333333;
      position: absolute;
      background: #FFFFFF;
      border: 1px solid #D8D8D8;
      border-radius: 4px;
      overflow-y: overlay;
      box-shadow: 0px 4px 8px 0px rgb(169 169 169 / 70%);

      .menu-li {
        width: 100%;
        max-width: 200px;
        position: relative;
        height: 26px;
        line-height: 26px;
        z-index: 1;
        padding: 0 30px 0 10px;
        white-space: nowrap;

        &.hover,
        &:hover {
          background: $styleBg1Hover;
        }

        &.sel {
          color: $styleColor;

          &:after {
            content: "";
            position: absolute;
            top: 7px;
            right: 10px;
            width: 8px;
            height: 4px;
            border-left: 2px solid $styleColor;
            border-bottom: 2px solid $styleColor;
            -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
          }
        }

        &.sub {
          &:after {
            content: "";
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-width: 4px 0 4px 4px;
            border-style: solid;
            border-color: transparent transparent transparent #999;
          }
        }
      }
    }
  }
}
</style>