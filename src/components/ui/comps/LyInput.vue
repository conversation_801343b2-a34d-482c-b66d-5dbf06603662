<template>
  <div class="default-input">
    <i class="file-ico"></i>
    <input
      :placeholder="placeholder"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      @blur="$emit('blur')"
      @focus="$emit('focus')"
    />
    <i class="close-ico" v-show="modelValue&&modelValue.length>0" @click="clearInput()"></i>
  </div>
</template>
<script>
export default {
  props: {
    modelValue: {
      default: "",
      type: String,
    },
    type: {
      default: "",
      type: String,
    },
    placeholder: {
      default: "",
      type: String,
    },
    searchFile: {
      type: Function
    },
  },
  setup(props, { emit }) {
    // 清空输入
    function clearInput(){
      emit('update:modelValue', '');
      if(props.searchFile){
        props.searchFile();
      }
    }
    return {
      clearInput,
    };
  },
};
</script>
<style lang="scss" scoped>
.default-input {
  position: relative;
  display: inline-block;
  input {
    border: 1px solid #E0E0E0;
    width: 200px;
    font-size: 12px;
    height: 26px;
    padding: 2px 8px 2px 28px;
    transition: 0.2s;
    border-radius: 4px;
  }
  input:focus {
    border: 1px solid #333333;
  }
  .file-ico {
    position: absolute;
    background: url("/img/search_icon_file.png") no-repeat;
    width: 14px;
    height: 15px;
    left: 8px;
    top: 5px;
    background-size: 17px 17px;
  }
  .close-ico{
    position: absolute;
    background: url("/img/search/close.png") no-repeat;
    width: 12px;
    height: 12px;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    background-size: 12px 12px;
    cursor: pointer;
  }
}
</style>