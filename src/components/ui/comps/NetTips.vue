<template>
  <div class="net-tips">
    <div v-show="netInfo.pingCount>=3||netInfo.errorCount>=3"
         :class="['net-tips-box',type==1?'login-tip-box':'']">
      <i :class="['net-tips-icon',type==1?'error':'']"></i>
      <span class="net-tips-text">
      {{ netInfo.pingCount >= 3 ? "网络异常，请检查您的网络状态" : netInfo.errorCount >= 3 ? "服务器异常，信息已上报，请稍后" : `您网络延迟为${netInfo.currentDelay}ms，系统可能会卡顿，请检查您的网络状态` }}
      </span>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";

export default {
  name: "NetTips",
  props: {
    // 类型-1登录
    type: {
      type: Number,
    },
  },
  setup(props, ctx) {
    const store = useStore();
    let jjsProxy = store.getters.getJJsProxy;
    // 获取当前的网络信息
    let netInfo = ref({
      pingCount: jjsProxy.pingCount,
      errorCount: jjsProxy.errorCount,
      currentDelay: jjsProxy.proxyCurrent.delay,
      maxDelay: jjsProxy.maxDelay
    });
    watch(() => store.state.jjsProxy.key,
      (newValue, oldValue) => {
        let jjsProxy = store.getters.getJJsProxy;
        netInfo.value.pingCount = jjsProxy.pingCount;
        netInfo.value.errorCount = jjsProxy.errorCount;
        netInfo.value.currentDelay = jjsProxy.proxyCurrent.delay;
        netInfo.value.maxDelay = jjsProxy.maxDelay;
      }, {
        deep: true
      }
    );
    return {
      netInfo
    }
  }
}
</script>
<style scoped lang="scss">
.net-tips {
  .net-tips-box {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 36px;
    font-size: 13px;
    line-height: 16px;
    color: #333333;
    background: #F9F5D5;
    z-index: 14;

    &.login-tip-box {
      top: auto;
      bottom: 0;
      height: 30px;
      font-size: 12px;
    }

    .net-tips-icon {
      width: 14px;
      height: 14px;
      margin-right: 8px;
      background: url("/img/login/tips.png") no-repeat;
      background-size: 100%;

      &.error {
        background: url("/img/login/error.png") no-repeat;
        background-size: 100%;
      }
    }
  }
}
</style>