<template>
  <div class="net-check">
    <!--发货验证码弹窗-->
    <LyDialog class="create-group-type-dialog" title="发货验证码" :width="312" :closeOnClickModal="false" :visible="true" v-show="netCheckObj.show"
              @close="netCheckObj.show=false" @confirm="uploadPcInfo()">
      <div class="input-box">
        <input ref="netCheckInputRef" type="text" placeholder="请输入发货验证码" maxlength="50" v-model="netCheckObj.code">
      </div>
      <div class="error-box selAll" v-show="netCheckObj.errorMsg">{{ netCheckObj.errorMsg }}</div>
    </LyDialog>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import LyDialog from "@comp/ui/comps/LyDialog";
import {uploadPcInfoApi} from "@utils/net/api";
import {toast, loading} from "@comp/ui";

export default {
  name: "NetCheck",
  components: {LyDialog},
  setup(props, ctx) {
    const store = useStore();
    let netCheckInputRef = ref();
    let netCheckObj = ref({
      show: false,// 是否显示发货验证码弹窗
      code: "",// 发货验证码
      errorMsg: "",// 错误提示
    });
    watch(() => store.state.emit.netCheck,
      (newValue, oldValue) => {
        netCheckObj.value.show = true;
        netCheckObj.value.code = "";
        netCheckObj.value.errorMsg = "";
        nextTick(() => {
          netCheckInputRef.value.focus();
        });
      }, {
        deep: true
      }
    );

    // 发货验证码上传电脑信息
    async function uploadPcInfo() {
      netCheckObj.value.errorMsg = "";
      loading();
      let secret = await store.dispatch("getSecretEnCrypt", {param: {}});
      let res = await uploadPcInfoApi({
        verificationCode: netCheckObj.value.code,
        computerInfo: secret
      });
      loading().hide();
      if (res.success) {
        toast({title: "提交成功！", type: 1});
        netCheckObj.value.show = false;
      }
      netCheckObj.value.errorMsg = res.errorMsg
    }

    return {
      netCheckInputRef,
      netCheckObj,

      uploadPcInfo,
    }
  }
}
</script>
<style scoped lang="scss">
.net-check {
  :deep(.ly-dialog .content) {
    padding: 16px;
  }

  .input-box {
    input {
      width: 100%;
      height: 30px;
      line-height: 28px;
      padding: 0 10px;
      border-radius: 4px;
      border: 1px solid #E0E0E0;

      &:focus {
        border: 1px solid #333333;
      }
    }
  }

  .error-box {
    color: $styleColor;
    margin-top: 8px;
    line-height: 17px;
  }
}
</style>