<template>
  <div ref="searchBoxRef" class="collect-search-modal win-drag" v-show="isShow" @mousedown="showComponents({type:1})">
    <div class="collect-search-box win-no-drag win-no-resize" @mousedown.stop="">
      <!--顶部标题-->
      <div class="collect-search-header">
        <span class="title">搜索</span>
        <img class="close" src="/img/close.png" alt="" @click="showComponents({type:1})">
      </div>
      <div class="collect-search-content">
        <div class="collect-search-input-box">
          <input ref="inputRef" type="text" v-model.trim="collectStoryObj.keyWord" placeholder="请输入关键字搜索" @input="searchInput">
          <i class="search-close" v-show="collectStoryObj.keyWord.length>0" @click="searchAll"></i>
        </div>
        <ul class="collect-search-tab">
          <li v-if="getCollectStoryPur()" :class="[collectSearchObj.key==-1?'sel':'']" @click="changeTab(-1)">全部</li>
          <li v-if="getCollectStoryPur()" :class="[collectSearchObj.key==0?'sel':'']" @click="changeTab(0)">公共话术</li>
          <li v-if="getCollectStoryPur()" :class="[collectSearchObj.key==1?'sel':'']" @click="changeTab(1)">我的话术</li>
          <li :class="[collectSearchObj.key==2?'sel':'']" @click="changeTab(2)">全部收藏</li>
        </ul>
        <div class="collect-search-details" v-if="collectStoryObj.keyWord.length==0||collectSearchObj.hasList">
          <div class="collect-search-none">
            <img src="/img/content_search.png"/>
            <div>{{ collectStoryObj.keyWord.length == 0 ? "请输入内容搜索" : "没有找到相关结果，换个条件试试吧~" }}</div>
          </div>
        </div>
        <div class="collect-search-details" v-else>
          <div v-if="collectSearchObj.key==-1">
            <div v-if="collectSearchObj.allObj.publicList.length>0" class="collect-search-details-box">
              <div class="collect-search-details-title">公共话术</div>
              <CollectMsg :type="type" :collectType="0" :collectStatus="collectSearchObj.loading" :collectList="collectSearchObj.allObj.publicList" :sessionInfo="sessionInfo"></CollectMsg>
            </div>
            <div v-if="collectSearchObj.allObj.selfList.length>0" class="collect-search-details-box">
              <div class="collect-search-details-title">我的话术</div>
              <CollectMsg :type="type" :collectType="1" :collectStatus="collectSearchObj.loading" :collectList="collectSearchObj.allObj.selfList" :sessionInfo="sessionInfo"></CollectMsg>
            </div>
            <div v-if="collectSearchObj.allObj.collectList.length>0" class="collect-search-details-box">
              <div class="collect-search-details-title">收藏</div>
              <CollectMsg :type="type" :collectType="2" :collectStatus="collectSearchObj.loading" :collectList="collectSearchObj.allObj.collectList" :sessionInfo="sessionInfo"></CollectMsg>
            </div>
            <div v-if="collectSearchObj.allObj.publicList.length==0&&collectSearchObj.allObj.selfList.length==0&&collectSearchObj.allObj.collectList.length==0" class="collect-search-none">
              <img src="/img/content_none.png"/>
              <div>暂无数据</div>
            </div>
          </div>
          <div v-else>
            <CollectMsg :type="type" :collectType="collectSearchObj.key" :collectStatus="collectSearchObj.loading" :collectList="collectSearchObj.list" :sessionInfo="sessionInfo"></CollectMsg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import CollectMsg from "@comp/chat/CollectMsg";
import {useStore} from "vuex";
import {alert, loading, toast} from "@comp/ui";
import {queryCollectStoryContentApi} from "@utils/net/api.js";
import {convertCollectMsg, debounce, deepClone, setJJSEvent} from "@utils";

export default {
  name: "CollectSearch",
  props: {
    type: {
      type: String,
      default: ""
    },
    isShow: {
      type: Boolean,
      default: false
    },
    showComponents: {
      type: Function
    },
    text: {
      type: String,
      default: ""
    },
    // 发送会话信息
    sessionInfo: {
      type: Object,
      default: {}
    },
  },
  components: {CollectMsg},
  setup(props, ctx) {
    const store = useStore();
    let searchBoxRef = ref();
    let inputRef = ref();
    let userInfo = store.getters.getUserInfo;
    let collectSearchObj = ref({
      key: -2,// 分类下标
      loading: false,// 搜索状态
      list: [],// 搜索的列表内容
      hasList: false,// 是否有搜索结果列表
      hasMore: false,// 是否有更多
      // 显示数据
      allObj: {
        publicList: [],// 公共话术列表
        selfList: [],// 我的话术
        collectList: [],// 我的收藏
      },
      // 原始数据
      originObj: {
        publicList: [],// 公共话术列表
        selfList: [],// 我的话术
        collectList: [],// 我的收藏
      }
    });
    // 搜索收藏/话术参数
    let collectStoryObj = ref({
      type: 0,// 0全部1公共2私有3收藏
      gid: "",// 分组id
      pageNumber: 1,// 查询页数
      pageSize: 20,// 查询页面数量
      groupId: "",// 分组id
      showType: "",// 1图片
      keyWord: "",// 搜索关键词
    });
    watch(() => props.text,
      (newValue, oldValue) => {
        collectStoryObj.value.keyWord = newValue;
      }, {
        deep: true
      }
    );
    watch(() => props.isShow,
      (newValue, oldValue) => {
        if (!newValue) {
          collectStoryObj.value.keyWord = "";
        }
        // 显示搜索窗口初始化数据
        if (getCollectStoryPur()) {
          collectSearchObj.value.key = -1;
        } else {
          changeTab(2);
        }
        collectSearchObj.value.list = [];
        collectSearchObj.value.allObj.publicList = [];
        collectSearchObj.value.allObj.selfList = [];
        collectSearchObj.value.allObj.collectList = [];
        nextTick(() => {
          inputRef.value.focus();
          searchInput();
        });
      }, {
        deep: true
      }
    );

    // 更新我的话术/收藏数据
    watch(() => store.state.emit.saveCollectItem,
      (newValue, oldValue) => {
        if (newValue) {
          let thisItem = deepClone(newValue);
          let thisList = collectSearchObj.value.key == 2 ? collectSearchObj.value.allObj.collectList : collectSearchObj.value.allObj.selfList;
          let collectIndex = thisList.findIndex(item => {return thisItem.id == item.collectId})
          if (collectIndex > -1) {
            // 替换消息数据
            if (collectSearchObj.value.key == 2) {
              collectSearchObj.value.originObj.collectList[collectIndex].oHtml = newValue.oHtml;
              collectSearchObj.value.originObj.collectList[collectIndex].oText = newValue.oText;
              collectSearchObj.value.originObj.collectList[collectIndex].title = newValue.title;
              collectSearchObj.value.originObj.collectList[collectIndex].custom = newValue.custom;
              collectSearchObj.value.originObj.collectList[collectIndex].content = newValue.content;
              collectSearchObj.value.originObj.collectList[collectIndex].type = newValue.type;
              if (newValue.type == "text") {
                delete collectSearchObj.value.originObj.collectList[collectIndex].msgJson;
              }
              let thisCollectList = convertCollectMsg([deepClone(collectSearchObj.value.originObj.collectList[collectIndex])]);
              let p = [];
              thisCollectList.map(item => {
                p.push(store.dispatch("setMsgField", {item: item}));
              });
              Promise.all(p).then(res => {
                remote.store.dispatch("getPersons", {doneFlag: true, account: thisCollectList.map(item => {return item.from})}).then(personInfo => {
                  collectSearchObj.value.allObj.collectList.splice(collectIndex, 1, thisCollectList[0]);
                })
              });
            } else {
              collectSearchObj.value.originObj.selfList[collectIndex].speechContent = newValue.speechContent;
              let thisSelfList = [{collectId: thisItem.id, parentId: thisItem.parentId, from: userInfo.workerNo, type: "custom", content: {type: "multi", msgs: thisItem.speechContent}}];
              let p = [];
              thisSelfList.map(item => {
                p.push(store.dispatch("setMsgField", {item: item}));
              });
              Promise.all(p).then(res => {
                remote.store.dispatch("getPersons", {doneFlag: true, account: thisSelfList.map(item => {return item.from})}).then(personInfo => {
                  collectSearchObj.value.allObj.selfList.splice(collectIndex, 1, thisSelfList[0]);
                })
              });
            }
          }
        }
      }, {
        deep: true
      }
    );

    // 切换搜索分类
    function changeTab(key) {
      collectSearchObj.value.key = key;
      if (collectStoryObj.value.keyWord) {
        queryCollectStoryContent();
      }
    }

    // 切换显示列表
    function changeShowList() {
      switch (collectSearchObj.value.key) {
        case 0:
          collectSearchObj.value.list = collectSearchObj.value.allObj.publicList;
          break;
        case 1:
          collectSearchObj.value.list = collectSearchObj.value.allObj.selfList;
          break;
        case 2:
          collectSearchObj.value.list = collectSearchObj.value.allObj.collectList;
          break;
      }
    }

    // 查询话术分组内容
    async function queryCollectStoryContent() {
      if (!props.isShow) {
        return;
      }
      if (collectStoryObj.value.pageNumber == 1) {
        collectSearchObj.value.loading = true;
      }
      loading();
      collectStoryObj.value.type = collectSearchObj.value.key + 1;
      let res = await queryCollectStoryContentApi({
        msgBody: JSON.stringify(collectStoryObj.value),
      });
      if (res.success) {
        try {
          let publicList = [];// 公共话术列表
          let selfList = [];// 我的话术
          let collectList = [];// 我的收藏
          switch (collectStoryObj.value.type) {
            case 0:
              publicList = res.data.data.public;
              selfList = res.data.data.private;
              collectList = res.data.data.collect;
              break;
            case 1:
              publicList = res.data.data.list;
              break;
            case 2:
              selfList = res.data.data.list;
              break;
            case 3:
              collectList = res.data.data;
              break;
          }
          // 设置原始数据
          collectSearchObj.value.originObj.publicList = deepClone(publicList);
          collectSearchObj.value.originObj.selfList = deepClone(selfList);
          collectSearchObj.value.originObj.collectList = deepClone(collectList);
          // 处理显示数据
          let thisPublicList = publicList.map(item => {
            return {collectId: item.id, ...item}
          });
          let thisSelfList = selfList.map(item => {
            return {collectId: item.id, parentId: item.parentId, from: userInfo.workerNo, type: "custom", content: {type: "multi", msgs: item.speechContent}}
          });
          let thisCollectList = convertCollectMsg(collectList);
          let p = [];
          thisSelfList.map(item => {
            p.push(store.dispatch("setMsgField", {item: item}));
          });
          thisCollectList.map(item => {
            p.push(store.dispatch("setMsgField", {item: item}));
          });
          Promise.all(p).then(res => {
            remote.store.dispatch("getPersons", {doneFlag: true, account: thisSelfList.concat(thisCollectList).map(item => {return item.from})}).then(personInfo => {
              loading().hide();
              collectSearchObj.value.allObj.publicList = thisPublicList;
              collectSearchObj.value.allObj.selfList = thisSelfList;
              collectSearchObj.value.allObj.collectList = thisCollectList;
              collectSearchObj.value.loading = false;
              changeShowList();
            })
          });
          setJJSEvent("P28648704", JSON.stringify({
            type: collectSearchObj.value.key + 1,
            aid: collectSearchObj.value.originObj.publicList.length + collectSearchObj.value.originObj.selfList.length + collectSearchObj.value.originObj.collectList.length,
            keywords: collectStoryObj.value.keyWord,
            workerId: userInfo.workerId
          }));
        } catch (e) {
          loading().hide();
          collectSearchObj.value.loading = false;
        }
      } else {
        loading().hide();
        collectSearchObj.value.loading = false;
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 搜索
    function searchInput() {
      debounce({
        timerName: "doSearch",
        time: 500,
        fnName: () => {
          if (collectStoryObj.value.keyWord) {
            queryCollectStoryContent();
          } else {
            collectSearchObj.value.list = [];
            collectSearchObj.value.allObj.publicList = [];
            collectSearchObj.value.allObj.selfList = [];
            collectSearchObj.value.allObj.collectList = [];
          }
        }
      });
    }

    function searchAll() {
      collectStoryObj.value.keyWord = "";
      queryCollectStoryContent();
    }

    // 获取是否有收藏话术权限
    function getCollectStoryPur() {
      return store.getters.getCollectStoryPur;
    }

    return {
      searchBoxRef,
      inputRef,
      collectSearchObj,
      collectStoryObj,

      changeTab,
      searchInput,
      searchAll,
      getCollectStoryPur,
    }
  }
}
</script>
<style scoped lang="scss">
.collect-search-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 89;

  .collect-search-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 640px;
    height: 520px;
    background: #FFFFFF;
    box-shadow: 0px 6px 12px 0px rgba(0, 0, 0, 0.2);
    border: 1px solid #D8D8D8;
    border-radius: 6px;

    .collect-search-header {
      position: relative;
      display: flex;
      align-items: center;
      height: 30px;
      padding: 0 16px;
      background: #F0F0F0;
      font-weight: 700;
      border-radius: 6px 6px 0px 0px;
      font-size: 13px;

      .intr {
        color: #666666;
        margin-right: 5px;
      }

      .close {
        position: absolute;
        top: 50%;
        right: 12px;
        transform: translateY(-50%);
        width: 20px;
        cursor: pointer;
        padding: 4px;
      }
    }

    .collect-search-content {
      border-radius: 0px 0px 6px 6px;

      .collect-search-input-box {
        display: flex;
        align-items: center;
        position: relative;
        height: 30px;
        line-height: 30px;
        margin: 12px 16px 6px;
        padding: 0 10px 0 26px;
        border-radius: 4px;
        border: 1px solid #333333;
        background-image: url("/img/search/icon_search.png");
        background-repeat: no-repeat;
        background-size: 16px 16px;
        background-position: 7px center;

        input {
          flex: 1;
        }

        .search-close {
          width: 16px;
          height: 100%;
          flex-shrink: 0;
          background: url("/img/search/close.png") center no-repeat;
          background-size: 12px;
          cursor: pointer;
        }
      }

      .collect-search-tab {
        display: flex;
        padding: 0 10px;
        border-bottom: 1px solid #D8D8D8;

        li {
          position: relative;
          padding: 6px 10px;
          cursor: pointer;
          color: #666666;

          &:hover {
            color: $styleColor;
          }

          &.sel {
            color: $styleColor;
            font-weight: bold;

            &:after {
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              content: "";
              width: 12px;
              height: 3px;
              background: $styleColor;
            }
          }
        }
      }

      .collect-search-details {
        height: calc(520px - 110px);
        overflow-y: auto;

        ::v-deep(.collect-msg) {
          height: auto !important;
        }


        .collect-search-none {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #666666;

          img {
            width: 160px;
            margin: 100px 0 10px;
          }
        }

        .collect-search-details-title {
          font-size: 13px;
          padding: 12px 16px 2px;
          color: #333333;
          font-weight: bold;
        }
      }
    }
  }
}
</style>