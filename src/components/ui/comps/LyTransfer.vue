<template>
  <!-- 穿梭框,固定高宽 -->
  <div class="default-transfer">
    <div class="transfer-left">
      <header>
        <template v-if="newProps.hasSearch">
          <i></i>
          <input
              type="text"
              v-model="qkey"
              @input="searchInput"
              placeholder="请输入搜索关键字"
          />
        </template>
      </header>
      <div class="main">
        <ul v-if="supplyList.length > 0">
          <li
              v-for="item in supplyList"
              :key="item[newProps.value]"
              @click="setChecked(item,$event)"
          >
            <input :disabled="notDelete?.indexOf(item[newProps.value]) >= 0" :checked="item.checked" type="checkbox"
                   class="check-box"/>
            <div class="creat-li-img">
              <img
                  :src="item.avatar||item[newProps.headPic]"
                  :onerror="avatarError.bind(this, 'p2p', item[newProps.value], '')"
              />
            </div>
            <p>{{ item[newProps.label] }}</p>
          </li>
        </ul>
        <div class="flex-center no-data" v-else>暂无人员数据</div>
      </div>
    </div>
    <div class="transfer-right">
      <header>
        <slot name="right-header"></slot>
      </header>
      <div class="main">
        <ul>
          <li class="flex flex-bet" v-for="item in choiceData" :key="item[newProps.value]">
            <div class="flex flex-vcenter">
              <div class="creat-li-img">
                <img
                    :src="item.avatar||item[newProps.headPic]"
                    :onerror="avatarError.bind(this, 'p2p', item[newProps.value], '')"
                />
              </div>
              <p>{{ item[newProps.label] }}</p>
            </div>
            <i
                class="to-create-close1"
                v-if="notDelete?.indexOf(item[newProps.value]) === -1"
                @click="toRemove(item)"
            ></i>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import {computed, nextTick, ref} from "vue";
import {toast} from "@comp/ui";
import {debounce, avatarError, deepClone} from "@utils";

export default {
  props: {
    // 供货数据
    supplyData: {
      type: Array,
      default: [],
    },
    // 选择的数据
    choiceData: {
      type: Array,
      default: [],
    },
    // 群已经存在的数据，用于不可被选中
    baseData: {
      type: Array,
      default: [],
    },
    // 不可删除的数据,id的数组
    notDelete: {
      type: Array,
      default: [],
    },
    // 配置项
    defaultProps: {
      type: Object,
      default: {
        value: "id",
        label: "name",
        headPic: "headPic",
        hasSearch: false, //如果为true，func函数必传
        func: function () {
        },
        maxLength: 0, //可选的最大长度
        maxThenToast: "不允许大于0人",
      },
    },
  },
  emits: ["toChoice", "toRemove"],
  setup(props, {emit}) {
    let qkey = ref("");
    let t_list = ref([]); //搜索到的数据
    //配置项重置
    const newProps = computed(() => {
      return {
        value: props.defaultProps.value || "id",
        label: props.defaultProps.label || "name",
        headPic: props.defaultProps.headPic || "headPic",
        hasSearch: props.defaultProps.hasSearch || false,
        func: props.defaultProps.func || function () {
        },
        maxLength: props.defaultProps.maxLength || 0,
        maxThenToast: props.defaultProps.maxThenToast || "不允许大于0人",
      };
    });

    //选中的idlist
    const choiceDataIdList = computed(() => {
      let choice = props.choiceData.map((i) => {
        return i[newProps.value.value];
      });
      let base = props.baseData.map((i) => {
        return i[newProps.value.value];
      });
      return choice.concat(base);
    });

    //点击选中
    function toChoice(item) {
      if (
          newProps.value.maxLength !== 0 &&
          choiceDataIdList.value.length >= newProps.value.maxLength
      ) {
        toast({
          title: newProps.value.maxThenToast,
          type: 2,
        });
        return;
      }
      if (choiceDataIdList.value.indexOf(item[newProps.value.value]) > -1) {
        toast({
          title: item[newProps.value.label] + "已在列表中!",
          type: 2,
        });
      } else {
        emit("toChoice", item);
      }
    }

    //点击删除
    function toRemove(item) {
      let t_index = choiceDataIdList.value.indexOf(item[newProps.value.value]);
      if (t_index > -1) {
        emit("toRemove", t_index);
      } else {
        toast({
          title: item[newProps.value.label] + "已删除!",
          type: 2,
        });
      }
    }

    //搜索-防抖
    function searchInput(e) {
      debounce({
        timerName: "doTransferSearch",
        time: 500,
        e: e,
        fnName: newProps.value.func.bind(this, qkey.value),
      });
    }

    //渲染列表
    const supplyList = computed(() => {
      let list = props.supplyData.map((item) => {
        let data = deepClone(item)
        data.checked = isChecked(data)
        return {
          ...data
        }
      })
      return list
    })

    //是否选中
    function isChecked(item) {
      let flag = false
      if (choiceDataIdList.value.indexOf(item[newProps.value.value]) > -1) flag = true
      return flag
    }

    function setChecked(item, event) {
      //notDelete取props传过来
      if (props.notDelete.indexOf(item[newProps.value.value]) >= 0) return false;
      if (isChecked(item)) {
        toRemove(item)
      } else {
        //超过数量不添加
        if (
            newProps.value.maxLength !== 0 &&
            choiceDataIdList.value.length >= newProps.value.maxLength
        ) {
          toast({
            title: newProps.value.maxThenToast,
            type: 2,
          });
          nextTick(() => {
            event.target.checked = false
          })
          return false;
        }
        toChoice(item)
      }
    }

    return {
      avatarError,
      t_list,
      toChoice,
      choiceDataIdList,
      toRemove,
      newProps,
      searchInput,
      qkey,

      supplyList,
      isChecked,
      setChecked
    };
  },
};
</script>
<style lang="scss" scoped>
.no-data {
  height: 100%;
  color: #666666;
}

.default-transfer {
  height: 331px;
  overflow: hidden;
  display: flex;
  border-radius: 4px;
  border: 1px solid #E0E0E0;
  justify-content: space-between;


  .transfer-left,
  .transfer-right {
    flex: 1;
    display: flex;
    flex-direction: column;


    header {
      width: 100%;
      height: 46px;
      padding: 16px 16px 0px 16px;
    }

    .main {
      flex: 1;
      width: 100%;
      overflow: auto;
      margin: 6px 0px;

      ul {
        li {
          display: flex;
          cursor: pointer;
          align-items: center;
          padding: 5px 16px;

          &:hover {
            background: $styleBg1Hover;
          }

          .check-box {
            width: 14px;
            height: 14px;
          }

          .creat-li-img {
            width: 20px;
            height: 20px;
            overflow: hidden;
            margin-right: 6px;
            border-radius: 50%;


            img {
              display: block;
              width: 20px;
              image-rendering: -webkit-optimize-contrast;
            }
          }

          p {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 180px;
          }
        }
      }
    }
  }

  .transfer-left {
    border-right: 1px solid #E0E0E0;

    header {
      position: relative;

      i {
        background-image: url("/img/mail/search.png");
        background-repeat: no-repeat;
        top: calc(50% + 10px);
        transform: translateY(-50%);
        width: 16px;
        height: 16px;
        position: absolute;
        left: 25px;
        background-size: 16px 16px;
      }

      input {
        height: 30px;
        width: 100%;
        background: #FFFFFF;
        border-radius: 4px;
        border: #3333 1px solid;
        box-sizing: border-box;
        padding: 7px 10px;
        padding-left: 26px;

        &:focus {
          border: #666666 1px solid;
        }

        &:hover {
          border: #666666 1px solid;
        }
      }

      input::-webkit-input-placeholder {
        color: #999999;
        font-size: 12px;
        line-height: 17px;
      }
    }
  }

  .transfer-right {
    li {
      position: relative;

      &:hover i {
        display: block;
      }

      i {
        display: none;
        background: url(/img/close.png);
        background-size: 10px 10px;
        height: 10px;
        width: 10px;
        cursor: pointer;
      }
    }
  }
}
</style>