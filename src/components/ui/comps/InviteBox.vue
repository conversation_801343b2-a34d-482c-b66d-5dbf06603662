<template>
  <div class="invite-box">
    <!--讨论组类型弹窗-->
    <LyDialog class="create-group-type-dialog" title="创建讨论组" :width="422" :closeOnClickModal="false" :visible="inviteObj.type==1"
              @close="dialogOperate(1,1)" @confirm="dialogOperate(1,2)">
      <ul :class="['create-group-type','create-group-type-'+inviteObj.typeList.length]">
        <li v-for="(item,key) in inviteObj.typeList" @click="selType(item.value)" :class="{ sel: inviteObj.groupType == item.value }">
          <i class="radio-i"></i>
          <span>{{ item.label }}</span>
        </li>
      </ul>
    </LyDialog>
    <!--拉人列表-->
    <LyDialog class="create-user-dialog" :title="inviteObj.dialogTitle" :width="592" @close="dialogOperate(2,1)" @confirm="dialogOperate(2,2)"
              :visible="inviteObj.type!=-1&&inviteObj.type!=1">
      <div :class="['create-user',inviteObj.type!=5?'classify-'+inviteObj.classifyType:'']" ref="createUserRef">
        <div class="user-left-box">
          <div class="search-box">
            <input type="text" :placeholder="inviteObj.type==2?'请输入人员名称':'搜索'" maxlength="50" v-model="inviteObj.searchText" @input="doSearch()">
            <span class="icon-close" v-show="inviteObj.searchText" @click="doSearch(true)"></span>
          </div>
          <div class="tab-box" v-if="inviteObj.classifyType==4&&inviteObj.type!=5">
            <div class="tab-title">筛选：</div>
            <ul class="tab-ul">
              <li :class="['tab-li',inviteObj.customerType==1?'sel':'']" @click="changeCustomerType(1)">全部</li>
              <li :class="['tab-li',inviteObj.customerType==2?'sel':'']" @click="changeCustomerType(2)">未分组客户</li>
            </ul>
          </div>
          <ul class="sel-user-ul">
            <li v-show="!inviteObj.searchFlag" v-for="(item,key) in inviteObj.searchList" :key="inviteObj.isTeamLabel?item.teamId:item.workerNo" @click="selItem(item)">
              <i :class="['sel-box-i',inviteObj.selMap[inviteObj.isTeamLabel?item.teamId:item.workerNo]?'sel':'',inviteObj.personMap[inviteObj.isTeamLabel?item.teamId:item.workerNo]?'disabled':'']"></i>
              <div class="user-avatar-box">
                <div class="avatar-box">
                  <img class="avatar" :src="item.avatar" :onerror="avatarError.bind(this, inviteObj.isTeamLabel?'team':'p2p', inviteObj.isTeamLabel?item.teamId:item.workerNo, '')">
                </div>
              </div>
              <span class="textEls">{{ item.userShowName || item.name }}</span>
            </li>
            <li v-show="inviteObj.searchText&&!inviteObj.searchFlag&&inviteObj.searchList.length==0" class="user-none">暂无{{ inviteObj.type == 2 ? '人员' : '' }}数据</li>
            <li v-show="inviteObj.searchFlag" class="user-none">
              <img class="loading-img" src="/img/waitting.gif" alt="">
            </li>
          </ul>
        </div>
        <div class="user-right-box">
          <div class="tips-box" v-show="inviteObj.showTips">{{ inviteObj.showTips }}</div>
          <div :class="['sel-user-tips',!inviteObj.showTips?'show':'']">
            <span class="sel-user-text">
              <span>已选择</span><span class="highlight">{{ calcSelNum(1) }}</span><span>{{ inviteObj.type != 3 ? "人" : "个会话" }}</span>
            </span>
            <span v-if="!inviteObj.isCalcDefault&&!inviteObj.isTeamLabel" class="sel-user-text">
              <span>{{ inviteObj.type == 2 ? "群" : "标签" }}成员</span><span class="highlight">{{ calcSelNum(2) }}</span><span>人</span>
            </span>
          </div>
          <ul :class="['sel-user-ul',!inviteObj.showTips?'sel-user-ul-show':'']">
            <!--默认列表，创建的时候显示-->
            <li v-if="inviteObj.isCalcDefault||(inviteObj.type==2&&!inviteObj.sessionInfo.id)" v-for="(item,key) in inviteObj.personMap" :key="item.workerNo">
              <div class="user-avatar-box">
                <div class="avatar-box">
                  <img class="avatar" :src="item.avatar" :onerror="avatarError.bind(this, 'p2p', item.workerNo, '')">
                </div>
              </div>
              <span class="textEls">{{ item.userShowName || item.name }}</span>
            </li>
            <!--选择列表-->
            <li class="close-li" v-for="(item,key) in inviteObj.selMap" :key="item.workerNo">
              <div class="user-avatar-box">
                <div class="avatar-box">
                  <img class="avatar" :src="item.avatar" :onerror="avatarError.bind(this, 'p2p', item.workerNo, '')">
                </div>
              </div>
              <span class="textEls">{{ item.userShowName || item.name }}</span>
              <i class="close" @click="selItem(item)"></i>
            </li>
          </ul>
        </div>
      </div>
      <div v-show="inviteObj.inviteTips" class="create-tips">{{ inviteObj.inviteTips }}</div>
    </LyDialog>
  </div>
</template>
<script>
import {nextTick, ref, watch} from "vue";
import {useStore} from "vuex";
import LyDialog from "@comp/ui/comps/LyDialog";
import {debounce, avatarError, deepClone, regReplace, getPersons, setUserBaseInfo} from "@utils";
import {searchUsersApi, findIMEmpByAuthApi, createGroupApi, getSystemPerpleEmpApi, comeInAndGoOutGroupApi,} from "@utils/net/api.js";
import {toast, loading, alert} from "@comp/ui";

export default {
  name: "InviteBox",
  components: {LyDialog},
  setup(props, ctx) {
    const store = useStore();
    let config = store.getters.getConfig.config;
    let userInfo = store.getters.getUserInfo;
    let createUserRef = ref();
    // 邀请对象
    let inviteObj = ref({
      type: -1,// 1创建讨论组2群邀请好友3分组添加会话4分组添加群/成员5分组移出成员
      classifyType: 2,//type为3和4的时候存在，1群2讨论组3同事4客户
      sessionInfo: {},// 拉人进群信息
      teamMembers: [],// 群成员列表
      groupType: 0,// 选择的讨论组类型
      selGroupType: 0,// 确认选择的讨论组类型
      typeList: store.getters.getGroupTypeList,// 讨论组列表
      personMap: {},// 创建默认人员列表（不可删除）
      itemMap: {},// 添加会话默认标签列表
      searchText: "",// 搜索人员关键词
      searchList: [],// 搜索列表
      searchFlag: false,// 搜索加载状态
      searchDefaultList: store.getters.getFriends,// 搜索默认列表（通讯录好友）
      searchDefaultGroupList: [],// 搜索群
      selMap: {},// 选择人员列表
      showTips: "",// 创建讨论组提示
      inviteTips: "",// 讨论组创建/邀请提示
      classifyUUID: "",// 讨论组分组uuid
      classifyName: "",// 讨论组分组名
      classifyValue: "",// 分组值
      done: "", // 操作完成的回调
      notSendCustomSysMsg: false,// 是否不发送自定义消息
      showList: [],// 显示的删除列表
      dialogTitle: "",// 显示弹窗名
      isTeamLabel: false,// 是否群标签
      isCalcDefault: true,// 是否统计默认成员
      customerType: 1,// 客户筛选~1全部2未分组客户
    });

    // 讨论组类型列表
    watch(() => store.state.groupTypeList,
      (newValue, oldValue) => {
        inviteObj.value.typeList = newValue;
      }, {
        deep: true
      }
    );
    // 通讯录好友列表
    watch(() => store.state.friends,
      (newValue, oldValue) => {
        inviteObj.value.searchDefaultList = newValue;
        if (!inviteObj.value.searchText) {
          inviteObj.value.searchList = deepClone(newValue);
        }
      }, {
        deep: true
      }
    );
    watch(() => store.state.userInfo,
      (newValue, oldValue) => {
        userInfo = store.getters.getUserInfo;
      }, {
        deep: true
      }
    );

    // 初始化弹窗对象
    function inviteBoxInit(param) {
      initParam(param);
      switch (inviteObj.value.type) {
        case 1:
          // 1创建讨论组
          addGroup();
          break;
        case 2:
          // 2群邀请好友
          inviteGroup();
          break;
        case 3:
          // 3讨论组分组添加讨论组会话
          classifyAddGroup();
          break;
        case 4:
        case 5:
          // 分组添加/移除成员
          classifyAddLabel();
          break;
      }
    }

    // 初始化弹窗参数
    function initParam(param) {
      inviteObj.value.type = param.type;
      inviteObj.value.classifyType = Number(param.classifyType);
      inviteObj.value.selMap = {};
      inviteObj.value.personMap = {};
      inviteObj.value.itemMap = param.itemMap || {};
      inviteObj.value.selGroupType = param?.sessionInfo?.detailInfo?.groupType || 0;
      inviteObj.value.sessionInfo = param?.sessionInfo || {};
      inviteObj.value.teamMembers = param?.teamMembers || [];
      inviteObj.value.classifyUUID = param?.uuid || "";
      inviteObj.value.classifyName = param?.name || "";
      inviteObj.value.classifyValue = param?.value || "";
      inviteObj.value.inviteTips = "";
      inviteObj.value.showTips = "";
      inviteObj.value.searchDefaultGroupList = store.getters.getTeams({type: 5});
      inviteObj.value.notSendCustomSysMsg = param.notSendCustomSysMsg || false;
      inviteObj.value.done = param.done;
      inviteObj.value.showList = param.showList || [];
      inviteObj.value.searchList = [];
      inviteObj.value.isTeamLabel = inviteObj.value.classifyType == 1 || inviteObj.value.classifyType == 2;
      inviteObj.value.customerType = 1;
      if (inviteObj.value.type != 5) {
        inviteObj.value.isCalcDefault = false;
      } else {
        inviteObj.value.isCalcDefault = true;
      }
      let dialogTitle = "";
      switch (inviteObj.value.type) {
        case 1:
          dialogTitle = "创建讨论组";
          break;
        case 2:
          dialogTitle = "邀请好友";
          break;
        case 3:
          dialogTitle = "添加" + (inviteObj.value.isTeamLabel ? "讨论组/群" : inviteObj.value.classifyType == 3 ? "同事" : inviteObj.value.classifyType == 4 ? "客户" : "");
          break;
        case 4:
          dialogTitle = "添加标签人员";
          break;
        case 5:
          dialogTitle = "移除标签人员";
          break;
      }
      // 操作同事标签判断权限
      if (inviteObj.value.classifyType == 3 && (inviteObj.value.type == 3 || inviteObj.value.type == 4)) {
        store.dispatch("getForwardPur");
      }
      inviteObj.value.dialogTitle = dialogTitle;
    }

    // 创建讨论组弹窗判断
    function addGroup() {
      // 每次打开重新获取讨论组列表
      store.dispatch("setGroupTypeList");
      if (inviteObj.value.typeList.length == 1) {
        // 打开创建讨论组选择人员
        selShowType(2);
      } else {
        // 选择创建讨论组类型
        selShowType(1);
      }
      // 设置默认成员
      setPersonMap();
    }

    // 邀请好友
    function inviteGroup() {
      selShowType(2);
      // 设置默认成员
      setPersonMap();
    }

    // 讨论组分组添加讨论组会话
    function classifyAddGroup() {
      selShowType(3);
    }

    // 讨论组分组添加讨论组会话
    function classifyAddLabel() {
      selShowType(inviteObj.value.type);
      // 移除成员默认列表为showList
      if (inviteObj.value.type != 5) {
        // 设置默认成员
        setPersonMap();
      }
    }

    // 设置默认成员
    function setPersonMap(defaultList) {
      inviteObj.value.personMap = {};
      // 默认群列表有自己
      if (inviteObj.value.type == 1 || inviteObj.value.type == 2) {
        inviteObj.value.personMap[userInfo.workerNo] = userInfo;
      }
      // 已在群成员列表
      (inviteObj.value.teamMembers || []).map(item => {
        inviteObj.value.personMap[item.account] = getPersons(store.state, item.account);
      });
      // 设置群类型默认列表
      (defaultList || []).map(item => {
        inviteObj.value.personMap[item.workerNo] = getPersons(store.state, item.workerNo);
      });
      store.dispatch("getPersons", {doneFlag: true, account: Object.keys(inviteObj.value.personMap)}).then(personInfo => {
        for (let key in inviteObj.value.personMap) {
          inviteObj.value.personMap[key] = getPersons(store.state, inviteObj.value.personMap[key].workerNo);
        }
      });
    }

    // 选择讨论组类型
    function selType(val) {
      inviteObj.value.groupType = val;
    }

    // 显示弹窗类型
    function selShowType(type) {
      inviteObj.value.type = type;
      doSearch();
      if (type == 2) {
        let inviteTips = "";
        if (inviteObj.value.selGroupType != 0) {
          if (inviteObj.value.sessionInfo.id) {
            inviteTips = "邀请成功后，需要被邀人至【群通知】同意后，才会成功加入";
          }
        } else {
          if (config.jjrDutyNumbers.indexOf(userInfo.dutyNumber) != -1) {
            inviteTips = "你只能拉自己分行的人员进讨论组，可用二维码邀请他人入讨论组";
          } else if (config.gkfwDutyNumbers.indexOf(userInfo.dutyNumber) != -1) {
            inviteTips = "你只能拉自己管辖范围内的人以及自己直接上级入讨论组";
          }
        }
        inviteObj.value.inviteTips = inviteTips;
      } else if (type == 3 && inviteObj.value.isTeamLabel) {
        inviteObj.value.showTips = "群助手、客户咨询、售后系统中的讨论组，将无法在分组中收发信息";
      }
    }

    // 显示弹窗 type1讨论组类型2选择人员 key1关闭2确认
    async function dialogOperate(type, key) {
      switch (type) {
        case 1:
          if (key == 1) {
            inviteObj.value.selGroupType = inviteObj.value.groupType;
            selShowType(-1);
          } else {
            if (inviteObj.value.groupType != 0) {
              // 查询默认添加人员
              loading();
              let res = await getSystemPerpleEmpApi({
                msgBody: JSON.stringify({groupType: inviteObj.value.groupType})
              });
              loading().hide();
              if (!res.success) {
                toast({title: res.errorMsg || "系统错误", type: 2});
                return;
              }
              if (res?.data?.accidList) {
                // 设置默认成员
                setPersonMap(res.data.accidList);
              }
              inviteObj.value.showTips = res?.data?.tips || "";
            } else {
              inviteObj.value.showTips = "";
            }
            inviteObj.value.selGroupType = inviteObj.value.groupType;
            // 打开创建讨论组选择人员
            selShowType(2);
          }
          // 重置默认选项
          selType(0);
          break;
        case 2:
          if (key == 1) {
            inviteObj.value.selGroupType = 0;
            selShowType(-1);
            // 重置搜索内容
            inviteObj.value.searchText = "";
            inviteObj.value.searchList = [];
            doSearch();
          } else {
            // 选择人员确认创建
            switch (inviteObj.value.type) {
              case 2:
                if (inviteObj.value.sessionInfo.id) {
                  // 邀请好友
                  inviteGroupConfirm();
                } else {
                  // 创建讨论组
                  createGroupConfirm();
                }
                break;
              case 3:
                classifyAddGroupConfirm();
                break;
              case 4:
              case 5:
                classifyAddLabelConfirm();
                break;
            }
          }
          break;
      }
    }

    // 搜索人员
    function doSearch(reset) {
      if (reset) {
        inviteObj.value.searchText = "";
      }
      let val = inviteObj.value.searchText || "";
      // 1创建讨论组2群邀请好友3分组添加会话4分组添加群/成员5分组移出成员
      switch (inviteObj.value.type) {
        case 1:
        case 2:
          // 查询讨论组/群
          doSearchMethods(3, val);
          break;
        case 3:
        case 4:
          // 搜索人员2讨论组4客户默认同事
          switch (inviteObj.value.classifyType) {
            case 1:
            case 2:
            case 4:
              doSearchMethods(inviteObj.value.classifyType, val);
              break;
            default:
              doSearchMethods(3, val);
              break;
          }
          break;
        case 5:
          // 搜索显示列表
          let showList = deepClone(inviteObj.value.showList);
          let valReg = new RegExp(regReplace(val), "i");
          inviteObj.value.searchList = showList.filter(item => {return valReg.test(item.account) || valReg.test(item.name) || valReg.test(item.workerSpell)});
          break;
      }
    }

    // 搜索key-2讨论组-3同事-4客户
    function doSearchMethods(key, val) {
      switch (key) {
        case 1:
        case 2:
          // 搜索群
          let list = deepClone(inviteObj.value.searchDefaultGroupList);
          let resList = [];
          if (!val) {
            inviteObj.value.searchList = list;
            return;
          }
          list.map(item => {
            if (new RegExp(regReplace(val), "i").test(item.name)) {
              resList.push(item);
            }
          });
          inviteObj.value.searchList = resList;
          nextTick(() => {
            createUserRef.value.querySelector(".user-left-box .sel-user-ul").scrollTop = 0;
          });
          break;
        case 3:
          // 搜索人
          debounce({
            timerName: "doSearch",
            time: 300,
            fnName: async function () {
              if (!val) {
                inviteObj.value.searchList = deepClone(inviteObj.value.searchDefaultList);
                return;
              }
              let params = {
                page: 1,
                rows: 50,
                name: val,
                keyword: val,
                workerNo: userInfo.workerNo,
                status: "1",
                hasAi: "1",
              };
              let res;
              inviteObj.value.searchFlag = true;
              if ((inviteObj.value.type == 2 && inviteObj.value.selGroupType != 0) || inviteObj.value.classifyType == 3) {
                res = await searchUsersApi({
                  msgBody: JSON.stringify(params),
                });
              } else {
                res = await findIMEmpByAuthApi({
                  msgBody: JSON.stringify(params),
                });
              }
              if (!res.success) {
                toast({title: res.errorMsg || "系统错误", type: 2});
              } else if (res?.data?.data || res?.data?.empList) {
                var list = res.data.data || res.data.empList;
                // 设置人员数据
                for (let i = 0; i < list.length; i++) {
                  setUserBaseInfo(list[i]);
                }
                inviteObj.value.searchList = list;
              }
              inviteObj.value.searchFlag = false;
              nextTick(() => {
                createUserRef.value.querySelector(".user-left-box .sel-user-ul").scrollTop = 0;
              });
            }
          });
          break;
        case 4:
          // 搜索客户
          let searchList = deepClone(store.getters.getNimFriend({val: val}));
          if (inviteObj.value.customerType == 2) {
            let labelSettings = store.getters.getClassifyList({type: 4});
            // 去除已分组客户
            for (let i = 0; i < labelSettings.length; i++) {
              let item = labelSettings[i];
              if (item.value) {
                item.value.split(",").map(wItem => {
                  let searchIndex = searchList.findIndex(sItem => {return sItem.workerNo == wItem});
                  if (searchIndex > -1) {
                    searchList.splice(searchIndex, 1);
                  }
                });
              }
            }
          }
          inviteObj.value.searchList = searchList;
          nextTick(() => {
            createUserRef.value.querySelector(".user-left-box .sel-user-ul").scrollTop = 0;
          });
          break;
      }
    }

    // 选择人员/讨论组
    function selItem(item) {
      if (inviteObj.value.isTeamLabel) {
        // 切换选择讨论组
        if (inviteObj.value.selMap[item.teamId]) {
          delete inviteObj.value.selMap[item.teamId];
          return;
        }
        if (calcSelNum(1) >= 50) {
          toast({title: "单次添加讨论组个数不超过50个", type: 2});
          return;
        }
        inviteObj.value.selMap[item.teamId] = item;
      } else {
        if (inviteObj.value.personMap[item.workerNo]) {
          // 默认人员不给取消选中
          return;
        }
        // 切换选择人员
        if (inviteObj.value.selMap[item.workerNo]) {
          delete inviteObj.value.selMap[item.workerNo];
          return;
        }
        if (inviteObj.value.type == 3 || inviteObj.value.type == 4) {
          if (inviteObj.value.classifyType == 3) {
            // 同事标签最多10/50人
            let maxNum = store.getters.getPurMap({key: "forward"}) ? 50 : 10;
            if (calcSelNum(2) >= maxNum) {
              toast({title: `同事标签不允许大于${maxNum}人`, type: 2});
              return;
            }
          } else if (inviteObj.value.classifyType == 4) {
            // 客户标签最多50人
            if (calcSelNum(2) >= 50) {
              toast({title: "客户标签不允许大于50人", type: 2});
              return;
            }
          }
        } else if (inviteObj.value.type == 2 && !inviteObj.value.sessionInfo.id && inviteObj.value.selGroupType != 0 && calcSelNum(1) >= 20) {
          // 业务讨论组创建只能20人
          toast({title: "业务类讨论组直接拉人人数不允许大于20人", type: 2});
          return;
        }
        inviteObj.value.selMap[item.workerNo] = item;
      }
    }

    // 计算选择人员 type1选择数量2总群成员
    function calcSelNum(type) {
      let selNum = Object.values(inviteObj.value.selMap).length;
      if (type == 1) {
        // 创建加入默认人员
        return selNum;
      } else if (type == 2) {
        return Object.values(Object.assign({}, inviteObj.value.selMap, inviteObj.value.personMap, inviteObj.value.itemMap)).length;
      }
    }

    // 创建讨论组确定
    async function createGroupConfirm() {
      if (calcSelNum(2) < 2) {
        toast({title: "请添加至少一个成员！", type: 2});
        return;
      }
      let params = {
        groupName: `${userInfo.name}等人`,
        workerNos: Object.keys(inviteObj.value.selMap).concat(Object.keys(inviteObj.value.personMap)).toString(),
        owner: userInfo.workerNo,
        workerId: userInfo.workerId,
        groupType: inviteObj.value.selGroupType,
      }
      loading();
      let res = await createGroupApi({msgBody: JSON.stringify(params)});
      loading().hide();
      if (res.success) {
        setTimeout(() => {
          // 跳到新的讨论组
          store.dispatch("setCurrentSession", {id: `team-${res.data.tid}`, type: "open"});
        }, 500);
        dialogOperate(2, 1);
        if (res.data?.tipMsg) {
          alert({content: res.data?.tipMsg, showCancel: false, okText: "我知道了"});
        }
      } else {
        toast({title: res.errorMsg || res.err || "系统错误", type: 2});
      }
    }

    // 邀请人员确定
    async function inviteGroupConfirm() {
      if (calcSelNum(1) < 1) {
        toast({title: "请添加至少一个成员！", type: 2});
        return;
      }
      loading()
      // 群和超大群走后台
      let params = {
        type: 1,
        teamType: inviteObj.value.sessionInfo?.detailInfo?.detailType == "group" ? "2" : "1",
        tid: inviteObj.value.sessionInfo?.to,
        workerNo: Object.keys(inviteObj.value.selMap).toString()
      }
      let res = await comeInAndGoOutGroupApi({msgBody: JSON.stringify(params)})
      loading().hide()
      if (res.success) {
        alert({content: res.data?.data || "邀请成功", showCancel: false, okText: "我知道了"});
        dialogOperate(2, 1);
      } else {
        alert({content: res.errorMsg || res.err || "系统错误", showCancel: false, okText: "我知道了"});
      }
    }

    // 讨论组分组添加讨论组确定
    async function classifyAddGroupConfirm() {
      if (calcSelNum(1) < 1) {
        toast({title: `请选择至少一个${inviteObj.value.isTeamLabel ? "讨论组/群" : "成员"}！`, type: 2});
        return;
      }
      let groupList = Object.keys(inviteObj.value.selMap);
      store.commit("setEmit", {
        type: "initClassify", value: {
          type: 5,
          classifyType: inviteObj.value.classifyType,
          classifyName: inviteObj.value.classifyName,
          classifyUUID: inviteObj.value.classifyUUID,
          classifyValue: [{uuid: "0", value: groupList.toString()}],
          notSendCustomSysMsg: inviteObj.value.notSendCustomSysMsg,
          time: Date.now(),
          done: res => {
            if (res.success) {
              dialogOperate(2, 1);
              groupList.map(tid => {
                // 不存在会话则插入本地
                remote.store.dispatch("insertLocalSession", {scene: inviteObj.value.isTeamLabel ? "team" : "p2p", to: tid});
              });
              inviteObj.value.done && inviteObj.value.done(res, groupList);
            }
          }
        }
      });
    }

    // 标签分组添加人员确定
    async function classifyAddLabelConfirm() {
      if (calcSelNum(1) < 1) {
        toast({title: "请选择至少一个成员！", type: 2});
        return;
      }
      inviteObj.value.done && inviteObj.value.done(Object.values(inviteObj.value.selMap));
      dialogOperate(2, 1);
    }

    // 改变客户搜索类型
    function changeCustomerType(type) {
      inviteObj.value.customerType = type;
      doSearch();
    }

    return {
      createUserRef,
      inviteObj,

      inviteBoxInit,
      addGroup,
      inviteGroup,
      selType,
      dialogOperate,
      avatarError,
      doSearch,
      selItem,
      calcSelNum,
      changeCustomerType,
    };
  },
}
</script>
<style lang="scss" scoped>
.invite-box {
  .sel-box-i {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    position: relative;
    border: 1px solid #BCBCBC;
    border-radius: 2px;

    &.sel {
      border: 1px solid $styleColor;
      background: $styleColor;

      &:after {
        content: "";
        width: 8px;
        height: 3px;
        border: 2px solid #FFFFFF;
        border-top: transparent;
        border-right: transparent;
        position: absolute;
        top: 2px;
        left: 1px;
        transform: rotate(-45deg);
      }
    }

    &.disabled {
      border: 1px solid #DDDDDD;
      background: $styleBg1Hover;

      &:after {
        content: "";
        width: 8px;
        height: 3px;
        border: 2px solid #DDDDDD;
        border-top: transparent;
        border-right: transparent;
        position: absolute;
        top: 2px;
        left: 1px;
        transform: rotate(-45deg);
      }
    }
  }

  .user-avatar-box {
    height: 20px !important;
    width: 20px !important;
    margin-right: 6px;

    .avatar-box {
      cursor: default;
    }
  }

  .sel-user-ul {
    width: 100%;
    overflow-y: auto;
    margin: 6px 0;
    font-size: 13px;

    li {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      height: 30px;
      padding: 0 16px;

      &.close-li {
        padding-right: 31px;

        .close {
          position: absolute;
          top: 50%;
          right: 11px;
          transform: translateY(-50%);
          background: url("/img/close.png") no-repeat center;
          background-size: 10px 10px;
          height: 100%;
          width: 20px;
          cursor: pointer;
        }
      }

      &:not(.user-none):hover {
        background: $styleBg1Hover;
      }

      &.user-none {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;

        .loading-img {
          width: 32px;
        }
      }

      .sel-box-i {
        margin-right: 10px;
      }
    }
  }

  .create-group-type-dialog {
    :deep(.ly-dialog .content) {
      padding: 20px 2px 10px 16px;
    }

    .create-group-type {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      max-height: 270px;
      padding-right: 8px;
      overflow: auto;

      &.create-group-type-2,
      &.create-group-type-3 {
        justify-content: center;
        padding-right: 14px;

        li {
          justify-content: center;
          width: auto;
          margin-right: 30px;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      li {
        position: relative;
        display: flex;
        align-items: center;
        width: 90px;
        line-height: 17px;
        margin: 0 10px 10px 0;
        cursor: pointer;

        &:nth-child(4n) {
          margin-right: 0;
        }

        &.sel {
          .radio-i {
            position: relative;
            background: $styleColor;
            border: 1px solid $styleColor;

            &:after {
              content: "";
              position: absolute;
              top: 50%;
              left: 50%;
              width: 5px;
              height: 5px;
              transform: translate(-50%, -50%);
              border-radius: 50%;
              background: #FFFFFF;
            }
          }
        }

        .radio-i {
          width: 14px;
          height: 14px;
          border-radius: 50%;
          border: 1px solid #E0E0E0;
          margin-right: 4px;
        }
      }
    }
  }

  .create-user-dialog {
    :deep(.ly-dialog .content) {
      padding: 16px;
    }

    .create-user {
      display: flex;
      width: 100%;
      height: 360px;
      border: 1px solid #E0E0E0;
      border-radius: 4px;

      &.classify-4 {
        .user-left-box {
          .sel-user-ul {
            height: calc(100% - 46px - 38px - 6px);
            margin-top: 0;
          }
        }
      }

      .user-left-box,
      .user-right-box {
        width: 50%;
        height: 100%;
      }

      .user-left-box {
        border-right: 1px solid #E0E0E0;

        .search-box {
          position: relative;
          margin: 16px 16px 0;

          input {
            width: 100%;
            height: 30px;
            padding: 0 30px;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            background-image: url("/img/search/icon_search.png");
            background-repeat: no-repeat;
            background-size: 16px 16px;
            background-position: 7px;

            &:hover,
            &:focus {
              border: 1px solid #666666;
            }
          }

          .icon-close {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: url("/img/search/close.png") no-repeat;
            background-size: 12px;
            cursor: pointer;
          }
        }

        .tab-box {
          display: flex;
          align-items: center;
          margin: 10px 0 8px;
          padding: 0 16px;
          color: #666666;
          line-height: 18px;

          .tab-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }

          .tab-ul {
            display: flex;
            flex-wrap: wrap;
            max-height: 62px;
            overflow: hidden;

            .tab-li {
              padding: 1px 10px;
              border: 1px solid #E0E0E0;
              margin-right: 6px;
              border-radius: 2px;
              cursor: pointer;

              &.sel {
                color: $styleColor;
                border: 1px solid $styleColor;
                font-weight: bold;
              }
            }
          }
        }

        .sel-user-ul {
          height: calc(100% - 46px - 12px);
        }
      }

      .user-right-box {
        .tips-box {
          display: grid;
          align-items: center;
          width: 100%;
          height: 34px;
          overflow-y: auto;
          color: #999999;
          margin: 14px 0;
          padding: 0 16px;
        }

        .sel-user-tips {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 16px;
          color: #333333;
          font-weight: bold;
          line-height: 17px;

          &.show {
            margin-top: 30px;
          }

          .sel-user-text {
            display: flex;
            align-items: center;

            .highlight {
              margin: 0 4px;
            }
          }
        }

        .sel-user-ul {
          height: calc(100% - 34px - 28px - 17px - 12px);

          &.sel-user-ul-show {
            height: calc(100% - 30px - 17px - 12px);
          }
        }
      }
    }
  }

  .create-tips {
    margin-top: 16px;
  }
}
</style>