<template>
  <div class="team-editor">
    <LyDialog title="编辑讨论组" class="editor-group-dialog" :width="360" :visible="groupObj.showType==1" @close="dialogOperate(1,1)" @confirm="dialogOperate(1,2)">
      <div class="sel-box" v-show="groupObj.typeList.length>0">
        <div class="sel-label">类型:</div>
        <ul class="sel-ul">
          <li v-for="(item,key) in groupObj.typeList" :key="item.groupType" :class="[item.id==groupObj.groupType?'sel':'']" @click="selItem(1,item)">
            <i class="radio-i"></i>
            <span>{{ item.typeName }}</span>
          </li>
        </ul>
      </div>
      <div class="sel-box" v-show="groupObj.statusList.length>0">
        <div class="sel-label">状态:</div>
        <ul class="sel-ul">
          <li v-for="(item,key) in groupObj.statusList" :key="item.id" :class="[item.value==groupObj.groupStatus?'sel':'']" @click="selItem(2,item)">
            <i class="radio-i"></i>
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
    </LyDialog>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import LyDialog from "@comp/ui/comps/LyDialog";
import {batchUpdateStatusForFrontApi, getEditMapForFrontApi} from "@utils/net/api";
import {toast, loading, alert} from "@comp/ui";

export default {
  name: "teamEditor",
  props: {
    callback: {
      type: Function,
      default: function () {}
    }
  },
  components: {LyDialog},
  setup(props, ctx) {
    const store = useStore();
    let groupObj = ref({
      showType: -1,// 1显示编辑讨论组
      tids: "",// 编辑的群id
      groupType: "",// 选中的群类型
      groupStatus: "",// 选中的群状态
      typeList: [],// 可切换群类型列表
      statusList: [],// 可切换群状态列表
    });

    // 初始化查询讨论组可切换类型
    async function initTeamInfo(tids, teamInfo) {
      if (teamInfo && !store.getters.getTeams({id: tids}).teamId) {
        // 不是自己所在的群不能修改
        return;
      }
      groupObj.value.tids = tids;
      loading();
      let res = await getEditMapForFrontApi({msgBody: JSON.stringify({tids: tids})});
      loading().hide();
      if (!res.success) {
        toast({title: res.errorMsg, type: 2});
        return;
      }
      groupObj.value.typeList = res.data.typeList || [];
      groupObj.value.statusList = res.data.statusList || [];
      groupObj.value.groupType = "";
      groupObj.value.groupStatus = "";
      // 群类型
      for (let i = 0; i < groupObj.value.typeList.length; i++) {
        let item = groupObj.value.typeList[i];
        // groupType和typeName一致才选中
        if (item.id == teamInfo?.serverCustom?.groupType && item.typeName == teamInfo?.serverCustom?.typeName) {
          groupObj.value.groupType = item.id;
          break;
        }
      }
      // 群状态
      for (let i = 0; i < groupObj.value.statusList.length; i++) {
        let item = groupObj.value.statusList[i];
        // value和typeStatus一致才选中
        if (item.value == teamInfo?.serverCustom?.status && item.label == teamInfo?.serverCustom?.typeStatus) {
          groupObj.value.groupStatus = item.value;
          break;
        }
      }
      groupObj.value.showType = 1;
    }

    // 显示弹窗 type1编辑讨论组 key1关闭2确认
    async function dialogOperate(type, key) {
      if (type == 1) {
        if (key == 2) {
          let groupType = groupObj.value.groupType;
          let groupStatus = groupObj.value.groupStatus;
          if (groupType == null || groupType === "") {
            toast({title: "请选择讨论组类型"});
            return;
          }
          if (groupObj.value.statusList.length > 0 && (groupStatus == null || groupStatus === "")) {
            toast({title: "请选择讨论组状态"});
            return;
          }
          // 确认修改群状态
          loading();
          let res = await batchUpdateStatusForFrontApi({
            msgBody: JSON.stringify({
              tids: groupObj.value.tids,
              groupType: groupType,
              groupStatus: `${(groupStatus || 0)}`,
            })
          })
          loading().hide();
          props.callback(res);
          if (!res.success) {
            toast({title: res.errorMsg, type: 2});
            return;
          }
          if (res.data?.fail?.length > 0) {
            toast({title: res.errorMsg || "讨论组状态修改成功，部分讨论组状态修改失败", type: 1});
          } else {
            toast({title: "修改成功", type: 1});
          }
        }
        groupObj.value.showType = -1;
      }
    }

    // 选择 type1类型2状态
    function selItem(type, item) {
      if (type == 1) {
        groupObj.value.groupType = item.id;
      } else if (type == 2) {
        groupObj.value.groupStatus = item.value;
      }
    }

    return {
      groupObj,

      initTeamInfo,
      dialogOperate,
      selItem,
    }
  }
}
</script>
<style scoped lang="scss">
.team-editor {
  .editor-group-dialog {
    :deep(.ly-dialog .content) {
      padding: 16px 2px 6px 16px;
    }

    .sel-box {
      display: flex;
      line-height: 17px;

      .sel-label {
        width: 38px;
        flex-shrink: 0;
        color: #666666;
      }

      .sel-ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        max-height: 270px;
        padding-right: 8px;
        overflow: auto;

        li {
          position: relative;
          display: flex;
          align-items: center;
          width: 90px;
          margin: 0 10px 10px 0;
          cursor: pointer;

          &:nth-child(3n) {
            margin-right: 0;
          }

          &.sel {
            .radio-i {
              position: relative;
              background: $styleColor;
              border: 1px solid $styleColor;

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                width: 5px;
                height: 5px;
                transform: translate(-50%, -50%);
                border-radius: 50%;
                background: #FFFFFF;
              }
            }
          }

          .radio-i {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            border: 1px solid #E0E0E0;
            margin-right: 4px;
          }
        }
      }
    }
  }
}
</style>