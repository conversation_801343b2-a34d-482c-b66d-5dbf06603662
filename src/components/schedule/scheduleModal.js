/**
 * 日程弹窗
 * Created by tangyaping on 2021/8/12.
 *[依赖文件]: jquery
 */
import {alert, loading, toast} from "@comp/ui";
import {sortTeamMembers, getLink, openForward} from '@utils'
import {
  smartSaveApi, smartDetailApi, smartUpdateApi, searchUsersApi, createGroupApi, smartDeleteApi, smartTransmitApi, smartMakeOverApi, smartConflictEmpApi,
  smartConferenceRoomListApi, smartAddMemoApi
} from '@utils/net/api'

let store = {};

export function initScheduleModal(param) {
  store = param;
  let ctrl = {
    alert: alert,
    loading: loading,
    toast: toast,
    userInfo: store.getters.getUserInfo,
    config: store.getters.getConfig.config,
    baseComputerInfo: store.getters.getBaseComputerInfo,
    nim: store.getters.getNim,
    openChatBox: openChatBox,
    getTeam: getTeam,
    getTeamMembers: getTeamMembers,
    getALinkNew: getLink,
    coaSdk: {
      selSearchApi: searchUsersApi,
      createGroup: createGroupApi,
      smartSave: smartSaveApi,
      smartDetail: smartDetailApi,
      smartUpdate: smartUpdateApi,
      smartDelete: smartDeleteApi,
      smartMakeOver: smartMakeOverApi,
      smartTransmit: smartTransmitApi,
      smartConferenceRoomList: smartConferenceRoomListApi,
      smartAddMemo: smartAddMemoApi,
      smartConflictEmp: smartConflictEmpApi,
    }
  }

  // 打开会话
  function openChatBox(account, scene) {
    store.dispatch("setCurrentSession", {id: scene + "-" + account});
  }

  // 获取群信息
  function getTeam(id) {
    if (id) {
      return store.getters.getTeams({id: id}) || {};
    } else {
      return store.getters.getTeams({sort: 1}) || [];
    }
  }

  // 获取群成员
  function getTeamMembers(id) {
    return new Promise(resolve => {
      store.dispatch("getTeamMembers", {id: id}).then(res => {
        // 加载群信息出错
        if (res.err) {
          resolve([]);
          return;
        }
        // 群主、管理员、普通成员排序
        let list = sortTeamMembers(res.obj);
        // 获取群员详情
        store.dispatch("getPersons", {doneFlag: true, account: list.map(item => {return item.account})}).then(personInfo => {
          resolve(Object.values(personInfo));
        });
      });
    })
  }

  //公共弹出框
  let alertObj = {};
  var alertBox = ctrl.alert;
  var timeoutNum = 10000;  //几秒超时设置
  var limitPerson = 100; //添加关联人限制个数
  //精确日程
  var alarmList1 = [
    {text: '事件发生时', value: '7-0'},
    {text: '提前5分钟', value: '1-5'},
    {text: '提前15分钟', value: '1-15'},
    {text: '提前30分钟', value: '1-30'},
    {text: '提前1小时', value: '2-1'},
    {text: '提前2小时', value: '2-2'},
    {text: '提前1天', value: '3-1'},
    {text: '提前2天', value: '3-2'},
    {text: '提前1周', value: '4-1'}
  ];
  //全天日程
  var alarmList2 = [
    {text: '当天08:00', value: '5-8'},
    {text: '当天09:00', value: '5-9'},
    {text: '当天10:00', value: '5-10'},
    {text: '提前1天08:00', value: '6-8'},
    {text: '提前1天09:00', value: '6-9'},
    {text: '提前1天10:00', value: '6-10'}
  ];
  var curUserInfo = {};  //当前登录人信息
  var defaults = {
    type: 1, //1新增   3查看详情
    workerId: '',  //日历归属人id，type传3的时候传
    workerName: '',//日历归属人姓名，type传3的时候传
    id: '', //日程scheduleId，type传3的时候传
    x: 0,  //弹窗距离左边的距离
    y: 0,  //弹窗距离顶部的距离
    time: '', //例如：2021年08月16日 10:00-2021年08月16日 10:30，type传1的时候传，不传时分代表是全天日程例如2021年08月16日-2021年08月16日
    title: '',//标题
    callback: null, //回调  返回第一个参数 'add' 新增成功返回  'update' 编辑成功  'cancel' 点击关闭  'feedback'  详情点击返回按钮 'transfer' 转让 'delete' 删除
    //第二个参数 1接受 2待定 4拒绝
    colorType: '0',  //背景色默认0  本人日程传0  按设计稿上顺序传0,1，2，3..
    parentObj: '',  //创建弹窗父级,默认$("#periphery")
  }

  var teamId = "";// 一键建群后的群id，并判断群是否被解散
  function ScheduleModal(opts) {
    this.opts = $.extend(true, {}, defaults, opts);
    if (this.opts.changeStatus) {
      this.changeScheduleStatus(this.opts.changeStatus, opts);
    } else {
      this.init();
    }
  }

  ScheduleModal.prototype = {
    constructor: ScheduleModal,
    init: function () {
      var _this = this;
      this.modalObj = null; //modal最高元素str
      this.tempDetailObj = null; //日程详情暂存
      this.seachEmpTimer = null;
      curUserInfo = ctrl.userInfo;
      if (this.opts.type < 3) {
        this.fillFormHtml()
      } else {
        if (!this.opts.workerId) {
          ctrl.toast({title: '当前本人非该日程参与者，请重新加入', type: 2});
          return;
        }
        var data = {
          id: this.opts.id,
          workerId: this.opts.workerId,
        }
        getScheduleDetailAjax(data, function (data) {
          //暂存
          _this.tempDetailObj = data;
          // 获取当前日程讨论组信息
          teamId = ctrl.getTeam(_this.tempDetailObj.groupId).teamId || "";
          _this.fillDetailHtml(data);
        })
      }
    },
    //表单
    fillFormHtml: function (data) {
      var _this = this;
      var isOrg = !data || (curUserInfo.workerId == _this.tempDetailObj.organizerNumber); //新增日程或者是组织者

      var styleStr = '';
      //设置弹窗位置
      if (_this.opts.x && _this.opts.y) {
        var x = _this.opts.x;
        var y = _this.opts.y;
        var bodyWidth = $("body").width();
        var bodyHeight = $("body").height();
        // x不能大于980 - (486 + 80) = 414  y不能大于 680- (444 + 40) =196
        var maxX = parseInt(bodyWidth) - (486 + 80);
        var maxY = parseInt(bodyHeight) - (444 + 40);
        if (x < 180) {
          x = 180;
        }
        if (x > maxX) {
          x = maxX;
        }
        if (y < 40) {
          y = 40;
        }
        if (y > maxY) {
          y = maxY;
        }
        styleStr = 'left:' + x + 'px;top:' + y + 'px;'
      }
      //发现页面已经有scheduleModal  id换个名字
      var idRandom = '';
      if ($("body").find("#scheduleModal").length) {
        for (var i = 0; i < 10; i++) {
          idRandom += (parseInt(Math.random() * 10) + '')
        }
      }

      var _html = '<div class="rc-modal-overlay visible lay" id="scheduleModal' + (idRandom ? ('_' + idRandom) : '') + '">';
      _html += '<div class="rc-modal" style = "' + styleStr + '">\n' +
        '       <div class="rc-head">\n' +
        '        <span class="rc-title modal-title" data-isorg="' + (isOrg ? 1 : 0) + '">' + (_this.opts.type == 2 && _this.opts.id ? '编辑' : '添加') + '日程</span>\n' +
        '        <i class="rc-close"></i>\n' +
        '      </div>';
      _html += '<div class="rc-body"><div class="rc-contain">';

      //主题
      _html += '<div class="rc-block' + (data && !isOrg ? ' mb0' : '') + '">\n' +
        '      <i class="block-icon theme"></i>\n' +
        '      <div class="bloc-right">';
      //编辑 并且不是组织者
      if (data && !isOrg) {
        _html += '<span class="line-30 font14 bold">' + ((data.title || '') || '无主题') + '</span>';
      } else {
        _html += '<input class="rc-input tit" value="' + (data ? data.title : (_this.opts.title && typeof _this.opts.title == 'string' ? _this.opts.title.substring(0, 20) : '')) + '" name="title" type="text" placeholder="请输入日程主题" maxlength="20" autocomplete="off">';
      }
      _html += '</div></div>';

      //日期
      _html += '<div class="rc-block">\n' +
        '      <i class="block-icon time"></i>';
      if (data && data.startTimeStr) {
        var startDate = data.startTimeStr.split(" ")[0];
        var startTime = data.startTimeStr.split(" ")[1];
      }
      if (data && data.endTimeStr) {
        var endDate = data.endTimeStr.split(" ")[0];
        var endTime = data.endTimeStr.split(" ")[1];
      }
      var dayType = (data && data.dayType == 1) || 0;
      if (!data && _this.opts.time) {
        var startTimeStr = _this.opts.time.split("-")[0];
        var endTimeStr = _this.opts.time.split("-")[1];
        var startDate = startTimeStr.split(" ")[0];
        var startTime = startTimeStr.split(" ")[1];

        var endDate = endTimeStr.split(" ")[0];
        var endTime = endTimeStr.split(" ")[1];
        if (!startTime || !endTime) {
          dayType = 1;
          startTime = '00:00';
          endTime = '23:59';
        }
      }
      //编辑 并且不是组织者
      if (data && !isOrg) {
        _html += '<span class="line-30">' + data.startTimeStr + ' ' + data.endTimeStr + '</span>' +
          '        <span class="fl" style="display: none">\n' +
          '              <div class="icheckbox_square blue' + (dayType ? ' checked' : '') + '">\n' +
          '                <input class="dayAll" ' + (dayType ? 'checked' : '') + ' name="dayAll" type="checkbox" value="1" autocomplete="off">全天\n' +
          '              </div>\n' +
          '            </span>';
      } else {
        _html += '<div class="bloc-right h30 line-30 schedule-date">\n' +
          '            <span class="fl date-input relative pr6">\n' +
          '              <input class="date-start prefix" value="' + (startDate || '') + '" name="startDate" type="text" data-type="2" readonly style="width: 114px;padding: 5px 10px;" autocomplete="off">\n' +
          '            </span>\n' +
          '        <span style="display: ' + (dayType ? 'none' : 'block') + ';" class="fl time-input pr6">\n' +
          '              <input type="text" name="startTime" value="' + (startTime || '') + '" class="fl time-start suffix" style="width: 54px;padding: 5px 10px;" maxlength="5" autocomplete="off">\n' +
          '            </span>\n' +
          '        <span class="fl pr6 font12">-</span>\n' +
          '        <span style="display: ' + (dayType ? 'none' : 'block') + ';" class="fl time-input pr6">\n' +
          '              <input type="text" name="endTime" value="' + (endTime || '') + '" class="fl time-end suffix" style="width: 54px;padding: 5px 10px;" maxlength="5" autocomplete="off">\n' +
          '            </span>\n' +
          '        <span class="fl date-input relative pr6">\n' +
          '              <input class="date-end prefix" value="' + (endDate || '') + '" name="endDate" type="text" data-type="2" readonly style="width: 114px;padding: 5px 10px;" autocomplete="off">\n' +
          '            </span>\n' +
          '        <span class="fl">\n' +
          '              <div class="icheckbox_square blue' + (dayType ? ' checked' : '') + '">\n' +
          '                <input class="dayAll" ' + (dayType ? 'checked' : '') + ' name="dayAll" type="checkbox" value="1" autocomplete="off">全天\n' +
          '              </div>\n' +
          '            </span>\n' +
          '      </div>';
      }
      var empHtml = '';
      if (data && data.empList && data.empList.length) {
        empHtml = data.empList.map(function (v, i) {
          var org = '';
          var busy = '';
          if (v.type == 3) {
            org = '<i class="org-icon make-black-tip" data-text="组织者"></i>';
          }
          if (v.busy == 1) {
            busy = '<i class="busy-icon make-black-tip" data-text="该时段繁忙"></i>';
          }
          return '<div class="emp-li" data-empnumber="' + v.empNumber + '"><span>' + '<span>' + v.empName + '&nbsp;&nbsp;' + '</span>' + '<span class="emp-li-inner-deptName">' + v.deptName + '</span></span>\n' + org + busy +
            '<a class="del-e-icon" style="display: ' + (v.canDel ? 'block' : 'none') + '" href="javascript:;"></a></div>';
        }).join("")
      }
      _html += '</div>\n' +
        '    <div class="rc-block sch-relate-box">\n' +
        '      <i class="block-icon people"></i>\n' +
        '      <div class="bloc-right select-block">\n' +
        '        <input class="rc-input empOrGroupSearch" type="text" placeholder="添加关联人或群" maxlength="20" autocomplete="off">\n' +
        '        <span class="count-num"><span class="num">' + (data && data.empList && data.empList.length ? data.empList.length : 0) + '</span>/' + limitPerson + '</span>\n' +
        '        <div class="eg-selected-list">' + empHtml + '</div>\n' +
        '      </div>\n' +
        '    </div>';
      //会议室
      if (!data || isOrg || data.roomIds || data.customContent) {
        _html += '<div class="rc-block hys-block' + (data && data.customContent && !data.roomIds ? ' cust' : '') + '">\n' +
          '      <i class="block-icon hy"></i>\n' +
          '      <div class="bloc-right">';
        //编辑 并且不是组织者
        if (data && !isOrg) {
          _html += '<span class="edit-show font12 line-18">' + ((data.roomIds && data.roomNames) ? data.roomNames.split(',').join(';') : data.customContent) + '</span>'
        } else {
          var hysHtml = '';
          if (data && data.roomIds && data.roomNames) {
            var roomIdArr = data.roomIds.split(",");
            var roomNameArr = data.roomNames.split(",");
            hysHtml = roomIdArr.map(function (v, i) {
              var roomId = v.split(":")[0];
              return '<div class="hys-sel" data-id="' + roomId + '" data-name="' + roomNameArr[i] + '">\n' +
                '                <i class="blue-del"></i>\n' +
                '                <span>' + roomNameArr[i] + '</span>\n' +
                '              </div>';
            }).join("");
          }
          _html += '<div class="h30">\n' +
            '              <span class="fl mr6">\n' +
            '                <button id="addHys" class="btn-blue" ' + (data && data.customContent && !data.roomIds ? 'disabled' : '') + ' type="button">添加会议室</button>\n' +
            '              </span>\n' +
            '              <span class="fl line-30 mr10">\n' +
            '                <div class="icheckbox_square blue' + (data && data.customContent && !data.roomIds ? ' checked' : '') + '">\n' +
            '                  <input id="custCheckbox" ' + (data && data.customContent && !data.roomIds ? 'checked' : '') + ' type="checkbox" value="1" autocomplete="off">自定义\n' +
            '                </div>\n' +
            '              </span>\n' +
            '              <span class="fl red-tip" style="display: none;">日程已变更，请重选</span>\n' +
            '        </div>\n' +
            '        <div class="cust-hys mt6 relative">\n' +
            '          <textarea class="rc-input tc-textarea' + (data && data.customContent ? ' open' : '') + ' tc-inp-count" name="custHys" placeholder="添加会议室描述" maxlength="200">' + (data && data.customContent ? data.customContent : '') + '</textarea>\n' +
            '          <span class="count-num area"><span class="num">' + (data && data.customContent ? data.customContent.length : '0') + '</span>/200</span>\n' +
            '        </div>\n' +
            '        <div class="hys-sel-list">' + hysHtml + '</div>';
        }
        _html += '</div></div>';
      }

      //视频会议信息
      var videoContent = ''
      if (data && data.videoContent) {
        videoContent = data.videoContent;
      }
      //编辑并且不是组织者并且为空 才不展示
      if (!data || isOrg || videoContent) {
        _html += '<div class="rc-block">\n' +
          '      <i class="block-icon video"></i>\n' +
          '      <div class="bloc-right relative">\n';

        //编辑 并且不是组织者
        if (data && !isOrg) {
          _html += '<span class="edit-show font12 line-18">' + (videoContent ? videoContent.replace(/\r|\n/g, "<br/>") : '') + '</span>';
        } else {
          _html += '<textarea class="rc-input tc-textarea' + (videoContent ? ' open' : '') + ' tc-inp-count" name="videoContent" placeholder="添加视频会议信息" maxlength="200">' + (videoContent || '') + '</textarea>\n' +
            '<span class="count-num area"><span class="num">' + (videoContent ? videoContent.length : 0) + '</span>/200</span>';
        }
        _html += '</div></div>';
      }

      //添加描述
      var content = ''
      if (data && data.content) {
        content = data.content;
      }
      //编辑并且不是组织者并且为空 才不展示
      if (!data || isOrg || content) {
        _html += '<div class="rc-block">\n' +
          '      <i class="block-icon pen"></i>\n' +
          '      <div class="bloc-right area relative">';

        //编辑 并且不是组织者
        if (data && !isOrg) {
          _html += '<span class="edit-show font12 line-18">' + (content ? content.replace(/\r|\n/g, "<br/>") : '') + '</span>'
        } else {
          _html += '<textarea class="rc-input tc-textarea' + (content ? ' open' : '') + ' tc-inp-count" name="content" placeholder="添加描述" maxlength="200">' + (content || '') + '</textarea>\n' +
            '<span class="count-num area"><span class="num">' + (content ? content.length : 0) + '</span>/200</span>';
        }
        _html += '</div></div>';
      }

      //添加提醒
      var remindHtml = '';
      if (data && data.remindList && data.remindList.length) {
        var remindArr = data.remindStr.split(";");
        remindHtml = data.remindList.map(function (v, i) {
          return '<div class="clearfix ala-line">\n' +
            '          <div class="fl select-block relative alarm-select">\n' +
            '            <input class="rc-input select-show" value="' + remindArr[i] + '" type="text" readonly style="width: 167px;" autocomplete="off">\n' +
            '            <input class="rc-input select-val" value="' + (v.timeType + '-' + v.time) + '" type="hidden" style="width: 167px;" autocomplete="off">\n' +
            '            <i class="check-arrow"></i>\n' +
            '          </div>\n' +
            '          <div class="fl">\n' +
            '            <i class="alarm-del"></i>\n' +
            '          </div>\n' +
            '        </div>';
        }).join("")
      } else if (!data) {  //新增日程，默认展示一条提醒
        remindHtml = '        <div class="clearfix ala-line">\n' +
          '          <div class="fl select-block relative alarm-select">\n' +
          '            <input class="rc-input select-show" type="text" readonly style="width: 167px;" autocomplete="off">\n' +
          '            <input class="rc-input select-val" type="hidden" style="width: 167px;" autocomplete="off">\n' +
          '            <i class="check-arrow"></i>\n' +
          '          </div>\n' +
          '          <div class="fl">\n' +
          '            <i class="alarm-del"></i>\n' +
          '          </div>\n' +
          '        </div>';
      }
      _html += '<div class="rc-block mb0">\n' +
        '      <i class="block-icon alarm"></i>\n' +
        '      <div class="bloc-right">\n' + remindHtml +
        '        <div class="clearfix line-30 alarm-act" style="display: ' + (data && data.remindList && data.remindList.length >= 10 ? 'none' : '') + ';">\n' +
        '          <a class="add-alarm font12" href="javascript:;">添加提醒</a>\n' +
        '        </div>\n' +
        '      </div>\n' +
        '    </div>';
      _html += '</div></div>\n' +
        '  <div class="rc-footer tc shadow">\n' +
        '    <button class="btn-white mr20 cancel-btn" type="button">取消</button>\n' +
        '    <button class="btn-default schedule-submit" type="button">保存</button>\n' +
        '  </div>\n' +
        '<div class="add-hys">\n' +
        '   <div class="add-head">\n' +
        '       <span class="rc-title">添加会议室</span>\n' +
        '   </div>\n' +
        '   <div class="hys-list">\n' +
        '    </div>\n' +
        '</div>';
      if (this.opts.parentObj) {
        $(this.opts.parentObj).append(_html);
      } else {
        $("body").append(_html);
      }
      _this.modalObj = "#scheduleModal" + (idRandom ? ('_' + idRandom) : '');
      this.bindEditHtml();

      if (!data && !_this.opts.time) {
        this.initTimeStr();
      }

      if ($(_this.modalObj).find(".schedule-date").length) {
        //编辑初始化需要自动改变选择全天，默认选中自定义会议室
        _this.changeDateVal($(_this.modalObj).find(".schedule-date"), 1)
      }

      if (!data) {
        this.alarmChange()
      }
      _this.judgeFormBusyList()

      // _this.judgeShadow()
      // //监听页面高度变化
      // $(_this.modalObj).find(".rc-contain").bind('resize',function() {
      //   _this.judgeShadow()
      // })
    },
    //详情
    fillDetailHtml: function (data) {
      var _this = this;
      //日期
      var finalTimeStr = '';
      if (data.startTimeStr.split(' ')[0] == data.endTimeStr.split(' ')[0]) {
        //全天日程不展示时分
        if (data.dayType == 1) {
          finalTimeStr = data.startTimeStr.split(' ')[0];
        } else {
          finalTimeStr = data.startTimeStr.split(' ')[0] + '&nbsp;&nbsp;' + data.startTimeStr.split(' ')[1] + ' - ' + data.endTimeStr.split(' ')[1];
        }
      } else {
        //全天日程不展示时分
        if (data.dayType == 1) {
          finalTimeStr = data.startTimeStr.split(' ')[0] + ' - ' + data.endTimeStr.split(' ')[0];
        } else {
          finalTimeStr = data.startTimeStr + ' - ' + data.endTimeStr;
        }
      }
      //当前登录人是否是组织者
      var isOrg = (data.organizerNumber == curUserInfo.workerId);
      //组织者和创建者
      var orgHtml = '';
      if (data.organizerNumber == data.empNumber) {
        orgHtml = '<div>\n' +
          '   <span class="fl">' + data.empName + '</span>\n' +
          '   <span class="peo-tag">组织者</span>\n' +
          '</div>';
      } else {
        orgHtml = '<div>\n' +
          '   <span class="fl">' + data.organizerName + '</span>\n' +
          '   <span class="peo-tag">组织者</span>\n' +
          '</div>\n' +
          '<div class="mt6">\n' +
          '   <span class="fl">' + data.empName + '</span>\n' +
          '   <span class="peo-tag">创建者</span>\n' +
          '</div>';
      }
      //参与者
      var cyzHtml = '';
      var allCyzHtml = '';
      if (data.empList && data.empList.length) {
        var stClass = ['p-icon agree', 'p-icon wait', 'p-unanswer', 'p-icon refuse'];

        var statusNum = [0, 0, 0, 0];
        var orgIndex = 0;
        for (var i = 0; i < data.empList.length; i++) {
          if (data.empList[i].type == 1 || data.empList[i].type == 3) {
            orgIndex = i;
            break;
          }
        }
        var orgEmp = data.empList.splice(orgIndex, 1);
        data.empList.unshift(orgEmp[0])
        data.empList.forEach(function (v, i) {
          var orgH = '';
          var busyH = '';
          if (v.type == 1 || v.type == 3) {
            orgH = '<i class="p-icon org make-black-tip" data-text="组织者"></i>';
          }
          if (v.busy) {
            busyH = '<i class="p-icon busy make-black-tip" data-text="该时段繁忙"></i>';
          }
          //只统计接受和拒绝的-->全部统计
          if (v.status == 1 || v.status == 4 || v.status == 3 || v.status == 2) {
            statusNum[v.status - 1]++;
          }
          allCyzHtml += '<span class="pant-one" data-empnumber="' + v.empNumber + '">\n' +
            '<span class="fl" data-scene="p2p" data-account="' + v.empNo + '">' + v.empName + '</span>' + orgH +
            '<i class="' + stClass[v.status - 1] + (v.status != 3 ? ' make-black-tip' : '') + '" data-text="' + (v.status == 1 ? '已接受' : (v.status == 2 ? '已待定' : '已拒绝')) + '">' + (v.status == 3 ? '未回复' : '') + '</i>\n' +
            '</span>';
          if (i >= 6) {
            return;
          }
          cyzHtml += '<span class="pant-one" data-empnumber="' + v.empNumber + '">\n' +
            '<span class="fl" data-scene="p2p" data-account="' + v.empNo + '">' + v.empName + '</span>' + orgH +
            '<i class="' + stClass[v.status - 1] + (v.status != 3 ? ' make-black-tip' : '') + '" data-text="' + (v.status == 1 ? '已接受' : (v.status == 2 ? '已待定' : '已拒绝')) + '">' + (v.status == 3 ? '未回复' : '') + '</i>\n' +
            '</span>';
        });
        var statusText = [];
        var textArr = ['人接受', '人待定', '人未回复', '人拒绝'];
        statusNum.forEach(function (v, i) {
          if (v > 0) {
            statusText.push(v + textArr[i])
          }
        })
        statusText = statusText.join(" | ");
      }

      var actHtml = '';

      //日程归属人等于登录人 有操作按钮
      var ismyCale = (this.opts.workerId == curUserInfo.workerId); //是否是本人日历
      var curUserStatus = 0;  //当前人对于日程的状态
      var empNumbers = data.empList ? data.empList.map(function (v, i) {
        if (v.empNumber == curUserInfo.workerId) {
          curUserStatus = v.status;
        }
        return v.empNumber;
      }) : ''
      //当前登录人是参与者
      var isPart = empNumbers.includes(curUserInfo.workerId);
      if (ismyCale && !isOrg && !isPart) {
        ctrl.toast({title: '当前本人非该日程参与者，请重新加入', type: 2});
        return;
      }
      //是本人日历，展示操作和日程提醒
      if (ismyCale) {
        var delHtml = '';
        if (isOrg) {
          delHtml = '<span class="act-icon more-icon">\n' +
            '<div class="more-act-box">\n' +
            '<p class="m-a-l l-zr">转让</p>\n' +
            '<p class="m-a-l l-del">删除</p>\n' +
            '</div>\n' +
            '</span>';
        } else {
          delHtml = '<span class="act-icon make-black-tip del-icon" data-text="删除日程"></span>';
        }
        actHtml = '<span class="act-icon make-black-tip edit-icon" data-text="编辑日程"></span>\n' +
          '<span class="act-icon make-black-tip bw-icon" data-text="转备忘"></span>\n' +
          '<span class="act-icon make-black-tip share-icon" data-text="分享日程"></span>' + delHtml;
      }

      var feedHtml = '';
      //是本人日历，并且是参与者，展示反馈操作
      if (ismyCale && isPart) {
        feedHtml = '<div class="rc-det-btns shadow">\n' +
          '<a class="det-btn agree' + (curUserStatus == 1 ? ' checked' : '') + '" data-status="1" href="javascript:;">' + (curUserStatus == 1 ? '已接受' : '接受') + '</a>\n' +
          '<a class="det-btn refuse' + (curUserStatus == 4 ? ' checked' : '') + '" data-status="4" href="javascript:;">' + (curUserStatus == 4 ? '已拒绝' : '拒绝') + '</a>\n' +
          '<a class="det-btn wait' + (curUserStatus == 2 ? ' checked' : '') + '" data-status="2" href="javascript:;">' + (curUserStatus == 2 ? '已待定' : '待定') + '</a>\n' +
          '</div>';
      }

      var notMy = false; //与我无关
      //不是本人日历而且本人非组织者&参与者
      if (!ismyCale && !isPart && !isOrg) {
        notMy = true;
      }
      //发现页面已经有scheduleModal  id换个名字
      var idRandom = '';
      if ($("body").find("#scheduleModal").length) {
        // idRandom = parseInt(Math.random() * 10)
        for (var i = 0; i < 10; i++) {
          idRandom += (parseInt(Math.random() * 10) + '');
        }
      }
      _this.modalObj = "#scheduleModal" + (idRandom ? ('_' + idRandom) : '');
      var _html = '<div class="rc-modal-overlay visible" id="scheduleModal' + (idRandom ? ('_' + idRandom) : '') + '">';
      _html += '<div class="rc-det-modal selAll">\n' +
        '      <div class="rc-det-head color_' + (_this.opts.colorType || 0) + '">\n' +
        '        <div class="action-head clearfix">\n' +
        '          <div class="rc-acts-list">' + actHtml +
        '            <span class="act-icon make-black-tip close-icon" data-text="关闭"></span>\n' +
        '          </div>\n' +
        '        </div>\n' +
        '        <div class="">\n' +
        '          <p class="sche-title">' + (notMy ? '非公开日程' : ((data.title || '').trim() || '无主题')) + '</p>\n' +
        '          <p class="sche-time">' + finalTimeStr + '</p>\n' +
        '        </div>';
      //当日程中，参与人员>=3人时，显示“一键建群”入口，日程中的组织者、参与者均可以“一键建群”
      if (ismyCale && (isPart || isOrg) && _this.tempDetailObj && _this.tempDetailObj.empList) {
        var personNum = _this.tempDetailObj.empList.length;
        if (_this.tempDetailObj.participateNos.indexOf(_this.tempDetailObj.organizerNo) == -1) {
          personNum++;
        }
        if (personNum >= 3) {
          _html += '<div class="">\n' +
            '          <div class="build-group" data-id="' + teamId + '">' + (teamId ? '进群讨论' : _this.tempDetailObj.groupId ? '一键入群' : '一键建群') + '</div>\n' +
            '        </div>';
        }
      }
      _html += '</div>';

      // _html +='<div class="urgent-tip">紧急事项，请尽快处理</div>';

      //not-my 与自己无关的日历只展示归属者
      _html += '<div class="rc-det-main' + (notMy ? ' not-my' : '') + '">';
      //会议室
      var roomHtml = ''
      if ((!data.roomIds || !data.roomNames) && data.customContent) {
        roomHtml = '<span>' + data.customContent + '</span>';
      } else if (data.roomIds && data.roomNames) {
        roomHtml = data.roomNames.split(",").map(function (v, i) {
          return '<span class="hys-na">' + v + '</span>';
        }).join("")
      }
      if (roomHtml) {
        _html += '<div class="rc-block">\n' +
          '          <i class="block-icon hys"></i>\n' +
          '          <div class="bloc-right clearfix">\n' + ctrl.getALinkNew(roomHtml) +
          '          </div>\n' +
          '        </div>';
      }
      if (data.videoContent) {
        _html += '<div class="rc-block">\n' +
          '          <i class="block-icon video"></i>\n' +
          '          <div class="bloc-right">\n' +
          '            <p><span>' + (data.videoContent ? ctrl.getALinkNew(data.videoContent.replace(/\r|\n/g, "<br/>")) : '') + '</span></p>\n' +
          '          </div>\n' +
          '        </div>';
      }

      _html += '<div class="rc-block">\n' +
        '          <i class="block-icon man"></i>\n' +
        '          <div class="bloc-right">\n' + orgHtml +
        '          </div>\n' +
        '        </div>\n' +
        '        <div class="rc-block partner-block">\n' +
        '          <i class="block-icon people"></i>\n' +
        '          <div class="bloc-right">\n' +
        '            <div class="par-tner' + (data.empList && data.empList.length > 6 ? ' link' : '') + '">' + (data.empList && data.empList.length) + '位参与者</div>\n' +
        '            <p class="par-status">' + statusText + '</p>\n' +
        '          </div>\n' +
        '          <div class="par-show clearfix">' + cyzHtml +
        '          </div>\n' +
        '        </div>';
      //描述
      if (data.content) {
        _html += '<div class="rc-block">\n' +
          '          <i class="block-icon pen"></i>\n' +
          '          <div class="bloc-right">\n' +
          '            <p>' + (data.content ? ctrl.getALinkNew(data.content.replace(/\r|\n/g, "<br/>")) : '') + '</p>\n' +
          '          </div>\n' +
          '        </div>';
      }

      //本人日历才显示提醒
      if (ismyCale && data.remindStr) {
        _html += '<div class="rc-block">\n' +
          '          <i class="block-icon alarm"></i>\n' +
          '          <div class="bloc-right">\n' +
          '            <p>' + (data.remindStr.replace(/\;/g, ' | ') || '暂无提醒') + '</p>\n' +
          '          </div>\n' +
          '        </div>';
      }
      //非本人日历，展示日历归属人
      if (!ismyCale) {
        _html += '<div class="rc-block gsr-block">\n' +
          '  <i class="block-icon cale"></i>\n' +
          '   <div class="bloc-right">\n' +
          '     <p>' + _this.opts.workerName + '</p>\n' +
          '   </div>\n' +
          '</div>';
      }
      // _html +='<div class="rc-block">\n' +
      // '          <i class="block-icon detail"></i>\n' +
      // '          <div class="bloc-right">\n' +
      // '            <p>详情内容</p>\n' +
      // '            <p class="c666 mt2">快乐基金编号:\n' +
      // '              KLJ-112021070305010341992\n' +
      // '              KLJ-3320210703094919344</p>\n' +
      // '          </div>\n' +
      // '        </div>\n' +
      // '        <div class="rc-block">\n' +
      // '            <i class="block-icon time"></i>\n' +
      // '            <div class="bloc-right">\n' +
      // '              <p>计划完成时间：2021-08-30  23:59:59</p>\n' +
      // '              <a class="blue mt10" href="javascript:;">查看详情</a>\n' +
      // '            </div>\n' +
      // '          </div>';
      _html += '</div>' + feedHtml +
        '      <div class="partner-shows" style="display: none">\n' +
        '        <div class="return-head">\n' +
        '          <a class="p-ret-icon" href="javascript:;"></a>\n' +
        '        </div>\n' +
        '        <div class="">\n' +
        '          <p class="p-s-t">' + (data.empList && data.empList.length) + '位参与者</p>\n' +
        '          <p class="p-s-sub">' + statusText + '</p>\n' +
        '        </div>\n' +
        '        <div class="pa-ner-bo">\n' + allCyzHtml +
        '        </div>\n' +
        '      </div>\n' +
        '    </div>'
      if (this.opts.parentObj) {
        $(this.opts.parentObj).append(_html);
      } else {
        $("body").append(_html);
      }

      var styleStr = '';
      var modalHieght = $(_this.modalObj).find(".rc-det-modal").height();
      if (_this.opts.x && _this.opts.y) {
        var x = _this.opts.x;
        var y = _this.opts.y;
        var bodyWidth = $("body").width();
        var bodyHeight = $("body").height();
        // x不能大于980 - (364 + 80) = 536  y不能大于 680- (maxHeight + 40)
        // var maxHeight = 680- (parseInt(modalHieght) + 40);
        var maxX = bodyWidth - (364 + 80)
        var maxY = bodyHeight - (parseInt(modalHieght) + 40)
        if (x < 180) {
          x = 180;
        }
        if (x > maxX) {
          x = maxX;
        }
        if (y < 40) {
          y = 40;
        }
        if (y > maxY) {
          y = maxY;
        }
        styleStr = 'left:' + x + 'px;top:' + y + 'px;'
      }
      $(_this.modalObj).find(".rc-det-modal").attr("style", styleStr)
      $(_this.modalObj).on("click", ".rc-det-main .par-show .pant-one>span,.partner-shows .pa-ner-bo .pant-one>span", function (e) {
        e.stopPropagation();
        store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: e.currentTarget.dataset.account, selUserElm: e}});
      })
      this.bindDetailHtml()
      // judgeDetailBusyList(_this.tempDetailObj)
    },

    bindEditHtml: function () {
      var _this = this;
      var schObj = $(_this.modalObj);
      // $("#main #left").on("click",function (){
      //   _this.closeModal("cancel")
      // })
      $("#main #left").on("click", function () {
        $("#scheduleDateDiv").remove();
        $(".sch-select-list").remove();
      })
      if (!_this.opts.parentObj) {
        $("#main #left").on("click", function () {
          if (!_this.opts.parentObj) {
            _this.closeModal("cancel")
          }
        })
      }

      schObj.on("click", ".rc-close", function () {
        _this.closeModal("cancel")
      })

      schObj.on("click", ".cancel-btn", function () {
        _this.closeModal("cancel")
      })

      schObj.on("click", ".date-input", function () {
        _this.datepicker($(this).find(".prefix"));
        $(this).find(".prefix").select();
      }).on("click", ".suffix", function () {
        $(this).select()
        _this.timeSelect(this, _this.modalObj, _this.tempDetailObj);
      });

      //点击阴影处关闭弹窗
      // schObj.on("click",function(){
      //   _this.closeModal("cancel")
      // })

      schObj.on("click", ".rc-modal", function (event) {
        event.stopPropagation()
        if ($(event.target).parents(".date-input, .dateDiv").length == 0) {
          $("#scheduleDateDiv").remove();
        }
        if ($(event.target).parents(".time-input, .sch-select-list, .select-block").length == 0) {
          $(".sch-select-list").remove();
          $("body").off("click", "#selectList li");
          $("body").off("click", "#empselectList li");
        }
      })

      //复选框点击
      schObj.on("click", ".icheckbox_square input", function () {
        var isChecked = $(this).prop("checked");
        if (isChecked) {
          $(this).parent(".icheckbox_square").addClass("checked")
        } else {
          $(this).parent(".icheckbox_square").removeClass("checked")
        }
      })

      schObj.on("focus", ".tc-textarea", function () {
        $(this).addClass("open");
      })

      schObj.on("blur", ".tc-textarea", function () {
        if ($(this).val() == '') {
          $(this).removeClass("open")
        }
      })

      schObj.find('.tc-inp-count').each(function () {

        if ($(this).val() != '') {
          this.setAttribute('style', 'height:' + (parseInt(this.scrollHeight > 78 ? (this.scrollHeight + 16) : 78) - 10) + 'px;overflow-y:hidden;');
        } else {
          // this.setAttribute('style', 'height: 30px;');
        }
      }).on('input', function () {
        if ($(this).val() != '') {
          $(this).css("height", "auto")
          $(this).scrollTop(0)
          $(this).height(parseInt($(this)[0].scrollHeight > 78 ? ($(this)[0].scrollHeight + 16) : 78) - 10)
        } else {
          this.setAttribute('style', 'height: 30px;');
        }

        $(this).val($(this).val().substring(0, 200));
        $(this).siblings(".count-num").find(".num").html($(this).val().length);
      });

      // schObj.on("input",".tc-inp-count",function (){
      //   $(this).val($(this).val().substring(0,200))
      //   $(this).siblings(".count-num").find(".num").html($(this).val().length)
      // })

      schObj.on("click", ".dayAll", function () {
        var isChecked = $(this).prop("checked");
        if (isChecked) {
          $(this).parents("span").siblings(".time-input").hide()
        } else {
          $(this).parents("span").siblings(".time-input").show()
          //全天改为非全天，时间改为后半小时
          var now = new Date();
          if (now.getMinutes() >= 30) {
            now.setHours(now.getHours() + 1);
            now.setMinutes(0);
          } else {
            now.setMinutes(30);
          }
          var start_time_str = ('00' + now.getHours()).slice(-2) + ":" + ('00' + now.getMinutes()).slice(-2);
          $(_this.modalObj).find(".time-start").val(start_time_str);

          if (now.getHours() >= 23 && now.getMinutes() == 30) {
            now.setDate(now.getDate() + 1);
            now.setHours(0);
            now.setMinutes(0);
          } else if (now.getHours() >= 23 && now.getMinutes() == 0) {
            now.setMinutes(30);
          } else if (now.getMinutes() == 30) {
            now.setHours(now.getHours() + 1);
            now.setMinutes(0);
          } else {
            now.setMinutes(30);
          }
          var end_time_str = ('00' + now.getHours()).slice(-2) + ":" + ('00' + now.getMinutes()).slice(-2);
          $(_this.modalObj).find(".time-end").val(end_time_str);
        }
        _this.alarmChange()
        _this.judgeFormBusyList()
        _this.changeDateVal($(_this.modalObj).find(".schedule-date"))
      })

      //会议室自定义
      schObj.on("click", "#custCheckbox", function () {
        var isChecked = $(this).prop("checked");
        if (isChecked) {
          $("#addHys").prop("disabled", "disabled")
          $(this).parents(".hys-block").addClass("cust")
        } else {
          $("#addHys").prop("disabled", "")
          $(this).parents(".hys-block").removeClass("cust")
        }
        $(".hys-block .red-tip").hide()
      })
      //会议室删除
      schObj.on("click", ".hys-block .hys-sel-list .hys-sel .blue-del", function () {
        $(this).parent(".hys-sel").remove()
      })
      //时间编辑
      schObj.on("input", ".time-input .suffix", function () {
        var val = $(this).val().replace(/[^\d:：]/, '');
        val = val.replace(/[：]/, ':');
        $(this).val(val);
        _this.changeDateVal($(this).parents(".schedule-date"))
      })

      //搜索关联人或群
      //允许搜索人员状态
      var empSearchFlag = true;
      var empSearchTimer = null;
      schObj.on("click", ".empOrGroupSearch", function () {
        $(".sch-select-list").remove();
      })
      schObj.on("compositionstart", ".empOrGroupSearch", function () {
        empSearchFlag = false;
      })
      schObj.on("compositionend", ".empOrGroupSearch", function () {
        empSearchFlag = true;
      })
      schObj.on("input", ".empOrGroupSearch", function () {
        var that = this;
        if (empSearchTimer) {
          clearTimeout(empSearchTimer);
          empSearchTimer = "";
        }
        empSearchTimer = setTimeout(function () {
          if (empSearchFlag) {
            var listObj = {}
            var key = $(that).val()
            if ($.trim(key) == '') {
              $("body").off("click", "#empselectList .use-li");
              $(".sch-select-list").remove();
              return;
            }
            var tempTeamList = _this.filterTeamList(key);
            if (tempTeamList.length) {
              var teamList = [];
              var groupList = [];
              tempTeamList.forEach(function (v, i) {
                //是否是讨论组
                if (ctrl.getTeam(v.teamId).detailType == "group") {
                  groupList.push(v);
                } else {
                  teamList.push(v);
                }
              })
              listObj.team = teamList;
              listObj.group = groupList;
            }
            _this.searchEmp(key, that, listObj)
          }
        }, 100)
      })

      schObj.on("click", ".eg-selected-list .group-li .gro-name", function () {
        $(this).parents(".group-li").toggleClass("open")
      })
      schObj.on("click", ".eg-selected-list .group-li .group-head .arr-icon", function () {
        $(this).parents(".group-li").toggleClass("open")
      })

      schObj.on("click", "#addHys", function () {
        //日期
        //是否全天 0-否 1-是
        var dayType = schObj.find(".dayAll").prop("checked") ? 1 : 0;
        //时间
        var startDate = schObj.find("input[name='startDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
        var endDate = schObj.find("input[name='endDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
        var startTime = schObj.find("input[name='startTime']").val();
        var endTime = schObj.find("input[name='endTime']").val();
        var startTimeStr = (dayType ? (startDate + ' 00:00') : (startDate + ' ' + startTime));
        var endTimeStr = (dayType ? (endDate + ' 23:59') : (endDate + ' ' + endTime));
        if (!dayType && (startTime == '' || endTime == '')) {
          ctrl.toast({title: '请完善日程时间', type: 3});
          return;
        } else if (!dayType && (!(/^\d{1,2}:\d{2}$/.test(startTime)) || !(/^\d{1,2}:\d{2}$/.test(endTime)))) {
          ctrl.toast({title: '请输入正确的时间格式', type: 3});
          return;
        } else if (startTime.split(":")[0] > 23 || startTime.split(":")[1] > 59 || endTime.split(":")[0] > 23 || endTime.split(":")[1] > 59) {
          ctrl.toast({title: '请输入正确的时间格式', type: 3});
          return;
        } else if (judgeTime(startTimeStr, endTimeStr)) {
          ctrl.toast({title: '开始时间请勿大于结束时间', type: 3});
          return;
        }

        getRoomListAjax({
          startTime: startTimeStr,
          endTime: endTimeStr,
        }, function (result) {
          $(_this.modalObj).find(".add-hys").show()
          _this.fillHysHtml(result);
        })
      })

      schObj.on("click", ".add-hys .add-head .rc-title", function () {
        $(_this.modalObj).find(".add-hys").hide()
      })
      //添加提醒
      schObj.on("click", ".add-alarm", function () {
        var isAllday = $(_this.modalObj).find(".dayAll").prop("checked");
        if (isAllday) {
          var alarmText = '当天08:00';
          var alarmVal = '5-8';
        } else {
          var alarmText = '提前5分钟';
          var alarmVal = '1-5';
        }
        var alarmHtml = '<div class="clearfix ala-line">\n' +
          '              <div class="fl select-block relative alarm-select">\n' +
          '                <input class="rc-input select-show" type="text" value="' + alarmText + '" readonly style="width: 167px;" autocomplete="off">\n' +
          '                <input class="rc-input select-val" type="hidden" style="width: 167px;" value="' + alarmVal + '" autocomplete="off">\n' +
          '                <i class="check-arrow"></i>\n' +
          '              </div>\n' +
          '              <div class="fl">\n' +
          '                <i class="alarm-del"></i>\n' +
          '              </div>\n' +
          '            </div>'
        $(this).parent().before(alarmHtml);
        if ($(_this.modalObj).find(".ala-line").length >= 10) {
          $(_this.modalObj).find(".alarm-act").hide()
        }
      })

      schObj.on("click", ".ala-line .alarm-del", function () {
        $(this).parents(".ala-line").remove();
        if ($(_this.modalObj).find(".ala-line").length < 10) {
          $(_this.modalObj).find(".alarm-act").show()
        }
      })

      schObj.on("click", ".alarm-select", function () {
        var isAllday = $(_this.modalObj).find(".dayAll").prop("checked");
        if (isAllday) {
          _this.commonSelect($(this)[0], alarmList2)
        } else {
          _this.commonSelect($(this)[0], alarmList1)
        }
      })

      //保存日程
      schObj.on("click", ".schedule-submit", function () {
        _this.saveSchedule(_this.opts.type == 2 ? _this.opts.id : '')
      })

      schObj.on("mouseover", ".make-black-tip", function () {
        var text = $(this).attr("data-text");
        showBlackTip(this, text, _this.modalObj);
      })
      schObj.on("mouseout", ".make-black-tip", function () {
        schObj.find(".over-black-tip").remove()
      })

      //人员删除
      schObj.on("click", ".eg-selected-list .emp-li .del-e-icon", function () {
        $(this).parents(".emp-li").remove();
        _this.countRelatedNum();
      })
      //群组删除
      schObj.on("click", ".eg-selected-list .group-li .del-e-icon", function () {
        $(this).parents(".group-li").remove();
        _this.countRelatedNum();
      })
      //群组展开
      schObj.on("click", ".eg-selected-list .group-li .extend-e-icon", function () {
        var $groupLi = $(this).parents(".group-li");
        var $egSelectedList = $groupLi.parents(".eg-selected-list");
        $groupLi.find(".emp-li .del-e-icon").show();
        // 已经选中的人员id组合
        var empnos = []
        $egSelectedList.find(">.emp-li").each(function (index, e) {
          empnos.push($(e).data("empnumber"));
        })
        // 过滤完成的节点
        var extendEmps = [];
        $groupLi.find(".child-list .emp-li").each(function (index, e) {
          if (empnos.indexOf($(e).data("empnumber")) == -1) {
            extendEmps.push(e.outerHTML)
          }
        })
        $groupLi.replaceWith(extendEmps.join(""))
        _this.countRelatedNum();
      })
    },
    bindDetailHtml: function () {
      var _this = this;
      var schObj = $(_this.modalObj);
      // $("#main #left").on("click",function (){
      //   _this.closeModal("cancel")
      // })
      $("#main #left").on("click", function () {
        $("#scheduleDateDiv").remove();
        $(".sch-select-list").remove();
      })
      if (!_this.opts.parentObj) {
        $("#main #left").on("click", function () {
          if (!_this.opts.parentObj) {
            _this.closeModal("cancel")
          }
        })
      }
      //点击阴影处关闭弹窗
      schObj.on("click", function () {
        _this.closeModal("cancel")
        store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
      })
      schObj.on("click", ".rc-det-modal", function (event) {
        event.stopPropagation()
        store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
      })
      schObj.on("click", ".partner-block .par-tner.link", function () {
        schObj.find(".partner-shows").show()
      })

      schObj.on("click", ".partner-shows .p-ret-icon", function () {
        schObj.find(".partner-shows").hide()
      })

      schObj.on("click", ".rc-det-btns .det-btn:not(.checked)", function () {
        var status = $(this).attr("data-status");
        _this.changeScheduleStatus(status);
      })

      //关闭弹窗
      schObj.on("click", ".rc-acts-list .close-icon", function () {
        _this.closeModal("cancel");
      })
      //编辑日程
      schObj.on("click", ".rc-acts-list .edit-icon", function () {
        $(_this.modalObj).remove();
        var data = {
          id: _this.opts.id,
          workerId: _this.opts.workerId,
        }
        getScheduleDetailAjax(data, function (data) {
          //暂存
          _this.tempDetailObj = data;
          _this.opts.type = 2;
          _this.opts.x = "";
          _this.opts.y = "";
          _this.fillFormHtml(data)
        })
      })

      //转备忘
      schObj.on("click", ".rc-acts-list .bw-icon", function () {
        addMemoAjax({
          type: 1,
          workerId: curUserInfo.workerId,
          memotext: '【日程】' + _this.tempDetailObj.title
        }, function () {
          ctrl.toast({title: '转备忘成功', type: 1});
        })
      })

      //分享日程
      schObj.on("click", ".rc-acts-list .share-icon", function () {
        let thisMsg = {
          id: _this.tempDetailObj.id,
          forwardContent: '[日程邀约]' + ((_this.tempDetailObj.title || '').trim() || '无主题'),
          forwardType: "scheduleInvite",
          type: "custom",
        }
        openForward(thisMsg);
      })

      // 一键建群
      schObj.on("click", ".build-group", function () {
        if ($(this).data("id")) {
          $(_this.modalObj).remove();
          $(_this.modalObj).children().off();
          ctrl.openChatBox(_this.tempDetailObj.groupId, "team");
          return;
        }
        if (_this.tempDetailObj.groupId) {
          ctrl.nim.applyTeam({
            teamId: _this.tempDetailObj.groupId,
          }).then(res => {
            let {err, obj} = res;
            if (err) {
              console.log("modal-applyTeam", err);
              if (err.code == "803") {
                _this.createGroup();
              } else {
                ctrl.toast({title: err.message || '入群失败请重试', type: 2});
              }
              return;
            }
            ctrl.loading();
            setTimeout(function () {
              $(_this.modalObj).remove();
              $(_this.modalObj).children().off();
              ctrl.openChatBox(_this.tempDetailObj.groupId, "team");
              ctrl.loading().hide();
            }, 2000);
          });
          return;
        }
        _this.createGroup();
      })

      //删除日程
      schObj.on("click", ".rc-acts-list .del-icon", function () {
        _this.delSchedule()
      })
      schObj.on("click", ".rc-acts-list .l-del", function () {
        _this.delSchedule()
      })
      //转让日程
      schObj.on("click", ".rc-acts-list .l-zr", function () {
        var zrHtml = '<div class="schedule-modal-zr">' +
          '<div class="mr10" style="color: #666;line-height: 30px;">新组织者:</div>' +
          '<div class="">' +
          '   <div><input class="emp-input" style="width: 266px;" type="text" placeholder="搜索新的组织者" autocomplete="off"></div>' +
          '   <div style="color: #999;line-height: 17px;margin-top: 6px;font-size: 10px;">转让后，原组织者将被替换</div>' +
          '</div>' +
          '</div>';
        alertObj = alertBox({
          title: '转让日程',
          content: zrHtml,
          isClose: false,
          on: function (elm) {
            var $elm = $(elm);
            var searchFlag = true;
            var searchTimer;
            $elm.on("compositionstart", ".emp-input", function () {
              searchFlag = false;
            })
            $elm.on("compositionend", ".emp-input", function () {
              searchFlag = true;
            })
            $elm.on("input", ".emp-input", function () {
              var that = this;
              if (searchTimer) {
                clearTimeout(searchTimer);
                searchTimer = "";
              }
              searchTimer = setTimeout(function () {
                var listObj = {}
                var key = $(that).val()
                if ($.trim(key) == '') {
                  $("body").off("click", "#empselectList .use-li");
                  $(".sch-select-list").remove();
                  return;
                }
                $(that).attr("data-empnumber", "")
                ctrl.coaSdk.selSearchApi({
                  msgBody: JSON.stringify({
                    workerId: ctrl.userInfo.workerId,
                    name: key,
                    status: "1",//默认为全部1在职2为离职
                    page: 1,
                    rows: 50
                  })
                }).then(function (res) {
                  if (res.success && res.data && res.data.empList && res.data.empList.length) {
                    var empList = [];
                    for (var i = 0; i < res.data.empList.length; i++) {
                      //过滤离职人员,过滤林董
                      if (res.data.empList[i].empStatus != 4 && res.data.empList[i].empNumber != 88888888) {
                        empList.push(res.data.empList[i])
                      }
                    }
                    listObj.emp = empList;
                  }
                  _this.empOrGroupSearch(that, listObj, key, 2);
                }, function (err) {
                })
              }, 300)
            })
            $elm.on("blur", ".emp-input", function () {
              if (!$(this).attr("data-empnumber")) {
                $(this).addClass("err")
              } else {
                $(this).removeClass("err")
              }
            })
          },
          done: function (type) {
            if (type == 1) {
              var empNumber = $(".schedule-modal-zr .emp-input").attr("data-empnumber");
              if (!empNumber) {
                return;
              }
              transferScheduleAjax({
                id: _this.opts.id,
                toWorkerId: empNumber
              }, function () {
                ctrl.toast({title: '转让成功', type: 1});
                alertObj.hide();
                _this.closeModal("transfer");
              })
            } else {
              $(".sch-select-list").hide()
              alertObj.hide();
            }
          }
        })
      })

      schObj.on("mouseover", ".make-black-tip", function () {
        var text = $(this).attr("data-text");
        showBlackTip(this, text, _this.modalObj);
      })
      schObj.on("mouseout", ".make-black-tip", function () {
        schObj.find(".over-black-tip").remove()
      })
    },
    judgeShadow: function () {
      var modalHieght = $(this.modalObj).find(".rc-contain").height();
      if (modalHieght > 444) {
        $(this.modalObj).find(".rc-footer").addClass("shadow");
      } else {
        $(this.modalObj).find(".rc-footer").removeClass("shadow");
      }
    },
    searchEmp: function (key, that, listObj) {
      var _this = this;
      if (this.seachEmpTimer) {
        clearTimeout(this.seachEmpTimer);
        this.seachEmpTimer = "";
      }
      this.seachEmpTimer = setTimeout(function () {
        key = $(_this.modalObj).find(".empOrGroupSearch").val();
        ctrl.coaSdk.selSearchApi({
          msgBody: JSON.stringify({
            workerId: ctrl.userInfo.workerId,
            name: key,
            status: "1",//默认为全部1在职2为离职
            page: 1,
            rows: 50
          })
        }).then(function (res) {
          _this.seachEmpTimer = ''
          if (res.success && res.data && res.data.empList && res.data.empList.length) {
            var empList = [];
            for (var i = 0; i < res.data.empList.length; i++) {
              //过滤离职人员,过滤林董
              if (res.data.empList[i].empStatus != 4 && res.data.empList[i].empNumber != 88888888) {
                empList.push(res.data.empList[i])
              }
            }
            listObj.emp = empList;
          }
          if ($(_this.modalObj).find(".empOrGroupSearch").val() == '') {
            $("body").off("click", "#empselectList .use-li");
            $(".sch-select-list").remove();
            return;
          }
          _this.empOrGroupSearch(that, listObj, key, 1);
        }, function (err) {
        })
      }, 300)

    },
    delSchedule: function () {
      var _this = this;
      alertObj = alertBox({
        title: '删除日程',
        content: '<div style="padding: 28px 12px 24px;text-align: center;">是否确认删除日程？<br>删除后，将彻底从你的日历中移除</div>',
        isClose: false,
        on: function () {},
        done: function (type) {
          if (type == 1) {
            var data = {
              id: _this.opts.id,
              workerId: curUserInfo.workerId
            }
            deleteScheduleAjax(data, function () {
              alertObj.hide();
              ctrl.toast({title: '删除成功', type: 1});
              _this.closeModal("delete");
            })
          } else {
            alertObj.hide();
          }
        }
      })
    },
    //填充会议室
    fillHysHtml: function (list) {
      var _this = this;
      var useList = [];
      var choosedList = _this.opts.type == 2 && _this.tempDetailObj.roomIds ? _this.tempDetailObj.roomIds.split(",").map(function (v, i) {
        return v.split(":")[0];
      }) : [];
      $(".hys-block .hys-sel-list .hys-sel").each(function (i, v) {
        useList.push($(v).attr("data-id"));
      })
      var isTimeChanged = _this.isTimeChanged();
      var hysHtml = list.map(function (v, i) {
        //时间修改了，会议室根据接口返回展示繁忙；时间没有修改时，若已选择的会议室繁忙不显示繁忙
        var infoList = v.reserveInfoModelList || [];
        //只有一条预定信息，并且该预定信息为组织者，并且详情有预定这个会议室
        // var isOrdered = false;
        // if(_this.opts == 2 && infoList.length && infoList.length == 1 && infoList[0].scheduledEmpNumber == _this.tempDetailObj.organizerNumber && choosedList.includes(v.siteId)){
        //   isOrdered = true;
        // }
        //详情有预定这个会议室，这个会议室已被预定
        //繁忙列表大于1 繁忙
        //繁忙列表为1  预定人不是自己 繁忙
        //繁忙列表为1  预定人是自己  详情有该会议室 预定时间
        // var isBusy = !choosedList.includes(v.siteId) && infoList.length; //不是自己选择的，展示繁忙状态
        var isBusy = infoList.length; //有数据直接展示繁忙状态
        return '<div class="hys-li' + (isBusy ? ' busy' : '') + (!isBusy && useList.includes(v.siteId) ? ' added' : '') + '" data-id="' + v.siteId + '" data-name="' + v.siteName + '">\n' +
          '            <i class="hys-icon"></i>\n' +
          '            <span class="room-name">' + v.siteName + '</span><span class="blue use">已添加</span><span class="busy-status">该时段繁忙</span>\n' +
          '          </div>'
      })

      $(".add-hys .hys-list").html(hysHtml);
      $(".add-hys .hys-list .hys-li:not(.added):not(.busy)").on("click", function () {
        var siteId = $(this).attr("data-id");
        var siteName = $(this).attr("data-name");
        var hysTemp = '<div class="hys-sel" data-id="' + siteId + '" data-name="' + siteName + '">\n' +
          '                <i class="blue-del"></i>\n' +
          '                <span>' + siteName + '</span>\n' +
          '              </div>';
        $(".hys-block .hys-sel-list").append(hysTemp);
        $(this).addClass("added");
        $(".hys-block .red-tip").hide()
        $(_this.modalObj).find(".add-hys").hide()
      })
    },
    initTimeStr: function () {
      var _this = this;
      var now = new Date();
      if (now.getMinutes() >= 30) {
        now.setHours(now.getHours() + 1);
        now.setMinutes(0);
      } else {
        now.setMinutes(30);
      }
      var start_date_str = now.getFullYear() + "-" + ('00' + (now.getMonth() + 1)).slice(-2) + "-" + ('00' + now.getDate()).slice(-2);
      var start_time_str = ('00' + now.getHours()).slice(-2) + ":" + ('00' + now.getMinutes()).slice(-2);
      $(_this.modalObj).find(".date-start").val(start_date_str);
      $(_this.modalObj).find(".time-start").val(start_time_str);

      if (now.getHours() >= 23 && now.getMinutes() == 30) {
        now.setDate(now.getDate() + 1);
        now.setHours(0);
        now.setMinutes(0);
      } else if (now.getHours() >= 23 && now.getMinutes() == 0) {
        now.setMinutes(30);
      } else if (now.getMinutes() == 30) {
        now.setHours(now.getHours() + 1);
        now.setMinutes(0);
      } else {
        now.setMinutes(30);
      }

      var end_date_str = now.getFullYear() + "-" + ('00' + (now.getMonth() + 1)).slice(-2) + "-" + ('00' + now.getDate()).slice(-2);
      var end_time_str = ('00' + now.getHours()).slice(-2) + ":" + ('00' + now.getMinutes()).slice(-2);
      $(_this.modalObj).find(".date-end").val(end_date_str);
      $(_this.modalObj).find(".time-end").val(end_time_str);
    },
    //搜索群组
    filterTeamList: function (key) {
      var _this = this;
      var teamList = ctrl.getTeam();
      var newTeamList = []
      teamList.forEach(function (v, i) {
        if (v.name.indexOf(key) != -1 && v.memberNum < limitPerson) {
          newTeamList.push(v)
        }
      })
      return newTeamList;
    },
    //添加提醒默认值变化
    alarmChange: function () {
      var _this = this;
      var isAllday = $(_this.modalObj).find(".dayAll").prop("checked");
      var showObj = $(".alarm-select .select-show");
      var valObj = $(".alarm-select .select-val");
      if (isAllday) {
        showObj.val("当天08:00");
        valObj.val(alarmList2[0].value);
      } else {
        showObj.val("提前5分钟");
        valObj.val(alarmList1[1].value);
      }
      $(_this.modalObj).find(".ala-line:gt(0)").remove();
      $(_this.modalObj).find(".alarm-act").show();
    },
    //下拉框控件
    commonSelect: function (obj, listObj) {
      //解除点击事件
      $("body").off("click", "#selectList li");
      $(".sch-select-list").remove();
      var selectHtml;
      var width = $(obj).outerWidth();
      var x = $(obj).offset().left;
      var y = $(obj).offset().top + 31;
      var val = $(obj).find(".select-val").val();
      var index = 0;
      selectHtml = '<ul class="sch-select-list" id="selectList" style="width: ' + width + 'px;position:absolute;left:' + x + 'px;top:' + y + 'px;z-index:51;">';
      for (var i = 0; i < listObj.length; i++) {
        selectHtml += '<li data-val="' + listObj[i].value + '">' + listObj[i].text + '</li>';
        if (val == listObj[i].value) {
          index = i;
        }
      }
      selectHtml += '</ul>';
      $("body").append(selectHtml);
      //下拉框高度溢出乐聊框外，控制高度减短
      var bodyHeight = $("body").height();
      var maxTop = bodyHeight - 200;
      if ($("#selectList").offset().top > maxTop) {
        var latop = $("#selectList").offset().top - maxTop
        $("#selectList").css("max-height", 180 - latop + 'px')
      }
      var liIndex = $("#selectList").find("li").eq(index);
      var top = liIndex.offset().top - y;
      $("#selectList").scrollTop(top);
      liIndex.addClass("curr")

      //绑定点击事件
      $("body").on("click", "#selectList li", function () {
        $(obj).find(".select-show").val($(this).text());
        $(obj).find(".select-val").val($(this).attr("data-val"));
        $("#selectList").remove();
        //解除点击事件
        $("body").off("click", "#selectList li");
      });
    },
    //搜索群人员和群组
    empOrGroupSearch: function (obj, listObj, keyword, type) {
      var _this = this;
      //解除点击事件
      $("body").off("click", "#empselectList .use-li");
      $(".sch-select-list").remove();
      var selectHtml;
      var width = $(obj).outerWidth();
      var x = $(obj).offset().left;
      var y = $(obj).offset().top + 31;
      var val = $(obj).val();

      selectHtml = '<ul class="sch-select-list" id="empselectList" style="width: ' + width + 'px;position:absolute;left:' + x + 'px;top:' + y + 'px;z-index:101;">';

      if (listObj.emp && listObj.emp.length) {
        selectHtml += '<li class="eg-tit">联系人</li>';
        for (var i = 0; i < listObj.emp.length; i++) {
          var titHtml = '<span>' + _this.getHilightStr(listObj.emp[i].empName, keyword) + '&nbsp;&nbsp;</span><span class="emp-li-inner-deptName">' + _this.getHilightStr(listObj.emp[i].deptName, keyword) + '</span>';
          selectHtml += '<li class="use-li" data-type="emp" data-empno="' + listObj.emp[i].empNo + '" data-empnumber="' + listObj.emp[i].empNumber + '">' + titHtml + '</li>';
        }
      }

      if (listObj.team && listObj.team.length) {
        selectHtml += '<li class="eg-tit">群组（' + limitPerson + '人以内）</li>';
        for (var i = 0; i < listObj.team.length; i++) {
          var titHtml = _this.getHilightStr(listObj.team[i].name, keyword);
          selectHtml += '<li class="use-li" data-type="team" data-length="' + listObj.team[i].memberNum + '" data-teamid="' + listObj.team[i].teamId + '">' + titHtml + '</li>';
        }
      }

      if (listObj.group && listObj.group.length) {
        selectHtml += '<li class="eg-tit">讨论组（' + limitPerson + '人以内）</li>';
        for (var i = 0; i < listObj.group.length; i++) {
          var titHtml = _this.getHilightStr(listObj.group[i].name, keyword);
          selectHtml += '<li class="use-li" data-type="team" data-length="' + listObj.group[i].memberNum + '" data-teamid="' + listObj.group[i].teamId + '">' + titHtml + '</li>';
        }
      }

      if ((!listObj.emp || !listObj.emp.length) && !listObj.team && !listObj.group) {
        selectHtml += '<li class="empty-li">暂无相关数据</li>';
      }
      selectHtml += '</ul>';
      $("body").off("click", "#scheduleDateDiv tr td");
      $("body").off("click", "#scheduleDateDiv thead .date-prev");
      $("body").off("click", "#scheduleDateDiv thead .date-next");
      $("body").find("#scheduleDateDiv").remove();
      if (!$(this.modalObj).find(".add-hys").length || $(this.modalObj).find(".add-hys").css("display") == 'none') {
        $("body").append(selectHtml);
        this.bindEmpselect(obj, type)
      }
    },
    bindEmpselect: function (obj, type) {
      //绑定点击事件
      var _this = this;
      $("body").on("click", "#empselectList .use-li", function () {
        var liType = $(this).attr("data-type");
        var text = $(this).text();
        var innerHtml = $(this).html();
        var empNumber = $(this).attr("data-empnumber");
        var empNo = $(this).attr("data-empno");
        //从转让日程搜索人员过来
        if (type == 2) {
          if (liType == 'emp') {
            $(obj).val(text);
            $(obj).attr("data-empnumber", empNumber)
            $(obj).removeClass("err")
          }
          $("#empselectList").remove();
          //解除点击事件
          $("body").off("click", "#empselectList .use-li");
          return;
        }
        //从搜索关联人过来
        var length = $(".eg-selected-list .emp-li").length;
        //第一次添加其他人员或群组，默认本人进参与者
        if (!length && empNumber != curUserInfo.workerId) {
          var empHtml = '<div class="emp-li" data-empNo="' + curUserInfo.workerNo + '" data-empnumber="' + curUserInfo.workerId + '">\n' +
            '<span>' + '<span>' + curUserInfo.name + '&nbsp;&nbsp;' + '</span>' + '<span class="emp-li-inner-deptName">' + curUserInfo.deptName + '</span>' + '</span><i class="org-icon make-black-tip" data-text="组织者"></i>\n' +
            '<a class="del-e-icon" href="javascript:;"></a>\n' +
            '</div>'
          $(obj).siblings(".eg-selected-list").append(empHtml)
        }
        //选择人员
        if (liType == 'emp') {
          var isUsed = false;
          //已添加过不重复添加
          $(".eg-selected-list>.emp-li").each(function (ii, pp) {
            if ($(pp).attr("data-empnumber") == empNumber) {
              isUsed = true;
              return;
            }
          })
          if (!isUsed) {
            var orgHtml = '';
            var busyHtml = '';
            if ((_this.opts.type == 1 && empNumber == curUserInfo.workerId) || (_this.opts.type == 2 && empNumber == _this.tempDetailObj.organizerNumber)) {
              orgHtml = '<i class="org-icon make-black-tip" data-text="组织者"></i>'
            }
            if (0) {
              busyHtml = '<i class="busy-icon make-black-tip" data-text="该时段繁忙"></i>'
            }
            var empHtml = '<div class="emp-li" data-empno="' + empNo + '" data-empnumber="' + empNumber + '">\n' +
              '<span>' + innerHtml + '</span>\n' + orgHtml + busyHtml +
              '<a class="del-e-icon" href="javascript:;"></a>\n' +
              '</div>'
            $(obj).siblings(".eg-selected-list").prepend(empHtml)
          }
          _this.judgeFormBusyList()
        } else {  //选择群组
          var isUsed = false;
          var teamId = $(this).attr("data-teamid");
          //已添加过不重复添加
          $(".eg-selected-list>.group-li").each(function (ii, pp) {
            if ($(pp).attr("data-teamid") == teamId) {
              isUsed = true;
              return;
            }
          })
          if (!isUsed) {
            _this.getTeamNumbers(teamId, function (list) {
              var empHtml = ''
              list.forEach(function (v, i) {
                if (new RegExp(ctrl.config.ai).test(v.workerNo)) {
                  return;
                }
                var orgHtml = '';
                var busyHtml = '';
                if ((_this.opts.type == 1 && v.workerId == curUserInfo.workerId) || (_this.opts.type == 2 && v.workerId == _this.tempDetailObj.organizerNumber)) {
                  orgHtml = '<i class="org-icon make-black-tip" data-text="组织者"></i>'
                }
                if (0) {
                  busyHtml = '<i class="busy-icon make-black-tip" data-text="该时段繁忙"></i>'
                }
                empHtml += ' <div class="emp-li" data-empno="' + v.workerNo + '" data-empnumber="' + v.workerId + '">\n' +
                  '<span>' + '<span>' + v.workerName + '&nbsp;&nbsp;' + '</span>' + '<span class="emp-li-inner-deptName">' + v.deptName + '</span>' + '</span>\n' + orgHtml + busyHtml +
                  '<a class="del-e-icon" style="display: none;" href="javascript:;"></a></div>';
              })
              var teamHtml = '<div class="group-li" data-teamid="' + teamId + '">\n' +
                '                <div class="group-head">\n' +
                '                  <i class="arr-icon"></i>\n' +
                '                  <span class="gro-name">' + text + '（' + list.length + '）</span>\n' +
                '                  <a class="del-e-icon" href="javascript:;"></a>\n' +
                '                  <a class="extend-e-icon" href="javascript:;"></a>\n' +
                '                </div>\n' +
                '                <div class="child-list">' + empHtml +
                '                </div>\n' +
                '              </div>'
              $(obj).siblings(".eg-selected-list").prepend(teamHtml);
              _this.countRelatedNum();
              _this.judgeFormBusyList()
            });
          }
        }
        $(obj).val("");
        $(obj).focus();
        _this.countRelatedNum();
        $("#empselectList").remove();

        //解除点击事件
        $("body").off("click", "#empselectList .use-li");
      });
    },
    //统计关联人个数
    countRelatedNum: function () {
      var length = $(".eg-selected-list .emp-li").length;
      if (length >= limitPerson) {
        // $("#empOrGroupSearch").prop("disabled","disabled")
      } else {
        // $("#empOrGroupSearch").prop("disabled","")
      }
      $(this.modalObj).find(".empOrGroupSearch").siblings(".count-num").find(".num").html(length);
    },
    //根据群组id获取群人员
    getTeamNumbers: function (teamId, callback) {
      ctrl.getTeamMembers(teamId).then(res => {
        callback(res);
      });
    },
    getHilightStr: function (str, key) {
      if (key == ".") {
        return str.replace(/\./g, '%%.%%').split('%%');
      }
      var titHtml = '';
      var strArr = str.replace(new RegExp(key, 'g'), '%%' + key + '%%').split('%%');
      strArr.forEach(function (p, j) {
        if (p == key) {
          titHtml += '<span class="light">' + p + '</span>'
        } else {
          titHtml += p
        }
      })
      return titHtml;
    },
    //判断时间是否改变了
    isTimeChanged: function () {
      var _this = this;
      //是否全天 0-否 1-是
      var dayType = $(_this.modalObj).find(".dayAll").prop("checked") ? 1 : 0;
      //时间
      var startDate = $(_this.modalObj).find("input[name='startDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
      var endDate = $(_this.modalObj).find("input[name='endDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
      var startTime = $(_this.modalObj).find("input[name='startTime']").val();
      var endTime = $(_this.modalObj).find("input[name='endTime']").val();
      var startTimeStr = (dayType ? (startDate + ' 00:00') : (startDate + ' ' + startTime));
      var endTimeStr = (dayType ? (endDate + ' 23:59') : (endDate + ' ' + endTime));
      if (!dayType && (startTime == '' || endTime == '')) {
        ctrl.toast({title: '请完善日程时间', type: 3});
        return;
      } else if (!dayType && (!(/^\d{1,2}:\d{2}$/.test(startTime)) || !(/^\d{1,2}:\d{2}$/.test(endTime)))) {
        ctrl.toast({title: '请输入正确的时间格式', type: 3});
        return;
      } else if (startTime.split(":")[0] > 23 || startTime.split(":")[1] > 59 || endTime.split(":")[0] > 23 || endTime.split(":")[1] > 59) {
        ctrl.toast({title: '请输入正确的时间格式', type: 3});
        return;
      } else if (judgeTime(startTimeStr, endTimeStr)) {
        ctrl.toast({title: '开始时间请勿大于结束时间', type: 3});
        return;
      }
      //日程时间是否有变更？
      var timeChanged = false;
      if (_this.tempDetailObj && _this.opts.type == 2) {
        var oldStartTimeStr = _this.tempDetailObj.startTimeStr.replace(/年|月/g, '-').replace(/日/g, '');
        var oldEndTimeStr = _this.tempDetailObj.endTimeStr.replace(/年|月/g, '-').replace(/日/g, '');
        if (startTimeStr && endTimeStr && (oldStartTimeStr != startTimeStr || oldEndTimeStr != endTimeStr)) {
          timeChanged = true;
        }
      }
      return timeChanged;
    },
    saveSchedule: function (id) {
      var _this = this;
      //是否是组织者
      var isOrg = _this.tempDetailObj && _this.tempDetailObj.organizerNumber == curUserInfo.workerId;
      if (!id || isOrg) {
        //日程主题
        var title = $(_this.modalObj).find("input[name='title']").val().replace(/(^\s*)|(\s*$)/g, "");
        //日期
        //是否全天 0-否 1-是
        var dayType = $(_this.modalObj).find(".dayAll").prop("checked") ? 1 : 0;
        //时间
        var startDate = $(_this.modalObj).find("input[name='startDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
        var endDate = $(_this.modalObj).find("input[name='endDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
        var startTime = $(_this.modalObj).find("input[name='startTime']").val();
        var endTime = $(_this.modalObj).find("input[name='endTime']").val();
        var startTimeStr = (dayType ? (startDate + ' 00:00') : (startDate + ' ' + startTime));
        var endTimeStr = (dayType ? (endDate + ' 23:59') : (endDate + ' ' + endTime));
        if (!dayType && (startTime == '' || endTime == '')) {
          ctrl.toast({title: '请完善日程时间', type: 3});
          return;
        } else if (!dayType && (!(/^\d{1,2}:\d{2}$/.test(startTime)) || !(/^\d{1,2}:\d{2}$/.test(endTime)))) {
          ctrl.toast({title: '请输入正确的时间格式', type: 3});
          return;
        } else if (startTime.split(":")[0] > 23 || startTime.split(":")[1] > 59 || endTime.split(":")[0] > 23 || endTime.split(":")[1] > 59) {
          ctrl.toast({title: '请输入正确的时间格式', type: 3});
          return;
        } else if (judgeTime(startTimeStr, endTimeStr)) {
          ctrl.toast({title: '开始时间请勿大于结束时间', type: 3});
          return;
        }

        //描述
        var videoContent = $(_this.modalObj).find("textarea[name='videoContent']").val().replace(/(^\s*)|(\s*$)/g, "");
        var content = $(_this.modalObj).find("textarea[name='content']").val().replace(/(^\s*)|(\s*$)/g, "");
      }
      //页面上的参与者
      var empNumbers = [];
      var empNos = [];
      $(_this.modalObj).find(".eg-selected-list .emp-li").each(function (i, v) {
        var empnumber = $(v).attr("data-empnumber");
        var empno = $(v).attr("data-empno");
        //过滤掉林董
        if (!empNumbers.includes(empnumber) && empnumber != 88888888) {
          empNumbers.push(empnumber);
          empNos.push(empno);
        }
      })
      if (empNumbers.length > limitPerson) {
        ctrl.toast({title: '目前暂不支持超' + limitPerson + '人日程', type: 3});
        return;
      }

      //编辑
      if (id) {
        var oldEmpList = _this.tempDetailObj.empList || [];
        var oldEmpNumbers = oldEmpList.map(function (v, i) {
          return v.empNumber
        });
        var delEmpNumbers = [];
        var addEmpNumbers = [];
        var addEmpnos = [];
        oldEmpNumbers.forEach(function (v, i) {
          if (!empNumbers.includes(v)) {  //旧的人员不在里面，代表已经删除
            delEmpNumbers.push(v)
          }
        })
        empNumbers.forEach(function (v, i) {
          if (!oldEmpNumbers.includes(v)) {  //新的人员不在旧的里面，代表是新增
            addEmpNumbers.push(v);
            addEmpnos.push(empNos[i]); //工号列表用来转发
          }
        })
        addEmpNumbers = addEmpNumbers.join(",");
        delEmpNumbers = delEmpNumbers.join(",");
      } else {  //新增
        if (!empNumbers.length) {
          empNumbers = curUserInfo.workerId;
        } else {
          empNumbers = empNumbers.join(",");
        }
      }
      //日程时间是否有变更？
      var timeChanged = false;
      if (_this.tempDetailObj && id) {
        var oldStartTimeStr = _this.tempDetailObj.startTimeStr.replace(/年|月/g, '-').replace(/日/g, '');
        var oldEndTimeStr = _this.tempDetailObj.endTimeStr.replace(/年|月/g, '-').replace(/日/g, '');
        if (startTimeStr && endTimeStr && (oldStartTimeStr != startTimeStr || oldEndTimeStr != endTimeStr)) {
          timeChanged = true;
        }
      }

      //会议室
      var customContent = '';
      if ($("#custCheckbox").prop("checked")) {
        customContent = $(_this.modalObj).find("textarea[name='custHys']").val().replace(/(^\s*)|(\s*$)/g, "");
      } else {
        var roomIdArr = [];
        var roomNameArr = [];

        $(".hys-sel-list .hys-sel").each(function (i, v) {
          var roomId = $(v).attr("data-id");
          var oldRoomIds = (_this.opts.type == 2 && _this.tempDetailObj && _this.tempDetailObj.roomIds) ? _this.tempDetailObj.roomIds.split(",") : [];
          if (oldRoomIds.length && !timeChanged) {
            for (var j = 0; j < oldRoomIds.length; j++) {
              if (roomId == oldRoomIds[j].split(":")[0]) {
                roomId = oldRoomIds[j];
              }
            }
          }
          roomIdArr.push(roomId);
          roomNameArr.push($(v).attr("data-name"));
        })
        var roomIds = roomIdArr.join(",");
        var roomNames = roomNameArr.join(",");
      }

      //提醒
      var remindJson = [];
      var remindVal = [];
      $(".ala-line").each(function (i, v) {
        var alVal = $(v).find(".select-val").val();
        if (!remindVal.includes(alVal)) {
          remindVal.push(alVal);
          remindJson.push({
            timeType: alVal.split('-')[0],
            time: alVal.split('-')[1]
          })
        }
      })

      //编辑
      if (id) {
        if (isOrg) {
          var data = {
            id: id,
            title: title ? utf16toEntities(title) : '',
            addParticipateIds: addEmpNumbers,
            delParticipateIds: delEmpNumbers,
            videoContent: videoContent ? utf16toEntities(videoContent) : '',
            content: content ? utf16toEntities(content) : '',
            customContent: customContent ? utf16toEntities(customContent) : '',
            dayType: dayType,
            startTimeStr: startTimeStr,
            endTimeStr: endTimeStr,
            roomIds: roomIds || '',
            roomNames: roomNames || '',
            remindJson: JSON.stringify(remindJson)
          }
        } else {
          var data = {
            id: id,
            addParticipateIds: addEmpNumbers,
            delParticipateIds: delEmpNumbers,
            remindJson: JSON.stringify(remindJson)
          }
        }

        //日程主题是否有变更？
        //是否有新增参与人员？
        var themeChanged = false;
        if (title && _this.tempDetailObj.title != utf16toEntities(title)) {
          themeChanged = true;
        }
        var alertText = '';
        var sType = 2;
        if (!isOrg && addEmpNumbers != "") {
          alertText = '<div style="text-align: center">是否将日程转发至各个新增的参与人员的<br>乐聊中？</div>';
          sType = 4;
        } else {
          if ((themeChanged || timeChanged) && addEmpNumbers == "" && empNumbers != curUserInfo.workerId) {  //参与者只有本人不提醒
            alertText = '<div style="text-align: center">是否向参与者发送日程更新通知？</div>'
            sType = 5;
          } else if ((!themeChanged && !timeChanged) && addEmpNumbers != "") {
            alertText = '<div style="text-align: center">是否将日程转发至各个新增的参与人员的乐聊中？</div>';
            sType = 4;
          } else if ((themeChanged || timeChanged) && addEmpNumbers != "") {
            alertText = '<div style="text-align: center">是否向新增参与者发送邀请，并向现有参与者发送日程更新提醒？</div>'
            sType = '4,5'
          }
        }

        function updateSchedule(data, alertText) {
          updateScheduleAjax(data, function (result) {
            // 自动入群
            if (addEmpNumbers.length > 0 && _this.tempDetailObj.groupId) {
              ctrl.nim.addTeamMembers({
                teamId: _this.tempDetailObj.groupId,
                accounts: result.participateNos.split(","),
              }).then(res => {
                console.log("updateScheduleAjax-addTeam", res);
              });
            }
            if (alertText) {
              alertObj = alertBox({
                title: '提示',
                content: alertText,
                isClose: false,
                done: function (type) {
                  if (type == 1) {
                    transmitScheduleAjax({
                      id: result.id,
                      toIds: addEmpnos.join(","),
                      type: sType,
                      asyncSend: true,
                      updateType: themeChanged && timeChanged ? '1,2' : (themeChanged ? '1' : (timeChanged ? '2' : ''))
                    }, function () {
                      ctrl.toast({title: "转发成功", type: 1});
                    })
                    alertObj.hide();
                    _this.closeModal("update");
                  } else {
                    alertObj.hide();
                    _this.closeModal("update");
                  }
                }
              })
            } else {
              ctrl.toast({title: "保存成功", type: 1});
              _this.closeModal("update");
            }
          }, function () {
            _this.updateHysList()
          })
        }

        // 时间改变且参与人大于1时提示
        var partLength = _this.tempDetailObj.participateNos.split(",").length + (addEmpNumbers.length > 0 ? addEmpNumbers.split(",").length : 0) - (delEmpNumbers.length > 0 ? delEmpNumbers.split(",").length : 0)
        if (timeChanged && partLength > 1) {
          alertBox({
            title: '提示',
            content: '<div style="padding:28px 0 24px;text-align: center">是否确认变更时间信息？变更后，原参与者反馈状态（接受/拒绝/待定）将被重置。</div>',
            done: function (type) {
              if (type == 1) {
                updateSchedule(data, alertText);
              }
            }
          })
        } else {
          updateSchedule(data, alertText)
        }
      } else {  //新增
        var data = {
          title: title ? utf16toEntities(title) : '',
          addParticipateIds: empNumbers,
          videoContent: videoContent ? utf16toEntities(videoContent) : '',
          content: content ? utf16toEntities(content) : '',
          customContent: customContent ? utf16toEntities(customContent) : '',
          dayType: dayType,
          startTimeStr: startTimeStr,
          endTimeStr: endTimeStr,
          roomIds: roomIds || '',
          roomNames: roomNames || '',
          remindJson: JSON.stringify(remindJson)
        }
        saveScheduleAjax(data, function (result) {
          if (empNumbers == curUserInfo.workerId) {
            ctrl.toast({title: "创建成功", type: 1});
            _this.closeModal("add");
          } else {
            alertObj = alertBox({
              title: '提示',
              content: '<div style="padding:28px 0 24px;text-align: center">是否将日程转发至各个参与人员的<br>乐聊中？</div>',
              isClose: false,
              done: function (type) {
                if (type == 1) {
                  var data = {
                    id: result.id,
                    type: 5,
                    asyncSend: true
                  }
                  transmitScheduleAjax(data, function () {
                    ctrl.toast({title: "转发成功", type: 1});
                  })
                  alertObj.hide();
                  _this.closeModal("add");
                } else {
                  ctrl.toast({title: "创建成功", type: 1});
                  alertObj.hide();
                  _this.closeModal("add");
                }
              }
            })
          }
        })
      }
    },
    //编辑日程后异常，会议室更新
    updateHysList: function () {
      var _this = this;
      var data = {
        id: this.opts.id,
        workerId: this.opts.workerId,
      }
      getScheduleDetailAjax(data, function (data) {
        //暂存
        _this.tempDetailObj.roomIds = data.roomIds || '';
        _this.tempDetailObj.roomNames = data.roomNames || '';

        var hysHtml = '';
        if (data && data.roomIds) {
          var roomIdArr = data.roomIds.split(",");
          var roomNameArr = data.roomNames.split(",");
          hysHtml = roomIdArr.map(function (v, i) {
            var roomId = v.split(":")[0];
            return '<div class="hys-sel" data-id="' + roomId + '" data-name="' + roomNameArr[i] + '">\n' +
              '                <i class="blue-del"></i>\n' +
              '                <span>' + roomNameArr[i] + '</span>\n' +
              '              </div>';
          }).join("");
        }
        $(_this.modalObj).find(".hys-sel-list").html(hysHtml);
      })
    },
    //表单获取繁忙状态
    judgeFormBusyList: function () {
      var _this = this;
      var modalObj = _this.modalObj;
      var tempDetailObj = _this.tempDetailObj;
      //组织者编辑
      if ($(modalObj).find(".modal-title").attr("data-isorg") == '1') {
        //是否全天 0-否 1-是
        var dayType = $(modalObj).find(".dayAll").prop("checked") ? 1 : 0;
        //时间
        var startDate = $(modalObj).find("input[name='startDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
        var endDate = $(modalObj).find("input[name='endDate']").val().substring(0, 10).replace(/年|月|日/g, '-');
        var startTime = $(modalObj).find("input[name='startTime']").val();
        var endTime = $(modalObj).find("input[name='endTime']").val();
        var startTimeStr = (dayType ? (startDate + ' 00:00') : (startDate + ' ' + startTime));
        var endTimeStr = (dayType ? (endDate + ' 23:59') : (endDate + ' ' + endTime));
        if (!dayType && (startTime == '' || endTime == '')) {
          return;
        } else if (!dayType && (!(/^\d{1,2}:\d{2}$/.test(startTime)) || !(/^\d{1,2}:\d{2}$/.test(endTime)))) {
          return;
        } else if (startTime.split(":")[0] > 23 || startTime.split(":")[1] > 59 || endTime.split(":")[0] > 23 || endTime.split(":")[1] > 59) {
          return;
        } else if (judgeTime(startTimeStr, endTimeStr)) {
          return;
        }
        //非组织者编辑
      } else {
        var startTimeStr = tempDetailObj.startTimeStr ? tempDetailObj.startTimeStr.replace(/日/g, '').replace(/年|月|日/g, '-') : '';
        var endTimeStr = tempDetailObj.endTimeStr ? tempDetailObj.endTimeStr.replace(/日/g, '').replace(/年|月|日/g, '-') : '';
      }

      var empNumberArr = [];
      $(".eg-selected-list .emp-li").each(function (i, v) {
        empNumberArr.push($(v).attr("data-empnumber"))
      })
      if (!empNumberArr.length) {
        return;
      }
      var data = {
        empNumbers: empNumberArr.join(","),
        endTime: endTimeStr + ':00',
        startTime: startTimeStr + ':00'
      }
      getConflictEmp(data, function (data) {
        $(".eg-selected-list .emp-li").each(function (i, v) {
          $(v).find(".busy-icon").remove()
          if (data.includes($(v).attr("data-empnumber"))) {
            $(v).find(".del-e-icon").before("<i class='busy-icon make-black-tip' data-text='该时段繁忙'></i>");
          }
        })
      })
    },
    //日期时间下拉框
    timeSelect: function (obj) {
      var _this = this;
      //解除点击事件
      $("body").off("click", "#timeList li");
      $("#timeList").remove();
      $("body").off("click", "#selectList li");
      $(".sch-select-list").remove();
      var timeHtml;
      var x = $(obj).offset().left;
      var y = $(obj).offset().top + 31;
      var time = $(obj).val().split(":");
      var index = time[1] == '30' ? (time[0] * 2 + 1) : time[0] * 2;
      timeHtml = '<ul class="sch-select-list time-list" id="timeList" style="position:absolute;left:' + x + 'px;top:' + y + 'px;z-index:51;">';
      for (var i = 0; i <= 23; i++) {
        timeHtml += '<li>' + ('00' + i).slice(-2) + ':00</li>'
          + '<li>' + ('00' + i).slice(-2) + ':30</li>';
      }
      timeHtml += '</ul>';
      $("body").append(timeHtml);
      var liIndex = $("#timeList").find("li").eq(index);
      var top = liIndex.length ? liIndex.offset().top - y : 0;
      $("#timeList").scrollTop(top);
      liIndex.length ? liIndex.addClass("curr") : '';

      //绑定点击事件
      $("body").on("click", "#timeList li", function () {
        $(obj).val($(this).text());
        var schedule_data = $(obj).parents(".schedule-date");
        _this.changeDateVal(schedule_data)

        $("#timeList").remove();
        //解除点击事件
        $("body").off("click", "#timeList li");
      });
    },
    /**
     * 当开始时间>结束时间时,对结束时间进行调整,fromInitEdit从初次获取编辑详情过来，不执行某些操作
     */
    changeDateVal: function (schedule_data, fromInitEdit) {
      var _this = this;
      var modalObj = this.modalObj;
      var tempDetailObj = this.tempDetailObj;
      var timeStart = $(schedule_data).find(".time-start").val();
      var dateStart = $(schedule_data).find(".date-start").val();

      var timeEnd = $(schedule_data).find(".time-end").val();
      var dateEnd = $(schedule_data).find(".date-end").val();
      var timeReg = /^\d{1,2}:\d{2}$/;
      var isAllday = $(modalObj).find(".dayAll").prop("checked");
      if (!isAllday && (!(timeReg.test(timeStart)) || !(timeReg.test(timeEnd)))) {
        return;
      }
      //全天日程
      if (isAllday) {
        //当开始时间>结束时间时,将结束日期调整为开始日期后一天
        if (!fromInitEdit && judgeTime(dateStart, dateEnd)) {
          _this.setNextDate(dateStart, schedule_data)
        }
        _this.setCustHys()
      } else {  //非全天日程
        //当开始时间>结束时间时,将结束时间调整为开始时间后30min
        if (!fromInitEdit && judgeTime(dateStart + ' ' + timeStart, dateEnd + ' ' + timeEnd)) {
          var timeStarts = timeStart.split(":")
          //大于23点30
          if (timeStarts[0] == 23 && timeStarts[1] >= 30) {
            var time_str = "00:00";
            $(schedule_data).find(".time-end").val(time_str);
            _this.setNextDate(dateStart, schedule_data)
          } else {
            var time_str = timeStarts[1] >= 30 ? ((parseInt(timeStarts[0]) + 1) + ':' + ('00' + (parseInt(timeStarts[1]) - 30)).slice(-2)) : (timeStarts[0] + ':' + (parseInt(timeStarts[1]) + 30));
            $(schedule_data).find(".time-end").val(time_str);
            $(schedule_data).find(".date-end").val(dateStart);
          }
        }

        //当结束日期>开始日期一天时，会议室只能选择自定义
        if (judgeTime(dateEnd, dateStart)) {
          _this.setCustHys()
        } else {
          $(modalObj).find("#custCheckbox").parent().removeClass("disabled");
        }
      }
      //结束时间＜当前时间 添加会议室模块隐藏
      var timeEnd = $(schedule_data).find(".time-end").val();
      var dateEnd = $(schedule_data).find(".date-end").val().substring(0, 10).replace(/\-|年|月|日/g, "/");
      var endDateStr = isAllday ? (dateEnd + ' 23:59') : (dateEnd + ' ' + timeEnd);
      var endDate = new Date(endDateStr)
      var curDate = new Date();
      if (endDate < curDate) {
        $(modalObj).find(".hys-block").hide()
      } else {
        $(modalObj).find(".hys-block").show()
        //时间改变，清空选择的会议室
        if (!fromInitEdit && $(modalObj).find(".hys-sel-list .hys-sel").length) {
          // $(modalObj).find(".hys-sel-list").empty();
          //自定义没有选中，红标语展示
          // if(!($(modalObj).find("#custCheckbox").prop("checked"))){
          //   $(modalObj).find(".hys-block .red-tip").show();
          // }
        }
      }
      this.judgeFormBusyList()
    },
    //设置会议室只能选择自定义
    setCustHys: function () {
      var modalObj = this.modalObj;
      var custCheckbox = $(modalObj).find("#custCheckbox");
      var isChecked = custCheckbox.prop("checked");
      if (!isChecked) {
        custCheckbox.trigger("click");
      }
      custCheckbox.parent().addClass("disabled");
    },
    /**
     * 结束日期设为开始日期的后一天
     */
    setNextDate: function (dateStart, schedule_data) {
      var gDateStart = dateStart.substring(0, 10).replace(/\-|年|月/g, '/');
      var nextDate = new Date(gDateStart);
      nextDate.setDate(nextDate.getDate() + 1);

      var year = nextDate.getFullYear();//年
      var month = nextDate.getMonth() + 1;//月
      var day = nextDate.getDate();//日

      var date_str = year + "年" + ('00' + month).slice(-2) + "月" + ('00' + day).slice(-2) + '日';
      $(schedule_data).find(".date-end").val(date_str)
    },
    /**
     * 日期展示
     * @param obj
     */
    datepicker: function (obj) {
      //解除点击事件
      $("body").off("click", "#scheduleDateDiv tr td");
      $("body").off("click", "#scheduleDateDiv thead .date-prev");
      $("body").off("click", "#scheduleDateDiv thead .date-next");
      $("body").find("#scheduleDateDiv").remove();
      var x, y;

      x = $(obj).offset().left;
      y = $(obj).offset().top + 31;
      var curDate = $(obj).val().replace(/\-|年|月|日/g, '/');
      var now = new Date(curDate);//当前日期
      var year = now.getFullYear();//年
      var month = now.getMonth();//月
      // var weeks = ['日','一','二','三','四','五','六'];//星期
      var weeks = ['一', '二', '三', '四', '五', '六', '日'];//星期
      var day = now.getDate();//日
      var firstday = new Date(year, month, 1); // 当月第一天Date
      var weekDay = firstday.getDay() == 0 ? 6 : (firstday.getDay() - 1); // 当月第一天星期几
      var allDay = getMonthAllDay(year, month);  //当前月份总天数

      this.showCanledar(year, month, day, weekDay, allDay, x, y, obj);
    },
    /**
     * 显示日期框
     */
    showCanledar: function (year, month, day, weekDay, allDay, x, y, obj) {
      var _this = this;
      var item, datePrev, dateCurr, dateNext;
      var prevDay = getMonthAllDay(year, (month - 1) < 0 ? "0" : (month - 1));
      var NextDay = getMonthAllDay(year, month);
      var rows = Math.ceil((allDay + weekDay) / 7);
      var dateDiv = '<div class="dateDiv" id="scheduleDateDiv" data-curdate="' + (year + '-' + (month + 1) + '-' + day) + '" style="position:absolute;left:' + x + 'px;top:' + y + 'px;z-index:51;">'
        + '<table  class="date-table" cellpadding="0" cellspacing="0" width="100%" border="0">'
        + '<thead><tr>'
        + '<th><a class="date-prev" data-change="prev" href="javascript:;"></a></th>'
        + '<th colspan="5">' + year + '年' + (month + 1) + '月</th>'
        + '<th><a class="date-next" data-change="next" href="javascript:;"></a></th>'
        + '</tr>'
        + '<tr class="weekdate">'
        + '<th>一</th><th>二</th><th>三</th><th>四</th><th>五</th><th>六</th><th>日</th>'
        + '</tr></thead>'
        + '<tbody>';
      for (var i = 0; i < rows; i++) {
        dateDiv += '<tr>';
        for (var k = 0; k < 7; k++) { // 表格每行的单元格
          var idx = i * 7 + k; // 单元格自然序列号
          var date_str = idx - weekDay + 1; // 计算日期
          var prev_str = prevDay + date_str;
          var next_str = date_str - NextDay;
          var monthStr = ('00' + (month + 1)).slice(-2);
          var fdate_str = ('00' + date_str).slice(-2);
          var full_date = year + "-" + monthStr + "-" + fdate_str;

          var curDate = new Date();
          var curYear = curDate.getFullYear();
          var curMonth = curDate.getMonth() + 1;
          var curDay = curDate.getDate();
          var isToday = false;
          if (parseInt(year) == parseInt(curYear) && parseInt(monthStr) == parseInt(curMonth) && parseInt(fdate_str) == parseInt(curDay)) {
            isToday = true;
          }
          if (date_str <= 0) {
            var prevDate = new Date(year, month - 1, prev_str);
            var prevDateStr = prevDate.getFullYear() + "-" + ('00' + (prevDate.getMonth() + 1)).slice(-2) + "-" + ('00' + prevDate.getDate()).slice(-2);

            datePrev = '<td class="transparent prev' + (isToday ? ' today' : '') + '" value="' + prevDateStr + '"><span>' + (isToday ? '今' : prev_str) + '</span></td>';
            dateDiv += datePrev;
          } else if (0 < date_str && date_str <= allDay) {
            if (date_str == day) {
              $("#dayCount").data("day", full_date);
              dateCurr = '<td class="change curr' + (isToday ? ' today' : '') + '" value="' + full_date + '"><span>' + (isToday ? '今' : date_str) + '</span></td>';
            } else {
              dateCurr = '<td class="' + (isToday ? 'today' : '') + '" value="' + full_date + '"><span>' + (isToday ? '今' : date_str) + '</span></td>';
            }
            dateDiv += dateCurr;
          } else {
            var nextDate = new Date(year, month + 1, next_str);
            var nextDateStr = nextDate.getFullYear() + "-" + ('00' + (nextDate.getMonth() + 1)).slice(-2) + "-" + ('00' + nextDate.getDate()).slice(-2);
            dateNext = '<td class="transparent next' + (isToday ? ' today' : '') + '" value="' + nextDateStr + '"><span>' + (isToday ? '今' : next_str) + '</span></td>';
            dateDiv += dateNext;
          }
        }
        dateDiv += '</tr>';
      }
      dateDiv += '</tbody></table></div>';

      $("body").append(dateDiv);

      //绑定点击事件
      $("body").on("click", "#scheduleDateDiv thead .date-prev", function () {
        changeDate(this)
      })
      $("body").on("click", "#scheduleDateDiv thead .date-next", function () {
        changeDate(this)
      })
      $("body").on("click", "#scheduleDateDiv tr td", function () {
        var val = $(this).attr("value").split('-');
        $(obj).val(val[0] + '年' + val[1] + '月' + val[2] + '日');
        _this.changeDateVal($(obj).parents(".schedule-date"))
        $("#scheduleDateDiv").remove();
        //解除点击事件
        $("body").off("click", "#scheduleDateDiv tr td");
        $("body").off("click", "#scheduleDateDiv thead .date-prev");
        $("body").off("click", "#scheduleDateDiv thead .date-next");
      });
    },
    closeModal: function (type, data) {
      var _this = this;
      typeof (this.opts.callback) == 'function' && this.opts.callback(type || '', data || '');
      if (!$(_this.modalObj).length) {
        return;
      }
      $(_this.modalObj).remove();
      $("#scheduleDateDiv").remove();
      $(".sch-select-list").remove();
      $(_this.modalObj).children().off();
    },
    // 创建讨论组
    createGroup: function () {
      var _this = this;
      alertObj = alertBox({
        title: "提示",
        content: '<div style="padding:28px 0 24px;text-align: center">创建后，所有参与者都会进入日程群组，后续加入的人员，也会自动进入群组，是否确认操作？</div>',
        isClose: false,
        done: function (type) {
          if (type == 1) {
            var organizerNo = _this.tempDetailObj.organizerNo;
            var participateNos = _this.tempDetailObj.participateNos;
            if (participateNos.indexOf(organizerNo) == -1) {
              participateNos += "," + organizerNo;
            }
            var teamName = (_this.tempDetailObj.title || "").trim() || "无主题";
            if (teamName.length > 20) {
              teamName = teamName.slice(0, 20);
            }
            ctrl.loading();
            alertObj.hide();
            ctrl.coaSdk.createGroup({
              msgBody: JSON.stringify({
                groupName: teamName,
                workerNos: participateNos,
                owner: ctrl.userInfo.workerNo,
                workerId: ctrl.userInfo.workerId,
              })
            }).then(function (res) {
              ctrl.loading().hide();
              if (res.success && res.data) {
                let tid = res.data.tid;
                // 告知服务端群id
                ctrl.coaSdk.smartUpdate({
                  id: _this.tempDetailObj.id,
                  groupId: tid
                });
                $(_this.modalObj).remove();
                $(_this.modalObj).children().off();
                ctrl.openChatBox(tid, "team");
              } else {
                var errStr = res.errorMsg + "";
                if (errStr && errStr.indexOf("code=806") == -1) {
                  ctrl.toast({title: res.errorMsg, type: 2});
                } else {
                  ctrl.toast({title: '创建讨论组已满，请处理后重试。', type: 2});
                }
              }
            })
          } else {
            alertObj.hide();
          }
        },
        // 返回生成的jq元素，可用于事件监听处理
        on: function ($elm) {
        }
      })
    },
    // 日程拒绝弹窗
    changeScheduleStatus: function (status, param) {
      var _this = this;
      var refuseObj = null;
      if (status == 4) {
        var areaHtml = '<div class="sch-refuse-content"><div>是否确认拒绝该日程？</div><div class="area-cont mt10">' +
          '<textarea class="refuse-area" placeholder="请输入拒绝原因，不超过50个字符，非必填" maxlength="50"></textarea>' +
          '<span class="count-num"><span class="num">0</span>/50</span>' +
          '</div>' +
          '<div><p class="red-tip">拒绝原因不能为空</p></div></div>';
        alertObj = alertBox({
          title: '拒绝原因',
          content: areaHtml,
          isClose: false,
          on: function (elm) {
            var $elm = $(elm);
            refuseObj = $elm;
            $elm.on("input", ".refuse-area", function () {
              $(this).val($(this).val().substring(0, 50))
              $elm.find(".num").html($(this).val().length);
            })
            $elm.on("click", ".refuse-area", function () {
              $elm.find(".sch-refuse-content").removeClass("err")
            })
            $elm.on("blur", ".refuse-area", function () {
              $elm.find(".sch-refuse-content").removeClass("err")
            })
          },
          done: function (type) {
            if (type == 1) {
              var refuseText = refuseObj.find(".refuse-area").val();
              // if($.trim(refuseText) == ''){
              //   refuseObj.find(".sch-refuse-content").addClass("err");
              // }else{
              var data = {
                status: 4,
                id: _this.opts.id,
                rejectContent: $.trim(refuseText || '')
              }
              updateScheduleAjax(data, function () {
                ctrl.toast({title: "操作成功", type: 1});
                alertObj.hide();
                _this.closeModal("feedback", 4);
              })
            } else {
              alertObj.hide();
            }
          }
        })
      } else {
        var data = {
          status: status,
          id: _this.opts.id
        }
        if (param && param.join) {
          data.join = param.join;
          data.addParticipateIds = param.addParticipateIds;
        }
        updateScheduleAjax(data, function () {
          ctrl.toast({title: "操作成功", type: 1});
          _this.closeModal("feedback", status);
        })
      }
    },
  }

  //详情繁忙状态
  function judgeDetailBusyList(tempDetailObj) {
    //时间
    var startTimeStr = tempDetailObj.startTimeStr.replace(/日/g, '').replace(/年|月|日/g, '-');
    var endTimeStr = tempDetailObj.endTimeStr.replace(/日/g, '').replace(/年|月|日/g, '-');
    var empNumberArr = [];
    $(".partner-shows .pant-one").each(function (i, v) {
      empNumberArr.push($(v).attr("data-empnumber"))
    })
    if (!empNumberArr.length) {
      return;
    }
    var data = {
      empNumbers: empNumberArr.join(","),
      endTime: endTimeStr + ':00',
      startTime: startTimeStr + ':00'
    }
    getConflictEmp(data, function (data) {
      $(".par-show .pant-one").each(function (i, v) {
        $(v).find(".busy-icon").remove()
        if (data.includes($(v).attr("data-empnumber"))) {
          $(v).append("<i class='p-icon busy make-black-tip' data-text='该时段繁忙'></i>");
        }
      })
      $(".partner-shows .pant-one").each(function (i, v) {
        $(v).find(".busy-icon").remove()
        if (data.includes($(v).attr("data-empnumber"))) {
          $(v).append("<i class='p-icon busy make-black-tip' data-text='该时段繁忙'></i>");
        }
      })
    })
  }

  /**
   * 新增日程
   */
  function saveScheduleAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartSave, data, callback);
  }

  /**
   * 获取日程详情
   */
  function getScheduleDetailAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartDetail, data, callback);
  }

  /**
   * 修改日程
   */
  function updateScheduleAjax(data, callback1, callback2) {
    scheduleAjax(ctrl.coaSdk.smartUpdate, data, callback1, callback2 || function () {});
  }

  /**
   * 删除日程
   */
  function deleteScheduleAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartDelete, data, callback);
  }

  /**
   * 转让日程
   */
  function transferScheduleAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartMakeOver, data, callback);
  }

  /**
   * 转发日程
   */
  function transmitScheduleAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartTransmit, data, callback);
  }

  /**
   * 会议室列表
   */
  function getRoomListAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartConferenceRoomList, data, callback);
  }

  /**
   * 转备忘
   */
  function addMemoAjax(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartAddMemo, data, callback);
  }

  /**
   * 获取人员冲突id
   */
  function getConflictEmp(data, callback) {
    scheduleAjax(ctrl.coaSdk.smartConflictEmp, data, callback);
  }

  function scheduleAjax(api, data, callback1, callback2) {
    ctrl.loading();
    api(data).then(function (res) {
      ctrl.loading().hide();
      if (res.success) {
        callback1(res.data || {})
      } else {
        callback2 && callback2();
        ctrl.toast({title: res.errorMsg || "系统错误", type: 2});
      }
    });
  }

  /**
   * 判断时间大小
   * @param date1
   * @param date2
   * @returns {boolean}
   */
  function judgeTime(date1, date2) {
    date1 = date1.replace(/\-|年|月|日/g, "/");
    date2 = date2.replace(/\-|年|月|日/g, "/");
    var newdate1 = new Date(date1);
    var newdate2 = new Date(date2);
    return newdate1 > newdate2 ? true : false;
  }

  /**
   * 是否为闰年
   * @param year
   * @returns {number}
   */
  function is_leap(year) {
    var res = "";
    return (year % 100 == 0 ? res = (year % 400 == 0 ? 1 : 0) :
      res = (year % 4 == 0 ? 1 : 0));
  }

  /**
   * 获取当月的天数
   * @param year
   * @param month
   * @returns {number}
   */
  function getMonthAllDay(year, month) {
    var m_days = new Array(31, 28 + is_leap(year), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31);
    return m_days[month];
  }

  function changeDate(obj) {
    var _this = $(obj);

    var changeType = _this.data("change");

    var dateTxt = changeType == 'prev' ? _this.parent().next().text() : _this.parent().prev().text();
    var firstday = strFormatToDate('yyyy-MM-dd', dateTxt, changeType);
    var year = firstday.getFullYear();
    var month = firstday.getMonth();
    var day = firstday.getDate();
    var weekDay = firstday.getDay() == 0 ? 6 : (firstday.getDay() - 1); // 当月第一天星期几
    var allDay = getMonthAllDay(year, month);  //当前月份总天数
    changeTbody(year, month, weekDay, allDay, _this);
    _this.closest("tr").find("th").eq(1).text(year + '年' + (month + 1) + '月');
  }

  function changeTbody(year, month, weekDay, allDay, obj) {
    var item, datePrev, dateCurr, dateNext;
    var now = new Date(year, month, 1);
    var y = now.getFullYear();
    var m = now.getMonth();
    var d = now.getDate();
    var el = $(obj).parents("table").find("tbody");
    var rows = Math.ceil((allDay + weekDay) / 7);
    var prevDay = getMonthAllDay(year, (month - 1) < 0 ? "0" : (month - 1));
    var NextDay = getMonthAllDay(year, month);
    var html = '<tr>';
    for (var i = 0; i < rows; i++) {
      for (var k = 0; k < 7; k++) { // 表格每行的单元格
        var idx = i * 7 + k; // 单元格自然序列号
        var date_str = idx - weekDay + 1; // 计算日期
        var prev_str = prevDay + date_str;
        var next_str = date_str - NextDay;
        var mStr = ('00' + (m + 1)).slice(-2);
        var fdate_str = ('00' + date_str).slice(-2);
        var full_date = y + "-" + mStr + "-" + fdate_str;
        //(date_str <= 0 || date_str > allDay) ? html += '<td class="transparent"></td>' :  (year==y&&month==m&&date_str == d) ? html += '<td value="'+ full_date +'"><span>' + date_str + '</span></td>' : html += '<td value="'+ full_date +'"><span>' + date_str + '</span></td>';
        var selectDate = $("#scheduleDateDiv").attr("data-curDate");
        var selectYear = selectDate.split("-")[0];
        var selectMonth = selectDate.split("-")[1];
        var selectDay = selectDate.split("-")[2];
        //判断今天
        var curDate = new Date();
        var curYear = curDate.getFullYear();
        var curMonth = curDate.getMonth() + 1;
        var curDay = curDate.getDate();
        var isToday = false;
        var isSelected = false;
        if (parseInt(year) == parseInt(curYear) && parseInt(mStr) == parseInt(curMonth) && parseInt(fdate_str) == parseInt(curDay)) {
          isToday = true;
        }
        if (parseInt(year) == parseInt(selectYear) && parseInt(mStr) == parseInt(selectMonth) && parseInt(fdate_str) == parseInt(selectDay)) {
          isSelected = true;
        }
        if (date_str <= 0) {
          var prevDate = new Date(year, month - 1, prev_str);
          var prevDateStr = prevDate.getFullYear() + "-" + ('00' + (prevDate.getMonth() + 1)).slice(-2) + "-" + ('00' + prevDate.getDate()).slice(-2);
          datePrev = '<td class="transparent prev' + (isToday ? ' today' : '') + '" value="' + prevDateStr + '"><span>' + (isToday ? '今' : prev_str) + '</span></td>';
          html += datePrev;
        } else if (0 < date_str && date_str <= allDay) {
          if (year == y && month == m && date_str == d) {
            dateCurr = '<td class="' + (isToday ? 'today' : '') + (isSelected ? ' curr' : '') + '"  value="' + full_date + '"><span>' + (isToday ? '今' : date_str) + '</span></td>';
          } else {
            dateCurr = '<td class="' + (isToday ? 'today' : '') + (isSelected ? ' curr' : '') + '" value="' + full_date + '"><span>' + (isToday ? '今' : date_str) + '</span></td>';
          }
          html += dateCurr;
        } else {
          var nextDate = new Date(year, month + 1, next_str);
          var nextDateStr = nextDate.getFullYear() + "-" + ('00' + (nextDate.getMonth() + 1)).slice(-2) + "-" + ('00' + nextDate.getDate()).slice(-2);
          dateNext = '<td class="transparent next' + (isToday ? ' today' : '') + '" value="' + nextDateStr + '"><span>' + (isToday ? '今' : next_str) + '</span></td>';
          html += dateNext;
        }
      }
      html += '</tr>';
    }
    el.html('').append(html);

    var new_date = new Date();
    var yyyy = new_date.getFullYear();
    var mm = new_date.getMonth();
    var dd = new_date.getDate();
    var full_date = y + "-" + mStr + "-" + fdate_str;
    //判断当天日期
    el.find('td[value=' + yyyy + "-" + ('00' + (mm + 1)).slice(-2) + "-" + ('00' + dd).slice(-2) + ']').addClass('change');

  };

  function strFormatToDate(formatStr, dateStr, type) {
    var year = 0;
    var start = -1;
    var len = dateStr.length;
    if ((start = formatStr.indexOf('yyyy')) > -1 && start < len) {
      year = dateStr.substr(start, 4);
    }
    var month = 0;
    if ((start = formatStr.indexOf('MM')) > -1 && start < len) {
      month = parseInt(dateStr.substr(start, 2)) - 1;
    }
    if (type == 'prev') {
      month--;
      if (month < 0) {
        month = 11;
        year = year - 1;
      }
    } else {
      month++;
      if (month > 12) {
        month = 0;
        year = year + 1;
      }
    }

    return new Date(year, month, 1);
  }

  /**
   * 黑色背景小浮框
   * @param obj
   * @param text
   */
  function showBlackTip(obj, text, modalObj) {
    var x = $(obj).offset().left + $(obj).width() / 2;
    var y = $(obj).offset().top + $(obj).height() + 3;
    var tipHtml = '<span style="left: ' + x + 'px;top: ' + y + 'px;" class="over-black-tip">' + text + '</span>';
    $(modalObj).append(tipHtml);
  }

  /**
   * 获取访问接口管控的head
   * @param serviceCode
   * @param methodCode
   * @param version
   * @returns {null|{deptName: string, serviceCode, v, empNumber, appName: string, empName: string, imei: string, deptNumber: (string|*), empNo: *, methodCode, token: (number|*)}}
   */
  function getHeaders(serviceCode, methodCode, version) {
    var emp = ctrl.userInfo;
    if (!emp) {
      return null;
    }
    return {
      serviceCode: serviceCode,
      methodCode: methodCode,
      v: version,
      empNo: emp.workerNo,
      empNumber: emp.workerId,
      empName: encodeURI(encodeURI(emp.name)),
      deptNumber: emp.deptNumber,
      deptName: encodeURI(encodeURI(emp.deptName)),
      imei: ctrl.baseComputerInfo.hostName,
      token: emp.token,//'651719e0673ed75cbf1197f685a11096',
      appName: 'pc-im'
    }
  }

  /**
   * 判断非空
   * @param value
   * @returns {boolean}
   */
  function isEmpty(value) {
    if (value == undefined || value == null || value == 'null'
      || value == 'undefined' || value.length == 0) {
      return true;
    }
    return false;
  }

  /**
   * 用于把用utf16编码的字符转换成实体字符，以供后台存储
   * @param  {string} str 将要转换的字符串，其中含有utf16字符将被自动检出
   * @return {string}     转换后的字符串，utf16字符将被转换成&#xxxx;形式的实体字符
   */
  function utf16toEntities(str) {
    var patt = /[\ud800-\udbff][\udc00-\udfff]/g; // 检测utf16字符正则
    str = str.replace(patt, function (char) {
      var H, L, code;
      if (char.length === 2) {
        H = char.charCodeAt(0); // 取出高位
        L = char.charCodeAt(1); // 取出低位
        code = (H - 0xD800) * 0x400 + 0x10000 + L - 0xDC00; // 转换算法
        return "&#" + code + ";";
      } else {
        return char;
      }
    });
    return str;
  }

  return ScheduleModal;
}
