<template>
  <div class="smart-schedule smart-schedule-box" id="smartScheduleBox">
    <div class="smart-left">
      <div class="smart-calendar-box"></div>
      <!--列表视角选择展示内容-->
      <div class="smart-list-sel-box">
        <div class="smart-selection-box selection-sel">
          <div class="selection-title-box">
            <div>事项选择</div>
            <i class="icon-arrow toggle"></i>
          </div>
          <ul class="selection-content-box">
            <li class="selection-list list1 sel" data-type="1">日程</li>
            <li class="selection-list list4 sel" data-type="4">快速行动</li>
            <li class="selection-list list2 sel" data-type="2">待办</li>
            <li class="selection-list list3 sel" data-type="3">YCYA任务</li>
          </ul>
        </div>
      </div>
      <!--日/周/月视角选择展示内容-->
      <div class="smart-other-sel-box">
        <div class="smart-search-box">
          <input class="smart-search-input" type="text" placeholder="搜索联系人">
          <div class="smart-search-result">
            <div class="smart-search-content">
              <div class="smart-search-intr">联系人</div>
              <ul class="smart-search-ul"></ul>
              <div class="smart-search-none">暂无搜索结果</div>
            </div>
          </div>
        </div>
        <div class="smart-selection-box my-schedule">
          <div class="selection-title-box">
            <div>我的日历</div>
            <i class="icon-arrow toggle"></i>
          </div>
          <ul class="selection-content-box">
            <li class="selection-list list4 sel" data-type="1">自己</li>
          </ul>
        </div>
        <div class="smart-selection-box sub-schedule">
          <div class="selection-title-box">
            <div>订阅日历</div>
            <i class="icon-arrow toggle"></i>
          </div>
          <ul class="selection-content-box"></ul>
        </div>
      </div>
      <div class="smart-guide" data-href="https://docs.qq.com/doc/p/ec91394d6c60b0b49a053eb07b5667d40843414b?dver=2.1.27255074">快速了解智能日程</div>
    </div>
    <div class="smart-right">
      <div class="smart-right-header" data-no-drag="false">
        <div class="header-left">
          <div class="today-btn">今天</div>
          <i class="icon-arrow pre" content="上一天"></i>
          <i class="icon-arrow next" content="下一天"></i>
          <div class="header-time">2021年7月27日</div>
          <div class="today">今天</div>
          <div class="header-time-label">班</div>
        </div>
        <ul class="header-right no-dragable">
          <li class="header-list sel" data-type="1">列表</li>
          <li class="header-list" data-type="2">日</li>
          <li class="header-list" data-type="3">周</li>
          <li class="header-list" data-type="4">月</li>
        </ul>
      </div>
      <!--列表/日/周/月视角内容-->
      <div class="smart-right-content">
        <!-- 网络提示 -->
        <NetTips></NetTips>
        <div class="smart-show-list show-list show-list1">

          <div class="schedule-box list-box list-box1">
            <h1 class="list-title">日程</h1>
            <div class="list-detail-box schedule-detail-box">
              <ul class="schedule-ul schedule-ul-header">
                <li>时间</li>
                <li>日程主题</li>
                <li>会议室</li>
                <li>组织者</li>
                <li style="padding-left: 2px;">操作</li>
              </ul>
              <div class="schedule-content-box"></div>
            </div>
            <div class="list-none">
              <span>今日暂无日程，</span>
              <span id="scheduleAdd">快速创建</span>
            </div>
            <div class="list-loading"></div>
          </div>

          <!--快速行动-->
          <div class="quick-acting-box list-box list-box4">
            <h1 class="list-title">
              快速行动
              <a class="create-quick-acting-btu" style="float: right;margin-right: 0;" @click="onOpenCreateQuickActing">创建快速行动</a>
            </h1>
            <div class="list-detail-box schedule-detail-box" style="display: unset">
              <ul class="schedule-ul schedule-ul-header">
                <li>截止时间</li>
                <li>行动名称</li>
                <li>发起人</li>
                <li>操作</li>
              </ul>
              <div class="schedule-content-box"></div>
            </div>
            <div class="list-none">
              <span>今日暂无快速行动, <a class="create-quick-acting-btu" @click="onOpenCreateQuickActing">快速创建</a></span>
            </div>
            <div class="quick-acting-loading"></div>
          </div>

          <div class="upcoming-box list-box list-box2">
            <h1 class="list-title">待办</h1>
            <ul class="list-detail-box upcoming-ul sel-list-ul"></ul>
            <div class="list-none">
              <span>今日暂无待办</span>
            </div>
            <div class="list-loading"></div>
          </div>

          <div class="ycya-box list-box list-box3">
            <h1 class="list-title">YCYA任务</h1>
            <ul class="list-detail-box ycya-ul sel-list-ul"></ul>
            <div class="list-none">
              <span>今日暂无YCYA任务</span>
            </div>
            <div class="list-loading"></div>
          </div>

        </div>
        <div class="smart-show-day show-list show-list2">
          <div class="column-user-header"></div>
          <div class="column-user-content">
            <div class="column-user-time"></div>
            <div class="column-user-box"></div>
          </div>
        </div>
        <div class="smart-show-week show-list show-list3">
          <div class="column-user-header"></div>
          <div class="column-user-content">
            <div class="column-user-time"></div>
            <div class="column-user-box"></div>
          </div>
        </div>
        <div class="smart-show-month show-list show-list4"></div>
      </div>
    </div>
    <i class="smart-add-btn"></i>
    <div class="smart-hover-box"></div>
    <div class="smart-tips-box"></div>
    <div class="smart-detail-list-modal">
      <div class="smart-detail-box">
        <div class="smart-modal-close"></div>
        <div class="smart-modal-intr">周X</div>
        <div class="smart-modal-title">X月X日</div>
        <div class="smart-modal-list-box"></div>
      </div>
    </div>
    <quick-acting-model ref="quickActingModelRef" @del="onDelQuickActing"></quick-acting-model>

  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import {initCalendar} from "./calendar"
import {initScheduleModal} from "./scheduleModal"
import {initSmartScheduleBox} from "./smartSchedule"
import {alert, loading, toast} from "@comp/ui";
import {htmlEscapeAll, dateFormat, getLink} from '@utils'
import {
  smartCalendarApi,
  smartQueryListApi,
  smartZnzlListApi,
  smartTaskListApi,
  smartAttentionListApi,
  smartUpdateApi,
  smartTaskDelApi,
  smartAttentionAddApi,
  searchUsersApi,
  smartAttentionDelApi,
  taskListApi, quickActingSearchPublicTaskListApi
} from '@utils/net/api'
import NetTips from "@comp/ui/comps/NetTips";
import QuickActingModel from "@comp/schedule/QuickActingModel.vue";

export default {
  name: "SmartSchedule",
  components: {QuickActingModel, NetTips},
  setup(props, ctx) {
    const quickActingModelRef = ref(null);

    const store = useStore();
    let smartScheduleBox = "";
    let ctrl = {
      alert: alert,
      loading: loading,
      toast: toast,
      htmlEscapeAll: htmlEscapeAll,
      dateFormat: dateFormat,
      userInfo: store.getters.getUserInfo,
      config: store.getters.getConfig.config,
      baseComputerInfo: store.getters.getBaseComputerInfo,
      nim: store.getters.getNim,
      getALinkNew: getLink,
      setScheduleStatus: setScheduleStatus,
      scheduleModal: initScheduleModal(store),
      onClickQuickActing: onClickQuickActing,

      coaSdk: {
        smartCalendar: smartCalendarApi,
        smartQueryList: smartQueryListApi,
        smartZnzlList: smartZnzlListApi,
        smartTaskList: smartTaskListApi,
        smartAttentionList: smartAttentionListApi,
        smartUpdate: smartUpdateApi,
        smartTaskDel: smartTaskDelApi,
        smartAttentionAdd: smartAttentionAddApi,
        selSearchApi: searchUsersApi,
        smartAttentionDel: smartAttentionDelApi,
        taskListApi: quickActingSearchPublicTaskListApi,
      }
    }
    onMounted(() => {
      // 日历对象
      ctrl.initCalendar = initCalendar.bind(ctrl)($);
      ctrl.smartScheduleBox = initSmartScheduleBox.bind(ctrl)($);
      smartScheduleBox = new ctrl.smartScheduleBox();
      smartScheduleBox.initSmartScheduleHtml();
    });

    // 监听重新加载
    watch(() => store.state.router.currentRoute.query.id,
      (newValue, oldValue) => {
        if (store.state.router.currentRoute.path == "/index/smartSchedule") {
          smartScheduleBox.initSmartScheduleHtml();
        }
      }, {
        deep: true
      }
    );

    // 聚焦主窗口
    watch(()=> store.state.emit.focus,(newValue, oldValue)=>{
      smartScheduleBox?.initQuickActingContentHtml()
    })
    // 设置日程状态
    function setScheduleStatus(id, status) {
      store.dispatch("setScheduleStatus", {id: id, status: status});
    }
    // 打开快速行动卡片
    function onClickQuickActing(batchNo,x,y){
      quickActingModelRef.value?.showQuickActingModel(batchNo,x,y);
    }
    // 删除快速行动回调
    function onDelQuickActing(){
      smartScheduleBox?.initQuickActingContentHtml()
    }
    function onOpenCreateQuickActing(){
      quickActingModelRef.value?.showQuickActingCopyBox();
    }
    return {
      onDelQuickActing,
      onOpenCreateQuickActing,
      quickActingModelRef
    }
  }
}
</script>
<style lang="scss">
/*智能日程*/
#smartScheduleBox,
#smartScheduleBox * {
  box-sizing: border-box;
}

#smartScheduleBox {
  display: flex;
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  user-select: text;
  overflow: hidden;
}

#smartScheduleBox .smart-left {
  width: 244px;
  height: 100%;
  border-right: 1px solid #E0E0E0;
  background: #FFFFFF;
  flex-shrink: 0;
  user-select: none;
  position: relative;
}

#smartScheduleBox .smart-guide {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 24px;
  line-height: 24px;
  color: $styleColor;
  background: $styleBg2;
  padding-left: 30px;
  cursor: pointer;
}

#smartScheduleBox .smart-guide:before,
#smartScheduleBox .smart-guide:after {
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
  background-image: url("/img/schedule/guide.png");
  background-repeat: no-repeat;
  background-size: 28px 14px;
}

#smartScheduleBox .smart-guide:before {
  left: 10px;
  background-position: 0 0;
}

#smartScheduleBox .smart-guide:after {
  right: 10px;
  background-position: -14px 0;
}

#smartScheduleBox .smart-right {
  flex: 1;
  width: calc(100% - 244px);
  height: 100%;
  border-bottom: 1px solid #e7e7e7;
}

#smartScheduleBox .smart-left .smart-calendar-box {
  width: 100%;
  position: relative;
  padding: 20px 16px;
  margin-bottom: 16px;
}

#smartScheduleBox .smart-left .smart-calendar-box:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 212px;
  height: 1px;
  background: #D8D8D8;
  border-radius: 2px;
}

#smartScheduleBox .smart-left .smart-search-box {
  width: 212px;
  height: 32px;
  background: #FFFFFF;
  position: relative;
  font-size: 12px;
  margin: 16px 16px 6px;
  border-radius: 4px;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-input {
  width: 100%;
  height: 100%;
  padding-left: 32px;
  background-image: url("/img/schedule/search.png");
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: 8px 8px;
  outline: none;
  border-radius: 4px;
  border: 1px solid #E7E7E7;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-input:focus {
  border: 1px solid #333333;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-result {
  position: absolute;
  top: 33px;
  left: 0;
  width: 100%;
  background: #FFFFFF;
  z-index: 10;
  box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
  border: 1px solid #CCCCCC;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-none {
  height: 60px;
  line-height: 60px;
  text-align: center;
  color: #999999;
  display: none;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-result,
#smartScheduleBox .smart-left .smart-search-box .smart-search-content {
  display: none;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-intr {
  font-weight: 500;
  color: #000000;
  background: $styleBg1;
  width: 100%;
  height: 24px;
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  padding-left: 8px;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-ul {
  max-height: 144px;
  overflow: auto;
  padding-top: 24px;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-list {
  height: 30px;
  padding-left: 8px;
  display: flex;
  align-items: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  cursor: pointer;
}

#smartScheduleBox .smart-left .smart-search-box .smart-search-list:hover {
  background: $styleBg1;
}

#smartScheduleBox .smart-left .smart-selection-box .selection-content-box .selection-list {
  height: 32px;
  padding: 0 16px;
}

#smartScheduleBox .smart-left .smart-selection-box .selection-content-box .selection-list:hover {
  background: #E7E7E7;
}

#smartScheduleBox .smart-left .smart-selection-box .selection-content-box .selection-list:hover .remove-attention {
  display: block;
}

#smartScheduleBox .smart-selection-box.hide .selection-content-box {
  display: none;
}

#smartScheduleBox .sub-schedule {
  padding-bottom: 20px;
}

#smartScheduleBox .sub-schedule .selection-content-box {
  overflow-y: auto;
}

#smartScheduleBox .sub-schedule .selection-content-box .selection-list {
  height: 32px;
  padding: 0 16px;
}

#smartScheduleBox .selection-title-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  padding: 0 16px;
  color: #999999;
}

#smartScheduleBox .icon-arrow {
  background-image: url("/img/schedule/calendar.png");
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  background-size: 240px 20px;
  cursor: pointer;
  position: relative;
}

#smartScheduleBox .icon-arrow:hover:after {
  position: absolute;
  top: 26px;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 12px;
  font-size: 12px;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  z-index: 1;
  border-radius: 2px;
}

#smartScheduleBox .icon-arrow.pre {
  background-position: -20px 0;
}

#smartScheduleBox .icon-arrow.pre:hover {
  background-position: 0px 0;
}

#smartScheduleBox .icon-arrow.pre:hover:after {
  content: attr(content);
  z-index: 20;
}

#smartScheduleBox .icon-arrow.next {
  background-position: -60px 0;
}

#smartScheduleBox .icon-arrow.next:hover {
  background-position: -40px 0;
}

#smartScheduleBox .icon-arrow.next:hover:after {
  content: attr(content);
  z-index: 20;
}

#smartScheduleBox .icon-arrow.toggle {
  background-position: -100px 0;
}

#smartScheduleBox .icon-arrow.toggle:hover {
  background-position: -80px 0;
}

#smartScheduleBox .icon-arrow.toggle:hover:after {
  content: "\6536\8d77";
  z-index: 12;
}

#smartScheduleBox .icon-arrow.toggle.hide {
  background-position: -140px 0;
}

#smartScheduleBox .icon-arrow.toggle.hide:hover {
  background-position: -120px 0;
}

#smartScheduleBox .icon-arrow.toggle.hide:hover:after {
  content: "\5c55\5f00";
  z-index: 12;
}

#smartScheduleBox .sub-schedule .selection-list .remove-attention {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  cursor: pointer;
  box-sizing: content-box;
  background-image: url("/img/schedule/calendar.png");
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  background-size: 240px 20px;
  background-position: -160px 0;
  display: none;
}

#smartScheduleBox .sub-schedule .selection-list .remove-attention:hover {
  background-position: -200px 0;
}

#smartScheduleBox .selection-list {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

#smartScheduleBox .sel-list-check:before,
#smartScheduleBox .selection-list:before {
  content: '';
  width: 14px;
  height: 14px;
  border: 1px solid $styleColor;
  display: inline-block;
  border-radius: 50%;
  margin-right: 8px;
  box-sizing: border-box;
}

#smartScheduleBox .selection-list.sel:before {
  background: $styleColor;
}

#smartScheduleBox .gray .sel-list-check:after,
#smartScheduleBox .selection-list.sel:after {
  content: '';
  width: 15px;
  height: 9px;
  border: 3px solid #FFFFFF;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 11px;
  left: 16px;
  vertical-align: middle;
  transform: rotate(-45deg) scale(0.5);
  box-sizing: border-box;
}

#smartScheduleBox .gray .sel-list-check:after {
  width: 11px;
  height: 6px;
  top: 3px;
  left: 2px;
}

#smartScheduleBox .selection-list.list2:before {
  border: 1px solid #FF8800;
}

#smartScheduleBox .selection-list.list2.sel:before {
  border: 1px solid #FF8800;
  background: #FF8800;
}

#smartScheduleBox .selection-list.list3:before {
  border: 1px solid #00BC55;
}

#smartScheduleBox .selection-list.list3.sel:before {
  border: 1px solid #00BC55;
  background: #00BC55;
}

#smartScheduleBox .selection-list.list4:before {
  border: 1px solid #2D91E6;
}

#smartScheduleBox .selection-list.list4.sel:before {
  border: 1px solid #3188E8;
  background: #3188E8;
}

#smartScheduleBox .selection-list.list5:before {
  border: 1px solid #FF8800;
}

#smartScheduleBox .selection-list.list5.sel:before {
  border: 1px solid #FF8800;
  background: #FF8800;
}

#smartScheduleBox .selection-list.list6:before {
  border: 1px solid #FFC608;
}

#smartScheduleBox .selection-list.list6.sel:before {
  border: 1px solid #FFC608;
  background: #FFC608;
}

#smartScheduleBox .selection-list.list7:before {
  border: 1px solid #34C725;
}

#smartScheduleBox .selection-list.list7.sel:before {
  border: 1px solid #34C725;
  background: #34C725;
}

#smartScheduleBox .selection-list.list8:before {
  border: 1px solid #00D6B9;
}

#smartScheduleBox .selection-list.list8.sel:before {
  border: 1px solid #00D6B9;
  background: #00D6B9;
}

#smartScheduleBox .selection-list.list9:before {
  border: 1px solid #4E83FD;
}

#smartScheduleBox .selection-list.list9.sel:before {
  border: 1px solid #4E83FD;
  background: #4E83FD;
}

#smartScheduleBox .selection-list.list10:before {
  border: 1px solid #14C0FF;
}

#smartScheduleBox .selection-list.list10.sel:before {
  border: 1px solid #14C0FF;
  background: #14C0FF;
}

#smartScheduleBox .selection-list.list11:before {
  border: 1px solid #935AF6;
}

#smartScheduleBox .selection-list.list11.sel:before {
  border: 1px solid #935AF6;
  background: #935AF6;
}

#smartScheduleBox .selection-list.list12:before {
  border: 1px solid #D136D0;
}

#smartScheduleBox .selection-list.list12.sel:before {
  border: 1px solid #D136D0;
  background: #D136D0;
}

#smartScheduleBox .selection-list.list13:before {
  border: 1px solid #F14BA9;
}

#smartScheduleBox .selection-list.list13.sel:before {
  border: 1px solid #F14BA9;
  background: #F14BA9;
}

#smartScheduleBox .selection-list.list14:before {
  border: 1px solid #8F959E;
}

#smartScheduleBox .selection-list.list14.sel:before {
  border: 1px solid #8F959E;
  background: #8F959E;
}

#smartScheduleBox .smart-right-header {
  background: #FFFFFF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  height: 62px;
  padding: 0 16px;
  border-bottom: 1px solid #D8D8D8;
  user-select: none;
}

#smartScheduleBox .smart-right-content {
  position: relative;
  height: calc(100% - 62px);
  /*overflow-x: visible;*/
  /*overflow-y: auto;*/
}

#smartScheduleBox .smart-right-content .show-list {
  user-select: none;
  height: 100%;
  overflow: auto;
  position: relative;
}

#smartScheduleBox .smart-right-content .show-list.show-list1 {
  user-select: text;
}

#smartScheduleBox .smart-right-content .show-list.show-list2 {
  padding-top: 60px;
}

#smartScheduleBox .smart-right-content .show-list.show-list2 .hide {
  display: none;
}

#smartScheduleBox .header-left,
#smartScheduleBox .header-right {
  display: flex;
  align-items: center;
}

#smartScheduleBox .header-left .today-btn {
  font-size: 12px;
  color: #222222;
  width: 52px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #E7E7E7;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8px;
  cursor: pointer;
}

#smartScheduleBox .header-left .today-btn:hover {
  color: $styleColor;
  border: 1px solid $styleColor;
}

#smartScheduleBox .header-left .icon-arrow {
  margin-right: 8px;
}

#smartScheduleBox .header-left .header-time,
#smartScheduleBox .header-left .today {
  font-size: 16px;
  font-weight: 500;
  margin-right: 8px;
  color: #222222;
}

#smartScheduleBox .header-left.curr .header-time {
  color: $styleColor;
}

#smartScheduleBox .header-left .today {
  display: none;
}

#smartScheduleBox .header-left.curr .today {
  display: block;
  color: $styleColor;
}

#smartScheduleBox .header-left .header-time-label {
  font-size: 12px;
  line-height: 12px;
  color: $styleColor;
  border-radius: 2px;
  border: 1px solid $styleColor;
  padding: 2px 5px;
  margin-top: 2px;
}

#smartScheduleBox .header-left.status2 .header-time-label {
  border: 1px solid #FFC608;
  color: #FFC608;
}

#smartScheduleBox .header-left.status3 .header-time-label {
  border: 1px solid #FFC608;
  color: #FFC608;
}

#smartScheduleBox .header-right {
  position: absolute;
  bottom: 0;
  right: 16px;
}

#smartScheduleBox .header-right .header-list {
  text-align: center;
  font-size: 13px;
  font-weight: 400;
  color: #666666;
  cursor: pointer;
  padding: 10px;
}

#smartScheduleBox .header-right .header-list.sel {
  color: $styleColor;
  position: relative;
  font-weight: bold;
}

#smartScheduleBox .header-right .header-list.sel:after {
  width: 12px;
  height: 3px;
  background: $styleColor;
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  border-radius: 2px;
}

#smartScheduleBox .column-user-box {
  display: flex;
  flex-flow: row wrap;
  width: 100%;
  height: 100%;
  /*overflow: scroll;*/
  position: relative;
  flex-wrap: nowrap;
}

#smartScheduleBox .smart-show-day .column-user-box {
  padding-left: 63px;
}

#smartScheduleBox .column-user-header {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px #EBEBEB;
  z-index: 13;
  overflow: hidden;
  width: 100%;
  min-height: 60px;
  max-height: 102px;
}

#smartScheduleBox .smart-show-day .column-user-header {
  padding-left: 63px;
}

#smartScheduleBox .smart-show-week .column-user-header {
  max-height: 129px;
}

#smartScheduleBox .column-user-content {
  display: flex;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: scroll;
}

#smartScheduleBox .smart-show-day .column-user-content::-webkit-scrollbar {
  height: 8px;
}

#smartScheduleBox .column-user-time {
  position: relative;
  width: 63px;
  flex-shrink: 0;
}

#smartScheduleBox .smart-show-day .column-user-time {
  position: absolute;
  left: 0;
  height: 1151px;
  top: 0;
  z-index: 12;
  background: #FFFFFF;
  /*border-right: 1px solid #E7E7E7;*/
}

#smartScheduleBox .column-user-all {
  border-left: 1px solid #E7E7E7;
  min-height: 60px;
  position: relative;
}

#smartScheduleBox .column-user-all-toggle {
  width: 63px;
  position: relative;
  text-align: center;
  padding-top: 39px;
  color: #999999;
  flex-shrink: 0;
}

#smartScheduleBox .smart-show-week .column-user-all-toggle {
  padding-top: 67px;
}

#smartScheduleBox .smart-show-day .column-user-all-toggle {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #FFFFFF;
  z-index: 13;
  /*border-right: 1px solid #E7E7E7;*/
}

#smartScheduleBox .column-user-all-toggle.show i {
  content: "";
  position: absolute;
  bottom: 2px;
  right: 3px;
  background-image: url(/img/schedule/calendar.png);
  background-position: -140px 0;
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  background-size: 240px 20px;
  margin-left: 12px;
  cursor: pointer;
}

#smartScheduleBox .column-user-all-toggle.show i:hover {
  background-position: -220px 0;
}

#smartScheduleBox .column-user-all-toggle.show.toggle i {
  background-position: -100px 0;
}

#smartScheduleBox .column-user-all-toggle.show.toggle i:hover {
  background-position: -180px 0;
}

#smartScheduleBox .column-user-schedule-box .block-html,
#smartScheduleBox .column-user-all .all-day-html {
  width: calc(100% - 8px);
  height: 20px;
  line-height: 20px;
  margin-bottom: 2px;
  padding-left: 6px;
  white-space: nowrap;
  overflow: hidden;
  z-index: 1;
  cursor: pointer;
}

#smartScheduleBox .column-user-schedule-box .block-html.hide,
#smartScheduleBox .all-day-html.hide {
  display: none !important;
}

#smartScheduleBox .column-user-all .all-day-html:last-child {
  margin-bottom: 0;
}

#smartScheduleBox .column-user-all .column-user-all-list {
  position: relative;
  height: calc(100% - 37px);
  min-height: 21px;
  max-height: 200px;
  padding-bottom: 2px;
  margin-left: 2px;
}

#smartScheduleBox .column-user-all .column-user-all-list.scroll {
  overflow-y: scroll;
}

#smartScheduleBox .column-user-all .column-user-all-list.scroll::-webkit-scrollbar {
  width: 6px;
}

#smartScheduleBox .smart-show-week .column-user-all .column-user-all-list {
  height: calc(100% - 66px);
}

#smartScheduleBox .column-user-all .show-all-more {
  width: 100%;
  height: 21px;
  position: absolute;
  top: 44px;
  left: 0;
  padding-left: 6px;
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
  background: #FFFFFF;
  cursor: pointer;
}

#smartScheduleBox .column-user-all .show-all-more:hover {
  color: #2D91E6;
}

#smartScheduleBox .column-user-all .column-user-name {
  color: #222222;
  font-weight: 600;
  line-height: 17px;
  padding: 10px;
}

#smartScheduleBox .column-user-all,
#smartScheduleBox .column-user-box .column {
  min-width: 165px;
}

#smartScheduleBox .smart-show-week .column-user-all {
  min-width: auto;
  width: calc((100% - 63px) / 7);
}

#smartScheduleBox .smart-show-week .column-user-box .column {
  min-width: auto;
}

#smartScheduleBox .column-user-all:first-child {
  margin-left: 63px;
}

#smartScheduleBox .column-user-all,
#smartScheduleBox .column-box {
  flex: 1;
}

#smartScheduleBox .column-box.curr .column-list {
  background: #FCFCFC;
}

#smartScheduleBox .column-box,
#smartScheduleBox .column-box .column-list {
  position: relative;
}

#smartScheduleBox .column-box.default-column-box {
  position: absolute;
  width: calc(100% - 63px);
  display: none;
}

#smartScheduleBox .curr-time,
#smartScheduleBox .column-user-time .column-time {
  position: absolute;
  top: -10px;
  left: 0px;
  width: 63px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 400;
  color: #666666;
}

#smartScheduleBox .column-box .column-user-schedule-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#smartScheduleBox .column-box .column {
  height: 48px;
  border-bottom: 1px solid #DEE0E3;
  border-left: 1px solid #DEE0E3;
  flex: 1;
}

#smartScheduleBox ::-webkit-scrollbar {
  width: 0;
  height: 0;
}

#smartScheduleBox .curr-time {
  color: #F54A45;
  left: 0;
  z-index: 15;
}

#smartScheduleBox .curr-line {
  width: calc(100% - 63px);
  position: absolute;
  left: 63px;
  height: 1px;
  background: #F54A45;
  z-index: 15;
}

#smartScheduleBox .curr-line:after {
  content: "";
  width: 8px;
  height: 8px;
  background: #F54A45;
  border-radius: 50%;
  position: absolute;
  top: -4px;
  left: -4px;
}

#smartScheduleBox .sel-area {
  width: 100%;
  height: 25px;
  line-height: 19px;
  padding-left: 8px;
  background: $styleBg2;
  box-shadow: 0px 2px 5px 0px #BFBFBF;
  border: 1px solid $styleColor;
  font-size: 12px;
  color: $styleColor;
  position: absolute;
  left: 0;
  z-index: 10;
  white-space: nowrap;
  overflow: hidden;
}

#smartScheduleBox .sel-area .sel-area-title {
  font-weight: 500;
}

#smartScheduleBox .smart-show-list .list-box {
  border-top: 1px solid #e7e7e7;
  padding: 20px 8px 23px 16px;
}

#smartScheduleBox .smart-show-list .list-box:first-child {
  border: 0;
}

#smartScheduleBox .smart-show-list .list-box.hide {
  display: none;
}

#smartScheduleBox .smart-show-list .list-title {
  font-size: 16px;
  font-weight: 500;
  color: #000000;
  line-height: 22px;
  padding-left: 12px;
  position: relative;
}

#smartScheduleBox .smart-show-list .list-title:after {
  content: "";
  width: 4px;
  height: 15px;
  background: $styleColor;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
}

#smartScheduleBox .smart-show-list .upcoming-box .list-title:after {
  background: #FF8800;
}

#smartScheduleBox .smart-show-list .ycya-box .list-title:after {
  background: #00BC55;
}

#smartScheduleBox .smart-show-list .quick-acting-box .list-title:after {
  background: #3188E8;
}

#smartScheduleBox .smart-show-list .list-none {
  font-size: 13px;
  font-weight: 400;
  color: #222222;
  line-height: 18px;
  margin-top: 15px;
  display: flex;
  align-items: center;
}

#smartScheduleBox .smart-show-list .list-detail-box,
#smartScheduleBox .smart-show-list .list-none,
#smartScheduleBox .smart-show-list .list-loading , #smartScheduleBox .smart-show-list .quick-acting-loading {
  display: none;
}

#smartScheduleBox .smart-show-list .list-none #scheduleAdd {
  color: $styleColor;
  cursor: pointer;
}

#smartScheduleBox .schedule-content-box {
  margin: 0 -8px;
}

#smartScheduleBox .schedule-detail-box .schedule-ul {
  width: 100%;
  font-size: 13px;
  color: #000000;
  line-height: 18px;
  font-weight: 400;
  display: flex;
  align-items: center;
  position: relative;
  height: 32px;
  padding: 0 8px 0 22px;
}

#smartScheduleBox .schedule-content-box .schedule-ul:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 8px;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: $styleColor;
}

#smartScheduleBox .schedule-content-box  .quick-acting:after{
  content: "";
  position: absolute;
  top: 50%;
  left: 8px;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #3188E8;
}
#smartScheduleBox .schedule-content-box .schedule-ul:hover {
  background: $styleBg1;
}

#smartScheduleBox .schedule-detail-box .schedule-ul.gray {
  color: #999999;
}

#smartScheduleBox .schedule-content-box .schedule-ul.gray:after {
  opacity: 0;
}

#smartScheduleBox .schedule-detail-box .schedule-ul.schedule-ul-header {
  font-weight: 500;
  padding-left: 9px;
  margin: 17px 6px 0;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 30px;
  position: relative;
  cursor: pointer;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.schedule-btn-box {
  line-height: 100%;
  height: 100%;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.schedule-btn-box > span {
  display: flex;
  align-items: center;
  height: 100%;

}

#smartScheduleBox .schedule-detail-box .schedule-ul li.schedule-btn-box > span > span {
  display: flex;
  align-items: center;
  height: 100%;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.show-li {
  overflow: visible;
  position: relative;
  height: 18px;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.show-li .show-title {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.show-li .show-hover {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.8);
  box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
  color: #FFFFFF;
  display: none;
  border-radius: 2px;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.show-li:first-child .show-hover {
  left: 0;
  transform: translateY(0);
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.show-li:hover .show-hover {
  display: block;
  z-index: 1;
  white-space: normal;
  word-break: break-all;
  max-height: 100px;
  overflow: scroll;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li.show-li .show-hover:before {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: -14px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 7px;
  border-style: solid;
  border-color: transparent transparent #000000 transparent;
  opacity: 0.8;
  border-radius: 2px;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li:nth-child(1) {
  width: 18%;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li:nth-child(2) {
  width: 34%;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li:nth-child(3) {
  width: 20%;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li:nth-child(4) {
  width: 8%;
}

#smartScheduleBox .schedule-detail-box .schedule-ul li:nth-child(5) {
  width: 20%;
  margin-right: 0;
  display: flex;
  align-items: center;
}


#smartScheduleBox .quick-acting-box .schedule-ul li:nth-child(1) {
  width: 23%;
}

#smartScheduleBox .quick-acting-box .schedule-ul li:nth-child(2) {
  width: 49%;
}

#smartScheduleBox .quick-acting-box .schedule-ul li:nth-child(3) {
  width: 8%;
}

#smartScheduleBox .quick-acting-box .schedule-ul li:nth-child(4) {
  width: 20% !important;
  margin-right: 0 !important;;
  display: flex;
  align-items: center;
}
#smartScheduleBox .quick-acting-box .show-li-detail{
  margin-right: 30px;
}


#smartScheduleBox .schedule-detail-box .schedule-btn {
  cursor: pointer;
}


#smartScheduleBox .schedule-detail-box .schedule-btn.schedule-btn-refuse {
  margin: 0 12px;
}

#smartScheduleBox .schedule-detail-box .status .schedule-btn {
  color: #000000;
  font-size: 12px;
}

#smartScheduleBox .schedule-detail-box .status .schedule-btn.schedule-btn-hover {
  color: $styleColor;
}

#smartScheduleBox .schedule-detail-box .new-sel {
  color: $styleColor !important;
}

#smartScheduleBox .schedule-detail-box .new-sel-none {
  display: none !important;
}

#smartScheduleBox .schedule-detail-box .new-sel-prohibit {
  opacity: 0.45;
}
#smartScheduleBox .schedule-detail-box .schedule-btn-box {
  display: flex;
  align-items: center;
  position: relative;
  overflow: visible !important;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box {
  position: absolute;
  z-index: 1;
  width: 88px;
  top: 32px;
  left: -28px;
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(169, 169, 169, 0.7);
  border: 1px solid #D8D8D8;
  border-radius: 4px;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box:before {
  box-sizing: content-box;
  width: 0px;
  height: 0px;
  position: absolute;
  top: -11.5px;;
  right: 6px;
  padding: 0;
  border-bottom: 6px solid white;
  border-top: 6px solid transparent;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  display: block;
  content: '';
  z-index: 12;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box:after {
  box-sizing: content-box;
  width: 0px;
  height: 0px;
  position: absolute;
  top: -14px;;
  right: 5px;
  padding: 0;
  border-bottom: 7px solid #D8D8D8;
  border-top: 7px solid transparent;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  display: block;
  content: '';
  z-index: 10
}


#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box .select-box-tag {
  height: 30px;
  padding: 7px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box .select-box-tag:last-child {
  border-bottom: none;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box .select-box-tag:hover {
  background: $styleBg1;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box .select-box-tag.curr {
  color: $styleColor;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .select-box .select-box-tag.curr:after {
  content: '';
  width: 15px;
  height: 9px;
  border: 3px solid $styleColor;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 50%;
  right: 6px;
  vertical-align: middle;
  transform: translateY(-50%) rotate(-45deg) scale(.5);
  box-sizing: border-box;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .schedule-btn-i {
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .schedule-btn-i.schedule-btn-i-top {
  width: 0;
  height: 0;
  margin-left: 4px;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 5px solid $styleColor;
}

#smartScheduleBox .schedule-detail-box .schedule-btn-box .schedule-btn-i.schedule-btn-i-bottom {
  width: 0;
  height: 0;
  margin-left: 4px;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 5px solid #999;
}


#smartScheduleBox .schedule-detail-box .status .schedule-btn-accept.sel {
  color: #42B810;
}

#smartScheduleBox .schedule-detail-box .status .schedule-btn-refuse.sel {
  color: $styleColor;
}

#smartScheduleBox .schedule-detail-box .status .schedule-btn-wait.sel {
  color: #FF9100;
}


#smartScheduleBox .smart-show-list .sel-list-ul .sel-list-li {
  display: flex;
  align-items: center;
  margin-top: 15px;
  cursor: pointer;
}

#smartScheduleBox .sel-list-ul .sel-list-li .sel-list-title {
  padding-left: 24px;
  font-size: 13px;
  font-weight: 400;
  color: #222222;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

#smartScheduleBox .sel-list-ul .sel-list-li .upcoming-title {
  padding-left: 12px;
}

#smartScheduleBox .sel-list-ul .sel-list-li .sel-list-title:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #FF8800;
}

#smartScheduleBox .sel-list-ul .sel-list-li .upcoming-title:after {
  left: 0;
}

#smartScheduleBox .sel-list-ul .sel-list-li .ycya-title:after {
  background: #00BC55;
}

#smartScheduleBox .sel-list-ul .sel-list-li.gray .sel-list-title {
  text-decoration: line-through;
  color: #999999;
}

#smartScheduleBox .sel-list-ul .sel-list-li.gray .sel-list-title:after {
  opacity: 0.3;
}

#smartScheduleBox .sel-list-check {
  width: 14px;
  height: 14px;
  position: relative;
}

#smartScheduleBox .sel-list-check:before {
  border-radius: 0;
  margin-right: 0;
  border: 1px solid #CCCCCC;
}

#smartScheduleBox .gray .sel-list-check:before {
  border: 1px solid #CFCFCF;
  background: #CFCFCF;
}

#smartScheduleBox .upcoming-detail-box {
  width: 362px;
  position: fixed;
  top: 300px;
  left: 500px;
  background: #ffffff;
  box-shadow: 0px 2px 20px 0px rgba(165, 165, 165, 0.5);
  border-radius: 4px;
  border: 1px solid #CCCCCC;
  overflow: hidden;
  font-size: 12px;
  font-weight: 400;
  color: #000000;
  line-height: 16px;
  z-index: 12;
}

#smartScheduleBox .upcoming-detail-box .upcoming-detail-close {
  width: 12px;
  height: 12px;
  position: absolute;
  top: 14px;
  right: 14px;
  cursor: pointer;
}

#smartScheduleBox .upcoming-detail-box .upcoming-detail-title {
  width: 100%;
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  line-height: 28px;
  padding: 40px 16px 16px;
  background-color: #FF8800;
  background-image: url("/img/schedule/smart-upcoming-bg.png");
  background-repeat: no-repeat;
  background-size: 362px 84px;
  background-position: bottom center;
  word-break: break-all;
}

#smartScheduleBox .upcoming-detail-box .upcoming-detail-tips {
  font-size: 10px;
  font-weight: 400;
  color: #FF0000;
  line-height: 14px;
  padding: 8px 30px;
  background-color: #FFEEEE;
  background-image: url("/img/schedule/tips.png");
  background-repeat: no-repeat;
  background-size: 10px 10px;
  background-position: 15px center;
}

#smartScheduleBox .upcoming-detail-box .upcoming-detail-intr {
  padding: 16px 16px 2px 42px;
  color: #000000;
  background-image: url("/img/schedule/upcoming.png");
  background-repeat: no-repeat;
  background-size: 16px 16px;
  background-position: 17px 17px;
}

#smartScheduleBox .upcoming-detail-box .upcoming-detail-content {
  color: #666666;
  padding: 0 16px 16px 42px;
  max-height: 80px;
  overflow-y: auto;
  word-break: break-all;
}

#smartScheduleBox .sub-schedule .selection-content-box::-webkit-scrollbar,
#smartScheduleBox .smart-search-ul::-webkit-scrollbar,
#smartScheduleBox .upcoming-detail-box ::-webkit-scrollbar {
  width: 4px;
}

#smartScheduleBox .smart-add-btn {
  width: 54px;
  height: 54px;
  position: fixed;
  bottom: 14px;
  right: 24px;
  background-image: url("/img/schedule/btn_add.png");
  background-repeat: no-repeat;
  background-size: 108px 54px;
  cursor: pointer;
  z-index: 11;
}

#smartScheduleBox .smart-add-btn:hover {
  background-position: -54px 0;
}

#smartScheduleBox .smart-hover-box {
  position: absolute;
  top: 0;
  left: 0;
  padding: 10px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.8);
  box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
  color: #FFFFFF;
  z-index: 11;
  white-space: normal;
  word-break: break-all;
  max-width: 300px;
  display: none;
  border-radius: 2px;
}

#smartScheduleBox .smart-hover-box:after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  top: -14px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 7px;
  border-style: solid;
  border-color: transparent transparent #000000 transparent;
  opacity: 0.8;
}

#smartScheduleBox .smart-hover-box.rotate:after {
  border-color: #000000 transparent transparent transparent;
  top: auto;
  bottom: -14px;
}

#smartScheduleBox .smart-tips-box {
  position: absolute;
  padding: 6px 12px;
  font-size: 12px;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  z-index: -1;
  border-radius: 2px;
}

#smartScheduleBox .column-week-box {
  margin: 8px 10px;
}

#smartScheduleBox .column-week-box .column-week-title-box {
  display: flex;
  align-items: center;
}

#smartScheduleBox .column-week-box .column-week-title {
  margin-right: 4px;
  color: #666666;
  line-height: 18px;
  white-space: nowrap;
}

#smartScheduleBox .column-week-label {
  color: #FF8800;
  /*border: 1px solid #FF8800;*/
  padding: 2px 4px;
  border-radius: 2px;
  line-height: 12px;
  white-space: nowrap;
}

#smartScheduleBox .column-week-box .column-week-day {
  font-size: 20px;
  font-weight: 600;
  color: #333333;
  line-height: 28px;
  margin-top: 4px;
}

#smartScheduleBox .column-user-all.gray .column-week-title,
#smartScheduleBox .column-user-all.gray .column-week-day {
  color: #BFBFBF;
}

#smartScheduleBox .column-user-all.curr .column-week-title,
#smartScheduleBox .column-user-all.curr .column-week-day {
  color: $styleColor;
}

#smartScheduleBox .smart-show-month .smart-calendar-month {
  width: 100%;
  height: 100%;
}

#smartScheduleBox .smart-show-month .smart-month-header,
#smartScheduleBox .smart-show-month .smart-month-content {
  display: flex;
  flex-wrap: wrap;
}

#smartScheduleBox .smart-show-month .smart-month-header {
  box-shadow: 0px 2px 2px 0px #EBEBEB;
  font-size: 13px;
  font-weight: 500;
  color: #222222;
  position: relative;
  z-index: 1;
}

#smartScheduleBox .smart-show-month .smart-month-content {
  height: calc(100% - 36px);
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  line-height: 17px;
}

#smartScheduleBox .smart-show-month .smart-month-header .smart-month-list,
#smartScheduleBox .smart-show-month .smart-month-content .smart-month-detail-box {
  width: calc(100% / 7);
  border-right: 1px solid #DEE0E3;
  border-top: 1px solid #DEE0E3;
  position: relative;
}

#smartScheduleBox .smart-show-month .smart-month-content .smart-month-detail-box.curr .smart-month-box,
#smartScheduleBox .smart-show-month .smart-month-content .smart-month-detail-box.curr .column-user-all-list,
#smartScheduleBox .smart-show-month .smart-month-content .smart-month-detail-box.curr .show-more-modal {
  background: #FCFCFC;
}

#smartScheduleBox .smart-show-month .smart-month-header .smart-month-list:nth-child(7n),
#smartScheduleBox .smart-show-month .smart-month-content .smart-month-detail-box:nth-child(7n) {
  border-right: 0;
}

#smartScheduleBox .smart-show-month .smart-month-content .smart-month-detail-box:nth-child(-n+7) {
  border-top: 0;
}

#smartScheduleBox .smart-show-month .smart-month-header .smart-month-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  border-top: 0;
}

#smartScheduleBox .smart-show-month .smart-month-header .smart-month-list.curr {
  color: $styleColor;
}

#smartScheduleBox .smart-show-month .smart-month-box {
  display: flex;
  align-items: center;
  padding: 2px 5px;
}

#smartScheduleBox .smart-show-month .smart-month-box .smart-month-day {
  height: 27px;
  border-radius: 50%;
  margin-right: 5px;
  font-size: 17px;
  color: #333333;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  font-weight: 600;
}

#smartScheduleBox .smart-show-month .smart-month-box.gray .smart-month-day {
  color: #BFBFBF;
}

#smartScheduleBox .smart-show-month .smart-month-box.curr .smart-month-day {
  width: 27px;
  background: $styleColor;
  color: #FFFFFF;
}

#smartScheduleBox .smart-show-month .smart-month-box .lundar {
  width: calc(100% - 32px);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

#smartScheduleBox .smart-show-month .smart-month-box .lundar.heightline {
  color: #E03737;
}

#smartScheduleBox .smart-show-month .smart-month-content .column-user-all-list {
  height: calc(100% - 31px);
  overflow: hidden;
  position: relative;
}

#smartScheduleBox .smart-show-month .smart-month-content .all-day-html {
  white-space: nowrap;
  overflow: hidden;
  display: flex;
  align-items: center;
  color: #000000;
  margin-right: 5px;
  cursor: pointer;
  line-height: 18px;
}

#smartScheduleBox .smart-show-month .smart-month-content .all-day-html.hide {
  display: none;
}

#smartScheduleBox .all-day-html:hover,
#smartScheduleBox .block-html:hover,
#smartScheduleBox .smart-show-month .smart-month-content .all-day-html:hover {
  box-shadow: 0px 2px 4px 0px #CCCCCC;
}

#smartScheduleBox .smart-show-month .smart-month-content .all-day-html .icon-style {
  width: 6px;
  height: 6px;
  margin: 0 3px 0 7px;
  flex-shrink: 0;
}

#smartScheduleBox .smart-show-month .smart-month-content .all-day-html .text {
  width: calc(100% - 18px);
  margin-left: 5px;
}

#smartScheduleBox .smart-show-month .smart-month-content .show-more-modal {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: calc(100% - 71px);
  background: #FFFFFF;
  padding-left: 7px;
  cursor: pointer;
}

#smartScheduleBox .smart-show-month .smart-month-content .show-more-modal:hover {
  color: #2D91E6;
}

#smartScheduleBox .smart-detail-list-modal {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
}

#smartScheduleBox .smart-detail-list-modal .smart-detail-box {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 360px;
  background: #FFFFFF;
  box-shadow: 0px 4px 12px 0px rgba(182, 182, 182, 0.5);
  font-size: 12px;
  font-weight: 400;
  color: #000000;
  line-height: 17px;
  padding: 16px 0 16px 16px;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-close {
  position: absolute;
  top: 15px;
  right: 10px;
  width: 18px;
  height: 18px;
  background: url(/img/close.png) no-repeat center;
  background-size: 10px;
  cursor: pointer;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-intr {
  color: #666666;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  line-height: 28px;
  margin: 4px 0 12px 0;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list-box {
  max-height: 256px;
  overflow: scroll;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list {
  display: flex;
  align-items: center;
  height: 22px;
  line-height: 22px;
  margin: 0 8px 0 -5px;
  cursor: pointer;
}

#smartScheduleBox .smart-modal-list0.modal-sel,
#smartScheduleBox .smart-modal-list0:hover {
  background: $styleBg2;
}

#smartScheduleBox .smart-modal-list1.modal-sel,
#smartScheduleBox .smart-modal-list1:hover {
  background: #FDE2E2;
}

#smartScheduleBox .smart-modal-list2.modal-sel,
#smartScheduleBox .smart-modal-list2:hover {
  background: #FEE9D2;
}

#smartScheduleBox .smart-modal-list3.modal-sel,
#smartScheduleBox .smart-modal-list3:hover {
  background: #FAF1D0;
}

#smartScheduleBox .smart-modal-list4.modal-sel,
#smartScheduleBox .smart-modal-list4:hover {
  background: #D9F5D6;
}

#smartScheduleBox .smart-modal-list5.modal-sel,
#smartScheduleBox .smart-modal-list5:hover {
  background: #D5F6F2;
}

#smartScheduleBox .smart-modal-list6.modal-sel,
#smartScheduleBox .smart-modal-list6:hover {
  background: #E1E9FF;
}

#smartScheduleBox .smart-modal-list7.modal-sel,
#smartScheduleBox .smart-modal-list7:hover {
  background: #D9F3FD;
}

#smartScheduleBox .smart-modal-list8.modal-sel,
#smartScheduleBox .smart-modal-list8:hover {
  background: #ECE2FE;
}

#smartScheduleBox .smart-modal-list9.modal-sel,
#smartScheduleBox .smart-modal-list9:hover {
  background: #F8DEF8;
}

#smartScheduleBox .smart-modal-list10.modal-sel,
#smartScheduleBox .smart-modal-list10:hover {
  background: #DEE0E3;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list:last-child {
  margin-bottom: 0;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list .icon-style {
  width: 6px;
  height: 6px;
  border-radius: 1px;
  margin: 0 3px 0 6px;
  flex-shrink: 0;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list .time {
  width: 45px;
  flex-shrink: 0;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list .text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#smartScheduleBox .smart-detail-list-modal .smart-modal-list-box::-webkit-scrollbar {
  width: 6px;
}

/*日历*/
#calendarAllBox {
  width: 100%;
}

#calendarAllBox.hide .calendar-day {
  display: none;
}

#calendarAllBox .calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#calendarAllBox .calendar-header .calendar-title {
  font-size: 14px;
  font-weight: 500;
  color: #222222;
}

#calendarAllBox .calendar-change-box {
  display: flex;
}

#calendarAllBox .calendar-icon {
  background-image: url("/img/schedule/calendar.png");
  background-repeat: no-repeat;
  width: 20px;
  height: 20px;
  background-size: 240px 20px;
  margin-left: 12px;
  cursor: pointer;
  position: relative;
}

#calendarAllBox .calendar-icon:hover:after {
  position: absolute;
  top: 26px;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 12px;
  font-size: 12px;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  z-index: 1;
  border-radius: 2px;
}

#calendarAllBox .calendar-icon.pre {
  background-position: -20px 0;
}

#calendarAllBox .calendar-icon.pre:hover {
  background-position: 0px 0;
}

#calendarAllBox .calendar-icon.pre:hover:after {
  content: "\4e0a\4e2a\6708";
  z-index: 1;
}

#calendarAllBox .calendar-icon.next {
  background-position: -60px 0;
}

#calendarAllBox .calendar-icon.next:hover {
  background-position: -40px 0;
}

#calendarAllBox .calendar-icon.next:hover:after {
  content: "\4e0b\4e2a\6708";
  z-index: 1;
}

#calendarAllBox .calendar-icon.toggle {
  background-position: -100px 0;
}

#calendarAllBox .calendar-icon.toggle:hover {
  background-position: -80px 0;
}

#calendarAllBox .calendar-icon.toggle:hover:after {
  content: "\6536\8d77\65e5\5386";
  z-index: 14;
}

#calendarAllBox .calendar-icon.toggle.not-toggle {
  display: none;
}

#calendarAllBox .calendar-icon.toggle.hide {
  background-position: -140px 0;
}

#calendarAllBox .calendar-icon.toggle.hide:hover {
  background-position: -120px 0;
}

#calendarAllBox .calendar-icon.toggle.hide:hover:after {
  content: "\5c55\5f00\65e5\5386";
  z-index: 1;
}

#calendarAllBox .calendar-day {
  font-size: 12px;
  line-height: 17px;
  font-weight: 500;
}

#calendarAllBox .calendar-day .rest,
#calendarAllBox .calendar-day .gray {
  color: #BFBFBF;
}

#calendarAllBox .calendar-day .day-header,
#calendarAllBox .calendar-day .day-content .day-ul {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

#calendarAllBox .calendar-day .day-header,
#calendarAllBox .calendar-day .day-ul {
  margin-top: 11px;
}

#calendarAllBox .calendar-day .day-list {
  width: 22px;
  height: 22px;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

#calendarAllBox .calendar-day .day-content .day-list {
  cursor: pointer;
  position: relative;
}

#calendarAllBox .calendar-day .day-content .day-list:hover {
  background: #E7E7E7;
  border-radius: 50%;
}

#calendarAllBox .calendar-day .day-content .day-list.curr {
  color: $styleColor;
  border-radius: 50%;
  line-height: normal;
}

#calendarAllBox .calendar-day .day-content .day-list.sel {
  background: $styleBg2;
  color: $styleColor;
  border-radius: 50%;
  line-height: normal;
}

#calendarAllBox .calendar-day .day-list .day-radio {
  width: 4px;
  height: 4px;
  background: $styleColor;
  border-radius: 50%;
  position: absolute;
  bottom: -7px;
  left: 50%;
  transform: translateX(-50%);
}

#calendarAllBox .calendar-day .day-list.rest .day-radio,
#calendarAllBox .calendar-day .day-list.gray .day-radio {
  background: #BFBFBF;
}

.schedule-btn-box .quick-acting-btu{
  cursor: pointer;
  color: $styleColor;
}
.quick-acting-btu + .quick-acting-btu{
  margin-left: 10px;
}
.schedule-btn-box .quick-acting-btu-distory{
  color: #E0323673;
}
.create-quick-acting-btu{
  cursor: pointer;
  color: #E03236;
  margin-right: 26px;
  font-size: 13px;
}
</style>
