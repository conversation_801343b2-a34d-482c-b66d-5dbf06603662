/**
 * 2021.08.04 日历组件
 * */
export function initCalendar($) {
  'use strict';
  var ctrl = this;

  // 日历组件
  function calendar(callBack) {
    var that = this;
    // 服务器时间
    that.serverDate = new Date(new Date().toLocaleDateString()).getTime();
    that.serverDateTime = new Date().getTime();
    // 当前选择时间
    that.selDate = that.serverDate;
    // 上一次选择的时间
    that.preSelDate = that.serverDate;
    // 当天状态
    that.status = "";
    // 当月信息
    that.monthData = "";
    // 回调
    that.callBack = callBack;
    // 展开状态
    that.toggleStatus = true;
    ctrl.nim.getServerTime().then(res => {
      let {err, obj} = res;
      if (!err) {
        that.serverDateTime = obj;
        that.serverDate = new Date(new Date(obj).toLocaleDateString()).getTime();
      }
    });

    // 选择日期
    $("body").on("click", "#calendarAllBox .day-ul .day-list", function () {
      var $this = $(this);
      var selDate = new Date($this.data("curr"));
      that.setCurrDay({
        year: selDate.getFullYear(),
        month: selDate.getMonth() + 1,
        day: selDate.getDate()
      });
    });


    // 上下月切换和收起展开
    $("body").on("click", "#calendarAllBox .calendar-icon", function () {
      var $this = $(this);
      // 收起展开
      if ($this.is(".toggle")) {
        $this.toggleClass("hide");
        $this.parents("#calendarAllBox").toggleClass("hide");
        that.callBack.toggleShow({
          toggleStatus: !that.toggleStatus
        });
        return;
      }
      var selDate = new Date(that.changeDate || that.selDate);
      var year = selDate.getFullYear();
      var month = selDate.getMonth() + 1;
      var day = selDate.getDate();
      if ($this.is(".pre")) {
        month--;
      } else if ($this.is(".next")) {
        month++;
      }
      if (month <= 0) {
        year--;
        month += 12;
      } else if (month >= 13) {
        year++;
        month -= 12;
      }
      that.setCurrDay({
        year: year,
        month: month,
        day: day,
        notChange: true
      });
    });
  }

  // 获取当前日历
  calendar.prototype.setCurrDay = function (param) {
    var that = this;
    if (!param) {
      var paramDate = new Date();
      param = {
        year: paramDate.getFullYear(),
        month: paramDate.getMonth() + 1,
        day: paramDate.getDate()
      }
    }
    // 限制选择日期
    if (param.limit) {
      that.callBack.limit = param.limit;
    }
    if (that.callBack.limit) {
      var setTime = new Date(new Date(param.year + "/" + param.month + "/" + param.day).toLocaleDateString()).getTime();
      // 切换日历月份判断1号时间
      var notChangeFlag = true;
      if (that.callBack.notToggle) {
        var startData = new Date(that.callBack.limit.startTime);
        var startDataTime = new Date(new Date(startData.getFullYear() + "/" + (startData.getMonth() + 1) + "/1").toLocaleDateString()).getTime();
        if (setTime == startDataTime) {
          notChangeFlag = false;
        }
      }
      if (notChangeFlag && (setTime < that.callBack.limit.startTime || (that.callBack.limit.type == 2 && setTime > that.callBack.limit.startTime + that.callBack.limit.durTime))) {
        var setMsg = that.callBack.limit.msg;
        if (setTime < that.callBack.limit.startTime) {
          setMsg = "不能选择已经过去的时间";
          if (that.callBack.limit.type == 2) {
            setMsg = "结束时间不能小于开始时间";
          }
        }
        ctrl.toast({title: setMsg, type: 2});
        return new Promise(function (resolve, reject) {
          resolve("")
        });
      }
    }
    param.type = ctrl.userInfo.deptId || "330007";
    ctrl.nim.getServerTime().then(res => {
      let {err, obj} = res;
      if (!err) {
        var currDate = new Date(new Date(obj).toLocaleDateString()).getTime();
        // 不是当天重新渲染日历今天
        if (that.serverDate != currDate) {
          var $calendarAllBox = $("#calendarAllBox");
          $calendarAllBox.find(".day-list.curr").removeClass("curr");
          var $lastDay = $calendarAllBox.find('.day-list[data-curr="' + that.serverDate + '"] .day-title');
          $lastDay.text($lastDay.data("day"));
          var currDay = $calendarAllBox.find('.day-list[data-curr="' + currDate + '"]').addClass("curr");
          currDay.find(".day-title").text("今");
        }
        that.serverDateTime = obj;
        that.serverDate = new Date(new Date(obj).toLocaleDateString()).getTime()
      }
    });

    var firstDay = new Date(param.year + '/' + param.month);
    var lastYear = param.year;
    var lastMonth = param.month + 1;
    if (lastMonth > 12) {
      lastYear++;
      lastMonth -= 12;
    }
    var lastDay = new Date(new Date(lastYear + '/' + lastMonth).getTime() - 24 * 60 * 60 * 1000);
    // 周日开始
    // var startTime = ctrl.dateFormat(firstDay.getTime() - firstDay.getDay() * 24 * 60 * 60 * 1000, "yyyy-MM-dd");
    // var endTime = ctrl.dateFormat(lastDay.getTime() + (6 - lastDay.getDay()) * 24 * 60 * 60 * 1000, "yyyy-MM-dd");
    // 周一开始
    var startTime = ctrl.dateFormat(firstDay.getTime() - (firstDay.getDay() == 0 ? 6 : firstDay.getDay() - 1) * 24 * 60 * 60 * 1000, "yyyy-MM-dd");
    var endTime = ctrl.dateFormat(lastDay.getTime() - (lastDay.getDay() - 7) * 24 * 60 * 60 * 1000, "yyyy-MM-dd");

    if (that.callBack.default) {
      // 默认日历
      return new Promise(function (resolve, reject) {
        var defaultStartDate = new Date(startTime);
        var defaultEndDate = new Date(endTime);
        var defaultDuring = (defaultEndDate.getTime() - defaultStartDate.getTime()) / 24 / 60 / 60 / 1000;
        var calendarList = [];
        var weekList = ["一", "二", "三", "四", "五", "六", "日"]
        for (var i = 0; i <= defaultDuring; i++) {
          var item = new Date(defaultStartDate.getTime() + i * 24 * 60 * 60 * 1000);
          calendarList[i] = {
            year: item.getFullYear(),
            month: item.getMonth() + 1,
            day: item.getDate(),
            week: weekList[i % 7],
            solarDate: ctrl.dateFormat(item, "yyyy-M-dd")
          }
        }
        var data = {
          calendar: {year: param.year, month: param.month},
          calendarList: calendarList
        }
        if (!param.notChange) {
          that.preSelDate = that.selDate;
          that.selDate = new Date(param.year + '/' + param.month + '/' + param.day).getTime();
          that.monthData = data;
        }
        data.notChange = param.notChange;
        data.more = param.more;
        resolve(that.currDayHtml(data));
      });
    } else {
      // 公司日历
      return new Promise(function (resolve, reject) {
        ctrl.coaSdk.smartCalendar({
          searchMonth: param.year + '-' + param.month,
          startTime: startTime,
          endTime: endTime
        }).then(function (res) {
          if (res && res.data && res.data.length > 0) {
            var data = {
              calendar: {year: param.year, month: param.month},
              calendarList: res.data
            }
            if (!param.notChange) {
              that.preSelDate = that.selDate;
              that.selDate = new Date(param.year + '/' + param.month + '/' + param.day).getTime();
              that.monthData = data;
            }
            data.notChange = param.notChange;// 不更选中改日期
            data.more = param.more;// 多个日期组件初始化

            resolve(that.currDayHtml(data));
          } else {
            resolve("");
            ctrl.toast({title: "系统错误", type: 2});
          }
        });
      });
    }
  }

  // 当日html
  calendar.prototype.currDayHtml = function (data) {
    var that = this;
    var currInfo = data.calendar;
    var list = data.calendarList;
    var html = "";
    html += '<div id="calendarAllBox">';
    html += ' <div class="calendar-header">';
    html += '   <div class="calendar-title">' + (currInfo.year + '年' + currInfo.month + '月') + '</div>';
    html += '   <div class="calendar-change-box">';
    html += '     <i class="calendar-icon pre"></i>';
    html += '     <i class="calendar-icon next"></i>';
    html += '     <i class="calendar-icon toggle' + (that.callBack.notToggle ? ' not-toggle' : '') + '"></i>';
    html += '   </div>';
    html += ' </div>';
    html += ' <div class="calendar-day">';
    html += '   <div class="day-header">';
    html += '     <div class="day-list">一</div>';
    html += '     <div class="day-list">二</div>';
    html += '     <div class="day-list">三</div>';
    html += '     <div class="day-list">四</div>';
    html += '     <div class="day-list">五</div>';
    html += '     <div class="day-list">六</div>';
    html += '     <div class="day-list">日</div>';
    html += '   </div>';
    html += '   <div class="day-content">';
    // 拆分成多行
    var idx = 0;
    var listArr = [];
    var selWeekList = [];
    for (var i = 0; i < list.length; i++) {
      if (i != 0 && i % 7 == 0) {
        idx++;
      }
      if (!listArr[idx]) {
        listArr[idx] = [];
      }
      listArr[idx].push(list[i]);
    }
    for (var i = 0; i < listArr.length; i++) {
      var listArrI = listArr[i];
      html += '   <div class="day-ul">';
      for (var j = 0; j < listArrI.length; j++) {
        // 兼容换年接口数据问题
        if (listArrI[j].month == 0) {
          listArrI[j].month = 12;
          listArrI[j].year--;
          listArrI[j].solarDate = `${listArrI[j].year}-${listArrI[j].month}-${listArrI[j].day}`;
        } else if (listArrI[j].month == 13) {
          listArrI[j].month = 1;
          listArrI[j].year++;
          listArrI[j].solarDate = `${listArrI[j].year}-${listArrI[j].month}-${listArrI[j].day}`;
        }
        var curDate = new Date(listArrI[j].solarDate.replace(/-/g, "/")).getTime();// 获取当天0点时间戳
        // 不是当前月
        var notCurrMonth = listArrI[j].isMonth == 1;
        // 过去了的时间
        var beforeToday = curDate - new Date(new Date().toLocaleDateString()).getTime() < 0;
        // 放假
        if (!listArrI[j].status && (listArrI[j].week == '六' || listArrI[j].week == '日')) {
          listArrI[j].status = 2;
        }
        if (curDate == that.selDate) {
          that.status = listArrI[j].status;
        }
        if (curDate == that.selDate && selWeekList.length == 0) {
          selWeekList = listArrI;
        }
        html += '   <div class="day-list ' + (curDate == that.serverDate ? 'curr ' : '') + (curDate == that.selDate ? 'sel ' : '') + (listArrI[j].status == 2 ? 'rest ' : '') + (notCurrMonth || beforeToday ? 'gray ' : '') + '" data-status="' + listArrI[j].status + '" data-month="' + listArrI[j].month + '" data-curr="' + curDate + '">';
        html += '     <div class="day-title" data-day="' + listArrI[j].day + '">' + (curDate == that.serverDate ? '今' : listArrI[j].status == 2 ? '休' : listArrI[j].day) + '</div>';
        if (listArrI[j].hasSchedule) {
          html += '   <div class="day-radio"></div>';
        }
        html += '   </div>';
      }
      html += '   </div>';
    }
    html += '   </div>';
    html += ' </div>';
    html += '</div>';
    // 选择日历回调
    var preSelDate = new Date(that.preSelDate);
    var selDate = new Date(that.selDate);
    if (!data.notChange) {
      that.callBack.changeDate({
        selDate: selDate.toLocaleDateString(),
        serverDate: new Date(that.serverDate).toLocaleDateString(),
        serverDateTime: that.serverDateTime,
        status: that.status,
        monthData: that.monthData,
        selWeekList: selWeekList,
        more: data.more
      });
      // 不同月/年份重新渲染
      // if (preSelDate.getMonth() != selDate.getMonth() || preSelDate.getFullYear() != selDate.getFullYear() || (that.changeDate && (that.changeDate.getMonth() != selDate.getMonth() || that.changeDate.getFullYear() != selDate.getFullYear()))) {
      $("#calendarAllBox").parent().html(html);
      that.changeDate = new Date(currInfo.year + "/" + currInfo.month + "/");
      // } else {
      //   $("#calendarAllBox").find('.day-content .day-list.sel').removeClass("sel");
      //   $("#calendarAllBox").find('.day-content .day-list[data-curr="' + that.selDate + '"]').addClass("sel").siblings();
      // }
    } else {
      that.changeDate = new Date(currInfo.year + "/" + currInfo.month + "/");
      $("#calendarAllBox").parent().html(html);
    }
    return html;
  }

  return calendar;
}
