import {emitMsg, getFreeLoginUrl, linkFormat} from "@utils";
import {quickActingSearchFilling} from "@utils/net/api";
import {toast} from "@comp/ui";

/**
 * 2021.08.04 日能日程面板
 * */
export function initSmartScheduleBox($) {
  'use strict';
  var ctrl = this;
  var $smartScheduleBox = $("#smartScheduleBox");
  // 弹窗元素
  let alertObj = {};

  function smartScheduleBox(flag) {
    var that = this;
    // 1列表 2日 3周 4月
    that.type = 1;
    // 切换显示日程、待办、YCYA任务
    that.toggleList = [true, true, true,true];
    // 拖拽选择的信息
    that.selInfo = {};
    // 允许搜索状态
    that.smartSearchFlag = true;
    // 当前用户信息
    that.userInfo = ctrl.userInfo;
    // 切换成不显示的关注日程
    that.hideAttentionMap = {};
    // 关注日程数据
    that.attentionMap = {};
    // 关注日程数据按天区分
    that.attentionDateMap = {};
    // 关注列表
    that.attentionList = [];
    // 显示全部全天日程 日/周列表
    that.showAllFlag = [false, false];
    // 月视角时间宽度
    that.timeWidth = 40;
    // 日视角滚动位置
    that.scrollLeft = 0;
    // 是否触发下拉框按钮
    that.selectBoxShow = true;
    // 关注颜色
    that.attentionColorList = [
      {color: "#E03236", bg: "#FFE4E4"}, {color: "#B16206", bg: "#FEE9D2"},
      {color: "#AA7804", bg: "#FAF1D0"}, {color: "#237B19", bg: "#D9F5D6"}, {color: "#078372", bg: "#D5F6F2"},
      {color: "#1D4CBA", bg: "#E1E9FF"}, {color: "#037eaa", bg: "#D9F3FD"}, {color: "#4E1BA7", bg: "#ECE2FE"},
      {color: "#8C218C", bg: "#F8DEF8"}, {color: "#9E1361", bg: "#FDDDEF"}, {color: "#383D44", bg: "#DEE0E3"}
    ];
    if (!flag) {
      // 初始化日历组件
      ctrl.calendar = new ctrl.initCalendar({
        changeDate: function (data) {
          ctrl.loading();
          that.selDate = data.selDate;
          that.serverDate = data.serverDate;
          that.serverDateTime = data.serverDateTime;
          that.status = data.status;
          that.monthData = data.monthData;
          that.selWeekList = data.selWeekList;
          that.initHeaderHtml();
          $smartScheduleBox.find(".my-schedule .selection-list").text(that.userInfo.name).data("id", that.userInfo.workerId).data("no", that.userInfo.workerNo);

          that.initSmartContentHtml();
        },
        toggleShow: function () {
          if (that.type != 1) {
            that.resize();
          }
        }
      });
    }

    // 设置当前时间线
    that.currTimeInterval = setInterval(function () {
      that.setCurrTimeLine();
    }, 60000);

    // 事项选择收起展开
    $smartScheduleBox.on("click", ".icon-arrow.toggle", function () {
      var $this = $(this);
      $this.toggleClass("hide");
      $this.parents(".smart-selection-box").toggleClass("hide");
      if (that.type != 1) {
        that.resize();
      }
    });


    $smartScheduleBox.on("click", ".smart-show-list .quick-acting-btu", async function (e) {
      e.stopPropagation();
      var $this = $(this);
      var type = $this.data("type");
      var taskStatus = $this.data("status");
      var batchNo = $this.data("batchno");
      // if(type==2 && taskStatus!=1){
      //   return;
      // }
      if(type==1){
        await that.onLookProgress($this)
      }else{
        await that.onOpenQuickActing ($this)
      }
    })

    // 事项选择点击
    $smartScheduleBox.on("click", ".selection-content-box .selection-list", function () {
      var $this = $(this);
      var type = $this.data("type");
      var id = $this.data("id");
      $this.toggleClass("sel");
      that.toggleList[type - 1] = !that.toggleList[type - 1];
      if (that.type == 1) {
        $smartScheduleBox.find(".smart-show-list .list-box" + type).toggle();
      } else {
        that.hideAttentionMap[id] = !that.hideAttentionMap[id];
        var $currBox = "";
        var $columnAll = "";
        if (that.type == 2) {
          $currBox = $smartScheduleBox.find(".smart-show-day");
          // 日视角隐藏对应人日程
          $currBox.find('.column-user-all[data-wid="' + id + '"]').toggleClass("hide").find(".all-day-html").toggleClass("hide");
          $currBox.find('.column-box[data-wid="' + id + '"]').toggleClass("hide").find(".block-html ").toggleClass("hide");
          // 全天日程切换显示更多箭头 和 显示默认时间网格
          $columnAll = $currBox.find(".column-user-all");
          if ($columnAll.not(".hide").find(".show-all-more").length > 0) {
            if (!$currBox.find(".column-user-all-toggle").is(".show")) {
              $currBox.find(".column-user-all-toggle").addClass("show");
            }
          } else {
            $currBox.find(".column-user-all-toggle").removeClass("show");
          }

          if ($columnAll.not(".hide").not(".default-column-box").length > 0) {
            $currBox.find(".default-column-box").hide();
          } else {
            $currBox.find(".default-column-box").show();
          }

          $currBox.find(".column-user-content").scrollLeft(0);
        } else if (that.type == 3) {
          $currBox = $smartScheduleBox.find(".smart-show-week");
          that.initScheduleDetailHtml();
        } else if (that.type == 4) {
          $currBox = $smartScheduleBox.find(".smart-show-month");
          if (that.hideAttentionMap[id]) {
            $currBox.find('.all-day-html[data-wid="' + id + '"]').addClass("hide");
            $currBox.find('.block-html[data-wid="' + id + '"]').addClass("hide");
          } else {
            $currBox.find('.all-day-html[data-wid="' + id + '"]').removeClass("hide")
            $currBox.find('.block-html[data-wid="' + id + '"]').removeClass("hide");
          }
          $columnAll = $currBox.find(".column-user-all-list");
          $currBox.find(".show-more-modal").hide();
          var $columnAllToggle = $currBox.find(".column-user-all-toggle");
          $columnAllToggle.removeClass("show");
          for (var i = 0; i < $columnAll.length; i++) {
            var $columnAllI = $($columnAll[i]);
            if ($columnAllI.find(".all-day-html").not(".hide").length > 3) {
              if (!that.showAllFlag[that.type - 2]) {
                var text = "还有" + ($columnAllI.find(".all-day-html").not(".hide").length - 2) + "个日程";
                if (that.type == 3) {
                  $columnAllI.find(".show-all-more").text(text).show();
                } else {
                  $columnAllI.parents(".smart-month-detail-box").find(".show-more-modal").text(text).show()
                }
              }
              if (!$columnAllToggle.is("show")) {
                $columnAllToggle.addClass("show")
              }
            }
          }
        } else {
          $currBox = $smartScheduleBox.find(".smart-show-month");
        }

        $currBox.css("paddingTop", $currBox.find(".column-user-all-toggle").outerHeight());
        that.resize();
      }
    });

    // 跳转回今天
    $smartScheduleBox.on("click", ".header-left .today-btn", function () {
      that.initDayInfoHtml(1);
    });

    // 上下天切换
    $smartScheduleBox.on("click", ".smart-right-header .icon-arrow", function () {
      var $this = $(this);
      if ($this.is(".pre")) {
        that.initDayInfoHtml(2);
      } else if ($this.is(".next")) {
        that.initDayInfoHtml(3);
      }
    });

    // 列表/日/月/周视角切换
    $smartScheduleBox.on("click", ".smart-right-header .header-list", function () {
      that.toggleViewType($(this).data("type"), that.selDate);
    });

    $smartScheduleBox.on('mouseleave', ".schedule-btn-box", function (e) {

      that.removeSelectBox()
    })
    $smartScheduleBox.on('mouseenter', ".schedule-btn-select", function (e) {
      if (that.selectBoxShow) {
        that.removeSelectBox()
        var $this = $(this);
        var $li = $this.parents(".schedule-btn-box")
        var contentHtml = that.creatSelectBox($this.context.innerText)
        $li.append(contentHtml)
        $this.parent().addClass("schedule-btn-hover")
        $this.parent().find(".schedule-btn-i").removeClass("schedule-btn-i-bottom").addClass("schedule-btn-i-top")
      }
    })
    // 日程操作
    $smartScheduleBox.on("click", ".smart-show-list .schedule-btn", function (e) {
      e.stopPropagation();
      var $this = $(this);
      var $ul = $this.parents(".schedule-ul")
      var $li = $this.parents(".schedule-btn-box")
      var type = $this.data("type");

      var id = $ul.data("sid");
      if ($this.is(".sel")) {
        return;
      }
      that.smartUpdateStatus({
        id: id,
        type: type,
        handleState: $this.context.innerText.indexOf('已') > -1,//是否处理,ture不处理，false处理
        // done: function (err, data) {
        //   if (!err) {
        //     if (type == 2) {
        //       // 拒绝的日程在列表视角不显示
        //       $ul.remove();
        //       if ($smartScheduleBox.find(".schedule-content-box .schedule-ul ").length == 0) {
        //         // 初始化日程
        //         that.initScheduleContentHtml({
        //           type: 3
        //         });
        //       }
        //       return;
        //     }
        //     $this.addClass("sel").siblings().removeClass("sel");
        //     if (!$this.parent().is("status")) {
        //       $this.parent().addClass("status");
        //     }
        //   }
        // },
        done: function (error, data, status) {
          if (!error) {
            that.removeSelectBox()
            switch (type) {
              case 1:
                //接受
                $li.html('<span class="schedule-btn schedule-btn-select" data-type="4">已接受</span><i class="schedule-btn-i schedule-btn-i-bottom"></i>')
                break
              case 2:
                //拒绝
                $li.html('<span class="schedule-btn schedule-btn-select" data-type="4">已拒绝</span><i class="schedule-btn-i schedule-btn-i-bottom"></i>')
                $ul.remove();
                if ($smartScheduleBox.find(".schedule-content-box .schedule-ul ").length == 0) {
                  // 初始化日程
                  that.initScheduleContentHtml({
                    type: 3
                  });
                }
                break
              case 3:
                //待办
                $li.html('<span class="schedule-btn schedule-btn-select" data-type="4">已待定</span><i class="schedule-btn-i schedule-btn-i-bottom"></i>')
                break
              // case 4:
              //   $this.find(".schedule-btn-img").attr("src","../../images/toTop.png")
              //   $li.append(innerHtml)
              //   break

            }
            if ($('.list-detail-box').find('.schedule-btn-box').length == $('.list-detail-box').find('.schedule-btn-select').length) {
              //是否要缩进
              $('.list-detail-box').find('ul').each(function (index, item) {
                $(this).find("li:last-child").css("margin", "0 10px").css("width", "10%")
              })
            }
            ctrl.setScheduleStatus(id, status);
          }
        }
      });
    });

    // 待办操作
    $smartScheduleBox.on("click", ".list-detail-box .upcoming-list", function (e) {
      e.stopPropagation();
      var $this = $(this);
      var title = $this.data("title");
      var content = $this.data("content");
      var tips = $this.data("tips");
      var margin = 5;
      var top = e.clientY + margin;
      var left = e.clientX + margin;
      var clientX = window.document.body.clientWidth;
      var clientY = window.document.body.clientHeight;
      if (left + 362 > clientX) {
        left = left - 362 - margin;
      }
      var html = "";
      html += '<div class="upcoming-detail-box" style="top:' + top + 'px;left:' + left + 'px">';
      html += ' <img class="upcoming-detail-close" src="../../images/schedule/close.png">';
      html += ' <div class="upcoming-detail-title">' + title + '</div>';
      tips ? html += ' <div class="upcoming-detail-tips">' + tips + '</div>' : '';
      html += ' <div class="upcoming-detail-intr">详细内容</div>';
      html += ' <div class="upcoming-detail-content">' + content + '</div>';
      html += '</div>';
      var $elm = $(html);
      var $upcomingBox = $smartScheduleBox.find(".upcoming-detail-box");
      if ($upcomingBox.length == 0) {
        $smartScheduleBox.append($elm);
      } else {
        $upcomingBox.prop("outerHTML", html);
      }
      $elm = $smartScheduleBox.find(".upcoming-detail-box");
      if (top + $elm.height() > clientY) {
        top = clientY - $elm.height() - margin;
        $elm.css("top", top);
      }
      // 关闭弹窗
      $elm.on("click", ".upcoming-detail-close", function () {
        $elm.remove();
      });
      $elm.on("click", function (e) {
        e.stopPropagation()
      });
    });

    // 清除待办/搜索弹窗
    $smartScheduleBox.on("click", function (e) {
      var $upcomingBox = $smartScheduleBox.find(".upcoming-detail-box");
      if ($upcomingBox.length > 0) {
        $smartScheduleBox.find(".upcoming-detail-box").remove();
      }
      $smartScheduleBox.find(".smart-search-result").hide();
    });

    // ycya操作
    $smartScheduleBox.on("click", ".list-detail-box .ycya-list", function (e) {
      var $this = $(this);
      var taskId = $this.data("id");
      if ($(e.target).is(".ycya-check")) {
        alertObj = ctrl.alert({
          title: "提示",
          content: '此操作不会变更任务状态，若需变更状态，请前往任务详情，勾选后，后续将不再提醒您该任务，是否确认操作？',
          isClose: false,
          done: (type) => {
            if (type == 1) {
              ctrl.coaSdk.smartTaskDel({
                taskId: taskId,
                workerId: that.userInfo.workerId
              }).then(function (res) {
                if (res.success) {
                  alertObj.hide();
                  ctrl.toast({title: res.errorMsg || '操作成功', type: 1});
                  // 获取待办
                  that.initYcyaContentHtml();
                } else {
                  ctrl.toast({title: res.errorMsg || '操作失败', type: 2});
                }
              })
            } else {
              alertObj.hide();
            }
          },
          // 返回生成的jq元素，可用于事件监听处理
          on: function ($elm) {
          }
        })
      } else {
        window.open(ctrl.config[ctrl.config.env].jjsHome + '/taskpf/task-manager/index?scheduleId=' + taskId, "login");
      }
    });

    // 搜索联系人
    $smartScheduleBox.on('compositionstart', ".smart-search-input", function () {
      that.smartSearchFlag = false;
    });
    $smartScheduleBox.on('compositionend', ".smart-search-input", function () {
      that.smartSearchFlag = true;
    });
    $smartScheduleBox.on("input", ".smart-search-input", function (e) {
      var _this = this;
      if (that.smartSearchTimer) {
        clearTimeout(that.smartSearchTimer);
        that.smartSearchTimer = "";
      }
      that.smartSearchTimer = setTimeout(function () {
        if (that.smartSearchFlag) {
          var $this = $(_this);
          var val = $this.val().trim();
          if (val.length == 0) {
            $smartScheduleBox.find(".smart-search-result").hide();
            return;
          }
          ctrl.coaSdk.selSearchApi({
            msgBody: JSON.stringify({
              workerId: ctrl.userInfo.workerId,
              name: val,
              status: "1",//默认为全部1在职2为离职
              page: 1,
              rows: 50
            })
          }).then(function (res) {
            var $searchResult = $smartScheduleBox.find(".smart-search-result");
            $searchResult.find(".smart-search-content").hide();
            $searchResult.find(".smart-search-none").hide();
            $searchResult.find(".smart-search-intr").hide();
            $searchResult.find(".smart-search-ul").hide();
            var html = "";
            if (res && res.data && res.data.empList && res.data.empList.length > 0) {
              var list = res.data.empList;
              for (var i = 0; i < list.length; i++) {
                // 过滤林董和自己
                if (list[i].workerNo == "000001" || list[i].workerNo == ctrl.userInfo.workerNo) {
                  continue;
                }
                html += '<li class="smart-search-list" data-id="' + list[i].workerId + '" data-no="' + list[i].workerNo + '" data-name="' + list[i].workerName + '">' + list[i].workerName.replace(val, '<span style="color:#E03236">' + val + '</span>') + "&nbsp;" + list[i].deptName + '</li>';
              }
            }
            $searchResult.find(".smart-search-ul").html(html).scrollTop(0);
            if (!html) {
              $searchResult.find(".smart-search-none").show();
            } else {
              $searchResult.find(".smart-search-ul").show();
              $searchResult.find(".smart-search-intr").show();
            }
            $searchResult.show();
            $searchResult.find(".smart-search-content").show();
          }, function (err) {
            ctrl.toast({title: err || '系统错误', type: 2});
          });
        }
      }, 300);
    });

    // 添加关注
    $smartScheduleBox.on("click", ".smart-search-list", function (e) {
      var $this = $(this);
      var id = $this.data("id");
      var no = $this.data("no");
      var name = $this.data("name");
      // 存在该订阅用户直接隐藏搜索结果
      if ($smartScheduleBox.find('.sub-schedule .selection-list[data-id="' + id + '"]').length > 0) {
        $smartScheduleBox.find(".smart-search-result").hide();
        return;
      }
      ctrl.loading();
      ctrl.coaSdk.smartAttentionAdd({
        followId: id,
        followNo: no,
        followName: name,
        type: 1,
      }).then(function (res) {
        ctrl.loading().hide();
        if (res.success) {
          let data = res.data;
          ctrl.toast({title: data.errorMsg || '订阅成功', type: 1});
          $smartScheduleBox.find(".smart-search-input").val("");
          $smartScheduleBox.find(".smart-search-result").hide();
          that.initAttentionContentHtml();
        } else {
          ctrl.toast({title: res.errorMsg || '系统错误', type: 2});
        }
      })
    });

    // 取消关注
    $smartScheduleBox.on("click", ".selection-list .remove-attention", function (e) {
      e.stopPropagation();
      var $this = $(this);
      var $parent = $this.parent();
      ctrl.loading();
      ctrl.coaSdk.smartAttentionDel({
        followId: $parent.data("id"),
        type: 1,
      }).then(function (res) {
        ctrl.loading().hide();
        if (res.success) {
          ctrl.toast({title: res.errorMsg || '取消订阅成功', type: 1});
          that.initAttentionContentHtml();
          // $smartScheduleBox.find('.smart-show-day .column-user-all[data-wid="' + $parent.data("id") + '"]').remove();
          // $smartScheduleBox.find('.smart-show-day .column-box[data-wid="' + $parent.data("id") + '"]').remove();
          // 移除该关注列表
          $parent.remove();
          that.hideAttentionMap[$parent.data("id")] = "";
          if (that.type == 2) {
            $smartScheduleBox.find(".smart-show-day .column-user-content").scrollLeft(0);
          }
        } else {
          ctrl.toast({title: res.errorMsg || '操作失败', type: 2});
        }
      })
    });

    // 日历点击
    $smartScheduleBox.on("mousedown", ".column-list,.curr-time-box,.sel-area,.column-user-all-list", function (e) {
      if ($(e.target).is(".show-all-more")) {
        return;
      }
      that.mouseElm(e, 1);
      console.log("$smartScheduleBoxMousedown", that.selInfo);
    });
    $smartScheduleBox.on("mousedown", ".all-day-html", function (e) {
      e.stopPropagation();
    })

    // 日历拖拽
    document.addEventListener("mousemove", function (e) {
      if (that.selInfo.startDay && !that.selInfo.allDay) {
        var scrollHeight = 48 * 24;
        var $smartRightContent = "";
        // 日/周/月视角滚动区域
        if (that.type == 2) {
          $smartRightContent = $smartScheduleBox.find(".smart-right-content .smart-show-day .column-user-content");
        } else if (that.type == 3) {
          $smartRightContent = $smartScheduleBox.find(".smart-right-content .smart-show-week .column-user-content");
        } else if (that.type == 4) {
          $smartRightContent = $smartScheduleBox.find(".smart-right-content .smart-show-month");
        }
        if (e.pageY - 20 <= $smartRightContent.offset().top) {
          if ($smartRightContent.scrollTop() > 0) {
            var scrollTop = $smartRightContent.scrollTop() - 10;
            if (scrollTop < 0) {
              scrollTop = 0;
            }
            $smartRightContent.scrollTop(scrollTop);
          }
        } else if (e.pageY + 20 >= window.document.body.clientHeight) {
          if ($smartRightContent.height() + $smartRightContent.scrollTop() < scrollHeight) {
            var scrollTop = $smartRightContent.scrollTop() + 10;
            if (scrollTop > scrollHeight) {
              scrollTop = scrollHeight;
            }
            $smartRightContent.scrollTop(scrollTop);
          }
        }
        that.mouseElm(e, 2);
        that.selTimeArea(e);
      }
    }, true);

    // 日历拖拽结束
    document.addEventListener("mouseup", function (e) {
      if (that.selInfo.startDay || that.selInfo.allDay) {
        var $selArea = "";
        var time = "";
        if (that.selInfo.allDay) {
          $selArea = $('<div class="sel-area" style="height: 21px">添加日程</div>');
          that.selInfo.$elm.prepend($selArea);
          time = ctrl.dateFormat(that.selInfo.startDay, "yyyy年MM月dd日") + "-" + ctrl.dateFormat(that.selInfo.startDay, "yyyy年MM月dd日");
        } else {
          that.mouseElm(e, 2);
          that.selTimeArea(e);
          $selArea = $smartScheduleBox.find(".sel-area");
          time = ctrl.dateFormat($selArea.data("start"), "yyyy年MM月dd日 HH:mm") + "-" + ctrl.dateFormat($selArea.data("end"), "yyyy年MM月dd日 HH:mm");
        }
        new ctrl.scheduleModal({
          parentObj: $smartScheduleBox,
          type: 1,
          workerId: that.userInfo.workerId,
          workerName: that.userInfo.name,
          x: e.pageX,
          y: e.pageY,
          time: time,
          callback: function (result) {
            $selArea.remove();
            if (result == "add") {
              if (that.type == 1) {
                // 初始化日程
                that.initScheduleContentHtml({
                  type: 3
                });
              } else {
                // 初始化日程
                that.initScheduleContentHtml({
                  type: 2,
                  empNumbers: that.attentionList.map(function (item) {
                    return item.followId
                  }).join(",")
                });
              }
            }
          }
        })
        that.selInfo = {};
      }
    }, true);

    // 快速创建日程
    $smartScheduleBox.on("click", "#scheduleAdd,.smart-add-btn", function (e) {
      var startTime = "";
      var endTime = ""
      var currDate = new Date(that.selDate);
      var serverDate = new Date(that.selDate);
      var time = "";
      if ($(this).is("#scheduleAdd") && that.selDate != that.serverDate) {
        startTime = currDate.getTime();
        // endTime = startTime + 24 * 60 * 60 * 1000;
        time = ctrl.dateFormat(startTime, "yyyy年MM月dd日") + "-" + ctrl.dateFormat(startTime, "yyyy年MM月dd日");
      } else {
        // 当天传刻度
        currDate = new Date();
        var day = serverDate.toLocaleDateString();
        var startHour = currDate.getHours();
        var startMinutes = currDate.getMinutes();
        if (startMinutes <= 30) {
          startMinutes = 30;
        } else {
          startHour++;
          startMinutes = 0;
        }
        startTime = new Date(day + " " + startHour + ":" + startMinutes + ":00").getTime();
        endTime = startTime + 30 * 60 * 1000
        time = ctrl.dateFormat(startTime, "yyyy年MM月dd日 HH:mm") + "-" + ctrl.dateFormat(endTime, "yyyy年MM月dd日 HH:mm");
      }
      new ctrl.scheduleModal({
        parentObj: $smartScheduleBox,
        type: 1,
        workerId: that.userInfo.workerId,
        workerName: that.userInfo.name,
        time: time,
        callback: function (result) {
          if (result == "add") {
            if (that.type == 1) {
              // 初始化日程
              that.initScheduleContentHtml({
                type: 3
              });
            } else {
              // 初始化日程
              that.initScheduleContentHtml({
                type: 2,
                empNumbers: that.attentionList.map(function (item) {
                  return item.followId
                }).join(",")
              });
            }
          }
        }
      })
    });

    // 点击快速行动显示详情
    $smartScheduleBox.on("click", ".schedule-content-box .quick-acting", function (e) {
      e.stopPropagation();
      if(e.currentTarget?.dataset?.batchno){
        // console.log("e",e)
        ctrl.onClickQuickActing(e.currentTarget?.dataset?.batchno,e.clientX,e.clientY);
      }
    })
    // 点击日程显示详情-content-box .schedule-ul
    $smartScheduleBox.on("click", ".all-day-html,.block-html,.schedule-content-box .schedule-ul", function (e) {
      var $this = $(this);
      if($this.context.classList.contains("noschedule")) return;
      $this.addClass("modal-sel");
      var id = $this.data("sid");
      var wid = $this.data("wid") || that.userInfo.workerId;
      var name = $this.data("name") || that.userInfo.name;

      new ctrl.scheduleModal({
        parentObj: $smartScheduleBox,
        type: 3,
        id: id,
        workerId: wid,
        workerName: name,
        x: e.pageX,
        y: e.pageY,
        colorType: $this.data("index") || 0,
        callback: function (result, status) {
          $smartScheduleBox.find(".modal-sel").removeClass("modal-sel");
          if (result == "delete" || result == "transfer" || result == "update" || result == "feedback") {
            if (that.type == 1) {
              // 初始化日程
              that.initScheduleContentHtml({
                type: 3
              });
            } else {
              // 初始化日程
              that.initScheduleContentHtml({
                type: 2,
                empNumbers: that.attentionList.map(function (item) {
                  return item.followId
                }).join(",")
              });
            }
            ctrl.setScheduleStatus(id, status);
          }
        }
      })
    });

    // 窗口大小变化
    $(window).resize(function () {
      if ($smartScheduleBox.css("display") != "none" && that.type != 1) {
        that.resize();
      }
    });

    // 提示框
    $smartScheduleBox.on("mouseenter", ".show-title", function (e) {
      var $this = $(this);
      var $hoverBox = $smartScheduleBox.find(".smart-hover-box");
      $hoverBox.text($this.data("title"));
      var $font = $('<span style="position: fixed;z-index: -1">' + $this.data("title") + '</span>');
      $this.append($font);
      var textWidth = $font.width();
      $font.remove();
      // 文字省略悬浮才显示
      if (textWidth < $this.width()) {
        return;
      }
      var cssStyle = {
        top: $this.offset().top + $this.height() + 7,
        left: $this.offset().left + $this.width() / 2 - $hoverBox.width() / 2
      }
      $hoverBox.removeClass("rotate");
      if (cssStyle.top + $hoverBox.innerHeight() + 7 > window.document.body.clientHeight) {
        cssStyle.top = cssStyle.top - $hoverBox.innerHeight() - $this.height() - 14;
        $hoverBox.addClass("rotate");
      }
      $hoverBox.css(cssStyle).show();
    });
    $smartScheduleBox.on("mouseenter", ".remove-attention", function (e) {
      var $this = $(this);
      $this.show();
      var $tipsBox = $smartScheduleBox.find(".smart-tips-box");
      $tipsBox.text($this.data("tips"));
      $tipsBox.css({
        top: e.pageY + 20,
        left: e.pageX - $tipsBox.outerWidth() / 2,
        zIndex: 20
      });
    });
    $smartScheduleBox.on("mousemove", ".show-title,.remove-attention", function (e) {
      e.stopPropagation();
      e.preventDefault();
    });
    $smartScheduleBox.on("mousemove", function (e) {
      $smartScheduleBox.find(".smart-hover-box").text("").hide();
      $smartScheduleBox.find(".smart-tips-box").text("").css("zIndex", "-1");
    });

    // 切换显示全天日程
    $smartScheduleBox.on("click", ".show-all-more,.column-user-all-toggle i", function () {
      if (that.type == 2 || that.type == 3) {
        that.showAllFlag[that.type - 2] = !that.showAllFlag[that.type - 2];
        var maxHeight = 104;
        if (that.type == 3) {
          maxHeight = 132;
        }
        var $showList = $(this).parents(".show-list");
        var $showMore = $showList.find(".show-all-more");
        var $toggle = $showList.find(".column-user-all-toggle");
        if (that.showAllFlag[that.type - 2]) {
          maxHeight = "none";
          $showMore.hide();
          $toggle.addClass("toggle");
          $smartScheduleBox.find(".column-user-all-list").addClass("scroll");
        } else {
          $showMore.show();
          $toggle.removeClass("toggle");
          $smartScheduleBox.find(".column-user-all-list").removeClass("scroll");
        }
        $showList.find(".column-user-header").css("maxHeight", maxHeight);
        $showList.css("paddingTop", $toggle.outerHeight());
      }
    });

    // 月视角列表详情弹窗
    $smartScheduleBox.on("click", ".show-more-modal", function (e) {
      e.stopPropagation();
      var $this = $(this);
      var day = $this.data("day");
      var week = "周" + $this.data("week");
      var html = "";
      if (day) {
        var $smartModalList = $smartScheduleBox.find(".smart-modal-list-box");
        $smartModalList.css("height", "");
        $smartScheduleBox.find(".smart-modal-intr").text(week);
        $smartScheduleBox.find(".smart-modal-title").text(ctrl.dateFormat(day, "MM月dd日")).data("day", day);
        html += that.getMonthListHtml(day);
        $smartScheduleBox.find(".smart-modal-list-box").html(html).find(".time").css("width", that.timeWidth);
        var clientX = e.clientX;
        var clientY = e.clientY
        var clientWidth = window.document.body.clientWidth;
        var clientHeight = window.document.body.clientHeight;
        var $smartModal = $smartScheduleBox.find(".smart-detail-box");
        var margin = 10;
        if ($smartModal.outerWidth() + clientX + margin > clientWidth) {
          clientX = clientX - $smartModal.outerWidth() - margin;
        } else {
          clientX += margin;
        }
        if ($smartModal.outerHeight() + clientY + margin > clientHeight) {
          clientY = clientY - $smartModal.outerHeight() - margin;
        } else {
          clientY += margin;
        }
        $smartModal.css({
          top: clientY,
          left: clientX,
          transform: ""
        });
        if ($smartModalList.height() <= 256) {
          $smartModalList.height($smartModalList.height() + 2);
        }
        $smartScheduleBox.find(".smart-detail-list-modal").css({
          zIndex: 20
        });
      }
    });

    // 关闭月视角列表详情弹窗
    $smartScheduleBox.on("click", ".smart-detail-list-modal,.smart-modal-close", function () {
      $smartScheduleBox.find(".smart-detail-list-modal").css({
        zIndex: -1
      }).find(".smart-modal-list-box").scrollTop(0);
    });

    $smartScheduleBox.on("click", ".smart-detail-list-modal .smart-detail-box", function (e) {
      e.stopPropagation();
    });

    // 日视角滚动联动
    $smartScheduleBox.find(".column-user-content").on("scroll", function (e) {
      that.scrollLeft = $(this).scrollLeft();
      var borderRight = "";
      if (that.scrollLeft > 0) {
        borderRight = "1px solid #E7E7E7";
      }
      $smartScheduleBox.find(".smart-show-day .column-user-all-toggle").css("left", that.scrollLeft).css("borderRight", borderRight);
      $smartScheduleBox.find(".smart-show-day .column-user-time").css("left", that.scrollLeft).css("borderRight", borderRight);
      $smartScheduleBox.find(".smart-show-day .curr-time").css("left", that.scrollLeft);
      $smartScheduleBox.find(".smart-show-day .curr-line").css("left", 63 + that.scrollLeft);
      $smartScheduleBox.find(".smart-show-day .column-user-header").scrollLeft(that.scrollLeft);
    });

    // 智能日程培训宣传资料
    $smartScheduleBox.find(".smart-guide").on("click", function () {
      window.open($(this).data("href"));
    });

  }

  //生成搜索框
  /**
   * @param type
   */
  smartScheduleBox.prototype.creatSelectBox = function (typeData) {
    var type = typeData == "已接受" ? 1 : typeData == "已拒绝" ? 4 : 2;
    var contentHtml = "";
    contentHtml += '<div class="select-box">';
    contentHtml += ('<div class="schedule-btn select-box-tag ' + (type == 1 ? "curr" : "") + '" data-type="1" >' + (type == 1 ? "已" : "") + '接受</div>');
    contentHtml += ('<div class="schedule-btn select-box-tag ' + (type == 4 ? "curr" : "") + '" data-type="2" >' + (type == 4 ? "已" : "") + '拒绝</div>');
    contentHtml += ('<div class="schedule-btn select-box-tag ' + (type == 2 ? "curr" : "") + '" data-type="3" >' + (type == 2 ? "已" : "") + '待定</div>');
    contentHtml += '</div>';
    return contentHtml
  }
  //删除搜索框
  smartScheduleBox.prototype.removeSelectBox = function () {
    var $selectBox = $(".select-box")//所有弹窗选择（已接收/拒绝/..）
    var $scheduleBtnImg = $(".schedule-btn-i")//所有箭头小图标
    $selectBox.remove()
    $(".schedule-btn").removeClass("schedule-btn-hover")
    $scheduleBtnImg.removeClass("schedule-btn-i-top").addClass("schedule-btn-i-bottom")
  }

  // 初始化日历组件日期和渲染
  smartScheduleBox.prototype.initSmartScheduleHtml = function () {
    var that = this;
    if ($smartScheduleBox.find("#calendarAllBox").length == 0) {
      ctrl.calendar.setCurrDay().then(function (html) {
        $smartScheduleBox.find(".smart-left .smart-calendar-box").html(html);
      });
    } else if ($smartScheduleBox.find("#scheduleModal").length == 0) {
      // 没有日程弹窗的时候回到当天列表页面
      that.toggleViewType(1, that.serverDate);
    } else {
      // 其他情况刷新数据不改变日期和视角
      var selDate = new Date(that.selDate);
      ctrl.calendar.setCurrDay({
        year: selDate.getFullYear(),
        month: selDate.getMonth() + 1,
        day: selDate.getDate()
      });
    }
  }

  // 初始化头部内容渲染
  smartScheduleBox.prototype.initHeaderHtml = function () {
    var that = this;
    if (that.type == 1 || that.type == 2) {
      var className = "header-left status" + that.status;
      if (that.selDate == that.serverDate) {
        className += ' curr';
        $smartScheduleBox.find(".today").show()
      } else {
        $smartScheduleBox.find(".today").hide()
      }
      $smartScheduleBox.find(".header-left")[0].className = className;
      $smartScheduleBox.find(".header-time").text(ctrl.dateFormat(that.selDate, "yyyy年MM月dd日"));
      $smartScheduleBox.find(".header-time-label").text(that.status == 2 ? '休' : that.status == 3 ? '下午休' : '班').show();
    } else {
      if (that.type == 3) {
        $smartScheduleBox.find(".header-time").text(ctrl.dateFormat(that.selDate, "yyyy年MM月"));
      } else if (that.type == 4) {
        $smartScheduleBox.find(".header-time").text(ctrl.dateFormat(that.selDate, "yyyy年MM月"));
      }
      $smartScheduleBox.find(".header-time-label").hide();
      $smartScheduleBox.find(".today").hide()
    }
  }

  // 初始化日历当前日期信息 1当前日 2上一日/周/年 3下一日/周/年
  smartScheduleBox.prototype.initDayInfoHtml = function (type) {
    var that = this;
    var time = that.selDate;
    if (type == 1) {
      time = that.serverDate;
    }
    var selDate = new Date(time);
    var year = selDate.getFullYear();
    var month = selDate.getMonth() + 1;
    var day = selDate.getDate();
    if (that.type == 1 || that.type == 2) {
      if (type == 2) {
        selDate = selDate.getTime() - 24 * 60 * 60 * 1000;
      } else if (type == 3) {
        selDate = selDate.getTime() + 24 * 60 * 60 * 1000;
      }
      selDate = new Date(selDate);
      year = selDate.getFullYear();
      month = selDate.getMonth() + 1;
      day = selDate.getDate();
    } else if (that.type == 3) {
      if (type == 2) {
        selDate = selDate.getTime() - 7 * 24 * 60 * 60 * 1000;
      } else if (type == 3) {
        selDate = selDate.getTime() + 7 * 24 * 60 * 60 * 1000;
      }
      selDate = new Date(selDate);
      year = selDate.getFullYear();
      month = selDate.getMonth() + 1;
      day = selDate.getDate();
    } else if (that.type == 4) {
      if (type == 2) {
        month--;
      } else if (type == 3) {
        month++;
      }
      if (month <= 0) {
        year--;
        month += 12;
      } else if (month >= 13) {
        year++;
        month -= 12;
      }
    }
    ctrl.calendar.setCurrDay({
      year: year,
      month: month,
      day: day
    });
  }

  // 初始化智能日程内容
  smartScheduleBox.prototype.initSmartContentHtml = function () {
    var that = this;
    // 默认显示订阅日历
    $smartScheduleBox.find(".smart-list-sel-box").hide();
    $smartScheduleBox.find(".smart-other-sel-box").show();
    // 初始化视角内容
    if (that.type == 1 || that.type == 2) {
      if (that.type == 2) {
        // 初始化关注列表
        that.initAttentionContentHtml();
      } else {
        // 初始化日程
        that.initScheduleContentHtml({
          type: 3
        });
        // 隐藏订阅日历
        $smartScheduleBox.find(".smart-list-sel-box").show();
        $smartScheduleBox.find(".smart-other-sel-box").hide();
        if (that.selDate == that.serverDate) {
          // 获取待办
          that.initZnzlContentHtml();
          // 获取ycya
          that.initYcyaContentHtml();

          $smartScheduleBox.find(".selection-sel .list2").show();
          $smartScheduleBox.find(".selection-sel .list3").show();
          if (that.toggleList[1]) {
            $smartScheduleBox.find(".smart-show-list .upcoming-box").show();
          }
          if (that.toggleList[2]) {
            $smartScheduleBox.find(".smart-show-list .ycya-box").show();
          }
        } else {
          $smartScheduleBox.find(".selection-sel .list2").hide();
          $smartScheduleBox.find(".selection-sel .list3").hide();
          $smartScheduleBox.find(".smart-show-list .upcoming-box").hide();
          $smartScheduleBox.find(".smart-show-list .ycya-box").hide();
        }
        // 获取快速行动
        that.initQuickActingContentHtml();
      }
    } else if (that.type == 3) {
      // 初始化关注列表
      that.initAttentionContentHtml();
    } else if (that.type == 4) {
      // 初始化关注列表
      that.initAttentionContentHtml();
    }
  }

  // 初始化日程
  smartScheduleBox.prototype.initScheduleContentHtml = function (data) {
    var that = this;
    var param = {
      del: 0,//是否已删除 0-否 1-是
      page: 1,
      showType: data.type,//展示类型 1-按日 2-按人 3-列表
    };
    if (that.type == 1) {
      param.rows = 100;
    } else if (data.type == 2) {
      param.empNumbers = data.empNumbers;
    } else {
      param.empNumber = ctrl.userInfo.workerId;
    }
    if (that.type == 1 || that.type == 2) {
      param.startTimeStr = that.selDate.replace(/\//g, "-");
      param.endTimeStr = that.selDate.replace(/\//g, "-");
    } else if (that.type == 3) {
      param.startTimeStr = that.selWeekList[0].solarDate;
      param.endTimeStr = that.selWeekList[that.selWeekList.length - 1].solarDate;
    } else if (that.type == 4) {
      param.startTimeStr = that.monthData.calendarList[0].solarDate;
      param.endTimeStr = that.monthData.calendarList[that.monthData.calendarList.length - 1].solarDate;
    }
    ctrl.loading();
    ctrl.coaSdk.smartQueryList(param).then(function (result) {
      ctrl.loading().hide();
      if (result.success) {
        let res = result.data;
        if (data.type == 2) {
          that.attentionMap = {};
          that.attentionDateMap = {};
          // 查询多人日常分类
          if (res && res.list && res.list.length > 0) {
            for (var i = 0; i < res.list.length; i++) {
              var item = res.list[i];
              if (!that.attentionMap[item.empNumber]) {
                that.attentionMap[item.empNumber] = [];
              }
              if (that.type == 2 || that.type == 3 || that.type == 4) {
                var calendarList = [];
                if (that.type == 2) {
                  calendarList = [{solarDate: that.selDate}];
                } else if (that.type == 3) {
                  calendarList = that.selWeekList;
                } else if (that.type == 4 && that.monthData && that.monthData.calendarList) {
                  calendarList = that.monthData.calendarList;
                }
                // 0-当天 1-全天日程 2-跨天日程
                for (var j = 0; j < calendarList.length; j++) {
                  if (!that.attentionDateMap[calendarList[j].solarDate.replace(/-/g, "/")]) {
                    that.attentionDateMap[calendarList[j].solarDate.replace(/-/g, "/")] = [];
                  }
                }
                if (item.dayType == 1 || item.dayType == 2) {
                  for (var key in that.attentionDateMap) {
                    var itemTime = new Date(key).getTime();
                    if (item.startTime < (itemTime + 24 * 60 * 60 * 1000) && item.endTime >= itemTime) {
                      that.attentionDateMap[key].push(item);
                    }
                  }
                } else {
                  if (that.attentionDateMap[new Date(item.startTime).toLocaleDateString()]) {
                    that.attentionDateMap[new Date(item.startTime).toLocaleDateString()].push(item);
                  }
                }
              }
              that.attentionMap[item.empNumber].push(item);
            }

          }

          that.initScheduleDetailHtml();
        } else {
          $smartScheduleBox.find(".smart-show-list .list-loading").hide();
          if (res && res.list && res.list.length > 0) {
            that.toggleContent(1, 2, res.list);
          } else {
            that.toggleContent(1, 3);
          }
        }
        if ($('.list-detail-box').find('.schedule-btn-box').length == $('.list-detail-box').find('.schedule-btn-select').length) {
          //是否要缩进
          $('.list-detail-box').find('ul').each(function (index, item) {
            $(this).find("li:last-child").css("margin", "0 10px").css("width", "10%")
          })
        } else {
          $('.list-detail-box').find('ul').each(function (index, item) {
            $(this).find("li:last-child").css("margin", "0").css("width", "20%")
          })
        }
      } else {
        console.log("initScheduleContentHtml", err);
        ctrl.toast({title: res.errorMsg || '操作失败', type: 2});
        if (that.type == 2) {
          that.toggleContent(1, 3);
        }
      }
    });
  }

  // 初始化多人日程详情
  smartScheduleBox.prototype.initScheduleDetailHtml = function () {
    var that = this;
    var $currBox = "";
    if (that.type == 2) {
      $currBox = $smartScheduleBox.find(".smart-show-day");
    } else if (that.type == 3) {
      $currBox = $smartScheduleBox.find(".smart-show-week");
    } else if (that.type == 4) {
      $currBox = $smartScheduleBox.find(".smart-show-month");
    }
    var $columnAllToggle = $currBox.find(".column-user-all-toggle");
    $columnAllToggle.removeClass("show").removeClass("toggle");

    if (that.type == 2) {
      for (var i = 0; i < that.attentionList.length; i++) {
        var key = that.attentionList[i].followId;
        that.getDateListHtml(that.attentionMap[key], key);
      }
    } else if (that.type == 3) {
      for (var dateKey in that.attentionDateMap) {
        if (that.attentionDateMap[dateKey]) {
          that.getDateListHtml(that.attentionDateMap[dateKey], dateKey);
        }
      }
    } else if (that.type == 4) {
      if (that.monthData && that.monthData.calendarList) {
        var html = "";
        var currServerTime = new Date(that.serverDate).getTime();
        var isDay = new Date(that.serverDate).getDay();
        html += ' <div class="smart-calendar-month">';
        html += '   <div class="smart-month-header">';
        html += '     <div class="smart-month-list' + (isDay == 1 ? ' curr' : '') + '">周一</div>';
        html += '     <div class="smart-month-list' + (isDay == 2 ? ' curr' : '') + '">周二</div>';
        html += '     <div class="smart-month-list' + (isDay == 3 ? ' curr' : '') + '">周三</div>';
        html += '     <div class="smart-month-list' + (isDay == 4 ? ' curr' : '') + '">周四</div>';
        html += '     <div class="smart-month-list' + (isDay == 5 ? ' curr' : '') + '">周五</div>';
        html += '     <div class="smart-month-list' + (isDay == 6 ? ' curr' : '') + '">周六</div>';
        html += '     <div class="smart-month-list' + (isDay == 0 ? ' curr' : '') + '">周日</div>';
        html += '   </div>';
        html += '   <div class="smart-month-content">';
        for (var i = 0; i < that.monthData.calendarList.length; i++) {
          var item = that.monthData.calendarList[i];
          var itemTime = new Date(item.solarDate).getTime();
          var itemDay = item.solarDate.replace(/-/g, "/");
          var rest = item.status == 2 || item.status == 3;
          html += '   <div class="smart-month-detail-box' + (itemTime == currServerTime ? ' curr' : '') + '" style="height:' + (100 / (that.monthData.calendarList.length / 7)) + '%">';
          html += '     <div class="smart-month-box' + ((itemTime < currServerTime || rest) ? ' gray' : '') + (itemTime == currServerTime ? ' curr' : '') + '">';
          html += '       <div class="smart-month-day">' + item.day + '</div>';
          html += '       <div class="lundar' + (item.festival ? ' heightline' : '') + '">' + (item.festival || item.lundarDay) + '</div>';
          // if (item.status == 2 || item.status == 3) {
          //   html += '     <div class="column-week-label">' + (item.status == 2 ? '休' : item.status == 3 ? '下午休' : '班') + '</div>';
          // }
          html += '     </div>';
          html += '     <div class="column-user-all-list" data-day="' + itemDay + '" data-wid="' + that.userInfo.workerId + '" data-name="' + that.userInfo.name + '">';
          if (that.attentionDateMap[itemDay]) {
            html += that.getMonthListHtml(itemDay);
          }
          html += '     </div>';
          if (that.attentionDateMap[itemDay] && that.attentionDateMap[itemDay].length > 0) {
            var idx = 0;
            for (var j = 0; j < that.attentionDateMap[itemDay].length; j++) {
              if (!that.hideAttentionMap[that.attentionDateMap[itemDay][j].empNumber]) {
                idx++;
              }
            }
            html += '   <div class="show-more-modal" style="display:' + (idx > 3 ? 'block' : 'none') + '" data-day="' + itemDay + '" data-week="' + item.week + '">还有' + (idx - 2) + '个日程</div>';
          }
          html += '   </div>';
        }
        html += '   </div>';
        $currBox.html(html);
        if ($smartScheduleBox.find(".smart-detail-list-modal").css("zIndex") == 20) {
          $smartScheduleBox.find(".smart-modal-list-box").html(that.getMonthListHtml($smartScheduleBox.find(".smart-detail-list-modal .smart-modal-title").data("day"))).find(".time").css("width", that.timeWidth);
        }
        that.resize();
      }
    }


    $currBox.css("paddingTop", $columnAllToggle.outerHeight());
    // 按日滚动到8点
    if (that.scrollToFlag) {
      that.scrollToFlag = false;
      if (that.type == 2 || that.type == 3) {
        $currBox.find(".column-user-content").scrollTop($currBox.find('.column-box').not(".hide").find('.column-list[data-hour="8"]').position().top || $currBox.find('.default-column-box .column-list[data-hour="8"]').position().top);
      }
    }
  }

  // 初始化视角时间列表
  smartScheduleBox.prototype.initScheduleBoxHtml = function () {
    var that = this;
    var html = "";
    var headerHtml = "";
    var timeHtml = "";
    var defaultHtml = "";// 默认网格
    var num = 0;
    if (that.type == 2) {
      for (var i = 0; i < that.attentionList.length; i++) {
        var item = that.attentionList[i];
        var name = ctrl.htmlEscapeAll(item.followName || "-");
        var id = ctrl.htmlEscapeAll(item.followId || "-");

        if (!that.hideAttentionMap[id]) {
          num++;
        }

        if (i == 0) {
          headerHtml += ' <div class="column-user-all-toggle">';
          headerHtml += '   <div>全天日程</div>';
          headerHtml += '   <i></i>';
          headerHtml += ' </div>';
        }
        headerHtml += ' <div class="column-user-all ' + (that.hideAttentionMap[id] ? 'hide' : ' ') + '"  data-day="' + that.selDate + '" data-wid="' + id + '" data-name="' + name + '">';
        headerHtml += '   <div class="column-user-name">' + name + '</div>';
        headerHtml += '   <div class="column-user-all-list"></div>';
        headerHtml += ' </div>';

        html += '<div class="column-box' + (that.hideAttentionMap[id] ? ' hide' : ' ') + '" data-day="' + that.selDate + '" data-wid="' + id + '" data-name="' + name + '">';
        html += '  <div class="column-user-schedule-box"></div>';
        if (i == 0) {
          defaultHtml += '<div class="column-box default-column-box">';
          defaultHtml += '  <div class="column-user-schedule-box"></div>';
        }
        // 日视角拖拽日历
        for (var j = 0; j < 24; j++) {
          if (i == 0) {
            defaultHtml += '<div class="column-list" data-day="' + that.selDate + '" data-hour="' + j + '">';
            defaultHtml += '  <div class="column"></div>';
            defaultHtml += '</div>';
          }
          html += '<div class="column-list" data-day="' + that.selDate + '" data-hour="' + j + '">';
          if (j != 0 && j != 24 && i == 0) {
            timeHtml += '<span class="column-time" data-hour="' + j + '" style="top:' + (j * 48 - 10) + 'px">' + (j < 10 ? '0' + j : j) + ':00</span>';
          }
          html += '  <div class="column"></div>';
          html += '</div>';
        }
        if (i == 0) {
          defaultHtml += '</div>';
        }
        html += '</div>';
      }
      that.setCurrTimeLine();
      $smartScheduleBox.find(".smart-show-day .column-user-header").html(headerHtml);
      $smartScheduleBox.find(".smart-show-day .column-user-time").html(timeHtml);
      $smartScheduleBox.find(".smart-show-day .column-user-box").html(html + defaultHtml);
      if (num == 0) {
        $(".default-column-box").show();
      }

      var borderRight = "";
      if (that.scrollLeft > 0) {
        borderRight = "1px solid #E7E7E7";
      }
      $smartScheduleBox.find(".smart-show-day .column-user-all-toggle").css("left", that.scrollLeft);
      $smartScheduleBox.find(".smart-show-day .column-user-header").scrollLeft(that.scrollLeft);
      $smartScheduleBox.find(".smart-show-day .column-user-all-toggle").css("borderRight", borderRight);
      $smartScheduleBox.find(".smart-show-day .column-user-time").css("borderRight", borderRight);
    } else if (that.type == 3) {
      for (var i = 0; i < that.selWeekList.length; i++) {
        var item = that.selWeekList[i];
        var day = item.solarDate.replace(/-/g, "/");
        if (i == 0) {
          headerHtml += ' <div class="column-user-all-toggle">';
          headerHtml += '   <div>全天日程</div>';
          headerHtml += '   <i></i>';
          headerHtml += ' </div>';
        }
        headerHtml += ' <div class="column-user-all' + (new Date(day).getTime() < that.serverDateTime ? ' gray' : '') + (day == that.serverDate ? ' curr' : '') + '" data-day="' + day + '" data-wid="' + that.userInfo.workerId + '" data-name="' + that.userInfo.name + '">';
        headerHtml += '   <div class="column-week-box">';
        headerHtml += '     <div class="column-week-title-box">';
        headerHtml += '       <div class="column-week-title">周' + item.week + '</div>';
        if (item.status == 2 || item.status == 3) {
          headerHtml += '     <div class="column-week-label">' + (item.status == 2 ? '休' : item.status == 3 ? '下午休' : '班') + '</div>';
        }
        headerHtml += '     </div>';
        headerHtml += '     <div class="column-week-day">' + item.day + '</div>';
        headerHtml += '   </div>';
        headerHtml += '   <div class="column-user-all-list"></div>';
        headerHtml += ' </div>';

        html += '<div class="column-box' + (day == that.serverDate ? ' curr' : '') + '" data-day="' + item.solarDate.replace(/-/g, "/") + '" data-wid="' + that.userInfo.workerId + '" data-name="' + that.userInfo.name + '">';
        html += '  <div class="column-user-schedule-box"></div>';
        // 日视角拖拽日历
        for (var j = 0; j < 24; j++) {
          html += '<div class="column-list" data-day="' + item.solarDate.replace(/-/g, "/") + '" data-hour="' + j + '">';
          if (j != 0 && j != 24 && i == 0) {
            timeHtml += '<span class="column-time" data-hour="' + j + '" style="top:' + (j * 48 - 10) + 'px">' + (j < 10 ? '0' + j : j) + ':00</span>';
          }
          html += '  <div class="column"></div>';
          html += '</div>';
        }
        html += '</div>';
      }
      that.setCurrTimeLine();
      $smartScheduleBox.find(".smart-show-week .column-user-header").html(headerHtml);
      $smartScheduleBox.find(".smart-show-week .column-user-time").html(timeHtml);
      $smartScheduleBox.find(".smart-show-week .column-user-box").html(html);
    }
    ctrl.loading().hide();
  }

  // 初始化待办
  smartScheduleBox.prototype.initZnzlContentHtml = function () {
    var that = this;
    ctrl.coaSdk.smartZnzlList({
      currPage: 1,
      pageSize: 100,
      beginTime: that.serverDate.replace(/\//g, "-") + ' 00:00:00',
      workerId: ctrl.userInfo.workerId
    }).then(function (result) {
      if (result.success) {
        let res = result.data;
        $smartScheduleBox.find(".smart-show-list .list-loading").hide();
        if (res && res.data && res.data.list && res.data.list.length > 0) {
          that.toggleContent(2, 2, res.data.list);
        } else {
          that.toggleContent(2, 3);
        }
      } else {
        that.toggleContent(2, 3);
      }
    })
  }

  // 初始化ycya列表
  smartScheduleBox.prototype.initYcyaContentHtml = function () {
    var that = this;
    ctrl.coaSdk.smartTaskList({
      current: 1,
      size: 100,
      workerId: ctrl.userInfo.workerId
    }).then(function (result) {
      if (result.success) {
        let res = result.data;
        $smartScheduleBox.find(".smart-show-list .list-loading").hide();
        if (res && res.data && res.data.records && res.data.records.length > 0) {
          that.toggleContent(3, 2, res.data.records);
        } else {
          that.toggleContent(3, 3);
        }
      } else {
        that.toggleContent(3, 3);
      }
    });
  }
  // 初始化快速行动列表
  smartScheduleBox.prototype.initQuickActingContentHtml = function () {
    var that = this;
    ctrl.coaSdk.taskListApi({
      templateTypes:7,
      startDate:that.selDate.replace(/\//g, "-") + ' 00:00:00',
      // endDate:that.selDate.replace(/\//g, "-") + ' 23:59:59',
      quickLabels: "1,2",
      searchSource:"2",
    }).then(function (result) {
      if (result.success) {
        let res = result.data;
        $smartScheduleBox.find(".smart-show-list .quick-acting-loading").hide();
        if (res && res.length > 0) {
          that.toggleContent(4, 2, res);
        } else {
          that.toggleContent(4, 3);
        }
      } else {
        that.toggleContent(4, 3);
      }
    });
  }

  // 初始化关注列表
  smartScheduleBox.prototype.initAttentionContentHtml = function () {
    var that = this;
    var empNumberList = [that.userInfo.workerId];
    // 初始化只有自己的关注列表
    that.attentionList = [{
      followName: that.userInfo.name,
      followId: that.userInfo.workerId,
      followNo: that.userInfo.workerNo
    }];
    ctrl.coaSdk.smartAttentionList().then(function (res) {
      if (res.success) {
        let data = res.data;
        var html = ""
        if (data && data.list && data.list.length > 0) {
          var list = data.list;
          for (var i = 0; i < list.length; i++) {
            // if(!that.hideAttentionMap[list[i].followId]){
            empNumberList.push(list[i].followId);
            // }
            that.attentionList.push(list[i]);
            html += that.attentionContentHtml(i, list[i]);
          }
        }
        // 初始化视角时间列表
        that.initScheduleBoxHtml();
        // 初始化日程
        that.initScheduleContentHtml({
          type: 2,
          empNumbers: empNumberList.join(",")
        });
        $smartScheduleBox.find(".sub-schedule .selection-content-box").html(html);
        that.resize();
      } else {
        console.log("initAttentionContentHtml", res.errorMsg);
        that.initScheduleBoxHtml();
      }
    });
  };

  // 窗口大小改变
  smartScheduleBox.prototype.resize = function () {
    // 设置订阅日历高度
    var $subBox = $smartScheduleBox.find(".sub-schedule .selection-content-box");
    $subBox.height(window.document.body.clientHeight - $subBox.offset().top - 24);
    // 设置月视角-还有xx个日程高度
    if ($smartScheduleBox.find(".smart-show-month").css("display") != "none") {
      $smartScheduleBox.find(".smart-detail-box").css({
        top: "50%",
        left: "50%",
        transform: "translate(-50%,-50%)"
      })
      var $showMore = $smartScheduleBox.find(".smart-show-month").find(".show-more-modal");
      for (var i = 0; i < $showMore.length; i++) {
        var $showMoreI = $($showMore[i]);
        var $monthBox = $showMoreI.parents(".smart-month-detail-box");
        var $smartList = $monthBox.find(".smart-modal-list").not(".hide");
        var titleHeight = $monthBox.find(".smart-month-box").outerHeight();
        if ($smartList.length * 18 > $monthBox.height() - titleHeight) {
          var num = parseInt(($monthBox.height() - titleHeight) / 18) - 1;
          var height = $monthBox.height() - titleHeight - num * 18 - 4;
          $showMoreI.text("还有" + ($smartList.length - num) + "个日程").height(height < 18 ? height + 18 : height);
          if ($smartList.length - num > 0) {
            $showMoreI.show()
          } else {
            $showMoreI.hide()
          }
        } else {
          $showMoreI.hide()
        }
      }
    }
  }

  // 关注列表html
  smartScheduleBox.prototype.attentionContentHtml = function (i, item) {
    var that = this;
    var html = ""
    var name = ctrl.htmlEscapeAll(item.followName || "-");
    var id = ctrl.htmlEscapeAll(item.followId || "-");
    var no = ctrl.htmlEscapeAll(item.followNo || "-");
    html += '<li class="selection-list list' + (5 + parseInt(i)) + (!that.hideAttentionMap[id] ? ' sel' : '') + '" data-id="' + id + '" data-no="' + no + '">';
    html += ' <span>' + name + '</span>';
    html += ' <i class="remove-attention" data-tips="取消订阅"></i>';
    html += '</li>';
    return html;
  }

  // 显示隐藏事项选择内容 type-1日程,2待办,3ycya,4快速行动, status-1加载中 2加载有数据 3加载失败/空
  smartScheduleBox.prototype.toggleContent = function (type, status, list) {
    var that = this;
    var html = "";
    // 列表视角
    if (this.type == 1) {
      $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-loading').hide();
      $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-none').hide();
      $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-detail-box').hide()
      if (status == 1) {
        $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-loading').show();
      } else if (status == 2) {
        if (type == 1) {
          var showTime = "";
          var showTitle = "";
          var showRoomNames = "";
          var showName = "";
          var allStatus = true;
          for (var i = 0; i < list.length; i++) {
            html += '<ul class="schedule-ul ' + (list[i].endTime < that.serverDateTime ? 'gray' : '') + '" data-wid="' + list[i].empNumber + '" data-name="' + list[i].empName + '" data-sid="' + list[i].scheduleId + '" data-accid="' + list[i].empNo + '" data-start="' + list[i].startTime + '" data-end="' + list[i].endTime + '">';
            // 0-当天 1-全天日程 2-跨天日程
            showTime = that.timeShow(list[i]);
            html += ' <li class="show-li show-li-detail">';
            html += '   <span class="show-title" data-title="' + showTime + '">' + showTime + '</span>';
            html += ' </li>';
            showTitle = (ctrl.htmlEscapeAll(list[i].title || '').trim() || '无主题');
            html += ' <li class="show-li show-li-detail">';
            html += '   <span class="show-title" data-title="' + showTitle + '">' + showTitle + '</span>';
            html += ' </li>';
            showRoomNames = ctrl.htmlEscapeAll(list[i].roomNames || list[i].customContent || '-');
            html += ' <li class="show-li show-li-detail">';
            html += '   <span class="show-title" data-title="' + showRoomNames + '">' + showRoomNames + '</span>';
            html += ' </li>';
            showName = ctrl.htmlEscapeAll(list[i].organizerName || '-')
            html += ' <li class="show-li show-li-detail">';
            html += '   <span class="show-title" data-title="' + showName + '">' + showName + '</span>';
            html += ' </li>';
            html += ' <li class="schedule-btn-box status">';
            if (list[i].status == 3) {
              html += '   <span class="schedule-btn schedule-btn-accept new-sel") + " data-type="1">接受</span>';
              html += '   <span class="schedule-btn schedule-btn-refuse new-sel") + " data-type="2">拒绝</span>';
              html += '   <span class="schedule-btn schedule-btn-wait new-sel") + " data-type="3">待定</span>';
              allStatus = false
            } else {
              html += '   <span class="schedule-btn schedule-btn-accept" data-type="4" data-status=' + list[i].status + '>';
              switch (list[i].status) {
                case 1:
                  html += '   <span class="schedule-btn-select">已接受</span>';
                  break
                case 4:
                  html += '   <span class="schedule-btn-select">已拒绝</span>';
                  break
                case 2:
                  html += '   <span class="schedule-btn-select">已待定</span>';
                  break
              }
              html += '   <i class="schedule-btn-i schedule-btn-i-bottom"></i></span>';
            }
            html += ' </li>';
            html += '</ul>';
          }
          $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .schedule-content-box').html(html);
          if (allStatus) {
            //全部已经处理了
            $('.list-detail-box').find('ul').each(function (index, item) {
              $(this).find("li:last-child").css("margin", "0 10px").css("width", "10%")
            })
          }
        } else if (type == 2) {
          // title-提醒标题 content-提醒内容 hintMsg-提示语 remindGrade-事务级别1宽裕 2较急 3紧急
          for (var i = 0; i < list.length; i++) {
            var upcomingTitle = ctrl.htmlEscapeAll(list[i].title || "-");
            var upcomingContent = ctrl.htmlEscapeAll(list[i].content || "-");
            var upcomingHintMsg = ctrl.htmlEscapeAll(list[i].hintMsg || "");
            html += '<li class="upcoming-list sel-list-li show-title" data-title="' + upcomingTitle + '" data-content="' + upcomingContent + '" data-tips="' + upcomingHintMsg + '">';
            // html += '  <span class="upcoming-check sel-list-check"></span>';
            html += '  <span class="upcoming-title sel-list-title">' + upcomingTitle + '</span>';
            html += '</li>';
          }
          $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-detail-box').html(html);
        } else if (type == 3) {
          for (var i = 0; i < list.length; i++) {
            var ycyaTitle = ctrl.htmlEscapeAll(list[i].taskTitle || "-");
            html += '<li class="ycya-list sel-list-li" data-id="' + list[i].id + '">';
            html += '  <span class="ycya-check sel-list-check"></span>';
            html += '  <span class="ycya-title sel-list-title show-title" data-title="' + ycyaTitle + '">' + ycyaTitle + '</span>';
            html += '</li>';
          }
          $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-detail-box').html(html);
        }else if (type == 4 ){
          var allStatus = true;
          for (var i = 0; i < list.length; i++) {
            html += '<ul class="schedule-ul quick-acting noschedule ' + (list[i].taskStatus!==1 ? 'gray' : '')  + '" data-batchno="'+ list[i].batchNo + '" >';
            let taskStopTimeStr = list[i].taskStopTimeStr;
            html += ' <li class="show-li show-li-detail">';
            html += '   <span class="show-title" data-title="' + taskStopTimeStr + '">' + taskStopTimeStr + '</span>';
            html += ' </li>';
            let taskName = list[i].batchName;
            html += ' <li class="show-li show-li-detail ">';
            html += '   <span class="show-title" data-title="' + taskName + '">' + taskName + '</span>';
            html += ' </li>';
            let taskPublicName  = list[i].taskPublicName;
            html += ' <li class="show-li show-li-detail">';
            html += '   <span class="show-title" data-title="' + taskPublicName + '">' + taskPublicName + '</span>';
            html += ' </li>';
            html += ' <li class="schedule-btn-box status">';
            const prohibit = list[i].taskStatus!=1?'quick-acting-btu-distory':''
            html += '   <span class="quick-acting-btu  ' + prohibit + '" data-status="'+ list[i].taskStatus+'" data-type="1" data-batchno="'+ list[i].batchNo+'">查看进度</span>';
            html += '   <span class="quick-acting-btu ' + prohibit + '" data-status="'+ list[i].taskStatus+'"  data-type="2" data-batchno="'+ list[i].batchNo+'">数据填报</span>';
            html += ' </li>';

            allStatus = false

            html += ' </li>';
            html += '</ul>';
          }
          $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .schedule-content-box').html(html);
          if (allStatus) {
            //全部已经处理了
            $('.list-detail-box').find('ul').each(function (index, item) {
              $(this).find("li:last-child").css("margin", "0 10px").css("width", "10%")
            })
          }
        }
        $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-detail-box').show();

      } else {
        $smartScheduleBox.find('.smart-show-list .list-box' + type + ' .list-none').show();
      }
    }

  }
  // 点击查看进度
  smartScheduleBox.prototype.onLookProgress = async function (e) {
    let batchNo = e.data("batchno");
    let pcUrl = `${ctrl.config[ctrl.config.env].jjsHome}/lyj-front/mission-center/#/progress`;
    let path = await getFreeLoginUrl(`${linkFormat(pcUrl)}/${batchNo}`,'jjsHome')
    emitMsg("msg", {
      type: "window", newWin: 1, name: "webview", width: 1200, height: 800, changePath: true, frame: true,resizable:false,
      path: path,
    });
  }

  // 点击打开填报页面
  smartScheduleBox.prototype.onOpenQuickActing = async function (e) {
    let batchNo = e.data("batchno");
    const { success,errorInfo} = await quickActingSearchFilling({batchNo: batchNo,isLook:1});
    if(!success){
      toast({title: errorInfo, type: 3});
      return;
    }
    let thisUserInfo = store.getters.getUserInfo;
    let pcFillInUrl = `${ctrl.config[ctrl.config.env].jjsHome}/lyj-front/lbgapp/quickActing.html`;
    const path = await getFreeLoginUrl(`${linkFormat(pcFillInUrl)}?batchNo=${batchNo}&empNumber=${thisUserInfo.workerId}`,'jjsHome')
    emitMsg("msg", {
      type: "window", newWin: 1, name: "webview", width: 410, height: 450, minWidth: 410, minHeight: 450, changePath: true, frame: true,resizable:false,
      path: path,
    });
  }


  // 日程操作接受/拒绝/待定
  smartScheduleBox.prototype.smartUpdateStatus = function (param) {
    if (param.handleState) {
      // 不处理，只关闭下拉框
      this.removeSelectBox();
      var that = this;
      that.selectBoxShow = false;
      setTimeout(function () {
        that.selectBoxShow = true
      }, 30)
      return
    }
    var that = this;
    var id = param.id;
    var type = param.type;
    //参与状态 1-接受 2-待定 3-未回复 4-拒绝
    var status = "";
    if (type == 1 || type == 3) {
      status = 1;
      if (type == 3) {
        status = 2;
      }
      that.smartUpdateApi({
        id: id,
        status: status,
        done: function (err, data) {
          that.selectBoxShow = false;
          setTimeout(function () {
            that.selectBoxShow = true
          }, 30)
          param.done(err, data, status);
        }
      });
      // } else if(type == 4){
      //   var contentHtml = "";
      //   contentHtml += '<div class="select-box">';
      //   contentHtml += '<div class="schedule-btn select-box-tag" data-type="1" >已接受</div>';
      //   contentHtml += '<div class="schedule-btn select-box-tag" data-type="2" >已拒绝</div>';
      //   contentHtml += '<div class="schedule-btn select-box-tag" data-type="3" >已待办</div>';
      //   contentHtml += '</div>';
      //   param.done(false,'',contentHtml);
    } else if (type == 2) {
      status = 4;
      var contentHtml = "";
      var $textAreaBox = "";
      contentHtml += '<div class="refuse-box" style="width: 100%;position: relative;font-size: 12px;">';
      contentHtml += '  <div style="font-size: 14px;font-weight: 500;">是否确认拒绝该日程？</div>';
      contentHtml += '  <textarea class="refuse-textarea" placeholder="请输入拒绝原因，不超过50个字符，非必填" style="width: 100%;height:88px;border-radius: 2px;padding: 10px;border: 1px solid #CCCCCC;word-break: break-all;margin:10px 0;"></textarea>';
      contentHtml += '  <span class="refuse-box-text" style="position: absolute;bottom: 20px;right: 10px;color: #999999">0/50</span>';
      contentHtml += '</div>';
      alertObj = ctrl.alert({
        title: "拒绝原因",
        isClose: false,
        content: contentHtml,
        done: type => {
          if (type == 1) {
            var val = ctrl.htmlEscapeAll($textAreaBox.find(".refuse-textarea").val().trim());
            that.smartUpdateApi({
              id: id,
              status: status,
              rejectContent: val,
              done: function (err, data) {
                param.done(err, data, status);
                if (!err) {
                  alertObj.hide();
                }
              }
            });
          } else {
            alertObj.hide();
          }
        },
        on: function (elm) {
          $textAreaBox = $(elm);
          // 拒绝原因输入监听
          var textareaFlag = true;
          $textAreaBox.on('compositionstart', ".refuse-textarea", function () {
            textareaFlag = false;
          });
          $textAreaBox.on('compositionend', ".refuse-textarea", function () {
            textareaFlag = true;
          });
          $textAreaBox.on("input", ".refuse-textarea", function () {
            var _this = this;
            setTimeout(function () {
              if (textareaFlag) {
                var pos = 0;
                pos += _this.selectionEnd;
                var $_this = $(_this);
                var val = $_this.val();
                var changeVal = val.replace(/[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g, '');
                $_this.val(changeVal);
                pos -= val.length - changeVal.length;
                if (changeVal.length > 50) {
                  changeVal = changeVal.slice(0, 50);
                  $_this.val(changeVal);
                }
                $textAreaBox.find(".refuse-box-text").text(changeVal.length + "/50");

                // 还原光标位置
                _this.selectionStart = _this.selectionEnd = pos;
              }
            }, 0);
          });
        }
      });
    }
  }

  // 设置当前时间线
  smartScheduleBox.prototype.setCurrTimeLine = function () {
    var that = this;
    if (that.type == 1 || that.type == 4 || (that.type == 2 && that.selDate != that.serverDate) || (that.type == 3 && that.selWeekList.findIndex(function (item) {
      return item.solarDate.replace(/-/g, "/") == that.serverDate
    }) == -1)) {
      $smartScheduleBox.find(".curr-time-box").remove();
      return;
    }
    ctrl.nim.getServerTime().then(res => {
      let {err, obj} = res;
      var serverDate = new Date(obj);
      if (err) {
        console.log("getServerTimeError", err);
        serverDate = new Date();
      } else {
        that.serverDateTime = obj
      }
      var hour = serverDate.getHours();
      var minutes = serverDate.getMinutes();
      var position = hour + minutes / 60;
      var html = "";
      html += '<div class="curr-time-box" data-wid="' + that.userInfo.workerId + '" data-name="' + that.userInfo.name + '" data-day="' + serverDate.toLocaleDateString() + '" data-hour="' + hour + '" data-minutes="' + minutes + '">';
      // 当前时间
      html += ('  <span class="curr-time" style="top:' + (position * 48 - 10) + 'px;left: ' + that.scrollLeft + 'px">' + (hour < 10 ? '0' + hour : hour) + ':' + (minutes < 10 ? '0' + minutes : minutes) + '</span>');
      // 当前时间线
      html += ('  <div class="curr-line" style="top:' + position * 48 + 'px;left: ' + (that.scrollLeft + 63) + 'px"></div>');
      html += '</div>';

      $smartScheduleBox.find(".column-time").show();

      // 隐藏对应时间
      if (minutes <= 15) {
        $smartScheduleBox.find('.column-time[data-hour="' + hour + '"]').hide();
      } else if (minutes >= 45) {
        $smartScheduleBox.find('.column-time[data-hour="' + (hour + 1) + '"]').hide();
      }

      $smartScheduleBox.find(".curr-time-box").remove();
      $smartScheduleBox.find(".column-user-content").append(html);
    });
  }

  // 获取鼠标选中区域 type 1开始 2移动 3结束
  smartScheduleBox.prototype.mouseElm = function (e, type) {
    var that = this;
    var $this = $(e.currentTarget);
    var day = $this.data("day") || $this.parents(".column-user-all").data("day");
    var id = $this.data("wid") || $this.parents(".column-box").data("wid") || $this.parents(".column-user-all").data("wid");
    if (type == 1 && id != that.userInfo.workerId) {
      ctrl.toast({title: "请在本人日历上创建日程"});
      return;
    }

    if ($this.is(".column-user-all-list")) {
      that.selInfo.startDay = day;
      that.selInfo.allDay = that.selDate;
      that.selInfo.$elm = $this;
      return;
    }

    var $rightContent = "";
    // 日/周/月视角滚动区域
    if (that.type == 2) {
      $rightContent = $smartScheduleBox.find(".smart-right-content .smart-show-day .column-user-content");
    } else if (that.type == 3) {
      $rightContent = $smartScheduleBox.find(".smart-right-content .smart-show-week .column-user-content");
    } else if (that.type == 4) {
      $rightContent = $smartScheduleBox.find(".smart-right-content .smart-show-month");
    }

    // 超过拖拽范围取最大值/最小值
    var pageY = e.pageY;
    if (pageY < $rightContent.offset().top) {
      pageY = $rightContent.offset().top;
    } else if (pageY > window.document.body.clientHeight) {
      pageY = window.document.body.clientHeight;
    }

    if (type == 1) {
      that.selInfo = {
        startDay: day,
        startY: pageY,
        startScrollTop: $rightContent.scrollTop()
      }
    } else if (type == 2 || type == 3) {
      that.selInfo.endDay = day;
      that.selInfo.endY = pageY;
      that.selInfo.endScrollTop = $rightContent.scrollTop()
    }
    that.selInfo.top = $rightContent.offset().top;
  }

  // 选择时间区域
  smartScheduleBox.prototype.selTimeArea = function (e) {
    var that = this;
    var startY = that.selInfo.startY - that.selInfo.top + that.selInfo.startScrollTop;
    var endY = that.selInfo.endY - that.selInfo.top + that.selInfo.endScrollTop;

    if (startY % 24 != 0) {
      startY = startY - startY % 24;
    }
    if (endY % 12 != 0) {
      endY = endY - endY % 12;
    }

    var height = Math.abs(startY - endY);
    // 最小刻度24px
    if (height < 24) {
      height = 24;
      if (startY > endY) {
        endY = startY - 24;
      } else {
        endY = startY + 24;
      }
    }
    var top = startY <= endY ? startY : endY;

    var startHour = "";
    var startMinutes = "";
    var endHour = "";
    var endMinutes = "";
    // 计算当前坐标对应时间
    if (startY < endY) {
      startHour = parseInt(startY / 48);
      startMinutes = startY % 48 / 48 * 60;
      endHour = parseInt(endY / 48);
      endMinutes = endY % 48 / 48 * 60;
    } else {
      startHour = parseInt(endY / 48);
      startMinutes = endY % 48 / 48 * 60;
      endHour = parseInt(startY / 48);
      endMinutes = startY % 48 / 48 * 60;
    }

    var startTime = (startHour < 10 ? '0' + startHour : startHour) + ':' + (startMinutes < 10 ? '0' + startMinutes : startMinutes);
    var endTime = (endHour < 10 ? '0' + endHour : endHour) + ':' + (endMinutes < 10 ? '0' + endMinutes : endMinutes);

    if (!that.selInfo.endDay) {
      that.selInfo.endDay = that.selInfo.startDay;
    }

    if ($smartScheduleBox.find(".sel-area").length > 0) {
      var $selArea = $smartScheduleBox.find(".sel-area");
      $selArea.data({
        "start": that.selInfo.startDay + " " + startTime,
        "end": that.selInfo.endDay + " " + endTime
      }).css({
        top: top,
        height: height,
        lineHeight: height < 36 ? "22px" : "16px",
      }).html('添加日程' + (height < 36 ? '，' : '<br>') + startTime + '-' + endTime);
    } else {
      var html = '<div class="sel-area" data-start="' + (that.selInfo.startDay + " " + startTime) + '" data-end="' + (that.selInfo.endDay + " " + endTime) + '" style="top:' + top + 'px;line-height: 22px;height: ' + height + 'px">添加日程' + (height < 36 ? '，' : '<br>') + startTime + ' - ' + endTime + ' </div>'
      $smartScheduleBox.find('.column-box[data-wid="' + that.userInfo.workerId + '"][data-day="' + that.selInfo.startDay + '"]').append(html);
    }
  }

  // 获取日/周视角html
  smartScheduleBox.prototype.getDateListHtml = function (list, currKey) {
    var that = this;
    var allList = [];
    var allHtml = "";
    var blockList = [];
    var blockHtml = "";
    var $currBox = "";
    var $currSchedule = "";
    var $columnBox = "";
    if (that.type == 2) {
      $currBox = $smartScheduleBox.find(".smart-show-day");
    } else if (that.type == 3) {
      $currBox = $smartScheduleBox.find(".smart-show-week");
    } else if (that.type == 4) {
      $currBox = $smartScheduleBox.find(".smart-show-month");
    }
    var $columnAllToggle = $currBox.find(".column-user-all-toggle");

    if (list) {
      for (var i = 0; i < list.length; i++) {
        var item = list[i];
        if (item.dayType == 0) {
          if (that.type == 2) {
            blockList.push(item);
          } else {
            if (!that.hideAttentionMap[item.empNumber]) {
              blockList.push(item);
            }
          }
        } else {
          var dataInfo = that.getDataInfo(item);
          if (dataInfo.keyIndex != null || dataInfo.keyIndex != -1) {
            allHtml += '<div class="all-day-html' + (that.hideAttentionMap[item.empNumber] ? ' hide' : '') + '"' + dataInfo.dataStr + 'style="color:' + that.attentionColorList[dataInfo.keyIndex].color + ';background:' + that.attentionColorList[dataInfo.keyIndex].bg + ';opacity:' + (item.endTime < that.serverDateTime ? '0.45' : '1') + dataInfo.showStyleStr + '">' + dataInfo.showTitle + "，" + that.timeShow(item) + '</div>'
          }
        }
      }
    }

    if (that.type == 2) {
      $currSchedule = $currBox.find('.column-user-all[data-wid="' + currKey + '"] .column-user-all-list');
      $columnBox = $currBox.find('.column-box[data-wid="' + currKey + '"]');
    } else {
      $currSchedule = $currBox.find('.column-user-all[data-day="' + currKey + '"] .column-user-all-list');
      $columnBox = $currBox.find('.column-box[data-day="' + currKey + '"]');
    }

    $currSchedule.html(allHtml);
    if ($currSchedule.find(".all-day-html").not(".hide").length > 3) {
      $columnAllToggle.addClass("show");
      if ($currSchedule.not("hide")) {
        if (that.showAllFlag[that.type - 2]) {
          $columnAllToggle.addClass("toggle");
        }
        $currSchedule.append('<div class="show-all-more" style="display:' + (that.showAllFlag[that.type - 2] ? 'none' : 'block') + '">还有' + ($currSchedule.find(".all-day-html").not(".hide").length - 2) + '个日程</div>');
        if (that.showAllFlag[that.type - 2] && !$currBox.find(".column-user-all-list").is("scroll")) {
          $currBox.find(".column-user-all-list").addClass("scroll");
        }
      }
    }

    blockList.sort(function (a, b) {
      return (a.endTime - a.startTime) - (b.endTime - b.startTime)
    });

    // 日程宽度
    var commonScheduleMap = {};// 日程交集
    var intersectList = [];
    var key = 0;
    var index = 0;
    for (var i = 0; i < blockList.length; i++) {
      intersectList[key] = [blockList[i]];
      for (var j = index + 1; j < blockList.length; j++) {
        var startTime = blockList[j].startTime;
        var endTime = blockList[j].endTime;
        var startTime0 = intersectList[key][0].startTime;
        var endTime0 = intersectList[key][0].endTime;
        if ((startTime >= startTime0 && startTime < endTime0) || (startTime <= startTime0 && endTime > startTime0) || (startTime == startTime0 && endTime == endTime0)) {
          intersectList[key].push(blockList[j]);
        }
      }
      key++;
      index++;
    }

    intersectList.sort(function (a, b) {
      return b.length - a.length;
    });

    // 去除并集
    for (var i = 0; i < intersectList.length; i++) {
      for (var j = i + 1; j < intersectList.length; j++) {
        var thisList = intersectList[i].concat(intersectList[j]).filter(function (item, index, arr) {
          return arr.indexOf(item, 0) === index;
        })
        if (isContained(thisList, intersectList[i])) {
          intersectList.splice(j, 1);
          i--;
          break;
        }
      }
    }

    intersectList.sort(function (a, b) {
      return b.length - a.length;
    });

    // 当前元素的定位和宽度
    var top = 0;
    var left = 0;
    var width = 0;
    var height = 0;
    var thisWidth = 98;// 总宽度-百分百

    var styleStr = "";
    var item = "";

    // 计算宽度
    for (var i = 0; i < intersectList.length; i++) {
      thisWidth = 98;// 总宽度-百分百
      if (!intersectList[i]) {
        continue
      }

      // 去除交集
      for (var j = 0; j < intersectList[i].length; j++) {
        item = intersectList[i][j];
        if (commonScheduleMap[item.scheduleId + "-" + item.empNumber]) {
          thisWidth -= commonScheduleMap[item.scheduleId + "-" + item.empNumber].width;
          intersectList[i].splice(j, 1);
          j--;
        }
      }

      // 计算非交集的宽度
      for (var j = 0; j < intersectList[i].length; j++) {
        item = intersectList[i][j];
        if (!commonScheduleMap[item.scheduleId + "-" + item.empNumber]) {
          item.width = thisWidth / intersectList[i].length;
          commonScheduleMap[item.scheduleId + "-" + item.empNumber] = item;
        }
      }

      // 计算元素定位和宽高
      left = 0;
      for (var j = 0; j < intersectList[i].length; j++) {
        item = intersectList[i][j];
        var dataInfo = that.getDataInfo(item);
        var startDate = new Date(item.startTime);
        var parentWidth = $columnBox.width() * thisWidth / 100; //父元素的宽度
        var showTime = ctrl.dateFormat(item.startTime, "HH:mm") + ' - ' + ctrl.dateFormat(item.endTime, "HH:mm");
        top = startDate.getHours() * 48 + startDate.getMinutes() / 60 * 48;
        width = item.width;
        height = (item.endTime - item.startTime) / 24 / 60 / 60 / 1000 * 24 * 48 - 1;
        if (height <= 23) {
          height = 23;
        }
        styleStr = ' style="position: absolute;top: ' + top + 'px;left:calc(' + left + '% + 2px);width:calc(' + width + '% - 2px);height:' + height + 'px;color:' + that.attentionColorList[dataInfo.keyIndex].color + ';background:' + that.attentionColorList[dataInfo.keyIndex].bg + (width * parentWidth <= 6 ? ';padding:0' : '') + ';opacity:' + (item.endTime < that.serverDateTime ? '0.45' : '1') + dataInfo.showStyleStr + '"';
        // 小于32px的1行展示
        if (height < 32) {
          blockHtml += '<div class="block-html' + (that.hideAttentionMap[item.empNumber] ? ' hide' : ' ') + '"' + styleStr + dataInfo.dataStr + '>' + dataInfo.showTitle + '，' + showTime + '</div>';
        } else {
          blockHtml += '<div class="block-html' + (that.hideAttentionMap[item.empNumber] ? ' hide' : ' ') + '" ' + styleStr + dataInfo.dataStr + '>';
          blockHtml += '  <div style="word-break: break-all; white-space: normal;overflow: hidden;line-height: 15px;max-height: calc(100% - 18px);">' + dataInfo.showTitle + '</div>';
          blockHtml += '  <div style="word-break: break-all; white-space: nowrap;overflow: hidden;margin-top: 2px;line-height: 12px;">' + showTime + '</div>';
          blockHtml += '</div>';
        }
        left += width;
      }
    }
    $columnBox.find('.column-user-schedule-box').html(blockHtml);
  }

  function isContained(a, b) {
    if (!(a instanceof Array) || !(b instanceof Array)) return false;
    if (a.length != b.length) return false;
    for (var i = 0, len = b.length; i < len; i++) {
      if (a.indexOf(b[i]) == -1 && b.indexOf(a[i]) == -1) {
        return false;
      }
    }
    return true;
  }

  // 获取月视角html
  smartScheduleBox.prototype.getMonthListHtml = function (day) {
    var that = this;
    var html = "";
    that.timeWidth = 40;
    if (that.attentionDateMap[day]) {
      for (var i = 0; i < that.attentionDateMap[day].length; i++) {
        var item = that.attentionDateMap[day][i];
        var dataInfo = that.getDataInfo(item);
        html += '<div class="smart-modal-list all-day-html smart-modal-list' + dataInfo.keyIndex + '' + (that.hideAttentionMap[item.empNumber] ? ' hide' : '') + '" ' + dataInfo.dataStr + ' style="opacity:' + (item.endTime < that.serverDateTime ? '0.45' : '1') + '">';
        html += ' <i class="icon-style" style="' + dataInfo.iconStyleStr + '"></i>';
        html += ' <span class="time" style="' + dataInfo.showStyleStr + '">' + dataInfo.showTime + '</span>';
        html += ' <span class="text" style="' + dataInfo.showStyleStr + '">' + dataInfo.showTitle + '</span>';
        html += '</div>';
      }
    }
    return html;
  }

  // 获取视角样式和数据
  smartScheduleBox.prototype.getDataInfo = function (item) {
    var that = this;
    var keyIndex = that.attentionList.findIndex(function (thisItem) {
      return item.empNumber == thisItem.followId
    });
    if (!that.attentionColorList[keyIndex]) {
      return {};
    }
    // 0-当天 1-全天日程 2-跨天日程
    var dataStr = 'data-wid="' + item.empNumber + '" data-name="' + item.empName + '" data-sid="' + item.scheduleId + '" data-start="' + ctrl.dateFormat(item.startTime, "yyyy/MM/dd HH:mm") + '" data-end="' + ctrl.dateFormat(item.endTime, "yyyy/MM/dd HH:mm") + '" data-index="' + keyIndex + '"';
    var title = (ctrl.htmlEscapeAll(item.title || '').trim() || '无主题');
    // 自己日历-组织非参与者[不参与]，参与者非拒绝[参与]，参与者拒绝[拒绝]
    // 他人日历-自己相关（组织/参与者）显示日程主题，自己无关显示费公开日程
    var showTitle = "";
    var iconStyleStr = 'background:' + that.attentionColorList[keyIndex].color;
    var showStyleStr = "";
    var showTime = "";
    if (item.dayType == 2) {
      that.timeWidth = that.timeWidth < 75 ? 75 : that.timeWidth;
      showTime = ctrl.dateFormat(item.startTime, "MM.dd") + "-" + ctrl.dateFormat(item.endTime, "MM.dd");
    } else if (item.dayType == 1) {
      var startTime = ctrl.dateFormat(item.startTime, "MM.dd");
      var endTime = ctrl.dateFormat(item.endTime, "MM.dd");
      showTime = startTime;
      if (startTime != endTime) {
        showTime += "-" + endTime;
        that.timeWidth = that.timeWidth < 75 ? 75 : that.timeWidth;
      }
    } else {
      showTime = ctrl.dateFormat(item.startTime, "HH:mm");
    }
    (item.roomNames || item.customContent || "").trim() != "" ? title += "，" + (item.roomNames || item.customContent) : "";
    (item.videoContent || "").trim() != "" ? title += "，" + "视频会议" : ""
    if (item.empNumber == that.userInfo.workerId) {
      if (!new RegExp(that.userInfo.workerId).test(item.participateIds)) {
        showTitle = '[不参与] ' + title;
        iconStyleStr = ';border:1px solid #999999';
        if (that.type == 4) {
          showStyleStr = ';color: #999999';
        } else {
          showStyleStr = ';border: 1px solid #CCCCCC;color: #999999;background: #FFFFFF';
        }
      } else {
        if (item.status == 4) {
          showTitle = '[拒绝] ' + title;
          iconStyleStr = ';border:1px solid #999999;line-height: 18px';
          if (that.type == 4) {
            showStyleStr = ';color: #999999;text-decoration:line-through; ';
          } else {
            showStyleStr = ';border: 1px solid #CCCCCC;color: #999999;background: #FFFFFF;text-decoration:line-through; ';
          }
          if (item.dayType != 0) {
            showStyleStr += ';line-height: 18px';
          }
        } else {
          showTitle = '[参与] ' + title;
          if (that.type == 4) {
            showStyleStr = ';color: #000000;';
          }
          if (item.dayType == 0) {
            showStyleStr += ';line-height: 22px';
          }
        }
      }
    } else {
      if (!new RegExp(that.userInfo.workerId).test(item.participateIds) && !new RegExp(that.userInfo.workerId).test(item.organizerNumber)) {
        showTitle = item.empName + ' 非公开日程';
      } else {
        showTitle = item.empName + ' ' + title;
      }
    }
    return {
      keyIndex: keyIndex,
      dataStr: dataStr,
      showTime: showTime,
      showTitle: showTitle,
      showStyleStr: showStyleStr,
      iconStyleStr: iconStyleStr
    }
  }

  // 日程接受/拒绝/待定api id-日程id status-操作状态
  smartScheduleBox.prototype.smartUpdateApi = function (param) {
    ctrl.loading();
    var data = {
      id: param.id,
      status: param.status
    }
    if (param.rejectContent) {
      data.rejectContent = param.rejectContent;
    }
    if (param.addParticipateIds) {
      data.addParticipateIds = param.addParticipateIds;
    }
    if (param.join) {
      data.join = param.join;
    }
    ctrl.coaSdk.smartUpdate(data).then(function (res) {
      ctrl.loading().hide();

      if (res.success) {
        param.done("", data);
        ctrl.toast({title: res.errorMsg || '操作成功', type: 1});
      } else {
        param.done("error", res.errorMsg);
        ctrl.toast({title: res.errorMsg || '操作失败', type: 2});
      }
    });
  }

  // 时间展示场景
  smartScheduleBox.prototype.timeShow = function (item) {
    var showTime = "";
    // 0-当天 1-全天日程 2-跨天日程
    if (item.dayType == 2) {
      showTime = ctrl.dateFormat(item.startTime, "MM月dd日HH:mm") + '-' + ctrl.dateFormat(item.endTime, "MM月dd日HH:mm");
    } else if (item.dayType == 1) {
      var startTime = ctrl.dateFormat(item.startTime, "MM月dd日");
      var endTime = ctrl.dateFormat(item.endTime, "MM月dd日");
      if (startTime == endTime) {
        showTime = ctrl.dateFormat(item.startTime, "MM月dd日");
      } else {
        showTime = ctrl.dateFormat(item.startTime, "MM月dd日") + '-' + ctrl.dateFormat(item.endTime, "MM月dd日");
      }
    } else {
      showTime = ctrl.dateFormat(item.startTime, "HH:mm") + ' - ' + ctrl.dateFormat(item.endTime, "HH:mm");
    }
    return showTime;
  }

  // 切换视角
  smartScheduleBox.prototype.toggleViewType = function (type, date) {
    var that = this;
    that.type = type;

    $smartScheduleBox.find('.header-list[data-type="' + type + '"]').addClass("sel").siblings().removeClass("sel");

    var $currBox = "";
    var maxHeight = 104;
    var selDate = new Date(date);

    var $pre = $smartScheduleBox.find(".smart-right-header .icon-arrow.pre");
    var $next = $smartScheduleBox.find(".smart-right-header .icon-arrow.next");


    if (that.type == 1) {
      $pre.attr("content", "上一天");
      $next.attr("content", "下一天");
    } else if (that.type == 2) {
      $pre.attr("content", "上一天");
      $next.attr("content", "下一天");
      that.showAllFlag[that.type - 2] = false;
      $currBox = $smartScheduleBox.find(".smart-show-day");
    } else if (that.type == 3) {
      $pre.attr("content", "上一周");
      $next.attr("content", "下一周");
      maxHeight = 132;
      that.showAllFlag[that.type - 2] = false;
      $currBox = $smartScheduleBox.find(".smart-show-week");
    } else if (that.type == 4) {
      $pre.attr("content", "上个月");
      $next.attr("content", "下个月");
      $currBox = $smartScheduleBox.find(".smart-show-month");
    }

    if ($currBox) {
      $currBox.find(".column-user-header").css("maxHeight", maxHeight);
    }

    // 初始化智能日程头部日期信息
    that.initHeaderHtml();
    $smartScheduleBox.find(".show-list").hide();
    $smartScheduleBox.find(".show-list" + type).show();
    that.scrollToFlag = true;
    that.scrollLeft = 0;
    $smartScheduleBox.find(".smart-show-day .column-user-content").scrollLeft(0);
    ctrl.calendar.setCurrDay({
      year: selDate.getFullYear(),
      month: selDate.getMonth() + 1,
      day: selDate.getDate()
    });
  }

  return smartScheduleBox;
}
