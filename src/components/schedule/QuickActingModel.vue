<script setup>
// 是否显示弹窗
import {computed, nextTick, onMounted, onUnmounted, ref} from "vue";
import {useStore} from "vuex";
import {quickActingDeleteBatchByNo, quickActingGetBatchCard, quickActingSearchFilling} from "@utils/net/api";
import {dateFormat, emitMsg, getFreeLoginUrl, linkFormat, openForward, userLocalStorage} from "@utils";
import config from "/config.js"
import {alert, toast} from "@comp/ui";
import LyDialog from "@comp/ui/comps/LyDialog.vue";
import QuickActing from "@comp/quickActing/QuickActing.vue";

const store = useStore();
const storeConfig = store.getters.getConfig;
const env = config[config.env];
const thisUserInfo = store.getters.getUserInfo;
const emits = defineEmits(["del"]);
const quickActingRef = ref(null);

const showQuickActingCopy = ref(false)
const showCopyIcon = computed(()=>{
  return new Date(info.value.taskPublicTimeStr).getTime() > new Date("2025-01-02 00:00:00").getTime()
})

const xy = ref({
  x:0,
  y:0,
  width: 362,
  height: 500,
})
const show = ref(false);

const infoBase = {
  batchName:"",
  batchNo:"",
  // 模版id
  templateId:null,

  taskStopTimeStr:"",

  // 指定参与人员 1指定人员 2谁都能填
  manTypeOption: 0,
  // 进度查看
  publicViewing:1,
  // 发起人
  taskPublicName:"",
  // 创建人
  taskPublicNumber:"",
  // 发起人部门
  taskPublicDeptName:"",
  // 发起时间
  taskPublicTimeStr: "",

  // 行动说明
  actionDescription:"",

  // 任务列表
  subTaskFormList:[],

  // 停止日期
  stopDate: null,

  // 停止时间
  stopTime: null,

  // 人员列表
  selectEmpOrGroup:[],

  // 提醒列表
  remindFormList:[],
}

const info = ref(JSON.parse(JSON.stringify(infoBase)));

const domRef = ref(null);

// 打开快速行动复制弹窗
async function showQuickActingCopyBox(){
  const res = userLocalStorage({key: "quickActingFormData",value:null}, 2);
  if(res?.length) {
    info.value = JSON.parse(res);
  }else{
    info.value = JSON.parse(JSON.stringify(infoBase));
  }
  showQuickActingCopy.value = true;
}

async function showQuickActingModel(batchNo,x,y){
  const err = await initData(batchNo)
  if(err?.length){
    return toast({title: err || "数据获取失败"});
  }

  show.value = true;

  nextTick(async ()=>{

    info.value.batchNo = batchNo;
    // 触碰边界
    if(x + xy.value.width > window.innerWidth ){
      x = x - xy.value.width
    }
    if(y + domRef.value?.getBoundingClientRect().height > window.innerHeight){
      y = window.innerHeight  - domRef.value?.getBoundingClientRect().height - 30
    }
    xy.value.x = x;
    xy.value.y = y;
  })


}

async function initData(batchNo){
  const {success,data,errorInfo} = await quickActingGetBatchCard({batchNo:batchNo})
  if(success){
    let manTypeOption = 0;
    let selectEmpOrGroup = [];
    let remindFormList = [];
    let publicViewing = 1;
    let screenMustBeUploaded = true;
    let remarkMustBeUploaded = true;

    if(data.otherJson){
      const parse = JSON.parse(data.otherJson);
      if( Object.keys(parse).length > 0){
        manTypeOption = parse.manTypeOption || 0;
        selectEmpOrGroup = (parse.listFrom || []).map(v=>{
          return {
            type:"empList",
            data:{
              empName:v.taskExecutorName,
              deptName:v.taskExecutorDeptName,
              empNumber:v.taskExecutorNumber,
            }
          }
        })
        remindFormList = parse.remindFormList || [];
        publicViewing = parse.publicViewing || 1
        screenMustBeUploaded = parse.screenMustBeUploaded
        remarkMustBeUploaded = parse.remarkMustBeUploaded
      }

    }

    info.value = {
      ...data,
      subTaskFormList: data.subTaskList,
      actionDescription: data.taskDesc,
      manTypeOption,
      selectEmpOrGroup,
      remindFormList,
      publicViewing,
      screenMustBeUploaded,
      remarkMustBeUploaded,
    }
    return "";
  }
  return errorInfo;
}
// 复制
async function onClickCopy(){
  info.value.stopDate = info.value.taskStopTimeStr.split(" ")[0];
  info.value.stopTime = info.value.taskStopTimeStr.split(" ")[1];
  info.value.templateId  = info.value.templateObj.id;

  showQuickActingCopy.value = true;
}
// 打开查进度
async function onClickLookProgress(){
  const url = await getFreeLoginUrl(`${env.jjsHome}/lyj-front/mission-center/#/progress/${info.value.batchNo}`,'jjsHome')
  emitMsg("msg", {
    type: "window", newWin: 1, name: "lookProgress", width: 1200, height: 800, changePath: true, frame: true,resizable:false,
    key:"imWebview",
    path: url,
  });
}

// 打开数据填报
async function onClickFill(){
  const { success,errorInfo} = await quickActingSearchFilling({batchNo: info.value.batchNo,isLook:1});
  if(!success){
    toast({title: errorInfo, type: 3});
    return;
  }

  const url = await getFreeLoginUrl(`${env.jjsHome}/lyj-front/lbgapp/quickActing.html?batchNo=${info.value.batchNo}&empNumber=${thisUserInfo.workerId}`,'jjsHome')
  emitMsg("msg", {
    type: "window", newWin: 1, name: "openQuickActing", width: 410, height: 450, minWidth: 410, minHeight: 450, changePath: true, frame: true,resizable:false,
    key:"imWebview",
    path: url,
  });
}

// 打开分享
function onClickShare(){
  /**
   * 这里的卡片需要跟另一个地方保持一致
   * @see /src/components/quickActing/QuickActing.vue#sendToForwsendToForwardard
   */
  const date = new Date(`${info.value.taskStopTimeStr}`);
  const msg = {
    type:'custom',
    content:{
      type: "quickActing",   // 固定消息体类型
      data:{
        "id": info.value.batchNo,                // 批次号
        "bodyHeight": 108,      // 内容体高度
        "titleHeight": 23,      // 标题高度

        "title": info.value.batchName,  // 标题
        "stopTime": dateFormat(date,"yyyy-MM-dd HH:mm"),    // 截止时间文本的形式
        "stopTimeStamp" : date.getTime(),  // 截止时间戳
        "actionDescription": info.value.actionDescription,
        "taskFrom": info.value.subTaskFormList.map(v=>{
          return {
            taskName:v.taskName,
            taskType: v.taskType,
            taskNumTarget:  v.taskNumTarget || "",
          }
        }),
        "pcFillInUrl":`${env.jjsHome}/lyj-front/lbgapp/quickActing.html`,
        "appFillInUrl":"/addQuickActingTaskApp.html",
        "pcProgressUrl":`${env.jjsHome}/lyj-front/mission-center/#/progress`,
        "appProgressUrl":"/progressQuickActingApp.html",
      }
    }
  }
  openForward(msg)
}

// 删除行动
function onClickDel(){
  alert({
    content: `你确定进行删除？`,
    done:async (type) => {
      if(type===1){
        const { success } = await quickActingDeleteBatchByNo({batchNo: info.value.batchNo})
        if(success){
          toast({title: "删除成功"});
          show.value = false;
          emits("del")
        }else{
          toast({title: "删除失败"});
        }
      }

    }
  });
}

// 创建快速行动
async function quickActingConfirm(){
  const status = await quickActingRef.value?.submit(1)
  if(status) {
    showQuickActingCopy.value = false;
    userLocalStorage({key: "quickActingFormData",value:null}, 1);
  }
}

// 关闭快速行动
function closeQuickActingDialog(){
  userLocalStorage({key: "quickActingFormData",value:JSON.stringify({...info.value})}, 1);
  showQuickActingCopy.value = false;
}

const closeDropdown = (event) => {
  if (domRef.value && !domRef.value.contains(event.target)) {
    show.value = false;
  }
};
onMounted(() => {
  document.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
})


defineExpose({
  showQuickActingModel,
  showQuickActingCopyBox,
})

</script>

<template>
  <teleport  v-if="show" to="body" >
    <div class="card"  :style="{top:xy.y+'px',left:xy.x+'px',maxHeight:xy.height+'px',width:xy.width+'px'}" ref="domRef">
      <div class="header">
        <div>
          <div class="tools">
            <i v-if="showCopyIcon" class="icon copy-icon" data-text="复制" @click="onClickCopy"></i>
            <i class="icon share-icon" data-text="分享行动" @click="onClickShare"></i>
            <i class="icon del-icon" v-if="thisUserInfo.workerId === info.taskPublicNumber" data-text="删除行动" @click="onClickDel"></i>
            <i class="icon close-icon" data-text="关闭" @click="show=false"></i>
          </div>
          <div class="info">
            <div class="title">{{ info.batchName }}</div>
            <div class="info-item">截止时间: {{ info.taskStopTimeStr }}</div>
            <div class="info-item">发起人员: {{info.taskPublicName}} {{info.taskPublicDeptName?'('+info.taskPublicDeptName+')':''}}</div>
          </div>
        </div>
      </div>
      <div class="flex-h">
        <div class="body">
          <div class="item">
            <span class="label">行动说明：</span>
            <div class="value">{{ info.actionDescription || "暂无行动说明"}}</div>
          </div>
          <div class="item">
            <span class="label">本次任务：</span>
            <div class="value">
              <div v-for="(subTask,index) in info.subTaskFormList">
                <div>
                  {{index+1}}. {{subTask.taskName}}
                </div>
              </div>
            </div>
          </div>
          <div class="item">
            <span class="label">参与人员：</span>
            <div class="value">
              {{ info.participantNum }}人
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="btu f-c-c" @click="onClickLookProgress">查看进度</div>
          <div class="btu f-c-c" @click="onClickFill">数据填报</div>
        </div>
      </div>
    </div>
  </teleport>
  <!--快速行动弹窗-->
  <LyDialog newClass="quick-acting-box"
            title="创建快速行动"
            :width="570"
            :height="460"
            cancelText="关闭"
            okText="完成创建"
            :overflowHidden="false"
            :closeOnClickModal="true"
            :visible="showQuickActingCopy"
            @close="closeQuickActingDialog"
            @confirm="quickActingConfirm">
    <QuickActing :data="info" :oldData="info" :nonSwarm="true" ref="quickActingRef"></QuickActing>
  </LyDialog>
</template>

<style scoped lang="scss">
.quick-acting-box{
  :deep(.content){
    max-height: 500px;
    overflow-y: auto;
    padding: 10px 16px !important;
  }
}

.card{
  //padding: 12px 16px;
  position: absolute;
  background-color: #fff;
  box-shadow: 0px 2px 20px 0px rgba(165,165,165,0.5);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 12;

  .header{
    background-image: url(/img/schedule/bg_color_0.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;

    .tools{
      padding: 12px 12px 0;
      display: flex;
      justify-content: flex-end;

      .icon{
        display: inline-block;
        width: 20px;
        height: 20px;
        background-repeat: no-repeat;
        cursor: pointer;
      }
      .icon + .icon{
        margin-left: 6px;
      }
      .copy-icon{
        background-size: 16px 16px;
        background-image: url(/img/schedule/copy-icon.png);
      }
      .share-icon{
        background-size: 16px 16px;
        background-image: url(/img/schedule/det_share.png);

      }
      .del-icon{
        background-size: 16px 16px;
        background-image: url(/img/schedule/quick-acting-del-icon.png);
      }
      .close-icon{
        background-size: 12px 12px;
        background-image: url(/img/schedule/det_close.png);
        background-position: 3px 2px;
      }
    }

    .info{
      padding: 10px 16px 10px;
      .title{
        font-weight: 600;
        font-size: 20px;
        color: #FFFFFF;
      }
      .info-item{
        margin: 4px 0;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
      }

    }

  }

  .flex-h{
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .body{
      height: 100%;
      padding: 16px;
      overflow-y: scroll;

      .item + .item{
        margin-top: 16px;
      }
      .item{
        display: flex;

        .label{
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          width: fit-content;
        }
        .value{
          flex: 1;
          font-weight: 400;
          font-size: 12px;
          color: #000000;
          line-height: 16px;
        }
      }

    }

    .footer{
      padding: 16px;
      background: #FFFFFF;
      box-shadow: 0px 2px 10px 0px rgba(165,165,165,0.5);
      display: flex;
      justify-content: space-around;

      .btu{
        width: 150px;
        height: 38px;
        border-radius: 4px;
        border: 1px solid #E03236;
        cursor: pointer;
        color: $styleColor;
        font-weight: 400;
        font-size: 12px;
      }
      .f-c-c{
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

  }


}
</style>
