<template>
  <div class="update win-drag" v-show="updateObj.show">
    <div class="update-box">
      <div class="update-header">
        <span>乐聊升级</span>
        <div class="operate-nav">
          <i class="min" @click="hideUpdate()"></i>
          <i class="close" @click="stopUpdate()"></i>
        </div>
      </div>
      <div class="update-content">
        <div class="update-done" v-if="updateObj.req.state=='error'">
          {{
            getDownloadTimes() > 6 ? "更新失败，请前往下载中心重新安装" :
              updateObj.req.code == 'ENOSPC' ? updateObj.req.msg :
                updateObj.req.code == 'ERR_STREAM_DESTROYED' ? "数据异常：请重启乐聊后更新" :
                  updateObj.req.code == 'NOT FOUND' ? "数据异常：已将错误问题提交至相关人员，稍后重试哦~~" : updateObj.req.msg
          }}
        </div>
        <div class="update-progress" v-else-if="updateObj.req.state=='process'">
          <progress max="100" :value="updateObj.req.percentage"></progress>
          <span>{{ updateObj.req.percentage }}%</span>
        </div>
        <div class="update-done" v-else-if="updateObj.req.state=='success'">更新已完成，使用新版本需要重新登录</div>
      </div>
      <div class="update-footer">
        <!--更新失败-->
        <div class="update-btn-box" v-if="updateObj.req.state=='error'">
          <div class="update-btn-box" v-if="getDownloadTimes()>6">
            <div class="update-btn" @click="stopUpdate()">取消</div>
            <div class="update-btn btn-confirm" @click="downloadApp()">前往</div>
          </div>
          <div class="update-btn-box" v-else>
            <div class="update-btn btn-confirm" @click="stopUpdate()" v-if="updateObj.req.code=='ENOSPC'">确定</div>
            <div class="update-btn" @click="reluanchApp()" v-else-if="updateObj.req.code=='ERR_STREAM_DESTROYED'">重启乐聊</div>
            <div class="update-btn-box" v-else>
              <div class="update-btn" @click="stopUpdate()">取消</div>
              <div class="update-btn btn-confirm" @click="updateMethods()">重新下载</div>
            </div>
          </div>
        </div>
        <div class="update-btn" @click="stopUpdate()" v-else-if="updateObj.req.state=='process'">取消更新</div>
        <div class="update-btn-box" v-else-if="updateObj.req.state=='success'">
          <div class="update-btn" @click="stopUpdate()">以后再说</div>
          <div class="update-btn btn-confirm" @click="startUpdate()">立即体验</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {dealMem, htmlEscapeAll, strToHtml, compareVersion, downloadFile, getAppPath, admZipFile, getFileCachedPath, dateFormat, reluanchApp, openLocalFile, setLocalUpdateObj} from "@utils";
import {sendFailUpdateApi} from "@utils/net/api.js";
import {alert, toast} from "@comp/ui";
import {ref, watch} from 'vue'

const fs = remote.require("fs");

export default {
  props: {
    type: {
      type: String,
    },
  },
  name: "Update",
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();

    // 版本号
    const config = store.getters.getConfig;
    // 当前用户
    const userInfo = store.getters.getUserInfo;
    // 电脑信息
    let computerInfo = ref(store.getters.getComputerInfo);
    let resObj = {};// 接口更新对象
    let isTogglePath = false;// 是否切换线路
    let reloadFlag = false;// 防止多次错误回调
    let updateObj = ref({
      show: false,// 是否显示下载框
      req: {},// 下载进度
      checkFlag: false,// 是否手动检查更新
    });// 升级对象
    // vip临时版本号
    let vipInfoPath = getAppPath(`\\temp.json`);

    watch(() => store.state.computerInfo,
      (newValue, oldValue) => {
        computerInfo.value = newValue;
        if (props.type != 3) {
          getUpdate();
        }
      }, {
        deep: true
      }
    );
    if (computerInfo.value.macAddress && props.type != 3) {
      getUpdate();
    }

    // 获取服务端最新版本号 flag-是否主动显示升级提示
    async function getUpdate(flag) {
      updateObj.value.checkFlag = flag;
      let res = await remote.store.dispatch("getUpdate", {...props});
      if (res.data) {
        res = res.data;
        resObj = res;
        if (res.env == config.env && res.version && (props.type != 2 || (res.vipNo || "").indexOf(userInfo.workerNo) > -1)) {
          if (compareVersion(config.version, res.version)) {
            if (!res.urlList[0] || !res.urlList[0].fullUrl) {
              // 不存在七牛下载地址不执行
              return;
            }
            if (res.incVersion && compareVersion(res.incVersion, config.version) || res.incVersion == config.version) {
              // 差量更新
              resObj.downloadSize = res.urlList[0].incrementFileSize;
              resObj.downloadUrl = res.urlList[0].incrementUrl;
              // 第二条线路
              resObj.downloadNextUrl = res.urlList[1] ? res.urlList[1].incrementUrl : resObj.downloadUrl;
            } else {
              // 全量更新
              resObj.downloadSize = res.urlList[0].fileSize;
              resObj.downloadUrl = res.urlList[0].fullUrl;
              // 第二条线路
              resObj.downloadNextUrl = res.urlList[1] ? res.urlList[1].fullUrl : resObj.downloadUrl;
            }
            let resExt = compareVersion(res.incVersion, config.version) ? ".zip" : ".exe";
            // 存在相同的下载文件不停止下载和写入
            if (props.type == 2 && (!window.global.updateReq || window.global.updateReq.name != resObj.version + resExt)) {
              // vip更新记录本地
              stopUpdate();
              fs.writeFileSync(vipInfoPath, JSON.stringify({currVersion: config.version, version: resObj.version, incVersion: resObj.incVersion, size: resObj.downloadSize, ext: resExt}, null, 2));
            }
            let alertParam = {
              contentStyle: "box-shadow: inset 0px -4px 4px 0px rgb(0 0 0 / 5%)"
            }
            let fileSize = res.fileSize ? dealMem(res.fileSize) : "";
            alertParam.content = `<div style="width: 100%;height: 120px;text-align: left;"><div style="color: #999;margin-bottom: 5px">更新内容:${fileSize}</div>${strToHtml(htmlEscapeAll(res.content.replace("\\n", "\n")))}</div>`;
            // 强更
            if (res.enforce == 2) {
              alertParam.showCancel = false;
              alertParam.showClose = false;
            }
            // 静默更新
            if (res.enforce == 3) {
              updateMethods();
              return;
            }
            //有更新包
            if (getIsAutoUpdate() && !flag) {
              // 自动下载更新
              if (getDownloadTimes() < 6) {
                updateMethods();
              } else if (getDownloadTimes() >= 6) {
                // 自动更新第6次手动提示下载
                alert({
                  ...alertParam,
                  done: (type) => {
                    if (type == 1) {
                      // 下载更新
                      updateMethods();
                    }
                  }
                });
              }
            } else {
              alert({
                ...alertParam,
                done: (type) => {
                  if (type == 1) {
                    // 下载更新
                    updateMethods();
                  }
                }
              });
            }
          } else {
            showToast(res);
          }
        } else {
          showToast(res);
        }
      } else {
        showToast(res);
      }
    }

    // 提示错误
    function showToast(res) {
      if (props.type == 3) {
        toast({title: res.errorMsg || "当前已经是最新版本"});
      }
    }

    // 下载更新
    function updateMethods() {
      // 失败次数大于6直接提示到网页下载
      if (getDownloadTimes() > 6) {
        updateObj.value.req = {state: "error"};
        updateObj.value.show = true;
        return;
      }
      try {
        // 全局更新进度
        if (window.global.updateReq) {
          // 超过30分钟没下载完也没触发超时则中断原下载
          if (window.global.updateReq.time - Date.now() > 30 * 60 * 60 * 1000) {
            stopUpdate();
          } else {
            window.global.updateReq.hideFlag = false;
            window.global.updateReq.globalDone = res => {
              changeUpdateStatus(res);
            };
            changeUpdateStatus(window.global.updateReq);
            return;
          }
        }
      } catch (e) {}
      reloadFlag = false;
      let downloadUrl = isTogglePath ? resObj.downloadNextUrl : resObj.downloadUrl;
      let downloadSize = resObj.downloadSize;
      let ext = resObj.downloadUrl.slice(resObj.downloadUrl.lastIndexOf("."));
      let downloadPath = getFileCachedPath({type: 5});
      let downloadName = resObj.version + ext;
      // vip包下载成功
      if (fs.existsSync(vipInfoPath)) {
        try {
          let vipInfo = fs.readFileSync(vipInfoPath);
          if (vipInfo.currVersion == config.version) {
            let vipFilePath = downloadPath + vipInfo.version + vipInfo.ext;
            if (fs.existsSync(vipFilePath)) {
              if (fs.statSync(vipFilePath).size == vipInfo.size) {
                // 下载完成
                updateObj.value.req = {state: "success", fileUrl: vipFilePath, ext: ext};
                updateObj.value.show = downloadShow();
                return;
              } else {
                // 删除原安装包
                fs.unlinkSync(vipFilePath);
                fs.unlinkSync(vipInfoPath);
              }
            }
          } else {
            // 当前版本不一致删除临时文件
            fs.unlinkSync(vipInfoPath);
          }
        } catch (e) {
          fs.unlinkSync(vipInfoPath);
        }
      }
      // 普通升级包下载成功
      let currFilePath = downloadPath + downloadName;
      if (fs.existsSync(currFilePath)) {
        try {
          if (fs.statSync(currFilePath).size == resObj.downloadSize) {
            // 下载完成
            writeTempFile(downloadPath, downloadName, ext, resObj.version);
            updateObj.value.req = {state: "success", fileUrl: downloadPath + downloadName, ext: ext};
            updateObj.value.show = downloadShow();
            return;
          } else {
            // 删除原安装包
            fs.unlinkSync(currFilePath);
          }
        } catch (e) {}
      }
      // 下载更新包
      downloadFile({
        url: downloadUrl,
        size: downloadSize,
        name: downloadName,
        path: downloadPath,
        ext: ext,
        done: (res) => {
          if (res.state != "process") {
            console.log("updateMethods", res);
          }
          // 下载过程
          if (res.state != "close") {
            changeUpdateStatus(res);
            if (window.global.updateReq.globalDone) {
              window.global.updateReq.globalDone(res);
            }
          }
          // 下载完成
          if (res.state == "success") {
            writeTempFile(downloadPath, downloadName, ext, resObj.version);
            return;
          } else if (res.state == "close") {
            window.global.updateReq = "";
          } else if (res.state == "error") {
            window.global.updateReq = "";
            if (reloadFlag) {
              return;
            }
            reloadFlag = true;
            // 切换线路-空间不足和无法读写不处理
            if (!isTogglePath && downloadErrStatus(res)) {
              isTogglePath = true;
              // 切换线路改为process显示进度
              updateObj.value.req.state = "process";
              // 10s后切换线路下载
              setTimeout(() => {
                updateMethods();
              }, 10 * 1000);
            } else {
              isTogglePath = false;
              let downloadTimes = getDownloadTimes() + 1;
              // 自动更新失败6次内自动下载
              if (downloadTimes < 6 && getIsAutoUpdate() && downloadErrStatus(res)) {
                // 自动下载10分钟后重新下载
                setTimeout(() => {
                  updateMethods();
                }, 10 * 60 * 1000);
              }
              // 记录下载失败次数
              setLocalUpdateObj({times: downloadTimes});
              if ((downloadTimes == 6 || res.code == "NOT FOUND") && downloadErrStatus(res)) {
                // 请求失败、自动更新第6次推送更新失败通知
                let currUserInfo = store.getters.getUserInfo;
                let thisUserInfo = currUserInfo && currUserInfo.deptName ? `${currUserInfo.name}(${currUserInfo.deptName})，` : userInfo && userInfo.deptName ? `${userInfo.name}(${userInfo.deptName})，` : "";
                sendFailUpdateApi({content: `${thisUserInfo}${computerInfo.value.macAddress}，${dateFormat(new Date(), "yyyy-MM-dd")}请求下载乐聊安装包失败，失败接口：${downloadUrl}`});
                // 上传日志
                if (downloadTimes == 6 && currUserInfo.workerNo) {
                  store.dispatch("uploadLogFile", {type: "1", no: currUserInfo.workerNo, date: dateFormat(new Date(), "yyyy-MM-dd")});
                  store.dispatch("uploadLogFile", {type: "1", no: "", date: dateFormat(new Date(), "yyyy-MM-dd")});
                }
              }
            }
          }
        }
      });
    }

    // 空间不足和无法读写不处理
    function downloadErrStatus(res) {
      return res.code != "ENOSPC" && res.code != "ERR_STREAM_DESTROYED"
    }

    // 手动/自动下载大于6次才显示弹窗
    function downloadShow() {
      return !getIsAutoUpdate() || getDownloadTimes() > 6;
    }

    // 显示更新状态
    function changeUpdateStatus(res) {
      if (window.global.updateReq && window.global.updateReq.hideFlag) {
        res.hideFlag = true;
      } else {
        updateObj.value.show = downloadShow();
      }
      window.global.updateReq = res;
      updateObj.value.req = {
        msg: res.msg,
        percentage: res.percentage,
        state: res.state,
        code: res.code,
        ext: "." + res.ext,
        fileUrl: res.path + res.name,
        time: Date.now()
      };
    }

    // 隐藏更新
    function hideUpdate() {
      if (window.global.updateReq) {
        window.global.updateReq.hideFlag = true;
      }
      updateObj.value.show = false;
    }

    // 停止更新
    function stopUpdate() {
      if (window.global.updateReq) {
        window.global.updateReq.clientRequest.destroy();
      }
      window.global.updateReq = "";
      updateObj.value.show = false;
    }

    // 获取手动/自动更新
    function getIsAutoUpdate() {
      let isAutoUpdate = localStorage.getItem("isAutoUpdate") == "false" ? false : true;
      return isAutoUpdate && !updateObj.value.checkFlag;
    }

    // 获取下载失败次数
    function getDownloadTimes() {
      return Number(setLocalUpdateObj().times || 0);
    }

    // 跳转下载应用
    function downloadApp() {
      store.dispatch("setOpenWindow", ["https://front.leyoujia.com/leyoujiaIm/%E6%96%B0sdk%E4%B9%90%E8%81%8A%E5%AE%89%E8%A3%85%E5%8C%85.exe"]);
    }

    // 更新当前用户信息
    function updateUserInfo(info) {
      userInfo.value = info;
    }

    // 开始更新
    function startUpdate() {
      if (process.env.NODE_ENV != "development") {
        remote.store.getters.getCurrentWindow().setAlwaysOnTop(false);
        if (updateObj.value.req.ext == ".zip") {
          admZipFile(updateObj.value.req.fileUrl, getAppPath());
          reluanchApp();
        } else {
          window.global.autoInstall(updateObj.value.req.fileUrl);
        }
      }
    }

    // 写入临时文件
    function writeTempFile(downloadPath, downloadName, ext, version) {
      let tempJson = {};
      if (fs.existsSync(vipInfoPath)) {
        tempJson = JSON.parse(fs.readFileSync(vipInfoPath));
      }
      tempJson.fileUrl = downloadPath + downloadName;
      tempJson.fileVersion = version;
      tempJson.fileExt = ext;
      fs.writeFileSync(vipInfoPath, JSON.stringify(tempJson, null, 2));
    }

    return {
      updateObj,

      updateMethods,
      hideUpdate,
      stopUpdate,
      getUpdate,
      getDownloadTimes,
      downloadApp,
      updateUserInfo,
      reluanchApp,
      startUpdate,
    }
  }
}
</script>
<style scoped lang="scss">
.update {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 101;

  .update-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 340px;
    background: #FFFFFF;
    box-shadow: 0px 6px 12px 0px rgba(155, 155, 155, 0.5);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #E7E7E7;

    .update-header {
      line-height: 30px;
      background: #F0F0F0;
      padding: 0 16px;
      font-size: 12px;
      font-weight: bold;

      .operate-nav {
        position: absolute;
        top: 0;
        right: 6px;
        display: flex;

        i {
          width: 30px;
          height: 30px;
          cursor: pointer;
          background-image: url(/img/icon_operate.png);
          background-repeat: no-repeat;
          background-size: 240px 30px;
        }

        .min {
          &:hover {
            background-position: -30px 0;
          }
        }

        .close {
          background-position: -180px 0;

          &:hover {
            background-position: -210px 0;
          }
        }
      }
    }

    .update-content {
      padding: 20px 16px;
      font-size: 14px;

      .update-progress {
        display: flex;
        align-items: center;

        progress {
          flex: 1;
          margin-right: 12px;
        }
      }

      .update-done {
        text-align: center;
      }
    }

    .update-footer {
      display: flex;
      justify-content: center;
      padding-bottom: 16px;

      .update-btn-box {
        display: flex;
        justify-content: center;
      }

      .update-btn {
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid #E0E0E0;
        cursor: pointer;

        &.btn-confirm {
          background: $styleColor;
          color: #FFFFFF;
          border: 1px solid $styleColor;
        }

        &:not(:first-child) {
          margin-left: 16px;
        }
      }
    }
  }
}
</style>