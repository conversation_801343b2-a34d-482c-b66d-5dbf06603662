<template>
  <div class="clear win-drag" v-show="clearObj.show">
    <div class="clear-box">
      <div class="clear-header">
        <span>清理提示</span>
        <div class="operate-nav">
          <i class="min" @click="hideClear()"></i>
          <i class="close" @click="hideClear(1)"></i>
        </div>
      </div>
      <div class="clear-content">
        <div class="clear-done" v-show="clearObj.type==1||clearObj.type==3">{{ clearObj.type == 1 ? "正在计算缓存数量(" + clearObj.searchTotal + ")..." : clearObj.type == 3 ? "已完成清理" : "" }}</div>
        <div class="clear-progress" v-show="clearObj.type==2">
          <span>进度:</span>
          <progress max="100" :value="clearObj.per"></progress>
          <span>{{ clearObj.per }}%</span>
        </div>
      </div>
      <div class="clear-footer">
        <div class="clear-btn-box">
          <div class="clear-btn" @click="hideClear(1)">{{ clearObj.type == 3 ? "关闭" : "取消清理" }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {ref, watch} from "vue";
import {getFileCachedPath} from "@utils";

const fs = remote.require("fs");
const path = remote.require("path");

export default {
  props: {
    type: {
      type: String,
    },
  },
  name: "Clear",
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();

    // 当前用户
    let userInfo = store.getters.getUserInfo;
    let clearObj = ref({
      show: false,// 是否显示下载框
      type: 0,// 0停止清理 1计算文件数量 2执行清理 3清理完成 4自动清理
      max: 500,// 每秒钟清理文件数
      maxMap: 1000,// 每次最多清理1000个文件对象
      num: 0,// 已经清理数
      total: 1,// 清理总数
      per: 0,// 清理进度
      fileMap: {},// 文件列表
      folderMap: {},// 文件夹列表
      autoMap: {},// 自动清理文件列表
      p: {},// 任务列表
      moreAuto: false,// 是否还有更多自动清理文件
      searchTotal: 0,// 查询缓存数量
    });

    watch(() => store.state.userInfo,
      (newValue, oldValue) => {
        userInfo = store.getters.getUserInfo;
        if (userInfo?.workerNo) {
          auto();
        }
      }, {
        deep: true
      }
    );

    // 全局定时器
    watch(() => store.state.emit.globalTimer,
      (newValue, oldValue) => {
        if (clearObj.value.type == 2) {
          // 每次最多删除max个文件
          if (Object.keys(clearObj.value.p).length >= clearObj.value.max) {
            return;
          }
          // 更新删除文件进度
          clearObj.value.per = (Math.round(clearObj.value.num / clearObj.value.total * 10000) / 100).toFixed(2);
          if (Object.keys(clearObj.value.fileMap).length > 0) {
            for (let key in clearObj.value.fileMap) {
              if (Object.keys(clearObj.value.p).length >= clearObj.value.max) {
                break;
              }
              if (!clearObj.value.p[key]) {
                clearObj.value.p[key] = () => {
                  new Promise(resolve => {
                    fs.unlink(clearObj.value.fileMap[key], function () {
                      delete clearObj.value.fileMap[key];
                      delete clearObj.value.p[key];
                      clearObj.value.num++;
                      resolve();
                    });
                  });
                }
                clearObj.value.p[key]();
              }
            }
            return;
          }
          // 文件删除完成后每次最多删除5个文件夹
          let folderList = Object.keys(clearObj.value.folderMap).reverse();
          if (folderList.length > 0) {
            for (let i = 0; i < folderList.length; i++) {
              let key = folderList[i];
              if (Object.keys(clearObj.value.p).length >= clearObj.value.max) {
                break;
              }
              if (!clearObj.value.p[key]) {
                clearObj.value.p[key] = () => {
                  new Promise(resolve => {
                    fs.rmdir(clearObj.value.folderMap[key], function () {
                      delete clearObj.value.folderMap[key];
                      delete clearObj.value.p[key];
                      clearObj.value.num++;
                      resolve();
                    });
                  })
                }
                clearObj.value.p[key]();
              }
            }
            return;
          }
          // 删除完成
          clearObj.value.type = 3;
          remote.store.commit("setRemoveDB", {workerNo: userInfo.workerNo, type: 2});
        } else if (clearObj.value.type == 4) {
          // 每次最多删除max个文件
          if (Object.keys(clearObj.value.autoMap).length > 0) {
            for (let key in clearObj.value.autoMap) {
              if (Object.keys(clearObj.value.p).length >= clearObj.value.max) {
                break;
              }
              if (!clearObj.value.p[key]) {
                clearObj.value.p[key] = () => {
                  new Promise(resolve => {
                    fs.unlink(clearObj.value.autoMap[key].path, function () {
                      // 删除本地关联
                      try {
                        remote.store.state.fileDB.del("name", clearObj.value.autoMap[key].name);
                      } catch (e) {}
                      delete clearObj.value.autoMap[key];
                      delete clearObj.value.p[key];
                      resolve();
                    });
                  });
                }
                clearObj.value.p[key]();
              }
            }
            return;
          } else if (clearObj.value.moreAuto) {
            // 再次获取清理maxMap文件
            clearObj.value.moreAuto = false;
            auto();
          } else {
            // 删除完成
            clearObj.value.type = 3;
          }
        }
      }, {
        deep: true
      }
    );

    // 清理缓存文件
    function clear() {
      clearObj.value.show = true;
      clearObj.value.type = 1;
      countFilesAndFolders({
        dir: getFileCachedPath({account: userInfo.workerNo, type: 1}),
        done: res => {
          // 设置文件清除弹窗数据
          clearObj.value.max = 500;
          clearObj.value.fileMap = res.fileMap;
          clearObj.value.folderMap = res.folderMap;
          clearObj.value.num = 0;
          clearObj.value.total = Object.keys(clearObj.value.fileMap).length + Object.keys(clearObj.value.folderMap).length;
          clearObj.value.type = 2;
        }
      });
    }

    // 后台自动清理文件
    function auto() {
      clearObj.value.fileMap = {};
      clearObj.value.folderMap = {};
      countFilesAndFolders({
        dir: getFileCachedPath({account: userInfo.workerNo, type: 1}),
        auto: true,
        done: res => {
          clearObj.value.max = 1;
          clearObj.value.autoMap = res.autoMap;
          clearObj.value.type = 4;
        }
      });
    }

    // 获取目录下的文件和目录数量
    function countFilesAndFolders(param) {
      let folderCount = 0;
      let totalCount = 0;
      let autoCount = 0;
      let fileMap = {};
      let folderMap = {};
      let autoMap = {};
      let fileList = [];
      let getFilePerFlag = false;

      function traverseDirectory(dir) {
        fs.readdir(dir, (err, files) => {
          folderCount--;
          if (files?.length > 0) {
            totalCount += files.length;
            files.map(item => {
              fileList.push(path.join(dir, item));
            });
            clearObj.value.searchTotal += files.length;
            if (!getFilePerFlag) {
              getFilePer(fileList);
            }
          } else {
            if (totalCount == 0 && folderCount == -1) {
              param?.done && param.done({fileMap: fileMap, folderMap: folderMap, autoMap: autoMap});
              initDate();
            }
          }
        });
      }

      // 每maxMap个文件切割遍历获取
      function getFilePer(allFiles) {
        getFilePerFlag = true;
        let files = allFiles.splice(0, clearObj.value.maxMap);
        let p = [];
        // 统计该目录下的文件/文件夹数量，
        for (let i = 0; i < files.length; i++) {
          let filePath = files[i];
          p.push(new Promise(resolve => {
            fs.stat(filePath, (err, stats) => {
              if (!err) {
                if (stats.isFile()) {
                  fileMap[new Date(stats.mtime).getTime() + "-" + filePath] = filePath;
                  // 登录乐聊后自动清理超过一个月的图片缓存，最多清理1000个
                  if (param.auto && autoCount <= clearObj.value.maxMap && Date.now() + remote.store.state.diffTime - new Date(stats.mtime).getTime() > 30 * 24 * 60 * 60 * 1000) {
                    autoMap[new Date(stats.mtime).getTime() + "-" + filePath] = {path: filePath, name: filePath.slice(filePath.lastIndexOf("\\") + 1)};
                    autoCount++;
                  }
                } else if (stats.isDirectory()) {
                  folderCount++;
                  folderMap[filePath.split("\\").length + "-" + filePath] = filePath;
                  traverseDirectory(filePath);
                }
              }
              totalCount--;
              if ((totalCount == 0 && folderCount == -1) && fileList.length == 0) {
                // 全部统计完成
                clearObj.value.moreAuto = false;
                param?.done && param.done({fileMap: fileMap, folderMap: folderMap, autoMap: autoMap});
                initDate();
              } else if (param.auto && autoCount >= clearObj.value.maxMap && !clearObj.value.moreAuto) {
                // 自动清理每次maxMap个
                clearObj.value.moreAuto = true;
                param?.done && param.done({fileMap: fileMap, folderMap: folderMap, autoMap: autoMap});
                initDate();
              }
              resolve();
            });
          }));
        }
        // 查询当前文件结束后
        Promise.all(p).then(res => {
          if (clearObj.value.type == -1) {
            // 取消清理停止遍历
            initDate();
            return;
          }
          getFilePerFlag = false;
          if (fileList.length > 0) {
            getFilePer(fileList);
          }
        });
      }

      // 清除闭包数据缓存
      function initDate() {
        fileMap = {};
        folderMap = {};
        autoMap = {};
        fileList = [];
      }

      traverseDirectory(param.dir);
    }

    // type1取消清理
    function hideClear() {
      clearObj.value.show = false;
      clearObj.value.type = -1;
      // 隐藏/关闭还原后台自动清理
      auto();
    }

    return {
      clearObj,

      clear,
      hideClear,
    }
  }
}
</script>
<style scoped lang="scss">
.clear {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 101;

  .clear-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 340px;
    background: #FFFFFF;
    box-shadow: 0px 6px 12px 0px rgba(155, 155, 155, 0.5);
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #E7E7E7;

    .clear-header {
      line-height: 30px;
      background: #F0F0F0;
      padding: 0 16px;
      font-size: 12px;
      font-weight: bold;

      .operate-nav {
        position: absolute;
        top: 0;
        right: 6px;
        display: flex;

        i {
          width: 30px;
          height: 30px;
          cursor: pointer;
          background-image: url(/img/icon_operate.png);
          background-repeat: no-repeat;
          background-size: 240px 30px;
        }

        .min {
          &:hover {
            background-position: -30px 0;
          }
        }

        .close {
          background-position: -180px 0;

          &:hover {
            background-position: -210px 0;
          }
        }
      }
    }

    .clear-content {
      padding: 20px 16px;
      font-size: 14px;

      .clear-progress {
        display: flex;
        align-items: center;

        progress {
          flex: 1;
          margin: 0 12px;
        }
      }

      .clear-done {
        text-align: center;
      }
    }

    .clear-footer {
      display: flex;
      justify-content: center;
      padding-bottom: 16px;

      .clear-btn-box {
        display: flex;
        justify-content: center;
      }

      .clear-btn {
        padding: 6px 12px;
        border-radius: 4px;
        border: 1px solid #E0E0E0;
        cursor: pointer;

        &.btn-confirm {
          background: $styleColor;
          color: #FFFFFF;
          border: 1px solid $styleColor;
        }

        &:not(:first-child) {
          margin-left: 16px;
        }
      }
    }
  }
}
</style>