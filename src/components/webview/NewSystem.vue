<template>
  <div class="new-system">
    <!--页签-->
    <ul class="tab-ul">
      <li class="tab-li" :class="webviewIndex==key?'curr':''" v-for="(item,key) in webViewList" :key="key" @dblclick="closeWebview(key)"
          @click="selWebview(key)">
        <span>{{ item.name }}</span>
      </li>
      <li class="tab-li" v-if="webViewList.length == 0">新标签页</li>
    </ul>
    <!--工具栏-->
    <ul class="tool-ul">
      <li class="tool-li">后退</li>
      <li class="tool-li">前进</li>
      <li class="tool-li">刷新</li>
      <li class="tool-li">首页</li>
    </ul>
    <!--页面内容-->
    <ul class="web-ul">
      <li class="web-li" v-for="(item,key) in webViewList" :key="key" v-show="webviewIndex==key">
        <webview :src="item.url" :ref="setWebviewRef"></webview>
      </li>
    </ul>
  </div>
</template>
<script>
import {ref, watch} from "vue";
import {alert, loading, toast} from "@comp/ui";
import {useStore} from "vuex";
import {avatarError, dateFormat} from "@utils";
import {getLoginSecretApi} from "@utils/net/api"

export default {
  name: "Template",
  setup(props, ctx) {
    const store = useStore();
    let computerInfo = ref(store.getters.getComputerInfo);
    let baseComputerInfo = store.getters.getBaseComputerInfo;
    // 页面列表
    let webViewList = ref([]);
    let nim = store.getters.getNim;
    // 用户信息
    let userInfo = store.getters.getUserInfo;
    // 配置环境
    let config = store.getters.getConfig.config;
    // webview对象
    let webviewRefs = [];
    // webview当前窗口下标
    let webviewIndex = ref(0);
    window.alert = function () {}

    // 设置webview对象
    function setWebviewRef(e) {
      if (e) {
        webviewRefs.push(e);
        // 添加窗口打开打开新窗口监听
        if (!e.newWindowListener) {
          webviewIndex.value = webViewList.value.length - 1;

          e.newWindowListener = newWindowListener;
          // 打开新窗口监听
          e.addEventListener("new-window", newWindowListener);
          // 加载完成监听
          e.addEventListener("load-commit", function (e) {});

          // dom完成监听
          e.addEventListener("dom-ready", function (e) {
            let webview = e.target;
            // 打开webview内部的调试工具
            // webview.openDevTools();
          });
        }
      }
    }

    // 关闭选中webview
    function closeWebview(key) {
      if (key == webviewIndex.value) {
        webviewIndex.value--;
      }
      webViewList.value.splice(key, 1);
    }

    // 选中当前webview
    function selWebview(key) {
      webviewIndex.value = key;
    }

    // 首次获取新系统登录参数
    if (webViewList.value.length == 0) {
      setTimeout(() => {
        computerInfo.value = store.getters.getComputerInfo;
        toNewSys();
      }, 1000)
    }

    function newWindowListener(e) {
      let url = e.url;
      switch (e.disposition) {
        case "foreground-tab":
          webViewList.value.push({url: url, name: url});
          break;
        case "new-window":
          break;
        default:
          webViewList.value.push({url: url, name: url});
          break;
      }
    }

    // 跳转新系统
    async function toNewSys() {
      loading();
      let timeObj = await nim.getServerTime();
      loading().hide();
      if (timeObj.err) {
        toast({title: timeObj.err, type: 2});
        return;
      }
      if (!computerInfo.value.macAddress) {
        alert({content: "获取设备MAC错误"});
        return;
      }
      loading();
      let res = await getLoginSecretApi({
        msgBody: JSON.stringify({
          time: dateFormat(timeObj.obj, "MMddHHmm"),
          netId: computerInfo.value.macAddress,
          ip: baseComputerInfo.address,
          imei: baseComputerInfo.hostName,
          workerId: userInfo.workerId,
          token: userInfo.token
        })
      });
      loading().hide();
      if (!res.success) {
        console.log("getLoginSecretApiErr", res);
        alert({content: "抱歉,登入新系统错误,请稍后再试哦!"});
        return;
      } else if (res.data && res.data.secretKey) {
        let url = `${config[config.env].jjsHome}/jjslogin/forward?id=${res.data.secretKey}`;
        webViewList.value[0] = {url: url, name: url};
      } else {
        alert({content: "电脑未认证，不能跳转新系统，请去乐聊-工作台-乐聊工具-公司电脑认证提交后再试。"});
      }
    }

    return {
      webViewList,
      webviewIndex,

      setWebviewRef,
      closeWebview,
      selWebview,
    }
  }
}
</script>
<style scoped lang="scss">
.new-system {
  width: 100%;
  height: 100%;

  .tab-ul {
    display: flex;
    background: #999999;

    .tab-li {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding: 10px;

      &.curr {
        background: #FFFFFF;
        border-radius: 10px 10px 0 0;
      }
    }
  }

  .tool-ul {
    display: flex;
    background: #FFFFFF;
    border-bottom: 1px #EEEEEE solid;
    padding: 5px;

    .tool-li {
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background: #EEEEEE;
      }
    }
  }

  .web-ul {
    width: 100%;
    height: calc(100% - 100px);

    .web-li {
      width: 100%;
      height: 100%;
    }

    webview {
      width: 100%;
      height: 100%;
    }
  }
}
</style>