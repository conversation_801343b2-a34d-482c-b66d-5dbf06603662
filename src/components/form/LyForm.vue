<template>
  <div>
    <slot></slot>
  </div>

</template>

<script setup>
import {ref, useSlots,provide} from "vue";

const props = defineProps({
  vnode: Object,
  rules: {
    type: Object,
    default: {}
  },
  formData: Object,
});
// 所有表单项
const fields = ref([]);

const emits = defineEmits(['submit']);
// const slots = useSlots();
// const formItems = slots.default && slots.default()[0]
// console.log("formItems",formItems)

// 错误信息
const errors = ref({});

// 添加表单项
function addField(field) {
  fields.value.push(field);
}
provide('addField', addField);

function validate() {
    errors.value = {};
    for (const item of fields.value) {
      const rules = [...(item.rules || []), ...(props.rules[item.prop] || [])];
      const { prop } = item;
      for (const rule of rules) {
        const { type, message, validator } = rule;
        // 使用自定义校验函数
        if (validator) {
          const isValid = validator(props.formData[prop]);
          if (!isValid) {
            errors.value[prop] = message || `${item.label}校验未通过。`;
          }
        } else {
          // 默认校验逻辑
          if (type === 'required' && !props.formData[prop]) {
            errors.value[prop] = message || `${item.label}是必填项。`;
          } else if (type === 'notEmpty' && props.formData[prop]?.trim() === '') {
            errors.value[prop] = message || `${item.label}不能为空。`;
          } else if (type === 'maxLength' && item.maxLength && props.formData[prop]?.length > item.maxLength) {
            errors.value[prop] = message || `${item.label}长度不能超过 ${item.maxLength}。`;
          }
        }
        if( errors.value[prop]){
          item?.error(errors.value[prop])
        }else{
          item?.error("")
        }
      }
    }
    return Object.keys(errors.value).length === 0;
}

function handleSubmit() {
    if (validate()) {
      emits('submit', props.formData);
    }
}

defineExpose({
  validate,
  handleSubmit
});
</script>
<style lang="scss" >
.form-item + .form-item{
  margin-bottom: 10px;
}
</style>
