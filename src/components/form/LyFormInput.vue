<template>
  <div class="default-input">
    <i class="left-ico" v-if="showLeftIcon">
      <slot name="left"></slot>
    </i>
    <input
      ref="inputDomRef"
      class="input"
      v-if="type !== 'textarea'"
      :style="{'padding-left':showLeftIcon?'28px':'','width':width}"
      :placeholder="placeholder"
      :value="modelValue"
      @input="onInput"
      @blur="$emit('blur')"
      @focus="$emit('focus')"
    />
    <textarea
        v-else
        ref="inputDomRef"
        :style="{'padding-left':showLeftIcon?'28px':'','width':width}"
        :placeholder="placeholder"
        :value="modelValue"
        :rows="(focus || modelValue) && rows?rows:1"
        @input="onInput"
        @blur="onBlur"
        @focus="onFocus"
    ></textarea>
    <i class="close-ico" v-show="showClearIcon&&modelValue&&modelValue.length>0" @click="clearInput()"></i>
  </div>
</template>
<script>
import {nextTick, ref} from "vue";

export default {
  name: 'LyFromInput',
  props: {
    modelValue: {
      type: [String,Number],
      default: "",
    },
    // 显示清理按钮
    showClearIcon:{
      default: false,
      type: Boolean,
    },
    // 显示左边slot
    showLeftIcon:{
        default: false,
        type: Boolean,
    },
    type: {
      default: "text",
      type: String,
    },
    placeholder: {
      default: "",
      type: String,
    },
    rows:{
      default: 2,
      type: Number
    },
    // 输入最大长度
    maxLength:{
        default: -1,
        type: Number,
    },
    // 过滤空白, 会导致五笔输入法吃字
    // lengthFilterTrim:{
    //     default: true,
    //     type: Boolean,
    // },
    // 宽度
    width:{
        default: '100%',
        type: String,
    },
    // 禁止表情包
    banEmoji:{
      default: true,
      type: Boolean,
    },
    // 跟随输入自动处理高度
    autoHeight:{
      default: false,
      type: Boolean,
    },
    // 校验是否可输入
    regularInput:{
      type: Function,
      default: (value) => value,
    }
  },
  setup(props, { emit }) {
    const focus = ref(false);
    const inputDomRef = ref(null);
    // 清空输入
    function clearInput(){
      emit('update:modelValue', '');
    }
    function onInput(event){
      let value = event.target.value
      // if(props.lengthFilterTrim){
      //   value = value.trim();
      // }
      if(props.maxLength!==-1){
        const length = event.target.value.length || 0;
        if(length>props.maxLength){
          value = value.slice(0,props.maxLength);
        }
      }
      if(props.banEmoji){
        value = value.replace(/[\ud800-\udbff][\udc00-\udfff]/g, '');
      }

      handleAutoHeight();
      event.target.value = props.regularInput(value)
      emit('update:modelValue', props.regularInput(value))
    }

    function handleAutoHeight(){
      if(props.autoHeight){
        nextTick(async () => {
          inputDomRef.value.target.style.height = 'auto' // 先重置元素的高度，此行代码可以试试注释看看打印效果 ~
          inputDomRef.value.target.target.style.height = event.target.scrollHeight + 'px' // 再设置元素的真实高度，此行代码可以试试注释看看打印效果 ~
        })
      }
    }
    // 焦点
    function onFocus(){
      emit('focus');
      focus.value = true;
    }
    function onBlur(){
      emit('blur');
      focus.value = false;
    }

    return {
      inputDomRef,
      clearInput,
      onInput,
      onFocus,
      onBlur,
      focus,
    };
  },
};
</script>
<style lang="scss" scoped>
.default-input {
  position: relative;
  display: inline;
  input , textarea {
    border: 1px solid #E0E0E0;
    font-size: 12px;
    padding: 6px 10px;

    transition: 0.2s;
    border-radius: 4px;
  }

  input::placeholder , textarea::placeholder {
    color: #999999;
  }
  input:focus ,textarea:focus {
    border: 1px solid #333333;
  }
  .left-ico {
    position: absolute;
    width: 14px;
    height: 15px;
    left: 8px;
    top: 5px;
    background-size: 17px 17px;
  }
  .close-ico{
    position: absolute;
    background: url("/img/search/close.png") no-repeat;
    width: 12px;
    height: 12px;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    background-size: 12px 12px;
    cursor: pointer;
  }
}
</style>
