<template>
  <div :class="{'ly-form-calendar':true,'hover':showCalendar}" ref="inputRef" @click="showCalendar=true" >
    <div class="default-input" :style="{'width':width}"  >
      <div class="full">
        <span class="placeholder-text" v-if="!modelValue || modelValue?.length === 0">{{ placeholder }}</span>
        <span v-else>{{ dateFormatUtils(modelValue,dateFormat) }}</span>
      </div>
      <i class="ico"></i>
    </div>
  </div>

  <teleport to="body">
    <div class="calendar" v-if="showCalendar" :style="optionsStyle" ref="selectOptionRef">
      <div class="calendar-header">
        <div class="calendar-title">{{ dateFormatUtils(changeDayInfo,"yyyy年MM月") }}</div>
        <div class="calendar-change-box">
          <i class="calendar-icon pre" @click="changeDate('pre')"></i>
          <i class="calendar-icon next" @click="changeDate('next')"></i>
          <i class="calendar-icon toggle" :class="calendarObj.toggle?'':'not-toggle'"></i>
        </div>
      </div>
      <div class="calendar-day">
        <div class="day-header">
          <div class="day-list">一</div>
          <div class="day-list">二</div>
          <div class="day-list">三</div>
          <div class="day-list">四</div>
          <div class="day-list">五</div>
          <div class="day-list">六</div>
          <div class="day-list">日</div>
        </div>
        <div class="day-content">
          <div class="day-ul" v-for="(item,key) in currentDayList" :key="key">
            <div class="day-list" :class="{'curr':isDay(item1),'sel':isDay(item1,1),'gray':(!isMonth(item1)||item1.disabled)}" v-for="(item1,key1) in item"
                 :key="item1" @click.stop="selItemDate(item1)">
              <div class="day-title">{{ isDay(item1) ? "今" : item1.day }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>
<script>
import {computed, onMounted, onUnmounted, ref, watch} from "vue";
import {useStore} from "vuex";
import { dateFormat as dateFormatUtils } from "@utils";

export default {
  name: "LyFormCalendar",
  props: {
    modelValue: {
      default: null,
      type: [Object, String,Number,null],
    },
    // 自定义禁止日期
    customDisabled: {
      type: Function,
      default: ()=>false
    },
    minDateDisabled:{
      type: [String,Number],
      default: "",
    },
    maxDateDisabled:{
      type: [String,Number],
      default: "",
    },
    // 宽度
    width:{
      default: '150px',
      type: String,
    },
    placeholder:{
      default: '',
      type: String
    },
    // 日期格式
    dateFormat:{
      default: 'yyyy-MM-dd',
      type: String
    }
  },
  setup(props, {emit}) {
    const store = useStore();
    // 组件ref对象
    const inputRef = ref(null);
    // 日期框
    const selectOptionRef = ref(null);
    // 日历信息
    let calendarObj = ref({
      toggle: false,
    });
    const showCalendar = ref(false);
    // 系统时间信息
    let serverDayInfo = ref(new Date());
    // 当前月份数据
    let currentDayInfo = ref(new Date());
    // 上下切换月份时间信息
    let changeDayInfo = ref(new Date());
    // 当前月份日期数组
    let currentDayList = ref([]);
    // 限制最小日期
    let minDate = "";
    // 限制最大日期
    let maxDate = "";

    const optionsStyle = computed(() => {
      if (inputRef.value && showCalendar.value) {
        const rect = inputRef.value.getBoundingClientRect();
        return {
          top: `${rect.bottom}px`,
          left: `${rect.left }px`,
        };
      }
      return {};
    });
    async function getCurrentDate() {
      let time = new Date().getTime();
      let timeObj = await store.dispatch("setDiffTime");
      if (!timeObj.err) {
        time = timeObj.obj;
      }
      return new Date(time);
    }

    // 获取日历 time-时间对象
    async function getCalendar(param) {
      let currDate = "";
      if (param.change) {
        currDate = changeDayInfo.value;
      } else {
        currDate = await getCurrentDate();
        serverDayInfo.value = currDate;
        // 传参日期
        if (param.date) {
          currDate = param.date;
        }
        // 限制日期范围
        if (param.minDate) {
          minDate = param.minDate;
        }
        if (param.maxDate) {
          maxDate = param.maxDate;
        }
        // 不显示切换
        if (param.toggle) {
          calendarObj.toggle = param.toggle;
        }
        currentDayInfo.value = currDate;
        changeDayInfo.value = currDate;
      }
      // 获取当月信息
      let year = currDate.getFullYear();
      let month = currDate.getMonth() + 1;
      let firstDay = new Date(year + '/' + month);
      // 上一天
      let preDay = new Date(firstDay - 24 * 60 * 60 * 1000);
      // 下个月
      let nextMonth = month + 1;
      let nextYear = year;
      if (nextMonth > 12) {
        nextYear++;
        nextMonth -= 12;
      }
      // 当月最后一天
      let lastDay = new Date(new Date(nextYear + '/' + nextMonth).getTime() - 24 * 60 * 60 * 1000);
      let monthDayList = [];
      // 周一开始计算 上个月在当前月显示的日期
      for (let i = preDay.getDay() - 1; i >= 0; i--) {
        monthDayList.push({
          year: preDay.getFullYear(),
          month: preDay.getMonth() + 1,
          day: preDay.getDate() - i
        });
      }
      // 当前月的日期
      for (let i = 1; i <= lastDay.getDate(); i++) {
        monthDayList.push({
          year: lastDay.getFullYear(),
          month: lastDay.getMonth() + 1,
          day: i
        });
      }
      let dayLength = 7;
      if (monthDayList.length + (dayLength - lastDay.getDay()) == 35) {
        dayLength = 14;
      }
      // 周日结束 下个月在当前月显示的日期
      for (let i = 1; i <= dayLength - lastDay.getDay(); i++) {
        monthDayList.push({
          year: nextYear,
          month: nextMonth,
          day: i
        });
      }
      // 当前月份每7天一个数组数据
      let monthIndex = -1;
      let monthList = [];
      monthDayList.map((item, index) => {
        if (index % 7 == 0) {
          monthIndex++;
        }
        if (!monthList[monthIndex]) {
          monthList[monthIndex] = [];
        }
        let dateTime = new Date(item.year + "/" + item.month + "/" + item.day).getTime();
        // 限制日期范围
        if(props.customDisabled(item)){
          item.disabled = "customDisabled";
        }else if (minDate && dateTime < minDate) {
          item.disabled = "minDate";
        }else if (maxDate && dateTime > maxDate) {
          item.disabled = "maxDate";
        }
        monthList[monthIndex].push(item);
      });
      currentDayList.value = monthList;
    }

    // 选择当前日期
    function selItemDate(item) {
      if (item.disabled) {
        return;
      }
      showCalendar.value = false;
      emit("update:modelValue", dateFormatUtils(new Date(item.year + "/" + item.month + "/" + item.day), props.dateFormat));
    }

    // 判断是否置顶时间-type-1当前选择时间-默认系统时间
    function isDay(item, type) {
      let flag = false;
      let dateInfo = serverDayInfo.value;
      if (type == 1) {
        dateInfo =  props.modelValue?new Date(props.modelValue):currentDayInfo.value;
      }
      if (dateInfo.getFullYear() == item.year && dateInfo.getMonth() + 1 == item.month && dateInfo.getDate() == item.day) {
        flag = true
      }
      return flag;
    }

    // 是否当前月
    function isMonth(item) {
      return changeDayInfo.value.getMonth() + 1 == item.month;
    }

    // 改变日期
    function changeDate(type) {
      let date = changeDayInfo.value;
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let dateTime = "";
      if (type == "pre") {
        month--;
        if (month < 1) {
          year--;
          month += 12;
        }
        dateTime = new Date(new Date(date.getFullYear() + "/" + (date.getMonth() + 1) + "/1") - 24 * 60 * 60 * 1000);
      } else if (type == "next") {
        month++;
        if (month > 12) {
          year++;
          month -= 12;
        }
        dateTime = new Date(year + "/" + month + "/1");
      }
      // 限制日期范围
      if (minDate && dateTime < minDate) {
        return;
      }
      if (maxDate && dateTime > maxDate) {
        return;
      }
      changeDayInfo.value = new Date(year + "/" + month + "/1");
      getCalendar({change: true});
    }
    const closeDropdown = (event) => {
      if (!inputRef?.value?.contains(event.target) && !selectOptionRef?.value?.contains(event.target)) {
        showCalendar.value = false;
      }
    };

    onMounted(() => {
      document.addEventListener('click', closeDropdown);
      const selSearchDate = props.modelValue || new Date(new Date().getTime() + store.getters.getDiffTime);
      getCalendar({date: selSearchDate instanceof Date?selSearchDate:new Date(selSearchDate), minDate: props.minDateDisabled, maxDate: props.maxDateDisabled});
    });

    onUnmounted(() => {
      document.removeEventListener('click', closeDropdown);
    });

    return {
      inputRef,
      selectOptionRef,
      calendarObj,
      currentDayInfo,
      changeDayInfo,
      currentDayList,
      showCalendar,
      optionsStyle,
      getCalendar,
      selItemDate,
      isDay,
      isMonth,
      changeDate,
      dateFormatUtils,
    }
  }
}
</script>
<style scoped lang="scss">
.ly-form-calendar {
  position: relative;
  display: inline;
  border: 1px solid #E0E0E0;
  font-size: 12px;
  padding: 6px 9px;
  transition: 0.2s;
  border-radius: 4px;
  min-height: 30px;

  .default-input {
    display: flex;
    align-items: center;

    .full {
      flex: 1;
    }

    .placeholder-text {
      color: #999999;
    }

    .ico {
      background: url("/img/form/icon-calendar.png") no-repeat;
      width: 16px;
      height: 16px;
      background-size: 16px 16px;
      display: inline-block;
    }
  }
}
.calendar {
  width: 200px;
  //height: 300px;
  position: absolute;
  top:34px;
  left:0;
  background: #FFFFFF;
  padding: 8px;
  z-index: 90;
  border: 1px solid #CCCCCC;
  &.hide .calendar-day {
    display: none;
  }

  .calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .calendar-title {
      font-size: 14px;
      font-weight: 500;
      color: #222222;
    }

    .calendar-change-box {
      display: flex;
    }

    .calendar-icon {
      background-image: url("/img/schedule/calendar.png");
      background-repeat: no-repeat;
      width: 20px;
      height: 20px;
      background-size: 240px 20px;
      margin-left: 12px;
      cursor: pointer;
      position: relative;

      &:hover:after {
        position: absolute;
        top: 26px;
        left: 50%;
        transform: translateX(-50%);
        padding: 6px 12px;
        font-size: 12px;
        color: #FFFFFF;
        background: rgba(0, 0, 0, 0.8);
        white-space: nowrap;
        z-index: 1;
        border-radius: 2px;
      }

      &.pre {
        background-position: -20px 0;

        &:hover {
          background-position: 0px 0;

          &:after {
            content: "\4e0a\4e2a\6708";
            z-index: 1;
          }
        }
      }

      &.next {
        background-position: -60px 0;

        &:hover {
          background-position: -40px 0;

          &:after {
            content: "\4e0b\4e2a\6708";
            z-index: 1;
          }
        }
      }

      &.toggle {
        background-position: -100px 0;

        &.not-toggle {
          display: none;

          &.hide {
            background-position: -140px 0;

            &:hover {
              background-position: -120px 0;

              &:after {
                content: "\5c55\5f00\65e5\5386";
                z-index: 1;
              }
            }
          }
        }

        &:hover {
          background-position: -80px 0;

          &:after {
            content: "\6536\8d77\65e5\5386";
            z-index: 1;
          }
        }
      }
    }
  }

  .calendar-day {
    font-size: 12px;
    line-height: 17px;
    font-weight: 500;

    .rest, .gray {
      color: #BFBFBF;

      .day-radio {
        background: #BFBFBF;
      }
    }

    .day-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-wrap: wrap;
      margin-top: 11px;
    }

    .day-content {
      .day-ul {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
      }

      .day-list {
        cursor: pointer;
        position: relative;

        &.curr {
          color: #2D91E6;
          border-radius: 50%;
          line-height: normal;
        }

        &:hover {
          background: #E7E7E7;
          border-radius: 50%;
        }

        &.sel {
          background: #2D91E6;
          color: #FFFFFF;
          border-radius: 50%;
          line-height: normal;
        }
      }
    }

    .day-ul {
      margin-top: 11px;
    }

    .day-list {
      width: 22px;
      height: 22px;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;

      .day-radio {
        width: 4px;
        height: 4px;
        background: #2D91E6;
        border-radius: 50%;
        position: absolute;
        bottom: -7px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
.hover{
  transition: 0.2s;
  border: 1px solid #333333 !important;
}
</style>
