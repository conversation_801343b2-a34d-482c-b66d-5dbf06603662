<template>
  <div class="form-item">
    <label class="label">
      <span class="tips" v-if="required">*</span> {{ label }}
    </label>
    <div class="value">
      <slot></slot>
      <div v-if="error" class="mt2" style="color: #E03236;">{{ error }}</div>
    </div>
  </div>
</template>

<script setup>

import {computed, inject, onMounted,ref} from "vue";

const props = defineProps({
  label: { type: String, required: true },
  prop: { type: String, required: true },
  required: { type: Boolean, default: false },
  maxLength: { type: Number, default: null },
  rules: { type: Array, default: () => [] },
});

const error = ref("")

onMounted(()=>{
  // 注册表单项
  inject('addField')({
    label: props.label,
    prop: props.prop,
    required: props.required,
    maxLength: props.maxLength,
    rules: props.rules,
    error:(errorMsg)=>error.value=errorMsg
  })
})
</script>
<style scoped lang="scss">
.form-item {
  display: flex;
  align-items: inherit;
  margin-bottom: 10px;

  & > .label{
    color: #666666;
    line-height: 30px;
    flex-shrink: 0;

    .tips {
      font-weight: 400;
      font-size: 12px;
      color: #E03236;
      line-height: 17px;
    }
  }
  & > .value{
    flex: 1;
    margin-left: 10px;
  }
}


</style>
