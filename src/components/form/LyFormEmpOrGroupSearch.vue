<template>
  <div class="custom-select" :style="{width:width}" @click="toggleDropdown" ref="selectRef">
    <div :class="{'select-trigger':true,'hover':isOpen}">
      <input v-model="input" class="input" :placeholder="placeholder" @input="isOpen = true"/>
      <slot v-if="rightIcon" name="rightIcon"></slot>
      <i class="close-ico" v-else-if="showClearIcon&&modelValue" @click="$emit('update:modelValue',null)"></i>
<!--      <span class="arrow" v-else></span>-->
    </div>
  </div>
  <teleport to="body">
    <!-- 下拉框人 -->
    <div v-if="isOpen && Object.keys(options).length !== 0" class="options" :style="optionsStyle" ref="selectOptionRef">
      <div
          v-for="(option,optionKey) in options"
          :key="optionKey">
        <div v-if="option?.length!==0">
          <div class="group-title">{{ optionKey==="empList"?'联系人':optionKey==="teamList"?'群组':'讨论组' }} </div>
          <div v-for="(item,index) in option" class="option" @click="selectOption(option,item,optionKey)">
            <span v-if="optionKey==='empList'"> {{ item.empName }} {{ item.deptName }}</span>
            <span v-else > {{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted,useSlots } from 'vue';
import {searchUsersApi} from "@utils/net/api";
import {sortTeamMembers} from "@utils";

export default {
  name: 'LyFormEmpOrGroupSearch',
  props: {
    modelValue: {
      type:[String,Number,Object,null],
      required: true
    },
    // 显示清理按钮
    showClearIcon:{
      default: false,
      type: Boolean,
    },
    keyValue: {
      type: String,
      default: 'key'
    },
    width:{
      default: '280px',
      type: String,
    },
    placeholder:{
      default:'',
      type:String,
    },
    // 限制大小
    limitPerson:{
      default: 100,
      type: Number,
    },
    // 是否选中后清理
    clearOption:{
      default: true,
      type: Boolean,
    }
  },
  emits: ['update:modelValue','handleSelect'],
  setup(props, { emit }) {
    const isOpen = ref(false);
    const { rightIcon } = useSlots();

    const selectRef = ref(null);
    const selectOptionRef = ref(null);
    // 弹窗样式
    const optionsStyle = ref({});
    // 选择的数据
    const select = ref([]);
    // 选项
    const options = ref({})
    const input = ref("");

    // 获取群信息
    function getTeam(id) {
      if (id) {
        return store.getters.getTeams({id: id}) || {};
      } else {
        return store.getters.getTeams({sort: 1}) || [];
      }
    }
    //搜索群组
    function filterTeamList(key) {
      let list = getTeam();
      let newTeamList = []
      let groupList = [];
      let teamList = []
      list.forEach(function (v, i) {
        if (v.name.indexOf(key) !== -1 && v.memberNum < props.limitPerson) {
          newTeamList.push(v)
        }
      })

      newTeamList.forEach(function (v, i) {
        //是否是讨论组
        if (getTeam(v.teamId).detailType === "group") {
          groupList.push(v);
        } else {
          teamList.push(v);
        }
      })

      return {groupList:JSON.parse(JSON.stringify(groupList)),teamList:JSON.parse(JSON.stringify(teamList))};
    }

    let empSearchTimer = null;

    function clearOption(){
      options.value = {};
    }
    function handleEmpSearchTimer(key){
      if(!key || key?.trim()?.length==0){
        return ;
      }
      if(!empSearchTimer){
        clearTimeout(empSearchTimer);
        empSearchTimer = null;
      }
      empSearchTimer = setTimeout(function () {
        searchUsersApi({
              msgBody: JSON.stringify({
                workerId: store.getters.getUserInfo,
                name: key,
                status: "1",//默认为全部1在职2为离职
                page: 1,
                rows: 50
              })
            }
        ).then(res=>{
          let resList = {};
          const {groupList,teamList} = filterTeamList(key);
          resList.empList = res.data.empList;
          resList.groupList = groupList;
          resList.teamList = teamList;
          options.value = resList;
        })
      }, 100)
    }
    watch(()=>input.value,(input)=>{
      handleEmpSearchTimer(input)
    })
    watch(()=>isOpen.value,(open)=>{
      if(open){
        handleOptionsStyle();
      }
    })

    watch(()=> props.modelValue,(newVal)=>{
      select.value = newVal || []
    })
    const toggleDropdown = () => {
      isOpen.value = !isOpen.value;
    };

    const selectOption = (options,option,type) => {
      if(type === "groupList" || type === "teamList"){
        store.dispatch("getTeamMembers", {id: option.teamId}).then(res => {
          // 加载群信息出错
          if (res.err) {
            console.error("加载群信息出错", res.err);
          }else{
            // 群主、管理员、普通成员排序
            let list = sortTeamMembers(res.obj);
            // 获取群员详情，只保留6位纯数字的账号
            store.dispatch("getPersons", {doneFlag: true, account: list.map(item => {return item.account}).filter(item => /^\d{6}$/.test(item)) }).then(personInfo => {
              let obj = JSON.parse(JSON.stringify(personInfo))
              Object.keys(obj).forEach(gV=>{
                if(!hasEmp(obj[gV])){
                  select.value.push({
                    type,
                    data: obj[gV],
                  })
                }
              })
            });
          }
        });
      }else if(type === "empList"){
        if(!hasEmp(option)){
          select.value.push({
            type,
            data: JSON.parse(JSON.stringify(option)),
          })
        }
      }
      handleOptionsStyle();
      isOpen.value = false
      input.value = "";
      if(props.clearOption){
        clearOption();
      }
      emit('update:modelValue', select.value);
    };

    // 判断是否存在
    function hasEmp(newEmp){
      for(let v of select.value){
        if((newEmp.empNumber != null && v.data.empNumber == newEmp.empNumber ) || (newEmp.workerId && v.data.workerId == newEmp.workerId)){
          return true;
        }
      }
      return false;
    }

    const closeDropdown = (event) => {
      if (selectRef.value && !selectRef.value.contains(event.target) && selectOptionRef.value && !selectOptionRef.value.contains(event.target)) {
        isOpen.value = false;
      }
    };

    // 重置弹窗位置
    function handleOptionsStyle(){
      if (selectRef.value && isOpen.value) {
        const rect = selectRef.value.getBoundingClientRect();
        optionsStyle.value = {
          top: `${rect.bottom}px`,
          left: `${rect.left }px`,
          width: `${rect.width}px`
        }
      }
    }


    onMounted(() => {
      document.addEventListener('click', closeDropdown);
      window.addEventListener('scroll', handleOptionsStyle);
    });

    onUnmounted(() => {
      document.removeEventListener('click', closeDropdown);
      window.removeEventListener('scroll', handleOptionsStyle);
    });

    return {
      input,
      select,
      isOpen,
      selectRef,
      selectOptionRef,
      rightIcon,
      toggleDropdown,
      selectOption,
      options,
      optionsStyle,
      clearOption,
    };
  }
};
</script>

<style scoped lang="scss">
.custom-select {
  position: relative;
}

.select-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  background-color: #fff;
  min-height: 30px;
  overflow: hidden;
  .input{
    width: calc(100% - 7px);
  }
  .input::placeholder{
    color: #999999;
  }
  .close-ico{
    position: absolute;
    background: url("/img/search/close.png") no-repeat;
    width: 12px;
    height: 12px;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
    background-size: 12px 12px;
    cursor: pointer;
  }

}
.hover{
  transition: 0.2s;
  border: 1px solid #000 !important;
}
.arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #4B4B4B;
  display: block;
  width: 0;
  height: 0;
  top: 0;
  right: 7px;
  bottom: 0;
  position: absolute;
  margin: auto 0;
}


.options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  z-index: 90;
}

.option {
  padding: 8px 12px;
  cursor: pointer;
}
.group-title{
  background: #F7F7F7;
  color: #000;
  font-weight: 700;
  line-height: 24px;
  padding-left: 12px;
}
.option:hover {
  background-color: #f7f7f7;
}
</style>
