<template>
  <div class="custom-select" :style="{width:width}" @click="toggleDropdown" ref="selectRef">
    <div :class="{'select-trigger':true,'hover':isOpen}">
      <input v-model="input" class="input" :placeholder="placeholder" />
      <slot v-if="rightIcon" name="rightIcon"></slot>
      <i class="close-ico" v-else-if="showClearIcon&&modelValue" @click="$emit('update:modelValue',null)"></i>
      <span class="arrow" v-else></span>
    </div>
  </div>
  <teleport to="body">
    <div v-if="isOpen" class="options" :style="optionsStyle" ref="selectOptionRef">
      <div
          v-for="(option,index) in filterOptions"
          :key="index"
          :class="{'option':true,'disabled':!optionsKeyByNow(option) && optionDisabled(option),'selectd': optionsKeyByNow(option)}"
          @click="selectOption(option)"
      >
        {{ option.label }}
      </div>
    </div>
  </teleport>
</template>

<script>
import { ref, computed, watch, onMounted, onUnmounted,useSlots } from 'vue';

export default {
  name: 'LyFromSelect',
  props: {
    modelValue: {
      type:[String,Number,Object,null],
      required: true
    },
    // 显示清理按钮
    showClearIcon:{
      default: false,
      type: Boolean,
    },
    // 描述
    placeholder:{
      default: "",
      type: String,
    },
    // 选项
    // [{label:"标题",value:"值"}]
    options: {
      type: Array,
      required: true
    },
    // modelValue为对象时，传这个用于关联某个项
    keyValue: {
      type: String,
      default: 'value'
    },
    width:{
      default: '140px',
      type: String,
    },
    // 禁止方法
    optionDisabled:{
      default: (option)=>false,
      type: Function,
    },
    // 是否开启过滤
    filter:{
      default: true,
      type:Boolean
    }
  },
  emits: ['update:modelValue','change'],
  setup(props, { emit }) {
    const isOpen = ref(false);
    const { rightIcon } = useSlots();

    const selectRef = ref(null);
    const selectOptionRef = ref(null);
    const input = ref("");
    // 选择的对象
    const selectObj = ref(null);

    // 处理输入后没输入内容，没选择时，要做清理。
    watch(()=>isOpen.value,(open)=>{
      if(!open){
        if(!props.modelValue){
          input.value = "";
        }else{
          let hf = selectObj.value?.label;
          if(!input.value){
            input.value = hf;
          }
          selectObj.value.label !== input.value && (input.value = hf);
        }
      }
    });

    // 监听modelValue变化
    watch(()=>props.modelValue,(value)=>{
      handleModelValue(value)
    })

    function handleModelValue(value){
      if(value==null) return;
      let tempSelectObj = null;
      tempSelectObj = props.options.find((option)=> {
        if (typeof props.modelValue === 'object') {
          if (option.value?.[props.keyValue] == value[props.keyValue]) {
            return true;
          }
        } else {
          if (option.value == value) {
            return true;
          }
        }
        return false;
      });
      if(tempSelectObj){
        selectObj.value = tempSelectObj;
        input.value = selectObj.value.label;
      }
    }
    // 当前选择的key值
    const optionsKeyByNow = function(option) {
      if(!selectObj.value) return false;
      if (typeof props.modelValue === 'object') {
        return selectObj.value.value?.[props.keyValue] === option.value?.[props.keyValue];
      }else{
        return selectObj.value?.value === option?.value;
      }
    }


    const optionsStyle = computed(() => {
      if (selectRef.value && isOpen.value) {
        const rect = selectRef.value.getBoundingClientRect();
        return {
          top: `${rect.bottom}px`,
          left: `${rect.left }px`,
          width: `${rect.width}px`
        };
      }
      return {};
    });

    const filterOptions = computed(()=>{
      if(!input.value) return props.options;
      if(!props.filter) return props.options;
      return props.options.filter(option => {
        // if (typeof props.modelValue?.value === 'object') {
        //   return option.label?.includes(input.value);
        // }
        return option.label.includes(input.value);
      });
    })

    const toggleDropdown = () => {
      isOpen.value = !isOpen.value;
    };

    const selectOption = (option) => {
      if(!props.optionDisabled(option)){
        input.value = option.label;
        selectObj.value = option;

        if (typeof props.modelValue?.value === 'object') {
          emit('update:modelValue', { ...option });
        } else {
          emit('update:modelValue', option.value);
        }
        emit('change', option.value,option);
        isOpen.value = false;
      }

    };

    const closeDropdown = (event) => {
      if (selectRef.value && !selectRef.value.contains(event.target) && selectOptionRef.value && !selectOptionRef.value.contains(event.target)) {
        isOpen.value = false;
      }
    };

    onMounted(() => {
      document.addEventListener('click', closeDropdown);
      window.addEventListener('scroll', ()=>{optionsStyle.value = 1;});
      handleModelValue(props.modelValue)
    });

    onUnmounted(() => {
      document.removeEventListener('click', closeDropdown);
      window.removeEventListener('scroll', ()=>{optionsStyle.value = 1});

    });

    return {
      input,
      isOpen,
      selectRef,
      selectOptionRef,
      rightIcon,
      toggleDropdown,
      selectOption,
      selectObj,
      optionsStyle,
      filterOptions,
      optionsKeyByNow,
    };
  }
};
</script>

<style scoped lang="scss">
.custom-select {
  position: relative;
}

.select-trigger {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  background-color: #fff;
  min-height: 30px;
  overflow: hidden;
  .input{
    width: calc(100% - 7px);
  }
  .input::placeholder{
    color: #999999;
  }
}
.hover{
  transition: 0.2s;
  border: 1px solid #000 !important;
}
.arrow {
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid #4B4B4B;
  display: block;
  width: 0;
  height: 0;
  top: 0;
  right: 7px;
  bottom: 0;
  position: absolute;
  margin: auto 0;
}


.options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 150px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
  z-index: 90;
  .selectd{
    color: #E03236;
    font-weight: bold;
  }
}
.disabled{
  cursor: not-allowed;
  color: #ccc;
}
.option {
  padding: 8px 12px;
  cursor: pointer;
}

.option:hover {
  background-color: #f5f7fa;
}
</style>
