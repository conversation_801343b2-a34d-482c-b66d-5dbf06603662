<template>
  <label class="ly-form-check">
    <input
        type="checkbox"
        :value="label"
        :name="name"
        :disabled="disabled"
        v-model="model"
        @change="handleChange"
    >
    <span class="label">
      <slot></slot>
      <template v-if="!$slots.default">{{ label }}</template>
    </span>
  </label>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'LyFormCheck',
  props: {
    modelValue: {
      default: "",
    },
    label: {
      type: [ Boolean],
      default: false
    },
    disabled: Boolean,
    name: String
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const model = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      }
    })

    const handleChange = () => {
      emit('change', model.value)
    }

    return {
      model,
      handleChange
    }
  }
}
</script>

<style scoped>
.ly-form-check {
  font-weight: 500;
  line-height: 1;
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  outline: none;
  font-size: 14px;
}

/* 隐藏默认的radio按钮 */
.ly-form-check input[type="radio"] {
  display: none;
}


/* 选中状态 */
.ly-form-check input[type="checkbox"]:checked + .custom-check {
  //border-color: red;
  border: 7px solid #e03236;
}

/* 选中状态时显示点 */
.ly-form-check input[type="radio"]:checked + .custom-radio::after {
  transform: translate(-50%, -50%) scale(1);
}

.label {
  font-size: 12px;
}

.ly-form-check.is-disabled {
  cursor: not-allowed;
}

.ly-form-check.is-disabled .custom-radio {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.ly-form-check.is-disabled .label {
  color: #c0c4cc;
  cursor: not-allowed;
}
input[type="checkbox"]:checked::before{
  width: 14px;
  height: 14px;
}
input[type="checkbox"]{
  margin-right: 6px;
  width: 14px;
  height: 14px;
}
</style>
