<template>
  <label class="ly-form-radio">
    <input
        type="radio"
        :value="label"
        :name="name"
        :disabled="disabled"
        v-model="model"
        @change="handleChange"
    >
    <span class="custom-radio"></span>
    <span class="label">
      <slot></slot>
      <template v-if="!$slots.default">{{ label }}</template>
    </span>
  </label>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'LyFormRadio',
  props: {
    modelValue: {
      default: "",
    },
    label: {
      type: [String, Number, Boolean],
      default: ''
    },
    disabled: Boolean,
    name: String
  },
  emits: ['update:modelValue', 'change'],
  setup(props, { emit }) {
    const model = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      }
    })

    const handleChange = () => {
      emit('change', model.value)
    }

    return {
      model,
      handleChange
    }
  }
}
</script>

<style scoped>
.ly-form-radio {
  font-weight: 500;
  line-height: 1;
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  outline: none;
  font-size: 14px;
}

/* 隐藏默认的radio按钮 */
.ly-form-radio input[type="radio"] {
  display: none;
}

/* 外部圆圈 */
.custom-radio {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 1px solid #ddd;
  border-radius: 50%;
  position: relative;
}

/* 选中状态 */
.ly-form-radio input[type="radio"]:checked + .custom-radio {
  //border-color: red;
  border: 7px solid #e03236;
}

/* 圆圈内的点 */
.custom-radio::after {
  content: '';
  display: block;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.2s ease-in-out;
}

/* 选中状态时显示点 */
.ly-form-radio input[type="radio"]:checked + .custom-radio::after {
  transform: translate(-50%, -50%) scale(1);
}

.label {
  font-size: 12px;
  padding-left: 4px;
}

.ly-form-radio.is-disabled {
  cursor: not-allowed;
}

.ly-form-radio.is-disabled .custom-radio {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

.ly-form-radio.is-disabled .label {
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>
