<template>
  <!--消息平台-->
  <span v-if="(item.content.type=='msg-center'||item.content.type=='msg-center-link')&&item.content.data" :class="['msg-center-type','msg-center-type'+type]">
    <!--头像-->
    <div v-if="type==1" class="user-avatar-box">
      <div class="avatar-box">
        <img class="avatar notCopy selNone" :src="'https://front.leyoujia.com/images/bm/'+item.content.data.headPic" alt=""
             :onerror="avatarError.bind(this, 'p2p', item.from, '')" @contextmenu.stop="setMenu($event, item, 4)">
      </div>
    </div>
    <div class="msg-center-box">
     <!--名字-->
      <div v-if="type==1" class="user-name-box">
        <div class="user-name notCopy">
          {{ item.content.data.belongDeptName || "" }}&nbsp;&nbsp;{{ dateFormat(item.time, 'yyyy-MM-dd HH:mm') }}
        </div>
      </div>
      <!--内容-->
      <div class="msg-center-content">
        <div class="msg-center-title">
          <span class="msg-center-label" :class="'msg-center-label-'+item.content.data.rank" v-if="item.content.data.rank==2||item.content.data.rank==3">
            {{ item.content.data.rank == 2 ? "重要" : item.content.data.rank == 3 ? "紧急" : "" }}
          </span>
          <span class="msg-center-text">{{ item.content.data.title }}</span>
        </div>
        <div class="msg-center-content-box">
          <template v-for="(item1,key1) in item.content.data.content" :key="item1.type+item.time">
            <div class="msg-center-intr" v-if="item1.value&&item1.type=='text'" v-html="setSubSerHtml(item1.value)"></div>
            <div class="msg-center-link-box" v-if="item1.value&&item1.type=='link'">
              <span>{{ item1.title }}</span>
              <span class="msg-center-link-detail" @click="toMsgCenterLink(item1)">{{ item1.value }}</span>
            </div>
            <div class="msg-center-img-box" v-else-if="item1.value&&item1.type=='img'">
              <img class="msg-img" :src="item1.value" :data-local="item1.value" :data-src="item1.value" :data-key="''+item.time+key1" :width="item1.showW" :height="item1.showH"
                   @dblclick="toViewer" @click="clickImage" :onerror="fangError">
            </div>
            <div class="msg-center-btn-box" v-else-if="item1.type=='button'">
              <div class="msg-center-btn textEls" v-for="(item2,key2) in item1.value" :key="key2" @click="toMsgCenterLink(item2)">
                {{ item2.value }}
              </div>
            </div>
          </template>
        </div>
        <div class="msg-center-remind-box" v-if="item.content.data.laterRemind == 2">
          <span class="msg-center-remind-intr">稍后提醒:</span>
          <span class="msg-center-remind-btn" @click="remindLater(item, 10)">10分钟</span>
          <span class="msg-center-remind-btn" @click="remindLater(item, 30)">30分钟</span>
          <span class="msg-center-remind-btn" @click="remindLater(item, 60)">1个小时</span>
        </div>
      </div>
    </div>
  </span>
</template>
<script>
import {useStore} from "vuex";
import {avatarError, fangError, dateFormat, setSubSerHtml} from "@utils";

export default {
  name: "MsgCenter",
  props: {
    type: {
      type: Number,
      default: 1,// 1公众号展示2普通会话展示
    },
    // 消息对象
    item: {
      type: Object,
      default: {},
    },
    setMenu: {
      type: Function,
      default: function () {},
    },
    toViewer: {
      type: Function,
      default: function () {},
    },
    clickImage: {
      type: Function,
      default: function () {},
    },
  },
  setup(props, ctx) {
    const store = useStore();

    // 跳转消息平台
    function toMsgCenterLink(item) {
      store.dispatch("toMsgCenterLink", {item: item, isTips: true});
    }

    // 稍后提醒日程
    function remindLater(item, minute) {
      store.dispatch("remindLater", {item: item, minute: minute});
    }

    return {
      avatarError,
      fangError,
      dateFormat,
      setSubSerHtml,

      toMsgCenterLink,
      remindLater,
    }
  }
}
</script>
<style scoped lang="scss">
.msg-center-type {
  display: flex !important;

  &.msg-center-type-1 {
    .msg-center-box {
      width: 72%;
      max-width: 660px;
    }

    .msg-center-content {
      &:after {
        content: "";
        width: 0;
        height: 0;
        position: absolute;
        top: 12px;
        border-width: 6px;
        border-style: solid;
        left: -12px;
        border-color: transparent #FFFFFF transparent transparent;
      }
    }
  }

  .msg-center-box {
    width: 456px;
    max-width: 100%;
    flex-shrink: 0;
  }

  .msg-center-content {
    background: #FFFFFF;
    border-radius: 4px;
    position: relative;

    .msg-center-title {
      padding: 10px 10px 0 10px;
      margin-bottom: 8px;
      word-break: break-all;

      .msg-center-label {
        background: #FF9100;
        border-radius: 1px;
        font-size: 12px;
        padding: 1px 6px;
        color: #FFFFFF;
        display: inline !important;
        vertical-align: unset !important;
        margin-right: 6px;

        &.msg-center-label-3 {
          background: $styleColor;
        }
      }

      .msg-center-text {
        font-weight: 600;
        font-size: 16px;
        line-height: 22px;
        display: inline !important;
        vertical-align: baseline !important;
      }
    }

    .msg-center-content-box {
      padding: 0 10px;
      border-bottom: 1px solid transparent;
    }

    .msg-center-intr {
      font-size: 14px;
      color: #333333;
      line-height: 20px;
      margin-bottom: 8px;
      white-space: pre-wrap;
      word-break: break-all;
      text-align: justify;
    }

    .msg-center-link-box {
      line-height: 20px;
      margin-bottom: 6px;
      font-size: 14px;

      .msg-center-link-detail {
        color: $styleLink;
        border-bottom: 1px solid $styleLink;
        cursor: pointer;
      }
    }

    .msg-center-img-box {
      width: 100%;
      display: flex;
      justify-content: center;
      margin-bottom: 8px;

      img {
        max-width: 100%;
      }
    }

    .msg-center-btn-box {
      display: flex;
      margin-bottom: 8px;
      background: #FFFFFF;

      .msg-center-btn {
        flex: 1;
        height: 30px;
        line-height: 28px;
        text-align: center;
        border-radius: 2px;
        border: 1px solid $styleColor;
        margin-right: 10px;
        color: $styleColor;
        font-size: 14px;
        cursor: pointer;

        &:hover {
          background: #FFF3F3;
        }

        &:last-child {
          margin-right: 0px;
        }
      }
    }

    .msg-center-remind-box {
      border-top: 1px solid #EEEEEE;
      height: 37px;
      line-height: 36px;
      padding: 0 10px;

      .msg-center-remind-intr {
        color: #666666;
      }

      .msg-center-remind-btn {
        margin-left: 10px;
        cursor: pointer;

        &:hover {
          color: $styleColor;
        }
      }
    }
  }
}
</style>