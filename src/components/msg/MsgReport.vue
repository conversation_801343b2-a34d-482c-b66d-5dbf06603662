<template>
  <div class="msg-report selAll" ref="reportRef">
    <!--消息播报-->
    <span v-if="msgItem.content.type=='msg-report'&&msgItem.content?.data" :class="['msg-report-type','msg-report-type'+type]">
      <!--头像-->
      <div v-if="type==1" class="user-avatar-box">
        <div class="avatar-box">
          <img class="avatar notCopy selNone" :src="'https://front.leyoujia.com/images/bm/'+msgItem.content.data.headPic" alt=""
               :onerror="avatarError.bind(this, 'p2p', msgItem.content.from, '')">
        </div>
      </div>
      <div class="msg-report-box">
        <!--名字-->
        <div v-if="type==1" class="user-name-box">
          <div class="user-name notCopy">
            {{ msgItem.content.data.belongDeptName || "" }}&nbsp;&nbsp;{{ dateFormat(msgItem.time, 'MM-dd HH:mm') }}
          </div>
        </div>
        <!--内容-->
        <div class="msg-report-content win-no-drag">
          <div class="msg-report-title">
             <!--头像-->
            <div class="user-avatar-box">
              <div class="avatar-box">
                <img class="avatar notCopy selNone" :src="getPerson(msgItem.content.data.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', msgItem.content.data.from, '')">
              </div>
            </div>
            <span class="msg-report-text textEls2" :title="msgItem.content.data.title">{{ msgItem.content.data.title }}</span>
          </div>
          <div class="msg-report-content-box">
            <template v-for="(item1,key1) in msgItem.content.data.content" :key="item1.type+item1.time">
              <div class="msg-report-btn-box" v-if="item1.type=='button'" v-show="type!=3">
                <div class="msg-report-btn textEls" v-for="(item2,key2) in item1.value" :key="key2" @click="toMsgCenterLink(item2)">
                  {{ item2.value }}
                </div>
              </div>
              <template v-else>
                <div class="msg-info">
                  <!--头像-->
                  <div class="user-avatar-box border notCopy selNone">
                    <div class="avatar-box notCopy selNone">
                      <img class="avatar notCopy selNone" :src="getPerson(item1.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item1.from, '')">
                    </div>
                  </div>
                  <div class="msg-box">
                    <!--名字-->
                    <div class="user-name-box">
                      <div class="user-name notCopy">{{ getPerson(item1.from).name }}&nbsp;&nbsp;{{ dateFormat(item1.time, 'MM-dd HH:mm') }}</div>
                      <div class="dataCopy">{{ getPerson(item1.from).name + ' ' + dateFormat(item1.time, 'yyyy-MM-dd HH:mm:ss') }}</div>
                    </div>
                    <div class="msg-content-box">
                      <div class="msg-content">
                        <!--文本-->
                        <div v-if="item1.type=='text'" class="msg-text" v-html="buildEmoji(strToHtml(item1.text,''))"></div>
                        <!--图片-->
                        <img v-else-if="item1.type=='image'&&item1.file" class="msg-img" :width="type!=3?item1.file.showW:''" :height="type!=3?item1.file.showH:''"
                             :src="item1.file.url" :data-src="item1.file.url" :data-key="''+item1.time+key1" :data-ext="item1.file.ext" :data-size="item1.file.size"
                             @error="errorImg.bind(this)" @click="clickImg($event,item1)" @dblclick="toViewer($event)">
                        <!--语音-->
                        <div v-else-if="item1.type=='audio'&&item1.file" class="msg-audio" :class="{'play':isPlayAudio(item1)}" @click="playAudio(item1,key1)"
                             @contextmenu.stop="setMenu($event, item1, msgItem)">
                          <div class="audio-box" :style="'width:'+(70+Math.round((item1.file.dur) / 1000)*2)+'px'">
                            <i class="audio-icon"></i>
                            <span class="audio-dur notCopy selNone">{{ secToTime(item1.file.dur || 0) }}</span>
                          </div>
                          <div class="audio-text-box" :class="{'no-text':!item1.audioText}" v-if="item1.audioTextStatus">
                            <img v-if="item1.audioTextStatus==1" class="audio-loading" src="/img/index/msg/audio_loading.png" alt="">
                            <span v-else>{{ item1.audioText ? item1.audioText : "未识别到文字。" }}</span>
                          </div>
                        </div>
                        <!--多种消息-->
                        <div v-else-if="item1.type=='custom'" class="msg-custom">
                          <div v-if="item1.content" class="msg-content-type">
                            <div v-if="item1.content.type=='multi'&&item1.content.msgs" class="msg-multi-all-box">
                              <!--多种消息遍历-->
                              <span class="msg-multi-box" v-for="(item2,key2) in item1.content.msgs" :key="key1">
                                <!--文本-->
                                <span v-if="item2.type=='text'" class="msg-text" v-html="buildEmoji(strToHtml(item2.text,'',item2))"></span>
                                <!--图片-->
                                <img v-else-if="item2.type=='image'&&item2.file" class="msg-img" :width="type!=3?item2.file.showW:''" :height="type!=3?item2.file.showH:''"
                                     :src="item2.file.url" :data-src="item2.file.url" :data-key="''+item1.time+key1+key2" :data-ext="item2.file.ext" :data-size="item2.file.size"
                                     @error="errorImg.bind(this)" @click="clickImg($event, item2)" @dblclick="toViewer($event)">
                              </span>
                            </div>
                          </div>
                        </div>

                      </div>
                      <div class="msg-audio-point" v-if="type==3&&item1.type=='audio'&&!item1?.file?.isRead"></div>
                    </div>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </div>
    </span>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import {
  avatarError, fangError, dateFormat, deepClone, setSubSerHtml, userWearPic, hideElm, strToHtml, secToTime, toViewerMethod, clickImg, errorImg, showMenu, dealMsgCenterContent,
} from "@utils";
import {toast, loading, alert} from "@comp/ui";

export default {
  name: "MsgReport",
  props: {
    type: {
      type: Number,
      default: 1,// 1公众号展示2普通会话展示3霸屏转发
    },
    // 消息对象
    item: {
      type: Object,
      default: {},
    },
  },
  setup(props, ctx) {
    const store = useStore();
    let msgItem = ref({});
    let reportRef = ref();
    // 语音转文字对象
    let audioMap = {};

    initMsg();

    function initMsg() {
      let thisItem = deepClone(props.item);
      if (thisItem?.content?.data?.content) {
        let list = thisItem.content.data.content;
        for (let i = 0; i < list.length; i++) {
          let msgItem = list[i];
          msgItem.idServer = "" + Date.now() + i;
          // 保留转语音
          if (audioMap[msgItem.idServer]) {
            msgItem.audioTextStatus = audioMap[msgItem.idServer].audioTextStatus;
            msgItem.audioText = audioMap[msgItem.idServer].audioText;
          }
          // 消息平台
          if (msgItem.content?.data?.content && (msgItem.content.type == "msg-center" || msgItem.content.type == "msg-report" || msgItem.content.type == "msg-center-link")) {
            msgItem.content.data.content = dealMsgCenterContent(msgItem);
          }
        }
      }
      msgItem.value = thisItem;
    }

    // 跳转消息平台
    function toMsgCenterLink(item) {
      store.dispatch("toMsgCenterLink", {item: item, isTips: true});
    }

    // 获取用户信息
    function getPerson(account) {
      return remote.store.getters.getPersons(account);
    }

    // 播放音频
    function playAudio(item, key) {
      let audioObjIdServer = remote.store.getters.getAudioObj.idServer;
      if (audioObjIdServer) {
        // 停止播放
        remote.store.dispatch("setAudioObj", {});
        if (audioObjIdServer == item.idServer) {
          return;
        }
      }
      nextTick(() => {
        let audioParam = {
          index: key,
          idServer: item.idServer,
          src: item.file.url,
          dur: item.file.dur,
          id: props.item.id,
          isPlay: true,
        };
        remote.store.dispatch("setAudioObj", audioParam);
        item.file.isRead = true;
      });
    }

    // 是否当前音频在播放
    function isPlayAudio(item) {
      if (item.idServer == remote.store.getters.getAudioObj.idServer) {
        return true;
      }
      return false;
    }

    // 打开查看大图/视频
    function toViewer(e) {
      toViewerMethod(e, "msg-img", reportRef.value);
    }

    // 右键操作
    function setMenu(e, item, parentItem) {
      let menuObj = {
        menu: {},// 菜单对象
        menuList: [],// 菜单列表
      }
      // 转文字
      if (item.type == "audio") {
        menuObj.menuList.push({
          label: "转文字", click: function () {
            // 获取语音转文字
            item.file.isRead = true;
            item.audioTextStatus = 1;
            store.dispatch("setSpeechToText", {
              url: item.file.url.split("?")[0] + "?audioTrans&type=mp3",
              idServer: parentItem.idServer + item.time,
              done: res => {
                item.audioTextStatus = 2;
                item.audioTextStatus = 2;
                if (res.success) {
                  item.audioText = res.resultText;
                } else {
                  toast({title: "转文字失败。" + res.errorMsg, type: 2});
                }
                store.commit("setEmit", {type: "scroll", value: "msg"});
                audioMap[item.idServer] = item;
              }
            });
          }
        });
      }
      if (menuObj.menuList.length > 0) {
        menuObj.menu = showMenu(menuObj.menuList);
        menuObj.popupItem = menuObj.menu.popup(e.x, e.y);
      }
      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    return {
      avatarError,
      fangError,
      dateFormat,
      setSubSerHtml,
      userWearPic,
      hideElm,
      strToHtml,
      secToTime,
      errorImg,
      clickImg,
      buildEmoji,

      msgItem,
      reportRef,

      toMsgCenterLink,
      getPerson,
      playAudio,
      isPlayAudio,
      toViewer,
      setMenu,
    }
  }
}
</script>
<style scoped lang="scss">
.msg-report {
  height: 100%;

  .msg-report-type {
    height: 100%;
    display: flex !important;

    .user-avatar-box {
      .avatar-box {
        cursor: default !important;
      }
    }

    &.msg-report-type-1 {
      .msg-report-box {
        width: 72%;
        max-width: 660px;
      }

      .msg-report-content {
        &:after {
          content: "";
          width: 0;
          height: 0;
          position: absolute;
          top: 12px;
          border-width: 6px;
          border-style: solid;
          left: -12px;
          border-color: transparent #FFFFFF transparent transparent;
        }
      }
    }

    &.msg-report-type3 {
      .msg-report-box {
        width: 100%;

        .msg-img {
          max-height: 300px;
        }
      }
    }

    .msg-report-box {
      width: 456px;
      height: 100%;
      max-width: 100%;
      flex-shrink: 0;
    }

    .msg-report-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      background: #EBEBEB;
      border-radius: 4px;
      position: relative;

      .msg-report-title {
        display: flex;
        align-items: center;
        padding: 0 20px;
        word-break: break-all;
        height: 58px;
        background: url("/img/index/msg/bg_report.png") no-repeat right center rgba(224, 50, 54);
        background-size: 456px 58px;
        border-radius: 4px 4px 0px 0px;
        flex-shrink: 0;

        .user-avatar-box {
          margin-right: 16px !important;
        }

        .msg-report-text {
          font-weight: bold;
          font-size: 20px;
          line-height: 28px;
          color: #FFFFFF;
        }
      }

      .msg-report-content-box {
        min-height: 0;
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;

        .msg-info {
          display: flex;
          padding: 10px 20px;
          position: relative;

          .user-avatar-box {
            margin-right: 10px !important;
          }

          .msg-box {
            min-width: 0;
            flex: 1;

            .user-name-box {
              color: #9B9B9B;
              line-height: 12px;
              margin-bottom: 5px;
            }

            .msg-content-box {
              width: 100%;
              display: flex;
              align-items: center;
              position: relative;

              .msg-content {
                background: #FFFFFF;
                border: 1px solid #EDEDED;
                display: inline-block;
                position: relative;
                word-break: break-all;
                min-width: 50px;
                min-height: 34px;
                line-height: 20px;
                border-radius: 4px;
                padding: 7px 10px;

                &:after {
                  content: "";
                  width: 0;
                  height: 0;
                  position: absolute;
                  top: 12px;
                  border-width: 6px;
                  border-style: solid;
                  left: -12px;
                  border-color: transparent #FFFFFF transparent transparent;
                }

                .msg-text {
                  max-width: 100%;
                  overflow: hidden;
                  cursor: default;
                  font-size: 14px;
                  white-space: pre-wrap;
                  word-wrap: break-word;
                }

                .msg-img {
                  min-width: 10px;
                  min-height: 10px;
                  max-width: 100%;
                  vertical-align: text-bottom;
                }

                .msg-audio {
                  &.play {
                    .audio-icon {
                      background-image: url("/img/index/msg/sell_icon_stop.png");
                    }
                  }

                  .audio-icon {
                    width: 16px;
                    height: 16px;
                    background-image: url("/img/index/msg/sell_icon_paly.png");
                    background-repeat: no-repeat;
                    background-size: 16px 16px;
                  }

                  .audio-box {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    max-width: 100% !important;

                    .audio-dur {
                      margin: 0 10px;
                    }
                  }

                  .audio-text-box {
                    position: relative;
                    width: 100%;
                    margin-top: 10px;
                    padding-top: 10px;
                    display: flex;
                    word-break: break-all;

                    &.no-text {
                      color: #7F7F7F;
                      justify-content: center;
                    }

                    .audio-loading {
                      width: 14px;
                      height: 14px;
                      animation: myLoading 800ms linear infinite;
                    }

                    &:after {
                      content: "";
                      position: absolute;
                      top: 0;
                      left: 0;
                      width: 100%;
                      height: 1px;
                      background: rgba(0, 0, 0, .13);
                    }
                  }
                }

              }

              .msg-audio-point {
                width: 6px;
                height: 6px;
                background: #f74b32;
                border-radius: 50%;
                display: inline-block;
                flex-shrink: 0;
                margin-left: 10px;
              }
            }
          }
        }
      }

      .msg-report-intr {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 8px;
        white-space: pre-wrap;
        word-break: break-all;
        text-align: justify;
      }

      .msg-report-img-box {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 8px;

        img {
          max-width: 100%;
        }
      }

      .msg-report-btn-box {
        display: flex;
        background: #FFFFFF;
        margin: 0 10px;

        .msg-report-btn {
          flex: 1;
          height: 30px;
          line-height: 28px;
          text-align: center;
          border-radius: 2px;
          border: 1px solid $styleColor;
          margin-right: 10px;
          color: $styleColor;
          font-size: 14px;
          cursor: pointer;

          &:hover {
            background: #FFF3F3;
          }

          &:last-child {
            margin-right: 0px;
          }
        }
      }

      .msg-report-remind-box {
        border-top: 1px solid #EEEEEE;
        height: 37px;
        line-height: 36px;
        padding: 0 10px;

        .msg-report-remind-intr {
          color: #666666;
        }

        .msg-report-remind-btn {
          margin-left: 10px;
          cursor: pointer;

          &:hover {
            color: $styleColor;
          }
        }
      }
    }
  }
}
</style>