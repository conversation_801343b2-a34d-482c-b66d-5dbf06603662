.dash-text {
  color: #999999;
  margin-left: 8px;
  font-size: 12px;
}

.content-line {
  max-width: 361px;
  overflow: hidden;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tab-box {
  display: inline-flex;

  > span {
    color: #999;
    font-size: 13px;
    cursor: pointer;
    white-space: nowrap;
    position: relative;

    & + span {
      margin-left: 15px;
    }

    &:hover {
      color: $styleColor;
    }
  }

  .active {
    color: $styleColor;
    font-weight: bold;

    &:after {
      content: "";
      width: 35%;
      height: 3px;
      background: $styleColor;
      position: absolute;
      left: 50%;
      bottom: -12px;
      transform: translateX(-50%);
    }
  }
}

.no-data {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  > img {
    height: 120px;
    width: 180px;
    margin-bottom: 10px;
  }

  > span {
    color: rgba(102, 102, 102, 1);
    font-weight: 400;
    color: #666666;
    line-height: 17px;
  }
}

.content-box {
  display: flex;
  height: 100%;
  flex-direction: column;
  background: white;

  .font-bold {
    font-weight: bold;
    color: #333333;
  }

  .header {
    padding: 11px 16px;
    font-size: 16px;
    line-height: 22px;
    width: 100%;
    border-bottom: 1px solid #e0e0e0;
  }

  .tag-box {
    flex: 1;
    overflow: auto;
    background-color: white;
  }

  .tag-item {
    position: relative;
    font-size: 15px;
    cursor: pointer;

    &.active i {
      transform: rotate(90deg);
    }

    &.active .title {
      font-weight: bold;
      color: rgba(0, 0, 0, 1);
    }

    &.active .count-text {
      font-weight: bold;
      color: rgba(0, 0, 0, 1) !important;
    }

    > .tag-title {
      display: flex;
      align-items: center;
      padding: 0px 16px 0px 0px;
      height: 36px;
      line-height: 36px;
      width: 100%;

      &:hover {
        background: $styleBg1Hover;
      }

      .icon-box {
        height: 100%;
        display: flex;
        align-items: center;
        padding-left: 13px;
        margin-right: 10px;

        i {
          display: block;
          width: 16px;
          height: 16px;
          background: url(/img/mail/crow_right.png) no-repeat;
          background-size: 16px 16px;
          transition: all 0.2s;
        }
      }


      .count-text {
        line-height: 17px;
      }
    }

    .check-box {
      width: 12px;
      height: 100%;
      align-items: center;
      font-size: 0px;
      border-radius: 2px;
      display: flex;
      box-sizing: content-box;
      padding-right: 10px;

      > input[type="checkbox"] {
        flex-shrink: 0;
        height: 14px;
        width: 14px;
      }
    }

    > .team-box {
      user-select: text;

      li {
        padding: 0px 39px;
        display: flex;
        position: relative;
        align-items: center;
        height: 40px;
        line-height: 40px;

        &:hover {
          background: $styleBg1Hover;
        }

        &:hover .chart-btn {
          display: block;
        }

        &:hover .copy-text {
          display: inline-block;
        }

        .copy-text {
          display: none;
          color: #999999;
          margin-left: 8px;
          font-size: 12px;

          &:hover {
            color: $styleColor;
          }
        }

        .chart-btn {
          display: none;
          position: absolute;
          right: 16px;
          padding: 5px 12px 4px 12px;
          border-radius: 4px;
          overflow: hidden;
          color: $styleColor;
          line-height: 17px;
          font-size: 12px;
          border: 1px solid $styleColor;
          cursor: pointer;
        }

        .dash-text {
          color: #999999;
          line-height: 17px;
        }

        .img {
          width: 24px;
          height: 24px;
          background: linear-gradient(138deg, #10c95e 0%, #02b753 100%);
          border-radius: 12px;
          margin-right: 8px;
        }
      }
    }
  }
}
