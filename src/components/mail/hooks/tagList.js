import {computed, reactive, ref} from 'vue';
import {searchUsersApi, addTagApi, addTagPowerApi} from '@utils/net/api.js';
import {getAvatar, deepClone, getFcwInfo} from '@utils';
import {toast, alert} from '@comp/ui';
import UUID from '@utils/uuid.js';

//客户标签,同事标签共用
function useTag(store, labelType) {

    const labelTypeText = labelType === 'Customer' ? '客户标签' : '同事标签';
    const isForward = ref(false); //是否有权限添加
    const mailList = ref([]); //通讯录列表.
    const userInfo = store?.getters?.getUserInfo; //用户信息
    usePower();

    // 获取权限
    async function usePower() {
        let res = await addTagPowerApi({
            msgBody: JSON.stringify({workerId: userInfo.workerId}),
        });
        if (res.data && res.data.data) isForward.value = true;
    }

    getUsers();

    // 获取权限
    async function getUsers() {
        mailList.value = store.getters.getMailList;
    }

    //通讯录标签
    let mailTags = store.getters.getGroupSetting;
    //联系人数据
    const baseList = computed(() => {
        return mailList.value.map((i) => i.list).flat(Infinity);
    });
    //基础的客户数据
    const customerList = computed(() => {
        let list = store.getters.getSessions({sort: 5})["5"] || [];
        list = list.filter((i) => {
            if (i.scene == "p2p") {return true}
        }).map((i) => {
            return {headPic: i.avatar, name: i?.detailInfo?.name || i.name, code: i.to, workerNo: i.workerNo, detailInfo: i.detailInfo};
        });
        return list;
    });
    const selectList = computed(() => {
            let list = []
            if (labelType === 'Colleague') {
                list = baseList.value
            } else {
                list = customerList.value
            }
            return list
        }
    )
    //同事标签
    const colleagueTag = computed(() => mailTags.filter((i) => i.type === 3 && i.uuid != -1));
    //客户标签
    const customerTag = computed(() => mailTags.filter((i) => i.type === 4));

    //渲染数据源
    const list = computed(() => {
        let arr = customerTag.value;
        if (labelType === 'Colleague') arr = colleagueTag.value;
        return arr;
    });

    // 弹窗相关
    const tagDialogConfig = reactive({
        name: '',
        visible: false,
        type: '',
        title: '',
        uuid: '',
        target: {}, //当前操作的标签
    });
    // 创建标签
    const createTag = () => {
        tagDialogConfig.type = 'create';
        tagDialogConfig.name = '';
        tagDialogConfig.title = '创建' + labelTypeText;
        tagDialogConfig.visible = true;
    };
    // 编辑标签
    const editTag = (item) => {
        tagDialogConfig.type = 'edit';
        tagDialogConfig.title = '编辑' + labelTypeText;
        tagDialogConfig.visible = true;
        tagDialogConfig.target = item;
        tagDialogConfig.name = item.name;
    };
    // 删除标签
    const delTag = (item) => {
        alert({
            content: `确认删除标签？`,
            done: async (type) => {
                if (type === 1) {
                    let params = deepClone(item);
                    params['remark'] = '删除标签名';
                    params['operation'] = 'delete';
                    let res = await doApi(params);
                    if (res && res.success) {
                    } else {
                        toast({title: res.errorMsg || '系统错误', type: 2});
                    }
                }
            },
        });
    };
    // 标签弹窗确认
    const tagDialogConfirm = async () => {
        if (tagDialogConfig.name.trim().length <= 0) {
            return toast({title: '请输入标签名称', type: 2});
        }
        // 编辑重命名
        if (tagDialogConfig.type == 'edit') {
            let params = deepClone(tagDialogConfig.target);
            params.name = tagDialogConfig.name.trim();
            params['remark'] = '修改标签名';
            params['operation'] = 'update';
            let res = await doApi(params);
            if (res && res.success) {
                tagDialogConfig.visible = false;
            } else {
                toast({title: res.errorMsg || '系统错误', type: 2});
            }
        } else {
            tagDialogConfig.visible = false;
            setPersonVisible(true, tagDialogConfig.type);
        }
    };
    // 选择人员弹窗设置
    const personDialogConfig = reactive({
        visible: false,
        title: '选择人员',
        type: '',
    });
    const transformConfig = reactive({
        defaultProps: {
            hasSearch: true,
            maxLength: true,
            maxThenToast: true,
            value: 'workerNo',
            label: 'name',
            headPic: 'headPic',
            func: doSearch,
        },
        supplyData: [],
        choiceData: [],
    });
    // 选择人员穿梭框设置
    const setPersonVisible = (visible, type) => {
        if (type === 'create') {
            transformConfig.choiceData = [];
            transformConfig.supplyData = selectList.value;
            initSameProps();
            personDialogConfig.title = '新增标签';
        } else if (type === 'insert') {
            transformConfig.supplyData = selectList.value;
            let choiceData = Object.values(detailConfig.memberList);
            //客户标签显示不同数据
            if (labelType === "Customer") {
                choiceData = detailConfig.memberList.map((item) => {
                    return {
                        workerNo: item.workerNo,
                        headPic: item.avatar,
                        name: item.name,
                        detailInfo: item.detailInfo,
                    }
                })
            }
            transformConfig.choiceData = choiceData
            initSameProps();
            personDialogConfig.title = '添加人员至' + labelTypeText;
        } else if (type === 'remove') {
            transformConfig.choiceData = [];
            let supplyData = Object.values(detailConfig.memberList);
            //客户标签显示不同数据
            if (labelType === "Customer") {
                supplyData = detailConfig.memberList.map((item) => {
                    return {
                        workerNo: item.workerNo,
                        headPic: item.avatar,
                        name: item.name,
                        detailInfo: item.detailInfo
                    }
                })
                transformConfig.defaultProps.hasSearch = false
            }
            transformConfig.supplyData = supplyData
            transformConfig.defaultProps.maxLength = false
            personDialogConfig.title = '删除人员';
        }
        personDialogConfig.visible = visible;
        personDialogConfig.type = type;
    };
    const initSameProps = () => {
        // 设置穿梭框组件

        //用户标签没有搜索
        transformConfig.defaultProps.hasSearch = labelType === 'Colleague'
        transformConfig.defaultProps.maxLength = 10;
        if(labelType == "Customer"){
          transformConfig.defaultProps.maxLength = 50;
        }
        if (isForward.value) {
            transformConfig.defaultProps.maxLength = 50;
        }
      transformConfig.defaultProps.maxThenToast = labelTypeText + '不允许大于'+transformConfig.defaultProps.maxLength+'人';
    };
    // 选择人员穿梭框选中回调
    const toChoice = (e) => {
        transformConfig.choiceData.push(e);
    };
    //选择人员穿梭框删除选中回调
    const toRemove = (index) => {
        transformConfig.choiceData.splice(index, 1);
    };

    //穿梭框搜索
    async function doSearch(e) {
        if (!e) {
            transformConfig.supplyData = selectList.value;
            return;
        }
        let params = {page: 1, rows: 50, name: e};
        let res = await searchUsersApi({
            msgBody: JSON.stringify(params),
        });
        if (res && res.success) {
            transformConfig.supplyData = res.data.empList.map((i) => {
                return {
                    headPic: getAvatar(i.headPic),
                    workerNo: i.workerNo,
                    name: `${i.workerName}（${i.deptName}）`,
                };
            });
        }
    };
    //请求参数配置
    const TYPECONFIG = {
        create: {
            dialogTitle: '创建标签',
            remark: '创建标签',
            operation: 'add',
        },
        insert: {
            dialogTitle: '添加标签成员',
            remark: '修改标签成员',
            operation: 'update',
        },
        remove: {
            dialogTitle: '删除标签成员',
            remark: '删除标签成员',
            operation: 'update',
        },
    };
    // 穿梭框弹窗回调
    const updatePersonConfirm = () => {
        let params = {};
        let type = personDialogConfig.type;
        if (type === 'create') {
            params = {
                name: tagDialogConfig.name,
                type: labelType === 'Customer' ? 4 : 3, //客户标签为4，同事为3
                uuid: UUID.generate().replace(/-/gi, ''),
                empNo: userInfo.workerNo,
                value: transformConfig.choiceData.map((i) => i.workerNo).join(','),
                remark: TYPECONFIG[type].remark,
                operation: TYPECONFIG[type].operation,
            };
        } else if (type === 'insert') {
            params = {
                name: detailConfig.selectTag.name,
                type: labelType === 'Customer' ? 4 : 3,
                uuid: detailConfig.selectTag.uuid,
                empNo: detailConfig.selectTag.empNo,
                value: transformConfig.choiceData.map((i) => i.workerNo).join(","),
                remark: TYPECONFIG[type].remark,
                operation: TYPECONFIG[type].operation,
            };
        } else if (type === 'remove') {
            let value_list = [];
            //去除调删除的标签
            let choice_id_list = transformConfig.choiceData.map((i) => i.workerNo);
            transformConfig.supplyData.forEach((i) => {
                if (choice_id_list.indexOf(i.workerNo) === -1) value_list.push(i.workerNo)
            });
            params = {
                name: detailConfig.selectTag.name,
                type: labelType === 'Customer' ? 4 : 3,
                uuid: detailConfig.selectTag.uuid,
                empNo: detailConfig.selectTag.empNo,
                value: value_list.join(","),
                remark: TYPECONFIG[type].remark,
                operation: TYPECONFIG[type].operation,
            };
        }
        if (transformConfig.choiceData.length === 0) {
            toast({title: '请至少选择一个成员', type: 2});
            return;
        }

        async function fn() {
            let res = await doApi(params);
            if (res && res.success) {
                if (personDialogConfig.type !== "create") {
                    clickTag(detailConfig.selectTag);
                } else {
                    detailConfig.memberList = transformConfig.choiceData
                }
                toast({title: personDialogConfig.type=='remove' ? '删除成功' : '添加成功', type: 1});
                personDialogConfig.visible = false;
            }
        }

        if (type === 'remove') {
            alert({
                content: `确认删除标签成员？`,
                done: (type) => {
                    if (type === 1) fn();
                },
            });
        } else fn();
    };
    // 拦截一次，多端通讯
    const doApi = async (params) => {
        let res = await addTagApi({
            msgBody: JSON.stringify({listJsons: [params]}),
        });
        params['upKey'] = 'tag';
        store.commit('updateGroupSetting', params); //更新store的分组
        store.getters.getNim.modifyMyInfo(params); //同步多端
        return res;
    };

    // 查看详情
    const detailConfig = reactive({
        selectTag: {},
        memberList: [],
    });
    // 点击标签
    const clickTag = async (item) => {
        if (labelType === "Colleague") {
            detailConfig.memberList = await store.dispatch('getPersons', {
                doneFlag: true,
                account: item.value ? item.value.split(',') : [],
            });
        } else if (labelType === "Customer") {
            let memberList = []
            let arr = (item.value ? item.value.split(",") : []);
            arr.forEach((val) => {
                let data = store.getters.getNimFriend(val)
                if (data[val]) {
                    memberList.push({
                        headPic: data[val].avatar,
                        avatar: data[val].avatar,
                        name: data[val].name,
                        code: data[val].account,
                        workerNo: data[val].workerNo,
                    })
                }
            });
            detailConfig.memberList = memberList
        }
        detailConfig.selectTag = item;
    };
    // 插入新成员
    const insertMember = () => {
        setPersonVisible(true, 'insert');
    };
    // 删除新成员
    const removeMember = () => {
        setPersonVisible(true, 'remove');
    };


    return {
        list,

        personDialogConfig,
        transformConfig,
        updatePersonConfirm,
        toChoice,
        toRemove,

        editTag,
        delTag,
        createTag,
        clickTag,
        tagDialogConfirm,
        tagDialogConfig,

        detailConfig,
        insertMember,
        removeMember,
    }
}

export {useTag};
