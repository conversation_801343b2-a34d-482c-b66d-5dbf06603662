<template>
    <div>
        <Layout>
            <template v-slot:layout-left>
                <div class="nav-box">
                    <span class="title">通讯录</span>
                    <div class="content">
                        <Navigation></Navigation>
                    </div>
                </div>
            </template>
            <template v-slot:layout-right>
                <component :is="navigation.com"></component>
            </template>
        </Layout>
        <Loading :isShowFlag="isShowFlag" :left="60"></Loading>
    </div>
</template>
<script>
import { useStore } from 'vuex';
import {computed, provide, reactive, ref, toRefs, watch} from 'vue';

import Navigation from './Navlist';
import Contacts from './components/Contacts'; //联系人
import FindPersons from './components/FindPersons'; //找对人
import Colleague from './components/Colleague'; //同事标签
import Customer from './components/Customer'; //客户标签
import Discussion from './components/Discussion'; //讨论组
import Group from './components/Group'; //群
import Service from './components/Service'; //服务号
import Subscribe from './components/Subscribe'; //订阅号
import Layout from '../layout/index.vue';
import Loading from '@comp/ui/comps/Loading';

export default {
    components: { Layout, Navigation, Contacts, Colleague, Customer, Discussion, Group, Service, Subscribe, FindPersons, Loading },
    setup() {
        // 会话列表
        const store = useStore();
        const state = reactive({
            navigation: {
                text: '',
            },
        });
        provide('setNavigation', (e) => (state.navigation = e));
        provide(
            'navigation',
            computed(() => {
                return state.navigation;
            })
        );
        // 是否显示loading
        let isShowFlag = ref(false);
        watch(() => store.state.emit.mailLoading,
            (newValue, oldValue) => {
                isShowFlag.value = newValue;
            }, {
                deep: true
            }
        );

        return {
            ...toRefs(state),
            isShowFlag,
        };
    },
};
</script>
<style scoped lang="scss">
.nav-box {
  border-top-left-radius: 4px;
}
.nav-box,
.content-box {
    display: flex;
    height: 100%;
    flex-direction: column;
    background: white;

    .title {
        display: block;
        padding: 11px 16px;
        font-size: 16px;
        line-height: 22px;
        width: 100%;
        border-bottom: 1px solid #e0e0e0;
        font-weight: bold;
        color: #333333;
    }

    .content {
        flex: 1;
        overflow: auto;
    }
}
</style>
