<template>
  <ul class="nav-tree">
    <li class="nav-box" :class="{ active: selected.com === item.com }" v-for="item in navList" :key="item.com"
        @click="select(item)">
      <img class="icon" :src="item.url"/>
      <span>{{ item.text }}</span>
    </li>
  </ul>
</template>

<script setup>
import {ref, inject} from 'vue';

// 导航列表
const navList = ref([
  {
    com: 'Customer',
    url: '/img/mail/mail_customer.png',
    text: '客户标签',
  },
  {
    com: 'Colleague',
    url: '/img/mail/mail_colleague.png',
    text: '同事标签',
  },
  {
    com: 'FindPersons',
    url: '/img/mail/mail_findPersons.png',
    text: '找对人',
  },
  {
    com: 'Discussion',
    url: '/img/mail/mail_discussion.png',
    text: '讨论组/群',
  },
  // {
  //   com: 'Contacts',
  //   url: '/img/mail/mail_contacts.png',
  //   text: '联系人',
  // },
  {
    com: 'Service',
    url: '/img/mail/mail_service.png',
    text: '服务号',
  },
  {
    com: 'Subscribe',
    url: '/img/mail/mail_subscribe.png',
    text: '订阅号',
  },
]);
// 选中的菜单
const selected = ref({});
const setNavigation = inject('setNavigation');
// 点击菜单
const select = (item) => {
  selected.value = item;
  setNavigation(item);
};
select(navList.value[3]);
</script>

<style scoped lang="scss">
.nav-tree {
  height: 100%;
  width: 100%;
  overflow: auto;

  li.nav-box {
    width: 100%;
    display: flex;
    padding: 10px 16px;
    background: white;
    line-height: 20px;
    cursor: pointer;
    min-height: 40px;
    font-size: 15px;

    .icon {
      height: 20px;
      width: 20px;
      border-radius: 50%;
    }

    span {
      margin-left: 10px;
    }

    &:hover {
      background: $styleBg1Hover;
    }

    &.active {
      font-weight: bold;
      color: #333333;
      background: #dcdcdc;
    }
  }
}
</style>
