<template>
  <div class="content-box">
    <div class="flex-bet flex-vcenter header">
      <span class="font-bold">{{ navigation.text }}</span>
      <div class="flex flex-center">
        <span class="dash-label">选择：</span>
        <div class="draw-box">
          <div class="select-input" :class="{active:showDraw}" @click.stop="toggleShowDraw()">
            <div class="input flex-vcenter">
              {{ keyWords }}
            </div>
            <i class="draw-icon" :class="{active:showDraw}"></i>
          </div>
          <!--搜索公司输入和搜索框-->
          <div class="search-user-ul-box" v-show="showDraw" @click.stop="stopPropagation">
            <div class="search-user-input-box">
              <input type="text" v-model="searchObj.text" maxlength="50" placeholder="请输入关键字搜索" @input="searchComp">
            </div>
            <ul class="search-user-ul" v-show="searchObj.list.length>0">
              <li :class="['textEls',item.none?'none':'']" v-for="(item,key) in searchObj.list" :key="item.compayId" :title="!item.none?item.compayName:''" @click="changeOption(item)">{{ item.compayName }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <ul class="content">
      <!-- 树状数据 -->
      <template v-if="deptList.length">
        <li class="dept-item" :class="{ active: item.active }" v-for="(item, index) in deptList" :key="index">
          <div class="action-box" @click="item.active=!item.active">
            <i></i>
            <span :class="{'font-bold':item.active}">{{ item.deptName }}</span>
          </div>
          <ul class="person-box" v-if="item.active">
            <li class="preson-item flex-vcenter" @click="toChat(spawVo)" v-for="(spawVo, index) in item.spawVoList"
                :key="index">
              <div class="img-box">
                <img class="avatar" :src="spawVo.headPic"
                     :onerror="avatarError.bind(this, 'p2p', spawVo.workerNo, '')"/>
              </div>
              <div class="info-box flex-vcenter">
                <span>{{ spawVo.nickname }}</span><span>({{ spawVo.obligation }})</span>
              </div>
            </li>
          </ul>
        </li>
      </template>
      <template v-else>
        <div class="no-data">
          <img src="/img/mail/no_data_1.png" alt="">
          <span>暂未找到相关人员</span>
        </div>
      </template>
    </ul>
  </div>
</template>
<script>
//联系人页面
import {getAvatar, avatarError, regReplace} from '@utils';
import {useStore} from 'vuex';
import {ref, inject, computed, watch} from 'vue';

export default {
  setup(props) {
    const store = useStore();
    //联系人列表
    const navigation = inject('navigation');
    //   所有公司数据
    const allInformation = store.getters.getFindPersons().information;

    //搜索
    const keyWords = ref(allInformation[0].compayName);
    // 下拉选择
    const drownList = computed(() => {
          return allInformation
        }
    );
    const selected = ref(allInformation[0])
    // 人员信息
    const deptList = computed(() => {
      return selected.value.groupList
    })

    // 搜索公司对象
    let searchObj = ref({
      text: "",
      list: [],
    });


    let showDraw = ref(false)
    watch(() => showDraw.value, (newVal) => {
      if (newVal) {
        document.addEventListener('click', () => showDraw.value = false)
      } else {
        document.removeEventListener('click', () => {
        })
      }
    })

    //前往聊天
    function toChat(item) {
      store.dispatch('setCurrentSession', {id: 'p2p-' + item.workerNo, type: 'open'});
    }

    function changeOption(e) {
      keyWords.value = e.compayName
      selected.value = e
      toggleShowDraw(false);
    }

    // 切换显示搜索公司
    function toggleShowDraw(flag) {
      if (flag != undefined) {
        showDraw.value = flag;
      } else {
        showDraw.value = !showDraw.value;
      }
      if (showDraw.value) {
        searchObj.value.list = drownList.value;
      }
    }

    // 搜索公司
    function searchComp() {
      let valReg = new RegExp(regReplace(searchObj.value.text), "i");
      let list = drownList.value.filter(item => {
        return valReg.test(item?.compayName || "");
      });
      if (list.length == 0) {
        list = [{compayName: "暂无搜索结果", none: true}];
      }
      searchObj.value.list = list;
    }

    function stopPropagation() {}

    return {
      navigation,

      showDraw,
      selected,
      changeOption,
      drownList,
      deptList,
      keyWords,
      searchObj,

      avatarError,
      getAvatar,
      toChat,
      toggleShowDraw,
      searchComp,
      stopPropagation,
    };
  },
};
</script>
<style lang="scss" scoped>

.no-data {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  > img {
    height: 120px;
    width: 180px;
    margin-bottom: 10px;
  }

  > span {
    color: rgba(102, 102, 102, 1);
    font-weight: 400;
    color: #666666;
    line-height: 17px;
  }
}

.dash-label {
  font-size: 12px;
  font-weight: 400;
  color: #666666;
  line-height: 17px;
}

.draw-box {
  width: 186px;
  height: 28px;
  position: relative;
  z-index: 18;

  .select-input {
    position: relative;
    cursor: pointer;

    &.active .input {
      border: 1px solid #333333 !important;
    }

    &:hover .input {
      border: 1px solid #333333 !important;
    }

    > .input {
      width: 186px;
      height: 28px;
      background: #FFFFFF;
      border-radius: 4px;
      font-size: 12px;
      padding: 5px 10px 6px 10px;
      border: 1px solid rgba(224, 224, 224, 1);
    }
  }

  .draw-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%) rotate(180deg);
    width: 12px;
    height: 12px;
    cursor: pointer;
    background: url(/img/mail/up_dash.png) no-repeat;
    background-size: 12px 12px;
    transition: all 0.2s;

    &.active {
      transform: translateY(-50%) rotate(0deg);
    }
  }

  .select-draw {
    height: 248px;
    overflow: auto;
    margin-top: 4px;
    padding: 4px 0px;
    background: white;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
    border-radius: 4px;
    border: 1px solid #E0E0E0;

    li {
      padding: 4px 20px 6px 10px;
      font-weight: 400;
      color: #000000;
      font-size: 12px;
      line-height: 17px;
      background: white;
      cursor: pointer;
      position: relative;

      .select-icon {
        position: absolute;
        display: block;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        width: 14px;
        height: 14px;
        cursor: pointer;
        background: url(/img/mail/selected_primary.png) no-repeat;
        background-size: 14px 14px;
      }

      &.active {
        color: $styleColor;
      }

      &:hover {
        background: $styleBg1Hover;
      }
    }
  }


  .search-user-ul-box {
    position: absolute;
    top: 32px;
    left: 0;
    width: 186px;
    background: #FFFFFF;
    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
    border-radius: 4px;
    border: 1px solid #E0E0E0;
    z-index: 1;

    .search-user-input-box {
      position: relative;
      display: flex;
      width: 100%;
      height: 30px;
      line-height: 30px;
      padding: 0 10px;
      background: url("/img/schedule/search.png") no-repeat right 10px top 8px;
      background-size: 18px 18px;

      input {
        width: calc(100% - 18px);
      }
    }

    .search-user-ul {
      max-height: 242px;
      overflow-y: auto;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        top: 0;
        left: 10px;
        width: calc(100% - 20px);
        height: 1px;
        background: #E0E0E0;
      }

      li {
        height: 30px;
        line-height: 28px;
        padding: 0 10px;
        font-size: 12px;

        &.none {
          text-align: center;
        }

        &:hover:not(.none) {
          background: #F5F5F5;
        }
      }
    }
  }
}

.content-box {
  display: flex;
  height: 100%;
  flex-direction: column;
  background: white;

  .font-bold {
    font-weight: bold;
    color: #333333;
  }

  .header {
    padding: 8px 16px;
    font-size: 16px;
    line-height: 22px;
    width: 100%;
    border-bottom: 1px solid #e0e0e0;
  }

  .content {
    overflow: auto;
    height: 100%;

    .dept-item {
      position: relative;

      &.active i {
        transform: rotate(90deg);
      }

      > .action-box {
        cursor: pointer;

        &:hover {
          background: $styleBg1Hover;
        }

        i {
          position: absolute;
          left: 14px;
          top: 10px;
          width: 16px;
          height: 16px;
          background: url(/img/mail/crow_right.png) no-repeat;
          background-size: 16px 16px;
          transition: all 0.2s;
        }

        > span {
          display: block;
          height: 36px;
          font-size: 15px;
          box-sizing: border-box;
          padding: 8px 16px 8px 36px;
        }
      }
    }

    .person-box {
      overflow: hidden;
    }

    .preson-item {
      font-size: 15px;
      padding: 0px 16px 0px 36px;
      height: 40px;
      cursor: pointer;

      &:hover {
        background: $styleBg1Hover;
      }

      .img-box {
        height: 24px;
        width: 24px;
        border-radius: 50%;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        .avatar {
          width: 24px;
        }
      }

      .info-box {
        display: flex;
        align-items: center;
        padding-left: 10px;
        justify-content: center;
        height: 40px;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 15px;

        > span:last-child {
          color: #999999;
          font-size: 12px;
          margin-left: 8px;
        }
      }
    }
  }
}
</style>
