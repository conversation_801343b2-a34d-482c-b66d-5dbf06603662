<template>
  <div class="content-box">
    <div class="title">
      {{ navigation.text }}
    </div>
    <ul class="content">
      <!-- 整个通讯录大类 -->
      <li class="contacts-tree">
        <template v-for="item in contactsList" :key="item.name">
          <span class="tag">{{ item.name }}</span>
          <!-- 二维数组 -->
          <div class="contact-item" @dblclick="toChat(worker)" v-for="worker in item.list"
               :key="worker.workerNo">
            <div class="img-box">
              <img class="avatar" :src="getAvatar(worker.headPic)"
                   :onerror="avatarError.bind(this, 'p2p', worker.workerNo, '')"/>
<!--              <img class="user-label" :src="worker.detailInfo.medalUrl" :onerror="hideElm"/>-->
            </div>
            <div class="info-box">
              <span>{{ worker.name }}</span>
              <span>{{ worker.dutyName }}</span>
            </div>
          </div>
        </template>
      </li>
    </ul>
  </div>
</template>
<script>
//联系人页面
import {getAvatar, avatarError, hideElm} from '@utils';
import {useStore} from 'vuex';
import {ref, inject} from 'vue';

export default {
  setup(props) {
    const store = useStore();
    //联系人列表
    const contactsList = ref([]);
    const navigation = inject('navigation');

    async function getUsers() {
      contactsList.value = store.getters.getMailList;
    }

    getUsers();

    //前往聊天
    function toChat(item) {
      store.dispatch('setCurrentSession', {
        id: 'p2p-' + item.workerNo,
        type: 'open'
      });
    }

    return {
      navigation,
      contactsList,
      avatarError,
      getAvatar,
      toChat,
      hideElm,
    };
  },
};
</script>
<style lang="scss" scoped>
.content-box {
  display: flex;
  height: 100%;
  flex-direction: column;
  background: white;

  .title {
    display: block;
    padding: 11px 16px;
    font-size: 16px;
    line-height: 22px;
    font-weight: bold;
    color: #333333;
    width: 100%;
    border-bottom: 1px solid #e0e0e0;
  }

  .content {
    overflow: auto;
    height: 100%;

    .contacts-tree {
      .tag {
        font-size: 12px;
        color: #333333;
        font-weight: bold;
        line-height: 28px;
        padding-left: 13px;
        margin: 10px 0px;
      }

      .contact-item {
        font-size: 14px;
        padding: 0px 16px;
        box-sizing: content-box;
        height: 40px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          background: $styleBg1Hover;
        }

        .img-box {
          height: 24px;
          width: 24px;
          border-radius: 50%;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;

          .avatar {
            width: 24px;
          }
        }

        .info-box {
          display: flex;
          align-items: center;
          padding-left: 10px;
          font-size: 15px;
          justify-content: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 15px;

          > span:last-child {
            color: #999999;
            font-size: 12px;
            margin-left: 10px;
          }
        }
      }
    }
  }
}
</style>
