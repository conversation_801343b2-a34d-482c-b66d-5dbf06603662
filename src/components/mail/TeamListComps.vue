<template>
  <ul class="team-ul" ref="teamUlRef">
    <li :class="['team-li',searchStatus==2?'list':'']" v-for="(item, index) in showTItem.tList" :key="item.teamId" @dblclick="openChat(item)" @contextmenu.stop="setItemMenu($event, 2, tItem, item)">
      <div class="team-li-box" v-if="index>=teamObj.start&&index<=teamObj.end">
        <div class="sel-box" @click="selItem(3,tItem,item)" @dblclick.stop="stopPropagation()">
          <span :class="['sel-box-i',selGroupMap[tItem.uuid+'-'+item.teamId]?'sel':'']"></span>
        </div>
        <div class="user-avatar-box">
          <div class="avatar-box">
            <img class="avatar" :src="item.avatar" :onerror="avatarError.bind(this, 'team', item.teamId , item)">
          </div>
        </div>
        <div class="team-li-info-box">
          <div class="team-li-info-name-box">
            <span class="team-li-info-name textEls" :title="item.name">{{ item.name }}</span>
            <span :class="['team-li-info-label','textEls',item?.serverCustom?.status==2?'status1':'']" v-if="item?.serverCustom?.typeName"
                  :title="item?.serverCustom?.typeName+(item?.serverCustom?.typeStatus?'('+item?.serverCustom?.typeStatus+')':'')" @click="changeTeamType(item.teamId, item)">
            {{ item?.serverCustom?.typeName + (item?.serverCustom?.typeStatus ? '(' + item?.serverCustom?.typeStatus + ')' : '') }}
          </span>
          </div>
          <div class="team-li-intr">
            <div class="team-li-intr-info">
              <span>创建：{{ dateFormat(item.createTime, "yy/MM/dd HH:mm") }}</span>
              <span class="line"></span>
              <span class="textEls">{{ item.userShowName || item.name }}</span>
            </div>
            <div class="team-li-intr-copy">
              <span class="textEls">群号：{{ item.teamId }}</span>
              <span class="copy-btn" @click="copyText(item.teamId)">复制</span>
            </div>
          </div>
        </div>
        <div class="search-btn btn-reset" @click="openChat(item)">聊天</div>
      </div>
    </li>
  </ul>
</template>
<script>
import {onMounted, ref, watch} from "vue";
import {useStore} from "vuex";
import {toast} from '@comp/ui'
import {dateFormat, avatarError, getParents, getOffset, deepClone, setUserBaseInfo} from "@utils";

export default {
  name: "teamListComps",
  props: {
    // 搜索状态1查询未加入业务讨论组、2有搜索条件
    searchStatus: {
      type: Number,
      default: -1,
    },
    // 群列表分组
    tItem: {
      type: Object,
      default: {}
    },
    // 选中的群对象
    selGroupMap: {
      type: Object,
      default: {}
    },
    // 选择对象
    selItem: {
      type: Function,
      default: function () {}
    },
    // 右键菜单
    setMenu: {
      type: Function,
      default: function () {}
    },
    // 打开会话
    openChat: {
      type: Function,
      default: function () {}
    },
    // 修改讨论组状态
    changeTeamType: {
      type: Function,
      default: function () {}
    }
  },
  setup(props, ctx) {
    const store = useStore();
    let teamUlRef = ref();
    let teamObj = ref({
      scrollElm: "",// 父元素滚动元素
      start: 0,
      end: 50,
    });
    let showTItem = ref(deepClone(props.tItem));
    setTItem();

    onMounted(() => {
      teamObj.value.scrollElm = getParents(teamUlRef.value, "team-list-box");
    });

    watch(() => props.tItem,
      (newValue, oldValue) => {
        showTItem.value = deepClone(newValue);
        setTItem();
      }, {
        deep: true
      }
    )
    // 滚动监听
    watch(() => store.state.emit.teamListCompScroll,
      (newValue, oldValue) => {
        let scrollElm = teamObj.value.scrollElm;
        if (scrollElm && teamUlRef.value) {
          let currentTop = getOffset(teamUlRef.value).top - getOffset(scrollElm).top;
          // 计算显示的li开始和结束下标
          if (currentTop <= scrollElm.scrollTop && currentTop + teamUlRef.value.clientHeight + scrollElm.clientHeight >= scrollElm.scrollTop) {
            let liHeight = teamUlRef.value.querySelector(".team-li").clientHeight;
            let start = Math.floor((scrollElm.scrollTop - currentTop) / liHeight) - 20;
            let end = Math.floor((scrollElm.scrollTop + scrollElm.clientHeight - currentTop) / liHeight) + 20;
            teamObj.value.start = Math.max(start, 0);
            teamObj.value.end = Math.min(end, showTItem.value.tList.length);
          } else {
            teamObj.value.start = 0;
            teamObj.value.end = 50;
          }
        }
      }, {
        deep: true
      }
    );

    // 设置群对象
    function setTItem() {
      for (let i = 0; i < showTItem.value.tList.length; i++) {
        let item = showTItem.value.tList[i];
        item.userShowName = setUserBaseInfo(getPerson(item.owner)).userShowName;
      }
    }

    // 设置菜单
    function setItemMenu($event, type, tItem, item) {
      if (!props.tItem.notSetMenu) {
        props.setMenu($event, type, tItem, item);
      } else {
        $event.preventDefault();
        $event.stopPropagation();
        return false;
      }
    }

    // 获取用户信息
    function getPerson(account) {
      return store.getters.getPersons(account);
    }

    // 复制文字
    function copyText(teamId) {
      remote.Clipboard.get().set(teamId);
      let teamInfo = store.getters.getTeams({id: teamId});
      toast({title: `${teamInfo.detailType == "group" ? "讨论组" : "群"}编号复制成功`, type: 1});
    }

    // 阻止点击穿透
    function stopPropagation() {}

    return {
      showTItem,
      teamUlRef,
      teamObj,

      setItemMenu,
      copyText,
      getPerson,
      stopPropagation,

      dateFormat,
      avatarError,
    }
  }
}
</script>
<style scoped lang="scss">
.team-ul {

  .sel-box-i {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    position: relative;
    border: 1px solid #BCBCBC;
    border-radius: 2px;

    &.sel {
      border: 1px solid $styleColor;
      background: $styleColor;

      &:after {
        content: "";
        width: 8px;
        height: 3px;
        border: 2px solid #FFFFFF;
        border-top: transparent;
        border-right: transparent;
        position: absolute;
        top: 2px;
        left: 1px;
        transform: rotate(-45deg);
      }
    }
  }

  .search-btn {
    line-height: 16px;
    padding: 6px 11px;
    border-radius: 4px;
    background: $styleColor;
    color: #FFFFFF;
    border: 1px solid $styleColor;
    margin: 0 0 6px 12px;
    cursor: pointer;

    &.btn-reset {
      background: transparent;
      color: $styleColor;
      border: 1px solid $styleColor;
      margin: 0 0 6px 6px;
    }
  }

  .team-li {
    display: flex;
    align-items: center;
    height: 56px;
    padding-right: 16px;
    cursor: pointer;

    &.list {
      padding-left: 16px;

      .sel-box {
        display: none;
      }

      .team-li-info-box {
        width: calc(100% - 46px - 64px) !important;
      }
    }

    &:hover {
      background: $styleBg1Hover;

      .team-li-info-box {
        width: calc(100% - 62px - 46px - 64px);

        .team-li-intr {
          .team-li-intr-info {
            width: 63.5%;
          }

          .team-li-intr-copy {
            display: flex;
            justify-content: flex-end;
            width: 36.5%;
            padding-left: 24px;
          }
        }
      }

      .search-btn {
        display: block;
      }
    }

    .team-li-box {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .sel-box {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 62px;
      height: 100%;
      padding-right: 8px;
      flex-shrink: 0;
    }

    .user-avatar-box {
      margin-right: 8px;
    }

    .team-li-info-box {
      display: flex;
      flex-direction: column;
      width: calc(100% - 22px - 46px);

      .team-li-info-name-box {
        display: flex;
        align-items: center;
        margin-bottom: 2px;

        .team-li-info-name {
          max-width: 70%;
          font-size: 15px;
          color: #000000;
          margin-right: 10px;
        }

        .team-li-info-label {
          max-width: 30%;
          position: relative;
          font-size: 11px;
          line-height: 14px;
          color: $styleColor;
          padding: 0 13px 0 3px;
          background: #FFF5F5;
          border-radius: 2px;
          border: 1px solid #FFA4A7;

          &.status1 {
            background: $styleBg1;
            border: 1px solid #DEDEDE;
            color: #666666;

            &:before {
              border-color: transparent transparent transparent $styleBg1;
            }

            &:after {
              border-color: transparent transparent transparent #666666;
            }
          }

          &:before {
            content: "";
            position: absolute;
            top: 50%;
            right: 5px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-width: 4px 0 4px 4px;
            border-style: solid;
            border-color: transparent transparent transparent #FFF5F5;
            z-index: 1;
          }

          &:after {
            content: "";
            position: absolute;
            top: 50%;
            right: 4px;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-width: 4px 0 4px 4px;
            border-style: solid;
            border-color: transparent transparent transparent $styleColor;
          }
        }
      }

      .team-li-intr {
        display: flex;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        line-height: 17px;
        color: #999999;

        .line {
          width: 1px;
          height: 10px;
          background: #E0E0E0;
          margin: 0 8px;
        }

        .team-li-intr-info {
          display: flex;
          align-items: center;
          width: 100%;

          span {
            flex-shrink: 0;
          }

          .textEls {
            width: calc(100% - 121px - 16px);
          }
        }

        .team-li-intr-copy {
          display: none;
          flex-shrink: 0;

          .textEls {
            width: calc(100% - 24px - 8px);
            text-align: right;
          }

          .copy-btn {
            margin-left: 8px;

            &:hover {
              color: $styleColor;
            }
          }
        }
      }
    }

    .search-btn {
      display: none;
      margin: 0 0 0px 16px;
    }
  }
}
</style>