<template>
  <div class="team-box" ref="teamListRef">
    <div v-show="level==1" :class="['comp-chat-list',searchObj.searchStatus==2?'list':'']">
      <div class="team-header">
        <ul class="title-ul">
          <li :class="['title-li',groupObj.tabType==1?'sel':'']" @click="toggleTabType(1)">全部</li>
          <li :class="['title-li',groupObj.tabType==2?'sel':'']" @click="toggleTabType(2)">分组</li>
        </ul>
        <div class="btn-box">
          <span v-show="groupObj.tabType==2" class="btn" @click="updateClassify({type:7})">分组排序</span>
          <span class="btn" @click="addGroup">创建讨论组</span>
          <span class="btn" @click="updateClassify({type:1})">创建分组</span>
        </div>
      </div>
      <div class="team-content">
        <div v-show="groupObj.tabType==1" class="search-content-box">
          <div class="search-content-label-box">
            <div class="search-intr">选择:</div>
            <div class="search-sel-box">
              <!--搜索人-->
              <div :class="['search-box','search-user-box',searchObj.type==1?'show':'',searchObj.userInfo.workerNo?'is-user':'']" @click.stop="showType(1)">
                <span class="text textEls">{{ searchObj.userInfo.userShowName || "请输入创建人" }}</span>
                <span class="search-arrow"></span>
                <span class="search-close" @click.stop="selUser({},true)"></span>
                <!--搜索人输入和搜索框-->
                <div class="search-user-ul-box" v-show="searchObj.type==1" @click.stop="stopPropagation">
                  <div class="search-user-input-box">
                    <input type="text" v-model="searchObj.userInfoObj.text" maxlength="50" placeholder="请输入关键字搜索" @input="searchUser">
                  </div>
                  <ul class="search-user-ul" v-show="searchObj.userInfoObj.list.length>0">
                    <li :class="['textEls',item.none?'none':'']" v-for="(item,key) in searchObj.userInfoObj.list" :key="item.workerNo" :title="!item.none?item.userShowName:''" @click="selUser(item)">{{ item.userShowName }}</li>
                  </ul>
                </div>
              </div>
              <!--查询人范围-->
              <div :class="['search-box','search-area-box',searchObj.type==2?'show':'']" @click.stop="showType(2)">
                <span class="textEls">{{ searchObj.areaList[searchObj.areaIndex].label }}</span>
                <span class="search-arrow"></span>
                <!--单选框-->
                <ul class="single-sel-box" v-show="searchObj.type==2" @click.stop="stopPropagation">
                  <li :class="[searchObj.areaIndex==key?'sel':'']" v-for="(item,key) in searchObj.areaList" :key="key" @click="selSingleBox(2,key)">{{ item.label }}</li>
                </ul>
              </div>
              <!--查询类型-->
              <div :class="['search-box','search-type-box',searchObj.type==3?'show':'']" @click.stop="showType(3)" @mouseenter="hoverType(3)" @mouseleave="hoverType(-1)">
                <span class="textEls" v-show="searchObj.typeAll">全部类型</span>
                <div class="type-label-box textEls" v-show="!searchObj.typeAll">
                <span class="type-label" v-show="item.sel" v-for="(item,key) in searchObj.typeList" :key="key">
                  <span>{{ item.label }}</span>
                  <span class="close" @click.stop="selMultiBox(1,key)"></span>
                </span>
                </div>
                <span class="search-arrow"></span>
                <!--多选框-->
                <ul class="multi-sel-box" v-show="searchObj.type==3" @click.stop="stopPropagation" @mouseenter="hoverType(-1)">
                  <li @click="selMultiBox(1,-1)">全部类型</li>
                  <li :class="[item.sel?'sel':'']" v-for="(item,key) in searchObj.typeList" :key="key" @click="selMultiBox(1,key)">
                    <span :class="['sel-box-i',item.sel?'sel':'']"></span>
                    <span>{{ item.label }}</span>
                  </li>
                </ul>
                <!--悬浮框-->
                <div class="multi-hover-modal" v-show="!searchObj.typeAll&&searchObj.typeHover==3" @click.stop="stopPropagation" @mouseenter="hoverType(3)" @mouseleave="hoverType(-1)">
                  <ul class="multi-hover-box">
                    <li v-for="(item,key) in searchObj.typeList" :key="key" v-show="item.sel">
                      <span>{{ item.label }}</span>
                      <span class="close" @click="selMultiBox(1,key)"></span>
                    </li>
                  </ul>
                </div>
              </div>
              <!--搜索群类型-->
              <div :class="['search-box','search-status-box',searchObj.type==4?'show':'']" @click.stop="showType(4)">
                <span class="textEls">{{ searchObj.statusList[searchObj.statusIndex].label }}</span>
                <span class="search-arrow"></span>
                <!--单选框-->
                <ul class="single-sel-box" v-show="searchObj.type==4" @click.stop="stopPropagation">
                  <li :class="[searchObj.statusIndex==key?'sel':'']" v-for="(item,key) in searchObj.statusList" :key="key" @click="selSingleBox(4,key)">{{ item.label }}</li>
                </ul>
              </div>
              <!--选择创建开始-结束时间-->
              <div :class="['search-box','search-time-box',searchObj.timeObj.startTime?'sel':'',searchObj.type==5?'show':'']" @click.stop="showType(5)">
                <span class="calendar-text">{{ searchObj.timeObj.startTime ? dateFormat(searchObj.timeObj.startTime, "yy/MM/dd") + " ~ " + dateFormat(searchObj.timeObj.endTime, "yy/MM/dd") : "请选择创建时间" }}</span>
                <span class="search-calendar"></span>
                <span class="search-close" @click.stop="setCurrentDay({start:'',end:''})"></span>
                <!--日历-->
                <div class="calendar-box" v-show="searchObj.type==5">
                  <CalendarRange ref="calendarRef" :setCurrentDay="setCurrentDay"></CalendarRange>
                </div>
              </div>
              <!--选择聊天范围-->
              <div class="search-box search-range-box">
                <span>超过</span>
                <input type="type" placeholder="请输入" maxlength="3" v-model="searchObj.timeRange" @input="inputTimeRange">
                <span>天未聊天</span>
              </div>
              <!--关键词搜索-->
              <input class="search-box search-input-box" type="text" placeholder="请输入关键词搜索" maxlength="50" v-model.trim="searchObj.text" @keyup.enter="searchGroup()">
              <div class="search-btn" @click="searchGroup()">查询</div>
              <div class="search-btn btn-reset" @click="searchGroup(-1)">重置</div>
              <div class="search-btn btn-reset" @click="showQuitQrCode()">自动退群设置</div>
            </div>
          </div>
          <div class="search-content-label-box">
            <div class="search-intr">标签:</div>
            <div class="search-sel-box">
              <span :class="['search-sel-label',searchObj.isInGroup==4?'sel':'']" @click="toggleSearchLabel(4)">我加入的讨论组</span>
              <span :class="['search-sel-label',searchObj.isInGroup==5?'sel':'']" @click="toggleSearchLabel(5)">我加入的群</span>
              <span :class="['search-sel-label',searchObj.isInGroup==3?'sel':'']" @click="toggleSearchLabel(3)">未分组的讨论组</span>
              <span :class="['search-sel-label',searchObj.isInGroup==2?'sel':'']" @click="toggleSearchLabel(2)">未加入的业务讨论组</span>
            </div>
          </div>
        </div>
        <!--没有查询条件展示分组-->
        <div class="team-list-box" @contextmenu="setMenu($event,1)" @scroll="scrollLi">
          <!--分组列表-->
          <ul :class="['team-classify-ul','team-classify-ul-'+groupObj.tabType]" v-for="(item, index) in groupObj.list" :key="index">
            <!--分组信息-->
            <li v-show="groupObj.tabType==2" class="team-classify-li" @contextmenu.stop="setMenu($event,1,item)" @click="selItem(1,item)">
              <span :class="['sel-arrow',groupObj.showMap[item.id]?'sel':'']"></span>
              <span :class="['sel-box-i',multiObj.selClassifyMap[item.id]?'sel':'']" @click.stop="selItem(2, item)"></span>
              <!--分组信息-->
              <div class="team-classify-info">
                <span class="team-classify-info-title">{{ item.name }}</span>
                <span class="team-classify-info-num">（{{ item.tList.length }}）</span>
              </div>
            </li>
            <!--讨论组列表-->
            <TeamListComps v-if="groupObj.showMap[item.id]||groupObj.tabType==1"
                           :searchStatus="searchObj.searchStatus" :tItem="item" :selGroupMap="multiObj.selGroupMap"
                           :selItem="selItem" :setMenu="setMenu" :openChat="openChat" :changeTeamType="changeTeamType"></TeamListComps>
          </ul>
          <!--没有列表-->
          <div v-show="!searchObj.searchFlag&&((groupObj.tabType==1&&(groupObj.list?.[0]?.tList?.length==0||groupObj.list.length==0))||groupObj.tabType==2&&groupObj.list.length==0)"
               class="team-list-none">
            <img class="none-img" src="/img/index/list_none.png" alt="">
            <div class="none-text">{{ groupObj.tabType == 1 ? "暂无讨论组/群" + (searchObj.searchStatus == -1 ? "可点击下方按钮快速创建" : "") : "暂无分组，可点击下方按钮快速创建" }}</div>
            <div v-show="searchObj.searchStatus==-1" class="none-btn" @click="groupObj.tabType==1?addGroup():updateClassify({type:1})">{{ groupObj.tabType == 1 ? "新建讨论组" : "新建分组" }}</div>
          </div>
        </div>
      </div>
      <div class="team-footer">
        <div class="sel-num-box">
          <div class="sel-num-info">
            <span>已选择</span>
            <span class="num">{{ multiObj.num }}</span>
            <span>个讨论组/群</span>
          </div>
        </div>
        <div class="sel-btn-box">
          <button class="default-btn" @click="selMulti(0)">{{ multiObj.selAll ? '取消全选' : '全选' }}</button>
          <button v-if="teamType==2" class="default-btn" @click="selMulti(1)">解散</button>
          <button v-if="teamType==2" class="default-btn" @click="selMulti(2)">退出</button>
          <button v-if="teamType==2" class="default-btn" @click="selMulti(3)">改状态</button>
          <button class="default-btn" @click="selMulti(4)">
            <span>添加至</span>
            <div class="btn-question-box">
              <i class="icon-question"></i>
              <span :class="['btn-tips',groupObj.tabType==1?'btn-tips-right':'']" @click.stop="stopPropagation">将选中的讨论组同步到目标分组中，被选中的讨论组原分组不受影响</span>
            </div>
          </button>
          <button v-show="groupObj.tabType==2" :class="['default-btn',(multiObj.selDefault&&!multiObj.selOther)||searchObj.searchStatus!=-1?'disabled':'']" @click="selMulti(5)">
            <span>移动至</span>
            <div class="btn-question-box">
              <i class="icon-question"></i>
              <span class="btn-tips" @click.stop="stopPropagation">将选中的讨论组移动到目标分组中，被选中的讨论组原分组不再存在选中讨论组</span>
            </div>
          </button>
          <button v-show="groupObj.tabType==2" :class="['default-btn',(multiObj.selDefault&&!multiObj.selOther)||searchObj.searchStatus!=-1?'disabled':'']" @click="selMulti(6)">移出分组</button>
        </div>
      </div>
    </div>

    <!--聊天内容-->
    <ChatContent v-if="level!=1" class="comp-chat-content" sessionType="1" :backMethods="back"></ChatContent>

    <!--移动至分组弹窗-->
    <LyDialog newClass="dialog-manger-box dialog-move-box" :title="dialogObj.type==1?'添加至':'移动至'" :width="396" :height="400" :closeOnClickModal="true"
              :visible="dialogObj.type==1||dialogObj.type==2" @close="dialogOperate(1)" @confirm="dialogOperate(2)">
      <div class="main-dialog-box">
        <div class="move-content-box">
          <div class="classify-tips">群助手、客户咨询、售后系统中的讨论组，将无法在分组中收发信息</div>
          <div class="add-classify-box">
            <span class="add-classify" @click="updateClassify({type:1})">新建分组</span>
          </div>
          <ul class="move-content-ul">
            <li v-for="(item,key) in groupObj.classifyList" :key="key" @click="dialogObj.moveSelItem=item" v-show="item.uuid!='default'">
              <span :class="['sel-box',String(dialogObj.moveSelItem.id)==String(item.id)?'sel':'']"></span>
              <span class="textEls">{{ item.name }}</span>
            </li>
          </ul>
        </div>
      </div>
    </LyDialog>

    <!--自动退群设置-->
    <LyDialog newClass="dialog-qrcode-box" title="自动退群设置" :width="300" :height="252" :closeOnClickModal="true" :foot="false"
              :visible="dialogObj.type==3" @close="dialogOperate(1)">
      <div class="main-dialog-box">
        <div class="qrcode-box">
          <img :src="dialogObj.qrcodeSrc" alt="">
          <div>用手机乐办公扫码，进入设置页面</div>
        </div>
      </div>
    </LyDialog>

    <!-- 切换讨论组类型 -->
    <TeamEditor ref="teamEditorRef" :callback="teamEditorDone"></TeamEditor>
  </div>
</template>

<script>
import {ref, watch, nextTick} from "vue";
import {useStore} from "vuex";
import {searchUsersApi, searchGroupFrontListApi, initSeachDateApi, leaveTeamApi, dismissGroupApi} from "@utils/net/api";
import {deepClone, isDisabledGroup, showMenu, dateFormat, avatarError, debounce, trimArray, throttle, createQrCode, setUserBaseInfo} from "@utils";
import ChatContent from "@comp/chat/ChatContent";
import {alert, loading, toast} from "@comp/ui";
import LyDialog from "@comp/ui/comps/LyDialog";
import CalendarRange from "@comp/ui/comps/CalendarRange";
import TeamEditor from "@comp/ui/comps/TeamEditor";
import TeamListComps from "@comp/mail/TeamListComps";

export default {
  name: "Team",
  props: {
    // 群类型1群2讨论组
    teamType: {
      type: String,
      default: "2",
    }
  },
  components: {ChatContent, LyDialog, CalendarRange, TeamEditor, TeamListComps},
  setup(props, ctx) {
    const store = useStore();
    let userInfo = store.getters.getUserInfo;
    let calendarRef = ref();
    let teamListRef = ref();
    let teamEditorRef = ref();
    // 搜索下拉信息
    let groupSearchInfo = ref({});
    // 搜索对象
    let searchObj = ref({
      userInfo: {},// 查询的人
      areaList: [{label: "查此人", value: "1"}, {label: "直接下属", value: "3"}, {label: "所有下属", value: "4"}, {label: "本人及直接下属", value: "2"}, {label: "管控范围", value: "6"}, {label: "查此部门", value: "5"}],// 查询范围
      areaIndex: 0,
      typeList: [{label: "普通", type: "0"}],// 群类型
      typeAll: true,
      statusList: [{label: "全部状态", value: ""}, {label: "进行中", value: "1"}, {label: "已结束", value: "2"}],// 群状态
      statusIndex: 0,
      timeObj: {
        startTime: "",
        endTime: "",
      },// 开始时间和结束时间
      timeRange: "",// 聊天时间范围
      text: "",// 搜索关键词
      isInGroup: 1,// 1默认、2未加入的业务讨论组、3未分组讨论组、4我加入的讨论组、5我加入的群
      type: -1,// 显示类型1搜索人、2人范围、3类型、4状态、5时间
      typeHover: -1,// 悬浮类型
      userInfoObj: {
        text: "",// 查询人关键词
        list: [],// 查询人列表
      },
      searchStatus: -1,// 搜索状态1查询未加入业务讨论组、2有搜索条件
      searchFlag: false,// 搜索状态
      page: 1,// 查询页数
      size: 20,// 查询条数
      hasMore: true,// 是否还有更多
    });
    // 讨论组对象
    let groupObj = ref({
      tabType: 1,// 1全部2分钟
      list: [],// 分组和群列表
      classifyList: [],// 分组列表
      showMap: {},// 展开列表
    });
    // 批量选择对象
    let multiObj = ref({
      type: -1,// 操作类型
      num: 0,// 操作数量
      selClassifyMap: {},// 选中的分组列表
      selGroupMap: {},// 选中的讨论组列表
      selAll: false,// 是否选择全部
      selDefault: false,// 是否选择了全部讨论组
      selOther: false,// 是否选择了其他讨论组
    });
    // 弹窗对象
    let dialogObj = ref({
      type: -1,// 1添加至2移动至3自动退群设置
      classifyName: "",// 分组名
      moveSelItem: {},// 移动至选择的对象
      qrcodeSrc: "",// 自动退群二维码
    });
    // 层级1列表2聊天内容
    let level = ref(1);
    // 获取下拉列表
    initSeachDate();
    // 初始化讨论组列表
    initGroup(true);
    // 获取服务器最新分组
    store.dispatch("setGroupSetting");
    // 自动退群二维码
    if (store.state.emit.showQuitQrcode) {
      store.commit("setEmit", {type: "showQuitQrcode", value: ""});
      showQuitQrCode();
    }

    // 讨论组分组变动
    watch(() => store.state.groupSettings,
      (newValue, oldValue) => {
        initGroup();
      }, {
        deep: true
      }
    );

    // 讨论组列表变动
    watch(() => store.state.teams,
      (newValue, oldValue) => {
        initGroup();
      }, {
        deep: true
      }
    );

    // 监听全局点击
    watch(() => store.state.emit.allClick,
      (newValue, oldValue) => {
        showType(-1);
      }, {
        deep: true,
      }
    );

    // 重置搜索条件
    function resetGroupParam() {
      searchObj.value.userInfo = {};
      searchObj.value.areaIndex = 0;
      selMultiBox(1, -1);
      searchObj.value.statusIndex = 0;
      searchObj.value.timeObj.startTime = "";
      searchObj.value.timeObj.endTime = "";
      searchObj.value.timeRange = "";
      searchObj.value.text = "";
      searchObj.value.isInGroup = 1;
      searchObj.value.searchStatus = -1;
      searchObj.value.searchFlag = false;
    }

    // 初始化讨论组数据
    function initGroup() {
      let list = [];
      // 获取服务器分组配置
      let groupSettings = store.getters.getClassifyList({type: 2});
      // 按照创建时间排序
      let teams = store.getters.getTeams({type: 6}) || [];
      teams = teams.sort((a, b) => {return b.createTime - a.createTime});
      let classifyList = [];
      for (let i = 0; i < groupSettings.length; i++) {
        let item = groupSettings[i];
        item.tList = item.tList || [];
        if (item.value) {
          // 讨论组分组存在群号获取讨论组信息
          let groupList = item.value.split(",");
          for (let j = 0; j < groupList.length; j++) {
            let teamIndex = teams.findIndex(tItem => {return tItem.teamId == groupList[j]});
            // 存在讨论组
            if (teamIndex > -1) {
              item.tList.push(teams[teamIndex]);
            }
          }
        }
        item.tList = item.tList.sort((a, b) => {return b.createTime - a.createTime});
        list.push(item);
        classifyList.push({id: item.id, name: item.name, uuid: item.uuid, sort: item.sort, type: item.type});
      }
      // 全部tab下面默认显示全部讨论组列表
      if (groupObj.value.tabType == 1) {
        list = [{id: "none", uuid: "none", tList: teams}];
      }
      // 不是搜索状态更新数据
      if (searchObj.value.searchStatus == -1) {
        groupObj.value.list = list;
        updateClassifyStatus();
      }
      groupObj.value.classifyList = classifyList;
    }

    // 获取下拉列表
    async function initSeachDate(tipsFlag) {
      setSearchInfo();
      let res = await initSeachDateApi();
      if (!res.success) {
        if (tipsFlag) {
          toast({title: res.errorMsg || "系统错误", type: 2});
        }
        return;
      }
      store.commit("setGroupSearchInfo", res.data);
      setSearchInfo();
    }

    // 设置下拉列表
    function setSearchInfo() {
      groupSearchInfo.value = store.getters.getGroupSearchInfo;
      if (!groupSearchInfo.value.typeList) {
        return;
      }
      searchObj.value.areaList = groupSearchInfo.value.personTag;
      searchObj.value.typeList = groupSearchInfo.value.typeList;
      searchObj.value.statusList = [{label: "全部状态", value: ""}].concat(groupSearchInfo.value.statusList);
    }

    // 左键选择 type 1展开分组2全选分组3选中讨论组
    async function selItem(type, item, item1, isSel) {
      switch (type) {
        case 1:
          // 展开分组
          toggleStatus(groupObj.value.showMap, item.id);
          break;
        case 2:
          // 切换选中分组状态
          if (isSel == null) {
            toggleStatus(multiObj.value.selClassifyMap, item.id);
          } else {
            // 改为选择/取消选择状态
            if (isSel) {
              multiObj.value.selClassifyMap[item.id] = item.id;
            } else {
              delete multiObj.value.selClassifyMap[item.id];
            }
          }
          // 选中当前分组下的全部讨论组
          item.tList.map(tItem => {
            if (multiObj.value.selClassifyMap[item.id]) {
              selItem(3, item, tItem, true);
            } else {
              selItem(3, item, tItem, false);
            }
          });
          if (isSel == null) {
            updateClassifyStatus();
          }
          break;
        case 3:
          // 选中讨论组
          let selKey = `${item.uuid}-${item1.teamId}`;
          if (isSel == null) {
            toggleStatus(multiObj.value.selGroupMap, selKey, {...item1, uuid: item.uuid});
          } else {
            // 改为选择/取消选择状态
            if (isSel) {
              multiObj.value.selGroupMap[selKey] = {...item1, uuid: item.uuid};
            } else {
              delete multiObj.value.selGroupMap[selKey];
            }
          }
          if (isSel == null) {
            updateClassifyStatus();
          }
          break;
      }
    }

    // 多选操作 type 0全选/取消全选1解散2退出3改状态4添加至5移动至6移出分组
    function selMulti(type) {
      multiObj.value.type = type;
      // 5移动至6移出分组 选中全部讨论组分组内容/搜索状态不支持操作
      if ((type == 5 || type == 6) && ((multiObj.value.selDefault && !multiObj.value.selOther) || searchObj.value.searchStatus != -1)) {
        return;
      }
      if (type != 0 && multiObj.value.num == 0) {
        toast({title: `请选择讨论组/群`, type: 2});
        return;
      }
      switch (type) {
        case 0:
          // 取消全选
          if (!multiObj.value.selAll) {
            multiObj.value.selClassifyMap = {};
            multiObj.value.selGroupMap = {};
          }
          // 全选/取消全选
          groupObj.value.list.map(item => {
            // 全选/取消全选分组
            if (multiObj.value.selAll) {
              selItem(2, item, "", false);
            } else {
              selItem(2, item, "", true);
            }
          });
          // 全选
          if (multiObj.value.selAll) {
            multiObj.value.selClassifyMap = {};
            multiObj.value.selGroupMap = {};
          }
          updateClassifyStatus();
          break;
        case 1:
          // 批量解散
          alert({
            content: `只能解散自己创建的讨论组，确定解散？`,
            done: async type => {
              if (type == 1) {
                multiOperate(multiObj.value.type);
              }
            }
          });
          break;
        case 2:
          // 批量退出
          alert({
            content: "只能退出不是自己创建的讨论组/群，确定退出？",
            done: async type => {
              if (type == 1) {
                multiOperate(multiObj.value.type);
              }
            }
          });
          break;
        case 3:
          // 批量改状态
          let groupTypeFlag = true;
          let selGroupType = "";
          let selTeamIdList = [];
          let selTeamItemList = [];
          // 遍历选中讨论组是否同一类型
          for (let key in multiObj.value.selGroupMap) {
            let currentGroupType = multiObj.value.selGroupMap[key]?.serverCustom?.groupType;
            if (!currentGroupType) {
              continue;
            }
            if (!selGroupType) {
              selGroupType = currentGroupType;
            }
            if (selGroupType != currentGroupType) {
              groupTypeFlag = false;
              alert({content: "不同类型的讨论组/群，不能批量修改状态，请检查", showCancel: false, okText: "我知道了"});
              break;
            }
            selTeamIdList.push(multiObj.value.selGroupMap[key].teamId);
            selTeamItemList.push(multiObj.value.selGroupMap[key]);
          }
          if (!selGroupType) {
            groupTypeFlag = false;
            alert({content: "该讨论组/群无群状态，不允许操作", showCancel: false, okText: "我知道了"});
          }
          if (groupTypeFlag) {
            if (selTeamIdList.length == 1) {
              teamEditorRef.value.initTeamInfo(selTeamIdList.toString(), selTeamItemList[0]);
            } else {
              teamEditorRef.value.initTeamInfo(selTeamIdList.toString());
            }
          }
          break;
        case 4:
          // 批量添加至
          dialogObj.value.type = 1;
          break;
        case 5:
          // 批量移动至
          if (multiObj.value.selDefault && multiObj.value.selOther) {
            alert({
              content: `全部讨论组下的讨论组无法移动至，将为您过滤这部分选择`,
              done: type => {
                if (type == 1) {
                  dialogObj.value.type = 2;
                }
              }
            })
          } else {
            dialogObj.value.type = 2;
          }
          break;
        case 6:
          // 批量移出分组
          alert({
            title: "移出提示",
            content: multiObj.value.selDefault && multiObj.value.selOther ? `全部讨论组下的讨论组无法移出，将为您过滤这部分选择` : `所选讨论组/群将会被移出分组`,
            done: async type => {
              if (type == 1) {
                let uuidMap = {};
                let valueList = [];
                for (let key in multiObj.value.selGroupMap) {
                  if (/default-/.test(key)) {
                    // 去除全部讨论组移动至/移出至数据
                    continue;
                  }
                  let item = multiObj.value.selGroupMap[key];
                  uuidMap[item.uuid] = item.uuid;
                  valueList.push({uuid: item.uuid, value: item.teamId});
                }
                let res = await updateClassify({type: 6, uuid: Object.keys(uuidMap).toString(), value: valueList});
                if (res.success) {
                  multiObj.value.selGroupMap = {};
                  dialogObj.value.moveSelItem = {};
                  // 更新选中状态
                  updateClassifyStatus(true);
                }
              }
            }
          });
          break;
      }
    }

    // 右键菜单 type 1分组2讨论组
    function setMenu(e, type, item, item1) {
      let menuList = [];
      // 查询未加入的业务讨论组不允许操作
      switch (type) {
        case 1:
          // 分组列表
          menuList.push({
            label: "创建分组",
            click: function () {
              updateClassify({type: 1});
            }
          });
          if (item?.uuid && item.uuid != "default") {
            menuList.push({
              label: "重命名分组",
              click: function () {
                updateClassify({type: 2, uuid: item.uuid, name: item.name});
              }
            });
            menuList.push({
              label: "删除分组",
              click: function () {
                updateClassify({type: 3, uuid: item.uuid, name: item.name});
              }
            });
          }
          break;
        case 2:
          // 讨论组-非多选且本地存在该讨论组/群
          let selTeamInfo = store.getters.getTeams({id: item1.teamId});
          if (multiObj.value.num == 0 && selTeamInfo.teamId) {
            let moveList = groupObj.value.classifyList.filter(listItem => {
                return (groupObj.value.tabType == 1 || (groupObj.value.tabType == 2 && item.id != listItem.id)) && listItem.uuid != "default"
              }
            );
            // 少于2个分组不显示
            if (moveList.length > 0) {
              menuList.push({
                label: "添加至",
                submenu: showMenu(moveList.map(menuItem => {
                  return {
                    label: menuItem.name, click: async function () {
                      updateClassify({type: 5, uuid: menuItem.uuid, name: menuItem.name, value: [{uuid: "", value: item1.teamId}]});
                    }
                  }
                }))
              });
            }
            if (groupObj.value.tabType == 2 && moveList.length > 0) {
              menuList.push({
                label: "移动至",
                submenu: showMenu(moveList.map(menuItem => {
                  return {
                    label: menuItem.name, click: async function () {
                      updateClassify({type: 4, uuid: menuItem.uuid, name: menuItem.name, value: [{uuid: item.uuid, value: item1.teamId}]});
                    }
                  }
                }))
              });
            }
            if (groupObj.value.tabType == 2) {
              menuList.push({
                label: "移出分组", click: function () {
                  updateClassify({type: 6, uuid: item.uuid, name: item.name, value: [{uuid: item.uuid, value: item1.teamId}]});
                }
              });
            }
            if (props.teamType == 2 && !isDisabledGroup(item1)) {
              // 进线客户讨论组不支持退出/解散
              if (item1.owner == userInfo.workerNo) {
                // 创建者为自己，且为讨论组可以解散
                if (selTeamInfo.detailType == "group") {
                  menuList.push({
                    label: "解散讨论组", click: function () {
                      store.dispatch("dismissTeam", {id: item1.teamId});
                    }
                  });
                }
              } else {
                menuList.push({
                  label: `退出该讨论组/群`, click: function () {
                    store.dispatch("leaveTeam", {id: item1.teamId});
                  }
                });
              }
            }
            break;
          }
      }
      if (menuList.length > 0) {
        showMenu(menuList).popup(e.x, e.y);
      }
      e.stopPropagation();
      e.preventDefault();
      return false;
    }

    // 选中状态和取消，key1为设置的值,不存在设置为key
    function toggleStatus(obj, key, key1) {
      if (obj[key]) {
        delete obj[key];
      } else {
        obj[key] = key1 ? key1 : key;
      }
    }

    // 计算多选讨论组数量
    function calcMultiNum() {
      multiObj.value.num = Object.keys(multiObj.value.selGroupMap).length;
    }

    // 点击打开会话
    function openChat(item) {
      let detailInfo = deepClone(item);
      if (!store.getters.getTeams({id: item.teamId}).teamId) {
        detailInfo.notInTeam = true;
      }
      level.value = 2;
      store.dispatch("setCurrentSession", {id: `team-${item.teamId}`, notInsert: true, detailInfo: detailInfo});
    }

    // 1创建分组2重命名分组3删除分组4移动至分组5添加至分组6移出至分组7分组排序
    async function updateClassify(info, notTips) {
      let showList = [];
      if (info.type == 7) {
        showList = deepClone(groupObj.value.classifyList);
        if (showList.length == 0) {
          toast({title: `暂无讨论组/群分组可排序`, type: 2});
          return;
        }
      }
      return new Promise(resolve => {
        store.commit("setEmit", {
          type: "initClassify", value: {
            classifyType: 2,
            type: info.type,
            classifyName: info.name,
            classifyUUID: info.uuid,
            classifyValue: info.value,
            classifySortList: showList,
            notTips: notTips,
            time: Date.now(),
            done: res => {
              resolve(res);
            }
          }
        });
      });
    }

    // 弹窗操作 key1关闭2确认
    async function dialogOperate(key) {
      if (key == 1) {
        dialogObj.value.type = -1
        return;
      }
      let uuid = dialogObj.value.moveSelItem.uuid;
      let valueList = [];
      for (let key in multiObj.value.selGroupMap) {
        if (dialogObj.value.type == 2 && /default-/.test(key)) {
          // 去除全部讨论组移动至/移出至数据
          continue;
        }
        let item = multiObj.value.selGroupMap[key];
        valueList.push({uuid: item.uuid, value: item.teamId});
      }
      let res = await updateClassify({type: dialogObj.value.type == 1 ? 5 : 4, uuid: uuid, value: valueList});
      if (res.success) {
        multiObj.value.selGroupMap = {};
        dialogObj.value.moveSelItem = {};
        // 更新选中状态
        updateClassifyStatus(true);
      }
      dialogObj.value.type = -1
    }

    // 更新所有分组的选中状态 flag为操作后清空分组勾选状态
    function updateClassifyStatus(flag) {
      // 判断所有分组是否被选中
      let isClassifyAll = groupObj.value.list.length > 0 ? true : false;
      // 是否选中全部讨论组
      let isSelDefault = false;
      // 是否选中其他讨论组
      let isSelOther = false;
      groupObj.value.list.map(item => {
        // 判断分组是否全部被选中
        let isAll = multiObj.value.selClassifyMap[item.id];
        for (let i = 0; i < item.tList.length; i++) {
          // 遍历判断是否选中该讨论组
          if (!multiObj.value.selGroupMap[`${item.uuid}-${item.tList[i].teamId}`]) {
            isAll = false;
          } else {
            // 判断是否有选中全部分组
            if (item.uuid == "default") {
              isSelDefault = true;
            } else {
              isSelOther = true;
            }
          }
        }
        if (isAll == null) {
          // 不存在分组选中，且讨论组列表存在
          if (item.tList.length > 0) {
            isAll = true;
          } else if (item.tList.length == 0) {
            // 不存在分组选中
            isClassifyAll = false;
          }
        }
        // 当前分组全部讨论组被选中则选中分组
        if (isAll) {
          multiObj.value.selClassifyMap[item.id] = item.id;
          // 操作完成出去所有为空的分组勾选状态
          if (flag && item.tList.length == 0) {
            delete multiObj.value.selClassifyMap[item.id];
            isClassifyAll = false;
          }
        } else {
          delete multiObj.value.selClassifyMap[item.id];
          // 群类型没有群列表不作为判断全选与否
          if (item.tList.length > 0) {
            isClassifyAll = false;
          }
        }
      });
      multiObj.value.selAll = isClassifyAll;
      multiObj.value.selDefault = isSelDefault;
      multiObj.value.selOther = isSelOther;
      calcMultiNum();
    }

    // 批量退出/解散 type1解散2退出
    async function multiOperate(type) {
      // 获取在讨论组列表
      let tList = store.getters.getTeams({ids: Object.values(multiObj.value.selGroupMap).map(tItem => {return tItem.teamId})});
      if (tList.length == 0) {
        alert({content: "您未进群", showCancel: false, okText: "我知道了"});
        return;
      }
      // 获取可解散/退出讨论组列表
      let quitList = tList.filter(item => {
        if (item) {
          switch (type) {
            case 1:
              // 可解散
              return item.owner == userInfo.workerNo && item.detailType == "group";
              break;
            case 2:
              // 可退出
              return item.owner != userInfo.workerNo;
              break;
          }
        }
      });
      if (quitList.length == 0) {
        alert({content: type == 1 ? "仅有讨论组创建者可解散" : type == 2 ? "创建者不能退讨论组/群" : "类型错误", showCancel: false, okText: "我知道了"});
        return;
      }
      dismissOrLeaveTeam(type, quitList);
    }

    // 退出/解散群操作
    async function dismissOrLeaveTeam(type, quitList) {
      let res;
      let tIdList = [];
      quitList.map(item => {
        tIdList.push(item.teamId);
      });
      store.commit("setEmit", {type: "mailLoading", value: true});
      switch (type) {
        case 1:
          // 批量解散讨论组
          res = await dismissGroupApi({
            msgBody: JSON.stringify({owner: userInfo.workerNo, tid: tIdList.toString()}),
            setTimeout: 40 * 1000
          });
          break;
        case 2:
          // 批量退出讨论组
          res = await leaveTeamApi({
            msgBody: JSON.stringify({empNo: userInfo.workerNo, tid: tIdList.toString(), teamType: "2"}),
            setTimeout: 40 * 1000
          });
          break;
      }
      store.commit("setEmit", {type: "mailLoading", value: false});
      if (!res.success) {
        alert({content: res.errorMsg || "操作失败请重试", showCancel: false, okText: "我知道了"});
      } else {
        if (res.data?.fail?.length > 0) {
          toast({title: res.errorMsg || `部分讨论组${type == 1 ? "解散" : "退出"}失败`});
        } else {
          if (type == 2) {
            multiObj.value.selGroupMap = {};
          }
          toast({title: res.errorMsg || `${type == 1 ? "解散" : "退出"}成功`});
        }
        if (res.data?.success?.length > 0) {
          // 遍历删除成功群列表
          res.data.success.map(item => {
            for (let key in multiObj.value.selGroupMap) {
              // 遍历删除选中的相关分组
              if (new RegExp(`-${item}$`).test(key)) {
                delete multiObj.value.selGroupMap[key];
              }
            }
            if (searchObj.value.searchStatus != -1) {
              // 搜索状态删除对应列表
              for (let i = 0; i < groupObj.value.list.length; i++) {
                let cItem = groupObj.value.list[i];
                for (let j = 0; j < cItem.tList.length; j++) {
                  let tItem = cItem.tList[j];
                  // 删除成功群列表
                  if (tItem.teamId == item) {
                    cItem.tList.splice(j, 1);
                    j--;
                  }
                }
              }
            }
          });
        }
        // 更新选中状态
        updateClassifyStatus(true);
        if (searchObj.value.searchStatus != -1 && groupObj.value.list[0].tList == 0) {
          searchGroup("", true);
        }
      }
    }

    // 显示类型
    function showType(type) {
      if (searchObj.value.type == type) {
        searchObj.value.type = -1;
      } else {
        searchObj.value.type = type;
      }
      switch (searchObj.value.type) {
        case 1:
          nextTick(() => {
            teamListRef.value.querySelector(".search-user-input-box input").focus();
          });
          break;
        case 2:
          break;
        case 3:
          // 选择讨论组类型
          if (!groupSearchInfo.value.typeList) {
            initSeachDate(true);
          }
          break;
        case 4:
          break;
        case 5:
          // 显示日历
          calendarRef.value.getCalendar({start: searchObj.value.timeObj.startTime, end: searchObj.value.timeObj.endTime});
          break;
      }
    }

    // 设置当前时间
    function setCurrentDay(res) {
      searchObj.value.timeObj.startTime = res.start;
      searchObj.value.timeObj.endTime = res.end;
      showType(-1);
    }

    // 设置超过xx天未聊天时间，只能输入数字
    function inputTimeRange() {
      let timeRange = searchObj.value.timeRange.replace(/[^\d]/g, "");
      if (timeRange > 365) {
        timeRange = 365;
      }
      searchObj.value.timeRange = timeRange;
    }

    // 搜索人员
    function searchUser() {
      debounce({
        timerName: "searchUser",
        time: 300,
        fnName: async () => {
          let res = await searchUsersApi({
            msgBody: JSON.stringify({
              workerId: userInfo.workerId,
              name: searchObj.value.userInfoObj.text,
              status: "1",//默认为全部1在职2为离职
              page: 1,
              rows: 50
            })
          });
          if (res?.data?.empList) {
            if (res.data.empList.length == 0) {
              searchObj.value.userInfoObj.list = [{name: "暂无搜索结果", none: true}];
            } else {
              res.data.empList.map(item => { setUserBaseInfo(item)});
              searchObj.value.userInfoObj.list = res.data.empList;
            }
          } else {
            toast({title: res.errorMsg || "系统错误", type: 2});
          }
        }
      })
    }

    // 选择搜索人员
    function selUser(item, reset) {
      if (!reset) {
        if (!item.workerNo) {
          return;
        }
        searchObj.value.userInfo = item;
      } else {
        searchObj.value.userInfo = {};
      }
      searchObj.value.userInfoObj.text = "";
      searchObj.value.userInfoObj.list = [];
      showType(-1);
    }

    // 单选
    function selSingleBox(type, index) {
      switch (type) {
        case 2:
          // 查询人范围
          searchObj.value.areaIndex = index;
          break;
        case 4:
          // 查询状态
          searchObj.value.statusIndex = index;
          break;
      }
      showType(-1);
    }

    // 多选
    function selMultiBox(type, index) {
      switch (type) {
        case 1:
          // 多选群类型
          if (index == -1) {
            searchObj.value.typeAll = true;
            // 去除其他群类型选择
            searchObj.value.typeList.map(tItem => {
              tItem.sel = false;
            });
          } else {
            // 去除全部类型和选择其他群类型
            searchObj.value.typeAll = false;
            searchObj.value.typeList[index].sel = !searchObj.value.typeList[index].sel;
            let flag = true;
            searchObj.value.typeList.map(tItem => {
              if (tItem.sel) {
                flag = false;
              }
            });
            if (flag) {
              searchObj.value.typeAll = true;
            } else {
              searchObj.value.typeAll = false;
            }
            return;
          }
          break;
      }
      showType(-1);
    }

    // 悬浮群类型
    function hoverType(type) {
      debounce({
        timerName: "searchUser",
        time: 100,
        fnName: async () => {
          searchObj.value.typeHover = type;
        }
      })
    }

    // 搜索和重置 notInitFlag不初始化标识
    async function searchGroup(type, notInitFlag) {
      // 点击搜索/重置按钮重置选中数量和状态
      searchObj.value.searchFlag = true;
      if (!notInitFlag) {
        // 查询初始化
        multiObj.value.selClassifyMap = {};
        multiObj.value.selGroupMap = {};
        searchObj.value.page = 1;
        updateClassifyStatus();
        teamListRef.value.querySelector(".team-list-box").scrollTop = 0;
      }
      if (type == -1 || getSearchStatus()) {
        // 重置
        resetGroupParam();
        initGroup();
        return;
      } else {
        let param = {
          creatorNumber: searchObj.value.userInfo.workerId,
          personTag: searchObj.value.areaList[searchObj.value.areaIndex].value,
          groupType: trimArray(searchObj.value.typeList.map(item => {if (item.sel) {return item.value}})).toString(),
          groupStatus: searchObj.value.statusList[searchObj.value.statusIndex].value,
          beginDate: dateFormat(searchObj.value.timeObj.startTime, "yyyy/MM/dd"),
          endDate: dateFormat(searchObj.value.timeObj.endTime, "yyyy/MM/dd"),
          dayDate: searchObj.value.timeRange,
          keyWord: searchObj.value.text,
          isInGroup: searchObj.value.isInGroup,
          noGroupType: 3,
          page: searchObj.value.page,
          rows: searchObj.value.size,
        }
        loading();
        let res = await searchGroupFrontListApi({msgBody: JSON.stringify(param)});
        loading().hide();
        if (!res.success) {
          toast({title: res.errorMsg || "系统错误", type: 2});
          return;
        }
        searchObj.value.allPage = res.data?.pages || 1;
        let teamList = [];
        // 加载更多状态
        if (notInitFlag && searchObj.value.page != 1) {
          teamList = groupObj.value.list[0].tList;
        }
        if (res.data?.list) {
          // 判断返回条数和查询条数不一致则没有更多
          if (res.data.list.length == searchObj.value.size) {
            searchObj.value.hasMore = true;
          } else {
            searchObj.value.hasMore = false;
          }
          res.data.list.map(item => {
            if (item.tid) {
              teamList.push({
                teamId: item.tid,
                avatar: item.icon || "/img/default/group.png",
                name: item.name,
                createTime: item.createTime,
                owner: item.owner,
                serverCustom: {
                  groupType: item.groupType,
                  status: item.groupStatus,
                  typeName: item.groupTypeStr,
                  typeStatus: item.groupStatusStr,
                },
              });
            }
          })
        } else {
          searchObj.value.hasMore = false;
        }
        if (searchObj.value.isInGroup == 2) {
          searchObj.value.searchStatus = 2;
        } else {
          searchObj.value.searchStatus = 1;
        }
        // 渲染列表数据
        if (teamList.length > 0 || notInitFlag) {
          groupObj.value.list = [{id: "none", uuid: "none", tList: teamList}];
        } else {
          groupObj.value.list = [];
        }
        // 非初始化重新计算选中数量
        if (!notInitFlag) {
          updateClassifyStatus(true);
        }
      }
      searchObj.value.searchFlag = false;
    }

    // 判断是否没有搜索条件
    function getSearchStatus() {
      return !searchObj.value.userInfo.workerNo && !searchObj.value.areaIndex && searchObj.value.typeAll && !searchObj.value.statusIndex &&
        !searchObj.value.timeObj.startTime && !searchObj.value.timeObj.endTime && !searchObj.value.timeRange && !searchObj.value.text && searchObj.value.isInGroup == 1;
    }

    // 批量修改状态回调
    function teamEditorDone(res) {
      if (res.data?.success?.length > 0) {
        res.data.success.map(item => {
          // 搜索状态删除对应列表
          for (let i = 0; i < groupObj.value.list.length; i++) {
            let cItem = groupObj.value.list[i];
            for (let j = 0; j < cItem.tList.length; j++) {
              let tItem = cItem.tList[j];
              // 修改成功群列表状态
              if (tItem.teamId == item.tid) {
                // 搜索状态循环改变字段
                if (searchObj.value.searchStatus != -1) {
                  cItem.tList[j].serverCustom = {
                    groupType: item.groupType,
                    status: item.groupStatus,
                    typeName: item.groupTypeStr,
                    typeStatus: item.groupStatusStr,
                  }
                }
                for (let key in multiObj.value.selGroupMap) {
                  // 遍历删除选中的相关分组
                  if (new RegExp(`-${item.tid}$`).test(key)) {
                    delete multiObj.value.selGroupMap[key];
                  }
                }
              }
            }
            // 更新选中状态
            updateClassifyStatus(true);
          }
        });
      }
    }

    // 滚动列表回调
    function scrollLi(e) {
      store.commit("setEmit", {type: "teamListCompScroll", value: Date.now()});
      if (searchObj.value.searchStatus != -1) {
        throttle({
          timerName: "scrollLiTeamList",
          time: 100,
          fnName: function () {
            if (!searchObj.value.searchFlag && searchObj.value.hasMore && e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight < 20) {
              searchObj.value.page++;
              searchGroup("", true);
            }
          }
        });
      }
    }

    // 打开切换讨论组状态
    function changeTeamType(to, detailInfo) {
      if (multiObj.value.num > 0) {
        return;
      }
      teamEditorRef.value.initTeamInfo(to, detailInfo);
    }

    // 切换选择搜索标签类型
    function toggleSearchLabel(type) {
      // 单选状态
      if (searchObj.value.isInGroup == type) {
        searchObj.value.isInGroup = 1;
      } else {
        // 不联动重置历史内容
        if (type == 3 || type == 4 || type == 5) {
          resetGroupParam();
        }
        searchObj.value.isInGroup = type
      }
      // 选择后触发搜索
      searchGroup();
    }

    // 创建讨论组
    function addGroup() {
      store.commit("setEmit", {type: "inviteBox", value: {type: 1, time: Date.now()}});
    }

    // 切换显示讨论组tab
    function toggleTabType(type) {
      groupObj.value.tabType = type;
      searchGroup(-1);
      initGroup();
    }

    // 失去焦点
    function blurInput(e) {
      e.target.blur();
    }

    // 返回显示列表
    function back() {
      level.value = 1;
    }

    // 阻止点击穿透
    function stopPropagation() {}

    // 自动退群设置
    async function showQuitQrCode() {
      dialogObj.value.qrcodeSrc = await createQrCode({text: `jjsApp:${JSON.stringify({className: {ios: "IM_Poi_TeamQuitSettingListVC", android: "com.jjshome.oa.searchUserOrShop.activity.TuiQunSettingActivity"}})}`, width: 150});
      dialogObj.value.type = 3;
    }

    return {
      calendarRef,
      teamListRef,
      teamEditorRef,
      searchObj,
      groupObj,
      multiObj,
      dialogObj,
      level,

      selItem,
      selMulti,
      setMenu,
      openChat,
      updateClassify,
      dialogOperate,
      showType,
      inputTimeRange,
      setCurrentDay,
      searchUser,
      selUser,
      selSingleBox,
      selMultiBox,
      hoverType,
      searchGroup,
      teamEditorDone,
      scrollLi,
      changeTeamType,
      toggleSearchLabel,
      addGroup,
      toggleTabType,

      blurInput,
      back,
      stopPropagation,
      showQuitQrCode,

      dateFormat,
      avatarError,
    }
  }
}
</script>

<style lang="scss" scoped>
.team-box {
  width: 100%;
  height: 100%;
  background: #FFFFFF;

  input {
    &::placeholder {
      color: #999999;
    }
  }

  .sel-arrow {
    &:before {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(calc(-50% - 1px), -50%);
      width: 0;
      height: 0;
      border-width: 6px 0 6px 6px;
      border-style: solid;
      border-color: transparent transparent transparent #FFFFFF;
      z-index: 1;
    }

    &:after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0;
      height: 0;
      border-width: 6px 0 6px 6px;
      border-style: solid;
      border-color: transparent transparent transparent #999999;
    }

    &.sel {
      &:before {
        transform: translate(-50%, calc(-50% - 1px));
        border-width: 6px 6px 0 6px;
        border-color: #FFFFFF transparent transparent transparent;
      }

      &:after {
        border-width: 6px 6px 0 6px;
        border-color: #999999 transparent transparent transparent;
      }
    }
  }

  .sel-box-i {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    position: relative;
    border: 1px solid #BCBCBC;
    border-radius: 2px;

    &.sel {
      border: 1px solid $styleColor;
      background: $styleColor;

      &:after {
        content: "";
        width: 8px;
        height: 3px;
        border: 2px solid #FFFFFF;
        border-top: transparent;
        border-right: transparent;
        position: absolute;
        top: 2px;
        left: 1px;
        transform: rotate(-45deg);
      }
    }
  }

  .search-btn {
    line-height: 16px;
    padding: 6px 11px;
    border-radius: 4px;
    background: $styleColor;
    color: #FFFFFF;
    border: 1px solid $styleColor;
    margin: 0 0 6px 6px;
    cursor: pointer;

    &.btn-reset {
      background: transparent;
      color: $styleColor;
      border: 1px solid $styleColor;
    }
  }

  .search-close {
    display: none;
    width: 12px;
    background: url("/img/mail/icon_close.png") no-repeat center;
    background-size: 12px 12px;
    cursor: pointer;
  }

  .default-btn {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 56px;
    padding: 4px 0;
    line-height: 16px;
    text-align: center;
    border-radius: 4px;
    border: 1px solid #E0E0E0;
    background: #FFFFFF;
    cursor: pointer;

    &.disabled {
      background: #CCCCCC;
      border: 1px solid #CCCCCC;
      color: #FFFFFF;

      .icon-question {
        background-position: -24px 0;
      }
    }

    &:not(.disabled):hover {
      color: $styleColor;
      border: 1px solid $styleColor;

      .icon-question {
        background-position: -12px 0;
      }
    }

    .icon-question {
      display: block;
      width: 12px;
      height: 12px;
      background-image: url("/img/mail/icon_question.png");
      background-repeat: no-repeat;
      background-size: 36px 12px;
    }

    .btn-question-box {
      position: relative;

      &:hover {
        .btn-tips {
          display: block;
        }
      }

      .btn-tips {
        display: none;
        position: absolute;
        bottom: 20px;
        left: -78px;
        width: 166px;
        line-height: 17px;
        padding: 6px 10px;
        color: #FFFFFF;
        background: #000000;
        border-radius: 4px;
        font-size: 12px;
        text-align: left;
        opacity: 0.8;
        z-index: 1;

        &.btn-tips-right {
          left: unset;
          right: -14px;

          &:before {
            left: unset;
            right: 14px;
            transform: none;
          }
        }

        &:before {
          content: "";
          position: absolute;
          bottom: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-width: 6px 6px 0 6px;
          border-style: solid;
          border-color: #000000 transparent transparent transparent;
        }
      }
    }
  }

  .comp-chat-list {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    &.list {
      .team-content {
        height: calc(100% - 44px);
      }

      .team-footer {
        display: none;
      }
    }

    .team-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 45px;
      padding: 0 16px;
      border-bottom: 1px solid #E0E0E0;

      .title-ul {
        height: 100%;
        display: flex;
        align-items: center;
        height: 100%;
        font-size: 16px;
        color: #666666;

        .title-li {
          display: flex;
          align-items: center;
          height: 100%;
          margin-right: 24px;
          cursor: pointer;

          &.sel {
            position: relative;
            color: $styleColor;
            font-weight: bold;

            &:after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 20px;
              height: 3px;
              background: $styleColor;
              border-radius: 2px;
            }
          }
        }
      }

      .btn-box {
        display: flex;
        align-items: center;

        .btn {
          font-size: 13px;
          color: $styleColor;
          cursor: pointer;
          margin-left: 16px;
        }
      }
    }

    .team-content {
      display: flex;
      flex-direction: column;
      height: calc(100% - 44px - 42px);

      .search-content-box {
        flex-shrink: 0;
        padding: 10px 16px 5px 16px;
        border-bottom: 1px solid #E0E0E0;

        .search-content-label-box {
          display: flex;
          flex-shrink: 0;

          .search-intr {
            line-height: 30px;
            color: #666666;
            margin-right: 4px;
            flex-shrink: 0;
          }

          .search-sel-box {
            display: flex;
            flex-wrap: wrap;
            align-items: center;

            .search-box {
              position: relative;
              height: 30px;
              line-height: 28px;
              display: flex;
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              margin: 0 0 6px 6px;
              padding: 0 6px 0 10px;

              &.show,
              &:hover {
                border: 1px solid #000000;

                &.show {
                  .search-arrow {
                    &:after {
                      border-width: 0 4px 4px 4px;
                      border-style: solid;
                      border-color: transparent transparent #4B4B4B transparent;
                    }
                  }
                }
              }

              &.search-user-box {
                width: 148px;

                &.is-user {
                  &:hover {
                    .search-arrow {
                      display: none;
                    }

                    .search-close {
                      display: block;
                    }
                  }

                  .text {
                    color: #000000;
                  }
                }

                .text {
                  color: #999999;
                }

                .search-user-ul-box {
                  position: absolute;
                  top: 32px;
                  left: 0;
                  width: 186px;
                  background: #FFFFFF;
                  box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
                  border-radius: 4px;
                  border: 1px solid #E0E0E0;
                  z-index: 1;

                  .search-user-input-box {
                    position: relative;
                    display: flex;
                    width: 100%;
                    height: 30px;
                    line-height: 30px;
                    padding: 0 10px;
                    background: url("/img/schedule/search.png") no-repeat right 10px top 8px;
                    background-size: 18px 18px;

                    input {
                      width: calc(100% - 18px);
                    }
                  }

                  .search-user-ul {
                    max-height: 242px;
                    overflow-y: auto;
                    position: relative;

                    &:after {
                      content: "";
                      position: absolute;
                      top: 0;
                      left: 10px;
                      width: calc(100% - 20px);
                      height: 1px;
                      background: #E0E0E0;
                    }

                    li {
                      height: 30px;
                      padding: 0 10px;

                      &.none {
                        text-align: center;
                      }

                      &:hover:not(.none) {
                        background: #F5F5F5;
                      }
                    }
                  }
                }
              }

              &.search-area-box {
                width: 80px;
              }

              &.search-type-box {
                width: 97px;

                .type-label-box {
                  display: flex;
                  align-items: center;

                  .type-label {
                    position: relative;
                    height: 20px;
                    line-height: 18px;
                    padding: 0 16px 0 4px;
                    color: $styleColor;
                    background: #FFF3F3;
                    border-radius: 2px;
                    border: 1px solid $styleColor;
                    margin-right: 6px;

                    .close {
                      position: absolute;
                      top: 50%;
                      right: 4px;
                      transform: translateY(-50%);
                      width: 8px;
                      height: 8px;
                      overflow: hidden;

                      &:before,
                      &:after {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        content: "";
                        background: $styleColor;
                        width: 1px;
                        height: 12px;
                      }

                      &:before {
                        transform: translate(-50%, -50%) rotate(45deg);
                      }

                      &:after {
                        transform: translate(-50%, -50%) rotate(-45deg);
                      }
                    }
                  }
                }
              }

              &.search-status-box {
                width: 80px;
              }

              &.search-time-box {
                width: 178px;
                color: #999999;

                &.sel {
                  color: #000000;

                  &:hover {
                    .search-calendar {
                      display: none;
                    }

                    .search-close {
                      display: block;
                    }
                  }
                }

                .calendar-text {
                  flex: 1;
                  margin-right: 5px;
                }

                .search-calendar {
                  width: 17px;
                  background: url("/img/mail/icon_calendar.png") no-repeat center;
                  background-size: 17px 16px;
                  cursor: pointer;
                }

                .search-close {
                  width: 17px;
                }

                .calendar-box {
                  width: 500px;
                  height: 360px;
                  position: absolute;
                  top: 32px;
                  right: 0;
                  background: #FFFFFF;
                  box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
                  border-radius: 4px;
                  border: 1px solid #E0E0E0;
                  z-index: 1;
                }
              }

              &.search-range-box {
                width: 148px;
                display: flex;
                align-items: center;

                input {
                  width: 36px;
                  margin: 0 9px;
                }
              }

              &.search-input-box {
                width: 183px;
              }

              .textEls {
                flex: 1;
              }

              .search-arrow {
                position: relative;
                width: 12px;
                height: 100%;
                flex-shrink: 0;

                &:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  width: 0;
                  height: 0;
                  border-width: 4px 4px 0 4px;
                  border-style: solid;
                  border-color: #4B4B4B transparent transparent transparent;

                  &.hover {
                    border-width: 0 4px 4px 4px;
                    border-color: transparent transparent $styleColor transparent;
                  }
                }
              }

              .single-sel-box {
                position: absolute;
                top: 32px;
                left: 0;
                width: 105px;
                max-height: 184px;
                overflow-y: auto;
                background: #FFFFFF;
                box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
                border-radius: 4px;
                border: 1px solid #E0E0E0;
                z-index: 1;

                li {
                  position: relative;
                  height: 30px;
                  line-height: 30px;
                  padding-left: 9px;

                  &:hover {
                    background: #F5F5F5;
                  }

                  &.sel {
                    color: $styleColor;
                    font-weight: bold;

                    &:after {
                      content: "";
                      position: absolute;
                      top: 10px;
                      right: 10px;
                      width: 8px;
                      height: 4px;
                      border-left: 2px solid $styleColor;
                      border-bottom: 2px solid $styleColor;
                      transform: rotate(-45deg);
                    }
                  }
                }
              }

              .multi-sel-box {
                position: absolute;
                top: 32px;
                left: 0;
                width: 120px;
                max-height: 218px;
                padding: 4px 0;
                overflow-y: auto;
                background: #FFFFFF;
                box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
                border-radius: 4px;
                border: 1px solid #E0E0E0;
                z-index: 1;

                li {
                  display: flex;
                  align-items: center;
                  padding: 0 10px;

                  &:hover {
                    background: #F5F5F5;
                  }

                  .sel-box-i {
                    margin-right: 6px;
                  }
                }
              }

              .multi-hover-modal {
                position: fixed;
                bottom: calc(100% - 98px);
                left: 593px;
                display: inline-flex;
                flex-wrap: wrap;
                width: max-content;
                max-width: 360px;
                max-height: 79px;
                box-shadow: 0px 2px 8px 0px rgba(1, 0, 13, 0.15);
                border: 1px solid #EBEBEB;
                border-radius: 4px;
                background: #FFFFFF;
                z-index: 101;
                padding: 10px 4px 4px 10px;

                &:after {
                  content: "";
                  width: 0;
                  height: 0;
                  position: absolute;
                  bottom: -11px;
                  left: 40px;
                  border-width: 6px;
                  border-style: solid;
                  border-color: #FFFFFF transparent transparent transparent;
                }

                .multi-hover-box {
                  display: inline-flex;
                  flex-wrap: wrap;
                  max-width: 360px;
                  max-height: 52px;
                  overflow-y: auto;
                  background: #FFFFFF;
                  border-radius: 4px;

                  li {
                    position: relative;
                    height: 20px;
                    line-height: 18px;
                    padding: 0 16px 0 4px;
                    background: #F2F2F6;
                    border-radius: 2px;
                    border: 1px solid #E8E8E8;
                    margin: 0 6px 6px 0;

                    .close {
                      position: absolute;
                      top: 50%;
                      right: 4px;
                      transform: translateY(-50%);
                      width: 8px;
                      height: 8px;
                      overflow: hidden;
                      cursor: pointer;

                      &:before,
                      &:after {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        content: "";
                        background: #A4A4A4;
                        width: 1px;
                        height: 12px;
                      }

                      &:before {
                        transform: translate(-50%, -50%) rotate(45deg);
                      }

                      &:after {
                        transform: translate(-50%, -50%) rotate(-45deg);
                      }
                    }
                  }
                }
              }
            }

            .search-radio-box {
              display: flex;
              align-items: center;
              margin: 0 0 6px 6px;
              cursor: pointer;

              .sel-box-i {
                margin-right: 4px;
              }
            }

            .search-sel-label {
              border-radius: 2px;
              border: 1px solid #E0E0E0;
              color: #666666;
              line-height: 18px;
              height: 20px;
              padding: 0 10px;
              margin-left: 6px;
              cursor: pointer;

              &.sel {
                color: $styleColor;
                border: 1px solid $styleColor;
                background: #FFF3F3;
              }
            }
          }
        }
      }

      .team-list-box {
        flex: 1;
        overflow-y: auto;

        .team-classify-ul {

          &.team-classify-ul-1 {
            ::v-deep(.sel-box) {
              width: 38px;
            }

            ::v-deep(.team-li) {
              &:hover {
                .team-li-info-box {
                  width: calc(100% - 38px - 46px - 64px);
                }
              }
            }
          }

          .team-classify-li {
            position: relative;
            display: flex;
            align-items: center;
            height: 36px;
            padding-left: 40px;
            cursor: pointer;

            &:hover {
              background: $styleBg1Hover;

              .sel-arrow {
                &.sel {
                  &:before {
                    border-color: $styleBg1Hover transparent transparent transparent;
                  }
                }

                &:before {
                  border-color: transparent transparent transparent $styleBg1Hover;
                }
              }
            }

            .sel-box-i {
              margin-right: 8px;
            }

            .sel-arrow {
              position: absolute;
              top: 50%;
              left: 24px;
              transform: translateY(-50%);
            }

            .team-classify-editor {
              width: 240px;
              height: 28px;
              line-height: 26px;
              border-radius: 2px;
              padding: 5px 10px 5px 10px;
              border: 1px solid #333333;
              font-size: 14px;
            }

            .team-classify-info {
              font-size: 15px;
              font-weight: bold;
              color: #333333;
            }
          }
        }

        .team-list-none {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          width: 100%;
          height: 100%;
          color: #666666;

          .none-img {
            width: 190px;
          }

          .none-text {
            color: #333333;
            line-height: 17px;
            margin: 10px 0;
          }

          .none-btn {
            padding: 6px 10px;
            color: #FFFFFF;
            line-height: 18px;
            background: $styleColor;
            border-radius: 4px;
            cursor: pointer;
          }
        }
      }
    }

    .team-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 42px;
      padding: 0 16px;
      border-top: 1px solid #E5E5E5;

      .sel-num-box {
        display: flex;
        align-items: center;

        .sel-num-info {
          display: flex;
          align-items: center;

          .num {
            color: $styleColor;
            margin: 0 4px;
          }
        }
      }

      .sel-btn-box {
        display: flex;
        align-items: center;

        .default-btn {
          margin-left: 8px;
        }
      }
    }
  }

  :deep(.dialog-manger-box) {
    .content {
      padding: 10px 0 0 !important;
    }

    footer {
      padding: 12px 0 !important;
      box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05), inset 0px 1px 0px 0px #DDDDDD;
    }
  }

  :deep(.dialog-move-box) {
    .content {
      padding: 0 !important;
    }
  }

  :deep(.dialog-qrcode-box) {
    .content {
      padding: 0 !important;
    }
  }

  .main-dialog-box {
    .classify-tips {
      height: 35px;
      line-height: 34px;
      padding: 0 16px;
      color: #666666;
      border-bottom: 1px solid #E0E0E0;
    }

    .add-classify-box {
      margin: 10px 16px 0;

      .add-classify {
        padding-left: 16px;
        color: $styleColor;
        background: url("/img/workbench/icon_add.png") no-repeat left center;
        background-size: 12px 12px;
        line-height: 16px;
        cursor: pointer;
      }
    }

    .ly-dialog-default-box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;

      .ly-dialog-default-label {
        color: #666666;
        line-height: 30px;
        flex-shrink: 0;

        .ly-dialog-default-tips {
          color: #EE3939;
          margin-right: 5px;
        }
      }

      .ly-dialog-default-detail {
        width: 282px;

        .ly-dialog-default-input {
          width: 100%;
          line-height: 28px;
          padding-left: 10px;
          border: 1px solid #CCCCCC;
          border-radius: 4px;

          &::placeholder {
            color: #999999;
          }

          &:focus {
            border: 1px solid #333333;
          }
        }
      }
    }

    .move-content-box {
      .move-content-ul {
        margin: 7px 0;
        height: 216px;
        overflow-y: auto;

        li {
          display: flex;
          align-items: center;
          height: 30px;
          padding: 0 16px;

          &:hover {
            background: $styleBg1Hover;
          }

          .sel-box {
            display: inline-block;
            width: 14px;
            height: 14px;
            position: relative;
            border: 1px solid #DDDDDD;
            border-radius: 50%;
            flex-shrink: 0;
            margin-right: 6px;

            &.sel {
              border: 1px solid $styleColor;
              background: $styleColor;

              &:after {
                content: "";
                width: 6px;
                height: 3px;
                border: 2px solid #FFFFFF;
                border-top: transparent;
                border-right: transparent;
                position: absolute;
                top: 3px;
                left: 2px;
                transform: rotate(-45deg);
              }
            }
          }
        }
      }
    }

    .qrcode-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px 16px 16px;
      font-size: 14px;

      img {
        margin-bottom: 16px;
      }
    }
  }
}
</style>