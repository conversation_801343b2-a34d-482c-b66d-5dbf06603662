<template>
  <!--标签列表-->
  <div class="label-list" v-show="!labelObj.showItem.id">
    <div class="label-header">
      <div class="title-box">
        <span class="title">{{ labelType == 3 ? "同事" : "客户" }}标签</span>
      </div>
      <div class="btn-box">
        <span class="btn" @click="updateClassify({type:7})">标签排序</span>
        <span class="btn" @click="updateClassify({type:1})">新增标签</span>
      </div>
    </div>
    <div class="label-content">
      <ul class="label-ul" v-if="labelObj.classifyList.length>0">
        <li class="label-li" v-for="(item,key) in labelObj.classifyList" :key="item.id" @click="changeShowItem(item)">
          <div class="label-name-box">
            <i class="label-icon"></i>
            <span class="label-name textEls">{{ item.name }}（{{ item.tList.length }}）</span>
          </div>
          <div class="label-operate-box">
            <span class="btn" @click.stop="updateClassify(({type: 2, uuid: item.uuid, name: item.name}))">重命名</span>
            <span class="btn" @click.stop="updateClassify(({type: 3, uuid: item.uuid, name: item.name}))">删除</span>
          </div>
        </li>
      </ul>
      <div class="label-none" v-else>
        <div class="btn" @click="updateClassify({type:1})">创建标签</div>
        <div class="intr">通过标签你可方便地查找和管理联系人</div>
      </div>
    </div>
  </div>
  <!--标签人员-->
  <div class="label-list" v-show="labelObj.showItem.id">
    <div class="label-header">
      <div class="title-box pointer" @click="changeShowItem({})">
        <i class="back"></i>
        <span class="title textEls">{{ labelObj.showItem.name }}（{{ labelObj.showItem.tList ? labelObj.showItem.tList.length : 0 }}）</span>
      </div>
      <div class="btn-box">
        <span class="btn" @click="modifyLabel(4)">添加成员</span>
        <span class="btn" @click="modifyLabel(5)">删除成员</span>
      </div>
    </div>
    <div class="label-content">
      <ul class="label-ul" v-if="labelObj.showItem.tList&&labelObj.showItem.tList.length>0">
        <li class="label-li" v-for="(item,key) in labelObj.showItem.tList" :key="item.workerNo" @dblclick="openChat(item)">
          <div class="label-name-box">
            <div class="avatar-box">
              <img class="avatar notCopy selNone" :src="item.avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.workerNo, '')">
            </div>
            <span class="label-name textEls">{{ item.userShowName || item.name }}</span>
            <span class="label-intr">{{ item.dutyName }}</span>
          </div>
          <div v-if="labelType==4" class="label-operate-box show">
            <span class="btn" @click.stop="showCustomerDialog(1,item)">备注</span>
            <span class="btn" @click.stop="showCustomerDialog(2,item)">举报</span>
          </div>
        </li>
      </ul>
      <div class="label-none" v-else>
        <div class="btn" @click="modifyLabel(4)">添加成员</div>
        <div class="intr">通过标签你可方便地查找和管理联系人</div>
      </div>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import {getPersons, avatarError, debounce, deepClone, setUserBaseInfo} from "@utils";
import {toast} from "@comp/ui";

export default {
  name: "LabelList",
  props: {
    // 标签类型3同事4客户
    labelType: {
      type: String,
      default: "3",
    },
  },
  setup(props, ctx) {
    const store = useStore();
    // 配置文件
    let config = store.getters.getConfig.config;

    let labelObj = ref({
      classifyList: [],
      showItem: {},// 显示标签对象
    });

    // 初始化分组列表
    initLabel();
    // 获取服务器最新分组
    store.dispatch("setGroupSetting");

    // 分组变动
    watch(() => store.state.groupSettings,
      (newValue, oldValue) => {
        initLabel();
      }, {
        deep: true
      }
    );
    // 客户备注
    watch(() => store.state.nimFriend,
      (newValue, oldValue) => {
        debounce({
          timerName: "labelNimFriend",
          time: 500,
          fnName: function () {
            initLabel();
          }
        })
      }, {
        deep: true
      }
    );

    // 初始化讨论组数据
    function initLabel() {
      let list = [];
      // 获取服务器分组配置
      let labelSettings = store.getters.getClassifyList({type: props.labelType});
      let labelWorkerNoMap = {};
      // 遍历获取对应同事/客户标签人员数据
      for (let i = 0; i < labelSettings.length; i++) {
        let item = labelSettings[i];
        let tList = [];
        if (item.value) {
          try {
            item.value.split(",").map(wItem => {
              // 客户标签过滤非客户脏数据
              if (!(item.type == 4 && !isFcw(wItem))) {
                labelWorkerNoMap[wItem] = wItem;
              }
              // 设置缓存人员数据
              let wUserInfo = getPersons(store.state, wItem);
              if (!isFcw(wItem)) {
                wUserInfo = setUserBaseInfo(wUserInfo);
              }
              tList.push(wUserInfo);
            });
          } catch (e) {
            console.log("initLabelErr", e);
          }
        }
        item.tList = tList;
        list.push(item);
        if (labelObj.value.showItem.id == item.id) {
          labelObj.value.showItem = item;
        }
      }
      // 设置人员数据
      let workerNos = Object.values(labelWorkerNoMap);
      if (workerNos.length > 0) {
        store.dispatch("getPersons", {doneFlag: true, account: workerNos}).then(personInfo => {
          list.map(item => {
            for (let i = 0; i < item.tList.length; i++) {
              item.tList[i] = getPersons(store.state, item.tList[i].workerNo)
              if (labelObj.value.showItem.id == item.id) {
                labelObj.value.showItem = item;
              }
            }
          });
        });
      }
      labelObj.value.classifyList = list;
    }

    // 创建讨论组-type4添加同事/客户5移出分组
    function modifyLabel(type) {
      // 默认列表
      let showList = [];
      let itemMap = {};
      if (type == 4) {
        itemMap = store.getters.getClassifyList({uuid: labelObj.value.showItem.uuid, valueMap: true});
      } else {
        showList = deepClone(labelObj.value.showItem.tList);
        if (showList.length == 0) {
          toast({title: "当前分组没有可删除的成员", type: 2});
          return;
        }
      }
      store.commit("setEmit", {
        type: "inviteBox", value: {
          type: type,
          classifyType: props.labelType,
          time: Date.now(),
          itemMap: itemMap,
          showList: showList,
          done: list => {
            if (type == 5) {
              updateClassify({type: 6, uuid: labelObj.value.showItem.uuid, name: labelObj.value.showItem.name, value: list.map(item => {return {uuid: labelObj.value.showItem.uuid, value: item.workerNo}})});
            } else {
              updateClassify({type: 5, uuid: labelObj.value.showItem.uuid, name: labelObj.value.showItem.name, value: list.map(item => {return {uuid: "", value: item.workerNo}})});
            }
          }
        }
      });
    }

    // 1创建分组2重命名分组3删除分组4移动至分组5添加至分组6移出至分组7分组排序
    async function updateClassify(info, notTips) {
      let showList = [];
      if (info.type == 7) {
        showList = deepClone(labelObj.value.classifyList);
        if (showList.length == 0) {
          toast({title: `暂无${props.labelType == 2 ? "同事" : "客户"}标签可排序`, type: 2});
          return;
        }
      }
      return new Promise(resolve => {
        store.commit("setEmit", {
          type: "initClassify", value: {
            classifyType: props.labelType,
            type: info.type,
            classifyName: info.name,
            classifyUUID: info.uuid,
            classifyValue: info.value,
            classifySortList: showList,
            notTips: notTips,
            time: Date.now(),
            done: res => {
              resolve(res);
            }
          }
        });
      });
    }

    // 改变显示标签人员列表
    function changeShowItem(item) {
      labelObj.value.showItem = item;
    }

    // 打开会话
    function openChat(item) {
      if (props.labelType == 4 && !isFcw(item.workerNo)) {
        return;
      }
      store.dispatch("setCurrentSession", {id: "p2p-" + item.workerNo, type: "open"});
    }

    // 显示客户弹窗 1备注2举报
    function showCustomerDialog(type, item) {
      if (type == 1) {
        store.commit("setEmit", {type: "customerDialog", value: {type: 1, account: item.workerNo, name: item.detailInfo.name, time: Date.now()}});
      } else if (type == 2) {
        store.commit("setEmit", {type: "customerDialog", value: {type: 2, account: item.workerNo, time: Date.now()}});
      }
    }

    // 判断是否房产网会话
    function isFcw(id) {
      return new RegExp(config.fcw).test(id);
    }

    return {
      labelObj,

      modifyLabel,
      updateClassify,
      changeShowItem,
      avatarError,
      openChat,
      showCustomerDialog,
      isFcw,
    }
  }
}
</script>
<style scoped lang="scss">
.label-list {
  width: 100%;
  height: 100%;
  background: #FFFFFF;

  .label-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 45px;
    padding: 0 16px;
    border-bottom: 1px solid #E0E0E0;

    .title-box {
      width: calc(100% - 136px);
      display: flex;
      align-items: center;

      &.pointer {
        cursor: pointer;
      }

      .back {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
        margin-right: 6px;
        background: url('/img/back.png') left center no-repeat;
        background-size: 16px 16px;
        cursor: pointer;
      }

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333333;
      }
    }

    .btn-box {
      display: flex;
      align-items: center;

      .btn {
        font-size: 13px;
        color: $styleColor;
        cursor: pointer;
        margin-left: 16px;
      }
    }
  }

  .label-content {
    display: flex;
    flex-direction: column;
    height: calc(100% - 45px);

    .label-ul {
      width: 100%;
      height: 100%;
      overflow-y: auto;

      .label-li {
        width: 100%;
        height: 40px;
        padding: 0 20px 0 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        &:hover {
          background: $styleBg1Hover;

          .label-operate-box {
            display: flex;
          }
        }

        .label-name-box {
          width: calc(100% - 100px);
          display: flex;
          align-items: center;
          font-size: 14px;

          .label-icon {
            flex-shrink: 0;
            width: 20px;
            height: 20px;
            background: url("/img/person_tag.png") no-repeat;
            background-size: 100%;
            margin-right: 8px;
          }

          .avatar-box {
            width: 24px;
            height: 24px;
            margin-right: 8px;
            border-radius: 50%;
            overflow: hidden;

            .avatar {
              width: 100%;
            }
          }

          .label-name {
            font-size: 14px;
          }

          .label-intr {
            margin-left: 6px;
            color: #999999;
            font-size: 12px;
          }
        }

        .label-operate-box {
          display: none;
          align-items: center;
          color: #666666;

          &.show {
            display: flex;
          }

          .btn {
            margin-left: 20px;
            cursor: pointer;

            &:hover {
              color: $styleColor;
            }
          }
        }
      }
    }

    .label-none {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: 14px;

      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 8px 20px;
        line-height: 20px;
        background: $styleColor;
        border-radius: 4px;
        color: #FFFFFF;
        cursor: pointer;
      }

      .intr {
        color: #999999;
        margin-top: 12px;
      }
    }
  }
}
</style>