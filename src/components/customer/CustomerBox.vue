<template>
  <div class="customer">
    <ChatBox sessionType="5"></ChatBox>
  </div>
</template>
<script>
import {useStore} from "vuex";
import ChatBox from "@comp/chat/ChatBox";

export default {
  name: "customer",
  components: {ChatBox},
  setup(props, ctx) {
    const store = useStore();
    return {}
  }
}
</script>
<style scoped lang="scss">
.customer {
  width: 100%;
  height: 100%;
}
</style>