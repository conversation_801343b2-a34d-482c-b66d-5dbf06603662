<template>
  <div class="chat-search">
    <div class="search-header">
      <div class="search-box win-no-drag">
        <input class="search-input" type="text" @input="searchInput()" v-model.trim="searchVal" placeholder="搜索" ref="searchInputRef"
               @keyup.enter="doSel" @keydown.up.prevent="selItem(1)" @keydown.down.prevent="selItem(2)" @keydown.esc.stop="toggleShow(1)"
               @blur="toggleShow(2)" @focus="searchInput(1)" @mousedown="stopPropagation">
        <span class="icon-close" v-show="searchVal.length>0" @click="searchVal=''"></span>
        <span class="icon-xl icon-search-tab show-text-hover" @click="openChat('xl')" content="智能小乐"></span>
      </div>
      <span class="icon-add icon-search-tab show-text-hover" @click="addGroup" content="创建讨论组"></span>
      <span class="icon-self icon-search-tab show-text-hover" @click="openChat('self')" content="发送到我的手机"></span>
      <span class="icon-update show-text-hover" :class="{'point':isUpdate}"
            @click="toAppUrl('https://i.leyoujia.com/lyjEtherpad/p/8fdaa1b3-6800-42f3-adbc-509298d6ee76?docId=8070019', 'update')" content="升级日志"></span>
      <span class="icon-bug show-text-hover" @click="toDemandOrBug" content="bug/好点子"></span>
      <span v-if="aiObj.workerNo" class="icon-ai show-text-hover" @click="openChat({},'ai')" content="乐搜">
        <img class="label" src="/img/search/icon_gpt.png" alt="">
      </span>
    </div>
    <div class="search-content win-no-drag" v-show="isShowSearch" @mousedown="searchFocus(1)">
      <ul class="search-tab-ul">
        <li :class="{'sel':searchObj.key == -1}" @click="selTab(-1)">综合</li>
        <li v-for="(item, key) in searchResList" :class="{'sel':searchObj.key == key}" @click="selTab(key)" v-show="!item.hideTab">{{ item.name }}</li>
      </ul>
      <div class="search-ul-box" ref="searchRef">
        <div class="search-content-box" v-show="allResNum>0">
          <ul class="search-ul" v-for="(item,key) in searchObj.list" :key="item.name">
            <div v-if="item.list&&item.list.length>0">
              <div class="content-header">
                <span>{{ item.name }}</span>
                <span v-if="(item.type==1&&!item.hideTab&&item.list.length>5)||(item.type==2&&item.list[0]&&item.list[0][0]&&item.list[0][0].allNumber>5)"
                      class="toggle" v-show="searchObj.key==-1" @click="selTab(key)">更多</span>
              </div>
              <div v-if="item.type==1">
                <!--一维数组-->
                <li class="search-li" v-for="(item1,key1) in item.list" :key="key1" v-show="key1<5||item.show||searchObj.key!=-1||item.hideTab"
                    @mouseenter="showUserInfo(item1.workerNo, $event, item1.showIndex)" @mouseleave="showUserInfo()">
                  <div v-if="item1" class="search-li-box" @click="openChat(item1,(item1.localKey||item.key),$event)"
                       :class="searchIndex==item1.showIndex?'curr':''">
                    <!--乐文档-->
                    <div v-if="item1.localKey=='doc'||item.key=='doc'" class="search-li-content">
                      <div class="avatar-box">
                        <img class="avatar" :src="`/img/search/icon_${item1.property==2?'excel':'doc'}.png`" alt="">
                      </div>
                      <div class="info-box doc-box">
                        <span class="doc-title textEls" :title="item1.title" v-html="getHighlight(htmlEscapeAll(item1.title), searchVal)"></span>
                        <span class="doc-intr textEls" :title="item1.empName+'('+item1.deptName+')'">{{ item1.empName }}({{ item1.deptName }})</span>
                      </div>
                    </div>
                    <!--应用-->
                    <div v-else-if="item1.localKey=='app'||item.key=='app'" class="search-li-content">
                      <div class="avatar-box">
                        <img class="avatar center" :src="getAppIcon(item1)" :onerror="errorIcon" alt="">
                      </div>
                      <div class="info-box doc-box">
                        <span class="doc-title textEls" v-html="getHighlight(htmlEscapeAll(item1.name), searchVal)"></span>
                      </div>
                    </div>
                    <!--会话-->
                    <div v-else class="search-li-content">
                      <div class="avatar-box">
                        <img class="avatar" :src="getAvatar(item1)" alt="" :onerror="avatarError.bind(this, 'p2p', item1.workerNo||item1.aNumber, '')">
                      </div>
                      <div class="info-box">
                        <p class="name textEls" :class="item1.empStatus==4?'resign':''" v-html="getHighlight(htmlEscapeAll(item1.userShowName||item1.name), searchVal)"></p>
                        <p class="intr textEls" v-if="item1.obligation">{{ item1.obligation }}</p>
                      </div>
                    </div>
                  </div>
                </li>
              </div>
              <!--二维数组-->
              <div v-if="item.type==2">
                <div v-for="(item1,key1) in item.list" :key="key1">
                  <div class="search-li-title" v-if="item1&&item1[0]" v-show="item1[0].searchNum<5||item.show||searchObj.key!=-1||item.hideTab">
                    {{ item1[0].companyName }}-{{ item1[0].deptName }}
                  </div>
                  <li class="search-li" v-for="(item2,key2) in item1" :key="item.id" v-show="item2.searchNum<5||item.show||searchObj.key!=-1||item.hideTab"
                      @mouseenter="showUserInfo(item2.workerNo, $event, item2.showIndex)" @mouseleave="showUserInfo()">
                    <div class="search-li-box" v-if="item2" @click="openChat(item2,(item1.localKey||item.key),$event)"
                         :class="searchIndex==item2.showIndex?'curr':''">
                      <div class="avatar-box">
                        <img class="avatar" :src="getAvatar(item2)" alt="" :onerror="avatarError.bind(this, 'p2p', item1.workerNo, '')">
                      </div>
                      <div class="info-box info-line-box">
                        <p class="name textEls" :class="item2.empStatus==4?'resign':''" v-html="getHighlight(htmlEscapeAll(item2.nickname), searchVal)"></p>
                        <p class="intr textEls">({{ item2.obligation }})</p>
                      </div>
                    </div>
                  </li>
                </div>
              </div>
            </div>
          </ul>
        </div>
        <!--loading或无结果-->
        <div class="search-status-box" v-show="allResNum==0">
          <img v-show="!searchObj.resultFlag" class="loading-img" src="/img/waitting.gif" alt="">
          <img v-show="searchObj.resultFlag" class="empty-img" src="/img/search/empty.png" alt="">
          <div class="show-text">{{ !searchObj.resultFlag ? "加载中" : searchVal.length == 0 ? "请输入内容搜索" : "没有找到相关结果，换个条件试试吧~" }}</div>
        </div>
      </div>
    </div>
    <ul class="top-tab-ul">
      <li class="top-tab-li show-text-hover" :class="'top-tab-li-'+item.img" v-for="(item,key) in topList" :key="item.id" :content="item.name" @click="toAppUrl(item.requestUrl)"></li>
      <li class="top-tab-li top-tab-li-add show-text-hover" v-if="topList.length==0" content="添加顶部栏应用" @click="toAppUrl('add')"></li>
    </ul>
  </div>
</template>
<script>
import {nextTick, ref, watch} from "vue";
import {
  debounce, setUserBaseInfo, avatarError, emitMsg, regReplace, deepClone, getHighlight, htmlEscapeAll, getBounding, openAppUrl, compareVersion, setLocalUpdateObj,
  setJJSEvent, scrollLi,
} from "@utils";
import {useStore} from "vuex";
import {searchUsersApi, searchDocApi} from '@utils/net/api'
import ChatUserInfoBox from "@comp/chat/ChatUserInfoBox";
import {useRouter} from "vue-router";

export default {
  name: "ChatSearch",
  components: {ChatUserInfoBox},
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    // 配置文件
    let config = store.getters.getConfig.config;
    // 搜索内容
    let searchVal = ref("");
    // 输入框组件
    let searchInputRef = ref();
    // 获取用户信息
    const userInfo = store.getters.getUserInfo;
    // 电脑信息
    let baseComputerInfo = store.getters.getBaseComputerInfo;
    // 是否显示搜索结果
    let isShowSearch = ref(false);
    // 搜索结果
    let searchResList = ref([]);
    // 搜索框元素
    let searchRef = ref();
    // 选中的搜索列下标
    let searchIndex = ref(0);
    // 总列表数
    let allResNum = ref(0);
    // 搜索对象
    let searchObj = ref({
      key: -1, // 搜索默认选中列
      flag: false,// 搜索状态
      otherFlag: false,// 乐文档/应用搜索状态
      resultFlag: false,// 显示搜索后的结果
      list: [],// 显示搜索内容列表
      timer: "", //定时器
      mouseDown: false,//长按列表不滚动到顶部
    });
    // 顶部tab列表
    let topList = ref(store.getters.getTabMap.topList);
    // 是否有更新
    let isUpdate = ref(false);
    // 智能助理对象
    let aiObj = ref(deepClone(remote.store.getters.getState("aiObj")));

    // 顶部tab列表监听
    watch(() => store.state.tabMap.topList,
      (newValue, oldValue) => {
        topList.value = newValue;
      }, {
        deep: true
      }
    );

    watch(() => isShowSearch.value,
      (newValue, oldValue) => {
        showUserMethods();
      }, {
        deep: true
      }
    );

    // 监听全局点击
    watch(() => store.state.emit.click,
      (newValue, oldValue) => {
        if (newValue && newValue.target && (/search-input/.test(newValue.target.className) || newValue.path.some(elm => /search-content/.test(elm.className)))) {
          return;
        }
        toggleShow(1);
      }, {
        deep: true
      }
    );

    // 监听快捷键聚焦搜索框
    watch(() => store.state.emit.searchFocus,
      (newValue, oldValue) => {
        searchFocus();
      }, {
        deep: true
      }
    );


    // 选择显示tab内容
    function selTab(key) {
      searchObj.value.key = key;
      setShowListIndex();
      // 乐文档需要请求
      if (!searchVal.value && searchResList.value?.[searchObj.value.key]?.key == "doc") {
        allResNum.value = 0;
        searchObj.value.flag = true;
        searchIndex.value = 0;
        searchDoc();
      }
      nextTick(() => {
        searchRef.value.scrollTop = 0;
        getLoadingStatus();
      });
    }

    // 设置搜索列表
    function setSearchList(param) {
      if (param) {
        // 设置对应列表数据
        for (let key in param) {
          for (let i = 0; i < searchResList.value.length; i++) {
            if (key == searchResList.value[i].key) {
              searchResList.value[i].list = param[key];
              break;
            }
          }
        }
      } else {
        searchResList.value = [
          {key: "customer", type: 1, name: "客户", list: []},
          {key: "empList", type: 1, name: "联系人", list: []},
          {key: "group", type: 1, name: "讨论组", list: []},
          {key: "team", type: 1, name: "群", list: []},
          {key: "findFriends", type: 2, name: "找对人", list: []},
          {key: "phoneList", type: 1, name: "按手机", list: []},
          {key: "app", type: 1, name: "应用", list: []},
          {key: "doc", type: 1, name: "乐文档", list: []},
          {key: "ser", type: 1, name: "服务号", list: []},
          {key: "sub", type: 1, name: "订阅号", list: []},
        ];
      }
    }

    // 搜索,type-1聚焦
    function searchInput(type) {
      // 第一次打开搜索弹窗选中第一个搜索
      if ((type == 1 && !isShowSearch.value) || !type) {
        if (!isShowSearch.value) {
          selTab(-1);
        }
        // 重新搜索重置数据
        setSearchList();
        allResNum.value = 0;
        searchObj.value.flag = true;
        searchObj.value.otherFlag = true;
        searchIndex.value = 0;
        toggleShow(3);

        debounce({
          timerName: "doSearch",
          time: 500,
          fnName: () => {
            if (searchResList.value?.[searchObj.value.key]?.key == "app") {
              setJJSEvent("P17324800", JSON.stringify({
                workerId: userInfo.workerId,
                type: searchObj.value.key
              }));
            }
            doSearch();
            setJJSEvent("P13945600", JSON.stringify({
              workerId: userInfo.workerId,
              type: searchObj.value.key
            }));
          }
        });
      } else {
        // 聚焦输入框
        toggleShow(3);
      }
    }

    // 显示/隐藏搜索内容-type1隐藏、2失去焦点延迟隐藏、3没输入内容聚焦显示常用、默认显示
    function toggleShow(type) {
      if (type == 1) {
        isShowSearch.value = false;
        searchInputRef.value.blur();
      } else if (type == 2) {
        // 失去焦点延迟判断是否失去焦点
        searchObj.value.tiemr = setTimeout(() => {
          toggleShow(1);
        }, 500)
      } else if (type == 3) {
        // 聚焦清除失焦定时器
        clearTimeout(searchObj.value.tiemr);
        isShowSearch.value = true;
        if (!searchVal.value) {
          // 没有输入内容的时候显示常用
          setSearchList();
          searchResList.value.push({type: 1, name: "最近", list: store.getters.getLocalSearch, hideTab: true});
          searchObj.value.flag = false;
          setShowListIndex();
          nextTick(() => {
            if (!searchObj.value.mouseDown) {
              searchRef.value.scrollTop = 0;
            } else {
              searchObj.value.mouseDown = false;
            }
          });
        }
      } else {
        isShowSearch.value = true;
      }
    }

    // 搜索api
    async function doSearch() {
      let val = searchVal.value || '';
      if (!val) {
        return;
      }
      // 搜索人员
      let searchParam = {
        workerId: userInfo.workerId,
        name: val,
        status: "",//默认为全部1在职2为离职
        page: 1,
        rows: 50,
        hasAi: "1",
        aiInclude: "1",
      }
      let searchRes = await searchUsersApi({
        msgBody: JSON.stringify(searchParam)
      });
      if (val != searchVal.value) {
        return;
      }
      // 关键词搜索到的人
      let searchInfo = {
        empList: [],
        phoneList: []
      }
      // 最近联系人
      let recentContactsMap = store.getters.getRecentContactsMap;
      if (searchRes?.data?.empList || searchRes?.data?.phoneList) {
        searchInfo = searchRes.data;
      } else if (searchRes.status == -1) {
        console.log("searchServeErr", searchRes.errorMsg || "系统错误");
        // // 超时重新判断备灾开关走本地-暂时不走判断，超时直接查本地
        // let proxyRes = await store.dispatch("getReProxyInfo");
        // // 查询本地数据库
        // if (proxyRes?.loginSwitch || proxyRes?.rsSwitch) {
        let searchDBRes = await store.getters.getUserDB.searchUser(searchParam);
        if (searchDBRes.length > 0) {
          searchInfo.empList = searchDBRes;
        }
        // }
      }
      // 处理人员数据
      if (searchInfo.empList) {
        for (let i = 0; i < searchInfo.empList.length; i++) {
          setUserBaseInfo(searchInfo.empList[i]);
          // 设置联系时间
          if (recentContactsMap["p2p-" + searchInfo.empList[i].workerNo]) {
            searchInfo.empList[i].recentTime = recentContactsMap["p2p-" + searchInfo.empList[i].workerNo];
          }
        }
        // 修改智能助理且匹配到本地智能助理数据则添加
        if (aiObj.value.workerNo) {
          let aiIndex = searchInfo.empList.findIndex(item => {return item.workerNo == aiObj.value.workerNo});
          let localAiItem = store.getters.getPersons(aiObj.value.workerNo);
          if (aiIndex != -1) {
            searchInfo.empList[aiIndex] = localAiItem;
          } else if (new RegExp(regReplace(val), "i").test(aiObj.value.name)) {
            searchInfo.empList.unshift(localAiItem);
          }
        }
        // 最近联系人按照打开会话时间排序
        searchInfo.empList = searchInfo.empList.sort((a, b) => {return (b.recentTime || 0) - (a.recentTime || 0)});
      }
      if (searchInfo.phoneList) {
        for (let i = 0; i < searchInfo.phoneList.length; i++) {
          setUserBaseInfo(searchInfo.phoneList[i]);
        }
      }
      // 客户
      let friends = store.getters.getNimFriend({val: val});
      // 找对人
      let findFriends = store.getters.getFindPersons({val: val});
      // 群
      // 讨论组
      let teamInfo = store.getters.getTeams({val: val});
      teamInfo.group.map(item => {
        let itemId = (item.detailType == "superTeam" ? "superTeam" : "team") + "-" + item.teamId;
        // 设置联系时间
        if (recentContactsMap[itemId]) {
          item.recentTime = recentContactsMap[itemId];
        }
      });
      teamInfo.group = teamInfo.group.sort((a, b) => {return (b.recentTime || 0) - (a.recentTime || 0)});
      teamInfo.team.map(item => {
        let itemId = (item.detailType == "superTeam" ? "superTeam" : "team") + "-" + item.teamId;
        // 设置联系时间
        if (recentContactsMap[itemId]) {
          item.recentTime = recentContactsMap[itemId];
        }
      });
      teamInfo.team = teamInfo.team.sort((a, b) => {return (b.recentTime || 0) - (a.recentTime || 0)});
      // 服务号
      // 订阅号
      let subAndSer = store.getters.getSubAndSer({val: val});
      setSearchList(deepClone({
        customer: friends,
        empList: searchInfo.empList,
        group: teamInfo.group,
        team: teamInfo.team,
        findFriends: findFriends,
        phoneList: searchInfo.phoneList,
        sub: subAndSer.sub,
        ser: subAndSer.ser,
      }));
      // 支持搜索群助手、群通知、订阅号
      let otherListInfo = [{
        name: "群助手",
        workerNo: config.helperAccount,
        avatar: "/img/default/qzs.png",
        workerSpell: "qzs",
      }, {
        name: "群通知",
        workerNo: config.systemMessage,
        avatar: "/img/default/qtz.png",
        workerSpell: "qtz",
      }, {
        name: "订阅号",
        workerNo: config.subscribe,
        avatar: "/img/default/dyh.png",
        workerSpell: "dyh",
      }, {
        name: "客户咨询",
        workerNo: config.customerAccount,
        avatar: "/img/default/khzx.png",
        workerSpell: "khzx",
      }];
      let otherList = [];
      for (let i = 0; i < otherListInfo.length; i++) {
        if (new RegExp(regReplace(val), "i").test(otherListInfo[i].name) || new RegExp(regReplace(val), "i").test(otherListInfo[i].workerSpell)) {
          otherList.push(otherListInfo[i]);
        }
      }
      if (otherList.length > 0) {
        searchResList.value.push({type: 1, name: "其他", list: otherList});
      }
      // 有权限搜索全局聊天记录的人
      let userId = "********,********,********,********,********,********,********,********,********,********"
      if (userId.indexOf(userInfo.workerId) > -1 || userInfo.managerId == "********") {
        searchResList.value.push({key: "toSearch", type: 1, name: "更多", list: [{name: "搜索聊天记录", obligation: `搜索关于"${val}"的本地聊天记录`, avatar: "/img/search/icon_search_more.png", type: "toSearch"}]})
      }
      searchObj.value.flag = false;
      searchRef.value.scrollTop = 0;
      setShowListIndex();
      // 渲染完人员后再请求渲染乐文档和应用
      searchApp();
      searchDoc();
    }

    // 搜索乐文档
    function searchDoc() {
      searchObj.value.otherFlag = true;
      searchDocApi({
        title: searchVal.value,
        rows: 50,
        property: -1,
      }).then(res => {
        searchObj.value.otherFlag = false;
        searchObj.value.flag = false;
        if (res.success) {
          setSearchList(deepClone({
            doc: res.data.list,
          }));
          setShowListIndex();
          if (searchResList.value?.[searchObj.value.key]?.key == "doc") {
            nextTick(() => {
              searchRef.value.scrollTop = 0;
            });
          }
        }
      });
    }

    // 搜索应用
    function searchApp() {
      // 需要有内容才搜索
      if (!searchVal.value) {
        searchObj.value.otherFlag = false;
        searchObj.value.flag = false;
        setSearchList(deepClone({
          app: [],
        }));
        return;
      }
      searchObj.value.otherFlag = true;
      let list = store.getters.getAppList({value: searchVal.value});
      searchObj.value.otherFlag = false;
      searchObj.value.flag = false;
      setSearchList(deepClone({
        app: list,
      }));
      setShowListIndex();
      if (searchResList.value?.[searchObj.value.key]?.key == "doc") {
        nextTick(() => {
          searchRef.value.scrollTop = 0;
        });
      }
    }

    // 设置展示列下标
    function setShowListIndex(index) {
      // 设置显示列表
      if (searchObj.value.key == -1) {
        searchObj.value.list = searchResList.value;
      } else {
        searchObj.value.list = [searchResList.value[searchObj.value.key]];
      }
      // 设置显示下标
      let searchResListIndex = 0;
      let returnItem = {};
      let returnKey = "";
      let thisList = searchObj.value.list;
      for (let i = 0; i < thisList.length; i++) {
        if (thisList[i].type == 1) {
          for (let j = 0; j < thisList[i].list.length; j++) {
            if (searchObj.value.key == -1 && !thisList[i].hideTab && !thisList[i].show && j >= 5) {
              break;
            }
            thisList[i].list[j].showIndex = searchResListIndex;
            if (index == searchResListIndex) {
              returnItem = thisList[i].list[j];
              returnKey = thisList[i].key;
            }
            searchResListIndex++;
          }
        } else if (thisList[i].type == 2) {
          for (let j = 0; j < thisList[i].list.length; j++) {
            for (let k = 0; k < thisList[i].list[j].length; k++) {
              if (searchObj.value.key == -1 && !thisList[i].hideTab && !thisList[i].show && thisList[i].list[j][k].searchNum >= 5) {
                break;
              }
              thisList[i].list[j][k].showIndex = searchResListIndex;
              if (index == searchResListIndex) {
                returnItem = thisList[i].list[j][k];
                returnKey = thisList[i].key;
              }
              searchResListIndex++;
            }
          }
        }
      }
      allResNum.value = searchResListIndex;
      return {key: returnKey, item: returnItem};
    }

    // 打开会话
    function openChat(item, key, e) {
      // 搜索打开会话用户双击习惯延迟判定
      if (e) {
        stopPropagation();
      }
      // 清除聚焦定时器
      clearTimeout(searchObj.value.focusTimer);
      if (key) {
        item.localKey = key;
      }
      switch (key) {
        case "toSearch":
          // 跳转搜索
          if (!item.localValue) {
            item.localValue = searchVal.value;
          }
          // 设置全局设置搜索关键词
          emitMsg("msg", {type: "global", setGlobal: 1, info: {searchVal: item.localValue}});
          // 打开子窗口
          emitMsg("msg", {type: "window", newWin: 1, name: "searchMsg", width: 1100, height: 700, path: "child/searchMsg?val=" + item.localValue});
          break;
        case "doc":
          // 跳转乐文档
          item.localValue = item.id;
          let urlJson = store.getters.getLinkUrlJson({
            type: "doc",
            doc: {
              padId: item.padId,
              docId: item.id,
            }
          });
          store.dispatch("setOpenWindow", [urlJson.url, urlJson.frameName]);
          break;
        case "app":
          // 跳转应用
          let thisUrl = item.url;
          item.localValue = thisUrl;
          toAppUrl(thisUrl);
          break;
        case "ai":
          toAppUrl(`${config[config.env].jjsHome}/lyj-front/lyj-ai-search/#/ls/index`);
          break;
        default:
          let id = "";
          let scene = "p2p-";
          if (item == "ai") {
            id = aiObj.value.workerNo;
          } else if (item == "xl") {
            id = config[config.env].robotEmpNo;
          } else if (item == "self") {
            id = userInfo.workerNo;
          } else if (item && item.workerNo) {
            id = item.workerNo;
          } else if (item && item.aNumber) {
            id = item.aNumber;
          } else if (item && item.teamId) {
            if (item.detailType == "superTeam") {
              scene = "superTeam-";
            } else {
              scene = "team-";
            }
            id = item.teamId
          }
          store.dispatch("setCurrentSession", {id: scene + id, type: "open"});
          if (item && typeof item != "string" && Object.keys(item).length > 0) {
            item.localValue = scene + id;
          }
          break;
      }
      searchVal.value = "";
      toggleShow(1);
      // 设置本地搜索记录
      if (item && item.localValue) {
        store.commit("setLocalSearch", item);
      }
    }

    // 选择上下元素 key 1上2下
    function selItem(key) {
      if (key == 1) {
        if (searchIndex.value > 0) {
          searchIndex.value--;
        } else {
          searchIndex.value = allResNum.value - 1;
        }
      } else if (key == 2) {
        if (searchIndex.value < allResNum.value - 1) {
          searchIndex.value++;
        } else {
          searchIndex.value = 0;
        }
      }
      nextTick(() => {
        scrollLi(searchRef.value, ".search-li-box");
      });
    }

    // 确定选择
    function doSel() {
      let obj = setShowListIndex(searchIndex.value);
      let item = obj.item;
      // 获取当前选中的key
      let currentKey = obj.key;
      if (searchResList.value[searchObj.value.key] && searchResList.value[searchObj.value.key].key) {
        currentKey = searchResList.value[searchObj.value.key].key;
      }
      // 存在结果再打开
      if (item && Object.keys(item).length > 0) {
        openChat(item, currentKey);
      }
    }

    // 显示用户详情弹窗
    let showUserInfoTimer = "";

    function showUserInfo(account, e, index) {
      if (!account) {
        showUserMethods(account, e, index);
        if (showUserInfoTimer) {
          clearTimeout(showUserInfoTimer);
          showUserInfoTimer = "";
        }
        return;
      }
      showUserInfoTimer = debounce({
        timerName: "showUserInfo",
        time: 400,
        fnName: () => {
          showUserMethods(account, e, index);
        }
      })
    }

    function showUserMethods(account, e, index) {
      let showTop = 0;
      let showLeft = 0;
      if (e && e.target) {
        let bounds = getBounding(e.target);
        showTop = bounds.top;
        showLeft = bounds.left + 200;
      }
      store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: account, selUserElm: e, hover: true, showTop: showTop, showLeft: showLeft}});
      if (index != null) {
        searchIndex.value = index;
      }
    }

    // 创建讨论组
    function addGroup() {
      store.commit("setEmit", {type: "inviteBox", value: {type: 1, time: Date.now()}});
    }

    // 获取人员头像
    function getAvatar(item) {
      let avatar = item.avatar;
      // 设置人员信息
      let thisPerson = store.getters.getPersons(item.workerNo);
      if (thisPerson.workerId) {
        avatar = thisPerson.avatar;
      }
      return avatar;
    }

    // 聚焦输入框
    function searchFocus(key) {
      if (key == 1) {
        searchObj.value.mouseDown = true;
      }
      searchObj.value.focusTimer = setTimeout(() => {
        searchInputRef.value.focus();
      }, 100);
    }

    // 阻止点击穿透
    function stopPropagation() {
      store.commit("setIsClick", false);
    }

    // 跳转应用url
    function toAppUrl(url, type) {
      if (url == "add") {
        // 跳转添加工作台
        router.push({path: "/index/more", query: {type: "top", time: Date.now()}})
      } else {
        openAppUrl(url);
        if (type == "update" && isUpdate.value) {
          // 取消更新红点
          setLocalUpdateObj({notTips: true});
          isUpdate.value = false;
        }
      }
    }

    getUpdate();
    watch(() => store.state.config.serverVersion,
      (newValue, oldValue) => {
        getUpdate();
      }, {
        deep: true
      }
    );

    // 监听智能助理变化
    watch(() => store.state.aiObj,
      (newValue, oldValue) => {
        aiObj.value = deepClone(newValue);
      }, {
        deep: true
      }
    );

    // 监听loading状态
    watch(() => [searchObj.value.otherFlag, searchObj.value.flag],
      (newValue, oldValue) => {
        getLoadingStatus();
      }, {
        deep: true
      }
    );

    // 是否有更新,且存在更新提示
    function getUpdate() {
      let {version, serverVersion} = store.getters.getConfig;
      isUpdate.value = compareVersion(version, serverVersion) && !setLocalUpdateObj().notTips;
    }

    // 跳转转需求平台
    function toDemandOrBug() {
      let frameName = "jjsHome";
      let url = `${config[config.env].jjsHome}/lyj-front/tapd-manage/?hideSideBar=true#/createOrder?source=15`;
      store.dispatch("setOpenWindow", [url, frameName, "demandOrBug"]);
    }

    // 判断是否显示loading状态
    function getLoadingStatus() {
      let key = searchResList.value?.[searchObj.value.key]?.key;
      if (((key == "doc" || key == "app") && !searchObj.value.otherFlag) || (!(key == "doc" || key == "app") && !searchObj.value.flag)) {
        searchObj.value.resultFlag = true;
      } else {
        searchObj.value.resultFlag = false;
      }
    }

    function getAppIcon(item) {
      let src = item.iconUrl || "-";
      if (!/https?:/.test(src)) {
        src = config[config.env].jjsHome + src;
      }
      return src;
    }

    // icon加载失败
    function errorIcon(e) {
      e.target.src = "/img/workbench/icon_app_default_new.png";
      e.target.onerror = "";
    }

    return {
      searchVal,
      isShowSearch,
      searchResList,
      searchRef,
      searchIndex,
      searchInputRef,
      allResNum,
      searchObj,
      topList,
      isUpdate,
      aiObj,
      getAppIcon,
      errorIcon,

      searchInput,
      openChat,
      toggleShow,
      selItem,
      doSel,
      showUserInfo,
      avatarError,
      getAvatar,
      selTab,
      searchFocus,
      getHighlight,
      htmlEscapeAll,
      addGroup,
      stopPropagation,
      toAppUrl,
      toDemandOrBug
    }
  }
}
</script>
<style scoped lang="scss">
.style-1 {
  .chat-search {
    left: 10px;

    .search-box {
      input {
        width: 230px !important;
      }
    }
  }
}

.chat-search {
  width: 100%;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);

  ::v-deep(.highlight) {
    font-weight: bold;
    color: #000000;
  }

  .icon-search-tab {
    background-image: url("/img/search/icon_search_tab.png");
    background-repeat: no-repeat;
    background-size: 212px 30px;
  }

  .search-header {
    display: flex;
    align-items: center;

    .search-box {
      position: relative;

      input {
        width: 240px;
        height: 30px;
        border-radius: 4px;
        border: 1px solid transparent;
        padding: 0 50px 0 30px;
        color: #000000;
        background-color: #FFFFFF;
        background-image: url("/img/search/icon_search.png");
        background-repeat: no-repeat;
        background-size: 16px 16px;
        background-position: 7px center;
        font-size: 13px;

        &:hover,
        &:focus {
          border: 1px solid #666666;
        }
      }

      .icon-close {
        position: absolute;
        top: 50%;
        right: 39px;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        background-image: url("/img/search/close.png");
        background-repeat: no-repeat;
        background-size: 12px 12px;
        cursor: pointer;
      }

      .icon-xl {
        position: absolute;
        top: 50%;
        right: 8px;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        background-position: 0 0;
        cursor: pointer;

        &:hover {
          background-position: -24px 0;
        }
      }
    }

    .icon-ai,
    .icon-bug,
    .icon-update,
    .icon-add,
    .icon-self {
      position: relative;
      width: 30px;
      height: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      border-radius: 4px;

      &.icon-add {
        background-position: -92px 0;
        margin-left: 12px;

        &:hover {
          background-position: -122px 0;
        }
      }

      &.icon-self {
        background-position: -152px 0;
        margin-left: 10px;

        &:hover {
          background-position: -182px 0;
        }
      }

      &.icon-update {
        background-image: url("/img/search/icon_update.png");
        background-size: 60px 30px;
        margin-left: 10px;

        &.point {
          &:before {
            content: "";
            position: absolute;
            top: 0px;
            right: 0px;
            width: 8px;
            height: 8px;
            background: #f74b32;
            border-radius: 50%;
          }
        }

        &:hover {
          background-position: -30px 0;
        }
      }

      &.icon-bug {
        background-image: url("/img/search/icon_bug.png");
        background-size: 60px 30px;
        margin-left: 10px;

        &:hover {
          background-position: -30px 0;
        }
      }

      &.icon-ai {
        background-image: url("/img/search/icon_ai.png");
        background-size: 60px 30px;
        margin-left: 10px;

        &:hover {
          background-position: -30px 0;
        }
      }

      .label {
        position: absolute;
        top: 0;
        left: 26px;
        height: 12px;
      }
    }
  }

  .search-content {
    position: absolute;
    top: 34px;
    left: 0;
    width: 490px;
    height: 428px;
    overflow-y: auto;
    background: #FFFFFF;
    box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
    border: 1px solid #E0E0E0;
    z-index: 10;

    .search-tab-ul {
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 10px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #E0E0E0;
      background: #FFFFFF;

      li {
        height: 36px;
        line-height: 36px;
        padding: 0 6px;
        color: #666666;
        position: relative;
        cursor: pointer;

        &:first-child {
          margin-left: 0;
        }

        &:nth-child(n+12) {
          display: none;
        }

        &:hover {
          color: $styleColor;
        }

        &.sel {
          color: $styleColor;
          font-weight: bold;

          &:after {
            content: "";
            width: 12px;
            height: 2px;
            background: $styleColor;
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
          }
        }
      }
    }

    .search-ul-box {
      margin-top: 36px;
      height: calc(100% - 36px);
      overflow-y: auto;

      .search-content-box {
        .search-ul {
          .content-header {
            line-height: 17px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 12px 4px 16px;
            font-weight: bold;
            color: #000000;

            .toggle {
              position: relative;
              cursor: pointer;
              color: #666666;
              font-weight: normal;
              padding-right: 8px;

              &:hover {
                color: $styleColor;

                &:after {
                  border-top: 1px solid $styleColor;
                  border-right: 1px solid $styleColor;
                }
              }

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 0;
                width: 5px;
                height: 5px;
                transform: translateY(-50%) rotate(45deg);
                border-top: 1px solid #666666;
                border-right: 1px solid #666666;
              }
            }
          }

          .search-li-title {
            line-height: 28px;
            padding-left: 16px;
            color: #666666;

            &:hover {
              background: rgba(195, 195, 196, .3);
            }
          }

          .search-li {
            .search-li-box {
              display: flex;
              align-items: center;
              width: 100%;
              padding: 7px 16px;

              &.curr {
                background: $styleBg1;
              }

              &:hover {
                background: $styleBg1Hover;
              }

              .search-li-content {
                display: flex;
                align-items: center;
                width: 100%;
              }

              .avatar-box {
                position: relative;
                width: 26px;
                height: 26px;
                margin-right: 8px;
                overflow: hidden;
                flex-shrink: 0;
                border-radius: 50%;

                .avatar {
                  width: 100%;

                  &.center {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                  }
                }
              }

              .info-box {
                width: calc(100% - 28px);

                &.info-line-box {
                  display: flex;
                  align-items: center;

                  .name {
                    font-size: 14px;
                    max-width: 40%;
                  }

                  .intr {
                    max-width: 60%;
                    margin-left: 6px;
                  }
                }

                &.doc-box {
                  display: flex;
                  align-items: center;
                  font-size: 14px;

                  .doc-title {
                    max-width: 60%;
                  }

                  .doc-intr {
                    max-width: 40%;
                    margin-left: 8px;
                    color: #999999;
                  }
                }

                .name {
                  max-width: 100%;
                  font-size: 14px;

                  &.resign {
                    color: $styleColor;

                    ::v-deep(.highlight) {
                      color: $styleColor;
                    }
                  }
                }

                .intr {
                  max-width: 100%;
                  color: #999999;
                }
              }
            }
          }
        }
      }

      .search-status-box {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .loading-img {
          width: 32px;
        }

        .empty-img {
          width: 180px;
        }

        .show-text {
          color: #666666;
          margin-top: 10px;
        }
      }
    }
  }

  .top-tab-ul {
    position: fixed;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;

    .top-tab-li {
      width: 30px;
      height: 30px;
      margin-right: 10px;
      background-image: url(#{"/img/index/tab/tab_top1.png"});
      background-size: 30px 30px;
      background-repeat: no-repeat;
      cursor: pointer;
      position: relative;

      &.top-tab-li-add {
        background-image: url(#{"/img/index/tab/tab_top_add.png"});

        &:hover {
          background-image: url(#{"/img/index/tab/tab_top_add_sel.png"}) !important;
        }
      }

      @for $i from 1 to 10 {
        &.top-tab-li-#{$i} {
          background-image: url("/img/index/tab/tab_top#{$i}.png") !important;

          &:hover {
            background-image: url("/img/index/tab/tab_top#{$i}_sel.png") !important;
          }
        }
      }

      &:hover {
        background-image: url(#{"/img/index/tab/tab_top1_sel.png"});
      }
    }
  }
}
</style>