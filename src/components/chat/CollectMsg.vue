<template>
  <div :class="['collect-msg',selMore?'':'not-sel-more',selCollectCount>0?'sel-status':'']" ref="collectMsgRef" @scroll="scrollCollect">
    <div v-if="collectStatus"></div>
    <div v-else-if="collectList.length==0" class="collect-details-none">
      <img src="/img/content_none.png"/>
      <div>暂无数据</div>
    </div>
    <!--公共话术-->
    <ul v-else-if="collectType==0" class="collect-details-ul public-collect-details-ul">
      <li class="collect-details-li" v-for="(item,key) in collectList" :key="item.collectId">
        <!--问题-->
        <div class="details-question">
          <span class="question-label">问</span>
          <span class="question-text">{{ item.speechContent }}</span>
        </div>
        <div :class="['details-idea-box',key1>0?'mt-other':'']" v-for="(item1,key1) in item.speechList" :key="item.id">
          <!--思路-->
          <div class="details-idea">
            <i class="icon-idea"></i>
            <span class="idea-text">回答思路：{{ item1.speechContent }}</span>
          </div>
          <!--答复-->
          <div class="details-sel-box show-hover" v-for="(item2,key2) in item1.speechList" :key="item2.id" @click.stop="clickChangeSelCollectList(1,item2)">
            <span class="sel-box" v-if="selMore">
              <i :class="['sel-box-i', selCollectMap[item2.id]?'sel':'']"></i>
            </span>
            <span class="answer-text textEls2" :title="item2.speechContent">答：{{ item2.speechContent }}</span>
            <span class="answer-btn" @click.stop="showEditItem(item2,collectType)">编辑/发送</span>
          </div>
        </div>
      </li>
    </ul>
    <!--我的话术-->
    <ul v-else-if="collectType==1" class="collect-details-ul">
      <li class="collect-details-li show-hover" v-for="(item,key) in collectList" :key="item.collectId" @click="clickChangeSelCollectList(1,item)">
        <!--答复-->
        <div class="details-sel-box">
          <span class="sel-box" v-if="selMore">
            <i :class="['sel-box-i', selCollectMap[item.collectId]?'sel':'']"></i>
          </span>
          <span class="answer-text textEls2" v-html="getPrimaryMsg(item, 4)"></span>
          <span class="answer-btn" @click.stop="showEditItem(item,collectType)">编辑/发送</span>
        </div>
      </li>
    </ul>
    <!--全部收藏-->
    <ul v-else-if="collectType==2" class="collect-details-ul">
      <li :class="['collect-details-li','show-hover',item.collectId==viewData.collectId?'show':'']" v-for="(item,key) in collectList" :key="item.collectId"
          @click="showMsg(item,collectType)" @contextmenu="setMenu($event, item)">
        <div class="details-sel-box">
          <span class="sel-box" @click.stop="clickChangeSelCollectList(1,item)" v-if="selMore">
            <i :class="['sel-box-i', selCollectMap[item.collectId]?'sel':'']"></i>
          </span>
          <div class="details-msg-box">
            <!--文本-->
            <div v-if="item.type=='text'" class="msg-text textEls2" v-html="getShowText(item)"></div>
            <!--图片-->
            <div v-else-if="item.type=='image'" class="msg-img" @click="toViewer">
              <img src="/img/image_default.png" :data-src="item.file.url" :data-ext="item.file.ext" @load="loadImage($event,item)" :onerror="errorImage.bind(this,item)">
            </div>
            <!--语音-->
            <div v-else-if="item.type=='audio'" class="msg-text">[语音消息]时长{{ Math.round((item.file.dur) / 1000) == 0 ? 1 : Math.round((item.file.dur) / 1000) }}s</div>
            <!--视频-->
            <div v-else-if="item.type=='video'" class="msg-file">
              <div class="file-icon-box">
                <img class="file-icon" src="/img/image_default.png" :data-src="item.file.videoPic" @load="loadImage($event,item)" :onerror="errorImage.bind(this,item)">
                <img class="file-play" src="/img/index/msg/video_play.png" alt="">
              </div>
              <div class="file-content">
                <div class="file-name" :title="item.file.name">
                  <span class="name textEls">{{ item.file.showName || item.file.name }}</span>
                  <span class="ext textEls" v-if="item.file.ext">{{ "." + item.file.ext }}</span>
                </div>
                <div class="file-size">{{ dealMem(item.file.size) }}</div>
              </div>
            </div>
            <!--文件-->
            <div v-else-if="item.type=='file'" class="msg-file">
              <img class="file-icon" :src="getFileIcon(item.file.ext)" alt="">
              <div class="file-content">
                <div class="file-name" :title="item.file.name">
                  <span class="name textEls">{{ item.file.showName || item.file.name }}</span>
                  <span class="ext textEls" v-if="item.file.ext">{{ "." + item.file.ext }}</span>
                </div>
                <div class="file-size">{{ dealMem(item.file.size) }}</div>
              </div>
            </div>
            <!--自定义消息-->
            <div v-else-if="item.type=='custom'" class="msg-file">
              <img class="file-icon" :src="getFileIcon()" alt="">
              <div class="file-content">
                <div class="file-name">
                  <span class="name textEls">自定义消息</span>
                </div>
                <div class="show-text textEls" v-html="getPrimaryMsg(item, 4)"></div>
              </div>
            </div>
            <!--位置-->
            <div v-else-if="item.type=='geo'" class="msg-file">
              <img class="file-icon notCopy selNone" alt="" :src="getFileIcon('geo')">
              <div class="file-content">
                <div class="file-name">
                  <span class="name textEls">位置分享</span>
                </div>
                <div class="file-size">{{ item.geo.title }}</div>
              </div>
            </div>
            <!--其他类型收藏-->
            <div v-else-if="item.type=='collectElse'" class="msg-file">
              <img class="file-icon" :src="getFileIcon()" alt="">
              <div class="file-content">
                <div class="file-name">
                  <span class="name textEls">其他类型</span>
                </div>
                <div class="show-text textEls" v-html="getShowText(item)"></div>
              </div>
            </div>
            <!--笔记-->
            <div v-else-if="item.type=='collectNote'" class="msg-file">
              <img class="file-icon" :src="getFileIcon()" alt="">
              <div class="file-content">
                <div class="file-name">
                  <span class="name textEls">笔记</span>
                </div>
                <div class="show-text textEls" v-html="getShowText(item)"></div>
              </div>
            </div>
            <!--其他-->
            <div v-else class="msg-text">格式错误，请联系管理员</div>
          </div>
          <!--搜索收藏编辑发送按钮-->
          <div v-if="!selMore" class="btn-box-details">
            <div v-if="item.type=='text'||item.type=='image'||item.type=='custom'||item.type=='collectElse'||item.type=='collectNote'" class="btn-box">
              <span class="btn w64" @click.stop="showEditItem(item,collectType)">编辑/发送</span>
            </div>
            <div v-else class="btn-box">
              <span class="btn" @click.stop="showMsgList([item])">查看</span>
              <span class="btn" @click.stop="forwardItem([item])">发送</span>
            </div>
          </div>
        </div>
        <div :class="['details-intr',item.type=='collectElse'||item.type=='collectNote'?'show-text-intr':'']">
          <span class="textEls">来源：{{ item.sourceName }}</span>
          <span class="details-intr-time">{{ dateFormat(new Date(item.updateTime), 'yy/MM/dd HH:mm') }}</span>
        </div>
      </li>
    </ul>
    <!--查看消息弹窗-->
    <LyDialog newClass="dialog-manger-box dialog-move-box" title="内容预览" :width="500" :height="400" :closeOnClickModal="true" :visible="viewData.msgList.length>0"
              @close="showMsgList([])" :foot="false">
      <div class="main-dialog-box">
        <ChatMsg :searchList="viewData.msgList" msgType="6"></ChatMsg>
      </div>
    </LyDialog>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import {dateFormat, getFileIcon, dealMem, showMenu, debounce, strToHtml, forwardCollectMsgs, getChildWin, deepClone, openViewer} from "@utils";
import ChatMsg from "@comp/chat/ChatMsg";
import {alert, loading, toast} from "@comp/ui";
import LyDialog from "@comp/ui/comps/LyDialog";

export default {
  name: "CollectMsg",
  components: {ChatMsg, LyDialog},
  props: {
    // type-1收藏页面-2聊天窗口搜索
    type: {
      type: String,
      default: ""
    },
    // 消息类型-0公共话术-1我的话术-2收藏
    collectType: {
      type: Number,
    },
    // 收藏消息是否加载中
    collectStatus: {
      type: Boolean,
    },
    // 是否显示多选状态
    selMore: {
      type: Boolean,
      default: false,
    },
    // 收藏消息列表
    collectList: {
      type: Array,
      default: [],
    },
    // 分组列表
    collectClassify: {
      type: Array,
      default: [],
    },
    // 显示收藏内容
    showMsgItem: {
      type: Function,
    },
    // 移除收藏内容
    removeContent: {
      type: Function,
    },
    // 移动收藏内容
    moveCollectContent: {
      type: Function,
    },
    // 切换选择
    changeSelCollectList: {
      type: Function,
    },
    // 选择的内容列表
    selCollectMap: {
      type: Object,
      default: {}
    },
    // 加载更多
    loadMore: {
      type: Function
    },
    sessionInfo: {
      type: Object,
      default: {}
    }
  },
  setup(props, ctx) {
    const store = useStore();
    let collectMsgRef = ref();
    // 视图参数
    let viewData = ref({
      collectId: "",// 当前选中的收藏id
      msgList: [],// 显示的消息列表
    });

    // 收藏多选数量-多选的时候不允许右键和编辑
    let selCollectCount = ref(0);
    watch(() => store.state.emit.selCollectCount,
      (newValue, oldValue) => {
        selCollectCount.value = newValue;
      }, {
        deep: true
      }
    );

    // 获取消息简要
    function getPrimaryMsg(msg, type) {
      return store.getters.getPrimaryMsg({msg: msg, primaryType: type});
    }

    // 显示消息详情
    function showMsg(item) {
      nextTick(() => {
        item = item || props.collectList[0];
        viewData.value.collectId = item?.collectId;
        if (props.showMsgItem) {
          props.showMsgItem(item);
        }
      })
    }

    // 点击显示编辑内容
    function showEditItem(item, editType) {
      if (selCollectCount.value > 0) {
        return;
      }
      let thisItem = item;
      if (editType == 0) {
        viewData.value.collectId = item.id;
        thisItem = {collectId: item.id, parentId: item.parentId, type: "text", text: item.speechContent};
      }
      let sessionInfo = "";
      // 快捷回复打开的编辑窗口
      if (props.sessionInfo.to) {
        sessionInfo = props.sessionInfo;
      }
      store.commit("setEmit", {type: "showComponents", value: {type: 2, key: 1, editItem: deepClone(thisItem), editType: editType, sessionInfo: sessionInfo}});
    }

    // 右键菜单
    function setMenu(e, item) {
      if (props.type != 1 || selCollectCount.value > 0) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      let menu = {};
      let menuList = [];

      // 收藏列表
      let collSubmenu = [];
      for (let i = 0; i < props.collectClassify.length; i++) {
        let item1 = props.collectClassify[i];
        // 去除全部分组
        if (item1.id !== "") {
          collSubmenu.push({
            label: item1.name,
            click: function () {
              props.moveCollectContent([item], item1.id);
            }
          })
        }
      }

      menuList.push({
        label: "移动到", submenu: showMenu(collSubmenu),
      });

      menuList.push({
        label: "转发", click: function () {
          forwardItem([item]);
        }
      });

      menuList.push({
        label: "删除", click: function () {
          props.removeContent([item]);
        }
      });

      if (menuList.length > 0) {
        menu = showMenu(menuList)
        menu.popup(e.x, e.y);
      }

      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    // 加载图片
    function loadImage(e, item) {
      let thisElm = e.target;
      let thisElmSrc = thisElm.getAttribute("data-src");
      if (/data:image\//.test(thisElm.src)) {
        return;
      }
      let timerKey = `list-${item.collectId}`;
      // 加载图片
      store.dispatch("setReloadImage", {thisElm: thisElm, thisElmSrc: thisElmSrc, timerKey: timerKey, time: 12 * 1000, type: 2, notChange: true});
    }

    // 图片加载失败
    function errorImage(item, e) {
      let thisElm = e.target;
      let timerKey = `list-${item.collectId}`;
      // 加载失败如果存在定时器继续加载
      if (store.getters.getImageLoadTimer[timerKey]) {
        thisElm.setAttribute("pre-src", thisElm.src);
        thisElm.setAttribute("pre-time", Date.now());
        thisElm.setAttribute("src", `/img/image_default.png?reloadFlag=true`);
      } else {
        // 停止加载图片
        thisElm.setAttribute("src", `/img/image_reload_min.png`);
      }
    }

    // 滚动列表
    function scrollCollect(e) {
      if (!props.loadMore) {
        return;
      }
      debounce({
        timerName: "scrollCollect",
        time: 100,
        fnName: () => {
          if (e.target.scrollTop + e.target.clientHeight > e.target.scrollHeight - 10) {
            // 加载更多
            props.loadMore(props.collectType);
          }
        }
      });
    }

    // 点击多选
    function clickChangeSelCollectList(type, item) {
      if (!item.collectId) {
        item.collectId = item.id;
      }
      if (props.selMore) {
        props.changeSelCollectList(type, item);
      }
    }

    // 获取显示文本
    function getShowText(item) {
      let text = buildEmoji(strToHtml(item.text || item.oHtml, true)) || "";
      return text.replace(/\n|<br>|<\/br>/g, " ");
    }

    // 显示消息内容
    function showMsgList(list) {
      viewData.value.msgList = list;
    }

    async function forwardItem(list) {
      loading();
      let forwardRes = await forwardCollectMsgs(list, 3, props.sessionInfo);
      loading().hide();
      if (!forwardRes.success) {
        toast({title: forwardRes.errorMsg, type: 2});
        return;
      }
      if (props.sessionInfo.to) {
        alert({
          title: "提示",
          content: `<div>确认将内容发送至 <b>乐有家网客户 | ${props.sessionInfo.name || props.sessionInfo.detailInfo.name}</b></div>`,
          done: async confirmType => {
            if (confirmType == 1) {
              // 关闭搜索弹窗
              remote.store.getters.getNim.forwardMsg({scene: props.sessionInfo.scene, to: props.sessionInfo.to, msg: forwardRes.thisMsg}).then(res => {
                remote.store.dispatch("sendMsgDone", res);
                if (props.sessionInfo.close && getChildWin("collect")) {
                  getChildWin("collect").close();
                  remote.store.dispatch("setChildWin", {type: "del", name: "collect"});
                }
              });
            }
          }
        });
      }
    }

    // 打开查看大图/视频
    function toViewer(e) {
      if (props.type != 2) {
        return;
      }
      let imgList = [{src: e.target.src, dataSrc: e.target.src, w: e.target.naturalWidth, h: e.target.naturalHeight, ext: e.target.dataset.ext}];
      let thisIndex = 0;
      openViewer(imgList, thisIndex, e.target.naturalWidth, e.target.naturalHeight);
    }

    return {
      collectMsgRef,
      viewData,
      selCollectCount,

      dateFormat,
      getFileIcon,
      dealMem,
      getPrimaryMsg,
      showMsg,
      showEditItem,
      setMenu,
      loadImage,
      errorImage,
      scrollCollect,
      clickChangeSelCollectList,
      strToHtml,
      getShowText,
      showMsgList,
      forwardItem,
      toViewer,
    }
  }
}
</script>
<style scoped lang="scss">
.collect-msg {
  width: 100%;
  height: 100%;
  overflow-y: auto;

  :deep(.dialog-move-box) {
    .content {
      padding-right: 0;

      .main-dialog-box {
        height: 318px;
        padding-right: 16px;
        overflow-y: auto;
      }
    }
  }

  &.not-sel-more {
    .details-question,
    .details-idea,
    .details-sel-box,
    .details-intr {
      padding-left: 16px !important;
    }

    .details-msg-box {
      max-width: calc(100% - 80px);
    }
  }

  &.sel-status {
    .answer-btn {
      color: #BFBFBF !important;
    }
  }

  .collect-details-none {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #666666;

    img {
      width: 160px;
      margin: 100px 0 10px;
    }
  }

  .collect-details-ul {

    &.public-collect-details-ul {
      .collect-details-li {
        padding-right: 0;
      }

      .details-sel-box {
        padding: 4px 20px 4px 0;
      }
    }

    .show-hover {
      &.show,
      &:hover {
        background: $styleBg1Sel;
      }
    }

    .collect-details-li {
      position: relative;
      padding: 10px 16px 10px 0;

      &:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 16px;
        width: calc(100% - 32px);
        height: 1px;
        background: #E7E7E7;
      }

      .details-question {
        display: flex;
        line-height: 18px;
        padding: 0 16px 0 40px;
        margin-bottom: 6px;
        font-size: 14px;
        color: #333333;
        font-weight: bold;
        word-break: break-all;

        .question-label {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-shrink: 0;
          width: 14px;
          height: 14px;
          margin: 2px 4px 0 0;
          background: $styleColor;
          font-size: 10px;
          font-weight: normal;
          color: #FFFFFF;
          border-radius: 2px;
        }
      }

      .details-idea-box {
        &.mt-other {
          margin-top: 11px;
        }
      }

      .details-idea {
        display: flex;
        line-height: 16px;
        padding: 0 16px 0 40px;
        margin-bottom: 4px;
        color: #999999;
        word-break: break-all;

        .icon-idea {
          width: 14px;
          height: 16px;
          margin-right: 4px;
          background: url("/img/collect/icon_idea.png") no-repeat center;
          background-size: 14px 14px;
          flex-shrink: 0;
        }
      }

      .details-sel-box {
        width: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;

        .sel-box {
          display: flex;
          width: 40px;
          flex-shrink: 0;
          padding-left: 16px;

          .sel-box-i {
            flex-shrink: 0;
            width: 14px;
            height: 14px;
            position: relative;
            border: 1px solid #DDDDDD;
            border-radius: 2px;

            &.sel {
              border: 1px solid $styleColor;
              background: $styleColor;

              &:after {
                content: "";
                width: 8px;
                height: 3px;
                border: 2px solid #FFFFFF;
                border-top: transparent;
                border-right: transparent;
                position: absolute;
                top: 2px;
                left: 1px;
                transform: rotate(-45deg);
              }
            }
          }
        }

        .details-msg-box {
          width: calc(100% - 54px);
          flex: 1;
        }

        .answer-text {
          font-size: 13px;
          flex: 1;
        }

        .msg-text {
          font-size: 13px;
          flex: 1;
          line-height: 17px;
        }

        :deep(.im-emoji) {
          width: 20px !important;
          height: 20px !important;
        }

        .answer-btn {
          margin-left: 23px;
          flex-shrink: 0;
          color: $styleColor;
        }

        .msg-img {
          width: 30px;
          height: 30px;
          overflow: hidden;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 100%;
          }
        }

        .msg-file {
          display: flex;

          .file-icon-box,
          .file-icon {
            position: relative;
            width: 34px;
            height: 34px;
            border-radius: 2px;
            margin-right: 8px;
          }

          .file-play {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 14px;
            height: 14px;
          }

          .file-content {
            max-width: calc(100% - 35px);

            .file-name {
              display: flex;
              font-size: 13px;
              margin-bottom: 2px;
            }

            .file-size,
            .show-text {
              font-size: 12px;
              color: #999999;
            }
          }
        }

        .btn-box-details {
          display: flex;

          .btn-box {
            display: flex;
            flex-shrink: 0;
            color: $styleColor;

            .btn {
              margin-left: 16px;
              text-align: right;

              &.w64 {
                width: 64px;
              }
            }
          }
        }
      }

      .details-intr {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 40px;
        color: #999999;
        line-height: 17px;
        margin-top: 8px;
      }

      .details-intr-time {
        flex-shrink: 0;
      }
    }
  }
}
</style>