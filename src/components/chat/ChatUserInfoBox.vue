<template>
  <div ref="chatUserInfoRef">
    <div class="chat-userinfo-box selAll" v-if="selUserWorkerNo && selUserInfo" @click.stop="stopPropagation()">
      <div class="my-info-header" :class="selUserInfo.styleFlag?'':'style1'">
        <div class="user-avatar-box">
          <div class="avatar-box">
            <img class="avatar" :src="selUserInfo.avatar" :onerror="avatarError.bind(this, 'p2p', selUserInfo.workerNo, '')" @click="showAvatar($event, selUserInfo)">
          </div>
        </div>
        <div class="user-name-box">
          <h5 class="textEls2" :title="selUserInfo.userShowName">{{ selUserInfo.userShowName }}</h5>
          <span v-if="selUserInfo.isAi&&selUserInfo.workerNo!=aiObj.workerNo" :class="['user-label',selUserInfo.isFree == 1?'user-label-1':'']">{{ selUserInfo.isFree == 1 ? "免费" : "付费" }}</span>
        </div>
        <template v-if="!selUserInfo.isAi">
          <ul class="user-info-box">
            <li class="info-box-li">
              <span class="info-label">
                <i>城</i><i>市：</i>
              </span>
              <span class="info-text info-text-city">{{ selUserInfo.cityName || "-" }}</span>
            </li>
            <li class="info-box-li">
              <span class="info-label">
                <i>职</i><i>位：</i>
              </span>
              <span class="info-text info-text-duty">{{ selUserInfo.dutyName || "-" }}</span>
            </li>
          </ul>
          <ul class="user-info-box">
            <li class="info-box-li">
              <span class="info-label">
                <i>上</i><i>级：</i>
              </span>
              <span class="info-text info-text-leader">{{ selUserInfo.leaderName || "-" }}</span>
            </li>
            <li class="info-box-li">
              <span class="info-label">所属管辖：</span>
              <span class="info-text info-text-region">{{ selUserInfo.region || "-" }}</span>
            </li>
          </ul>
          <ul class="user-info-box" v-show="selUserInfo.empType==2">
            <li class="info-box-li">
              <span class="info-label">所属公司：</span>
              <span class="info-text">{{ selUserInfo.compName || "-" }}</span>
            </li>
          </ul>
          <ul class="user-info-box">
            <li class="info-box-li max-info-box">
              <span class="info-label">联系方式：</span>
              <span class="info-text info-mobile info-text-mobile">{{ selUserInfo.showTelStr || "-" }}</span>
            </li>
          </ul>
          <ul class="user-info-box">
            <li class="info-box-li">
              <span class="info-label">已获勋章：</span>
              <span class="info-text">{{ selUserInfo.medalCount || 0 }}</span>
            </li>
            <li class="info-box-li" v-show="selUserInfo.levelName">
              <span class="info-label">荣誉等级：</span>
              <span class="info-text">{{ selUserInfo.levelName || 0 }}</span>
            </li>
          </ul>
        </template>
        <template v-else>
          <ul class="user-info-box">
            <li class="info-box-li">
              <span class="info-label">创建人/归属人：</span>
              <span class="info-text info-text-leader">{{ selUserInfo.leaderName || "-" }}</span>
            </li>
          </ul>
        </template>
      </div>
      <div class="my-info-content">
        <ul class="info-content-box pb2 my-medal-box-ul" v-if="selUserInfo.honourData">
          <li class="info-box-li max-info-box"><span class="info-label">荣誉勋章：</span>
            <div class="my-medal-box">
              <img class="notCopy selNone" v-for="(item,key) in selUserInfo.honourData" :src="item.medalPicUrl" :alt="item.prizeName" :key="item.id">
            </div>
          </li>
        </ul>
        <div class="cooperate-box" v-if="selUserInfo.labelDate">
          <ul class="info-content-box pb4 ability-label-box-ul">
            <li class="info-box-li max-info-box"><span class="info-label">个人成就：</span>
              <ul class="ability-label-box">
                <li v-for="(item, key) in selUserInfo.labelDate.avatar"
                    :key="key" :class="'ability-label'+(selUserInfo.styleFlag?'1':'3')">{{ item }}
                </li>
                <li v-for="(item, key) in selUserInfo.labelDate.nonAvatar"
                    :key="key" :class="'ability-label'+(selUserInfo.styleFlag?'2':'4')">{{ item }}
                </li>
              </ul>
            </li>
          </ul>
        </div>
        <div class="cooperate-box" v-show="selUserInfo.jjrStyle&&selUserInfo.cooperateInfo.workerId">
          <div class="info-content-box">
            <ul class="info-list">
              <li class="info-box-li">
                <span class="info-label">历史成交：</span>
                <span class="info-text cj-count">{{ selUserInfo.cooperateInfo.cjCount || 0 }}套</span>
              </li>
              <li class="info-box-li">
                <span class="info-label">近6月成交：</span>
                <span class="info-text six-cj-count">{{ selUserInfo.cooperateInfo.sixCjCount || 0 }}单</span>
              </li>
            </ul>
            <ul class="info-list">
              <li class="info-box-li">
                <span class="info-label">近6月速销：</span>
                <span class="info-text quick-salesNum">{{ selUserInfo.cooperateInfo.quickSalesNum || 0 }}套</span>
              </li>
              <li class="info-box-li">
                <span class="info-label">合作成交量：</span>
                <span class="info-text cooperate-count">{{ selUserInfo.cooperateInfo.cooperateCount || 0 }}个</span>
              </li>
            </ul>
          </div>
        </div>
        <div class="info-content-box">
          <div class="cooperate-box cooperate-box1" v-show="selUserInfo.jjrStyle&&selUserInfo.cooperateInfo.workerId">
            <ul class="info-list">
              <li class="info-box-li">
                <span class="info-label">行业经验：</span>
                <span class="info-text worker-year">{{ selUserInfo.cooperateInfo.workerYearStr }}</span>
              </li>
              <li class="info-box-li">
                <span class="info-label">业绩范围：</span>
                <span class="info-text grade-area">{{ selUserInfo.cooperateInfo.gradeArea || "无" }}</span>
              </li>
            </ul>
            <ul class="info-list">
              <li class="info-box-li">
                <span class="info-label">作业范围：</span>
                <span class="info-text info-intr job-scope">{{ selUserInfo.cooperateInfo.jobScope || "无" }}</span>
              </li>
            </ul>
          </div>
          <ul class="info-list">
            <li class="info-box-li max-info-box">
              <span class="info-label">{{ selUserInfo.jjrStyle ? "合作宣言：" : "职责说明：" }}</span>
              <span v-if="selUserInfo.jjrStyle" class="info-text info-intr self-introduce" :class="selUserInfo.cooperateInfo.selfIntroduce?'':'no-data'">
                {{ selUserInfo.cooperateInfo.selfIntroduce || "暂无，建议乐聊TA，聊一聊了解更多信息" }}
              </span>
              <span v-else class="info-text info-intr self-introduce" :class="selUserInfo.selfIntro?'':'no-data'">
                {{ selUserInfo.selfIntro || "暂无职责说明" }}
              </span>
            </li>
          </ul>
          <ul class="info-list" v-if="selUserInfo.otherRename">
            <li class="info-box-li max-info-box">
              <span class="info-label">其他说明：</span>
              <span class="info-text info-intr self-introduce">{{ selUserInfo.otherRename }}</span>
            </li>
          </ul>
        </div>
      </div>
      <div class="my-info-footer">
        <button v-show="!selUserInfo.isAi" class="btn-system" :disabled="!selUserInfo.hrAuthorityFlag" @click="toEmpSystem(selUserInfo.workerId)">人事档案</button>
        <button v-if="selUserInfo.workerNo==aiObj.workerNo" class="btn-system" @click="openLink(aiObj.userUrl)">助理设置</button>
        <button class="btn-tochat" :disabled="selUserInfo.isAi&&!selUserInfo.hasPermission" @click="openChat('p2p-'+selUserInfo.workerNo,$event)">聊天</button>
      </div>
    </div>
  </div>
</template>
<script>
import {ref, watch, nextTick} from "vue";
import {useStore} from "vuex";
import {avatarError, getUserTel, deepClone, linkFormat, setUserBaseInfo, openViewer} from "@utils";
import {queryCooperateApi, queryHrAuthorityApi, queryHonourDataListApi, updateUsersApi} from '@utils/net/api'

export default {
  name: "ChatUserInfoBox",
  props: {
    // 选择查看详情工号
    selUserWorkerNo: {
      type: String,
      default: "",
    },
    // 选择查看元素位置
    selUserElm: {},
  },
  setup(props, ctx) {
    const store = useStore();
    // 配置文件
    let config = store.getters.getConfig.config;
    // 获取用户信息
    let userInfo = store.getters.getUserInfo;
    let aiObj = ref((() => {
      try {
        if (remote && remote.store && remote.store.getters && remote.store.getters.getState) {
          return remote.store.getters.getState("aiObj");
        } else {
          // 回退到默认的 AI 对象
          return {
            workerNo: 'ai_assistant',
            name: '智能助理',
            avatar: '/default-avatar.png',
            headImg: '/default-avatar.png',
            selfIntro: '我是您的智能助理',
            prologue: '您好，有什么可以帮助您的吗？',
            userUrl: '#',
            isFree: 1,
            hasPermission: true,
            isAi: true
          };
        }
      } catch (error) {
        console.warn('获取 aiObj 失败:', error);
        return {
          workerNo: 'ai_assistant',
          name: '智能助理',
          avatar: '/default-avatar.png',
          headImg: '/default-avatar.png',
          selfIntro: '我是您的智能助理',
          prologue: '您好，有什么可以帮助您的吗？',
          userUrl: '#',
          isFree: 1,
          hasPermission: true,
          isAi: true
        };
      }
    })());
    // 弹窗元素
    let chatUserInfoRef = ref();
    // 显示当前的用户信息
    let selUserInfo = ref();
    let labelDate = "";
    let honourData = "";
    watch(() => props.selUserWorkerNo,
      (newValue, oldValue) => {
        labelDate = "";
        honourData = "";
        getPerson(newValue);
      }, {
        deep: true
      }
    );
    watch(() => props.selUserElm,
      (newValue, oldValue) => {
        if (props.selUserWorkerNo && newValue && newValue.x) {
          // 设置窗口位置
          setBoxPos();
        }
      }, {
        deep: true
      }
    );
    // 监听智能助理变化
    watch(() => store.state.aiObj,
      (newValue, oldValue) => {
        aiObj.value = deepClone(newValue);
      }, {
        deep: true
      }
    );
    watch(() => store.state.userInfo,
      (newValue, oldValue) => {
        userInfo = store.getters.getUserInfo;
      }, {
        deep: true
      }
    );

    // 获取用户信息 type-updata为更新人员信息
    function getPerson(account, type) {
      if (!account) {
        return "";
      }
      let isAi = new RegExp(config.ai).test(account);
      // 设置时间差
      store.dispatch("setDiffTime");
      // 获取人员信息
      let thisUserInfo = store.getters.getPersons(account);
      setUserBaseInfo(thisUserInfo);
      if (!thisUserInfo.workerId && !isAi) {
        // 获取人员信息
        store.dispatch("getPersons", {doneFlag: true, account: account}).then(personInfo => {
          // 服务器不存在该人员数据隐藏弹窗
          if (personInfo.workerId || personInfo.id) {
            getPerson(props.selUserWorkerNo);
          } else {
            store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
          }
        });
        return;
      }
      // 个人助理有权限
      if (account == aiObj.value.workerNo) {
        thisUserInfo.hasPermission = true;
      }
      thisUserInfo.showTelStr = getUserTel(thisUserInfo).join("，");
      // 经纪人展示样式
      thisUserInfo.jjrStyle = (thisUserInfo.dutyTypeGroup != 1 && thisUserInfo.specialHomeShow != 1) && !isAi;
      // 合作信息
      thisUserInfo.cooperateInfo = store.getters.getCooperateInfo({account: account}) || {};
      // 从业年限
      let workerYear = thisUserInfo.cooperateInfo.workerYear || 0;
      thisUserInfo.cooperateInfo.workerYearStr = workerYear < 0.5 ? "从业低于0.5年" : `从业${workerYear}年`;
      // 作业范围
      if (thisUserInfo.cooperateInfo.jobScope && Array.isArray(thisUserInfo.cooperateInfo.jobScope)) {
        thisUserInfo.cooperateInfo.jobScope = thisUserInfo.cooperateInfo.jobScope.join("，");
      }
      // 非营销且成交记录大于0且specialHomeShow（非营销人员营销副总经理及以上级别（即营销副总经理、营销副总裁、总裁）
      thisUserInfo.styleFlag = thisUserInfo.dutyTypeGroup != 1 && thisUserInfo.cooperateInfo.cjCount > 0;
      // 标签列表
      if (labelDate) {
        thisUserInfo.labelDate = labelDate;
      }
      // 荣誉勋章列表
      if (honourData) {
        thisUserInfo.honourData = honourData;
      }

      // 查询人事档案-半小时更新一次
      let thisHrAuthority = store.getters.getHrAuthority({account: account});
      let currTime = new Date().getTime() + store.getters.getDiffTime;
      if (thisHrAuthority && currTime - thisHrAuthority.time < 30 * 60 * 1000) {
        thisUserInfo.hrAuthorityFlag = thisHrAuthority.data;
      }
      thisUserInfo.isAi = isAi;

      selUserInfo.value = thisUserInfo;
      // 设置窗口位置
      setBoxPos();
      if (type == "update") {
        return;
      } else {
        // 更新云信信息
        if (!isAi) {
          updateUsersApi({
            "msgBody": JSON.stringify({
              empNoList: [account],
            })
          });
        }
        // 获取服务器人员信息状态
        store.dispatch("updatePersons", {
          account: account,
          done: function () {
            getPerson(props.selUserWorkerNo, "update");
          }
        });
      }

      // 同步获取经纪人合作关系信息
      // 营销且本地没有记录且specialHomeShow（非营销人员营销副总经理及以上级别（即营销副总经理、营销副总裁、总裁）
      if (!isAi && thisUserInfo.dutyTypeGroup == 2 && !store.getters.getCooperateInfo({account: account}) && thisUserInfo.specialHomeShow != 1) {
        queryCooperateApi({
          "msgBody": JSON.stringify({
            empNumber: thisUserInfo.workerId,
          }),
        }).then(res => {
          if (res && res.data && res.data.data) {
            store.commit("setCooperateInfo", {account: account, data: res.data.data});
            getPerson(props.selUserWorkerNo, "update");
          }
        });
      }

      // 查询人事档案-半小时更新一次
      if (!isAi && (!thisHrAuthority || currTime - thisHrAuthority.time >= 30 * 60 * 1000)) {
        queryHrAuthorityApi({
          "msgBody": JSON.stringify({
            empNumber: thisUserInfo.workerId,
            currEmpNumber: store.getters.getUserInfo.workerId,
          }),
        }).then(res => {
          // 设置是否有选项查看
          let hrFlag = false;
          if (res && res.data && res.data.data) {
            hrFlag = true;
            thisUserInfo.hrAuthorityFlag = hrFlag;
          }
          if (res.success) {
            store.commit("setHrAuthority", {account: account, data: hrFlag, time: currTime});
            getPerson(props.selUserWorkerNo, "update");
          }
        });
      }

      // 获取荣誉勋章
      if (!isAi) {
        queryHonourDataListApi({
          "msgBody": JSON.stringify({
            accid: account,
            adapterType: 2,
            hasMealSearch: 1
          }),
        }).then(res => {
          if (res && res.data) {
            // 显示能力标签
            let personLabelsWithAvatar = res.data.personLabelsWithAvatar
            if (personLabelsWithAvatar &&
              ((personLabelsWithAvatar.avatar && personLabelsWithAvatar.avatar.length > 0) || (personLabelsWithAvatar.nonAvatar && personLabelsWithAvatar.nonAvatar.length > 0))) {
              labelDate = personLabelsWithAvatar;
              selUserInfo.value.labelDate = personLabelsWithAvatar;
            }
            // 显示荣誉勋章
            let medalModels = res.data.medalModels;
            if (medalModels && medalModels.length > 0) {
              honourData = medalModels;
              selUserInfo.value.honourData = medalModels;
            }
            if (labelDate || honourData) {
              // 设置窗口位置
              setBoxPos();
            }
          }
        });
      }
    }

    // 阻止点击穿透
    function stopPropagation() {}

    // 跳转人事档案
    function toEmpSystem(workerId) {
      store.dispatch("setOpenWindow", [`${config[config.env].jjsHome}/nhr/workerMain/detailPage?workerId=${workerId}`, "login"])
    }

    // 打开会话
    function openChat(id, e) {
      store.dispatch("setCurrentSession", {id: id, type: "open"}).then(res => {
        nextTick(() => {
          store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
          store.commit("setEmit", {type: "click", value: e});
        })
      });
    }

    // 设置窗口位置
    function setBoxPos() {
      nextTick(() => {
        setTimeout(() => {
          let thisElm = chatUserInfoRef.value.querySelector(".chat-userinfo-box");
          if (!thisElm) {
            return;
          }
          let thisPadding = 10;
          // 点击位置
          let thisTop = props.selUserElm.y || props.selUserElm.clientY;
          let thisLeft = props.selUserElm.x || props.selUserElm.clientX;
          // 窗口宽高
          let windowWidth = window.document.body.clientWidth;
          let windowHeight = window.document.body.clientHeight;
          // 弹窗宽高
          let elmWidth = thisElm.clientWidth;
          let elmHeight = thisElm.clientHeight;
          // 设置位置
          if (windowWidth - thisLeft - thisPadding < elmWidth) {
            thisLeft = thisLeft - elmWidth - thisPadding;
          } else {
            // 不需要设置默认偏移
            if (!props.selUserElm.noPadding) {
              thisTop += thisPadding;
            }
          }          // 显示位置
          if (props.selUserElm.showTop) {
            thisTop = props.selUserElm.showTop;
          }
          if (props.selUserElm.showLeft) {
            thisLeft = props.selUserElm.showLeft;
          }
          // 超出边界
          if (windowHeight - thisTop - thisPadding < elmHeight) {
            thisTop = windowHeight - elmHeight - thisPadding;
          } else {
            if (!props.selUserElm.noPadding) {
              thisTop += thisPadding;
            }
          }
          thisElm.setAttribute("style", `top:${thisTop}px;left:${thisLeft}px;`);
        }, 100);
      });
    }

    // 打开链接
    function openLink(url) {
      store.dispatch("setOpenWindow", [linkFormat(url), "login"]);
    }

    // 查看头像大图
    function showAvatar(e, item) {
      let target = e.target;
      openViewer([{src: item.avatarOther, dataSrc: item.avatarOther, w: target.naturalWidth, h: target.naturalHeight, ext: "png"}], 0, target.naturalWidth, target.naturalHeight);
    }

    return {
      userInfo,
      chatUserInfoRef,
      selUserInfo,
      avatarError,
      stopPropagation,
      getUserTel,
      toEmpSystem,
      openChat,
      openLink,
      showAvatar,

      aiObj,
    }
  }
}
</script>
<style scoped lang="scss">
.chat-userinfo-box {
  width: 456px;
  position: fixed;
  top: -9999px;
  left: -9999px;
  z-index: 99;
  background: #FFFFFF;
  box-shadow: 0px 3px 10px 0px rgba(147, 147, 147, 0.5);
  border-radius: 8px;
  border: 1px solid #E0E0E0;
  overflow: hidden;

  .my-info-header,
  .my-info-content,
  .my-info-footer {
    width: 100%;
  }

  .my-info-header {
    min-height: 100px;
    background: linear-gradient(143deg, #FFFAF3 0%, #FFF4E2 100%);
    border: 1px solid #EDEDED;
    padding: 24px 24px 10px;
    position: relative;

    &.style1 {
      background: #FFF9F9;

      .user-name-box {
        .level-box {
          background: #FFEBE9;
          color: $styleColor;
          margin-left: 10px;
        }
      }
    }

    .user-avatar-box {
      width: 72px !important;
      height: 72px !important;
      position: absolute !important;
      top: 24px;
      right: 24px;

      .avatar-box {
        border-radius: 50% !important;
      }
    }

    .user-name-box {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      max-width: calc(100% - 72px);

      h5 {
        font-size: 16px;
        line-height: 16px;
        color: #000000;
      }

      .level-box {
        padding: 0 4px;
        height: 16px;
        background: #3B3D3A;
        border-radius: 1px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        font-weight: bold;
        color: #FFF4D2;
        margin-left: 10px;
      }

      .user-label {
        border-radius: 2px;
        border: 1px solid #FC9D03;
        color: #FC9D03;
        line-height: 16px;
        padding: 0 4px;
        margin-left: 10px;
        flex-shrink: 0;

        &.user-label-1 {
          color: #999999;
          border: 1px solid #999999;
        }
      }
    }

    .user-info-box {
      width: calc(100% - 72px);
      overflow: hidden;
      display: flex;
      flex-wrap: wrap;

      &:last-child {
        margin-bottom: 0;
      }

      .info-box-li {
        min-width: 40%;
        font-size: 12px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;
        margin-bottom: 4px;
      }
    }
  }

  .my-info-content {
    padding: 0 24px;

    .my-medal-box,
    .ability-label-box {
      display: flex;
      flex-wrap: wrap;
      min-height: 30px;
      max-height: 60px;
      overflow-y: auto;
    }

    .my-medal-box {
      img {
        max-width: 114px;
        height: 20px;
        margin: 0 10px 10px 0;
        user-select: none;
      }
    }

    .ability-label-box {
      max-height: 84px;

      li {
        height: 20px;
        padding: 0 6px;
        display: flex;
        align-items: center;
        border-radius: 2px;
        font-size: 12px;
        font-weight: 500;
        margin: 0 8px 8px 0;

        &.ability-label1 {
          background: #3B3D3A;
          color: #FFF4D2;
        }

        &.ability-label2 {
          background: #EAD9CD;
          color: #333333;
        }

        &.ability-label3 {
          background: #3B3D3A linear-gradient(180deg, #FFE7C7 0%, #F3D6B1 100%);
          color: #834107;
        }

        &.ability-label4 {
          background: #F5F7FA;
          color: #3D5688;
        }
      }
    }


    .info-content-box {
      width: 100%;
      padding: 12px 0;
      border-bottom: 1px solid #EDEDED;

      &.pb2 {
        padding-bottom: 2px;
      }

      &.pb4 {
        padding-bottom: 4px;
      }

      .info-list {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .cooperate-box1 {
        margin-bottom: 8px;
      }

      .info-box-li {
        min-width: 50%;
        font-size: 13px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .info-text {
        color: #000000;

        &.info-intr {
          max-height: 54px;
          overflow-y: auto;
          word-break: break-all;
        }

        &.no-data {
          color: #999999;
        }
      }
    }
  }

  .my-info-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;

    button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 136px;
      height: 36px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      color: $styleColor;
      border: 1px solid $styleColor;
      background-color: #FFFFFF;

      &:disabled {
        border: 1px solid #CCCCCC;
        background-color: #CCCCCC;
        color: #FFFFFF;

        &:hover {
          border: 1px solid #CCCCCC;
          background-color: #CCCCCC;
          color: #FFFFFF;
        }
      }

      &:hover {
        background-color: $styleColor;
        color: #FFFFFF;
      }

      &:last-child {
        margin-left: 20px;
      }
    }
  }

  .info-box-li {
    display: flex;

    &.max-info-box {
      width: 100% !important;
    }

    .info-label {
      flex-shrink: 0;
      min-width: 60px;
      display: inline-flex;
      justify-content: space-between;
    }

    .info-mobile {
      word-break: break-all;
    }
  }
}
</style>