<template>
  <div :class="['chat-file',fileObj.loading?'show-loading-modal':'']" ref="chatFileRef">
    <!--文件顶部-->
    <div class="chat-file-header">
      <!--文件导航-->
      <div class="chat-file-nav-box">
        <div class="chat-file-nav-left">
          <div class="chat-file-nav" v-show="!fileObj.key">
            <span :class="['chat-file-tab',fileObj.tabKey==1?'sel':'']" @click="selFileTabKey(1)">文件</span>
            <span :class="['chat-file-tab',fileObj.tabKey==2?'sel':'']" @click="selFileTabKey(2)">图片</span>
            <span :class="['chat-file-tab',fileObj.tabKey==3?'sel':'']" @click="selFileTabKey(3)">乐文档</span>
          </div>
          <div class="chat-file-nav-tips" v-show="fileObj.key">全部搜索结果</div>
        </div>
        <div v-show="fileObj.tabKey==1||fileObj.tabKey==3" class="chat-file-nav-right">
          <div class="file-input-box">
            <input class="search-input" type="text" placeholder="搜索文件名称" maxlength="50" v-model.trim="fileObj.key" @input="searchFile(true)">
            <span class="icon-close" v-show="fileObj.key" @click="resetFile"></span>
          </div>
          <div class="file-header-btn" @click="uploadFile">上传</div>
          <div v-show="sessionInfo.scene!='p2p'&&userTeamInfo.type!='normal'" class="file-header-btn" @click="showDialog(1)">管理文件夹</div>
        </div>
      </div>
      <!--文件夹-->
      <div v-show="fileObj.folderList.length>0&&(fileObj.tabKey==1||fileObj.tabKey==3)" class="chat-file-folder">
        <span class="chat-file-folder-intr">文件夹：</span>
        <div class="chat-file-folder-detail-box" :style="{maxHeight:fileObj.folderHeight+'px',overflow:fileObj.showFolder?'auto':'hidden'}">
          <span :class="['chat-file-label',fileObj.folderId==''?'sel':'']" @click="selFolder('')">全部</span>
          <span :class="['chat-file-label',fileObj.folderId==item.id?'sel':'']" v-for="(item,key) in fileObj.folderList" :key="item.id" @click="selFolder(item.id)">{{ item.name }}</span>
        </div>
        <span v-show="fileObj.showFolderMore" class="chat-file-folder-more" @click="toggleShowFolder()">
          <span>{{ fileObj.showFolder ? "收起" : "更多" }}</span>
          <span :class="['show-arrow',fileObj.showFolder?'arrow-bottom':'arrow-top']"></span>
        </span>
      </div>
    </div>
    <!--文件主体-->
    <div class="chat-file-content">
      <!--文件/乐文档列表-->
      <div v-show="fileObj.tabKey==1||fileObj.tabKey==3" class="chat-file-box">
        <!--文件/乐文档标题-->
        <ul class="chat-file-list-header chat-file-list-ul">
          <li class="chat-file-list-li">
            <div class="chat-file-cell chat-file-name">文件名称</div>
            <div class="chat-file-cell chat-file-time">更新时间</div>
            <div class="chat-file-cell chat-file-size">大小</div>
            <div class="chat-file-cell chat-file-user">上传者</div>
            <div class="chat-file-cell chat-file-operate">操作</div>
          </li>
        </ul>
        <!--文件/乐文档列表内容-->
        <ul class="chat-file-list-content chat-file-list-ul" v-show="fileObj.list.length>0" @scroll="scrollFile">
          <li class="chat-file-list-li" v-for="(item,key) in fileObj.list" :key="item.msgidServer+'-'+key+'-'+item.uniqueSign+'-'+item.updateKey"
              @click="selFileItem(1,item)" @contextmenu.stop="setMenu($event, item, 1)">
            <div class="chat-file-cell chat-file-name" :title="item.showName">
              <span v-show="sessionInfo.scene!='p2p'" :class="['sel-box-i',fileObj.selMap[item.msgidServer]?'sel':'',!item.msgidServer?'disabled':'']"></span>
              <img class="file-icon" :src="getFileIcon(item.showExt)" alt="">
              <div class="file-name textEls" :title="item.showName">{{ item.showName }}</div>
            </div>
            <div class="chat-file-cell chat-file-time" :title="item.showTime">
              <span class="textEls">{{ item.showTime }}</span>
            </div>
            <div class="chat-file-cell chat-file-size" :title="item.showSize">{{ item.showSize }}</div>
            <div class="chat-file-cell chat-file-user" :title="item.userInfo.name">
              <span class="textEls">{{ item.userInfo.name }}</span>
            </div>
            <div class="chat-file-cell chat-file-operate" v-show="item.showExt!='document'">
              <span class="file-operate-box" v-if="item.file.progress">
                <progress :value="item.file.progress" max="100" :key="item.progress"></progress>
                <span class="file-operate" @click="cancelSendFile(item)">取消{{ item.status == 'toSending' ? "上传" : "下载" }}</span>
              </span>
              <span v-else>
                <span class="chat-file-operate-btn" @click.stop="openFile(item)">{{ item.file.fileInfo?.url ? "打开" : "下载" }}</span>
                <span class="chat-file-operate-btn" @click.stop="openFolder(item)">{{ item.file.fileInfo?.url ? "打开文件夹" : "另存为" }}</span>
              </span>
            </div>
            <div class="chat-file-cell chat-file-operate" v-show="item.showExt=='document'">
              <span class="chat-file-operate-btn" @click.stop="openFile(item)">打开链接</span>
            </div>
          </li>
        </ul>
        <!--文件/乐文档为空-->
        <ul class="chat-file-none" v-show="fileObj.list.length==0&&!fileObj.loading">
          <img src="/img/content_none.png" v-show="!fileObj.key" width="160"/>
          <img src="/img/content_search.png" v-show="fileObj.key" width="160"/>
          <div>{{ !fileObj.key ? "暂无文件" : "暂无查询结果" }}</div>
        </ul>
        <!--多选操作-->
        <div class="chat-file-multi" v-show="fileObj.selMapLength>0&&sessionInfo.scene!='p2p'&&!fileObj.loading">
          <div class="chat-file-multi-num">
            <span>已选</span>
            <span class="highlight">{{ fileObj.selMapLength }}</span>
            <span>个文件</span>
          </div>
          <ul class="chat-file-btn-ul">
            <li class="chat-file-btn-li" @click="multiSel(1)">
              <span>全选</span>
              <span class="chat-file-btn-li-hover">已全选当前文件，下滑选择更多</span>
            </li>
            <li class="chat-file-btn-li" @click="multiSel(2)">取消勾选</li>
            <li v-show="userTeamInfo.type!='normal'" class="chat-file-btn-li" @click="multiSel(3)">移动分组</li>
            <li class="chat-file-btn-li" @click="multiSel(4)">转发</li>
            <li class="chat-file-btn-li" @click="multiSel(5)">下载</li>
            <li class="chat-file-btn-li" @click="multiSel(6)">删除</li>
          </ul>
        </div>
      </div>
      <!--图片列表-->
      <div v-show="fileObj.tabKey==2" class="chat-img-box">
        <!--图片列表-->
        <ul class="chat-img-box-ul" v-show="fileObj.imgList.length>0" @scroll="scrollFile">
          <li class="chat-img-box-li" v-for="(item,key) in fileObj.imgMap" :key="key">
            <div class="chat-img-time">{{ key }}</div>
            <div class="chat-img-li">
              <template v-for="(item1,key1) in item" :key="item1.msgidServer+'-'+item1.updateKey">
                <div class="img-box">
                  <img class="msg-img" :onerror="errorImg.bind(this,1)" @click="clickImage" @dblclick="toViewer" @contextmenu.stop="setMenu($event, item1, 2)"
                       :src="item1.file.detailInfo?.url||item1.file.url||'image_not_found_min.png'" :data-local="item1.file.detailInfo?.url"
                       :data-src="item1.file.url" :data-key="''+item1.msgidServer+key1" :data-ext="item1.file.ext" :data-size="item1.file.size">
                </div>
              </template>
            </div>
          </li>
        </ul>
        <!--图片为空-->
        <ul class="chat-img-none" v-show="fileObj.imgList.length==0&&!fileObj.loading">
          <img src="/img/content_none.png" width="160"/>
          <div>暂无文件</div>
        </ul>
      </div>
    </div>

    <!--管理文件夹-->
    <LyDialog newClass="dialog-folder-manager" :width="396" :height="391" :visible="dialogObj.type==1||dialogObj.type==2"
              @close="dialogOperate(dialogObj.type,1)" @confirm="dialogOperate(dialogObj.type,2)"
              :closeOnClickModal="true" :foot="dialogObj.type==2" :title="dialogObj.type==1?'管理文件夹':'移动至'">
      <div class="main-dialog-box">
        <div class="ly-dialog-add-box">
          <div class="ly-add-btn" @click="showDialog(dialogObj.type,1)">新建</div>
          <ul class="ly-folder-ul">
            <li class="ly-folder-li" v-for="(item,key) in fileObj.folderList" :key="item.id" @click="selFileItem(2,item)">
              <span v-show="dialogObj.type==2" :class="['sel-box-i',dialogObj.folderItem.id==item.id?'sel':'']"></span>
              <span class="ly-folder-li-name">{{ item.name }}</span>
              <div v-show="dialogObj.type==1" class="ly-folder-btn-box">
                <span class="ly-folder-btn" @click="folderOperate(1,item)">重命名</span>
                <span class="ly-folder-btn btn-delete" @click="folderOperate(2,item)">删除</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </LyDialog>
    <!--新建文件夹/重命名文件夹-->
    <LyDialog newClass="dialog-folder-add" :width="432" :height="172" :visible="dialogObj.type1==1||dialogObj.type1==2"
              @close="dialogOperate(dialogObj.type,1,dialogObj.type1)" @confirm="dialogOperate(dialogObj.type,2,dialogObj.type1)" :closeOnClickModal="true"
              :title="dialogObj.type1==1?'新增文件夹':'重命名文件夹'">
      <div class="main-dialog-box">
        <div class="ly-dialog-default-box">
          <label class="ly-dialog-default-label">
            <span class="ly-dialog-default-tips">*</span>
            <span>文件夹名称：</span>
          </label>
          <div class="ly-dialog-default-detail">
            <input ref="lyDialogInputRef" class="ly-dialog-default-input" v-model.trim="dialogObj.folderName" maxlength="20" placeholder="最多输入20字"/>
          </div>
        </div>
      </div>
    </LyDialog>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick, inject} from "vue";
import {useStore} from "vuex";
import {getFileIcon, dealMem, transTime, debounce, getFileExt, openForward, deepClone, loadCache, dateFormat, errorImg, selElm, toViewerMethod, showMenu, addMenu} from "@utils";
import {alert, loading, toast} from "@comp/ui";
import LyDialog from "@comp/ui/comps/LyDialog";
import {queryCloudMessageApi, getResourceGroupDataApi, addResourceGroupApi, updateResourceGroupApi, deleteResourceGroupApi, moveResourceGroupApi, deleteMessageApi} from "@utils/net/api";

export default {
  name: "ChatFile",
  components: {LyDialog},
  props: {
    // 选择文件
    selFile: {
      type: Function,
    }
  },
  setup(props, ctx) {
    const store = useStore();
    let chatFileRef = ref();
    let lyDialogInputRef = ref();
    let fileObj = ref({
      tabKey: 1,// 1文件2图片3乐文档
      tabKeyMap: {
        1: "FILE,DOCUMENT",
        2: "PICTURE,CUSTOM",
        3: "DOCUMENT",
      },// 查询对象
      folderId: "",// 选择文件夹id
      folderList: [],// 文件夹列表
      key: "",// 文件搜索关键词
      list: [],// 文件列表
      imgList: [],// 图片列表
      imgMap: {},// 图片对象
      selMap: {},// 选择文件对象
      selMapLength: 0,// 选择文件夹数量
      loading: false,// 加载状态
      page: 1,// 当前页
      pageSize: 50,// 页面加载数量
      hasMore: true,// 是否还有更多
      showFolder: false,// 是否显示全文件夹
      showFolderMore: false,// 文件夹是否显示更多
      folderHeight: 28,// 文件夹显示高度
      defaultFolderHeight: 28,// 文件夹默认高度
      maxFolderHeight: 28 * 6,// 最大显示6行
      uploadFileMap: {},// 上传文件对象
    });
    // 弹窗对象
    let dialogObj = ref({
      type: -1,// 1管理文件夹2移动至
      type1: -1,// 1新建文件夹2重命名文件夹
      folderName: "",// 新建/编辑文件夹名
      folderItem: {},// 编辑的文件对象
    });
    // 配置文件
    let config = store.getters.getConfig.config;
    let userInfo = store.getters.getUserInfo;
    // 当前会话信息
    let sessionInfo = ref(remote.store.getters.getSessions({id: store.getters.getCurrentSession.id}));
    // 当前成员在群信息
    let userTeamInfo = ref({});
    if (sessionInfo.value.scene != "p2p") {
      userTeamInfo.value = remote.store.getters.getUserTeamInfo[sessionInfo.value.to];
    }

    // 自己群信息变更
    watch(() => store.state.userTeamInfos,
      (newValue, oldValue) => {
        let thisUserTeamInfo = store.getters.getUserTeamInfo[sessionInfo.value.to];
        if (sessionInfo.value.to && sessionInfo.value.scene != "p2p" && thisUserTeamInfo) {
          userTeamInfo.value = thisUserTeamInfo;
        }
      }, {
        deep: true
      }
    );


    // 更新消息内容
    watch(() => store.state.emit.reloadMsg,
      async (newValue, oldValue) => {
        if ((newValue.type == "update" || newValue.type == "updateDone") && newValue.uniqueSign && newValue.id) {
          if (newValue.type == "updateDone") {
            // 上传完成删除对应数据
            removeItem(2, newValue.uniqueSign);
            delete fileObj.value.uploadFileMap[newValue.uniqueSign];
            toast({title: "上传成功，后台处理中，稍后查看", type: 1});
            return;
          }
          let uploadItem = fileObj.value.uploadFileMap[newValue.uniqueSign];
          if (uploadItem) {
            let msgItem = remote.store.getters.getMsgs({id: sessionInfo.value.id, uniqueSign: newValue.uniqueSign});
            if (msgItem && msgItem?.file?.progress) {
              // 更新消息
              fileObj.value.uploadFileMap[newValue.uniqueSign].file = msgItem.file;
              appendUploadFile();
            }
          }
        }
      }, {
        deep: true
      }
    );

    getResourceGroupData();
    searchFile(true);

    // 选择加载对象
    function selFileTabKey(key) {
      fileObj.value.tabKey = key;
      searchFile(true);
    }

    // 切换显示全部文件夹
    function toggleShowFolder() {
      fileObj.value.showFolder = !fileObj.value.showFolder;
      nextTick(() => {
        calcFolderHeight();
      });
    }

    // 计算文件夹高度
    function calcFolderHeight() {
      nextTick(() => {
        let folderElm = chatFileRef.value.querySelector(".chat-file-folder-detail-box");
        if (fileObj.value.showFolder) {
          if (folderElm.scrollHeight > fileObj.value.defaultFolderHeight) {
            fileObj.value.folderHeight = folderElm.scrollHeight > folderElm.maxFolderHeight ? folderElm.maxFolderHeight : folderElm.scrollHeight;
          }
        } else {
          fileObj.value.folderHeight = fileObj.value.defaultFolderHeight;
          if (folderElm.scrollHeight > fileObj.value.defaultFolderHeight) {
            fileObj.value.showFolderMore = true;
          } else {
            fileObj.value.showFolderMore = false;
          }
        }
      });
    }

    // 重置搜索文件
    function resetFile() {
      fileObj.value.key = "";
      searchFile(true);
    }

    // 搜索文件
    function searchFile(resetFlag) {
      if (resetFlag) {
        // 重置滚动回顶部
        let scrollElm = "";
        if (chatFileRef.value) {
          if (fileObj.value.tabKey == 1 || fileObj.value.tabKey == 3) {
            scrollElm = chatFileRef.value.querySelector(".chat-file-list-content");
          } else if (fileObj.value.tabKey == 2) {
            scrollElm = chatFileRef.value.querySelector(".chat-img-box-ul");
          }
        }
        if (scrollElm) {
          scrollElm.scrollTop = 0;
        }
        fileObj.value.page = 1;
        fileObj.value.list = [];
        fileObj.value.imgList = [];
        fileObj.value.hasMore = true;
      }
      fileObj.value.loading = true;
      debounce({
        timerName: "searchFile",
        time: 300,
        fnName: async function () {
          getFileList();
        }
      });
    }

    // 滚动文件列表加载更多
    function scrollFile(e) {
      if (e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight < 10) {
        if (fileObj.value.hasMore && !fileObj.value.loading) {
          fileObj.value.page++;
          searchFile();
        }
      }
    }

    // 获取文件
    async function getFileList(preList, updateFile) {
      let to = sessionInfo.value.to;
      let key = fileObj.value.key;
      let param = {
        keyWord: key,
        toTarget: to,
        msgType: fileObj.value.tabKeyMap[fileObj.value.tabKey],
        fromAccount: sessionInfo.value.scene == "p2p" ? userInfo.workerNo : "",
        page: fileObj.value.page,
        rows: fileObj.value.pageSize,
        joinTime: sessionInfo.value.scene == "p2p" ? "" : userTeamInfo.value?.joinTime,
        groupTypeId: fileObj.value.folderId,
      };
      // 删除文件后更新文件列表
      if (updateFile) {
        param.page = 1;
        param.rows = fileObj.value.page * fileObj.value.pageSize;
      }
      let res = await queryCloudMessageApi({
        msgBody: JSON.stringify(param)
      });
      // 非当前显示状态不处理
      if (sessionInfo.value.to != to || key != fileObj.value.key) {
        return;
      }
      if (!res?.success) {
        fileObj.value.loading = false;
        toast({title: res?.errorMsg || "系统错误", type: 2});
        return;
      }
      let defaultList = res.data?.data || [];
      let defaultLength = defaultList.length || 0;
      // 没有更多了
      if (res.data?.data && (res.data.data.length == 0 || fileObj.value.page >= res.data.pages)) {
        fileObj.value.hasMore = false;
      }
      let list = [];
      let userList = [];
      let p = [];
      // 遍历符合条件的文件
      for (let i = 0; i < defaultList.length; i++) {
        let item = defaultList[i]?.msgJson;
        if (fileObj.value.tabKey == 1 || fileObj.value.tabKey == 3) {
          // 文件/乐文档
          if (item?.attach?.url) {
            if (item.msgType == "DOCUMENT") {
              // 处理乐文档字段
              item.attach.ext = "document";
              item.attach.detailExt = getFileExt(item.attach.name);
              if (!item.attach.property) {
                item.attach.property = 1;
                if (item.attach.detailExt == "xlsx") {
                  item.attach.property = 2;
                }
              }
            }
            item.showName = item.attach.name;
            item.showExt = item.attach.ext;
            item.showTime = transTime(parseInt(item.msgTimestamp), 1);
            item.showSize = item.attach.size ? dealMem(item.attach.size) : "-";
            item.userInfo = remote.store.getters.getPersons(item.fromAccount);
            // 本地不存在数据的用户请求接口
            if (!item.userInfo.workerId) {
              userList.push(item.fromAccount);
            }
            item.file = new Promise(resolve => {
              (async item => {
                let file = await store.dispatch("setFileLocal", {file: item.attach});
                item.file = file;
                resolve();
              })(item);
            });
            p.push(item.file);
            list.push(item);
          }
        } else if (fileObj.value.tabKey == 2) {
          item.showName = dateFormat(parseInt(item.msgTimestamp), "yyyy-MM-dd");
          if (item?.msgType == "PICTURE") {
            // 图片
            if (item?.attach?.url) {
              item.file = item.attach;
              item.file.detailInfo = new Promise(resolve => {
                (async item => {
                  let detailInfo = await loadCache({
                    ...item.attach,
                    fileDB: store.getters.getFileDB,
                  });
                  item.file.detailInfo = detailInfo;
                  resolve();
                })(item);
              });
              p.push(item.file.detailInfo);
              list.push(item);
            }
          } else if (item?.msgType == "CUSTOM") {
            // 图文
            if (item?.attach?.msgs) {
              if (typeof item.attach.msgs == "string") {
                try {
                  item.attach.msgs = JSON.parse(item.attach.msgs);
                } catch (e) {
                }
              }
              if (item.attach.msgs.length > 0) {
                let updateKey = 0;
                item.attach.msgs.map(async mItem => {
                  if (mItem?.type == "image" && mItem?.file?.url) {
                    item = deepClone(item);
                    item.file = mItem.file;
                    item.updateKey = updateKey;
                    item.file.detailInfo = new Promise(resolve => {
                      (async item => {
                        let detailInfo = await loadCache({
                          ...mItem.file,
                          fileDB: store.getters.getFileDB,
                        });
                        item.file.detailInfo = detailInfo;
                        resolve();
                      })(item)
                    });
                    p.push(item.file.detailInfo);
                    list.push(item);
                    updateKey++;
                  }
                });
              }
            }
          }
        }
      }
      // 加载本地数据
      await Promise.allSettled(p);
      if (fileObj.value.tabKey == 1 || fileObj.value.tabKey == 3) {
        // 文件/乐文档加载数据
        let personInfo = await remote.store.dispatch("getPersons", {doneFlag: true, account: userList});
        if (Object.keys(personInfo).length > 0) {
          // 请求到数据重新赋值
          list.map(item => {
            if (personInfo[item.fromAccount]) {
              item.userInfo = personInfo[item.fromAccount];
            }
          });
        }
      }
      if (preList) {
        list = list.concat(preList);
      }
      // 最少渲染50个,还有更多数据继续请求
      if (defaultLength > 0 && list.length < fileObj.value.pageSize && fileObj.value.hasMore) {
        fileObj.value.page++;
        getFileList(list, updateFile);
        return;
      }
      list = list.sort((a, b) => {return b.msgTimestamp - a.msgTimestamp});
      fileObj.value.loading = false;
      if (fileObj.value.tabKey == 1 || fileObj.value.tabKey == 3) {
        fileObj.value.list = fileObj.value.list.concat(list);
        // 加入正在上传列表
        appendUploadFile();
      } else if (fileObj.value.tabKey == 2) {
        fileObj.value.imgList = fileObj.value.imgList.concat(list);
        // 设置图片结构对象
        let imgMap = {};
        fileObj.value.imgList.map(item => {
          if (!imgMap[item.showName]) {
            imgMap[item.showName] = [];
          }
          imgMap[item.showName].push(item);
        });
        fileObj.value.imgMap = imgMap;
      }
    }

    // 选择文件 type:1选择文件2选择文件移动至文件夹
    function selFileItem(type, item) {
      if ((type == 1 && !item.msgidServer) || sessionInfo.value.scene == "p2p") {
        return;
      }
      if (type == 1) {
        if (fileObj.value.selMap[item.msgidServer]) {
          delete fileObj.value.selMap[item.msgidServer];
        } else {
          fileObj.value.selMap[item.msgidServer] = item;
        }
        fileObj.value.selMapLength = Object.keys(fileObj.value.selMap).length;
      } else if (type == 2) {
        // 选择文件移动至文件夹
        dialogObj.value.folderItem = item;
      }
    }

    // 选择文件夹
    function selFolder(id) {
      fileObj.value.folderId = id;
      fileObj.value.page = 1;
      fileObj.value.list = [];
      fileObj.value.hasMore = true;
      getFileList();
    }

    // 显示弹窗
    function showDialog(type, type1) {
      dialogObj.value.folderItem = {};
      switch (type) {
        case 1:
          // 管理文件夹
          dialogObj.value.type = type;
          break;
        case 2:
          // 移动分组
          dialogObj.value.type = type;
          break;
      }

      switch (type1) {
        case 1:
          // 新建文件夹
          dialogObj.value.type1 = type;
          dialogObj.value.folderName = "";
          nextTick(() => {
            lyDialogInputRef.value.focus();
          });
          break;
      }
    }

    // 弹窗操作对象 type:1管理文件夹2移动至 key:1关闭2确认 type1:1新建文件夹2重命名文件夹
    async function dialogOperate(type, key, type1) {
      let res = {};
      let id = "";
      if (type1) {
        switch (type1) {
          case 1:
            // 新建文件夹
            if (key == 1) {
              dialogObj.value.type1 = -1;
            } else {
              if (!dialogObj.value.folderName) {
                toast({title: "文件夹名不能为空", type: 2});
              }
              loading();
              res = await addResourceGroupApi({
                msgBody: JSON.stringify({
                  tid: sessionInfo.value.to,
                  groupTypeName: dialogObj.value.folderName,
                  ownEmpNos: userInfo.workerNo,
                })
              });
              loading().hide();
              if (!res?.success) {
                toast({title: res?.errorMsg || "系统错误", type: 2});
                return;
              }
              toast({title: "新增成功", type: 1});
              dialogObj.value.type1 = -1;
              getResourceGroupData();
            }
            break;
          case 2:
            // 重命名文件夹
            if (key == 1) {
              dialogObj.value.type1 = -1;
            } else {
              if (!dialogObj.value.folderName) {
                toast({title: "文件夹名不能为空", type: 2});
              }
              id = dialogObj.value.folderItem.id;
              loading();
              let res = await updateResourceGroupApi({
                msgBody: JSON.stringify({
                  tid: sessionInfo.value.to,
                  groupTypeId: id,
                  groupTypeName: dialogObj.value.folderName,
                  ownEmpNos: userInfo.workerNo,
                })
              });
              loading().hide();
              if (!res?.success) {
                toast({title: res?.errorMsg || "系统错误", type: 2});
                return;
              }
              let folderItem = fileObj.value.folderList.find(item => {return item.id == id});
              folderItem.name = dialogObj.value.folderName;
              toast({title: "重命名成功", type: 1});
              dialogObj.value.type1 = -1;
            }
            break;
        }
      } else {
        switch (type) {
          case 1:
            // 管理文件夹
            if (key == 1) {
              dialogObj.value.type = -1;
            }
            break;
          case 2:
            // 移动分组
            if (key == 1) {
              dialogObj.value.type = -1;
            } else {
              if (!dialogObj.value.folderItem.id) {
                toast({title: "请选择分组进行操作", type: 2});
                return;
              }
              let msgIdVoList = [];
              for (let key in fileObj.value.selMap) {
                let item = fileObj.value.selMap[key];
                msgIdVoList.push({
                  msgIdServer: item.msgidServer,
                  msgIdClient: item.msgidClient,
                  fileType: item.msgType
                })
              }
              id = dialogObj.value.folderItem.id;
              loading();
              res = await moveResourceGroupApi({
                msgBody: JSON.stringify({
                  tid: sessionInfo.value.to,
                  groupTypeId: id,
                  ownEmpNos: userInfo.workerNo,
                  msgIdVoList: msgIdVoList,
                })
              });
              loading().hide();
              if (!res?.success) {
                toast({title: res?.errorMsg || "系统错误", type: 2});
                return;
              }
              toast({title: "移动分组成功", type: 1});
              // 移动成功后取消选择
              multiSel(2);
              dialogObj.value.type = -1;
              // 非当前文件夹和全部分组，删除对应的文件列表
              if (fileObj.value.folderId && fileObj.value.folderId != id) {
                msgIdVoList.map(item => {
                  removeItem(1, item.msgIdServer);
                });
              }
            }
            break;
        }
      }
    }

    // 文件夹操作 type:1重命名2删除
    async function folderOperate(type, item) {
      let res = {};
      switch (type) {
        case 1:
          // 重命名
          dialogObj.value.folderName = item.name;
          dialogObj.value.folderItem = item;
          dialogObj.value.type1 = 2;
          break;
        case 2:
          // 删除
          alert({
            content: `删除后，该文件夹内容将移动至“全部”分组，确定删除？`,
            done: async (type) => {
              if (type == 1) {
                let id = item.id;
                loading();
                res = await deleteResourceGroupApi({
                  msgBody: JSON.stringify({
                    tid: sessionInfo.value.to,
                    groupTypeId: id,
                    ownEmpNos: userInfo.workerNo,
                  })
                });
                loading().hide();
                if (!res?.success) {
                  toast({title: res?.errorMsg || "系统错误", type: 2});
                  return;
                }
                // 删除当前选中文件夹选会全部文件夹
                if (fileObj.value.folderId == id) {
                  fileObj.value.folderId = "";
                  searchFile(true);
                }
                toast({title: "删除成功", type: 1});
                // 删除对应文件夹
                let folderIndex = fileObj.value.folderList.findIndex(item => {return item.id == id});
                if (folderIndex > -1) {
                  fileObj.value.folderList.splice(folderIndex, 1);
                }
                getResourceGroupData(true);
              }
            }
          });
          break;
      }
    }

    // 打开/下载文件，openType-1重新下载
    function openFile(item, openType) {
      if (item.file.ext == "document") {
        // 打开乐文档
        let urlJson = store.getters.getLinkUrlJson({
          type: "doc",
          doc: {
            padId: item.file.docPadId,
            docId: item.file.docId,
          }
        });
        store.dispatch("setOpenWindow", [urlJson.url, urlJson.frameName]);
      } else {
        store.dispatch("setMsgsLocal", {item: item}).then(res => {
          store.dispatch("openFile", {item: item, openType: openType});
        });
      }
    }

    // 打开文件夹/另存为，openType-1另存为
    function openFolder(item, openType) {
      store.dispatch("openFolder", {item: item, openType: openType});
    }

    // 文件多选操作
    function multiSel(type, selItem) {
      let selMap = {};
      switch (type) {
        case 1:
          // 全选
          fileObj.value.list.map(item => {
            fileObj.value.selMap[item.msgidServer] = item;
          });
          fileObj.value.selMapLength = Object.keys(fileObj.value.list).length;
          break;
        case 2:
          // 取消全选
          fileObj.value.selMap = {};
          fileObj.value.selMapLength = 0;
          break;
        case 3:
          // 移动分组
          showDialog(2);
          break;
        case 4:
          // 转发
          // 消息体
          let thisMsg = {};
          // 消息标题
          let thisTitle = "";
          // 消息文案
          let thisPushContent = "";
          // 具体消息
          let multipleList = [];
          let idServerList = [];
          let msgIndex = 0;
          // 逐条/合并转发
          if (selItem) {
            selMap[selItem.msgidServer] = selItem;
          } else {
            selMap = fileObj.value.selMap;
          }
          let selMapLength = Object.keys(selMap).length;
          for (let key in selMap) {
            let item = selMap[key];
            if (fileObj.value.tabKey == 2) {
              multipleList.push({
                file: item.file,
                type: "image",
              });
            } else {
              if (item.msgType == "DOCUMENT") {
                multipleList.push({
                  content: deepClone({
                    type: "multi", msgs: [{
                      type: "document", file: {
                        docId: item.file.docId,
                        docImg: item.file.docImg,
                        docName: item.file.showName,
                        docPadId: item.file.docPadId,
                        empNumber: item.file.empNumber,
                        property: item.file.property,
                      }
                    }]
                  }),
                  type: "custom",
                });
              } else {
                multipleList.push({
                  file: deepClone(item.file),
                  type: "file",
                });
              }
            }
            idServerList.push(item.msgidServer);
            if (msgIndex < 4) {
              thisPushContent += `[文件]\n`;
            }
            msgIndex++;
          }
          if (selMapLength == 1) {
            thisMsg = multipleList[0];
          } else {
            let idServers = idServerList.join(",");
            thisTitle = `${sessionInfo.value.detailInfo.name}${sessionInfo.value.scene == "p2p" ? `与${userInfo.name}` : ""}的聊天记录`;
            thisMsg = {
              content: {
                data: {
                  title: `[合并转发]${thisTitle}`,
                  content: thisPushContent,
                  sessionId: sessionInfo.value.id,
                  sessionType: sessionInfo.value.type,
                  source: "pc-im",
                  url: `${config[config.env].jjsHome}/im/message/msgDetail?msgIds=${idServers}`,
                  other: {messageIds: idServers}
                },
                type: 9
              },
              type: "custom",
              pushContent: thisPushContent,
              forwardType: "item",
              forwardTitle: thisTitle,
              multipleList: multipleList,
            }
          }
          openForward(thisMsg);
          multiSel(2);
          break;
        case 5:
          // 下载
          let hasDoc = false;
          for (let key in fileObj.value.selMap) {
            let item = fileObj.value.selMap[key];
            if (item.msgType == "DOCUMENT") {
              hasDoc = true;
            }
          }
          if (hasDoc) {
            alert({
              content: "当前勾选的文件类型包含乐文档，系统将过滤后执行操作",
              done: (type) => {
                if (type == 1) {
                  for (let key in fileObj.value.selMap) {
                    let item = fileObj.value.selMap[key];
                    if (item.msgType == "FILE") {
                      store.dispatch("openFile", {item: item, openType: 2});
                    }
                  }
                }
              }
            })
          } else {
            for (let key in fileObj.value.selMap) {
              let item = fileObj.value.selMap[key];
              store.dispatch("openFile", {item: item, openType: 2});
            }
          }
          break;
        case 6:
          // 删除
          if (selItem) {
            selMap[selItem.msgidServer] = selItem;
          } else {
            selMap = fileObj.value.selMap;
          }
          // 非管理员不能删除非自己的文件
          let removeFlag = true;
          if (userTeamInfo.value.type == "normal" || sessionInfo.value.scene == "p2p") {
            for (let key in selMap) {
              if (selMap[key].fromAccount != userInfo.workerNo) {
                removeFlag = false;
                break
              }
            }
          }
          if (!removeFlag) {
            toast({title: "只能删除自己发送的文件", type: 2});
            return;
          }
          let selList = Object.values(fileObj.value.selMap);
          let selName = "";
          if (selList.length == 1) {
            selName = selList[0].file.showName;
          }
          alert({
            content: `是否确定删除${selName ? " " + selName + " " : "选择的"}文件？`,
            done: async (type) => {
              if (type == 1) {
                let param = {
                  msgIdServer: [],
                  docIdServer: [],
                };
                for (let key in selMap) {
                  let item = selMap[key];
                  if (item.msgType == "FILE") {
                    param.msgIdServer.push(item.msgidServer);
                  } else if (item.msgType == "DOCUMENT") {
                    param.docIdServer.push(item.msgidServer);
                  }
                }
                param.msgIdServer = param.msgIdServer.join(",");
                param.docIdServer = param.docIdServer.join(",");
                loading();
                let res = await deleteMessageApi({
                  msgBody: JSON.stringify(param),
                });
                loading().hide();
                if (!res?.success) {
                  toast({title: res?.errorMsg || "系统错误", type: 2});
                  return;
                }
                if (res.data.amountError === 0) {
                  toast({title: "删除成功", type: 1});
                } else {
                  // 提示删除失败文件
                  let nameList = [];
                  res.data.errorList.map(item => {
                    nameList.push(selMap[item].file.showName);
                  });
                  toast({title: `抱歉${nameList.join("、")}等文件删除失败,请稍后再试!`, type: 2,});
                }
                // 去除删除成功文件
                res.data.succeedList.map(item => {
                  delete fileObj.value.selMap[item];
                  removeItem(1, item);
                });
                fileObj.value.selMapLength = Object.keys(fileObj.value.selMap).length;
                // 后台删除后还有缓存，暂不重新加载
                // if (fileObj.value.list.length < fileObj.value.pageSize && fileObj.value.hasMore) {
                //   getFileList("", true);
                // }
              }
            }
          });
          break;
      }
    }

    // 获取文件夹
    async function getResourceGroupData(notTips) {
      if (sessionInfo.value.scene != "p2p") {
        let res = await getResourceGroupDataApi({msgBody: JSON.stringify({tid: sessionInfo.value.to})});
        if (!res?.success && !notTips) {
          toast({title: res?.errorMsg || "获取文件夹错误，请重试", type: 2});
        }
        if (res?.data?.data) {
          fileObj.value.folderList = res.data.data;
        }
        nextTick(() => {
          calcFolderHeight();
        });
      }
    }

    // 打开查看大图/视频
    function toViewer(e) {
      toViewerMethod(e, "msg-img", chatFileRef.value);
    }

    // 点击图片
    function clickImage(e) {
      selElm(e.target);
    }

    // 右键操作
    function setMenu(e, item, type) {
      let menuObj = {
        menu: {},// 菜单对象
        menuList: [],// 菜单列表
        popupItem: {},// 菜单弹出对象
      }
      if (item.msgidServer) {
        if (type == 1 && fileObj.value.selMapLength == 0) {
          if ((sessionInfo.value.scene != "p2p" && userTeamInfo.value.type != "normal") || item.fromAccount == userInfo.workerNo) {
            menuObj.menuList.push({
              label: `删除${item.msgType == "DOCUMENT" ? "乐文档" : "文件"}`, click: function () {
                multiSel(6, item);
              }
            });
          }
          menuObj.menuList.push({
            label: "转发", click: function () {
              multiSel(4, item);
            }
          });
          if (item.msgType != "DOCUMENT") {
            if (item.file.fileInfo?.url) {
              menuObj.menuList.push({
                label: "打开", click: function () {
                  openFile(item);
                }
              });
              menuObj.menuList.push({
                label: "打开文件", click: function () {
                  openFolder(item);
                }
              });
            }
            menuObj.menuList.push({
              label: "下载", click: function () {
                openFile(item, 1);
              }
            });
            menuObj.menuList.push({
              label: "另存为", click: function () {
                openFolder(item, 1);
              }
            });
          }
        } else if (type == 2) {
          clickImage(e);
          // 图片
          menuObj.menuList.push({
            label: "复制", click: function () {
              document.execCommand("copy");
            }
          });
          menuObj.menuList.push({
            label: "另存为", click: function () {
              openFolder(item, 1);
            }
          });
          menuObj.menuList.push({
            label: "收藏", click: async function () {
              let res = await store.dispatch("collectMsg", {
                sourceName: userInfo.workerName,
                sourceIcon: userInfo.headPic,
                sourceId: userInfo.workerNo,
                sessionInfo: {scene: "p2p", to: userInfo.workerNo},
                msg: {scene: "p2p", to: userInfo.workerNo, type: "custom", content: {type: "multi", msgs: [{type: "image", file: {url: item.file.url}}]}},
              });
              if (res.success) {
                toast({title: "收藏成功！", type: 1});
              } else {
                toast({title: "收藏失败。" + res.errorMsg, type: 2});
              }
            }
          });
          menuObj.menuList.push({
            label: "转发", click: function () {
              multiSel(4, item);
            }
          });
        }
      }
      if (menuObj.menuList.length > 0) {
        menuObj.menu = showMenu(menuObj.menuList)
        menuObj.popupItem = menuObj.menu.popup(e.x, e.y);
      }
      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    // 取消发送文件
    function cancelSendFile(item) {
      let downloadMD5 = item.file.downloadMD5 || item.uniqueSign;
      // 删除上传进度
      if (fileObj.value.uploadFileMap[downloadMD5]) {
        delete fileObj.value.uploadFileMap[downloadMD5];
        removeItem(2, downloadMD5);
      }
      try {
        remote.store.getters.getNimFileUpload(downloadMD5).abort();
      } catch (e) {}
      remote.store.commit("setNimFileUpload", {key: downloadMD5, value: "del"});
      delete item.file.downloadMD5;
      delete item.file.progress;
      delete item.uniqueSign;
      item.updateKey = (item.updateKey || 0) + 1;
    }

    // 上传文件
    function uploadFile() {
      props.selFile(2, function (files) {
        // 设置上传文件标识
        for (let i = 0; i < files.length; i++) {
          let file = files[i];
          file.progress = "0.00";
          fileObj.value.uploadFileMap[file.uniqueSign] = {
            file: file,
            uniqueSign: file.uniqueSign,
            userInfo: userInfo,
            showName: file.name,
            showExt: getFileExt(file.name),
            showSize: dealMem(file.size),
            showTime: transTime(Date.now(), 1),
            status: "toSending"
          };
        }
        appendUploadFile()
      });
    }

    // 加入上传列表
    function appendUploadFile() {
      if (Object.keys(fileObj.value.uploadFileMap).length > 0) {
        for (let key in fileObj.value.uploadFileMap) {
          let listItem = fileObj.value.list.find(item => {return item.uniqueSign == key});
          let item = fileObj.value.uploadFileMap[key];
          if (listItem) {
            listItem.progress = (listItem.progress || 0) + 1;
            listItem.file = item.file;
          } else {
            fileObj.value.list.unshift(item);
          }
        }
      }
    }

    // 删除对应文件列表
    function removeItem(type, key) {
      let itemIndex = fileObj.value.list.findIndex(item => {return type == 1 ? item.msgidServer == key : item.uniqueSign == key});
      if (itemIndex > -1) {
        fileObj.value.list.splice(itemIndex, 1);
      }
    }

    return {
      chatFileRef,
      lyDialogInputRef,
      fileObj,
      dialogObj,
      sessionInfo,
      userTeamInfo,

      getFileIcon,
      dealMem,
      transTime,
      errorImg,

      selFileTabKey,
      toggleShowFolder,
      resetFile,
      searchFile,
      scrollFile,
      selFileItem,
      selFolder,
      showDialog,
      dialogOperate,
      folderOperate,
      openFile,
      openFolder,
      multiSel,
      toViewer,
      clickImage,
      setMenu,
      cancelSendFile,
      uploadFile,
    }
  }
}
</script>
<style scoped lang="scss">
.chat-file {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #FFFFFF;


  .sel-box-i {
    flex-shrink: 0;
    width: 14px;
    height: 14px;
    position: relative;
    border: 1px solid #BCBCBC;
    border-radius: 2px;
    margin-right: 8px;

    &.sel {
      border: 1px solid $styleColor;
      background: $styleColor;

      &:after {
        content: "";
        width: 8px;
        height: 3px;
        border: 2px solid #FFFFFF;
        border-top: transparent;
        border-right: transparent;
        position: absolute;
        top: 2px;
        left: 1px;
        transform: rotate(-45deg);
      }
    }

    &.disabled {
      background-image: url("/img/index/disabled_sel.png");
      background-repeat: no-repeat;
      background-size: 100%;
      border: 0;
    }
  }

  .chat-file-header {
    color: #333333;

    .chat-file-nav-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;

      .chat-file-nav-left {
        .chat-file-nav {
          display: flex;
          align-items: center;

          .chat-file-tab {
            width: 46px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 6px;

            &.sel,
            &:hover {
              background: $styleColor;
              color: #FFFFFF;
            }
          }
        }
      }

      .chat-file-nav-right {
        display: flex;
        align-items: center;

        .file-input-box {
          display: flex;
          align-items: center;
          width: 200px;
          height: 26px;
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          padding-left: 26px;
          font-size: 13px;
          background: #FFFFFF url("/img/search/icon_search.png") no-repeat 7px center;
          background-size: 16px 16px;

          .search-input {
            flex: 1;
            min-width: 0;
          }

          .icon-close {
            flex-shrink: 0;
            width: 12px;
            height: 12px;
            margin: 0 10px;
            background-img: url("/img/search/close.png");
            background-repeat: no-repeat;
            background-size: 12px 12px;
            cursor: pointer;
          }
        }

        .file-header-btn {
          height: 26px;
          line-height: 24px;
          text-align: center;
          padding: 0 16px;
          border-radius: 4px;
          border: 1px solid $styleColor;
          color: $styleColor;
          cursor: pointer;
          margin-left: 6px;
        }
      }

    }

    .chat-file-folder {
      display: flex;
      padding: 0 16px;
      margin-bottom: 4px;

      .chat-file-folder-intr {
        flex-shrink: 0;
        height: 20px;
        line-height: 20px;
      }

      .chat-file-folder-detail-box {
        display: flex;
        flex-wrap: wrap;
        min-width: 0;
        flex: 1;

        .chat-file-label {
          flex-shrink: 0;
          line-height: 20px;
          padding: 0 10px;
          border-radius: 4px;
          cursor: pointer;
          border: 1px solid #E0E0E0;
          margin-right: 6px;
          margin-bottom: 6px;

          &.sel {
            background: $styleBg2;
            border: 1px solid $styleColor;
            color: $styleColor;
          }
        }
      }

      .chat-file-folder-more {
        position: relative;
        display: flex;
        align-items: center;
        flex-shrink: 0;
        height: 20px;
        line-height: 20px;
        padding-right: 12px;
        margin-left: 16px;
        cursor: pointer;

        &:hover {
          color: $styleColor;

          .arrow-top {
            &:before {
              border-color: $styleColor transparent transparent transparent;
            }
          }

          .arrow-bottom {
            &:before {
              border-color: transparent transparent $styleColor transparent;
            }
          }
        }

        .arrow-top {
          width: 8px;
          height: 4px;
          margin-left: 3px;

          &:before {
            border-color: #333333 transparent transparent transparent;
          }

          &:after {
            border-color: transparent transparent transparent transparent;
          }

          &:before,
          &:after {
            border-width: 4px 4px 0 4px;
          }
        }

        .arrow-bottom {
          width: 8px;
          height: 4px;
          margin-left: 5px;

          &:before {
            border-color: transparent transparent #333333 transparent;
          }

          &:after {
            border-color: transparent transparent transparent transparent;
          }

          &:before,
          &:after {
            border-width: 0 4px 4px 4px;
          }
        }
      }
    }
  }

  .chat-file-content {
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;

    .chat-file-box {
      display: flex;
      flex-direction: column;
      min-height: 0;
      flex: 1;

      .chat-file-list-ul {
        &.chat-file-list-header {
          height: 30px;
          font-weight: bold;
          border-top: 1px solid #E0E0E0;

          .chat-file-list-li {
            height: 100%;
            background: #FFFFFF !important;
            color: #000000;
          }
        }

        &.chat-file-list-content {
          min-height: 0;
          flex: 1;
          overflow-y: auto;

          .chat-file-list-li {
            color: #999999;
            cursor: pointer;

            &:hover {
              background: $styleBg1Hover;
            }

            .chat-file-name {
              padding-left: 16px;
            }
          }
        }

        .chat-file-list-li {
          display: flex;
          align-items: center;
          height: 40px;

          &:nth-child(odd) {
            background: #F9F9F9;
          }

          .chat-file-cell {
            display: flex;
            align-items: center;
            height: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            padding-right: 5px;

            &:last-child {
              border-right: none;
            }
          }

          .chat-file-name {
            flex-shrink: 0;
            width: calc(100% - 378px);
            padding-left: 38px;
            color: #000000;

            .file-icon {
              width: 26px;
              margin-right: 8px;
              flex-shrink: 0;
            }

            .file-name {
              flex: 1;
              min-width: 0;
            }
          }

          .chat-file-time {
            flex-shrink: 0;
            width: 100px;
          }

          .chat-file-size {
            flex-shrink: 0;
            width: 70px;
          }

          .chat-file-user {
            flex-shrink: 0;
            width: 90px;
          }

          .chat-file-operate {
            flex-shrink: 0;
            width: 118px;
            color: #000000;

            .file-operate-box {
              display: flex;
              align-items: center;

              progress {
                display: block;
                width: 40px;
                height: 3px;
                border: none;

                &::-webkit-progress-bar {
                  background-color: #CCCCCC;
                }

                &::-webkit-progress-value {
                  background-color: #2D91E6;
                }
              }

              .file-operate {
                color: #333333;
                margin-left: 15px;
                cursor: pointer;

                &:hover {
                  color: $styleColor;
                }
              }
            }

            .chat-file-operate-btn {
              color: #333333;
              cursor: pointer;
              margin-right: 8px;

              &:hover {
                color: $styleColor;
              }

              &:last-child {
                margin-right: 0;
              }
            }
          }
        }
      }

      .chat-file-none {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 0;
        flex: 1;
      }

      .chat-file-multi {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;
        border-top: 1px solid #EBEBEB;
        padding: 0 16px;

        .highlight {
          margin: 0 2px;
        }

        .chat-file-btn-ul {
          display: flex;
          align-items: center;

          .chat-file-btn-li {
            position: relative;
            height: 28px;
            line-height: 26px;
            padding: 0 12px;
            border-radius: 4px;
            color: #333333;
            border: 1px solid #E0E0E0;
            margin-left: 6px;
            cursor: pointer;

            &:hover {
              border-color: $styleColor;
              color: $styleColor;

              .chat-file-btn-li-hover {
                display: block;
              }
            }

            .chat-file-btn-li-hover {
              display: none;
              position: absolute;
              top: -40px;
              left: -60px;
              background: #FFFFFF;
              border: 1px solid #E0E0E0;
              border-radius: 2px;
              padding: 3px;
              width: 180px;
              line-height: 17px;
              color: #000000;
            }
          }
        }
      }
    }
  }

  .chat-img-box {
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    .chat-img-box-ul {
      padding: 0 20px;
      min-height: 0;
      flex: 1;
      overflow-y: auto;

      .chat-img-box-li {
        max-width: 650px;
        margin-bottom: 10px;

        .chat-img-time {
          background: url("/img/icon_time.png") no-repeat left center;
          background-size: 10px 10px;
          color: #9B9B9B;
          margin-bottom: 5px;
          padding-left: 16px;
        }

        .chat-img-li {
          display: flex;
          flex-wrap: wrap;

          .img-box {
            width: 104px;
            height: 104px;
            background: #D8D8D8;
            overflow: hidden;
            margin: 2px;
            user-select: text;

            .msg-img {
              user-select: text !important;
              width: 100%;
              height: 100%;
              border: 1px solid transparent;

              &:hover {
                border: 1px solid $styleLink;
              }
            }
          }
        }
      }
    }

    .chat-img-none {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 0;
      flex: 1;
    }
  }

  .main-dialog-box {
    .ly-dialog-default-box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;

      .ly-dialog-default-label {
        color: #666666;
        line-height: 30px;
        flex-shrink: 0;

        .ly-dialog-default-tips {
          color: #EE3939;
          margin-right: 5px;
        }
      }

      .ly-dialog-default-detail {
        width: 282px;

        .ly-dialog-default-input {
          width: 100%;
          line-height: 28px;
          padding-left: 10px;
          border: 1px solid #CCCCCC;
          border-radius: 4px;

          &::placeholder {
            color: #999999;
          }

          &:focus {
            border: 1px solid #333333;
          }
        }
      }
    }

    .ly-dialog-add-box {
      .ly-add-btn {
        display: inline-block;
        padding: 0 16px;
        margin: 0 0 8px 16px;
        line-height: 20px;
        height: 22px;
        color: $styleColor;
        border: 1px solid $styleColor;
        border-radius: 4px;
        cursor: pointer;
      }

      .ly-folder-ul {
        max-height: 200px;
        overflow: auto;

        .ly-folder-li {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 32px;
          padding: 0 16px;
          color: #333333;
          cursor: pointer;

          &:hover {
            background: #F5F5F5;

            .ly-folder-btn-box {
              display: flex;
            }
          }

          .ly-folder-li-name {
            flex: 1;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .ly-folder-btn-box {
            display: none;
            align-items: center;

            .ly-folder-btn {
              margin-left: 20px;

              &.btn-delete {
                color: $styleColor;
              }
            }
          }
        }
      }
    }
  }

  :deep(.dialog-folder-manager) {
    .content {
      padding: 24px 0 !important;
    }
  }
}
</style>