<template>
  <div class="team-notify">
    <header>
      <div class="header-left">
        <span
          @click="activeTag('invite')"
          :class="['mr15', tagType == 'invite' ? 'active' : '']"
          >入群确认</span
        >
        <span
          @click="activeTag('quit')"
          :class="[tagType == 'quit' ? 'active' : '']"
          >退群确认</span
        >
      </div>
      <div
        class="header-right"
        v-show="
          (tagType == 'invite' && localMsgs.length > 0) ||
          (tagType == 'quit' && quitTeamList.length > 0)
        "
      >
        <LyButton
          @click="openBatch"
          v-if="canSelect.length > 0"
          type="text"
          class="mr20"
          :fontsize="12"
          >{{ batchFlag ? "取消编辑" : "批量处理" }}</LyButton
        >
        <LyButton @click="removeAll" type="text" :fontsize="12">删除</LyButton>
      </div>
    </header>
    <main :class="[batchFlag ? 'has-footer' : '']">
      <section v-show="tagType == 'invite' && localMsgs.length > 0">
        <!-- 入群 -->
        <ul
          class="into-group-sys-msg-ul"
          v-for="(item, index) in localMsgs"
          :key="index"
        >
          <li class="invite-date">{{ item.time }}</li>
          <li class="li-data" v-for="item1 in item.values" :key="item1.time">
            <div
              :class="[
                'check-box-div',
                activeIds.indexOf(item1.idServer) > -1 ? 'active' : '',
              ]"
              v-if="
                [
                  'teamInvite',
                  'applyTeam',
                  'superTeamInvite',
                  'applySuperTeam',
                ].indexOf(item1.type) > -1 &&
                batchFlag &&
                item1.state == 'init'
              "
              @click="activeFunc(item1)"
            ></div>
            <div class="header-pic">
              <img
                :src="item1.detaiInfo.avatar"
                :onerror="
                  avatarError.bind(this, 'p2p', item1.detaiInfo.workerNo, '')
                "
              />/>
            </div>
            <div class="group-info-div">
              <h3>
                <span>{{ item1.detaiInfo.name }}</span>
                <span
                  v-if="
                    item1.type == 'teamInvite' ||
                    item1.type == 'superTeamInvite'
                  "
                  >邀请你加入{{ item1.attach.team.name }}群</span
                >
                <span
                  v-else-if="
                    item1.type == 'rejectTeamInvite' ||
                    item1.type == 'rejectSuperTeamInvite'
                  "
                  >拒绝加入{{
                    !item1.teamInfo.error ? item1.teamInfo.obj.name : ""
                  }}群</span
                >
                <span
                  v-else-if="
                    item1.type == 'applyTeam' || item1.type == 'applySuperTeam'
                  "
                  >申请加入{{
                    !item1.teamInfo.error ? item1.teamInfo.obj.name : ""
                  }}群</span
                >
                <span
                  v-else-if="
                    item1.type == 'rejectTeamApply' ||
                    item1.type == 'rejectSuperTeamApply'
                  "
                  >拒绝了你加入{{
                    !item1.teamInfo.error ? item1.teamInfo.obj.name : ""
                  }}群</span
                >
                <span v-else
                  >{{
                    !item1.teamInfo.error ? item1.teamInfo.obj.name : ""
                  }}群</span
                >
              </h3>
              <p>
                附加消息：<span :title="item1.ps">{{ item1.ps }}</span>
              </p>
            </div>
            <div class="group-info-action">
              <slot
                v-if="
                  [
                    'teamInvite',
                    'applyTeam',
                    'superTeamInvite',
                    'applySuperTeam',
                  ].indexOf(item1.type) > -1
                "
              >
                <slot v-if="item1.state == 'init'">
                  <button
                    class="group-info-refuse"
                    @click="refuseInvite(item1)"
                    :disabled="batchFlag"
                  >
                    拒绝</button
                  ><button
                    class="group-info-agree"
                    :disabled="batchFlag"
                    @click="agreeInvite(item1)"
                  >
                    同意
                  </button>
                </slot>
                <button v-else-if="item1.state == 'passed'" disabled>
                  已同意
                </button>
                <button v-else-if="item1.state == 'rejected'" disabled>
                  已拒绝
                </button>
                <button v-else disabled>已处理</button>
              </slot>
            </div>
          </li>
        </ul>
      </section>
      <section v-show="tagType == 'quit' && quitTeamList.length > 0">
        <!-- 退群 -->
        <ul
          class="into-group-sys-msg-ul"
          v-for="(item, index) in quitTeamList"
          :key="index"
        >
          <li class="invite-date">{{ item.time }}</li>
          <li class="li-data" v-for="item1 in item.values" :key="item1.time">
            <div
              :class="[
                'check-box-div',
                activeIds.indexOf(item1.id) > -1 ? 'active' : '',
              ]"
              v-if="batchFlag && item1.status == '1'"
              @click="activeFunc(item1)"
            ></div>
            <div class="header-pic">
              <img
                :src="item1.detaiInfo.avatar"
                :onerror="
                  avatarError.bind(this, 'p2p', item1.detaiInfo.workerNo, '')
                "
              />
            </div>
            <div class="group-info-div">
              <h3>
                <span>{{ item1.detaiInfo.name }}</span
                ><span>已不符合该入群条件</span>
              </h3>
              <p>
                附加消息：<span
                  :title="`${item1.detaiInfo.workerName}已不符合入${item1.groupName}群条件,请确认是否让${item1.detaiInfo.name}退群`"
                  >{{
                    `${item1.detaiInfo.workerName}已不符合入${item1.groupName}群条件,请确认是否让${item1.detaiInfo.name}退群`
                  }}</span
                >
              </p>
            </div>
            <div class="group-info-action">
              <slot v-if="item1.status == '1'">
                <button
                  class="group-info-refuse"
                  :disabled="batchFlag"
                  @click="removeForTeam(item1)"
                >
                  移除本群</button
                ><button
                  class="group-info-agree"
                  :disabled="batchFlag"
                  @click="stayForTeam(item1)"
                >
                  留在本群
                </button>
              </slot>
              <button
                v-else-if="item1.status == '2'"
                class="group-info-refuse"
                disabled
              >
                已留在本群
              </button>
              <button
                v-else-if="item1.status == '3'"
                class="group-info-refuse"
                disabled
              >
                已移出本群
              </button>
            </div>
          </li>
        </ul>
      </section>
      <section
        class="no-data"
        v-show="
          (tagType == 'invite' && localMsgs.length == 0) ||
          (tagType == 'quit' && quitTeamList.length == 0)
        "
      >
        <img src="/img/content_none.png" width="180" height="110" />
        <p>暂无消息</p>
      </section>
    </main>
    <footer v-if="batchFlag">
      <LyButton @click="activeAll" class="ml6" size="small2" type="simple"
        >全选</LyButton
      ><LyButton @click="clearActiveAll" class="ml6" size="small2" type="simple"
        >重置</LyButton
      ><LyButton @click="handeBtn" class="ml6" size="small2" type="simple"
        >确认处理</LyButton
      >
    </footer>
    <LyDialog
      title="拒绝群邀请"
      :width="493"
      :height="230"
      :closeOnClickModal="true"
      :visible="agreeInviteDialog"
      @close="dialogClose"
      @confirm="dialogConfirm"
    >
      <div class="dialog-body">
        <p>请输入拒绝理由</p>
        <textarea
          placeholder="15字以内"
          v-model="reason"
          maxlength="15"
        ></textarea>
      </div>
    </LyDialog>
  </div>
</template>
<script>
import { useStore } from "vuex";
import { computed, ref, watch } from "vue";
import LyButton from "@comp/ui/comps/LyButton";
import { dateFormat, deepClone, avatarError } from "@utils";
import {
  quitTeamMsgsApi,
  clearTransactionMsgApi,
  disposeTransactionMsg,
  disposeTransactionMsgs,
} from "@utils/net/api.js";
import { loading, toast, alert } from "@comp/ui";
import LyDialog from "@comp/ui/comps/LyDialog";

const dialog = function (nim) {
  let agreeInviteDialog = ref(false);
  let tParams = ref({});
  let reason = ref("");
  function openDialog(params) {
    agreeInviteDialog.value = true;
    reason.value = "";
    tParams.value = params;
  }
  function dialogClose() {
    agreeInviteDialog.value = false;
  }
  async function dialogConfirm() {
    let res = await nim.rejectTeamInvite({
      ...tParams.value,
      ps: reason.value,
    });
    if (res.err) {
      toast({ title: res.err.message, type: 2 });
    }
    agreeInviteDialog.value = false;
  }
  return {
    openDialog,
    agreeInviteDialog,
    dialogClose,
    dialogConfirm,
    reason,
  };
};

export default {
  components: {
    LyButton,
    LyDialog,
  },
  setup() {
    const store = useStore();
    // 查询所有本地消息通知
    store.commit("getLocalSysMsgs");
    const userInfo = store.getters.getUserInfo;
    const localMsgs = ref([]);
    const nim = store.getters.getNim;
    watch(
      () => store.getters.getLocalMsgs,
      async (n, o) => {
        let res = await resolving(
          n,
          { empNo: "from", time: "time", teamType: "category", teamId: "to" },
          true
        );
        localMsgs.value = res;
      },
      { deep: true }
    );
    let tagType = ref("invite");
    function activeTag(val) {
      tagType.value = val;
      batchFlag.value = false;
    }
    const {
      agreeInviteDialog,
      openDialog,
      dialogClose,
      dialogConfirm,
      reason,
    } = dialog(nim);
    // 解析函数
    async function resolving(l, props, needTeam = false) {
      let list = deepClone(l);
      let empNo = props.empNo;
      let time = props.time;
      let teamId = props.teamId;
      let teamType = props.teamType;
      let person_obj = await store.dispatch("getPersons", {
        doneFlag: true,
        account: Array.from(
          new Set(
            list.map((i) => {
              return i[empNo];
            })
          )
        ),
      });
      // 需要构造一个结构便于渲染
      let n_l_obj = {};
      for (let i = 0; i < list.length; i++) {
        let item = list[i];
        let t_time = dateFormat(item[time], "MM月dd日");
        let t_time_key = dateFormat(item[time], "yyyyMMdd");
        item.detaiInfo = person_obj[item[empNo]];
        if (needTeam) {
          item.teamInfo = await nim.getTeam({
            account: item[teamId],
            type: item[teamType],
          });
        }
        if (n_l_obj[t_time_key]) {
          n_l_obj[t_time_key].values.push(item);
        } else {
          n_l_obj[t_time_key] = { time: t_time, values: [item] };
        }
      }
      let t_sort_key = Object.keys(n_l_obj).sort((a, b) => {
        return parseInt(b) - parseInt(a);
      });
      return t_sort_key.map((i) => {
        return n_l_obj[i];
      });
    }

    //退群信息
    let quitTeamList = ref([]);
    // 获取退群的信息
    async function quitTeamMsgs() {
      let res = await quitTeamMsgsApi({
        msgBody: JSON.stringify({ empNo: userInfo.workerNo }),
      });
      if (res.success) {
        quitTeamList.value = await resolving(res.data.data, {
          time: "createTime",
          empNo: "empNo",
        });
      }
    }

    //拒绝
    async function refuseInvite(item) {
      let params = {
        teamId: item.to,
        from: item.from,
        idServer: item.idServer,
        type: item.category,
      };
      if (item.type == "applyTeam" || item.type == "applySuperTeam") {
        let res = await nim.rejectTeamApply(params);
        if (res.err) {
          toast({ title: res.err.message, type: 2 });
        }
      } else if (item.type == "teamInvite" || item.type == "superTeamInvite") {
        // 弹窗
        openDialog(params);
      }
    }
    //同意
    async function agreeInvite(item) {
      loading();
      let params = {
        teamId: item.to,
        from: item.from,
        idServer: item.idServer,
        type: item.category,
      };
      let res;
      if (item.type == "applyTeam" || item.type == "applySuperTeam") {
        res = await nim.passTeamApply(params);
      } else if (item.type == "teamInvite" || item.type == "superTeamInvite") {
        res = await nim.acceptTeamInvite(params);
      }
      loading().hide();
      if (res.err) {
        toast({ title: res.err.message, type: 2 });
      }
    }
    quitTeamMsgs();

    let batchFlag = ref(false);
    let batchSearch = ref([]); //批量操作选中的数据
    let canSelect = computed(() => {
      let _canSelect = [];
      if (tagType.value == "invite") {
        localMsgs.value.forEach((i) => {
          i.values.forEach((j) => {
            if (
              [
                "teamInvite",
                "applyTeam",
                "superTeamInvite",
                "applySuperTeam",
              ].indexOf(j.type) > -1 &&
              j.state == "init"
            ) {
              _canSelect.push(j);
            }
          });
        });
      } else {
        quitTeamList.value.forEach((i) => {
          i.values.forEach((j) => {
            if (j.status == "1") {
              _canSelect.push(j);
            }
          });
        });
      }
      return _canSelect;
    });
    let activeIds = computed(() => {
      return batchSearch.value.map((i) => {
        if (tagType.value == "invite") {
          return i.idServer;
        } else {
          //退群用的什么id？。。
          return i.id;
        }
      });
    });
    // 打开-关闭批量
    function openBatch() {
      if (!batchFlag.value) {
        batchSearch.value = [];
      }
      batchFlag.value = !batchFlag.value;
    }

    // 点击前面的选择框,type=1点击选择框，=2直接选择
    function activeFunc(item, type = 1) {
      let t_index;
      if (tagType.value == "invite") {
        t_index = activeIds.value.indexOf(item.idServer);
      } else {
        //退群用的什么id？。。
        t_index = activeIds.value.indexOf(item.idServer);
      }
      if (t_index > -1) {
        if (type == 1) {
          batchSearch.value.splice(t_index, 1);
        }
      } else {
        batchSearch.value.push(item);
      }
    }

    // 选中全部
    function activeAll() {
      if (tagType.value == "invite") {
        localMsgs.value.forEach((i) => {
          i.values.forEach((j) => {
            if (
              [
                "teamInvite",
                "applyTeam",
                "superTeamInvite",
                "applySuperTeam",
              ].indexOf(j.type) > -1 &&
              j.state == "init"
            ) {
              activeFunc(j, 2);
            }
          });
        });
      } else {
      }
    }
    // 确认处理
    function handeBtn() {
      if (batchSearch.value.length == 0) {
        toast({ title: "请选择您要操作的项", type: 2 });
      } else {
        alert({
          content:
            tagType.value == "invite"
              ? `请您选择是否将选中的成员加入本群？`
              : `请您选择是否将选中的成员留在本群？`,
          cancelText: tagType.value == "invite" ? "拒绝" : "留在本群",
          okText: tagType.value == "invite" ? "确定" : "移除本群",
          done: async (type) => {
            if (type == 1) {
              batchFlag.value = false;
              if (tagType.value == "invite") {
                batchSearch.value.forEach((i) => {
                  agreeInvite(i);
                });
              } else {
                let moveIds = [];
                let empNos = [];
                let tids = [];
                let personObj = {};
                batchSearch.value.forEach((i) => {
                  moveIds.push(i.id);
                  empNos.push(i.empNo);
                  tids.push(i.tid);
                  personObj[i.id] = i.detaiInfo.userName;
                });
                let res = await disposeTransactionMsgs({
                  msgBody: JSON.stringify({
                    moveIds: moveIds.join(","),
                    type: "3",
                    empNo: empNos.join(","),
                    tid: tids.join(","),
                  }),
                });
                if (res.success) {
                  let t = [];
                  res.data.data.forEach((i) => {
                    let tt = "";
                    if (i.status == "0") {
                      tt =
                        personObj[i.result.id] +
                        " 移除 " +
                        i.result.groupName +
                        " 失败!  ";
                    } else {
                      tt =
                        personObj[i.result.id] +
                        " 移除 " +
                        i.result.groupName +
                        " 成功!  ";
                    }
                    t.push(tt);
                  });
                  toast({ title: t.join("\n"), type: 1 });
                  quitTeamMsgs();
                } else {
                  toast({ title: res.errorMsg || "系统错误", type: 2 });
                }
              }
            } else if (type == 2) {
              batchFlag.value = false;
              // 拒绝
              if (tagType.value == "invite") {
                batchSearch.value.forEach((i) => {
                  nim.rejectTeamInvite({
                    teamId: i.to,
                    from: i.from,
                    idServer: i.idServer,
                    type: i.category,
                    ps: "批量处理拒绝入群.",
                  });
                });
              } else {
                let moveIds = [];
                let empNos = [];
                let tids = [];
                let personObj = {};
                batchSearch.value.forEach((i) => {
                  moveIds.push(i.id);
                  empNos.push(i.empNo);
                  tids.push(i.tid);
                  personObj[i.id] = i.detaiInfo.userName;
                });

                let res = await disposeTransactionMsgs({
                  msgBody: JSON.stringify({
                    moveIds: moveIds.join(","),
                    type: "2",
                    empNo: empNos.join(","),
                    tid: tids.join(","),
                  }),
                });
                if (res.success) {
                  let t = [];
                  res.data.data.forEach((i) => {
                    let tt = "";
                    if (i.status == "0") {
                      tt =
                        personObj[i.result.id] +
                        " 留在 " +
                        i.result.groupName +
                        " 失败!  ";
                    } else {
                      tt =
                        personObj[i.result.id] +
                        " 留在 " +
                        i.result.groupName +
                        " 成功!  ";
                    }
                    t.push(tt);
                  });
                  toast({ title: t.join("\n"), type: 1 });
                  quitTeamMsgs();
                } else {
                  toast({ title: res.errorMsg || "系统错误", type: 2 });
                }
              }
            }
          },
        });
      }
    }

    // 重置
    function clearActiveAll() {
      batchSearch.value = [];
    }

    // 清空所有通知消息
    async function removeAll() {
      if (tagType.value == "invite") {
        let res = await nim.deleteAllLocalSysMsgs();
        if (!res.err) {
          store.commit("getLocalSysMsgs");
        }
      } else {
        let res = await clearTransactionMsgApi({
          msgBody: JSON.stringify({ empNo: userInfo.workerNo }),
        });
        if (res.success) {
          quitTeamList.value = [];
        } else {
          toast({ title: res.errorMsg || "系统错误", type: 2 });
        }
      }
    }

    // 移除本群
    async function removeForTeam(item) {
      let res = await disposeTransactionMsg({
        msgBody: JSON.stringify({
          moveId: item.id,
          type: "3",
          empNo: item.empNo,
          tid: item.tid,
        }),
      });
      if (res.success) {
        item.status = "3";
      } else {
        toast({ title: res.errorMsg || "系统错误", type: 2 });
      }
    }

    // 留在本群
    async function stayForTeam(item) {
      let res = await disposeTransactionMsg({
        msgBody: JSON.stringify({
          moveId: item.id,
          type: "2",
          empNo: item.empNo,
          tid: item.tid,
        }),
      });
      if (res.success) {
        item.status = "2";
      } else {
        toast({ title: res.errorMsg || "系统错误", type: 2 });
      }
    }

    return {
      avatarError,
      removeForTeam,
      stayForTeam,
      canSelect,
      handeBtn,
      activeAll,
      clearActiveAll,
      activeIds,
      batchSearch,
      activeFunc,
      removeAll,
      openBatch,
      refuseInvite,
      agreeInvite,
      quitTeamList,
      tagType,
      activeTag,
      localMsgs,
      agreeInviteDialog,
      openDialog,
      dialogClose,
      dialogConfirm,
      reason,
      batchFlag,
    };
  },
};
</script>
<style lang="scss" scoped>
.team-notify {
  height: calc(100% - 64px);
  header {
    height: 40px;
    border-bottom: 1px solid #ededed;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    line-height: 26px;
    padding: 0 15px;
    .header-left {
      font-size: 14px;
      color: #000;
      > span {
        cursor: pointer;
        display: inline-block;
        width: 70px;
        height: 100%;
        text-align: center;
        &:hover {
          color: #4e9ee1;
        }
        &.active {
          color: #000 !important;
          border-bottom: 2px solid #4e9ee1;
        }
      }
    }
  }
  main {
    padding: 0 15px;
    overflow-y: auto;
    height: calc(100% - 40px);
    &.has-footer {
      height: calc(100% - 85px);
    }
    section {
      &.no-data {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        height: 100%;
        > img {
          margin-top: -60px;
        }
        > p {
          font-size: 14px;
          margin-top: 10px;
          color: #999;
        }
      }
      .into-group-sys-msg-ul {
        li {
          position: relative;
          padding: 10px 0;
          overflow: hidden;
          &.invite-date {
            font-size: 12px;
            line-height: 17px;
            color: #999999;
            padding-bottom: 2px;
          }
          &.li-data {
            .check-box-div {
              cursor: pointer;
              position: relative;
              float: left;
              margin: 12px 12px 12px 0;
              width: 16px;
              height: 16px;
              border: none;
              border-radius: 50%;
              background: url(/img/allicon1.png) -725px -50px;
              &.active {
                background: url(/img/allicon1.png) -703px -50px;
              }
            }
            .header-pic {
              float: left;
              width: 40px;
              height: 40px;
              margin: 0px 10px 0 1px;
              box-shadow: 0 0 3px #ccc;
              overflow: hidden;
              img {
                display: block;
                width: 40px;
                margin-top: -4px;
              }
            }
            .group-info-div {
              font-size: 13px;
              float: left;
              width: calc(100% - 250px);
              color: #666;
              line-height: 13px;
              h3 {
                font-size: 13px;
                font-weight: 400;
                line-height: 13px;
                > span:nth-of-type(1) {
                  margin-right: 10px;
                  color: #0084ed;
                  cursor: pointer;
                }
              }
              p {
                color: #666;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-top: 12px;
              }
            }
            .group-info-action {
              float: right;
              margin: 8px 0;
              button {
                float: left;
                min-width: 60px;
                height: 24px;
                margin-right: 10px;
                padding: 0 5px;
                color: #000;
                text-align: center;
                cursor: pointer;
                outline: none;
                border: 1px solid #d2d2d2;
                border-radius: 2px;
                background: #ffffff;
                &:hover {
                  background: #eceeef;
                }
                &:disabled {
                  background: #eceeef;
                }
              }
            }
          }
        }
      }
    }
  }
  footer {
    height: 45px;
    border-top: 1px solid #d8d8d8;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 15px;
  }
  .dialog-body {
    padding: 15px;
    p {
      margin-bottom: 10px;
    }
    textarea {
      resize: none;
      border-radius: 3px;
      width: 100% !important;
      height: 100px !important;
      background-color: #e8ecf0;
      padding: 10px 8px;
      box-sizing: border-box;
      border: 1px solid #ccc;
    }
  }
}
</style>