<template>
  <div class="chat-list" :class="'chat-list-'+showType">
    <!--乐文档-->
    <div class="chat-list-box" v-if="sessionListInfo.sessionTab=='doc'">
      <ul class="chat-ul" ref="chatUlRef" v-show="sessionListInfo.sessionList.length>0">
        <li class="chat-li" v-for="(item,key) in sessionListInfo.sessionList" :key="item.id" @click="openChat(item,'doc')">
          <div class="chat-li-box">
            <div class="user-avatar-box">
              <div class="avatar-box">
                <img class="avatar" :src="`/img/default/${item.property==2?'excel':'doc'}.png`">
              </div>
            </div>
            <div class="content-box">
              <!--名字和时间-->
              <div class="name-box">
                <span class="name" :title="item.title">{{ item.title }}</span>
              </div>
              <!--内容和关闭-->
              <div class="content textEls">
                <div class="content-details">{{ item.empName }}({{ item.deptName }})</div>
              </div>
            </div>
          </div>
        </li>
      </ul>
      <div class="chat-none" v-show="sessionListInfo.sessionList.length==0">
        <img v-show="sessionListInfo.sessionLoading" class="loading-img" src="/img/waitting.gif" alt="">
        <img v-show="!sessionListInfo.sessionLoading" class="none-img" src="/img/index/list_none.png" alt="">
        <div>{{ sessionListInfo.sessionLoading ? "加载中" : "暂无乐文档" }}</div>
      </div>
    </div>
    <!--会话列表-->
    <div class="chat-list-box" v-else-if="sessionType!=11">
      <div class="session-concern" v-show="sessionListInfo.sessionTab=='all'&&sessionType==-1&&sessionListInfo.sessionList.length>0&&sessionListInfo.concernCount>0" @click="scrollToChat(1)">
        <span class="concern-box">
          <i class="icon-concern"></i>
          <span class="concern-num">{{ sessionListInfo.concernCount }}</span>
          <span>个特别关心</span>
        </span>
        <i class="icon-concern concern-arrow"></i>
      </div>
      <!--获取fcw客户会话-->
      <div class="fcw-tips-box" v-show="sessionListInfo.sessionTab=='customer'&&sessionListInfo.sessionList.length<10">
        <span class="fcw-tips-content">
          <span class="textEls">点击右方按钮可获取历史会话</span>
          <i class="icon-question" @mouseenter="showTipsBox($event,'更换设备，客户消息丢失，可操<br>作找回最近100个客户会话')" @mouseleave="showTipsBox"></i>
        </span>
        <span class="fcw-tips-btn" @click="getFcwSession()">获取会话</span>
      </div>
      <ul class="chat-ul" :class="{
        'concern-show':(sessionType==-1&&sessionListInfo.concernCount>0)||(sessionListInfo.sessionTab=='customer'&&sessionListInfo.sessionList.length<10),
        'fold-top':topObj.showTop&&sessionListInfo.sessionTab=='all'}"
          ref="chatUlRef" v-show="sessionListInfo.sessionList.length>0" @scroll="scrollChat()">
        <div class="chat-ul-box" :style="{height:topObj.listHeight+'px'}">
          <ul class="chat-ul-scroll" :style="{top:topObj.top+'px'}">
            <li class="chat-li" v-for="(item,key) in sessionListInfo.sessionList.slice(topObj.start,topObj.end)" :key="item.id" :data-id="item.id"
                :class="{'chat-badge':item.unread&&!(item.isHelper||item.isNoTip),'curr':isCurrent(item.id),'is-concern':item.isConcern}">
              <div class="chat-li-box" :account="item.id" @dragstart="dragList($event, 1)" @dragend="dragList($event, 2, item)"
                   :draggable="sessionType==-1&&topObj.resizeKey==-1?true:false" @click="openChat(item,'',true)" @contextmenu.prevent="setMenu($event, item)"
                   :class="{'curr':isCurrent(item.id),'top':item.isTop,'always-top':item.alwaysTop,'unread':item.unread,
                 'show-top-btn':item.id==lastSessionTopId&&sessionListInfo.sessionTab=='all'&&topObj.flag&&key==sessionListInfo.sessionList.length-1}">
                <div class="user-avatar-box" :class="{'border': item.scene=='p2p'&&!isNaN(item.to)}">
                  <div class="avatar-box">
                    <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                  </div>
                  <img v-show="userWearPic(item.detailInfo)" class="user-label" :src="userWearPic(item.detailInfo)" :onerror="hideElm">
                  <div v-show="item.detailInfo.wearType==2" class="user-label-text">{{ item.detailInfo.wearName }}</div>
                </div>
                <div class="content-box">
                  <!--名字和时间-->
                  <div class="name-box">
                    <span class="name" :class="item.detailInfo.empStatus==4?'resign':''">{{ item.detailInfo.userShowName || item.detailInfo.name }}</span>
                    <span class="time" v-show="item.showLastTime">{{ transTime(item.showLastTime) }}</span>
                  </div>
                  <!--内容和关闭-->
                  <div class="content textEls">
                    <div class="content-details">
                      <!--草稿-->
                      <span class="draft" v-if="sessionListInfo.sessionInfo.id!=item.id&&hasEditorContent(item.id)">[草稿]</span>
                      <!--群助手提醒-->
                      <span v-else-if="isHelper(item)&&!item.lastMsg" title='[查看"收进群助手且不提醒"的群]'>[查看"收进群助手且不提醒"的群]</span>
                      <!--群助手未读提醒-->
                      <span v-else-if="isHelper(item)&&item.helperNum">[有{{ item.helperNum }}个群聊有未读消息]</span>
                      <!--消息内容-->
                      <span v-else>
                        <!--在线-->
                        <span class="user-online" v-show="item.userOnline">在线</span>
                        <span class="user-line" v-show="item.userOnline&&item.fcwShowUnread">|</span>
                        <span v-show="item.showLastMsg">
                          <!--已读/未读-->
                          <span :class="['user-read',item.fcwShowUnread==1?'unread':'']" v-show="item.fcwShowUnread">{{ item.fcwShowUnread == 1 ? "[未读]" : "[已读]" }}</span>
                          <!--发送错误-->
                          <span class="msg-err-icon" v-show="item.showLastStatus=='fail'"></span>
                          <!--@提醒-->
                          <span class="hait-tips" v-show="item.scene!='p2p'&&item.haitInfo?.isShowTips">
                            {{ item.haitInfo?.isHaitAll ? "[有全体消息]" : "[有人@我]" }}
                          </span>
                          <!--特别关心提醒-->
                          <span class="concern-tips" v-show="item.isConcern">
                            {{ item.scene == "p2p" ? "[特别关心]" : "[有关注的内容]" }}
                          </span>
                          <!--具体消息内容-->
                          <span v-html="item.showLastMsg"></span>
                        </span>
                      </span>
                    </div>
                    <!--关闭-->
                    <i class="close" v-show="showClose&&item.to!=config.customerAccount" @click.stop="closeChat(item.id)"></i>
                  </div>
                </div>
                <span class="badge" :class="[item.isHelper||item.isNoTip?'not-tips':'',isSubBox(item)?'badge-point':'']"
                      v-show="item.unread">{{ isSubBox(item) ? '' : item.unread > 99 ? "99+" : item.unread }}</span>
                <span class="mark-label-box">
                  <span class="icon-mark is-remind" v-show="getLocalUnreadMap(item.id)"></span>
                  <span class="icon-mark is-mark" v-show="getImportantMarkMap(item.id)"></span>
                </span>
              </div>
              <div class="top-toggle-btn" :class="{'bottom':topObj.isBottom,'show':topObj.showTop}" :style="{width:topObj.isBottom?topObj.bottomWidth+'px':'100%'}"
                   v-if="item.id==lastSessionTopId" v-show="topObj.flag&&sessionListInfo.sessionTab=='all'&&sessionType==-1"
                   @click="toggleShowTop()">{{ topObj.showTop ? topObj.num + "个置顶聊天" : "折叠置顶聊天" }}
              </div>
            </li>
          </ul>
        </div>
      </ul>
      <div :class="['chat-none',sessionListInfo.sessionTab=='customer'?'fcw-tips-none':'']" v-show="sessionListInfo.sessionList.length==0">
        <img class="none-img" src="/img/index/list_none.png" alt="">
        <div class="tips-box" v-if="(sessionListInfo.sessionTab||'').indexOf('-')>-1&&(sessionListInfo.sessionTab||'').indexOf('groupType-')==-1">
          <div>暂无{{ sessionListInfo.classifyName }}消息</div>
          <div class="tips-add-text">
            <span>快速</span>
            <span class="add-text" @click="addClassifyGroup()">添加{{ sessionListInfo.classifyName }}</span>
            <span>，分类管理消息</span>
          </div>
        </div>
        <div v-else>{{
            sessionListInfo.sessionTab == "customer" ? "暂无客户" : sessionListInfo.sessionTab == "hait" ? "暂无@消息" : sessionListInfo.sessionTab == "remind" ?
              "暂无稍后处理" : sessionListInfo.sessionTab == "doc" ? "暂无乐文档" : sessionListInfo.sessionTab == "team" ? "暂无群聊消息" :
                sessionListInfo.sessionTab == "mark" ? "暂无标记消息" : sessionListInfo.sessionTab == "p2p" ? "暂无单聊消息" : "暂无消息"
          }}
        </div>
      </div>
    </div>
    <!--公众号+订阅号-->
    <div class="chat-list-box" v-else-if="sessionType==11">
      <div class="sub-ser-list" ref="chatSubRef">
        <dl class="sub-box">
          <dt class="sub-title" @click="toggleShowSub(1)">
            <i class="sub-arrow" :class="{active:subAndSerObj.serShow}"></i>
            <span>服务号 {{ subAndSerObj.ser.length }}</span>
          </dt>
          <dd v-show="subAndSerObj.serShow">
            <ul class="chat-ul">
              <li class="chat-li" :class="{'curr':isCurrent(item.id),'chat-badge':item.unread}" :account="item.id"
                  v-for="(item,key) in subAndSerObj.ser" :key="item.id" @click="openChat(item)">
                <div class="chat-li-box">
                  <div class="user-avatar-box">
                    <div class="avatar-box">
                      <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                    </div>
                  </div>
                  <div class="content-box">
                    <div class="name-box">
                      <span class="name">{{ item.detailInfo.name }}</span>
                    </div>
                  </div>
                  <span class="badge" v-show="item.unread">{{ item.unread > 99 ? "99+" : item.unread }}</span>
                </div>
              </li>
            </ul>
          </dd>
        </dl>
        <dl class="sub-box">
          <dt class="sub-title" @click="toggleShowSub(2)">
            <i class="sub-arrow" :class="{active:subAndSerObj.subShow}"></i>
            <span>订阅号 {{ subAndSerObj.sub.length }}</span>
          </dt>
          <dd v-show="subAndSerObj.subShow">
            <ul class="chat-ul">
              <li class="chat-li" :class="{'curr':isCurrent(item.id),'chat-badge':item.unread}" :account="item.id"
                  v-for="(item,key) in subAndSerObj.sub" :key="item.id" @click="openChat(item)">
                <div class="chat-li-box">
                  <div class="user-avatar-box">
                    <div class="avatar-box">
                      <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, item.scene, item.to , item.detailInfo.detailType||'')">
                    </div>
                  </div>
                  <div class="content-box">
                    <div class="name-box">
                      <span class="name">{{ item.detailInfo.name }}</span>
                    </div>
                  </div>
                  <span class="badge" v-show="item.unread">{{ item.unread > 99 ? "99+" : item.unread }}</span>
                </div>
              </li>
            </ul>
          </dd>
        </dl>
      </div>
      <div class="chat-none" v-show="subAndSerObj.allList.length==0">
        <img class="none-img" src="/img/index/list_none.png" alt="">
        <div>暂无最近联系人</div>
      </div>
    </div>
  </div>
</template>
<script>
import {nextTick, onMounted, ref, watch} from "vue";
import {useStore} from "vuex";
import {
  transTime, avatarError, showMenu, emitMsg, hideElm, hasContent, getSessionType, getOffset, throttle, debounce, userWearPic, isDisabledGroup, setJJSEvent,
  isFcwList, scrollLi, deepClone, setSessionField
} from "@utils"
import {alert, toast} from "@comp/ui";
import {addSpecialConcernApi, deleteSpecialConcernApi, searchDocApi, updateActiveAccidsApi} from "@utils/net/api";

export default {
  name: "ChatList",
  props: {
    // 是否允许关闭会话
    showClose: {
      type: Boolean,
      default: true,
    },
    // 列表类型-utils.js/index.js的getSessionType
    sessionType: {
      type: String,
      default: "-1",
    },
    // 打开会话方法
    openChatMethods: {
      type: Function,
    },
    // 显示类型-1类型变更
    showType: {
      type: String
    },
    // 变更会话类型数量方法
    changeTabInfo: {
      type: Boolean,
      default: false,
    },
    // 是否会话列表-用户区分订阅号列表
    isSession: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, ctx) {
    const store = useStore();
    const chatUlRef = ref();
    const chatSubRef = ref();
    // 获取用户信息
    const userInfo = store.getters.getUserInfo;
    let baseComputerInfo = store.getters.getBaseComputerInfo;
    // 云信对象
    let nim = store.getters.getNim;
    // 配置文件
    let config = store.getters.getConfig.config;
    // 服务号+订阅号列表
    let subAndSerObj = ref({
      allList: [],
      sub: [],
      subShow: true,
      ser: [],
      serShow: true,
    });
    // 拖拽对象
    let dragObj = {
      startX: 0,
      startY: 0,
    }
    // 置顶对象
    let topObj = ref({
      flag: store.getters.getIsFoldTop,// 是否显示切换折叠置顶
      showTop: store.getters.getIsFoldTop,// 是否显示置顶会话
      isBottom: false,// 是否显示在底部
      bottomWidth: 239,// 折叠置顶在底部的宽度
      num: props.sessionType == -1 ? store.getters.getTopObj.foldTopNum : 0,// 可折叠置顶会话数
      topNum: props.sessionType == -1 ? store.getters.getTopObj.topNum : 0,// 置顶会话数
      start: 0,// 可视区域开始下标
      end: 0,// 可视区域结束下标
      height: 64,// chat-li-box元素高度
      index: -1,// 置顶会话下标
      top: 0,// 显示会话偏移高度
      listHeight: 0,// 列表总高度
      resizeKey: -1,// 拖拽1分组2列表
    });
    // 设置当前会话
    let openChaObj = {
      time: 0,
      id: ""
    };
    // 最后一个会话
    let lastSessionTopId = ref(store.getters.getLastSessionTopId);
    // 会话数据
    let sessionListInfo = ref({
      sessionTab: store.getters.getSessionTab,// 列表选择显示对象
      classifyType: "",// 分组类型
      classifyName: "",// 分组名
      concernCount: 0,// 特别关心数量
      sessionLoading: false,// 是否在加载状态
      sessionInfo: {},// 当前会话信息
      sessionList: [],// 会话列表
      sessionTabClassifyItem: store.getters.getSessionTabClassifyItem,// 分组选中对象
    });
    // 获取fcw会话对象
    let fcwSessionObj = {
      timer: false,
      flag: false
    };
    if (store.getters.getCurrentSession.id) {
      sessionListInfo.value.sessionInfo = store.getters.getSessions({id: store.getters.getCurrentSession.id})
    }
    onMounted(() => {
      nextTick(() => {
        // 获取当前显示会话列表
        getCurrentSession(sessionListInfo.value.sessionTab, "", "", true, "", sessionListInfo.value.sessionTabClassifyItem);
        // 折叠状态计数
        isShowToggleTop();
        if (chatUlRef.value && chatUlRef.value.querySelector(".chat-li-box")) {
          topObj.value.height = chatUlRef.value.querySelector(".chat-li-box").clientHeight;
        }
      });
    });

    // 会话更新
    watch(() => store.state.updateSessionsTime,
      (newValue, oldValue) => {
        throttle({
          timerName: `updateSessionTab-${props.sessionType}`,
          time: 100,
          fnName: function () {
            getCurrentSession(sessionListInfo.value.sessionTab, "", 1, "", "", sessionListInfo.value.sessionTabClassifyItem);
          }
        });
      }, {
        deep: true
      }
    );
    // 监听选择列表类型
    watch(() => store.state.emit.selSessionTab,
      (newValue, oldValue) => {
        if (newValue) {
          getCurrentSession(newValue.key, newValue.scrollTop, newValue.update, newValue.click, newValue.id, newValue.classifyItem);
        }
      }, {
        deep: true
      }
    );
    // 监听当前会话
    watch(() => store.state.currentSession.id,
      (newValue, oldValue) => {
        if (newValue) {
          sessionListInfo.value.sessionInfo = store.getters.getSessions({id: newValue});
        } else {
          sessionListInfo.value.sessionInfo = {};
        }
      }, {
        deep: true
      }
    );
    // 子窗口变更
    watch(() => store.state.childWin,
      (newValue, oldValue) => {
        getCurrentSession();
      }, {
        deep: true
      }
    );
    // 监听最后一个置顶会话变化
    watch(() => store.state.lastSessionTopId,
      (newValue, oldValue) => {
        lastSessionTopId.value = newValue;
        if (lastSessionTopId.value) {
          // 计算最后一个置顶会话下标
          for (let i = 0; i < sessionListInfo.value.sessionList.length; i++) {
            if (sessionListInfo.value.sessionList[i].id == lastSessionTopId.value) {
              topObj.value.index = i;
              break;
            }
          }
        }
      }, {
        deep: true
      }
    );
    // 上下切换会话
    watch(() => store.state.emit.keydown,
      (newValue, oldValue) => {
        throttle({
          timerName: "toggleSession",
          time: 50,
          fnName: function () {
            let item = "";
            let key = "";
            switch (newValue.key) {
              case "ArrowUp":
                key = 1;
                if (store.getters.getEmit.toggleSession) {
                  for (let i = 0; i < sessionListInfo.value.sessionList.length; i++) {
                    if (isCurrent(sessionListInfo.value.sessionList[i].id)) {
                      if (i > 0) {
                        item = sessionListInfo.value.sessionList[i - 1];
                      }
                      break;
                    }
                  }
                }
                break;
              case "ArrowDown":
                key = 2;
                if (store.getters.getEmit.toggleSession) {
                  for (let i = 0; i < sessionListInfo.value.sessionList.length; i++) {
                    if (isCurrent(sessionListInfo.value.sessionList[i].id)) {
                      if (i != sessionListInfo.value.sessionList.length - 1) {
                        item = sessionListInfo.value.sessionList[i + 1];
                      }
                      break;
                    }
                  }
                }
                break;
            }
            if (item) {
              scrollLi(chatUlRef.value, ".chat-li", key, scrollToChat.bind(this, 3));
              openChat(item);
            }
          }
        });
      }, {
        deep: true
      }
    );
    // 当前窗口拖拽缩放
    watch(() => store.state.emit.resize,
      (newValue, oldValue) => {
        scrollChat();
      }, {
        deep: true
      }
    );
    // 滚动到指定
    watch(() => store.state.emit.scrollSessionList,
      (newValue, oldValue) => {
        scrollToChat(2);
      }, {
        deep: true
      }
    );
    // 滚动到当前会话
    watch(() => store.state.emit.scrollCurrentSession,
      (newValue, oldValue) => {
        // 滚动会话列表到当前会话
        nextTick(() => {
          scrollToChat(3);
        });
      }, {
        deep: true
      }
    );
    // 获取可折叠置顶数量
    watch(() => store.state.topObj,
      (newValue, oldValue) => {
        if (props.sessionType == -1) {
          topObj.value.num = newValue.foldTopNum;
          topObj.value.topNum = newValue.topNum;
        } else {
          topObj.value.num = 0;
          topObj.value.topNum = 0;
        }
      }, {
        deep: true
      }
    );
    // 获取窗口分组/列表/内容是否在拖拽状态
    watch(() => store.state.emit.resizeObj,
      (newValue, oldValue) => {
        topObj.value.resizeKey = newValue.key;
      }, {
        deep: true
      }
    );
    // 监听会话状态
    watch(() => store.state.updateSessionId,
      (newValue, oldValue) => {
        // 客户会话需要更新列表已读/未读和在线状态
        if (newValue && new RegExp(config.fcw).test(newValue) &&
          (/customerClassify-/.test(sessionListInfo.value.sessionTab) || sessionListInfo.value.sessionTab == "customer" || sessionListInfo.value.sessionInfo.id == "p2p-" + config.customerAccount)) {
          for (let i = 0; i < sessionListInfo.value.sessionList.length; i++) {
            let item = sessionListInfo.value.sessionList[i];
            if (item.id == newValue) {
              let itemSessionInfo = deepClone(store.getters.getSessions({id: newValue}));
              setSessionField(itemSessionInfo);
              if (item.fcwShowUnread != itemSessionInfo.fcwShowUnread || item.userOnline != itemSessionInfo.userOnline) {
                sessionListInfo.value.sessionList[i] = itemSessionInfo;
              }
              break;
            }
          }
        }
      }, {
        deep: true
      }
    );
    // 乐聊分组模式
    watch(() => store.state.windowSizeType,
      (newValue, oldValue) => {
        // 列表宽度变化重新计算底部折叠宽度
        if (newValue.listWidth != oldValue.listWidth) {
          isShowToggleTop();
        }
      }, {
        deep: true
      }
    );

    // 获取当前会话列表
    function getCurrentSession(key, scrollTop, update, click, id, classifyItem) {
      // 初始化显示列表
      switch (props.sessionType) {
        case "-1":
          if (!update) {
            console.time("切换会话");
          }
          // 切换列表重置会换
          let keyFlag = false;
          if (key) {
            if (sessionListInfo.value.sessionTab != key && !id) {
              store.dispatch("resetCurrSession");
              keyFlag = true;
            }
            sessionListInfo.value.sessionTab = key;
            // 判断当前的分组类型
            if (/groupClassify-/.test(key)) {
              sessionListInfo.value.classifyType = 2;
              sessionListInfo.value.classifyName = "讨论组/群";
            } else if (/colleagueClassify-/.test(key)) {
              sessionListInfo.value.classifyType = 3;
              sessionListInfo.value.classifyName = "同事";
            } else if (/customerClassify-/.test(key)) {
              sessionListInfo.value.classifyType = 4;
              sessionListInfo.value.classifyName = "客户";
            } else {
              sessionListInfo.value.classifyType = "";
              sessionListInfo.value.classifyName = "";
            }
            store.commit("setSessionTab", key);
            if (classifyItem) {
              sessionListInfo.value.sessionTabClassifyItem = classifyItem;
              store.commit("setSessionTabClassifyItem", classifyItem);
            }
          }
          let tabSessionInfo = store.getters.getSessions({sort: -1});
          let currId = store.getters.getCurrentSession.id;
          // 默认列表
          let defaultList = tabSessionInfo["-1"];
          // 客户列表
          let customerList = tabSessionInfo["5"];
          // @我
          let haitList = tabSessionInfo["hait"];
          // 稍后处理
          let remindList = tabSessionInfo["remind"];
          sessionListInfo.value.concernCount = tabSessionInfo["concernCount"];
          // 群
          let teamList = tabSessionInfo["2"];
          // 同事
          let colleagueList = tabSessionInfo["4"];
          // ai
          let aiList = tabSessionInfo["12"];
          // 群和讨论组
          let groupAndTeam = tabSessionInfo["groupAndTeam"];
          // 标记
          let markList = tabSessionInfo["mark"];
          // 直接下属
          let subordinateList = tabSessionInfo["subordinate"];
          // 业务讨论组列表
          let groupTypeList = tabSessionInfo["groupTypeList"];
          if (props.changeTabInfo) {
            store.commit("setEmit", {
              type: "changeTabInfo", value: {
                key: sessionListInfo.value.sessionTab,// 当前显示的会话类型
                allCount: tabSessionInfo.unreadMap["-1"],// 默认会话未读数
                customerUnread: tabSessionInfo["khzxUnread"],// 客户咨询未读数
                haitCount: haitList.length,// @我会话数量
                remindCount: remindList.length,// 稍后处理会话数量
                concernCount: sessionListInfo.value.concernCount,// 特别关心会话数量
                teamCount: tabSessionInfo.unreadMap[" 2"],// 群未读数
                aiCount: tabSessionInfo.unreadMap["12"],// ai未读数
                groupChildList: tabSessionInfo.groupChildList,// 讨论组分组会话列表
                colleagueCount: tabSessionInfo.unreadMap["4"],// 同事会话未读数
                colleagueChildList: tabSessionInfo.colleagueChildList,// 同事分组会话列表
                groupAndTeamCount: tabSessionInfo.unreadMap["groupAndTeam"],//讨论组和群分组未读数
                customerChildList: tabSessionInfo.customerChildList,// 客户分组会话列表
                markCount: tabSessionInfo.unreadMap["mark"],// 标记会话列表未读数
                subordinateCount: tabSessionInfo.unreadMap["subordinate"],// 直接下属未读数
                groupTypeList: groupTypeList,
              }
            });
          }
          switch (sessionListInfo.value.sessionTab) {
            case "customer":
              sessionListInfo.value.sessionList = customerList;
              store.dispatch("setFcwOnlineEvent");
              break;
            case "hait":
              if (currId && haitList.findIndex(item => {return item.id == currId}) == -1) {
                haitList.unshift(store.getters.getSessions({id: currId}));
              }
              sessionListInfo.value.sessionList = haitList;
              break;
            case "remind":
              if (currId && remindList.findIndex(item => {return item.id == currId}) == -1) {
                remindList.unshift(store.getters.getSessions({id: currId}));
              }
              sessionListInfo.value.sessionList = remindList;
              break;
            case "doc":
              if (!key || !click) {
                return;
              }
              sessionListInfo.value.sessionList = [];
              sessionListInfo.value.sessionLoading = true;
              searchDocApi({
                title: "",
                rows: 50,
                property: -1,
              }).then(res => {
                sessionListInfo.value.sessionLoading = false;
                if (sessionListInfo.value.sessionTab != "doc") {
                  return;
                }
                if (res.success) {
                  sessionListInfo.value.sessionList = res.data.list;
                }
                // 是否滚动到顶部
                if (scrollTop) {
                  nextTick(() => {
                    chatUlRef.value.scrollTop = 0;
                  });
                }
              });
              break;
            case "team":
              sessionListInfo.value.sessionList = teamList;
              break;
            case "p2p":
            case "colleagueClassify":
              sessionListInfo.value.sessionList = colleagueList;
              break;
            case "groupClassify":
              sessionListInfo.value.sessionList = groupAndTeam;
              break;
            case "ai":
              sessionListInfo.value.sessionList = aiList;
              break;
            case "mark":
              sessionListInfo.value.sessionList = markList;
              break;
            case "subordinate":
              sessionListInfo.value.sessionList = subordinateList;
              break;
            default:
              if (/groupClassify-/.test(sessionListInfo.value.sessionTab)) {
                // 讨论组分组列表
                for (let i = 0; i < tabSessionInfo.groupChildList.length; i++) {
                  let groupChildItem = tabSessionInfo.groupChildList[i];
                  if (sessionListInfo.value.sessionTab == groupChildItem.key) {
                    sessionListInfo.value.sessionList = groupChildItem.list;
                    break;
                  }
                }
              } else if (/colleagueClassify-/.test(sessionListInfo.value.sessionTab)) {
                // 同事分组列表
                for (let i = 0; i < tabSessionInfo.colleagueChildList.length; i++) {
                  let colleagueChildItem = tabSessionInfo.colleagueChildList[i];
                  if (sessionListInfo.value.sessionTab == colleagueChildItem.key) {
                    sessionListInfo.value.sessionList = colleagueChildItem.list;
                    break;
                  }
                }
              } else if (/customerClassify-/.test(sessionListInfo.value.sessionTab)) {
                // 客户分组列表
                for (let i = 0; i < tabSessionInfo.customerChildList.length; i++) {
                  let customerChildItem = tabSessionInfo.customerChildList[i];
                  if (sessionListInfo.value.sessionTab == customerChildItem.key) {
                    sessionListInfo.value.sessionList = customerChildItem.list;
                    break;
                  }
                }
                store.dispatch("setFcwOnlineEvent");
              } else if (/groupType-/.test(sessionListInfo.value.sessionTab)) {
                sessionListInfo.value.sessionList = groupTypeList.find(item => {return "groupType-" + item.uuid == sessionListInfo.value.sessionTab}).list;
              } else {
                sessionListInfo.value.sessionList = defaultList;
              }
              break;
          }
          // 设置当前会话列表
          store.commit("setCurrSessionList", sessionListInfo.value.sessionList);
          // 默认打开第一个会话
          if (!id) {
            setDefaultSession();
          }
          // 是否滚动到顶部
          if (scrollTop) {
            nextTick(() => {
              chatUlRef.value.scrollTop = 0;
            });
          }
          if (!update) {
            nextTick(() => {
              console.timeEnd("切换会话", {sessions: sessionListInfo.value.sessionList.length});
            });
          }
          // 折叠的会话数（去除未读和当前会话（非未读））
          debounce({
            timerName: "sessionList",
            time: 300,
            fnName: function () {
              isShowToggleTop();
            }
          });
          if (click && key == "customer") {
            setJJSEvent("P95486464", JSON.stringify({
              workerId: userInfo.workerId,
              userId: userInfo.workerNo,
              number: customerList.length
            }));
          }
          break;
        case "5":
          sessionListInfo.value.sessionList = store.getters.getSessions({sort: 5})["5"];
          break;
        case "7":
          sessionListInfo.value.sessionList = store.getters.getSessions({sort: 7})["7"];
          break;
        case "8":
        case "9":
          if (props.isSession) {
            // 会话列表
            sessionListInfo.value.sessionList = store.getters.getSessions({sort: 8})["8"];
          } else {
            // 订阅号服务号获取
            // 订阅号列表
            let subList = [];
            // 服务号列表
            let serList = [];
            let allList = store.getters.getSessions({sort: 11})["11"];
            allList.map(item => {
              if (new RegExp(config.subscribe).test(item.id)) {
                subList.push(item);
              } else if (new RegExp(config.serveNumber).test(item.id)) {
                serList.push(item);
              }
            });
            if (props.sessionType == 8) {
              sessionListInfo.value.sessionList = subList;
            } else {
              sessionListInfo.value.sessionList = serList;
            }
          }
          break;
      }
      nextTick(() => {
        calcViewContent();
      });
    }

    // 设置默认打开的会话
    function setDefaultSession() {
      if (sessionListInfo.value.sessionList.length > 0) {
        // 全部列表才打开第一个会话
        if (sessionListInfo.value.sessionTab == "all" && !store.getters.getCurrentSession.id) {
          store.dispatch("setCurrentSession", {id: sessionListInfo.value.sessionList[0].id});
        }
        nextTick(() => {
          let id = store.getters.getCurrentSession.id;
          sessionListInfo.value.sessionInfo = store.getters.getSessions({id: id});
          if (id && id == store.getters.getUpdateSessionId && store.getters.getImEditor) {
            store.dispatch("activeImEditor", {id: id});
          }
        })
      }
    }

    // 打开会话
    function openChat(item, type, click) {
      if (new RegExp(config.fcw).test(item.id)) {
        if (click && store.getters.getCurrentSession.id == `p2p-${config.customerAccount}`) {
          setJJSEvent("P57908992", JSON.stringify({
            workerId: userInfo.workerId,
            userId: userInfo.workerNo,
            number: sessionListInfo.value.sessionList.length
          }));
        } else {
          setJJSEvent("P85756672", JSON.stringify({
            workerId: userInfo.workerId,
            userId: userInfo.workerNo,
            number: sessionListInfo.value.sessionList.length
          }));
        }
      }
      // 搜索打开会话用户双击习惯延迟判定
      if (!store.getters.getIsClick) {
        debounce({
          timerName: "setIsClick",
          time: 300,
          fnName: function () {
            store.commit("setIsClick", true);
          }
        });
        return;
      }
      if (props.openChatMethods) {
        props.openChatMethods(item, type);
        return;
      }
      console.time("打开会话");
      if (type == "doc") {
        // 跳转乐文档
        item.localValue = item.id;
        let urlJson = store.getters.getLinkUrlJson({
          type: "doc",
          doc: {
            padId: item.padId,
            docId: item.id,
          }
        });
        store.dispatch("setOpenWindow", [urlJson.url, urlJson.frameName]);
      } else {
        store.dispatch("setCurrentSession", {id: item.id});
        window.getSelection().removeAllRanges();
      }
      // 双击打开子窗口
      if (item.id == openChaObj.id && Date.now() - openChaObj.time <= 300) {
        console.log("openChildChat", Date.now(), openChaObj);
        openWin(item);
      }
      openChaObj.id = item.id;
      openChaObj.time = Date.now();
      nextTick(() => {
        console.timeEnd("打开会话", {sessions: sessionListInfo.value.sessionList.length});
      });
    }

    // 打开子窗口
    function openWin(item) {
      let sessionType = getSessionType(item).type;
      if (props.sessionType == -1 && (sessionType == 1 || sessionType == 2 || sessionType == 3 || sessionType == 4)) {
        emitMsg("msg", {type: "window", newWin: 1, id: item.id, name: "chat-" + item.id, width: 680, height: 680, path: "child/childChat?id=" + item.id});
        store.commit("setLocalUnreadMap", {type: "del", id: item.id});
      }
    }

    // 会话右键
    async function setMenu(e, item) {
      let menu = {};
      let menuItem = {};
      let menuList = [];
      let notCustomAccount = !new RegExp(`^p2p-(${config.subscribe}|${config.serveNumber}|${config.systemMessage}|${config.helperAccount}|${config.customerAccount})$`).test(item.id);
      let isFcwFlag = isFcwList(item);
      let classifyType = item.scene == "p2p" ? (isFcwFlag ? 4 : 3) : 2;
      if (props.sessionType == -1) {
        // 主会话列表
        // 是否黑名单
        let blackFlag = !!store.getters.getBlacklist({id: item.to});
        // 自定义会话无法置顶、已读未读
        if (notCustomAccount) {
          // 置顶状态-黑名单不允许置顶
          if (!blackFlag) {
            menuList.push({
              label: `${item.isTop ? "取消" : ""}置顶`, click: function () {
                let param = {type: 2, key: "session_top_setting", remark: "置顶", operation: "add", value: item.id, modify: true};
                if (item.isTop) {
                  param.operation = "delete";
                }
                store.dispatch("setModifySettings", param);
              }
            });
          }

          // 标记已读未读
          menuList.push({
            label: `标为${item.unread ? "已读（取消处理）" : "未读（稍后处理）"}`, click: function () {
              if (item.unread) {
                nim.resetSessionUnread(item.id);
                store.commit("setLocalUnreadMap", {type: "del", id: item.id});
              } else {
                store.commit("setLocalUnreadMap", {id: item.id});
              }
            }
          });

          // 是否重要标记
          let importantMarkFlag = store.getters.getConcernOrImportantMarkMap({id: item.id, keyType: 2});
          // 重要标记
          menuList.push({
            label: `${importantMarkFlag ? "取消标记（重要标记）" : "标记（重要标记）"}`, click: function () {
              setConcernOrImportantMark({item: item, flag: importantMarkFlag, specialType: 2});
            }
          });
        }

        let isGroup = item.detailInfo.detailType == "group";
        if (item.scene == "p2p") {
          // 1v1
          if (!isNaN(Number(item.to))) {
            // 非自己
            if (item.to != userInfo.workerNo) {
              // 是否特别关心
              let concernFlag = store.getters.getConcernOrImportantMarkMap({id: item.id, keyType: 1});
              // 特别关心
              menuList.push({
                label: `${concernFlag ? "取消" : "设为"}特别关心`, click: function () {
                  setConcernOrImportantMark({item: item, flag: concernFlag, specialType: 1});
                }
              });
              // 黑名单-离职人员不能加入黑名单
              if (!(!blackFlag && item.detailInfo.empStatus == 4)) {
                menuList.push({
                  label: `${blackFlag ? "解除" : "加入"}黑名单`, click: async function () {
                    if (!blackFlag) {
                      alert({
                        content: `您确定将${item.detailInfo.name}加入黑名单吗？确认后，将不再收到对方发送的消息<br/>若有需要可以右键【解除黑名单】哦~`,
                        done: async type => {
                          if (type == 1) {
                            store.dispatch("setNimBlacklist", {account: item.to, flag: !blackFlag});
                          }
                        }
                      });
                    } else {
                      store.dispatch("setNimBlacklist", {account: item.to, flag: !blackFlag});
                    }
                  }
                });
              }
            }
            // 私聊会话加入分组列表选项
            await menuAddClassifyItem(item, menuList, isFcwFlag, classifyType, menuItem);
          }
        } else {
          // 群
          // 讨论组
          // 进线开关为关时，客户讨论组允许设置
          let teamParam = {modify: true, type: 1, value: item.id, detailType: item.detailInfo.detailType};
          if (!isFcwFlag || (isFcwFlag && store.getters.getSettings[config.settings.type14] != 1)) {
            menuList.push({
              label: "接收且提醒", type: "checkbox", checked: !item.isHelper && !item.isNoTip, click: function () {
                let param = {...teamParam, key: item.isNoTip ? config.settings.type1 : config.settings.type2, remark: "接收且提醒", operation: "delete"};
                store.dispatch("setModifySettings", param);
              }
            });
            menuList.push({
              label: "接收但不提醒", type: "checkbox", checked: !item.isHelper && item.isNoTip, click: function () {
                let param = {...teamParam, key: config.settings.type1, remark: "接收但不提醒", operation: "add"};
                if (item.isHelper) {
                  param.operation = "update";
                  param.oldKey = config.settings.type2;
                }
                store.dispatch("setModifySettings", param);
              }
            });
            if (!isFcwFlag) {
              menuList.push({
                label: "收进群助手且不提醒", type: "checkbox", checked: item.isHelper, click: function () {
                  let param = {...teamParam, key: config.settings.type2, remark: "收进群助手且不提醒", operation: "add"};
                  if (item.isNoTip) {
                    param.operation = "update";
                    param.oldKey = config.settings.type1;
                  }
                  store.dispatch("setModifySettings", param);
                }
              });
            }

            // 讨论组加入分组列表选项
            await menuAddClassifyItem(item, menuList, isFcwFlag, classifyType, menuItem);
          }
          menuList.push({
            label: "@全员消息不提醒", type: "checkbox", checked: item.isHaitAllNoTip, click: function () {
              let param = {...teamParam, key: config.settings.type15, remark: "@全员消息不提醒", operation: item.isHaitAllNoTip ? "delete" : "add"};
              store.dispatch("setModifySettings", param);
            }
          });
          if (!isDisabledGroup(item)) {
            // 进线客户讨论组不支持退出/解散
            if (item.detailInfo.owner == userInfo.workerNo) {
              // 创建者为自己，且为讨论组可以解散
              if (isGroup) {
                menuList.push({
                  label: "解散讨论组", click: function () {
                    store.dispatch("dismissTeam", {id: item.to});
                  }
                });
              }
            } else {
              menuList.push({
                label: `退出该${isGroup ? "讨论组" : "群"}`, click: function () {
                  store.dispatch("leaveTeam", {id: item.to});
                }
              });
            }
          }
        }

        if (item.scene == "p2p" && isFcwFlag) {
          // 客户会话加入分组列表选项
          await menuAddClassifyItem(item, menuList, isFcwFlag, classifyType, menuItem);
        }

        if (item.id != `p2p-${config.customerAccount}`) {
          // 关闭会话
          menuList.push({
            label: "关闭会话", click: function () {
              closeChat(item.id);
            }
          });
        }

        if (/Classify-/.test(sessionListInfo.value.sessionTab)) {
          // 从该分组中移除
          menuList.push({
            label: `从该${sessionListInfo.value.classifyType == 2 ? "分组" : "标签"}中移除`, click: function () {
              groupAddClassify({...sessionListInfo.value.sessionTabClassifyItem, checked: true, value: item.to}, {}, classifyType);
            }
          });
        }
      } else if (props.sessionType == 5) {
        // 客户会话
        // 标记已读未读
        menuList.push({
          label: `标记${item.unread ? "已" : "未"}读`, click: function () {
            if (item.unread) {
              nim.resetSessionUnread(item.id);
              store.commit("setLocalUnreadMap", {type: "del", id: item.id});
            } else {
              store.commit("setLocalUnreadMap", {id: item.id});
            }
          }
        });
        if (item.scene == "p2p") {
          // 备注
          menuList.push({
            label: `备注`, click: function () {
              store.commit("setEmit", {type: "customerDialog", value: {type: 1, account: item.to, name: item.detailInfo.name, time: Date.now()}});
            }
          });
        }

        // 举报
        menuList.push({
          label: `举报`, click: function () {
            let account = item.to;
            let teamId = "";
            // 举报客户群被举报的人为groupTypeId
            if (item.detailInfo && item.detailInfo.groupType == 2 && item.detailInfo.serverCustom && item.detailInfo.serverCustom.groupTypeId) {
              account = item.detailInfo.serverCustom.groupTypeId;
              teamId = item.to;
            }
            store.commit("setEmit", {type: "customerDialog", value: {type: 2, account: account, teamId: teamId, time: Date.now()}});
          }
        });
      } else if (props.sessionType == 7) {
        // 群助手
        // 移出群助手
        let teamParam = {modify: true, type: 1, value: item.id, detailType: item.detailInfo.detailType}
        menuList.push({
          label: "移出群助手", click: function () {
            let param = {...teamParam, key: config.settings.type2, remark: "接收且提醒", operation: "delete"};
            store.dispatch("setModifySettings", param);
          }
        });
      }
      if (menuList.length > 0) {
        menu = showMenu(menuList);
        menuItem.item = menu.popup(e.x, e.y);
      }
      e.stopPropagation();
      e.preventDefault();
      return false;
    }

    // 右键菜单加入分组功能
    function menuAddClassifyItem(item, menuList, isFcwFlag, classifyType, menuItem) {
      let classifySubmenu = [];
      // 获取对应分组的分组列表
      let classifyList = store.getters.getClassifyList({type: classifyType});
      for (let i = 0; i < classifyList.length; i++) {
        let classifyItem = classifyList[i];
        // 移动/移除分组
        let classifyMenuItem = {label: classifyItem.name, value: item.to, uuid: classifyItem.uuid, classifyValue: classifyItem.value, notHide: true, checked: false};
        if (classifyItem.value) {
          let classifyList = classifyItem.value.split(",");
          let classifyIndex = classifyList.findIndex(to => {return to == item.to});
          if (classifyIndex > -1) {
            // 加入对应分组
            classifyMenuItem.checked = true;
          }
        }
        classifyMenuItem.click = (clickItem, clickSubmenuObj) => {
          groupAddClassify(clickItem, clickSubmenuObj, classifyType);
        }
        classifySubmenu.push(classifyMenuItem);
      }
      classifySubmenu.push({
        label: `+创建${classifyType == 2 ? "分组" : "标签"}`, notHide: true, checked: false, click: (clickItem, clickSubmenuObj) => {
          store.commit("setEmit", {
            type: "initClassify", value: {
              type: 1,
              classifyType: classifyType,
              time: Date.now(),
              done: res => {
                if (res.data?.data?.uuid) {
                  // 新增分组加入菜单
                  clickSubmenuObj.list.unshift({
                    label: res.data.data.name, value: item.to, uuid: res.data.data.uuid, notHide: true, checked: false,
                    click: (clickItem, clickSubmenuObj) => {
                      groupAddClassify(clickItem, clickSubmenuObj, classifyType);
                    }
                  });
                  nextTick(() => {
                    menuItem.item.calcWinWH(2);
                  });
                }
              }
            }
          });
        }
      });
      menuList.push({
        label: classifyType == 2 ? "讨论组/群分组" : classifyType == 3 ? "同事标签" : "客户标签",
        submenu: showMenu(classifySubmenu)
      });
    }

    // 讨论组加入分组
    function groupAddClassify(clickItem, clickSubmenuObj, classifyType) {
      store.commit("setEmit", {
        type: "initClassify", value: {
          type: clickItem.checked ? 6 : 5,
          classifyType: classifyType,
          classifyName: clickItem.label || clickItem.name,
          classifyUUID: clickItem.uuid,
          classifyValue: [{uuid: clickItem.uuid, value: clickItem.value}],
          itemMap: store.getters.getClassifyList({uuid: clickItem.uuid, valueMap: true}),
          time: Date.now(),
          done: res => {
            if (res.success) {
              clickItem.checked = !clickItem.checked;
            }
          }
        }
      });
    }

    // 判断是否当前会话
    function isCurrent(id) {
      let isCurrent = sessionListInfo.value.sessionInfo.id == id;
      if (props.type != 5) {
        // 订阅号和群助手选中
        let reg = new RegExp(config.subscribe);
        if ((sessionListInfo.value.sessionInfo.isHelper && id == "p2p-" + config.helperAccount) || (reg.test(sessionListInfo.value.sessionInfo.id) && id == "p2p-" + config.subscribe)) {
          isCurrent = true;
        }
      }
      return isCurrent;
    }

    // 关闭会话
    function closeChat(id) {
      store.dispatch("isRemoveSession", id);
      // 关闭会话后获取本地会话列表
      store.getters.getNim.getLocalSessions();
    }

    // 设置特别关心/重要标记 specialType:1关心2标记
    async function setConcernOrImportantMark(param) {
      let {flag, item, specialType} = param;
      let type = item.scene == "p2p" ? 1 : item.detailInfo?.detailType == "team" ? 2 : 3;
      let res = "";
      if (flag) {
        // 取消特别关心
        res = await deleteSpecialConcernApi({
          msgBody: JSON.stringify({
            accid: userInfo.workerNo,
            faccid: item.to,
            type: type,
            specialType: specialType,
          })
        });
      } else {
        // 添加特别关心
        res = await addSpecialConcernApi({
          msgBody: JSON.stringify({
            accid: userInfo.workerNo,
            faccid: item.to,
            type: type,
            specialType: specialType,
          }),
        });
      }
      if (res && res.success) {
        toast({title: "操作成功", type: 1});
        store.commit("setConcernOrImportantMark", {type: flag ? "del" : "", id: item.id, keyType: specialType});
        // 删除特别关心消息弹窗
        if (specialType == 1 && flag) {
          store.commit("setConcernMsgMap", {type: "deleteItem", account: item.to});
        }
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 滚动到特别关心/未读会话 type-1特别关心-2未读会话-3当前会话
    function scrollToChat(type) {
      if (chatUlRef.value) {
        let scrollTop = 0;
        let firstItemScrollTop = -1;
        let scrollTopI = 0;
        let firstItemScrollTopI = 0;
        let isShow = false;
        let currId = store.getters.getCurrentSession.id;
        let flag = topObj.value.flag;
        // 查询可视区域内是否有未读数
        if ((chatUlRef.value.scrollTop + chatUlRef.value.clientHeight < chatUlRef.value.querySelector(".chat-ul-box").clientHeight) || type == 3) {
          for (let i = 0; i < sessionListInfo.value.sessionList.length; i++) {
            let item = sessionListInfo.value.sessionList[i];
            if (type == 3) {
              if (item.id == currId) {
                scrollTopI = i;
                scrollTop = getSessionTop(i);
                break;
              }
            } else {
              let currElm = chatUlRef.value.querySelector(`.chat-li[data-id='${item.id}']`);
              if (currElm) {
                isShow = true;
              }
              // 查询可视区域的下一个会话
              if ((type == 1 && item.isConcern) || (type == 2 && item.unread && !(item.isHelper || item.isNoTip || item.to == config.subscribe))) {
                if (currElm) {
                  let currElmPos = currElm.offsetTop + parseInt(chatUlRef.value.querySelector(".chat-ul-scroll").style.top);
                  if (firstItemScrollTop == -1) {
                    firstItemScrollTopI = i;
                    firstItemScrollTop = getSessionTop(i);
                  }
                  if (currElmPos > chatUlRef.value.scrollTop) {
                    scrollTopI = i;
                    scrollTop = getSessionTop(i);
                    break;
                  }
                } else if (isShow) {
                  scrollTopI = i;
                  scrollTop = getSessionTop(i);
                  break;
                } else if (firstItemScrollTop == -1) {
                  firstItemScrollTopI = i;
                  firstItemScrollTop = getSessionTop(i);
                }
              }
            }
          }
        }
        chatUlRef.value.scrollTop = scrollTop || firstItemScrollTop;
        isShowToggleTop();
        nextTick(() => {
          isShowToggleTop();
          nextTick(() => {
            chatUlRef.value.scrollTop = getSessionTop(scrollTopI || firstItemScrollTopI, chatUlRef.value.querySelector(".top-toggle-btn"));
            // 判断折叠置顶元素40px
            if ((flag && topObj.value.flag && !topObj.value.isBottom)) {
              let nextTopElm = chatUlRef.value.querySelector(".top-toggle-btn");
              if (nextTopElm) {
                let offset = getOffset(nextTopElm);
                let parentOffset = getOffset(chatUlRef.value)
                if (offset.top && offset.top - parentOffset.top <= chatUlRef.value.scrollTop) {
                  chatUlRef.value.scrollTop += 40;
                }
              }
            }
          });
        });
      }
    }

    // 获取会话定位高度 flag定位后在判断是否去除折叠的高度
    function getSessionTop(i, flag) {
      let top = i * topObj.value.height;
      if (flag && topObj.value.showTop && i >= topObj.value.index) {
        top -= topObj.value.num * topObj.value.height;
      }
      return top;
    }

    // 是否群助手
    function isHelper(item) {
      return item.to == config.helperAccount;
    }

    // 是否有输入框内容
    function hasEditorContent(id) {
      let editorContent = store.getters.getEditorContentMap[id];
      let contentFlag = hasContent(editorContent);
      // 存在内容去除上下切换快捷键
      if (contentFlag && isCurrent(id)) {
        store.commit("setEmit", {type: "toggleSession", value: false});
      }
      return contentFlag;
    }

    // 切换服务号/公众号列表显示隐藏
    function toggleShowSub(type) {
      if (type == 1) {
        subAndSerObj.value.serShow = !subAndSerObj.value.serShow;
      } else if (type == 2) {
        subAndSerObj.value.subShow = !subAndSerObj.value.subShow;
      }
    }

    // 是否订阅号盒子
    function isSubBox(item) {
      return item.to == config.subscribe;
    }

    // 拖拽窗口
    function dragList(e, type, item) {
      if (type == 1) {
        // 开始拖拽
        dragObj.startX = e.x;
        dragObj.startY = e.y;
      } else if (type == 2) {
        // 结束拖拽范围大于10才打开子窗口
        if (Math.abs(e.x - dragObj.startX) > 20 || Math.abs(e.y - dragObj.startY) > 20) {
          openWin(item);
        }
      }
    }

    // 获取是否标记未读
    function getLocalUnreadMap(id) {
      return store.getters.getLocalUnreadMap[id];
    }

    // 获取是否重要标记
    function getImportantMarkMap(id) {
      return store.getters.getState("importantMarkMap")[id];
    }

    // 计算可视区域
    function calcViewContent() {
      if (chatUlRef.value) {
        // 滚动高度
        let scrollTop = chatUlRef.value.scrollTop;
        // 可视区域高度
        let chatUlHeight = chatUlRef.value.clientHeight;
        // 主列表且全部tab和折叠状态才计算折叠
        let isCalcTop = topObj.value.showTop && sessionListInfo.value.sessionTab == "all" && props.sessionType == -1;
        // 可折叠置顶的会话数
        let foldTopLength = isCalcTop ? topObj.value.num : 0;
        // 总置顶数
        let topLength = isCalcTop ? topObj.value.topNum : 0;
        // 总会话数
        let totalLength = sessionListInfo.value.sessionList.length;
        // 屏幕可显示的会话数
        let showLength = Math.floor(chatUlHeight / topObj.value.height);
        // 开始渲染的列表下标
        let start = Math.floor(scrollTop / topObj.value.height) - 10;
        topObj.value.start = Math.max(start > topLength ? start : 0, 0);
        let end = Math.min(start + showLength + topLength + 20, totalLength);
        topObj.value.end = end < 10 ? 10 : end;
        // 是否折叠置顶状态滚动超过折叠的高度
        let isScrollOverTop = topObj.value.start > topLength && isCalcTop;
        topObj.value.top = (isScrollOverTop ? topObj.value.start - foldTopLength : topObj.value.start) * topObj.value.height + (isScrollOverTop ? 40 : 0);
        topObj.value.listHeight = (totalLength - topLength) * topObj.value.height;
        // 判断最后一个置顶会话是否在可视区域
        let currentLastSessionTopId = store.getters.getLastSessionTopId;
        if (currentLastSessionTopId) {
          let currentSessionList = sessionListInfo.value.sessionList.slice(topObj.value.start, topObj.value.end);
          if (currentSessionList.findIndex(item => {return item.id == currentLastSessionTopId}) == -1) {
            // 不在可视区域内获取最后一个置顶会话设置
            currentSessionList = currentSessionList.reverse();
            for (let i = 0; i < currentSessionList.length; i++) {
              if (currentSessionList[i].isTop) {
                lastSessionTopId.value = currentSessionList[i].id;
              }
              break;
            }
          } else {
            lastSessionTopId.value = currentLastSessionTopId;
          }
        }
      }
    }

    // 判断是否显示折叠置顶消息
    function isShowToggleTop() {
      if (!chatUlRef.value) {
        nextTick(() => {
          if (chatUlRef.value) {
            isShowToggleTop();
          }
        })
        return;
      }
      // 折叠后不计算是否显示
      if (!topObj.value.showTop) {
        // 查找切换显示置顶元素
        let topElm = chatUlRef.value.querySelector(".top-toggle-btn");
        if (topElm) {
          let offset = getOffset(topElm.parentElement)
          if (offset.top + topElm.parentElement.clientHeight > document.body.clientHeight) {
            topObj.value.flag = true;
            // 不在可视区域内显示在底部
            topObj.value.isBottom = offset.top + topElm.parentElement.clientHeight - document.body.clientHeight - chatUlRef.value.scrollTop > 0;
            if (topObj.value.isBottom) {
              topObj.value.bottomWidth = chatUlRef.value.clientWidth;
            }
          } else {
            topObj.value.flag = false;
            topObj.value.isBottom = false;
          }
        } else {
          topObj.value.flag = false;
          topObj.value.isBottom = false;
        }
        calcViewContent();
      } else {
        topObj.value.isBottom = false;
        calcViewContent();
      }
    }

    // 切换显示折叠置顶状态
    function toggleShowTop() {
      topObj.value.showTop = !topObj.value.showTop;
      store.commit("setIsFoldTop", topObj.value.showTop);
      scrollChat();
    }

    // 滚动监听
    function scrollChat() {
      throttle({
        timerName: "scrollChat",
        time: 100,
        fnName: function () {
          isShowToggleTop();
        }
      });
      calcViewContent();
    }

    // 分组添加讨论组
    function addClassifyGroup() {
      let uuid = store.getters.getSessionTabClassifyItem.uuid;
      store.commit("setEmit", {
        type: "inviteBox", value: {
          type: 3,
          classifyType: sessionListInfo.value.classifyType,
          uuid: uuid,
          itemMap: store.getters.getClassifyList({uuid: uuid, valueMap: true}),
          time: Date.now(),
          notSendCustomSysMsg: true,
          done: (res, groupList) => {
            // 发送通知给自己和别人(用于多端同步)
            let customParam = {};
            if (sessionListInfo.value.classifyType == 2) {
              customParam.groupList = groupList;
            } else {
              customParam.personList = groupList;
            }
            store.dispatch("sendCustomSysMsgByGroups", customParam);
          }
        }
      });
    }

    // 显示悬浮框内容
    function showTipsBox(e, text, pos) {
      store.commit("setEmit", {
        type: "tipsBox", value: {type: 1, e: e, content: text, pos: pos}
      });
    }

    // 获取fcw会话
    async function getFcwSession() {
      if (fcwSessionObj.timer) {
        toast({title: "请勿频繁操作", type: 2});
        return;
      }
      fcwSessionObj.timer = setTimeout(() => {
        fcwSessionObj.timer = "";
      }, 1000);
      if (fcwSessionObj.flag) {
        return;
      }
      fcwSessionObj.flag = true;
      let res = await updateActiveAccidsApi({
        msgBody: JSON.stringify({accid: userInfo.workerNo})
      });
      fcwSessionObj.flag = false;
      if (res?.success) {
        if (res?.data?.accids?.length > 0) {
          toast({title: "会话获取成功", type: 1});
          // 不存在则插入会话
          res.data.accids.map(item => {
            let sessionId = "p2p-" + item;
            if (!store.getters.getSessions({id: sessionId}).id) {
              remote.store.dispatch("insertLocalSession", {scene: "p2p", to: item});
            }
          });
        } else {
          toast({title: "无客户相关消息可获取"});
        }
      } else {
        toast({title: "会话获取失败", type: 2});
      }
    }

    return {
      transTime,
      avatarError,
      openChat,
      openWin,
      isCurrent,
      closeChat,
      setMenu,
      scrollToChat,
      isHelper,
      hideElm,
      hasEditorContent,
      toggleShowSub,
      isSubBox,
      dragList,
      getLocalUnreadMap,
      getImportantMarkMap,
      toggleShowTop,
      scrollChat,
      userWearPic,
      getCurrentSession,
      addClassifyGroup,

      chatUlRef,
      chatSubRef,
      subAndSerObj,
      topObj,
      config,
      lastSessionTopId,
      sessionListInfo,
      showTipsBox,
      getFcwSession,
    }
  }
}
</script>
<style scoped lang="scss">
.chat-list {
  width: 100%;
  height: 100%;

  &.chat-list-1 {
    .chat-li-box {
      height: 40px !important;

      .user-avatar-box {
        width: 24px !important;
        height: 24px !important;
      }

      .badge {
        top: 2px !important;
        left: 30px !important;
      }

      .content-box {
        .content {
          display: none;

          .name-box {
            font-size: 15px;
          }
        }
      }
    }
  }

  .chat-list-box {
    width: 100%;
    height: 100%;
    position: relative;
    background: #FFFFFF;

    .session-concern {
      height: 30px;
      width: 100%;
      z-index: 10;
      line-height: 30px;
      padding: 0 12px;
      background: #E5E3E2;
      cursor: pointer;
      position: relative;

      &:hover {
        background: #DDDDDD;
      }

      .icon-concern {
        width: 16px;
        height: 16px;
        background-image: url("/img/index/icon_concern.png");
        background-size: 16px 44px;
        background-position: 0px -28px;
        margin-right: 6px;
      }

      .concern-box {
        display: flex;
        align-items: center;

        .concern-num {
          color: #FE7801;
        }
      }

      .concern-arrow {
        width: 14px;
        height: 14px;
        background-position: 0px 0px;
        position: absolute;
        top: 50%;
        right: 12px;
        transform: translateY(-50%);
        margin-right: 0;
      }
    }

    .fcw-tips-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 30px;
      padding: 0 10px;
      background: #FFEDED;
      font-size: 11px;
      color: #333333;

      .fcw-tips-content {
        display: flex;
        align-items: center;
        flex: 1;

        .textEls {
          min-width: 0;
        }

        .icon-question {
          flex-shrink: 0;
          width: 14px;
          height: 14px;
          background-image: url("/img/workbench/icon_helper_14.png");
          background-repeat: no-repeat;
          background-size: 28px 14px;
          cursor: pointer;

          &:hover {
            background-position: -14px 0;
          }
        }
      }

      .fcw-tips-btn {
        flex-shrink: 0;
        line-height: 20px;
        padding: 0 5px;
        border-radius: 2px;
        border: 1px solid $styleColor;
        color: $styleColor;
        cursor: pointer;
        margin-left: 6px;
      }
    }

    .chat-ul {
      width: 100%;
      height: 100%;
      overflow-x: hidden;
      overflow-y: overlay;

      &.concern-show {
        height: calc(100% - 30px);
      }

      &.fold-top {
        .chat-li {
          .chat-li-box {
            &.top {
              display: none;

              &.always-top,
              &.unread,
              &.curr {
                display: flex;
              }
            }
          }
        }
      }

      .chat-ul-box {
        width: 100%;
        position: relative;

        .chat-ul-scroll {
          width: 100%;
          position: absolute;
        }
      }

      .chat-li {
        width: 100%;
        position: relative;

        .chat-li-box {
          width: 100%;
          height: 64px;
          display: flex;
          align-items: center;
          padding: 14px 12px;
          position: relative;
          background: #FFFFFF;

          &.show-top-btn {
            margin-bottom: 40px;
          }

          &:hover {
            background: #DCDCDC !important;

            .close {
              display: block !important;
            }
          }

          &.top {
            background: $styleBg1Hover;
            position: relative;

            &:after {
              position: absolute;
              left: -1px;
              top: -4px;
              content: "";
              width: 0;
              height: 0;
              border-top: 6px solid transparent;
              border-bottom: 6px solid transparent;
              border-right: 6px solid #FBCBCC;
              transform: rotate(45deg);
            }
          }

          &.curr {
            background: #DCDCDC !important;
          }

          .user-avatar-box {
            flex-shrink: 0;
          }

          .content-box {
            flex-shrink: 0;
            width: calc(100% - 46px);
            margin-left: 10px;

            .name-box {
              display: flex;
              justify-content: space-between;
              align-items: center;

              .name {
                font-size: 15px;
                flex: 1;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;

                &.resign {
                  color: #F74B32;
                }
              }

              .time {
                font-size: 11px;
                color: #999999;
                margin-left: 5px;
              }
            }

            .content {
              width: 100%;
              height: 16px;
              line-height: 16px;
              margin-top: 4px;
              color: #999;
              position: relative;

              .content-details {
                width: calc(100% - 12px);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                .draft {
                  color: red;
                }

                .msg-err-icon {
                  display: inline-block;
                  vertical-align: text-top;
                  width: 14px;
                  height: 14px;
                  background: url("/img/index/error_tips.png") no-repeat;
                  background-size: 14px 14px;
                  margin-right: 5px;
                }

                .hait-tips {
                  color: red;
                }

                .concern-tips {
                  color: #FE7801;
                }

                .user-online {
                  position: relative;
                  color: #666666;
                  padding-left: 7px;
                  margin-right: 2px;

                  &:after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    background: #02B50D;
                  }
                }

                .user-line {
                  margin-right: 2px;
                  color: #BFBFBF;
                }

                .user-read {
                  margin-right: 4px;

                  &.unread {
                    color: #FF9E00;
                  }
                }
              }

              .close {
                display: none;
                position: absolute;
                top: 50%;
                right: 0px;
                transform: translateY(-50%);
                width: 14px;
                height: 14px;
                background-image: url("/img/index/session_close.png");
                background-repeat: no-repeat;
                background-size: 28px 14px;
                background-position: 0 0;

                &:hover {
                  background-position: -14px 0;
                }
              }
            }

            ::v-deep(.im-emoji) {
              width: 15px;
              height: 15px;
            }
          }

          .badge {
            position: absolute;
            top: 8px;
            left: 38px;

            &.badge-point {
              min-width: 12px;
              min-height: 12px;
              max-width: 12px;
              max-height: 12px;
            }

            &.not-tips {
              background-color: rgb(124, 213, 252);
            }
          }

          .mark-label-box {
            position: absolute;
            top: 2px;
            right: 10px;
            display: flex;
            align-items: center;

            .icon-mark {
              width: 9px;
              height: 10px;
              margin-left: 4px;

              &.is-remind {

              }

              &.is-mark {
                &:before {
                  background-position: -9px 0;
                }
              }

              &:before {
                display: block;
                content: "";
                width: 9px;
                height: 10px;
                background: url("/img/index/icon_remind.png") no-repeat;
                background-size: 18px 10px;
              }
            }
          }
        }

        .top-toggle-btn {
          display: flex;
          align-items: center;
          padding-left: 57px;
          width: 100%;
          height: 40px;
          color: #666666;
          font-size: 14px;
          border-top: 1px solid #E0E0E0;
          border-bottom: 1px solid #E0E0E0;
          cursor: pointer;
          background: $styleBg1Hover url("/img/index/icon_top.png") no-repeat 18px center;
          background-size: 20px 20px;
          position: relative;
          z-index: 1;

          &:after {
            content: "";
            width: 20px;
            height: 20px;
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            background-image: url("/img/index/icon_top_arrow.png");
            background-repeat: no-repeat;
            background-size: 80px 20px;
          }

          &.bottom {
            width: 240px;
            position: fixed;
            bottom: 0;
          }

          &.show {
            &:after {
              background-position: -40px 0;
            }
          }
        }
      }
    }

    .chat-none {
      width: 100%;
      height: 100%;
      color: #333333;
      background: #FFFFFF;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;

      &.fcw-tips-none {
        height: calc(100% - 30px);
      }

      .none-img {
        width: 160px;
        margin-bottom: 10px;
      }

      .loading-img {
        width: 32px;
        margin-bottom: 10px;
      }

      .tips-box {
        .tips-add-text {
          display: flex;
          align-items: center;
          margin-top: 4px;

          .add-text {
            margin-left: 4px;
            color: $styleColor;
            cursor: pointer;
          }
        }
      }
    }

    .sub-ser-list {
      height: 100%;
      overflow-y: auto;

      .sub-title {
        font-size: 15px;
        height: 44px;
        line-height: 44px;
        padding-left: 14px;
        display: flex;
        align-items: center;

        .sub-arrow {
          width: 16px;
          height: 16px;
          background-image: url("/img/people_icon.png");
          background-repeat: no-repeat;
          background-size: 16px;
          transform: rotate(0);
          margin-right: 5px;

          &.active {
            transform: rotate(90deg);
          }
        }
      }
    }
  }
}
</style>