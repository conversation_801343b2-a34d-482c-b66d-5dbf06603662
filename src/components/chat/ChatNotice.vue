<template>
  <div class="chat-notice">
    <slot v-if="showTag">
      <div class="new-post" v-if="isManage">
        <div class="new-center">
          <LyButton size="small" type="primary" class="mr4" radius="4px" @click="addNotice()">发布新通知</LyButton>
        </div>
      </div>
      <div
        class="post-list-container selAll"
        :style="{
          height: isManage ? 'calc(100% - 40px)' : '100%',
        }"
      >
        <div v-if="chatList.length === 0" class="file-img-box">
          <div class="file-tab-nothing">
            <img src="/img/content_none.png" width="180" height="110" />
            <p>暂无群公告</p>
          </div>
        </div>
        <div :class="{'post-list': true,active: item.acJson.version === active.acJson.version}" v-for="(item, index) in chatList"
          :key="item.id" :id="'notice-' + item.id" @click="activeItem(item)">
          <div class="post-list-content">
            <div>
              <div class="post-list-title">{{ item.acJson.title }}</div>
              <div class="post-list-details" v-html="strToHtml(item.acJson.announcement)"></div>
            </div>
            <img class="post-list-img" @click="toViewer(item, $event)" v-if="item.acJson.imgPath" :src="item.acJson.imgPath"/>
          </div>
          <div class="post-list-footer">
            <span class="post-list-intr">{{ item.createName }}({{item.createDeptName }}) {{dateFormat(item.createTime, "yyyy-MM-dd HH:mm") }}</span>
            <div class="post-list-btn-box notCopy selNone" v-if="isManage">
              <span class="post-list-btn" @click="addNotice(item)">编辑</span>
              <span class="post-list-btn" @click="removeNotice(item)">删除</span>
            </div>
          </div>
        </div>
      </div>
    </slot>
    <slot v-else>
      <div class="new-post-top">
        <div class="new-post-img">
          <div>
            <img
              class="new-img"
              :src="
                newNoticeItem.imgPath
                  ? newNoticeItem.imgPath
                  : file
                  ? file.path
                  : '/img/new_post_bg.png'
              "
              alt=""
            />
          </div>
          <p>
            <a href="javascript:void(0);" class="imgEdit" @click="updateFile"
              >编辑</a
            >
            <a href="javascript:void(0);" class="imgDel" @click="removeFile"
              >删除</a
            >
            <input
              type="file"
              id="uploadFile"
              @change="fileChange"
              style="display: none"
              ref="fileRef"
              accept="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
            />
          </p>
        </div>
        <div class="new-post-text">
          <input
            type="text"
            v-model="newNoticeItem.title"
            placeholder="标题：必填，15字内"
            maxlength="15"
          />
          <textarea
            placeholder="请在此处输入正文（必填，15至500字）"
            v-model="newNoticeItem.groupAnnouncement"
            maxlength="500"
          ></textarea>
        </div>
      </div>
      <p class="new-post-btn">
        <LyButton size="medium" class="mr10" type="simple" @click="showTag=true" radius="4px">取消</LyButton>
        <LyButton size="medium" type="primary" @click="addNoticeBtn" radius="4px">发布</LyButton>
      </p>
    </slot>
  </div>
</template>
<script>
import { computed, nextTick, ref, watch } from "vue";
import { useStore } from "vuex";
import { chatNoticeListApi, teamAnnouncementApi, get7NTokenApi } from "@utils/net/api";
import { alert, loading, toast } from "@comp/ui";
import LyButton from "@comp/ui/comps/LyButton";
import {
  dateFormat,
  uploadToQiNiu,
  showMenu,
  strToHtml,
  openViewer,
} from "@utils";
import { useRouter } from "vue-router";
export default {
  components: {
    LyButton,
  },
  props: {
    sessionInfo: {
      type: Object,
      default: {},
    },
    setAutoScroll: {
      type: Function,
      default: function () {}
    }
  },
  setup(props) {
    const store = useStore();
    const router = useRouter();
    const userInfo = store.getters.getUserInfo;
    let teamInfo = ref({}); //这个人在群的信息
    async function getTeamInfo() {
      let res = await store.dispatch("getTeamMembers",{
        id: props.sessionInfo.to,
        account: userInfo.workerNo,
      });
      teamInfo.value = res.obj;
    }
    //是否是群组
    let isTeam = computed(() => {
      return (
        props.sessionInfo.scene === "superTeam" ||
        props.sessionInfo.scene === "team"
      );
    });
    if (isTeam.value) {
      getTeamInfo();
    }
    let isManage = computed(() => {
      return (
        isTeam.value &&
        (props.sessionInfo.detailInfo.detailType === "group" ||
          ((props.sessionInfo.detailInfo.detailType === "team" ||
            props.sessionInfo.detailInfo.detailType === "superTeam") &&
            (teamInfo.value.type === "manager" ||
              teamInfo.value.type === "owner")))
      );
    }); //能否发送群通知
    let chatList = ref([]);

    // 高亮的,type:1是父组件调用的
    let active = ref({ acJson: {} });
    let activeType = ref(0);

    function activeItem(item, type = 0) {
      active.value = item;
      activeType.value = type;
    }
    // 获取群通知
    async function getChatNotice() {
      loading();
      let res = await chatNoticeListApi({
        msgBody: JSON.stringify({
          workerId: userInfo.workerId,
          tid: props.sessionInfo.to,
        }),
      });
      loading().hide();
      if (res.success) {
        // 默认无展开按钮以及展开状态为false
        chatList.value = res.data.announcementList.map((i) => {
          let version = active.value.acJson.version;
          if (i.acJson && i.acJson.version == version)
            return { ...i, hasBtn: false, open: true, focus: true };
          return { ...i, hasBtn: false, open: false };
        });
        store.commit("setNoticeUnread", {
          teamId: props.sessionInfo.to,
          type: 2,
        });
        // 设置主窗口缓存信息
        if (router.currentRoute.value.path == "/child/childChat") {
          remote.store.commit("setNoticeUnread", {teamId: props.sessionInfo.to, type: 2});
        }
      } else {
        toast({ title: res.errorMsg || "系统错误", type: 2 });
      }
    }

    getChatNotice();

    // 当前显示类型
    let showTag = ref(true);

    //通知的内容
    let newNoticeItem = ref({
      groupAnnouncement: "", //正文
      title: "", //标题
      imgPath: null, //图片
      type: "",//1新增3更新
    });

    //点击发布新通知
    function addNotice(item) {
      showTag.value = false;
      file.value = null;
      if (item) {
        newNoticeItem.value = JSON.parse(
          JSON.stringify({
            id: item.id,
            ...item.acJson,
            groupAnnouncement: item.acJson.announcement,
            type: 3,
          })
        );
      } else {
        newNoticeItem.value = {
          groupAnnouncement: "",
          title: "",
          imgPath: null,
          type: 1,
        };
      }
    }
    //点击确定发布
    async function addNoticeBtn() {
      if (newNoticeItem.value.title.trim().length === 0) {
        toast({ title: "标题不能为空", type: 2 });
        return;
      }
      if (newNoticeItem.value.groupAnnouncement.trim().length < 15) {
        toast({ title: "内容不能少于15字", type: 2 });
        return;
      }
      alert({
        content: `是否发布该公告？`,
        done: async (type) => {
          if (type === 1) {
            let _file = newNoticeItem.value.imgPath;
            if (qNToken.value) {
              if (file.value) {
                _file = await uploadToQiNiuFunc();
              }
            } else {
              toast({ title: "系统错误,请重试", type: 2 });
              return;
            }
            loading();
            setTeamAnnouncementApi(  {
              ...newNoticeItem.value,
              imgPath: _file,
              workerId: userInfo.workerId,
              workerNo: userInfo.workerNo,
              tid: props.sessionInfo.to,
              owner: props.sessionInfo.detailInfo.owner,
            });
            loading().hide();
          }
        },
      });
    }

    //删除
    function removeNotice(item) {
      alert({
        content: `是否删除该公告？`,
        done: async (type) => {
          if (type == 1) {
            setTeamAnnouncementApi( {
              workerId: userInfo.workerId,
              workerNo: userInfo.workerNo,
              id: item.id,
              type: 2,
            });
          }
        },
      });
    }

    // 设置群公告
    async function setTeamAnnouncementApi(param) {
      let res = await teamAnnouncementApi({
        msgBody: JSON.stringify(param),
      });
      if (res.success) {
        if (param.type == 2){
          // 删除
          getChatNotice();
        } else {
          // 新增/编辑
          showTag.value = true;
          getChatNotice();
          props.setAutoScroll("bottom");
        }
      } else {
        toast({ title: res.errorMsg || "系统错误", type: 2 });
      }
    }

    //--------------------------------文件上传-----------
    let qNToken = ref("");
    let file = ref(null); //上传的文件对象
    let fileRef = ref();// 文件ref对象
    // 获取七牛的token
    async function get7NToken() {
      let res = await get7NTokenApi({
        msgJson: JSON.stringify({ workerId: userInfo.workerId }),
      });
      if (res.success) {
        qNToken.value = res.data.token;
      }
    }
    // 将图片上传至七牛
    async function uploadToQiNiuFunc() {
      let res = await uploadToQiNiu(file.value, qNToken.value);
      res = res || {};
      return res.key ? res.key : null;
    }
    //点击编辑图片
    function updateFile() {
      let fileInput = document.getElementById("uploadFile");
      fileInput.click();
    }
    //文件改变的回调
    async function fileChange(e) {
      let t_file = e.target.files[0];
      if (t_file.type.indexOf("image/") === -1) {
        toast({ title: "请选择图片.", type: 2 });
        return;
      }
      if (t_file.size > 2 * 1024 * 1024) {
        toast({ title: "图片不能大于2M.", type: 2 });
        return;
      }
      file.value = t_file;
      newNoticeItem.value.imgPath = null; //将原来的清掉
    }
    //点击删除图片
    function removeFile() {
      newNoticeItem.value.imgPath = null; //将原来的清掉
      file.value = null;
      fileRef.value.value = "";
    }
    let config = store.getters.getConfig.config;
    // 右击
    function setMenu(e) {
      let menuList = [];
      let selection = window.getSelection();
      if (selection && selection.rangeCount > 0 && selection.type == "Range") {
        let node = document.createElement("div");
        node.appendChild(window.getSelection().getRangeAt(0).cloneContents());
        let text = node.innerText;
        menuList.push({
          label: "复制",
          click: function () {
            document.execCommand("copy");
          },
        });
        if (text.length > 0) {
          menuList.push({
            label: "提醒",
            click: function () {
              remote.store.dispatch("toScheduleModal", text);
            },
          });
        }
      }
      if (menuList.length > 0) {
        showMenu(menuList).popup(e.x, e.y);
      }
    }

    // 打开查看大图/视频
    function toViewer(item, e) {
      let imgList = [
        { src: item.acJson.imgPath, dataSrc: item.acJson.imgPath, w: e.target.naturalWidth, h: e.target.naturalHeight },
      ];
      let thisIndex = 0;
      openViewer(
        imgList,
        thisIndex,
        e.target.naturalWidth,
        e.target.naturalHeight
      );
    }

    get7NToken();
    return {
      setMenu,
      isManage,
      file,
      fileChange,
      removeFile,
      updateFile,
      uploadToQiNiuFunc,
      newNoticeItem,
      addNoticeBtn,
      removeNotice,
      addNotice,
      showTag,
      active,
      activeItem,
      chatList,
      dateFormat,
      strToHtml,
      toViewer,
      fileRef,
    };
  },
};
</script>
<style lang="scss" scoped>
.chat-notice {
  height: 100%;
  overflow: hidden;
  .new-post {
    height: 40px;
    padding: 0 16px;
    border-bottom: 1px solid #E0E0E0;
    background-color: #fff;
    .new-center {
      min-width: 500px;
      height: 40px;
      margin: 0 auto;
      max-width: 750px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  .post-list-container {
    flex: 1;
    padding: 15px 16px;
    width: 100%;
    height: calc(100% - 40px);
    overflow-x: hidden;
    overflow-y: scroll;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding-bottom: 0;
    .post-list {
      min-width: 500px;
      max-width: 750px;
      margin: 0 auto 16px;
      padding: 12px 16px 8px;
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #EDEDED;
      transition: all 0.2s ease-in-out;

      &.active,
      &:hover{
        box-shadow: 0px 3px 8px 0px rgba(165, 165, 165, 0.5);
      }

      .post-list-content{
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;

        .post-list-title{
          font-size: 16px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        .post-list-details{
          line-height: 22px;
          font-size: 14px;
          color: #666666;
          word-break: break-all;
        }

        .post-list-img {
          width: 80px;
          height: 60px;
          cursor: pointer;
          flex-shrink: 0;
          margin-left: 16px;
          border-radius: 4px;
        }
      }

      .post-list-footer{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 8px;
        border-top: 1px solid #E0E0E0;

        .post-list-intr {
          color: #999999;
        }

        .post-list-btn-box{
          display: flex;
          align-items: center;

          .post-list-btn{
            color: #333333;
            padding: 4px 12px;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            margin-left: 6px;
            cursor: pointer;

            &:hover{
              color: $styleColor;
              border: 1px solid $styleColor;
            }
          }
        }
      }
    }
    .file-img-box {
      height: 80%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .file-tab-nothing {
        > p {
          font-size: 14px;
          margin-top: 10px;
          color: #666666;
          text-align: center;
        }
      }
    }
  }
  .new-post-top {
    margin-top: 10px;
    display: flex;
    padding: 0 10px;
    position: relative;
    overflow: hidden;
    .new-post-img {
      width: 100px;
      margin-right: 10px;
      > div {
        width: 98px;
        height: 98px;
        border: 1px solid #e8e8e8;
        overflow: hidden;
        > img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }
      > p {
        line-height: 28px;
        height: 28px;
        text-align: center;
        > a {
          color: #666666;
        }
        > a:first-child {
          margin-right: 13px;
        }
      }
    }
    .new-post-text {
      flex: 1;
      font-size: 12px;
      input,
      textarea {
        border-radius: 4px;
        &:focus {
          border-color: #000000
        }
      }
      input {
        width: 100%;
        height: 28px;
        padding-left: 8px;
        margin-bottom: 10px;
        box-sizing: border-box;
        border: 1px solid #e8e8e8;
        box-shadow: none;
        border-radius: 2px;
      }
      textarea {
        width: 100%;
        height: 212px;
        resize: none;
        padding: 10px 8px;
        box-sizing: border-box;
        border: 1px solid #e8e8e8;
        box-shadow: none;
        border-radius: 2px;
      }
    }
  }
  .new-post-btn {
    padding-right: 10px;
    text-align: right;
    margin-top: 12px;
  }
}
</style>