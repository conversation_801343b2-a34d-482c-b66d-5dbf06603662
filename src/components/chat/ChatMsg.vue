<template>
  <div class="chat-msg selAll">
    <ul :class="['msg-ul',msgType==2?'quote-msg-ul':msgType==6?'collect-msg-ul':'']" ref="msgUlRef">
      <li class="msg-li" v-for="(item,key) in currMsgList" :key="item.time+'-'+(item.uniqueSign||item.idClient||item.idServer)+'-'+(item.updateKey||0)" :data-time="item.time"
          @contextmenu="setMenu($event, item, 1)"
          :class="[
        'msg-li-'+item.type,'msg-li-index-'+key,
        item.file&&item.file.isImage?'msg-li-file-img':'',
        'msg-li-'+(item.antispamMsg?'text':(item.type+(item.content&&item.content.type?'-'+item.content.type:''))),
        (item.content&&item.content.data&&item.content.data.sourceType=='app-lxt-zb'?'msg-li-zb':''),
        (item.content&&item.content.msgs&&item.content.msgs.length==1&&item.content.msgs[0].type=='file'?'msg-li-file':''),
        (item.content&&item.content.type=='card'?'msg-li-custom-card-'+item.content.cardType:''),
        (item.forbidFileMsg?'msg-forbid':''),
        ]"
      >
        <!--时间点-->
        <div class="msg-time-box notCopy">
          <div v-if="item.timeTag&&!isSubOrSer(currSessionInfo.id)" class="msg-time notCopy">{{ transTime(item.time, 1) }}</div>
        </div>
        <!--服务号/公众号消息-->
        <div v-if="isSubOrSer(currSessionInfo.id)" class="msg-sub-ser">
          <!--时间点-->
          <div class="msg-time-box">
            <div v-if="item.type=='custom'&&item.content.type==8&&item.content.data" class="msg-time notCopy">
              {{ transTime(item.time, 1) }}
            </div>
          </div>
          <!--消息内容-->
          <div v-if="item.type=='custom'" @contextmenu.stop="setMenu($event, item, 2)">
            <div v-if="item.content">
              <!--反垃圾-->
              <div v-if="item.antispamMsg" class="msg-tip-box">
                <div class="msg-tip notCopy selNone">[消息涉嫌违规，不支持查看]</div>
              </div>
              <!--普通通知-->
              <div v-else-if="item.content.type==8&&item.content.data" class="sub-ser-type">
                <div class="sub-ser-title textEls" :title="item.content.data.title">{{ item.content.data.title || "" }}</div>
                <div class="sub-ser-content" :title="item.content.data.content" v-html="setSubSerHtml(item.content.data.content)"></div>
                <div class="sub-ser-intr-box">
                  <div class="sub-ser-intr">{{ item.content.data.sourceName || '' }}&nbsp;&nbsp;{{ dateFormat(item.time, "yyyy年MM月dd日") }}</div>
                  <div class="sub-ser-btn-box">
                    <span class="sub-ser-btn msg-open" v-if="item.content.data.url&&(!item.content.data.openType||/pc/i.test(item.content.data.openType))"
                          @click="getLinkUrl(item)">查看详情</span>
                    <span class="sub-ser-btn msg-open" v-if="item.content.data.other&&item.content.data.other.hxId&&item.content.data.other.nativeType==5"
                          @click="openChat('p2p-'+item.content.data.other.hxId,1)">立即沟通</span>
                  </div>
                </div>
              </div>
              <!--消息平台-->
              <template v-else-if="(item.content.type=='msg-center'||item.content.type=='msg-center-link')&&item.content.data">
                <MsgCenter :type="1" :item="item" :setMenu="setMenu" :toViewer="toViewer" :clickImage="clickImage"></MsgCenter>
              </template>
              <!--消息播报-->
              <template v-else-if="item.content.type=='msg-report'&&item.content.data">
                <MsgReport :type="1" :item="item"></MsgReport>
              </template>
              <!--升级提示-->
              <div v-else class="msg-tip-box">
                <div class="msg-tip notCopy selNone">收到一条【自定义】消息，请升级到最新版本或前往手机端查看</div>
              </div>
            </div>
          </div>
        </div>
        <!--提示消息-->
        <div v-else-if="item.type=='tip'" class="msg-tip-box">
          <div class="msg-tip notCopy selNone">
            <span>{{ item.tip == "撤回了一条消息" ? (item.from == userInfo.workerNo ? "您" : (getPerson(item.from).userShowName || getPerson(item.from).name)) + "撤回了一条消息" : item.tip }}</span>
            <span class="re-editor" v-if="item.deleteMsgDetail" @click="reEditor(item.deleteMsgDetail)">重新编辑</span>
          </div>
        </div>
        <!--抖动-->
        <div v-else-if="item.type=='custom'&&item.content&&item.content.type=='shake'" class="msg-tip-box">
          <div class="msg-tip msg-shake notCopy selNone" v-if="item.scene=='p2p'">
            您{{ item.from != userInfo.workerNo ? '收到' : '发送' }}了一个窗口抖动<br>
            {{ item.content.heShakeStatus && item.content.heShakeStatus != '0' ? (item.from != userInfo.workerNo ? '您的抖动提醒未开启，点击' : '对方未开启抖动提醒') : '' }}
            <span v-if="item.content.heShakeStatus&&item.content.heShakeStatus!='0'&&item.from!=userInfo.workerNo" class="msg-open"
                  @click="openWindowShake()">打开</span>
          </div>
          <div class="msg-tip msg-shake notCopy selNone" v-else>{{ getPerson(item.from).name }}发送了一个窗口抖动</div>
        </div>
        <!--通知-->
        <div v-else-if="item.type=='notification'" class="msg-tip-box">
          <div v-if="item.announcement" class="msg-notification-box" @click="toNotice(item)" title="单击查看详情">
            <div class="msg-notification-content">
              <div :class="['msg-notification-text-box',item.attach.team.announcement.imgPath?'has-img':'']">
                <div class="msg-notification-title">{{ item.attach.team.announcement.title }}</div>
                <div class="msg-notification-intr textEls" :title="item.attach.team.announcement.announcement">
                  {{ item.attach.team.announcement.announcement }}
                </div>
              </div>
              <div class="msg-notification-img-box" v-if="item.attach.team.announcement.imgPath">
                <img :src="item.attach.team.announcement.imgPath" alt="" :onerror="fangError">
              </div>
            </div>
            <div class="msg-notification-tips">
              <span>群公告</span>
              <span v-if="item.attach.team.announcement.createTime">{{ item.attach.team.announcement.createEmp }} {{ dateFormat(item.attach.team.announcement.createTime, "yyyy-MM-dd HH:mm") }}</span>
            </div>
          </div>
          <div v-else class="msg-tip notCopy selNone">{{ getNotification(item) }}</div>
        </div>
        <!--卡片通知-->
        <div v-else-if="item.type=='custom'&&item.content&&item.content.type=='card'&&item.content.cardType==3&&item.content.data" class="msg-tip-box">
          <div class="msg-tip notCopy selNone">
            <span>{{ item.content.data.body }}</span>
          </div>
        </div>
        <!--其他消息-->
        <div v-else class="msg-info" @click="changeMultipleSel(item)" :data-idserver="item.idServer" :data-time="item.time"
             :class="[userInfo.workerNo==item.from&&msgType!=6?'msg-me':'msg-you','msg-info-'+item.idServer,
           (item.idServer&&getFocusMsg().idServer==item.idServer&&getFocusMsg().type==msgType?'focus ':''),
           parentIsMultiple&&multipleSelMap[item.idServer]?'msg-multiple-check':'',parentIsMultiple?'msg-multiple':'']">
          <!--多选-->
          <span class="multiple-sel-box" :class="{'disabled':item.antispamMsg||item.forbidMsg||item.status!='success'}" v-if="parentIsMultiple&&msgType==1"></span>
          <!--多选模态层-->
          <div class="multiple-sel-modal" v-if="parentIsMultiple&&msgType==1"></div>
          <div class="user-avatar-box border notCopy selNone">
            <div class="avatar-box notCopy selNone" @click="showUserInfo(item.from, $event)">
              <img class="avatar notCopy selNone" :src="getPerson(item.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.from, '')"
                   @contextmenu.stop="setMenu($event, item, 4)">
            </div>
            <img v-if="userWearPic(getPerson(item.from))" class="user-label notCopy selNone" :src="userWearPic(getPerson(item.from))" :onerror="hideElm">
            <div v-if="getPerson(item.from).wearType==2" class="user-label-text notCopy selNone">{{ getPerson(item.from).wearName }}</div>
          </div>
          <div class="msg-box" @contextmenu.stop="setMenu($event, item, 2)">
            <div class="user-name-box">
              <div class="user-name notCopy" v-show="item.scene!='p2p'||msgType==5">{{ getPerson(item.from).userShowName || getPerson(item.from).name }}</div>
              <div class="dataCopy">{{ (getPerson(item.from).userShowName || getPerson(item.from).name) + ' ' + dateFormat(item.time, 'yyyy-MM-dd HH:mm:ss') }}</div>
              <span v-if="strKey" class="msg-show-record" @click="showRecord(item.time,item.idServer)">查看前后消息</span>
            </div>
            <div class="msg-content-box" :class="[item.detailType=='document'?'msg-content-doc':'',item.aiLoading?'msg-ai-loading':'',item.isXLTyping?'msg-ai-typing':'']">
              <div :class="['msg-content',item.forbidFileMsg?'msg-forbid-content':'']">
                <i v-if="item.aiLoading" class="icon-ai-loading"></i>
                <!--反垃圾-->
                <div v-if="item.antispamMsg" class="msg-text notCopy selNone">[消息涉嫌违规，不支持查看]</div>
                <!--文本-->
                <div v-else-if="item.type=='text'" class="msg-text" v-html="item.showText&&!strKey?item.showText:buildEmoji(strToHtml(item.text,'',item,strKey))"></div>
                <!--图片-->
                <img v-else-if="item.type=='image'&&item.file" class="msg-img" :width="item.file.showW" :height="item.file.showH"
                     src="/img/image_default.png" :data-local="item.file.fileInfo?item.file.fileInfo.url:''"
                     :data-src="item.file.url||item.file.dataURL" :data-key="''+item.time+key" :data-ext="item.file.ext" :data-size="item.file.size"
                     @load="loadImage($event, item, item)" :onerror="errorImage.bind(this,item,'')"
                     @click="clickImage" @dblclick="toViewer" @contextmenu.stop="setMenu($event, item, 3, item)"
                     data-img="1" :data-idserver="item.idServer" :data-idclient="item.idClient" :data-time="item.time" :data-scene="item.scene" :data-from="item.from" :data-to="item.to" :data-sessionid="item.sessionId">
                <!--语音-->
                <div v-else-if="item.type=='audio'&&item.file" class="msg-audio" :class="{'play':isPlayAudio(item)}" @click="playAudio(item,key)">
                  <div class="audio-box" :style="'width:'+(70+Math.round((item.file.dur) / 1000)*2)+'px'">
                    <span class="audio-dur notCopy selNone">{{ Math.round((item.file.dur) / 1000) == 0 ? 1 : Math.round((item.file.dur) / 1000) }}"</span>
                    <i class="audio-icon"></i>
                  </div>
                  <div class="audio-text-box" :class="{'no-text':!item.audioText}" v-if="item.audioTextStatus" @click.stop="stopPropagation">
                    <img v-if="item.audioTextStatus==1" class="audio-loading" src="/img/index/msg/audio_loading.png" alt="">
                    <span v-else>{{ item.audioText ? item.audioText : "未识别到文字。" }}</span>
                  </div>
                </div>
                <!--视频-->
                <div v-else-if="item.type=='video'&&item.file" class="msg-video notCopy selNone" @click="openFile(item)">
                  <img class="video-img" src="/img/image_default.png" :data-local="item.file.fileInfo?item.file.fileInfo.url:''"
                       :data-src="item.file.videoPic" @load="loadImage($event, item, item)" :onerror="errorImage.bind(this,item,'')">
                  <div class="msg-video-info">
                    <div class="file-name" :title="item.file.name">
                      <span class="name textEls">{{ item.file.showName || item.file.name }}</span>
                      <span class="ext textEls" v-if="item.file.ext">{{ "." + item.file.ext }}</span>
                    </div>
                    <div class="video-info">
                      <div class="file-size" :key="item.progress">
                        {{ item.file.progress ? dealMem(item.file.size * item.file.progress / 100) + "/" : "" }}{{ dealMem(item.file.size) }}
                      </div>
                      <div class="video-time">{{ secToTime(item.file.dur || 0) }}</div>
                    </div>
                  </div>
                  <div class="msg-video-operate">
                    <div v-if="item.file.progress">
                      <!--下载进度-->
                      <div class="msg-video-progress">
                        <div class="msg-video-left" :key="item.progress"
                             :style="item.file.progress>=50?'transform:rotate('*****(item.file.progress-50)+'deg);':''"></div>
                        <div class="msg-video-right" :key="item.progress"
                             :style="item.file.progress>=50?'transform:rotate(0);border-color: #3BD4F4;':'transform:rotate('+(3.6*item.file.progress)+'deg)'"></div>
                        <div class="msg-video-circle"></div>
                        <div class="msg-video-cancel" @click.stop="cancelSendFile(item)">取消</div>
                      </div>
                    </div>
                    <div v-else>
                      <!--下载/播放-->
                      <img v-if="item.file.videoInfo.path" class="msg-video-download" src="/img/index/msg/video_play.png" alt="">
                      <img v-else class="msg-video-download" src="/img/index/msg/video_download.png" alt="">
                    </div>
                  </div>
                </div>
                <!--文件类型图片-->
                <div v-else-if="item.type=='file'&&item.file&&item.file.isImage&&item.file.size<25*1024*1024" class="msg-video"
                     @click="openFile(item)">
                  <img class="video-img" src="/img/image_default.png" :data-local="item.file.fileInfo?item.file.fileInfo.url:''"
                       :data-src="item.file.url+(item.file.url.indexOf('?')>-1?'&':'?')+'imageView&quality=10&thumbnail=250z300'" @load="loadImage($event, item, item)"
                       :onerror="errorImage.bind(this,item,'')" @click="clickImage" :width="item.file.showW" :height="item.file.showH">
                  <div class="msg-video-info notCopy selNone">
                    <div class="file-name" :title="item.file.name">
                      <span class="name textEls">{{ item.file.showName || item.file.name }}</span>
                      <span class="ext textEls" v-if="item.file.ext">{{ "." + item.file.ext }}</span>
                    </div>
                    <div class="video-info">
                      <div class="file-size" :key="item.progress">
                        {{ item.file.progress ? dealMem(item.file.size * item.file.progress / 100) + "/" : "" }}{{ dealMem(item.file.size) }}
                      </div>
                      <div class="video-time" v-if="item.status=='success'">{{ item.from == userInfo.workerNo ? "发送成功" : "收到文件" }}</div>
                    </div>
                  </div>
                  <div class="msg-video-operate notCopy selNone" v-if="item.file.size >= 2 * 1024 * 1024">
                    <div v-if="item.file.progress">
                      <!--下载进度-->
                      <div class="msg-video-progress">
                        <div class="msg-video-left" :key="item.progress"
                             :style="item.file.progress>=50?'transform:rotate('*****(item.file.progress-50)+'deg);':''"></div>
                        <div class="msg-video-right" :key="item.progress"
                             :style="item.file.progress>=50?'transform:rotate(0);border-color: #3BD4F4;':'transform:rotate('+(3.6*item.file.progress)+'deg)'"></div>
                        <div class="msg-video-circle"></div>
                        <div class="msg-video-cancel" @click.stop="cancelSendFile(item)">取消</div>
                      </div>
                    </div>
                    <div v-else>
                      <!--下载-->
                      <img v-if="!item.file.fileInfo?.path" class="msg-video-download" src="/img/index/msg/video_download.png" alt="">
                    </div>
                  </div>
                </div>
                <!--文件-->
                <div v-else-if="item.type=='file'&&item.forbidFileMsg" class="msg-text notCopy selNone">[非法文件，已被本站拦截]</div>
                <div v-else-if="item.type=='file'&&item.file&&!item.forbidFileMsg" class="msg-file">
                  <div class="file-info-box">
                    <img class="file-icon notCopy selNone" :src="getFileIcon(item.file.ext)" alt="" @contextmenu.stop="setMenu($event, item, 1)">
                    <div class="file-content">
                      <div class="file-name" :title="item.file.name">
                        <span class="name textEls">{{ item.file.showName || item.file.name }}</span>
                        <span class="ext textEls" v-if="item.file.ext">{{ "." + item.file.ext }}</span>
                      </div>
                      <div class="file-size">{{ dealMem(item.file.size) }}</div>
                    </div>
                  </div>
                  <progress v-if="item.file.progress" :value="item.file.progress" max="100" :key="item.progress"></progress>
                  <div class="file-status-box notCopy selNone">
                    <span class="file-status">{{
                        item.status == 'sending' || item.status == 'toSending' ? item.file.progress == 100 ? '正在拼命发送中' :
                          '发送中： ' + (item.file.progress || 0) + '%' : item.status == 'success' && item.file.progress ? item.file.progress + '%' :
                          item.status == 'fail' ? '发送失败' : item.from == userInfo.workerNo ? '发送成功' : '收到文件'
                      }}</span>
                    <div class="file-operate-box">
                      <span class="file-operate" v-if="item.status=='sending'||item.status=='toSending'||(item.status=='success'&&item.file.progress)"
                            @click="cancelSendFile(item)">取消{{ item.status == 'sending' || item.status == 'toSending' ? "发送" : "下载" }}</span>
                      <span v-else>
                        <span class="file-operate" v-if="item.status=='success'" @click="openFile(item)">
                          {{ item.file.fileInfo.url ? '打开' : '下载' }}
                        </span>
                        <span class="file-operate" v-if="item.status=='success'" @click="openFolder(item)">
                          {{ item.file.fileInfo.url ? '打开文件夹' : '另存为' }}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
                <!--自定义消息-->
                <div v-else-if="item.type=='custom'" class="msg-custom">
                  <div v-if="item.content" class="msg-content-type">
                    <!--多种消息-->
                    <div v-if="item.content.type=='multi'&&item.content.msgs" class="msg-multi-all-box">
                      <!--多种消息遍历-->
                      <span class="msg-multi-box" v-for="(item1,key1) in item.content.msgs" :key="key1">
                        <!--文本-->
                        <span v-if="item1.type=='text'" :class="['msg-text',item.isXLTyping?'msg-typing':'']" v-html="item1.showText&&!strKey?item1.showText:buildEmoji(strToHtml(item1.text,'',item1,strKey))"></span>
                        <!--图片-->
                        <img v-else-if="item1.type=='image'&&item1.file" class="msg-img" :width="item1.file.showW" :height="item1.file.showH"
                             src="/img/image_default.png" :data-local="item1.file.fileInfo?item1.file.fileInfo.url:''"
                             :data-src="item1.file.url||item1.file.dataURL" :data-key="''+item.time+key1" :data-ext="item1.file.ext" :data-size="item1.file.size"
                             @load="loadImage($event, item1, item)" :onerror="errorImage.bind(this,item1,item)" @click="clickImage"
                             @contextmenu.stop="setMenu($event, item, 3, item1)" @dblclick="toViewer($event)">
                        <!--文件-->
                        <span v-else-if="item.content.msgs.length==1&&item1.type=='file'&&item1.file" class="msg-file">
                          <div class="file-info-box">
                            <img class="file-icon notCopy selNone" :src="getFileIcon(item1.file.ext)" alt="" @contextmenu.stop="setMenu($event, item, 1)">
                            <div class="file-content">
                              <div class="file-name" :title="item1.file.name">
                                <span class="name textEls">{{ item1.file.showName || item1.file.name }}</span>
                                <span class="ext textEls " v-if="item1.file.ext">{{ "." + item1.file.ext }}</span>
                              </div>
                              <div class="file-size">{{ dealMem(item1.file.size) }}</div>
                            </div>
                          </div>
                          <div class="file-status-box notCopy selNone">
                          <span class="file-status">{{ item1.from == userInfo.workerNo ? '发送成功' : '收到文件' }}</span>
                          <div class="file-operate-box">
                            <span class="file-operate" @click="openFile(item1)">{{ item1.file.fileInfo.url ? '打开' : '下载' }}</span>
                            <span class="file-operate" @click="openFolder(item1)">{{ item1.file.fileInfo.url ? '打开文件夹' : '另存为' }}</span>
                          </div>
                        </div>
                        </span>
                        <!--乐文档卡片-->
                        <div v-else-if="item1.type=='document'&&item1.file" class="msg-doc" @click="toDocLink({item:item, docId:item1.file.docId, padId:item1.file.docPadId, docName:item1.file.docName, isShare:true})">
                          <div class="dataCopy">{{ getDocJson({docId: item1.file.docId, padId: item1.file.docPadId, docName: item1.file.docName, property: item1.file.property}).url }}</div>
                          <div class="msg-doc-header notCopy selNone">
                            <span class="msg-doc-text-box textEls2">
                              <i :class="['msg-doc-icon',getDocType(item1.file)==2?'msg-excel-icon':'']"></i>
                              <span class="msg-doc-text">{{ item1.file.docName }}</span>
                            </span>
                            <i class="msg-doc-back"></i>
                          </div>
                          <div class="msg-doc-content notCopy selNone">
                            <img class="notCopy selNone selNone" :src="`/img/index/msg/doc/${getDocType(item1.file)==1?'doc':'excel'}_bg.png`">
                            <div class="msg-doc-bg" :style="'background-image: url('+item1.file.docImg+')'"></div>
                          </div>
                          <div class="msg-doc-footer notCopy selNone" v-if="item1.file.empNumber==userInfo.workerId&&item.from==userInfo.workerNo&&!isFcw()&&!isAi()&&msgType!=5&&msgType!=6">
                            <span>赋予{{ sessionInfo.scene == "p2p" ? sessionInfo.detailInfo.workerName : "该会话成员" }}</span>
                            <span class="msg-doc-pur" :class="{'active':item1.file.flag}" @click.stop="showDocOp($event,item1.file)">
                              {{ item1.file.docPur == 1 ? "可阅读" : item1.file.docPur == 2 ? "可编辑" : "无权限" }}
                            </span>
                          </div>
                        </div>
                      </span>
                      <!--小乐消息-->
                      <div v-if="item.content.robot" class="msg-robot">
                        <!--相关问题-->
                        <div v-if="item.content.robot.likeAnswer">
                          <div class="robot-title">{{ item.content.robot.likeAnswer.keyName || "" }}</div>
                          <div class="robot-list-like" v-for="(item1,key1) in item.content.robot.likeAnswer.links" :key="key1">
                            <span class="robot-list-link" @click="getLinkUrl(item,item1.url)">{{ key1 + 1 }}、{{ item1.linkName }}</span>
                          </div>
                        </div>
                        <!--评价按钮-->
                        <div v-if="item.content.robot.commentButtons" class="robot-comment-box">
                          <span class="robot-title">{{ item.content.robot.commentButtons.keyName || "" }}</span>
                          <span class="robot-list-btn-box">
                            <span v-for="(item1,key1) in item.content.robot.commentButtons.links" :key="key1" @click="postUrl(item1)"
                                  :class="[(item1.linkName=='是'||item1.linkName=='否')?'robot-list-btn':'robot-list-link-btn']">
                              {{ item1.linkName }}
                              <span class="robot-comment-icon1" v-if="item1.linkName=='是'"></span>
                              <span class="robot-comment-icon2" v-if="item1.linkName=='否'"></span>
                            </span>
                          </span>
                        </div>
                        <!--评价小乐跳转-->
                        <span class="robot-link" v-else-if="item.content.robot.comment"
                              @click="getLinkUrl(item,item.content.robot.comment.url)">{{ item.content.robot.comment.keyName }}</span>
                        <!--问题清单-->
                        <span class="robot-link" v-if="item.content.robot.allAnswerLink"
                              @click="getLinkUrl(item,item.content.robot.allAnswerLink.url)">{{ item.content.robot.allAnswerLink.keyName }}</span>
                      </div>
                    </div>
                    <!--乐乐表情包-->
                    <img v-else-if="item.content.type==3&&item.content.data" class="ll-emoji notCopy selNone" alt=""
                         @contextmenu.stop="setMenu($event, item, 1)"
                         :src="'/img/emoji/'+item.content.data.catalog+'/'+item.content.data.chartlet+(item.content.data.catalog=='jjs'||item.content.data.catalog=='xl'?'.gif':'.png')">
                    <!--房源5-->
                    <div v-else-if="item.content.type==5&&item.content.data" class="house-content-type5" title="点击打开外部链接" @click="getLinkUrl(item)">
                      <img class="house-img" :src="getFangImage(item.content.data.houseImage)" alt="" @contextmenu.stop="setMenu($event, item, 1)" :onerror="fangError">
                      <span class="house-box">
                        <span class="house-title textEls">{{ item.content.data.title || "" }}</span>
                        <span class="house-intr textEls" v-if="item.content.data.houseType==4">
                          {{ item.content.data.areaName || "" }}-{{ item.content.data.placeName || "" }}
                        </span>
                        <span class="house-intr textEls" v-else>
                          {{ item.content.data.room || "" }}房{{ item.content.data.hall || "" }}厅
                          {{ item.content.data.fitment || "" }} {{ item.content.data.forward || "" }}
                        </span>
                        <span class="house-price textEls">
                          {{ item.content.data.price || "" }}
                          {{
                            item.content.data.houseType == 1 ? "元/月" : item.content.data.houseType == 2 ? "万" :
                              item.content.data.houseType == 3 ? "元/m²" : item.content.data.houseType == 4 ? "元/m²" : ""
                          }}
                        </span>
                      </span>
                    </div>
                    <!--房源6-->
                    <div v-else-if="item.content.type==6&&item.content.data" class="house-content-type5" title="点击打开外部链接" @click="getLinkUrl(item)">
                      <img class="house-img" :src="getFangImage(item.content.data.image)" alt="" @contextmenu.stop="setMenu($event, item, 1)" :onerror="fangError">
                      <span class="house-box">
                        <span class="house-title textEls">{{ item.content.data.title || "" }}</span>
                        <span class="house-intr textEls">{{ item.content.data.content || "" }}</span>
                        <span class="house-price textEls">{{ item.content.data.contentRed || "" }}</span>
                      </span>
                    </div>
                    <!--房源7-->
                    <div v-else-if="item.content.type==7&&item.content.data" class="house-content-type7" title="点击打开外部链接" @click="getLinkUrl(item)">
                      <img class="house-img" :src="getFangImage(item.content.data.image)" alt="" @contextmenu.stop="setMenu($event, item, 1)" :onerror="fangError">
                      <span class="house-box">
                        <span class="house-title textEls">{{ item.content.data.title || "" }}</span>
                        <span class="house-intr textEls">{{ item.content.data.content || "" }}</span>
                        <span class="house-price3 textEls" v-if="item.content.data.sourceType=='minApp-ycya'">{{ item.content.data.contentSell || "" }}</span>
                        <span class="house-price-box" v-else>
                          <span class="house-price1 textEls" v-if="item.content.data.contentSell">{{ item.content.data.contentSell }}</span>
                          <span class="house-price2 textEls" v-if="item.content.data.contentRent">{{ item.content.data.contentRent }}</span>
                        </span>
                      </span>
                    </div>
                    <!--链接卡片-->
                    <div v-else-if="item.content.type==8&&item.content.data&&item.content.data.sourceType!='app-lxt-zb'" class="card-content-type"
                         @click="getLinkUrl(item)">
                      <span class="card-box">
                        <span class="card-title textEls" :title="item.content.data.title">{{ item.content.data.title || "" }}</span>
                        <span class="card-content-box">
                          <img v-if="item.content.data.image" class="card-img" :src="item.content.data.image" alt=""
                               @contextmenu.stop="setMenu($event, item, 1)">
                          <span class="card-content textEls" :title="item.content.data.content" v-html="strToHtml(item.content.data.content, true)"></span>
                        </span>
                      </span>
                      <span class="card-intr-box">
                        <span class="card-intr textEls" :title="item.content.data.sourceName">{{ item.content.data.sourceName || "" }}</span>
                        <span class="msg-open" v-if="item.content.data.url&&(!item.content.data.openType||/pc/i.test(item.content.data.openType))"
                              title="点击打开外部链接">打开</span>
                      </span>
                    </div>
                    <!--合并转发消息记录-->
                    <div v-else-if="item.content.type==9&&item.content.data" class="card-content-type" @click="getLinkUrl(item)">
                      <span class="card-box">
                        <span class="card-title textEls" :title="item.content.data.title">{{ item.content.data.title || "" }}</span>
                        <span class="card-content-box">
                          <img v-if="item.content.data.image" class="card-img" :src="item.content.data.image" alt=""
                               @contextmenu.stop="setMenu($event, item, 1)">
                          <span class="card-content textEls" :title="item.content.data.content" v-html="strToHtml(item.content.data.content, true)"></span>
                        </span>
                      </span>
                      <span class="card-intr-box">
                        <span class="card-intr textEls">聊天记录</span>
                        <span class="msg-open" v-if="item.content.data.url" title="点击打开外部链接">打开</span>
                      </span>
                    </div>
                    <!--收藏-->
                    <div v-else-if="item.content.type=='collect'&&item.content.data" class="msg-file msg-collect-type" @click="getLinkUrl(item)"
                         title="点击打开外部链接">
                      <div class="file-info-box">
                        <img class="file-icon notCopy selNone" :src="item.content.data.imgUrl||'/img/index/msg/folder/link.png'" alt=""
                             @contextmenu.stop="setMenu($event, item, 1)">
                        <div class="file-content">
                          <div class="file-name">
                            <span class="name textEls" :title="item.content.data.title">{{ item.content.data.title || "" }}</span>
                          </div>
                          <div class="file-size textEls" :title="item.content.data.pushContent" v-html="strToHtml(item.content.data.pushContent, true)"></div>
                        </div>
                      </div>
                      <div class="file-status-box notCopy selNone">
                        <span class="file-status notCopy selNone">收藏消息</span>
                        <div class="file-operate-box">
                          <span class="file-operate">打开</span>
                          <span class="file-operate" @click.stop="openForward(item)">转发</span>
                        </div>
                      </div>
                    </div>
                    <!--minApp消息-->
                    <span v-else-if="item.content.type=='minApp'&&item.content.data" class="msg-text">
                      您有一条{{ item.content.data.minAppTitle || "[自定义]" }}信息，请到乐办公APP查看。
                    </span>
                    <!--小乐问答-->
                    <span v-else-if="(item.content.type=='problemList'||item.content.type=='leToFangUser')&&item.content.data" class="answer-content-type">
                      <div class="answer-title" :class="{'bold':item.content.type!='leToFangUser'}">{{ item.content.data.text }}:</div>
                      <div class="answer-title-bold" v-if="item.content.type=='leToFangUser'">您可能还想了解:</div>
                      <ul class="answer-list" v-if="item.content.type=='problemList'">
                        <li v-for="(item1,key1) in item.content.data.problemList" :key="key1">{{ key1 }}.{{ item1 }}</li>
                      </ul>
                      <ul v-else>
                         <li class="answer-list" v-for="(item1,key1) in item.content.data.intentList" :key="key1">{{ key1 + 1 }}.{{ item1.intentName }}</li>
                      </ul>
                    </span>
                    <!--日程卡片-->
                    <div v-else-if="item.content.type=='schedule_invite'&&item.content.schedule" class="schedule-invite-type">
                      <div class="schedule-invite-title-box" :title="(item.content.schedule.title||'').trim()||'无主题'">
                        <div class="schedule-invite-title textEls">
                          <span class="schedule-update" v-if="/1/.test(item.content.schedule.updType)">更新</span>
                          <span class="schedule-title">{{ (item.content.schedule.title || "").trim() || "无主题" }}</span>
                        </div>
                      </div>
                      <div :class="['schedule-invite-content-box',msgType!=5&&msgType!=6?'':'not-btn-box']">
                        <div class="schedule-invite-time">
                          <span class="schedule-title">{{ getScheduleTime(item.content.schedule, 1) }}</span>
                          <span class="schedule-update" v-if="/2/.test(item.content.schedule.updType)">更新</span>
                        </div>
                        <div v-if="(item.content.schedule.conflict && userInfo.workerNo == item.content.schedule.toNo) ||
                         (item.content.schedule.fromConflict && userInfo.workerNo == item.content.schedule.fromNo)"
                             class="schedule-invite-conflict">该时段存在冲突日程
                        </div>
                        <div class="schedule-invite-more" v-show="item.content.schedule.showStatus"
                             @click="setScheduleInfo($event,item.content.schedule.scheduleId, item.idServer)">查看更多日程详情
                        </div>
                      </div>
                      <div v-if="msgType!=5&&msgType!=6">
                        <div class="schedule-invite-intr-box" v-if="item.content.schedule.disabled">
                          <div class="schedule-invite-intr-title">
                            {{ item.content.schedule.fromWorkerName }}已{{ (item.content.schedule.fromStatus == 1 ? '接受' : item.content.schedule.fromStatus == 2 ? '待定' : '拒绝') }}
                          </div>
                          <div v-if="item.content.schedule.fromStatus == 4 && item.content.schedule.rejectContent"
                               class="schedule-invite-intr-detail">拒绝原因：{{ item.content.schedule.rejectContent }}
                          </div>
                        </div>
                        <div v-else class="schedule-invite-btn-box">
                          <div v-show="item.content.schedule.showStatus" class="schedule-invite-btn-content">
                          <span class="schedule-invite-status schedule-invite-accept" :class="{'sel':item.content.schedule.status==1}"
                                @click="setScheduleStatus(item.content.schedule.scheduleId,1,item.idServer)">
                            <i></i>
                            <span>{{ item.content.schedule.status == 1 ? '已' : '' }}接受</span>
                          </span>
                            <span class="schedule-invite-status schedule-invite-refuse" :class="{'sel':item.content.schedule.status==4}"
                                  @click="setScheduleStatus(item.content.schedule.scheduleId,4,item.idServer)">
                            <i></i>
                            <span>{{ item.content.schedule.status == 4 ? '已' : '' }}拒绝</span>
                          </span>
                            <span class="schedule-invite-status schedule-invite-upcoming" :class="{'sel':item.content.schedule.status==2}"
                                  @click="setScheduleStatus(item.content.schedule.scheduleId,2,item.idServer)">
                            <i></i>
                            <span>{{ item.content.schedule.status == 2 ? '已' : '' }}待定</span>
                          </span>
                          </div>
                          <div v-show="!item.content.schedule.showStatus" class="schedule-invite-btn-add"
                               @click="setScheduleStatus(item.content.schedule.scheduleId,1,item.idServer,true)">加入日程
                          </div>
                        </div>
                      </div>
                    </div>
                    <!--日程提醒-->
                    <span v-else-if="(item.content.type=='schedule_remind'||item.content.type=='schedule_del')&&item.content.schedule"
                          class="schedule-remind-type">
                      <div class="schedule-remind-box">
                        <div class="schedule-remind-title">日程{{ item.content.type == 'schedule_del' ? '删除通知' : '提醒' }}</div>
                        <div class="schedule-remind-content-box">
                          <span class="schedule-remind-content-title">日程主题:</span>
                          <span class="schedule-remind-content-detail">{{ (item.content.schedule.title || "").trim() || "无主题" }}</span>
                        </div>
                        <div class="schedule-remind-content-box">
                          <span class="schedule-remind-content-title">日程时间:</span>
                          <span class="schedule-remind-content-detail">{{ getScheduleTime(item.content.schedule) }}</span>
                        </div>
                        <div v-if="item.content.type=='schedule_remind'">
                          <div class="schedule-remind-content-box" v-if="item.content.schedule.roomNames || item.content.schedule.customContent">
                            <span class="schedule-remind-content-title"><span>会</span><span>议</span><span>室:</span></span>
                            <span class="schedule-remind-content-detail"
                                  v-html="strToHtml(item.content.schedule.roomNames || item.content.schedule.customContent)"></span>
                          </div>
                          <div class="schedule-remind-content-box" v-if="item.content.schedule.videoContent">
                            <span class="schedule-remind-content-title">视频会议:</span>
                            <span class="schedule-remind-content-detail" v-html="strToHtml(item.content.schedule.videoContent)"></span>
                          </div>
                          <div class="schedule-remind-more" @click="setScheduleInfo($event,item.content.schedule.scheduleId, item.idServer)">查看更多日程详情</div>
                        </div>
                      </div>
                      <div v-if="item.content.type=='schedule_remind'">
                        <div class="schedule-remind-btn-box1">
                          <span class="schedule-remind-btn-intr">稍后提醒:</span>
                          <span class="schedule-remind-btn" @click="remindLater(item, 10)">10分钟</span>
                          <span class="schedule-remind-btn" @click="remindLater(item, 30)">30分钟</span>
                          <span class="schedule-remind-btn" @click="remindLater(item, 60)">1个小时</span>
                        </div>
                      </div>
                      <div v-else class="schedule-remind-btn-box2">{{ item.content.schedule.msg }}</div>
                    </span>
                    <!--乐文档提醒-被评论@-->
                    <span v-else-if="item.content.type=='document_remind'&&item.content.document" class="doc-content-type"
                          @click="toDocLink({item:item, docId:'', padId:item.content.document.padId, clearId:item.content.document.id, location:item.content.document.location})">
                      <div class="doc-box">
                        <div class="doc-title">{{ item.content.document.sendEmpName }}({{ item.content.document.sendDeptName }})</div>
                        <div class="doc-content" v-html="getDocTitle(item)"></div>
                        <div class="doc-intr textEls">{{ item.content.document.infoContent }}</div>
                      </div>
                      <div class="doc-btn">阅读详情</div>
                    </span>
                    <!--消息平台-->
                    <template v-else-if="(item.content.type=='msg-center'||item.content.type=='msg-center-link')&&item.content.data">
                      <MsgCenter :type="2" :item="item" :setMenu="setMenu" :setSubSerHtml="setSubSerHtml" :toViewer="toViewer" :clickImage="clickImage"></MsgCenter>
                    </template>
                    <!--消息播报-->
                    <template v-else-if="item.content.type=='msg-report'&&item.content.data">
                      <MsgReport :type="2" :item="item"></MsgReport>
                    </template>
                    <!--集中销售-->
                    <span v-else-if="item.content.type=='imJZSell'&&item.showContent" class="sell-content-type"
                          :class="{'sell-content-type-style':item.showContent.shareList?.length>0}">
                      <div class="sell-header">
                        <!--分享人标题-->
                        <div class="sell-header-info-box textEls">
                          <div class="sell-img-box" v-if="item.showContent.shareList">
                            <div class="user-avatar-box" v-for="(item1,key1) in item.showContent.shareList" :key="key1">
                              <div class="avatar-box">
                                <img class="avatar notCopy selNone" :src="item1.headPic" alt="" :onerror="avatarError.bind(this, 'p2p', item.from, '')">
                              </div>
                            </div>
                          </div>
                          <i class="sell-icon" v-if="item.showContent.shareList?.length>0"></i>
                          <span class="sell-title textEls">{{ item.showContent.imHeaderTag || "" }}</span>
                        </div>
                        <!--倒计时-->
                        <div class="sell-header-time-box" v-if="item.showContent.timeObj">
                          <div class="sell-header-time-start" v-show="item.showContent.timeObj.start">
                            <div class="sell-header-time-start-content">
                              <span class="sell-header-time-num">{{ item.showContent.timeObj.time1 }}</span>
                              <span>{{ item.showContent.timeObj.time1Str }}</span>
                              <span class="sell-header-time-num">{{ item.showContent.timeObj.time2 }}</span>
                              <span>{{ item.showContent.timeObj.time2Str }}</span>
                              <span>后结束</span>
                            </div>
                          </div>
                          <div class="sell-header-time-end" v-show="!item.showContent.timeObj.start">本次集销已结束</div>
                        </div>
                        <div class="sell-header-time-box" v-else>
                          <div class="sell-header-time-end">本次集销进行中</div>
                        </div>
                      </div>
                      <div class="sell-content">
                        <div class="sell-to-link" @click="setEvent(item, 1)">
                          <!--房源信息-->
                          <div class="sell-content-info-box">
                            <div class="sell-content-info">
                              <span class="sell-content-info-title textEls" :title="item.showContent.lpName">{{ item.showContent.lpName || "" }}</span>
                              <span class="sell-content-info-intr textEls" :title="item.showContent.lpIntr">{{ item.showContent.lpIntr }}</span>
                            </div>
                            <div class="sell-content-info-more">查看房源</div>
                          </div>
                          <!--标签-->
                          <div class="sell-label-box" v-if="item.showContent.imImageTag?.length>0">
                            <span class="sell-label" :class="{'sell-label-main':item1=='主推房源'}" v-for="(item1,key1) in item.showContent.imImageTag" :key="key1">{{ item1 }}</span>
                          </div>
                        </div>
                        <!--音频-->
                        <div class="sell-audio" v-if="item.showContent.audioUrl&&item.showContent.audioDuration"
                             @click="playAudio({idServer: item.idServer, setTips: true,file:{url: item.showContent.audioUrl,dur: item.showContent.audioDuration *1000}})">
                          <i class="sell-audio-icon" :class="{'stop':isPlayAudio(item)}"></i>
                          <span class="sell-audio-time">{{ item.showContent.audioDuration }}″</span>
                          <span>点击播放说辞语音</span>
                        </div>
                        <!--介绍-->
                        <div class="sell-intr" v-if="item.content.data.fangContent" v-html="buildEmoji(strToHtml(item.content.data.fangContent))"></div>
                        <div class="sell-video-img-box selNone notCopy" :class="'sell-video-img-box-'+item.showContent.jzSellListCountNum"
                             v-if="item.showContent.jzSellListCountNum>0">
                          <!--视频-->
                          <div class="sell-video-img-content" v-if="item.showContent.videoUrls?.length>0"
                               v-for="(item1,key1) in item.showContent.videoCovers" :key="key1">
                            <div class="sell-img-box" @click="viewerVideo" :data-src="item.showContent.videoUrls[key1]">
                              <img class="sell-img" :onerror="fangError" :src="item1||'-'">
                              <img class="sell-video-icon" src="/img/index/msg/video_play.png" alt="">
                            </div>
                          </div>
                          <!--图片-->
                          <div class="sell-video-img-content" v-if="item.showContent.shareUrlList?.length>0"
                               v-for="(item1,key1) in item.showContent.shareUrlList" :key="key1">
                            <div class="sell-img-box" @dblclick="toViewer">
                              <img class="sell-img msg-img" :onerror="fangError" :src="getFangImage(item1)" @click="clickImage"
                                   :data-local="item1" :data-src="item1" :data-key="''+item.time+key1" data-ext="png">
                            </div>
                          </div>
                        </div>
                        <!--二维码-->
                        <div class="sell-info-box" v-if="item.showContent">
                          <div class="sell-info-content sell-phone-box" v-if="item.showContent.contactList?.length>0">
                            <div v-if="item.showContent.contactList.length<4" :class="['textEls','sell-phone-title','sell-phone-title-'+ item.showContent.contactList.length]">合作联系人：</div>
                            <div class="sell-phone-content" v-for="(item1,key1) in item.showContent.contactList" :key="key1">
                              <span class="sell-phone-name textEls" :title="item1.name">{{ item1.name }}</span>
                              <span class="sell-phone textEls">{{ item1.phone }}</span>
                            </div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                          <div class="sell-info-content sell-qrcode-box" v-if="item.qrcode">
                            <img class="sell-qrcode selNone notCopy" :src="item.qrcode"/>
                            <div class="sell-qrcode-tips">乐办公APP-工作台右上角扫码，可一键转发微信</div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                        </div>
                        <!--转发-->
                        <div class="sell-share-box" @click="openForward(item)">
                          <img class="sell-share-icon selNone notCopy" src="/img/index/msg/sell_share.png"/>
                          <span>转发给同事</span>
                        </div>
                      </div>
                    </span>
                    <!--新房集中销售-->
                    <span v-else-if="item.content.type=='imNewHouseJZSell'&&item.showContent" class="sell-content-type sell-new">
                      <div class="sell-header">
                        <!--分享人标题-->
                        <div class="sell-header-info-box textEls">
                          <span class="sell-title textEls">优质项目集中销售</span>
                        </div>
                      </div>
                      <div class="sell-content">
                        <div class="sell-to-link" @click="setEvent(item, 2)">
                          <!--房源信息-->
                          <div class="sell-content-info-box">
                            <div class="sell-content-info">
                              <span class="sell-content-info-title textEls" :title="item.showContent.projectName">{{ item.showContent.projectName || "" }}</span>
                              <span class="sell-content-info-intr textEls" :title="item.showContent.lpIntr">{{ item.showContent.lpIntr }}</span>
                            </div>
                            <div class="sell-content-info-more">查看项目</div>
                          </div>
                          <div class="label-price-box">
                            <!--标签-->
                            <div class="sell-label-box" v-if="item.showContent.imImageTag&&item.showContent.imImageTag.length>0">
                              <span class="sell-label" :class="{'sell-label-main':item1=='集团主推'}" v-for="(item1,key1) in item.showContent.imImageTag" :key="key1">{{ item1 }}</span>
                            </div>
                            <!--单价-->
                            <div class="sell-price-box textEls" v-if="item.showContent.priceIntr" :title="item.showContent.priceIntr">{{ item.showContent.priceIntr }}</div>
                          </div>
                        </div>
                        <!--音频-->
                        <div class="sell-audio" v-if="item.showContent.audioUrl&&item.showContent.audioDuration"
                             @click="playAudio({idServer: item.idServer, setTips: true,file:{url: item.showContent.audioUrl,dur: item.showContent.audioDuration *1000}})">
                          <i class="sell-audio-icon" :class="{'stop':isPlayAudio(item)}"></i>
                          <span class="sell-audio-time">{{ item.showContent.audioDuration }}″</span>
                          <span>点击播放说辞语音</span>
                        </div>
                        <!--介绍-->
                        <div class="sell-main-title" v-if="item.showContent.sellPoint">项目卖点</div>
                        <div class="sell-intr sell-main-intr" v-if="item.showContent.sellPoint" v-html="buildEmoji(strToHtml(item.showContent.sellPoint))"></div>
                        <div class="sell-main-title" v-if="item.showContent.commissionDes">佣金和其他说明</div>
                        <div class="sell-intr sell-main-intr" v-if="item.showContent.commissionDes" v-html="buildEmoji(strToHtml(item.showContent.commissionDes))"></div>
                        <div class="sell-video-img-box selNone notCopy" :class="'sell-video-img-box-'+item.showContent.jzSellListCountNum"
                             v-if="item.showContent.jzSellListCountNum>0">
                          <!--视频-->
                          <div class="sell-video-img-content" v-if="item.showContent.videoUrls?.length>0"
                               v-for="(item1,key1) in item.showContent.videoCovers" :key="key1">
                            <div class="sell-img-box" @click="viewerVideo" :data-src="item.showContent.videoUrls[key1]">
                              <img class="sell-img" :onerror="fangError" :src="item1||'-'">
                              <img class="sell-video-icon" src="/img/index/msg/video_play.png" alt="">
                            </div>
                          </div>
                          <!--图片-->
                          <div class="sell-video-img-content" v-if="item.showContent.shareUrlList?.length>0"
                               v-for="(item1,key1) in item.showContent.shareUrlList" :key="key1">
                            <div class="sell-img-box" @dblclick="toViewer">
                              <img class="sell-img msg-img" :onerror="fangError" :src="getFangImage(item1)" @click="clickImage"
                                   :data-local="item1" :data-src="item1" :data-key="''+item.time+key1" data-ext="png">
                            </div>
                          </div>
                        </div>
                        <!--二维码-->
                        <div class="sell-info-box" v-if="item.showContent">
                          <div class="sell-info-content sell-phone-box" v-if="item.showContent?.lxrArray?.length>0">
                            <div class="sell-phone-content" v-for="(item1,key1) in item.showContent.lxrArray" :key="key1">
                              <span class="sell-phone-type textEls" :title="item1.type">{{ item1.type }}</span>
                              <span class="sell-phone-name textEls" :title="item1.name">{{ item1.name }}</span>
                              <span class="sell-phone textEls">{{ item1.phone }}</span>
                            </div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                          <div class="sell-info-content sell-qrcode-box" v-if="item.qrcode">
                            <img class="sell-qrcode selNone notCopy" :src="item.qrcode"/>
                            <div class="sell-qrcode-tips">乐办公APP-工作台右上角扫码，可一键转发微信</div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                        </div>
                        <!--转发-->
                        <div class="sell-share-box" @click="openForward(item)">
                          <img class="sell-share-icon selNone notCopy" src="/img/index/msg/sell_share.png"/>
                          <span>转发给同事</span>
                        </div>
                      </div>
                    </span>
                    <!--批量房源-老-->
                    <span v-else-if="item.content.type=='imSomeHouseRecommend'&&item.showContent" class="sell-content-type">
                      <div class="sell-header">
                        <!--分享人标题-->
                        <div class="sell-header-info-box textEls">
                          <span class="sell-title textEls" :title="item.showContent.title||''">
                            {{ item.showContent.title || "" }}
                          </span>
                        </div>
                      </div>
                      <div class="sell-content">
                        <div class="sell-house-list" v-for="(item1,key1) in item.showContent.houseList">
                          <div class="sell-to-link" @click="getLinkUrl(item,'','',item1)">
                            <!--房源信息-->
                            <div class="sell-content-info-box">
                              <div class="sell-content-info">
                                <span class="sell-content-info-title textEls" :title="item1.lpName">{{ item1.lpName || "" }}</span>
                                <span class="sell-content-info-intr textEls" :title="item1.lpIntr">{{ item1.lpIntr }}</span>
                              </div>
                              <div class="sell-content-info-more">查看房源</div>
                            </div>
                            <!--标签-->
                            <div class="sell-label-box" v-if="item1.tagList&&item1.tagList.length>0">
                              <span class="sell-label" v-for="(item2,key2) in item1.tagList" :key="key2">{{ item2 }}</span>
                            </div>
                          </div>
                          <!--介绍-->
                          <div class="sell-intr" v-if="item1.content" v-html="buildEmoji(strToHtml(item1.content))"></div>
                          <!--图片-->
                          <div class="sell-video-img-box sell-video-img-box-1">
                            <div class="sell-video-img-content">
                              <div class="sell-img-box" @dblclick="toViewer">
                                <img class="sell-img msg-img" :onerror="fangError" :src="getFangImage(item1.imgUrl)" @click="clickImage"
                                     :data-local="item1.imgUrl" :data-src="item1.imgUrl" :data-key="''+item.time+'-'+item.idServer" data-ext="png">
                              </div>
                            </div>
                          </div>
                        </div>
                        <!--二维码-->
                        <div class="sell-info-box">
                          <div class="sell-info-content sell-phone-box" v-if="item.showContent.name">
                            <div class="textEls sell-phone-title sell-phone-title-1">合作联系人：</div>
                            <div class="sell-phone-content">
                              <span class="sell-phone-name textEls" :title="item.showContent.name">{{ item.showContent.name }}</span>
                              <span class="sell-phone textEls">{{ item.showContent.phone }}</span>
                            </div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                          <div class="sell-info-content sell-qrcode-box" v-if="item.qrcode">
                            <img class="sell-qrcode selNone notCopy" :src="item.qrcode"/>
                            <div class="sell-qrcode-tips">乐办公APP-工作台右上角扫码，查看多套房源详情</div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                        </div>
                        <!--转发-->
                        <div class="sell-share-box" @click="openForward(item)">
                          <img class="sell-share-icon selNone notCopy" src="/img/index/msg/sell_share.png"/>
                          <span>转发给同事</span>
                        </div>
                      </div>
                    </span>
                    <!--批量房源-新-->
                    <span v-else-if="item.content.type=='imHouseBatchShare'&&item.showContent" class="sell-content-type multi-house">
                      <div class="sell-header">
                        <!--分享人标题-->
                        <div class="sell-header-info-box textEls">
                          <span class="sell-title textEls" :title="item.showContent.title||''">
                            {{ item.showContent.title || "" }}
                          </span>
                        </div>
                      </div>
                      <div class="sell-content">
                        <!--房源列表-->
                        <div :class="['sell-multi-house-list',item.showContent.houseList.length-1==key1?'last':'']" v-for="(item1,key1) in item.showContent.houseList">
                          <div class="sell-to-link" @click="getLinkUrl(item,'','',item1)">
                            <!--房源信息-->
                            <div class="sell-multi-house-box">
                              <div class="sell-multi-house-info-box">
                                <div class="sell-multi-house-info">
                                  <div class="sell-multi-house-info-text">
                                    <span class="sell-multi-house-title textEls" :title="item1.lpName">{{ item1.lpName || "" }}</span>
                                    <span class="sell-multi-house-intr">
                                      <span>{{ item1.room || 0 }}室{{ item1.hall || 0 }}厅</span>
                                      <span>{{ item1.buildingArea || 0 }}㎡</span>
                                      <span>{{ item1.orientation || "" }}</span>
                                      <span class="highlight">{{ item1.price }}{{ item1.rsType == 1 ? "元/月" : "万" }}</span>
                                      <span v-if="item1.commissionPrice!=null">佣金{{ item1.commissionPrice || 0 }}%</span>
                                    </span>
                                  </div>
                                  <i class="show-arrow arrow-right"></i>
                                </div>
                                <div class="sell-multi-house-content textEls2" v-if="item1.internalStatement" :title="item1.internalStatement">{{ item1.internalStatement }}</div>
                              </div>
                            </div>
                            <!--最多显示6套蒙层-->
                            <div v-if="key1==5&&item.showContent.houseCount>6" class="sell-multi-house-modal"></div>
                          </div>
                        </div>
                        <div class="sell-multi-house-tips" v-if="item.showContent.houseCount>6">点击下方按钮查看全部房源</div>
                        <!--二维码-->
                        <div class="sell-info-box">
                          <div class="sell-info-content sell-phone-box" v-if="item.showContent.name">
                            <div class="textEls sell-phone-title sell-phone-title-1">合作联系人：</div>
                            <div class="sell-phone-content">
                              <span class="sell-phone-name textEls" :title="item.showContent.name">{{ item.showContent.name }}</span>
                              <span class="sell-phone textEls">{{ item.showContent.phone }}</span>
                            </div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                          <div class="sell-info-content sell-qrcode-box" v-if="item.qrcode">
                            <div class="sell-qrcode-img-box">
                              <img class="sell-qrcode selNone notCopy" :src="item.qrcode"/>
                              <div class="sell-qrcode-img-modal">
                                <img class="selNone notCopy" :src="item.qrcode"/>
                                <div class="sell-qrcode-img-modal-text selNone notCopy">乐办公扫码编辑或转发</div>
                              </div>
                            </div>
                            <div class="sell-qrcode-tips textEls2">鼠标悬浮后，乐办公扫码编辑或转发</div>
                          </div>
                          <div v-else class="sell-info-content"></div>
                        </div>
                        <!--转发-->
                        <div class="sell-multi-share-box">
                          <span class="sell-multi-btn" @click="openForward(item)">转发给同事</span>
                          <span class="sell-multi-btn textEls" v-if="item.showContent.batchShareId" @click="getLinkUrl(item,item.showContent.url,'login')">查看全部房源</span>
                        </div>
                      </div>
                    </span>
                    <!--审批消息-->
                    <span v-else-if="item.content.type=='card'" :class="'card-type-'+item.content.cardType">
                      <div v-if="item.content.cardType==4&&item.content.data" class="card-type">
                        <div class="card-header" :title="(item.content.data.title||'-')">
                          <div class="card-title-box textEls2">{{ (item.content.data.title || "-") }}</div>
                        </div>
                        <div class="card-content" :class="{'card-content-max':item.content.data.titleShowMax}">
                          <div class="card-info-box" v-for="(item1, key1) in item.content.data.listInfo" :key="key1">
                            <div v-if="item1.diyType==1" class="card-info-box1">
                              <span class="card-info-title">{{ item1.title }}:</span>
                              <span class="card-info-details">{{ item1.value || "-" }}</span>
                            </div>
                            <div v-else-if="item1.diyType==2">
                              <div class="card-link" @click="toCardLink(item1,1)">{{ item1.title }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="card-footer">
                          <div class="card-btn" v-for="(item1,key1) in item.content.data.jumpList" :key="key1" @click="toCardLink(item1,2)">{{ item1.content }}</div>
                        </div>
                      </div>
                      <div v-else class="msg-text">收到一条【自定义】消息，请升级到最新版本或前往手机端查看</div>
                    </span>
                    <!--集中销售通知提示-->
                    <span v-else-if="item.content.type=='jzxs-approval-notice'&&item.content.data" class="card-type">
                      <div class="card-header" :title="(item.content.data.title||'-')">
                        <div class="card-title-box textEls2">{{ (item.content.data.title || "-") }}</div>
                      </div>
                      <div class="card-content" :class="{'card-content-max':item.content.data.titleShowMax}">
                        <div class="card-info-box">
                          <div class="card-info-box1">
                            <span class="card-info-title">房源名称:</span>
                            <span class="card-info-details">{{ item.content.data.hslName || "-" }}</span>
                          </div>
                        </div>
                        <div class="card-info-box">
                          <div class="card-info-box1">
                            <span class="card-info-title">房源链接:</span>
                            <span class="card-info-details card-info-details-link" @click="getLinkUrl(item)">查看房源</span>
                          </div>
                        </div>
                        <div class="card-info-box">
                          <div class="card-info-box1">
                            <span class="card-info-title">核实信息:</span>
                            <span class="card-info-details" v-html="strToHtml(item.content.data.hslContent||'-')"></span>
                          </div>
                        </div>
                        <div class="card-info-box">
                          <div class="card-info-box1">
                            <span class="card-info-title">经纪人:</span>
                            <span class="card-info-details">{{ item.content.data.empName }} {{ item.content.data.phone }}</span>
                          </div>
                        </div>
                      </div>
                      <div class="card-footer" v-if="item.content.data.pcApplyUrl" @click="getLinkUrl(item,item.content.data.pcApplyUrl,'login')">
                        <div class="card-btn">去审批</div>
                      </div>
                    </span>
                    <!--有料有客文章卡片-->
                    <span v-else-if="item.content.type=='minApp_YLYK_Detail'&&item.content.data" class="ylyk-type">
                      <div class="ylyk-title textEls2" :title="item.content.data.minAppTitle">{{ item.content.data.minAppTitle }}</div>
                      <div class="ylyk-intr-box" @click="getLinkUrl(item)">
                        <div class="ylyk-intr-content">
                          <div class="ylyk-intr-label">摘要</div>
                          <div class="ylyk-intr-text textEls3" :title="item.content.data.des">{{ item.content.data.des }}</div>
                        </div>
                        <img class="ylyk-intr-img" :src="item.content.data.minAppImage" :onerror="errorAppImage.bind(this,1)">
                      </div>
                      <div class="ylyk-app-name-box">
                        <div class="ylyk-app-name">
                          <img class="notCopy selNone" :src="item.content.data.minAppIcon" :onerror="errorAppImage.bind(this,2)"/>
                          <div>{{ item.content.data.minAppName }}</div>
                        </div>
                        <div class="ylyk-app-more" @click="getLinkUrl(item)">查看全文</div>
                      </div>
                      <div class="ylyk-qrcode-box" v-if="item.qrcode">
                        <img class="ylyk-qrcode selNone notCopy" :src="item.qrcode"/>
                        <div class="ylyk-qrcode-text">乐办公APP-乐聊页面右上角+号扫一扫，可分享至微信吸粉</div>
                      </div>
                    </span>
                    <!--乐学堂短视频-->
                    <span v-else-if="item.content.type=='imShortVideo'" class="msg-text notCopy selNone">[乐学堂短视频，请到手机端查看]</span>
                    <!--乐学堂课程-->
                    <span v-else-if="item.content.type=='imCourseShare'&&item.content.data" class="msg-center-type">
                      <div class="msg-center-box">
                        <!--内容-->
                        <div class="msg-center-content">
                          <div class="msg-center-title textEls2">
                            <span class="msg-center-text" :title="item.content.data.title">{{ item.content.data.title || "乐学堂课程" }}</span>
                          </div>
                          <div class="msg-center-content-box">
                            <div class="msg-center-img-box">
                              <img class="msg-img" :src="getFangImage(item.content.data.image)" :data-local="item.content.data.image" :data-src="item.content.data.image"
                                   :data-key="''+item.time+key" @dblclick="toViewer" @click="clickImage" :onerror="fangError">
                            </div>
                            <div class="msg-center-intr textEls2" v-if="item.content.data.content" v-html="strToHtml(item.content.data.content)" :title="item.content.data.content"></div>
                            <div class="msg-center-btn-box">
                              <div class="msg-center-btn textEls" @click="showToast('请到手机乐办公APP查看此消息')">查看详情</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </span>
                    <!--资讯卡片-->
                    <span v-else-if="item.content.type=='imNews'&&item.content.data" class="im-news-type">
                      <div class="im-news-title-box" :title="item.content.data.title">
                        <span class="im-news-title">{{ item.content.data.title }}</span>
                        <span v-if="item.content.data.pcUrl" class="im-news-btn" @click="getLinkUrl(item,item.content.data.pcUrl,'login')">查看全文</span>
                      </div>
                      <div class="im-news-content-box">
                        <div class="im-news-content" v-if="item.content.data.content">
                          <div class="im-news-info-box" v-for="(item1,key1) in item.content.data.content" :key="key1">
                            <!--图片-->
                            <div class="im-news-img-box" v-if="item1.type=='img'" @dblclick="toViewer">
                              <img class="im-news-img msg-img" :onerror="fangError" :src="getFangImage(item1.value)" @click="clickImage" @load="loadDefaultImage"
                                   :data-local="item1.value" :data-src="item1.value" :data-key="''+item.time+'-'+item.idServer" data-ext="png" :width="item1.showW" :height="item1.showH">
                            </div>
                            <!--文字-->
                            <div class="im-news-text" v-if="item1.value&&item1.type=='text'" v-html="strToHtml(item1.value)"></div>
                          </div>
                        </div>
                        <!--二维码-->
                        <div :class="['im-news-qrcode-box',item.wxQrcode?'im-news-qrcode-box-wx':'']" v-if="item.content.data?.android?.appletId&&item.content.data?.weChatUrl">
                          <div class="im-news-qrcode-content" v-if="item.wxQrcode">
                            <div class="im-news-qrcode-img-box">
                              <img v-show="item.wxQrcode" class="im-news-qrcode-img selNone notCopy" :src="item.wxQrcode"/>
                              <i class="im-news-qrcode-icon icon-wx"></i>
                            </div>
                            <div class="im-news-qrcode-text-box textEls">
                              <div class="im-news-qrcode-title">微信扫码</div>
                              <div class="im-news-qrcode-intr">直接转发公众号文章</div>
                            </div>
                          </div>
                          <div class="im-news-qrcode-content" v-if="item.qrcode">
                            <div class="im-news-qrcode-img-box">
                              <img v-show="item.qrcode" class="im-news-qrcode-img selNone notCopy" :src="item.qrcode"/>
                              <i class="im-news-qrcode-icon icon-lbg"></i>
                            </div>
                            <div class="im-news-qrcode-text-box textEls">
                              <div class="im-news-qrcode-title">乐聊扫码</div>
                              <div class="im-news-qrcode-intr">获得个人专属海报</div>
                            </div>
                          </div>
                        </div>
                        <!--二维码-老版本-->
                        <div class="im-news-qrcode-box" v-else-if="item.content.data?.android?.appletId">
                          <div class="im-news-qrcode-content im-news-qrcode-content-old" v-if="item.qrcode">
                            <div class="im-news-qrcode-text-box" v-html="item.qrcodeText"></div>
                            <div class="im-news-qrcode-img-box">
                              <img v-show="item.qrcode" class="im-news-qrcode-img selNone notCopy" :src="item.qrcode"/>
                              <i class="im-news-qrcode-icon icon-lbg"></i>
                            </div>
                          </div>
                        </div>
                        <!--转发-->
                        <div class="im-news-share-box">
                          <span class="im-news-btn textEls" @click="openForward(item)">转发给同事</span>
                          <span v-if="item.content.data.pcUrl" class="im-news-btn textEls" @click="getLinkUrl(item,item.content.data.pcUrl,'login')">查看全文</span>
                        </div>
                      </div>
                    </span>
                    <!--合作卡片-->
                    <span v-else-if="item.content.type=='cooperate'&&item.content.data" class="cooperate-type">
                      <div class="cooperate-main-title">{{ item.content.data.title }}</div>
                      <div class="cooperate-main-intr">我有客户想与您发起合作，详细信息如下:</div>
                      <ul class="cooperate-detail-ul" v-if="item.content.data.requireType|| item.content.data.price||item.content.data.csCqPqName||item.content.data.houseArea">
                        <li class="cooperate-detail-li" v-if="item.content.data.requireType|| item.content.data.price">
                          <span class="cooperate-intr"><span>合</span><span>作</span><span>方:</span></span>
                          <span class="cooperate-detail">{{ item.content.data.hzfEmpName }}{{ item.content.data.hzfDeptName ? "(" + item.content.data.hzfDeptName + ")" : "" }}</span>
                        </li>
                        <li class="cooperate-detail-li" v-if="item.content.data.requireType|| item.content.data.price">
                          <span class="cooperate-intr">客户预算:</span>
                          <span class="cooperate-detail">{{ item.content.data.requireType ? item.content.data.requireType + "；" : "" }}{{ item.content.data.price }}</span>
                        </li>
                        <li class="cooperate-detail-li" v-if="item.content.data.csCqPqName">
                          <span class="cooperate-intr">意向区域:</span>
                          <span class="cooperate-detail">{{ item.content.data.csCqPqName }}</span>
                        </li>
                        <li class="cooperate-detail-li" v-if="item.content.data.houseArea">
                          <span class="cooperate-intr">意向户型:</span>
                          <span class="cooperate-detail">{{ item.content.data.houseArea }}</span>
                        </li>
                        <li class="cooperate-detail-li">
                          <span class="cooperate-intr">合作方式:</span>
                          <span class="cooperate-detail">{{ item.content.data.cooperateTypeStr }}</span>
                        </li>
                        <li class="cooperate-detail-li" v-if="item.content.data.cooperateType==1">
                          <span class="cooperate-intr">分佣比例:</span>
                          <span class="cooperate-detail">我方 {{ item.content.data.commissionMy }} ：合作方 {{ item.content.data.commissionYou }}</span>
                        </li>
                        <li class="cooperate-detail-li">
                          <span class="cooperate-intr">合作期限:</span>
                          <span class="cooperate-detail">{{ item.content.data.numDay }}天</span>
                        </li>
                        <li class="cooperate-detail-li" v-if="item.content.data.cooperateContent ">
                          <span class="cooperate-intr"><span>备</span><span>注:</span></span>
                          <span class="cooperate-detail">{{ item.content.data.cooperateContent }}</span>
                        </li>
                      </ul>
                      <div class="cooperate-intr-text">合作中，客户被合作方带看系统将通知发起人</div>
                      <div class="cooperate-btn" @click="openCooperateLink(item)">{{ item.content.data.fqfEmpNo == userInfo.workerNo ? "转发给他人发起新合作" : "查看详情" }}</div>
                    </span>
                    <!--ai卡片-->
                    <div v-else-if="item.content.type=='ai-card'" class="ai-card-type">
                      <div class="ai-title">{{ item.content.data.title }}</div>
                      <div class="ai-intr">{{ item.content.data.intr }}</div>
                      <!--ai应用-->
                      <ul v-if="item.content.data.appList?.length>0" class="ai-app">
                        <li v-for="(item1,key1) in item.content.data.appList" :key="key1" :class="['ai-app-box','ai-app-box-'+item.content.data.appList.length]"
                            @click="aiMsgOperate(6,item1)">
                          <div class="ai-app-icon-box notCopy selNone">
                            <img class="ai-app-icon" :src="item1.icon||'-'" :onerror="errorIcon">
                          </div>
                          <div class="ai-app-info">
                            <div class="ai-app-title textEls">{{ item1.title }}</div>
                            <div class="ai-app-intr textEls" :title="item1.intr">{{ item1.intr }}</div>
                          </div>
                        </li>
                      </ul>
                      <!--ai问题列表-->
                      <div v-if="item.content.data.questionObj" :class="['ai-other',!item.content.data.addAppObj?'ai-other-last':'']">
                        <div class="ai-other-title">{{ item.content.data.questionObj.title }}</div>
                        <ul v-for="(item1,key1) in item.content.data.questionObj.list" :key="key1" class="ai-other-ul">
                          <li v-if="item1.type=='msg'" class="ai-other-msg" :title="item1.text" @click="sendMsg(item1)">
                            <span class="ai-other-text">{{ item1.text }}</span>
                            <i class="show-arrow arrow-right"></i>
                          </li>
                        </ul>
                      </div>
                      <!--ai问题列表1-->
                      <ul v-if="item.content.data.msgList" class="ai-msg-ul">
                        <template v-for="(item1,key1) in item.content.data.msgList" :key="key1">
                          <li class="ai-msg-li" @click="sendMsg(item1)">{{ item1.title }}</li>
                        </template>
                      </ul>
                      <!--ai创建引用-->
                      <div v-if="item.content.data.addAppObj" class="ai-other">
                        <div class="ai-other-title">{{ item.content.data.addAppObj.title }}</div>
                        <ul class="ai-other-ul ai-other-app">
                          <template v-for="(item1,key1) in item.content.data.addAppObj.list" :key="key1">
                            <li v-if="item1.type=='addApp'" class="ai-other-add ai-app-box" @click="getLinkUrl(item,aiObj.appUrl+'?isShow=true','login')">
                              <div class="ai-app-info">
                                <div class="ai-app-title textEls">创建新的应用</div>
                                <div class="ai-app-intr textEls">发挥你的想象力，丰富我的技能</div>
                                <div class="show-arrow arrow-right"></div>
                              </div>
                            </li>
                            <li v-else-if="item1.type=='knowList'" class="ai-other-add ai-app-box" @click="getLinkUrl(item,aiObj.knowledgeUrl,'login')">
                              <div class="ai-app-info">
                                <div class="ai-app-title textEls">维护个人知识库</div>
                                <div class="ai-app-intr textEls">传什么知识，就能回答你什么问题</div>
                                <div class="show-arrow arrow-right"></div>
                              </div>
                            </li>
                          </template>
                        </ul>
                      </div>
                    </div>
                    <!--ai消息-->
                    <div v-else-if="item.content.type=='ai-msg'" class="ai-msg-type">
                      <template v-for="(item1,key1) in item.content.msgs" :key="key1">
                        <!--文本-->
                        <span v-if="item1.type=='text'" :class="['msg-text',item1.bold?'bold':'']" v-html="buildEmoji(strToHtml(item1.text,'',item1,strKey))"
                              :style="{'margin-top':item1.marginTop+'px','margin-bottom':item1.marginBottom+'px'}"></span>
                        <!--介绍文本-->
                        <span v-else-if="item1.type=='intr'" class="ai-msg-intr" v-html="buildEmoji(strToHtml(item1.text,'',item1,strKey))"></span>
                        <!--图片-->
                        <img v-else-if="item1.type=='image'&&item1.file" class="msg-img" :width="item1.file.showW" :height="item1.file.showH"
                             src="/img/image_default.png" :data-local="item1.file.fileInfo?item1.file.fileInfo.url:''"
                             :data-src="item1.file.url" :data-key="''+item.time+key1" :data-ext="item1.file.ext" :data-size="item1.file.size"
                             @load="loadImage($event, item1, item)" :onerror="errorImage.bind(this,item1,item)" @click="clickImage"
                             @contextmenu.stop="setMenu($event, item, 3, item1)" @dblclick="toViewer($event)">
                        <!--链接/发送文字/文件-->
                        <span v-else-if="item1.type=='link'" :class="['ai-msg-link-box'+(item1.isWrap?'-wrap':'')]">
                          <span v-if="item1.isWrap" class="ai-msg-link-wrap" @click="toAiMsgLink(item,item1)" :style="[item1.color?'color:'+item1.color:'']">{{ item1.text }}</span>
                          <span v-else class="ai-msg-link" @click="toAiMsgLink(item,item1)" :style="[item1.color?'color:'+item1.color:'']">{{ item1.text }}</span>
                        </span>
                        <!--按钮-->
                        <span v-else-if="item1.type=='button'&&item1.pc?.url||item1.pc?.type=='custom'" class="ai-msg-btn textEls notCopy selNone" @click="toAiMsgLink(item,item1)"
                              :style="{'margin-top':item1.marginTop+'px','margin-bottom':item1.marginBottom+'px'}">{{ item1.text }}</span>
                        <!--选项-->
                        <span v-if="item1.type=='column'" :class="['ai-option-box','ai-option-box-'+key1]">
                          <span class="ai-option-title">{{ item1.text }}</span>
                          <span v-for="(item2,key2) in item1.items" :key="key2" class="ai-option-text" @click="toAiMsgLink(item,item1,item2)">
                            <span>{{ item2.text }}</span>
                            <span class="show-arrow arrow-right"></span>
                          </span>
                        </span>
                        <!--多按钮-->
                        <span v-if="item1.type=='buttons'" class="ai-btn-box">
                          <template v-for="(item2,key2) in item1.items" :key="key2">
                            <span v-if="item2.pc" class="ai-btn textEls" @click="toAiMsgLink(item,item1,item2)">{{ item2.text }}</span>
                          </template>
                        </span>
                        <!--视频提示-->
                        <span v-if="item1.type=='video'" class="ai-video">[视频消息，请到手机端查看]</span>
                      </template>
                    </div>
                    <!--分享ai应用-->
                    <div v-else-if="item.content.type=='ai-app-card'&&item.content.data" class="ai-app-card-type">
                      <div class="card-header">
                        <span class="textEls2" :title="item.content.data.title">{{ item.content.data.title }}</span>
                      </div>
                      <div class="card-content">
                        <ul class="card-ul">
                          <template v-for="(item,key) in item.content.data.content" :key="key">
                            <li v-if="item.key||item.value" class="card-li">
                              <span v-if="item.key" class="intr">{{ item.key }}:</span>
                              <span class="detail" v-html="setSubSerHtml(item.value)"></span>
                            </li>
                          </template>
                        </ul>
                        <div v-if="item.content.data.button?.type" class="card-btn" @click="getPurToChat(item.content.data.button)">{{ item.content.data.button.name }}</div>
                      </div>
                    </div>
                    <!--乐学堂报名卡片-->
                    <span v-else-if="item.content.type=='lxt_signup'&&item.content.data" class="lxt-signup-type">
                      <div class="lxt-signup-title-box">
                        <div class="lxt-signup-title textEls2" :title="item.content.data.title">{{ item.content.data.title }}</div>
                      </div>
                      <ul class="lxt-signup-ul">
                        <li class="lxt-signup-li">
                          <div class="lxt-sign-flex-1">
                            <span class="lxt-sign-intr">已报:</span>
                            <span class="lxt-sign-detail textEls">{{ item.content.data.hasCount }}</span>
                          </div>
                          <div class="lxt-sign-flex-1">
                            <span class="lxt-sign-intr">剩余名额:</span>
                            <span class="lxt-sign-detail textEls">{{ item.content.data.remainCount }}</span>
                          </div>
                        </li>
                        <li class="lxt-signup-li">
                          <span class="lxt-sign-intr">时间:</span>
                          <span class="lxt-sign-detail textEls2">{{ item.content.data.time }}</span>
                        </li>
                        <li class="lxt-signup-li">
                          <span class="lxt-sign-intr">地点:</span>
                          <span class="lxt-sign-detail textEls2" :title="item.content.data.address">{{ item.content.data.address }}</span>
                        </li>
                      </ul>
                      <div class="lxt-signup-btn" @click="showToast('暂不支持PC端，请前往APP端操作')">我要报名</div>
                    </span>
                    <!--面试工单卡片-->
                    <span v-else-if="item.content.type=='monad-card'&&item.content.data" class="lxt-signup-type">
                      <div class="lxt-signup-title-box">
                        <div class="lxt-signup-title textEls2" :title="item.content.data.title">{{ item.content.data.title }}</div>
                      </div>
                      <ul class="lxt-signup-ul">
                        <li class="lxt-signup-li">
                          <span class="lxt-sign-intr">面试工单的类型:</span>
                          <span class="lxt-sign-detail textEls2">{{ item.content.data.applyType }}</span>
                        </li>
                        <li class="lxt-signup-li">
                          <span class="lxt-sign-intr">面试环节:</span>
                          <span class="lxt-sign-detail textEls2">{{ item.content.data.node }}</span>
                        </li>
                        <li class="lxt-signup-li">
                          <span class="lxt-sign-intr">应聘职位:</span>
                          <span class="lxt-sign-detail textEls2">{{ item.content.data.dutyName }}</span>
                        </li>
                        <li class="lxt-signup-li">
                          <span class="lxt-sign-intr">当前面试官:</span>
                          <span class="lxt-sign-detail textEls2" :title="item.content.data.empNames">{{ item.content.data.empNames }}</span>
                        </li>
                      </ul>
                      <div class="lxt-signup-btn" @click="toCardLink(item.content.data,3)">去审批</div>
                    </span>
                    <!--快速行动-->
                    <span  v-else-if="item.content.type==='quickActing'" class="schedule-invite-type" >
                      <QuickActingCard  :body="item.content.data"></QuickActingCard>
                    </span>

                    <!--自定义消息-->
                    <span v-else class="msg-text">收到一条【自定义】消息，请升级到最新版本或前往手机端查看</span>
                  </div>
                  <div v-else-if="item.detailType=='collectMsg'" class="msg-text">格式错误，请联系管理员</div>
                  <!--自定义消息-->
                  <span v-else class="msg-text">收到一条【自定义】消息，请升级到最新版本或前往手机端查看</span>
                </div>

                <!--位置-->
                <div v-else-if="item.type=='geo'" class="msg-file msg-geo" @click="getLinkUrl(item)" title="点击打开外部链接">
                  <div class="file-info-box">
                    <img class="file-icon notCopy selNone" alt="" @contextmenu.stop="setMenu($event, item, 1)" :src="getFileIcon('geo')">
                    <div class="file-content">
                      <div class="file-name">
                        <span class="name textEls">位置分享</span>
                      </div>
                      <div class="file-size textEls2" :title="item.geo.title">{{ item.geo.title }}</div>
                    </div>
                  </div>
                  <div class="file-status-box notCopy selNone">
                    <div></div>
                    <div class="file-operate-box">
                      <span class="file-operate">打开</span>
                    </div>
                  </div>
                </div>

                <!--收藏内存-->
                <div v-else-if="item.type=='collectNote'||item.type=='collectElse'" class="msg-text msg-collect" v-html="buildEmoji(item.oHtml)"
                     @click="setCollectEvent($event,'click')" @dblclick="setCollectEvent($event,'dblclick')" @contextmenu="setCollectEvent($event,'contextmenu')"></div>
                <div v-else-if="item.type=='collectError'" class="msg-text">格式错误，请联系管理员</div>
                <!--其他消息-->
                <div v-else class="msg-text">收到一条【自定义】消息，请升级到最新版本或前往手机端查看</div>
                <!--正在输入的闪烁效果-->
                <div v-if="item.isXLTyping" class="msg-tying-input">|</div>
                <!--消息操作框-->
                <div v-if="getShowMenu(item)" :class="['msg-operate-box','notCopy', 'selNone',showTag!=1?'show-fixed':'']">
                  <ul :class="['msg-operate-ul','msg-operate-ai',showOperateId==item.idServer||key==currMsgList.length-1?'show':'']" v-if="item.content?.type=='ai-msg'&&isShowAiOperate(item)" v-show="showTag==1">
                    <li v-if="item.content.feedback" class="msg-operate-li" @click="aiMsgOperate(1,item)" @mouseenter="showTipsBox($event,'有帮助')" @mouseleave="showTipsBox">
                      <i class="icon-msg-operate approve"></i>
                    </li>
                    <li v-if="item.content.feedback" class="msg-operate-li" @click="aiMsgOperate(2,item)" @mouseenter="showTipsBox($event,'没帮助')" @mouseleave="showTipsBox">
                      <i class="icon-msg-operate oppose"></i>
                    </li>
                    <li v-if="item.content.spend" class="msg-operate-li">
                      <i class="icon-msg-operate detail" @mouseenter="aiMsgOperate(3,item,$event)" @mouseleave="showTipsBox"></i>
                    </li>
                    <li class="msg-operate-li">
                      <i class="icon-msg-operate copy" @click="aiMsgOperate(4,item)" @mouseenter="showTipsBox($event,'复制')" @mouseleave="showTipsBox"></i>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="msg-audio-point" v-if="item.type=='audio'&&item.file&&isAudioUnread(item)"></div>
              <!--loading-->
              <span class="msg-icon-position msg-loading" v-if="userInfo.workerNo==item.from&&(item.status=='sending'||item.status=='toSending')"></span>
              <!--error-->
              <span class="msg-icon-position msg-err-icon" v-if="item.detailStatus"></span>
              <!--已读-->
              <div class="msg-icon-position msg-read notCopy selNone" v-if="item.showRead">已读</div>
              <!--ai消息停止响应/重试-->
              <div class="msg-stop notCopy selNone" v-if="item.aiLoading||item.resendFlag||item.isXLTyping" @click="aiMsgOperate(5,item)">
                <i class="icon-ai"></i>
                <span class="textEls">{{ item.resendFlag ? "刷新" : "停止响应" }}</span>
              </div>
            </div>

            <!-- 引用-->
            <div class="msg-quote-box selNone notCopy" v-if="msgType!=2&&msgType!=5&&msgType!=6&&item.custom&&(item.custom.quoteTxt||item.custom.quoteImageLength>0)"
                 @click.stop="showQuoteDetail($event,item)">
              <span class="quote-text textEls" v-if="item.isQuoteDelete">引用内容已被撤回或是进群前消息</span>
              <span class="quote-text textEls" v-else v-html="buildEmoji(strToHtml(item.custom.quoteTxt, true))"></span>
              <span class="quote-img-box" v-if="!item.isQuoteDelete">
                <img v-if="item.custom.quoteImageLength>0" :src="item.custom.quoteImageList[0].url" alt="" class="quote-img">
                <i v-if="item.custom.quoteImageLength>1" class="quote-more"></i>
              </span>
            </div>

            <!--发送失败-->
            <div class="msg-err" v-if="userInfo.workerNo==item.from&&item.status=='fail'&&!item.detailStatus">
              <span class="msg-err-icon"></span>
              <span>发送失败，</span>
              <span class="resend" @click="resendMsg(item)">点击重发</span>
            </div>
          </div>
        </div>

        <!--被拉黑提示-->
        <div v-if="item.detailStatus" class="msg-tip-box">
          <div class="msg-tip notCopy selNone">{{ item.detailStatus }}</div>
        </div>
        <!--房产网客户用语提示-->
        <div v-if="isFcw()&&item.type!='tip'&&item.showFcwTips" class="msg-tip-box">
          <div class="msg-tip notCopy selNone">{{ item.showFcwTips }}</div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
const http = remote.require("http");
const fs = remote.require("fs");
import {inject, nextTick, ref, watch} from "vue";
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {
  avatarError, fangError, selElm, dealMem, secToTime, strToHtml, transTime, linkFormat, htmlEscapeAll, dateFormat, deepClone, debounce, showMenu, addMenu,
  getFileIcon, openViewer, getScheduleLocalStatus, getScheduleTime, hideElm, strToImg, openForward, dealMsgCenterContent, getOffset,
  createQrCode, dealFileField, getParents, setJJSEvent, isMainWin, getChildWin, userWearPic, msgToHtml, editMenu, dealMsgField, isSubOrSer, encrypt,
  toViewerMethod, setSubSerHtml, getUrlParams, convertMsg, getQuoteMsg, heicToJpg,
} from "@utils";
import {alert, toast, loading} from "@comp/ui";
import axios from "@utils/net/http";
import {deleteMessageApi, postApi, feedbackApi, stopAnswerApi, getAppPurApi, findMsgByIdApi, aiSparringApi} from "@utils/net/api"
import ChatUserInfoBox from "@comp/chat/ChatUserInfoBox";
import UUID from '@utils/uuid.js';
import MsgCenter from "@comp/msg/MsgCenter";
import MsgReport from "@comp/msg/MsgReport";
import QuickActingCard from "./CustomMsg/QuickActingCard";

export default {
  name: "ChatMsg",
  components: {ChatUserInfoBox, MsgCenter, MsgReport,QuickActingCard},
  props: {
    // 会话信息
    sessionInfo: {
      type: Object,
      default: {},
    },
    // 消息内容 引用消息、消息记录、搜索记录
    searchList: {
      type: Array,
      default: []
    },
    // 消息类型-1会话消息-2引用消息-3消息记录-4搜索记录-5合并转发-6收藏
    msgType: {
      type: String,
      default: "1"
    },
    // 关键词匹配
    strKey: {
      type: String,
      default: ""
    },
    // 查看前后消息
    showRecord: {
      type: Function,
      default: function () {},
    },
    // 显示引用消息弹窗
    showQuoteDetail: {
      type: Function,
      default: function () {},
    },
    // 显示文档操作弹窗
    showDocOp: {
      type: Function,
      default: function () {},
    },
    // 滚动到底部元素
    scrollElm: {},
    // 改变多选状态
    changeMultiple: {
      type: Function,
      default: function () {},
    },
    // 加载完不滚动
    notScroll: {
      type: Boolean
    },
    // 跳转通知
    toNotice: {
      type: Function,
    },
  },
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    // 配置文件
    let config = store.getters.getConfig.config;
    // 获取用户信息
    const userInfo = store.getters.getUserInfo;
    // 文件数据库对象
    const fileDB = store.getters.getFileDB;
    // 云信对象
    let nim = remote.store.getters.getNim;
    // 消息内容元素
    let msgUlRef = ref();
    // 多选选中消息id列表
    let multipleSelMap = ref({});
    // 消息列表
    let currMsgList = ref([]);
    // 是否滚动到底部
    let isScrollBottom = ref(store.getters.getEmit.isScrollBottom);
    // 请求定时器
    let postTimerMap = {
      postUrl: "",
      toCardLink: "",
      aiMsgOperate: "",
      toAiMsgLink: "",
    };
    // 语音转文字对象
    let audioMap = {};
    // 倒计时消息列表
    let msgTimerList = [];
    // 当前会话
    let currSessionInfo = ref(props.sessionInfo);
    // 父元素中的消息内容
    let parentMsgList = inject("msgList", []);
    // 父元素中的是否多选状态
    let parentIsMultiple = inject("isMultiple", false);
    // 父元素中的showTag对象
    let showTag = inject("showTag", 0);
    // 智能助理对象
    let aiObj = ref(deepClone(remote.store.getters.getState("aiObj")));
    // 提示对象
    let tipsObj = ref({
      update: "暂不支持该类型操作,请升级到最新版本后重试"
    });
    // 点击更多不隐藏操作栏id
    let showOperateId = ref("");

    // 初始化当前消息内容列表
    if (props.msgType == 1) {
      // 当前会话
      let currentId = store.getters.getCurrentSession.id
      if (currentId) {
        setCurrMsg(store.getters.getMsgs({id: currentId}), "bottom");
        currSessionInfo.value = store.getters.getSessions({id: currentId});
        initAiTempIdMap(currentId);
      }
      // 切换会话
      watch(() => store.state.currentSession.id,
        (newValue, oldValue) => {
          audioMap = {};
          currSessionInfo.value = store.getters.getSessions({id: newValue});
          initAiTempIdMap(newValue);
        }, {
          deep: true
        }
      );
    } else {
      setCurrMsg(props.searchList, "bottom");
      watch(() => props.searchList,
        (newValue, oldValue) => {
          initSetCurrMsg(props.searchList);
          if (props.msgType == 2) {
            // 引用消息打印简要内容
            let thisQuoteMsg = props.searchList[0] || {};
            console.log("showQuote", encrypt(JSON.stringify({custom: thisQuoteMsg.custom, content: thisQuoteMsg.content, text: thisQuoteMsg.text, type: thisQuoteMsg.type})));
          }
        }, {
          deep: true
        }
      );
      if (!props.sessionInfo.id) {
        // 当前会话
        let currentId = store.getters.getCurrentSession.id || store.getters.getCurrentSession.tempId || `p2p-${userInfo.workerNo}`
        currSessionInfo.value = remote.store.getters.getSessions({id: currentId});
      }
      watch(() => props.sessionInfo.id,
        (newValue, oldValue) => {
          audioMap = {};
          currSessionInfo.value = props.sessionInfo;
        }, {
          deep: true
        }
      );
    }

    // 更新当前会话消息
    watch(() => parentMsgList,
      (newValue, oldValue) => {
        if (props.msgType == 1 || props.msgType == 5) {
          initSetCurrMsg(parentMsgList.value);
        }
      }, {
        deep: true
      }
    );

    // 会话消息滚动
    watch(() => store.state.emit.scrollMsg,
      (newValue, oldValue) => {
        // 获取可视区域的消息
        getVisibleAreaMsg(newValue.msgType);
      }, {
        deep: true
      }
    );

    // 全局定时器
    watch(() => store.state.emit.globalTimer,
      (newValue, oldValue) => {
        for (let i = 0; i < msgTimerList.length; i++) {
          let item = msgTimerList[i];
          // 集中销售倒计时
          if (item.content && item.content.type == "imJZSell") {
            let msgIndex = currMsgList.value.findIndex(item1 => item1.idServer == item.idServer);
            if (msgIndex != null) {
              setJZSellTimer(currMsgList.value[msgIndex]);
            }
          }
        }
      }, {
        deep: true
      }
    );

    // 更新消息内容
    watch(() => store.state.emit.reloadMsg,
      async (newValue, oldValue) => {
        if (currMsgList.value.length > 0) {
          // 设置子窗口消息
          let childWin = getChildWin("chat-" + newValue.id);
          if (childWin && isMainWin()) {
            childWin.window.store.commit("setEmit", {type: "reloadMsg", value: deepClone(newValue)});
          } else {
            if (newValue.type == "quote" && newValue.idServer) {
              // 引用被撤回
              let quoteDeleteMap = store.getters.getQuoteDeleteMap(currSessionInfo.value.id);
              currMsgList.value.map(item => {
                if (item.idServer == newValue.idServer) {
                  if (quoteDeleteMap[item.idServer]) {
                    item.isQuoteDelete = true;
                  }
                }
              });
            } else if (newValue.type == "update" && (newValue.idServer || newValue.uniqueSign) && newValue.id) {
              // 更新消息
              let stateMsg = [];
              if (isMainWin()) {
                stateMsg = store.getters.getMsgs({id: newValue.id});
              } else {
                stateMsg = remote.store.getters.getMsgs({id: newValue.id});
              }
              let thisMsg = {};
              for (let i = 0; i < stateMsg.length; i++) {
                if ((newValue.idServer && newValue.idServer == stateMsg[i].idServer) || (newValue.uniqueSign && newValue.uniqueSign == stateMsg[i].uniqueSign)) {
                  thisMsg = stateMsg[i];
                  break;
                }
              }
              if (thisMsg.idServer || thisMsg.uniqueSign) {
                for (let i = 0; i < currMsgList.value.length; i++) {
                  if ((newValue.idServer && thisMsg.idServer == currMsgList.value[i].idServer) || (newValue.uniqueSign && thisMsg.uniqueSign == currMsgList.value[i].uniqueSign)) {
                    currMsgList.value[i] = deepClone(thisMsg);
                    currMsgList.value[i].progress = (currMsgList.value[i].progress || 0) + 1;
                    break;
                  }
                }
              }
            } else {
              if (props.msgType == 1) {
                setCurrMsg(store.getters.getMsgs({id: currSessionInfo.value.id}), "");
              } else {
                setCurrMsg(props.searchList, "msg");
              }
            }
          }
        }
      }, {
        deep: true
      }
    );

    // 监听最新已读状态
    watch(() => store.state.updateSessionId,
      (newValue, oldValue) => {
        if (newValue && newValue == currSessionInfo.value.id) {
          currSessionInfo.value = store.getters.getSessions({id: newValue});
          getMsgReadStatus();
        }
      }, {
        deep: true
      }
    );

    // 是否滚动到底部
    watch(() => store.state.emit.isScrollBottom,
      (newValue, oldValue) => {
        isScrollBottom.value = newValue;
      }, {
        deep: true
      }
    );

    // sse消息服务
    watch(() => store.state.sseMsgMap,
      (newValue, oldValue) => {
        if (props.msgType == 1) {
          store.getters.getSseMsgList({id: currSessionInfo.value.id, msgList: currMsgList.value});
          // 滚动位置
          if (isScrollBottom.value) {
            store.commit("setEmit", {type: "scroll", value: "bottom"});
          }
        }
      }, {
        deep: true
      }
    );

    // 播放下一条语音
    watch(() => store.state.emit.nextAudio,
      (newValue, oldValue) => {
        if (props.msgType == 1 || props.msgType == 3) {
          // 当前会话查询消息播放下个未读语音
          if (newValue.id == currSessionInfo.value.id) {
            let currentAudioIndex = newValue.index;
            let msgIndex = currMsgList.value.findIndex(item => {return item.idServer == newValue.idServer});
            if (props.msgType == newValue.msgType) {
              if (currentAudioIndex != undefined) {
                if (msgIndex > -1) {
                  currentAudioIndex = msgIndex;
                }
                // 查询下一个未读语音播放
                for (let i = currentAudioIndex + 1; i < currMsgList.value.length; i++) {
                  let item = currMsgList.value[i];
                  if (item.type == 'audio' && item.file && isAudioUnread(item)) {
                    playAudio(item, i);
                    break;
                  }
                }
              }
            }
            // 更新页面播放状态
            if (currMsgList.value[msgIndex]) {
              currMsgList.value[msgIndex].updateKey = (currMsgList.value[msgIndex].updateKey || 0) + 1;
            }
          }
        }
      }, {
        deep: true
      }
    );

    // 监听智能助理变化
    watch(() => store.state.aiObj,
      (newValue, oldValue) => {
        aiObj.value = deepClone(newValue);
        debounce({
          timerName: "setAiMsgCard" + props.msgType,
          time: 100,
          fnName: function () {
            setAiMsgCard();
          }
        });
      }, {
        deep: true
      }
    );

    // 切换ai应用
    watch(() => store.state.emit.switchAppTempItem,
      (newValue, oldValue) => {
        if (newValue) {
          // 在ai会话切换应用判断发送提示语
          if (currSessionInfo.value.to == aiObj.value.workerNo && currMsgList.value[0]) {
            setAiMsgCard();
          }
        }
      }, {
        deep: true
      }
    );

    // 监听全局点击
    watch(() => store.state.emit.click,
      (newValue, oldValue) => {
        if (showOperateId.value && !global.textmenuShow) {
          showOperateId.value = "";
        }
      }, {
        deep: true
      }
    );
    // 窗口失去焦点
    watch(() => store.state.emit.blur,
      (newValue, oldValue) => {
        if (showOperateId.value) {
          showOperateId.value = "";
        }
      }, {
        deep: true
      }
    );

    // 初始化消息
    function initSetCurrMsg(newMsgList) {
      let pos = "msg";
      if (!newMsgList[0] || !currMsgList.value[0] ||
        (newMsgList[0] && currMsgList.value[0] && newMsgList[0].sessionId != currMsgList.value[0].sessionId)) {
        pos = "bottom";
      }
      setCurrMsg(newMsgList, pos);
    }

    // 设置消息体内容
    async function setCurrMsg(list, pos) {
      msgTimerList = [];
      // 设置消息宽度
      if (msgUlRef.value && msgUlRef.value.clientWidth && props.msgType == 1) {
        let msgWidth = (document.querySelector(".chat-body").clientWidth - 32 - 48) * 0.72;
        let msgHeight = document.querySelector("#contentBox").clientHeight;
        let maxCenterImageWidth = isSubOrSer(currSessionInfo.value.id) ? 660 : 456;
        store.commit("setMsgWidthObj", {key: "msg", value: msgWidth});
        store.commit("setMsgWidthObj", {key: "height", value: msgHeight});
        store.commit("setMsgWidthObj", {key: "centerImage", value: msgWidth > maxCenterImageWidth ? maxCenterImageWidth : msgWidth});
        store.commit("setMsgWidthObj", {key: "newsImage", value: 456});
      }
      let currMsg = []
      let msgTime = 0;
      let msgList = deepClone(list || []);
      let quoteDeleteMap = store.getters.getQuoteDeleteMap(currSessionInfo.value.id);
      let antispamMap = store.getters.getAntispamMap[currSessionInfo.value.id];
      for (let i = 0; i < msgList.length; i++) {
        let item = msgList[i];
        try {
          // 房产网敏感词命中词
          let fcwKeyMap = {};
          // 是否被反垃圾
          item.antispamMsg = antispamMap && antispamMap[item.idServer];
          // 补偿机制，获取消息本地数据
          if (item.idServer) {
            store.dispatch("setMsgsLocal", {item: item}).then(res => {
              let msgIndex = currMsgList.value.findIndex(item1 => item1.idServer == item.idServer);
              if (msgIndex > -1) {
                currMsgList.value[msgIndex] = {};
                currMsgList.value[msgIndex] = item;
                // 判断是否有在下载中的进程
                if (item.file && (item.file.fileMD5 || item.file.videoMD5)) {
                  let downloadMD5 = item.file.videoMD5 || item.file.fileMD5;
                  let fileUpload = remote.store.getters.getNimFileUpload(downloadMD5);
                  if (fileUpload?.event) {
                    item.file = fileUpload.file;
                    item.file.downloadMD5 = downloadMD5;
                    currMsgList.value.splice(msgIndex, 1, item);
                    let thisItem = currMsgList.value[msgIndex];
                    // 更新下载进度
                    fileUpload.event.on("progress", (param) => {
                      if (param?.key == "del") {
                        delete thisItem.file.progress;
                        delete thisItem.file.downloadMD5;
                        delete thisItem.progress;
                        thisItem.updateKey = (item.updateKey || 0) + 1;
                      } else {
                        thisItem.progress = (thisItem.progress || 0) + 1;
                      }
                    });
                  }
                  // 设置图片下载地址

                  if (item.file?.fileInfo?.url && ((list[msgIndex]?.file?.url && !list[msgIndex]?.file?.fileInfo?.url) ||
                    (store.state.removeMD5Obj[item.file.fileMD5]?.path && (store.state.removeMD5Obj[item.file.fileMD5]?.path == list[msgIndex].file?.fileInfo?.path)))
                  ) {
                    list[msgIndex].updateKey = (list[msgIndex].updateKey || 0) + 1;
                    list[msgIndex].file = item.file;
                    currMsgList.value.splice(msgIndex, 1, item);
                  }
                }
              }
            });
            // 保留转语音
            if (audioMap[item.idServer]) {
              item.audioTextStatus = audioMap[item.idServer].audioTextStatus;
              item.audioText = audioMap[item.idServer].audioText;
            }
          }
          // 智能日程消息获取本地最新状态
          if (item.content && item.content.type == "schedule_invite" && item.content.schedule) {
            item.content.schedule.msgTime = item.time;
            item.content.schedule = getScheduleLocalStatus(item.content.schedule);
          }
          // 引用被撤回
          if (quoteDeleteMap[item.idServer]) {
            item.isQuoteDelete = true;
          }
          // 自定义多个消息
          if (item.content && item.content.type == "multi") {
            let multiMsg = item.content.msgs;
            multiMsg.map(item1 => {
              if (item1.type == "document") {
                // 乐文档本地最新状态
                let localDocPur = JSON.parse(localStorage.getItem("docPur") || '{}');
                if (!localDocPur[userInfo.workerNo]) {
                  localDocPur[userInfo.workerNo] = {};
                }
                // 文档卡片全部状态
                let docPurObj = localDocPur[userInfo.workerNo][item.to + "-" + item1.file.docId] || {};
                item1.file.docPur = docPurObj.key;
              } else if (item1.type == "text") {
                // 房产网用语问题提示
                if (item?.custom?.hideMessage == 1 && isFcw()) {
                  fcwKeyMap = Object.assign(fcwKeyMap, store.getters.getFcwKeyMap({scene: item.scene, to: item.to, text: item1.text}));
                }
              }
            });
          }
          // 房产网用语问题提示
          if (item?.custom?.hideMessage == 1 && item.type == "text" && isFcw()) {
            fcwKeyMap = store.getters.getFcwKeyMap({scene: item.scene, to: item.to, text: item.text});
          }
          // 判断是否命中房产网敏感词
          if (isFcw()) {
            let fcwKeyText = store.getters.getFcwKeyText(fcwKeyMap);
            if (fcwKeyText) {
              item.showFcwTips = fcwKeyText;
            }
          }
          // 消息平台
          if (item.content?.data?.content && (item.content.type == "msg-center" || item.content.type == "msg-report" || item.content.type == "msg-center-link")) {
            item.content.data.content = dealMsgCenterContent(item);
          }
          // 集中销售
          if (item.content && item.content.type == "imJZSell" && item.content.data?.jsonData) {
            try {
              let jsonData = deepClone(item.content.data.jsonData);
              // 分享人列表
              if (jsonData.shareList) {
                jsonData.shareListNum = jsonData.shareList.length;
                if (jsonData.shareList.length > 4) {
                  jsonData.shareList.length = 4;
                }
                // 标题
                jsonData.imHeaderTag = jsonData.imHeaderTag || jsonData.pillHeaderTag;
                if (jsonData.shareList[0] && jsonData.shareList[0].workerName) {
                  jsonData.imHeaderTag = jsonData.shareList[0].workerName.slice(0, 1) + "总点赞";
                }
                let lpList = [];
                let lpName = "";
                jsonData.cityName && lpList.push(jsonData.cityName);
                jsonData.placeName && lpList.push(jsonData.placeName);
                lpName = lpList.join("-");
                // 楼盘介绍
                jsonData.lpIntr = (lpName ? `(${lpName}) ` : "") + `${jsonData.room || 0}室${jsonData.hall || 0}厅 ${jsonData.buildArea || 0}㎡ ${jsonData.orientation || ""}`
              }
              // 视频和图片数目（最多4个）
              let jzSellListCount = 4;
              let jzSellListCountNum = 0;
              if (jsonData.videoUrls && jsonData.videoUrls.length > 0) {
                jzSellListCountNum += jsonData.videoUrls.length;
                // 兼容存在视频不存在视频封面
                if (!jsonData.videoCovers) {
                  jsonData.videoCovers = [];
                  jsonData.videoCovers.length = jsonData.videoUrls.length;
                }
                if (jsonData.videoUrls.length > 4) {
                  jsonData.videoUrls.length = 4;
                  jsonData.videoCovers.length = 4;
                }
                jzSellListCount -= jsonData.videoUrls.length;
              }
              if (jsonData.shareUrlList && jsonData.shareUrlList.length > 0) {
                jzSellListCountNum += jsonData.shareUrlList.length;
                if (jsonData.shareUrlList.length > jzSellListCount) {
                  jsonData.shareUrlList.length = jzSellListCount;
                }
              }
              // 最多4个联系人
              if (jsonData.contactList && jsonData.contactList.length > 4) {
                jsonData.contactList.length = 4;
              }
              jsonData.jzSellListCountNum = jzSellListCountNum;
              item.showContent = jsonData;
              // 倒计时
              if (jsonData.expireDate) {
                setJZSellTimer(item, true);
              }
            } catch (e) {}
          }
          // 新房集中销售
          if (item.content && item.content.type == "imNewHouseJZSell" && item.content.data?.jsonData) {
            try {
              let jsonData = deepClone(item.content.data.jsonData);
              // 位置
              let lpList = [];
              let lpName = "";
              jsonData.cityName && lpList.push(jsonData.cityName);
              jsonData.areaName && lpList.push(jsonData.areaName);
              lpName = lpList.join("-");
              // 面积
              let areaName = getContentArea(jsonData.minArea, jsonData.maxArea);
              // 房间数
              let roomName = getContentArea(jsonData.minRoom, jsonData.maxRoom);
              // 价格
              let priceName = getContentArea(jsonData.minTotalPrice, jsonData.maxTotalPrice);
              // 单价
              let unitName = getContentArea(jsonData.minAvgPrice, jsonData.maxAvgPrice);
              // 楼盘介绍
              jsonData.lpIntr = (lpName ? `(${lpName}) ` : "") + (areaName ? `${areaName}㎡ ` : "") + (roomName ? `${roomName}房 ` : "");
              // 价格介绍
              jsonData.priceIntr = (priceName ? `${priceName}万/套 ` : "") + (unitName ? `${unitName}元/㎡ ` : "");
              // 标签
              let imImageTag = [];
              if (jsonData.tags) {
                imImageTag = jsonData.tags.split(",");
              }
              if (jsonData.isPush) {
                imImageTag.unshift("集团主推");
              }
              jsonData.imImageTag = imImageTag;
              // 视频和图片数目（最多4个）
              let jzSellListCount = 4;
              let jzSellListCountNum = 0;
              if (jsonData.videoUrl?.length > 0) {
                jsonData.videoUrls = jsonData.videoUrl.split(",");
                jzSellListCountNum += jsonData.videoUrls.length;
                // 兼容存在视频不存在视频封面
                if (!jsonData.videoCovers) {
                  jsonData.videoCovers = [];
                  jsonData.videoCovers.length = jsonData.videoUrls.length;
                } else {
                  jsonData.videoCovers = jsonData.videoCovers.split(",");
                }
                if (jsonData.videoUrls.length > 4) {
                  jsonData.videoUrls.length = 4;
                  jsonData.videoCovers.length = 4;
                }
                jzSellListCount -= jsonData.videoUrls.length;
              }
              if (jsonData.pushUrl?.length > 0) {
                jsonData.shareUrlList = jsonData.pushUrl.split(",");
                jzSellListCountNum += jsonData.shareUrlList.length;
                if (jsonData.shareUrlList.length > jzSellListCount) {
                  jsonData.shareUrlList.length = jzSellListCount;
                }
              }
              // 最多4个联系人
              if (jsonData.lxrArray && jsonData.lxrArray.length > 4) {
                jsonData.lxrArray.length = 4;
              }
              jsonData.jzSellListCountNum = jzSellListCountNum;
              item.showContent = jsonData;
            } catch (e) {}
          }
          // 批量房源-老
          if (item.content && item.content.type == "imSomeHouseRecommend" && item.content.data) {
            try {
              let jsonData = deepClone(item.content.data);
              if (!jsonData.houseList) {
                // 兼容历史版本不存在houseList字段
                jsonData.houseList = [{batchShareId: jsonData.batchShareId, buildingArea: jsonData.buildingArea, cityName: jsonData.cityName, content: jsonData.content, fhId: jsonData.fhId, fhIds: jsonData.fhIds, hall: jsonData.hall, imgUrl: jsonData.imgUrl, lpName: jsonData.lpName, name: jsonData.name, orientation: jsonData.orientation, phone: jsonData.phone, placeName: jsonData.placeName, room: jsonData.room, tags: jsonData.tags, title: jsonData.title, workerId: jsonData.workerId,}];
              } else {
                jsonData.houseListLength = jsonData.houseList.length;
              }
              for (let i = 0; i < jsonData.houseList.length; i++) {
                let jsonDataItem = jsonData.houseList[i];
                let lpList = [];
                let lpName = "";
                jsonDataItem.cityName && lpList.push(jsonData.cityName);
                jsonDataItem.placeName && lpList.push(jsonData.placeName);
                lpName = lpList.join("-");
                // 楼盘介绍
                jsonDataItem.lpIntr = (lpName ? `(${lpName}) ` : "") + `${jsonDataItem.room || 0}室${jsonDataItem.hall || 0}厅 ${jsonDataItem.buildingArea || 0}㎡ ${jsonDataItem.orientation || ""}`
                // 标签列表-去除为空的数据
                let tagList = (jsonDataItem.tags || "").split("@");
                for (let j = 0; j < tagList.length; j++) {
                  if (!tagList[j]) {
                    tagList.splice(j, 1);
                    j--;
                  }
                }
                jsonDataItem.tagList = tagList;
              }
              if (jsonData.houseList.length > 2) {
                jsonData.houseList.length = 2;
              }
              item.showContent = jsonData;
            } catch (e) {}
          }
          // 批量房源-新
          if (item.content && item.content.type == "imHouseBatchShare" && item.content.data) {
            try {
              let jsonData = deepClone(item.content.data);
              jsonData.title = `[${jsonData.houseCount}套]${jsonData.title}`;
              if (jsonData.houseList.length > 6) {
                jsonData.houseList.length = 6;
              }
              jsonData.url = `${config[config.env].jjsHome}/lyj-front/lbgapp/lousingRecommendation.html?shardId=${jsonData.batchShareId}&workerId=${jsonData.workerId}&openType=1`;
              item.showContent = jsonData;
            } catch (e) {}
          }
          // 审批卡片
          if (item.content && item.content.type == "card" && item.content.data && item.content.data.listInfo) {
            item.content.data.listInfo.map(item1 => {
              if (item1.diyType == 1 && (item1.title || "").length > 4) {
                item.content.data.titleShowMax = true;
              }
            });
          }
          // 时间节点2分钟间隔显示时间标签
          if (item.time - msgTime > 2 * 60 * 1000) {
            item.timeTag = true;
            msgTime = item.time;
          }
          currMsg.push(item);
        } catch (e) {
          console.log(`消息解析错误${JSON.stringify(item)}`, e);
        }
      }
      // 判断是否存在正在see消息
      if (props.msgType == 1) {
        store.getters.getSseMsgList({id: currSessionInfo.value.id, msgList: currMsg});
      }
      currMsgList.value = currMsg;
      debounce({
        timerName: "setAiMsgCard" + props.msgType,
        time: 100,
        fnName: function () {
          setAiMsgCard();
        }
      });
      getMsgReadStatus();
      // 消息记录滚动到底部
      if (props.scrollElm) {
        if (!props.notScroll && pos != "msg") {
          await nextTick(() => {
            setTimeout(() => {
              props.scrollElm.scrollTop = 9999999;
            }, 100)
          });
        }
      } else if (props.msgType != 1) {
        await nextTick(() => {
          setTimeout(() => {
            if (props.msgType == 1) {
              store.commit("setEmit", {type: "scroll", value: pos});
            }
          }, 100);
        });
      }
      getVisibleAreaMsg(props.msgType);
    }

    // 获取消息已读状态
    function getMsgReadStatus(sessionInfo) {
      // 只有主会话消息和1v1会话和非自己的会话标记已读
      if (props.msgType != 1 || currSessionInfo.value.scene != "p2p" || currSessionInfo.value.to == userInfo.workerNo) {
        return;
      }
      // 会话消息显示已读
      let showReadIndex = -1;
      let msgReceiptTime = sessionInfo?.msgReceiptTime || currSessionInfo.value.msgReceiptTime;
      // 自己在遍历中的上一条消息下标，用于补偿云信msgReceiptTime不存在和异常的场景
      let selfPreIndex = -1;
      for (let i = 0; i < currMsgList.value.length; i++) {
        let item = currMsgList.value[i];
        // 移除之前标记的消息已读
        if (item.showRead) {
          delete item.showRead;
        }
        // 提示、通知、抖动消息不标记已读
        if (item.type != "tip" && item.type != "notification") {
          // 抖动消息不标记
          if (item.type == "custom" && item.content && item.content.type == "shake") {
            continue;
          }
          if (item.status == "success") {
            if (item.from == userInfo.workerNo) {
              // 会话已读时间大于当前消息的已读消息
              if (msgReceiptTime >= item.time) {
                showReadIndex = i;
              }
              selfPreIndex = i;
            } else {
              showReadIndex = selfPreIndex;
            }
          }
        }
      }
      // 标记自己消息已读标记
      for (let i = showReadIndex; i >= 0; i--) {
        if (currMsgList.value[i].from == userInfo.workerNo) {
          currMsgList.value[i].showRead = true;
          break;
        }
      }
    }

    // 获取可视区域消息
    function getVisibleAreaMsg(currentMsgType) {
      if (currentMsgType == props.msgType) {
        debounce({
          timerName: "getVisibleAreaMsg" + props.msgType,
          time: 100,
          fnName: function () {
            if (msgUlRef.value) {
              let scrollBox = getParents(msgUlRef.value, "msg-content-box");
              if (props.msgType == 2) {
                scrollBox = getParents(msgUlRef.value, "msg-quote-detail-box");
              } else if (props.msgType == 3) {
                scrollBox = getParents(msgUlRef.value, "search-msg-content");
              } else if (props.msgType == 6) {
                scrollBox = getParents(msgUlRef.value, "collect-details-box");
              }
              for (let i = 0; i < currMsgList.value.length; i++) {
                let currLiElm = msgUlRef.value.querySelector(".msg-li-index-" + i);
                if (currLiElm && scrollBox) {
                  let scrollBoxOffset = getOffset(scrollBox);
                  let currLiOffset = getOffset(currLiElm);
                  if (currLiOffset.top + currLiElm.offsetHeight - scrollBoxOffset.top > scrollBox.scrollTop) {
                    createQrCodeItem(currMsgList.value[i]);
                  }
                }
              }
            }
          }
        });
      }
    }

    // 获取显示内容范围
    function getContentArea(min, max) {
      let list = [];
      max && list.push(max);
      (min || list.length > 0) && min != max && list.unshift(min || 0);
      return list.join("-");
    }

    // 获取聚焦元素
    function getFocusMsg() {
      return store.getters.getFocusMsg;
    }

    // 获取文档标题（高亮）
    function getDocTitle(item) {
      let docObj = item.content.document;
      let replaceIndex = 0;
      if (docObj.docNames && typeof docObj.docNames == "string") {
        try {
          docObj.docNames = JSON.parse(docObj.docNames);
        } catch (e) {
        }
      }
      let docTitle = htmlEscapeAll(docObj.title || "");
      docTitle = docTitle.replace(/\$docPos/g, function () {
        let itemStr = `<span class="doc-highlight">【${htmlEscapeAll(docObj.docNames[replaceIndex])}】</span>`;
        replaceIndex++;
        return itemStr;
      })
      return docTitle;
    }

    // 获取用户信息
    function getPerson(account) {
      return remote.store.getters.getPersons(account);
    }

    // 显示用户详情弹窗
    function showUserInfo(account, e) {
      // 问答助手不支持私聊
      if (props.msgType != 5 && currSessionInfo.value.detailInfo?.serverCustom?.ai1 != account) {
        store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: account, selUserElm: e}});
      }
    }

    // 加载图片
    async function loadImage(e, item, parentItem) {
      let thisElm = e.target;
      if (!/\/img\/(image_reload|image_not_found|image_default)/.test(thisElm.src) && item.file.isImage && thisElm.naturalWidth * thisElm.naturalHeight > 10000 * 10000) {
        item.file.isImage = false;
        item.file.w = thisElm.naturalWidth;
        item.file.h = thisElm.naturalHeight;
      }
      let thisElmSrc = thisElm.dataset.src;
      // 当前图片是base64不继续加载
      if (/data:image\//.test(thisElm.src) || /file:\/\/\//.test(thisElm.src)) {
        return;
      }
      // 当前图片发送是是base64不继续加载 或 发送文件类型图片
      if (!/\/img\/(image_reload|image_not_found)/.test(thisElm.src) &&
        (/data:image\//.test(thisElmSrc) || (item.type == "file" && item.type != "success" && thisElmSrc != thisElm.src && !/\/img\/image_default/.test(thisElm.src)))
      ) {
        thisElm.setAttribute("src", thisElmSrc);
        return;
      }
      let sessionId = currSessionInfo.value.id;
      let thisMD5 = item.file.fileMD5 || "";
      let idServer = item.idServer || (parentItem ? parentItem.idServer : "");
      let idClient = item.idClient || (parentItem ? parentItem.idClient : "");
      let timerKey = sessionId + "-" + idClient + "-" + thisMD5;

      // 判断是否存在本地图片缓存
      let localSrc = item.file.fileInfo ? item.file.fileInfo.path + item.file.fileInfo.name : "";
      let localThumSrc = item.file.fileInfo ? item.file.fileInfo.path + "thum\\" + item.file.fileInfo.name : "";
      let isExistImage = fs.existsSync(localSrc);
      if (isExistImage && fs.existsSync(localThumSrc)) {
        localSrc = localThumSrc;
      }

      // 存在本地图片加载本地图片
      if (localSrc && isExistImage && new RegExp(location.origin + "/img/image_default.png").test(thisElm.src)) {
        thisElm.setAttribute("src", localSrc);
        // 图片加载定时器
        let timer = store.getters.getImageLoadTimer[timerKey];
        if (timer) {
          store.commit("setImageLoadTimer", {type: "del", key: timerKey});
        }
      } else {
        if (localSrc && !isExistImage) {
          // 不存在删除缓存
          fileDB.del("md5", item.file.fileMD5);
        }
        // 加载图片
        if (idServer || idClient) {
          store.dispatch("setReloadImage", {thisElm: thisElm, thisElmSrc: thisElmSrc, timerKey: timerKey, time: 30 * 1000, type: 1, notChange: item.file.ext == "ico"});
        }
      }

      loadDefaultImage();
    }

    // 图片加载失败
    function errorImage(item, parentItem, e) {
      let thisElm = e.target;
      // 当前图片是base64不继续加载
      if (/data:image\//.test(thisElm.src)) {
        return;
      }
      let thisElmSrc = thisElm.dataset.src;
      let localSrc = thisElm.dataset.local;
      let sessionId = currSessionInfo.value.id;
      let thisMD5 = item.file.fileMD5 || "";
      let idServer = item.idServer || (parentItem ? parentItem.idServer : item.time);
      let idClient = item.idClient || (parentItem ? parentItem.idClient : "");
      let timerKey = sessionId + "-" + idClient + "-" + thisMD5;

      // 压缩失败删除文件
      if (localSrc) {
        fs.stat(localSrc, function (error, info) {
          if (info && info.size == 0) {
            fs.unlinkSync(localSrc);
          } else {
            // 判断是否heic图片，是则转为jpg
            heicToJpg({localSrc: localSrc});
          }
        });
      }

      // 加载失败如果存在定时器继续加载
      if (store.getters.getImageLoadTimer[timerKey]) {
        thisElm.setAttribute("pre-src", thisElm.src);
        thisElm.setAttribute("pre-time", Date.now());
        thisElm.setAttribute("src", `/img/image_default.png?reloadFlag=true`);
      } else {
        // 是缓存图片且本地存在
        if (/\/cached\/.*?\/images\//.test(thisElm.src) && fs.existsSync(localSrc)) {
          let thumReg = /\/thum\//;
          if (thumReg.test(thisElm.src)) {
            // 压缩图片出问题加载原始图片
            thisElm.setAttribute("src", thisElm.src.replace(thumReg, "\/"));
          } else {
            // 原图有问题则删除原图
            fileDB.del("md5", item.file.fileMD5);
            delete item.file.fileInfo;
            // 停止加载图片
            thisElm.setAttribute("src", `/img/image_reload_min.png`);
          }
        } else {
          // 停止加载图片
          thisElm.setAttribute("src", `/img/image_reload_min.png`);
        }
      }

      // 滚动位置
      loadDefaultImage();
    }

    // 加载消息平台图片
    function loadDefaultImage() {
      // 滚动位置
      if (isScrollBottom.value && props.msgType == 1) {
        nextTick(() => {
          store.commit("setEmit", {type: "scroll", value: "bottom"});
        });
      }
    }

    // 点击图片
    function clickImage(e) {
      let thisElm = e.target;
      if (thisElm.src == location.origin + "/img/image_reload_min.png") {
        // 重新加载失败图片
        thisElm.setAttribute("src", "/img/image_default.png?reload=true");
      } else {
        selElm(e.target);
      }
    }

    // 获取url跳转地址-item1是item类型的多个跳转地址
    function getLinkUrl(item, link, frameName, item1) {
      let urlJson = {};
      let param = {}
      if (link) {
        // 打开链接
        urlJson.frameName = frameName || "jjsHome";
        urlJson.url = link;
      } else if (item) {
        if (item.content && item.content.data) {
          // 卡片消息
          param.type = item.content.type;
          param.shareNo = item.from;
          param.content = {
            url: item.content.data.url,
            other: item1 || item.content.data.other || item.content.data.jsonData || item.content.data || {},
            sourceType: item.content.data.sourceType,
            sourceUrl: item.content.data.url,
            id: item.content.data.id,
            isOuterOpen: item.content.data.isOuterOpen,
          }
        } else if (item.type == "geo") {
          // 地理位置
          param.type = item.type;
          param.geo = item.geo;
        }
        // 存在类型
        if (param.type) {
          urlJson = store.getters.getLinkUrlJson(param);
        }
      }
      if (urlJson.param) {
        if (props.msgType == 5) {
          // 合并转发页面打开合并转发
          store.commit("setEmit", {type: "loadForwardMsg", value: urlJson.param.path});
        } else {
          // 打开合并转发窗口
          emitMsg("msg", urlJson.param);
        }
      } else {
        store.dispatch("setOpenWindow", [linkFormat(urlJson.url), urlJson.frameName]);
      }
    }

    // 获取通知消息内容
    function getNotification(item) {
      return store.getters.getNotification(item);
    }

    // 取消发送文件
    function cancelSendFile(item) {
      let downloadMD5 = item.file.downloadMD5 || item.uniqueSign;
      try {
        remote.store.getters.getNimFileUpload(downloadMD5).abort();
      } catch (e) {}
      remote.store.commit("setNimFileUpload", {key: downloadMD5, value: "del"});
      delete item.file.downloadMD5;
      delete item.file.progress;
      delete item.uniqueSign;
      item.updateKey = (item.updateKey || 0) + 1;
    }

    // 打开/下载文件，openType-1重新下载
    function openFile(item, openType) {
      store.dispatch("setMsgsLocal", {item: item}).then(res => {
        // 视频点击不重新下载
        if (item.type == "video" && item.file.progress) {
          return;
        }
        store.dispatch("openFile", {item: item, openType: openType});
      });
    }

    // 打开文件夹/另存为，openType-1另存为
    function openFolder(item, openType) {
      store.dispatch("openFolder", {item: item, openType: openType});
    }

    // 获取消息是否显示菜单
    function getShowMenu(item) {
      return !(router.currentRoute.value.path == "/child/searchMsg" || item.antispamMsg || item.forbidMsg || parentIsMultiple.value || item.isXLTyping) & (props.msgType == 1 || props.msgType == 3);
    }

    // 右键菜单-type-1消息列表(不显示复制)-2消息内容-3图片元素(图片元素需要对应图片文件消息fileItem)-4头像@人
    function setMenu(e, item, type, fileItem, notShowDefault) {
      // 反垃圾消息不触发右键、多选状态
      if (!getShowMenu(item) && props.msgType != 4) {
        e.preventDefault();
        e.stopPropagation();
        return false;
      }
      console.log("setMenu", item.sessionId, item.idServer, type);
      // 设置时间差
      store.dispatch("setDiffTime");
      let selection = window.getSelection();

      let menuObj = {
        menu: {},// 菜单对象
        menuList: [],// 菜单列表
        popupItem: {},// 菜单弹出对象
        menuIndex: 0,// 菜单列表下标
        collectIndex: 0,// 收藏菜单下标
        storyIndex: 0,//话术菜单下标
        e: e,
        posObj: {},// 菜单显示位置信息
      };
      let collectMap = store.getters.getCollectMap;

      // 获取选中的消息数据
      let node = document.createElement("div");
      if (selection && selection.rangeCount > 0) {
        node.appendChild(window.getSelection().getRangeAt(0).cloneContents());
      }
      let msgInfo = node.querySelectorAll(".msg-info");

      // 是否多选
      let isMoreSel = msgInfo.length > 1;
      // 是否抖动消息
      let isShake = item.type == "custom" && item.content && item.content.type == "shake";

      // 转文字
      if (!isMoreSel && item.type == "audio") {
        if (!notShowDefault) {
          menuObj.menuList.push({
            label: "转文字", click: function () {
              setMenuClick(e, "audio", item);
            }
          });
          menuObj.menuIndex++;
        }
      }

      // 复制
      if (type == 2 || type == 3) {
        // 选择图片
        if (type == 3) {
          selElm(e.target);
        }
        if (item.file && item.file.isImage && item.file.size > 5 * 1024 * 1024) {
          selection.removeAllRanges();
        }
        // 没有选中范围点击选中当前元素
        if (window.getComputedStyle(e.target)["user-select"] == "none" || !/range/i.test(selection.type)) {
          selElm(getParents(e.target, "msg-content") || e.target);
        }
        // 有选中范围和非禁止复制区域
        if (/range/i.test(selection.type) && !/selNone/.test(selection.focusNode.className) && item.type != "audio" && item.type != "video") {
          menuObj.menuList.push({
            label: "复制", click: function () {
              document.execCommand("copy");
            }
          });
          menuObj.menuIndex++;
        }
      } else {
        // 清除选中区域
        selection.removeAllRanges();
      }
      // 文字类型引用、图文、文件(正在发送/失败的消息不能引用) 非客户咨询 非服务号和公众号 非乐乐表情
      if (isShowMsgOperate(1, item)) {
        // 引用
        if (!notShowDefault) {
          menuObj.menuList.push({
            label: "引用", click: function () {
              setMenuClick(e, "quote", item, msgInfo);
            }
          });
          menuObj.menuIndex++;
        }
      }

      // 多选
      if (isShowMsgOperate(3, item)) {
        if (!notShowDefault) {
          menuObj.menuList.push({
            label: "多选", click: function () {
              setMenuClick(e, "multi", item, msgInfo);
            }
          });
          menuObj.menuIndex++;
        }
      }

      if (!isMoreSel && type == 4 && currSessionInfo.value.scene != "p2p") {
        // 判断是群成员才能被@
        store.dispatch("getTeamMembers", {id: currSessionInfo.value.to}).then(res => {
          let thisTeamMember = res.obj;
          if (thisTeamMember && thisTeamMember.length > 0 && thisTeamMember.findIndex(i => i.account == item.from) > -1) {
            menuObj.popupItem = addMenu(menuObj.menu, 0, {
              label: "@TA", click: function () {
                let thisUserInfo = getPerson(item.from);
                let haitName = `@${thisUserInfo.empType == 2 ? thisUserInfo.userShowName || thisUserInfo.name : thisUserInfo.name} `;
                store.getters.getImEditor.insertHaitImage(thisUserInfo.workerNo, haitName, `${strToImg(haitName)}`, currSessionInfo.value.id);
              }
            }).popup(menuObj.posObj.x, menuObj.posObj.y, "", menuObj.posObj.w, menuObj.posObj.h);
          }
        });
      }

      // 文件打开文件/文件夹、下载/另存为
      if (!isMoreSel && (item.type == "file" || item.type == "video") && item.file && type != 4) {
        let fileType = item.type == "video" ? 1 : "";
        let fileInfo = fileType == 1 ? item.file.videoInfo : item.file.fileInfo;
        if (fileInfo && fileInfo.path) {
          menuObj.menuList.push({
            label: "打开", click: function () {
              openFile(item);
            }
          });
          menuObj.menuIndex++;
          menuObj.menuList.push({
            label: "打开文件夹", click: function () {
              openFolder(item);
            }
          });
          menuObj.menuIndex++;
          menuObj.menuList.push({
            label: "另存为", click: function () {
              openFolder(item, 1);
            }
          });
          menuObj.menuIndex++;
        } else {
          if (item.status == "success" && !item.file?.progress) {
            menuObj.menuList.push({
              label: "下载", click: function () {
                openFile(item);
              }
            });
            menuObj.menuIndex++;
            menuObj.menuList.push({
              label: "另存为", click: function () {
                openFolder(item, 1);
              }
            });
            menuObj.menuIndex++;
          }
        }
      }

      // 图片另存为
      if (!isMoreSel && type == 3 && item.status == "success") {
        menuObj.menuList.push({
          label: "另存为", click: function () {
            openFolder(fileItem, 1);
          }
        });
        menuObj.menuIndex++;
      }

      // 非消息记录
      if (!isMoreSel && (props.msgType == 1 || props.msgType == 3)) {
        if (item.status == "success") {
          // 5分钟内消息显示撤回。提示、抖动通知消息禁止撤回
          if (item.from == userInfo.workerNo && new Date().getTime() + store.getters.getDiffTime - item.time < 5 * 60 * 1000 &&
            (item.type != "tip" && !isShake && item.type != "notification")) {
            menuObj.menuList.push({
              label: "撤回消息", click: function () {
                if (item.from == item.to && item.from == userInfo.workerNo) {
                  toast({title: "只能删除自己发送给别人的, 并且发送成功的非本地消息", type: 2});
                  return;
                }
                nim.deleteMsg(deepClone(item)).then(res => {
                  let {err} = res;
                  if (err) {
                    if (err.code === 508) {
                      toast({title: "发送时间超过5分钟的消息，不能被撤回。", type: 2});
                    } else {
                      toast({title: err.message || "操作失败", type: 2});
                    }
                    return;
                  }
                  deleteMessageApi({
                    msgBody: JSON.stringify({
                      msgIdServer: item.idServer
                    })
                  })
                });
              }
            });
            menuObj.menuIndex++;
          }

          // 转发
          if (isShowMsgOperate(2, item)) {
            if (!notShowDefault) {
              menuObj.menuList.push({
                label: "转发", click: function () {
                  setMenuClick(e, "forward", item);
                }
              });
              menuObj.menuIndex++;
            }
          }
          // 群通知、提醒、乐文档、日程、卡片通知，不允许收藏
          if (item.type != "notification" && item.type != "tip" && !(item.content && item.content.cardType == 3) && !isShake &&
            !(item.content && (item.content.type == "schedule_invite" || item.content.type == "schedule_remind" || item.content.type == "document_remind"))) {
            // 收藏
            menuObj.menuList.push({
              label: "收藏",
              submenu: showMenu(getSubMenu(1, collectMap.list, item, menuObj))
            });
            menuObj.collectIndex = menuObj.menuIndex;
            menuObj.menuIndex++;
            // 一分钟缓存一次
            if (Date.now() + store.getters.getDiffTime - collectMap.time > 60 * 1000) {
              store.dispatch("getCollectClassify").then(res => {
                if (res.success) {
                  store.commit("setCollectMap", {type: "collect", list: res.data.data});
                  // 菜单显示状态才显示
                  if (global.textmenuShow) {
                    menuObj.popupItem = editMenu(menuObj.menu, menuObj.collectIndex, {
                      label: "收藏",
                      submenu: showMenu(getSubMenu(1, res.data.data, item, menuObj))
                    }).popup(menuObj.posObj.x, menuObj.posObj.y, "", menuObj.posObj.w, menuObj.posObj.h);
                  }
                }
              });
            }
          }
          // 文字、图片、图文才支持收藏到话术
          if ((item.type == "text" || item.type == "image" || (item.type == "custom" && item.content && item.content.type == "multi")) && store.getters.getCollectStoryPur) {
            let isTextOrImage = true;
            if (item.type == "custom") {
              // 只要图文才能收藏
              item.content.msgs.map(item1 => {
                if (item1.type != "text" && item1.type != "image") {
                  isTextOrImage = false;
                }
              });
            }
            // 收藏话术
            if (isTextOrImage) {
              menuObj.menuList.push({
                label: "收藏到话术",
                submenu: showMenu(getSubMenu(2, collectMap.storyList, item, menuObj))
              });
              menuObj.storyIndex = menuObj.menuIndex;
              menuObj.menuIndex++;
              // 一分钟缓存一次
              if (Date.now() + store.getters.getDiffTime - collectMap.storyTime > 60 * 1000) {
                store.dispatch("getCollectStoryClassify", 2).then(res => {
                  if (res.success) {
                    store.commit("setCollectMap", {type: "story", list: res.data.data});
                    // 菜单显示状态才显示
                    if (global.textmenuShow) {
                      menuObj.popupItem = editMenu(menuObj.menu, menuObj.storyIndex, {
                        label: "收藏到话术",
                        submenu: showMenu(getSubMenu(2, res.data.data, item, menuObj))
                      }).popup(menuObj.posObj.x, menuObj.posObj.y, "", menuObj.posObj.w, menuObj.posObj.h);
                    }
                  }
                });
              }
            }
          }
        }

        // 有选中范围和非禁止复制区域-搜索话术
        if (/range/i.test(selection.type) && !/selNone/.test(selection.focusNode.className) && isFcw() && store.getters.getCollectStoryPur) {
          // 获取收藏话术权限
          menuObj.menuList.push({
            label: "搜索话术", click: function () {
              let node = document.createElement("div");
              node.appendChild(window.getSelection().getRangeAt(0).cloneContents());
              let text = node.innerText;
              store.commit("setEmit", {type: "showComponents", value: {type: 1, key: 1, text: text, sessionInfo: currSessionInfo.value}});
            }
          });
          menuObj.menuIndex++;
        }

        // 删除-非服务号和订阅号
        if (props.msgType == 1 && !isSubOrSer(currSessionInfo.value.id)) {
          menuObj.menuList.push({
            label: "删除", click: function () {
              deleteMsg(item);
            }
          });
          menuObj.menuIndex++;
        }
      }

      if (/range/i.test(selection.type) && !/selNone/.test(selection.focusNode.className)) {
        let node = getRangContent(2);
        if (!isMoreSel) {
          let text = node.innerText;
          // 提醒
          if (text.length > 0 && item.type != "audio") {
            menuObj.menuList.push({
              label: "提醒", click: function () {
                remote.store.dispatch("toScheduleModal", text);
              }
            });
            menuObj.menuIndex++;
          }
        }
        if ((item.type != "file" || (item.type == "file" && item.file.isImage && item.file.size < 5 * 1024 * 1024)) && item.type != "audio" && item.type != "video") {
          // 转需求问题
          menuObj.menuList.push({
            label: "bug/好点子", click: function () {
              if (msgInfo.length > 30) {
                toast({title: "最多选择30条消息", type: 2});
                return;
              }
              let bugList = [];
              if (msgInfo.length > 0) {
                // 存在多选获取多选消息列表
                for (let i = 0; i < msgInfo.length; i++) {
                  if (msgInfo[i].dataset.idserver) {
                    let thisMsg = currMsgList.value.find(item1 => item1.idServer == msgInfo[i].dataset.idserver);
                    bugList.push(thisMsg);
                  }
                }
              } else {
                let msgInfoParent = getParents(e.target, "msg-info");
                // 合并转发选中部分也获取具体内容转bug
                if (msgInfoParent) {
                  let thisMsg = currMsgList.value.find(item1 => item1.idServer == msgInfoParent.dataset.idserver);
                  if (thisMsg?.content?.type == 9 && thisMsg?.content?.data?.url) {
                    bugList.push(thisMsg);
                  }
                }
              }
              if (bugList.length > 0) {
                multiBugReport(bugList);
              } else {
                store.dispatch("setOpenWindow", [`${config[config.env].jjsHome}/lyj-front/tapd-manage/?hideSideBar=true#/createOrder?source=11&orderFeedbackContent=${encodeURIComponent(node.outerHTML)}`, "jjsHome", "demandOrBug"]);
              }
            }
          });
          menuObj.menuIndex++;
        }
      }

      // 图片加入识别二维码功能
      if (!isMoreSel && type == 3) {
        store.dispatch("getQrcode", e.target).then(res => {
          if (res && res.data) {
            menuObj.popupItem = addMenu(menuObj.menu, 0, {
              label: "识别二维码", click: async function () {
                console.log("qrcode:", res.data);
                res.alert = alert;
                res.loading = loading;
                store.dispatch("setQrcode", res);
              }
            }).popup(menuObj.posObj.x, menuObj.posObj.y, "", menuObj.posObj.w, menuObj.posObj.h);
            menuObj.collectIndex++;
            menuObj.storyIndex++;
          }
        });
      }

      if (menuObj.menuList.length > 0) {
        menuObj.menu = showMenu(menuObj.menuList)
        let elmBounds = e.currentTarget.getBoundingClientRect();
        let x = e.x;
        let y = e.y;
        let w = 0;
        let h = 0;
        if (notShowDefault) {
          x = elmBounds.left + elmBounds.width - 4;
          y = elmBounds.top - 4;
          w = elmBounds.width;
          h = elmBounds.height;
        }
        menuObj.posObj = {x, y, w, h};
        menuObj.popupItem = menuObj.menu.popup(menuObj.posObj.x, menuObj.posObj.y, "", menuObj.posObj.w, menuObj.posObj.h);
      }
      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    // 菜单右键点击
    function setMenuClick(e, type, item, msgInfo = []) {
      let selection = window.getSelection();
      switch (type) {
        // 引用
        case "quote":
          remote.store.commit("setQuoteMsg", {id: currSessionInfo.value.id, msg: getQuoteMsg(item)});
          store.dispatch("activeImEditor", {id: currSessionInfo.value.id, active: true});
          break;
        // 转发
        case "forward":
          openForward(item);
          break;
        // 多选
        case "multi":
          if (isScrollBottom.value) {
            store.commit("setEmit", {type: "scroll", value: "bottom"});
          }
          multipleSelMap.value = {};
          let thisSelMap = {};
          try {
            if (msgInfo.length == 0) {
              // 没有选择范围时选中当前消息对象
              let msgInfoParent = getParents(e.target, "msg-info");
              if (msgInfoParent) {
                let thisMsg = currMsgList.value.find(item1 => item1.idServer == msgInfoParent.dataset.idserver);
                if (isShowMsgOperate(4, thisMsg)) {
                  thisSelMap[msgInfoParent.dataset.idserver] = thisMsg;
                }
              }
            } else {
              for (let i = 0; i < msgInfo.length; i++) {
                if (msgInfo[i].dataset.idserver) {
                  let thisMsg = currMsgList.value.find(item1 => item1.idServer == msgInfo[i].dataset.idserver);
                  if (isShowMsgOperate(4, thisMsg)) {
                    thisSelMap[msgInfo[i].dataset.idserver] = thisMsg;
                  }
                }
              }
            }
          } catch (e) {
          }
          if (Object.values(thisSelMap).length > 30) {
            alert({content: "最多选择30条消息", showCancel: false});
            return;
          }
          if (Object.values(thisSelMap).length == 0) {
            changeMultipleSel(item);
          } else {
            multipleSelMap.value = thisSelMap;
          }
          props.changeMultiple(true);
          // 更新转发指定人权限
          store.dispatch("getButtonObj");
          // 获取转发至特定人群
          store.dispatch("getForwardGroupType");
          break;
        // 语音转文字
        case "audio":
          store.commit("setEmit", {type: "scroll", value: "msg"});
          item.audioTextStatus = 1;
          selection.removeAllRanges();
          setAudioTips(item);
          // 获取语音转文字
          store.dispatch("setSpeechToText", {
            url: item.file.url.split("?")[0] + "?audioTrans&type=mp3",
            idServer: item.idServer,
            done: res => {
              item.audioTextStatus = 2;
              if (updateMsg(item)) {
                item = updateMsg(item);
              }
              item.audioTextStatus = 2;
              if (res.success) {
                item.audioText = res.resultText;
              } else {
                toast({title: "转文字失败。" + res.errorMsg, type: 2});
              }
              store.commit("setEmit", {type: "scroll", value: "msg"});
              audioMap[item.idServer] = item;
              updateMsg(item, true);
            }
          });
          break;
        // 显示更多菜单
        case "more":
          showOperateId.value = item.idServer;
          selection.removeAllRanges();
          setMenu(e, item, 2, "", true);
          break;
      }
      showTipsBox();
    }

    // 是否可以可以操作type:1引用2转发3多选4多选点击
    function isShowMsgOperate(type, item) {
      let flag = false;
      switch (type) {
        case 1:
          // 引用
          // 文字类型引用、图文、文件(正在发送/失败的消息不能引用) 非客户咨询 非服务号和公众号 非乐乐表情
          if (!parentIsMultiple.value && !isFcw() && item.status == "success" && !isSubOrSer(currSessionInfo.value.id) && /text|file|custom|image/.test(item.type) &&
            item.content?.type != 3 && props.msgType != 5 && props.msgType != 6 && item.content?.type != "shake") {
            flag = true;
          }
          break;
        case 2:
          // 转发
          if (item.type != "tip" && item.type != "notification" && item.status == "success" && item.content?.cardType != 3 && item.content?.type != "shake") {
            flag = true;
          }
          break;
        case 3:
          // 多选
          if (props.msgType == 1 && !isSubOrSer(currSessionInfo.value.id)) {
            flag = true;
          }
          break;
        case 4:
          // 多选点击
          // 反垃圾、禁止、提示、抖动、通知禁止多选
          if (!(item.antispamMsg || item.forbidMsg || item.type == "tip" || item.content?.type == "shake" || item.type == "notification" || item.status != "success")) {
            flag = true;
          }
          break;
      }
      return flag;
    }

    // 获取二级菜单内容 type-1收藏-2收藏到话术
    function getSubMenu(type, list, item, menuObj) {
      let showList = [];
      try {
        if (type == 1) {
          // 收藏
          showList = list.map(item1 => {
            return {
              label: item1.name, click: function () {
                collectMsg(item, item1.id).then(res => {
                  if (res.success) {
                    toast({title: "收藏成功！", type: 1});
                  } else {
                    toast({title: "收藏失败。" + res.errorMsg, type: 2});
                  }
                });
              }
            }
          });
        } else if (type == 2) {
          // 收藏到话术
          showList = list.map(item1 => {
            return {
              label: item1.gname, click: function () {
                let collectStoryContent = [];
                let thisItem = deepClone(item);
                if (item.type == "text") {
                  // 文字
                  collectStoryContent.push({type: "text", text: item.text});
                } else if (item.type == "image") {
                  // 图片
                  dealMsgField(thisItem);
                  collectStoryContent.push({type: "image", file: thisItem.file});
                } else {
                  // 图文
                  for (let i = 0; i < item.content.msgs.length; i++) {
                    thisItem = deepClone(item.content.msgs[i]);
                    dealMsgField(thisItem);
                    collectStoryContent.push(thisItem);
                  }
                }
                store.dispatch("setCollectStoryContent", {parentId: item1.gid, content: collectStoryContent}).then(res => {
                  if (res.success) {
                    toast({title: "收藏成功！", type: 1});
                  } else {
                    toast({title: "收藏失败。" + res.errorMsg, type: 2});
                  }
                });
              }
            }
          });
        }
        if (!menuObj.addType) {
          showList.push({
            label: "+创建分组", notHide: true, click: function (clickItem, clickSubmenuObj) {
              store.commit("setEmit", {
                type: "collectAddClassifyDialog",
                value: {
                  type: 2,
                  key: type,
                  done: res => {
                    if (res.success) {
                      let menuList = [];
                      if (type == 1) {
                        // 创建收藏分组
                        menuList = showMenu(getSubMenu(1, [res.item], item, {...menuObj, addType: 1}));
                      } else if (type == 2) {
                        // 创建话术分组
                        menuList = showMenu(getSubMenu(2, [res.item], item, {...menuObj, addType: 1}));
                      }
                      if (menuList?.list?.[0]) {
                        clickSubmenuObj.list.splice(clickSubmenuObj.list.length - 1, 0, menuList.list[0]);
                      }
                      nextTick(() => {
                        menuObj.popupItem.calcWinWH(2);
                      });
                    }
                  }
                }
              });
            }
          });
        }
      } catch (e) {
        console.log("getSubMenuErr", e);
      }
      return showList;
    }

    // 收藏消息
    function collectMsg(msg, groupId) {
      return store.dispatch("collectMsg", {
        sessionInfo: currSessionInfo.value,
        sourceName: currSessionInfo.value.detailInfo.name,
        sourceIcon: currSessionInfo.value.detailInfo.avatar,
        sourceId: currSessionInfo.value.to,
        groupId: groupId,
        msg: msg,
      });
    }

    // 删除消息
    function deleteMsg(msg) {
      msg.toDelLocal = true;
      store.dispatch("setMsgs", {id: msg.sessionId, msgs: [msg]});
    }

    // 批量保存图片
    function multipleSave(imgList, selFilePath) {
      console.log("multipleSave", selFilePath);
      imgList.map((item, index) => {
        let param = {
          filePath: selFilePath + "\\",
          fileName: `乐聊图片${new Date().getTime()}${index}.png`,
          fileExt: "png",
          replaceFile: true,
        }
        store.dispatch("downloadFile", {item: item, param: param});
      });
    }

    // 打开窗口抖动设置
    function openWindowShake() {
      if (store.getters.getSettings[config.settings.type8] != 1) {
        toast({title: "您的抖动提醒已是打开状态", type: 1});
        return;
      }
      let param = {type: 3, key: config.settings.type8, remark: "窗口抖动", value: 0};
      store.dispatch("setModifySettings", param).then(res => {
        if (res.success) {
          toast({title: "设置成功", type: 1});
        } else {
          toast({title: "设置失败", type: 2});
        }
      });
    }

    // 打开查看大图/视频
    function toViewer(e) {
      toViewerMethod(e, "msg-img", msgUlRef.value, true);
    }

    // 打开查看视频
    function viewerVideo(e) {
      openViewer([{src: e.currentTarget.dataset.src, dataSrc: e.currentTarget.dataset.src, w: screen.width, h: screen.height}], 0, screen.width, screen.height, "video");
    }

    // 查看日程详情
    function setScheduleInfo(e, id, idServer) {
      let param = {
        id: id,
        x: e.pageX,
        y: e.pageY
      }
      store.commit("setEmit", {type: "showScheduleInfo", value: param});
    }

    // 设置日程状态
    function setScheduleStatus(id, status, idServer, join) {
      store.commit("setEmit", {type: "showScheduleInfo", value: {id: id, changeStatus: status, idServer: idServer, join: join}});
    }

    // 稍后提醒日程
    function remindLater(item, minute) {
      store.dispatch("remindLater", {item: item, minute: minute});
    }

    // 请求地址
    async function postUrl(item) {
      if (item.linkType == "post") {
        if (postTimerMap["postUrl"]) {
          toast({title: "请勿频繁操作", type: 2});
          return;
        }
        postTimerMap["postUrl"] = setTimeout(() => {
          postTimerMap["postUrl"] = "";
        }, 3000);
        let res = await axios.req({url: item.url, data: {empNo: userInfo.workerNo}});
        toast({title: res.content || res.errorMsg || (res.success ? "操作成功" : "操作失败"), type: res.isComment ? 1 : 2});
      } else {
        let url = item.url;
        if (url.indexOf("?") > -1) {
          url += "&";
        } else {
          url += "?";
        }
        url += `empNo=${userInfo.workerNo}`;
        getLinkUrl(item, url);
      }
    }

    // 是否显示语音消息未读标记-只在会话和记录页面显示
    function isAudioUnread(item) {
      return (props.msgType == 1 || props.msgType == 3) && item.from != userInfo.workerNo && !remote.store.getters.getAudioTipMsg({idServer: item.idServer});
    }

    // 播放音频
    function playAudio(item, key) {
      let audioObjIdServer = remote.store.getters.getAudioObj.idServer;
      if (audioObjIdServer) {
        // 停止播放
        remote.store.dispatch("setAudioObj", {});
        if (audioObjIdServer == item.idServer) {
          updateMsg(item, true);
          return;
        }
      }
      updateMsg(item, true);
      nextTick(() => {
        let audioParam = {
          index: key,
          idServer: item.idServer,
          src: item.file.url,
          dur: item.file.dur,
          id: currSessionInfo.value.id,
          playNext: !item.setTips,
          isPlay: true,
          msgType: props.msgType,
        };
        remote.store.dispatch("setAudioObj", audioParam);
        if (!item.setTips) {
          setAudioTips(item);
        }
        updateMsg(item, true);
      });
    }

    // 设置消息已读通知
    function setAudioTips(item) {
      if (isAudioUnread(item)) {
        // 未读的语音发送自定义通知消息漫游
        nim.sendTipMsg({
          scene: "p2p",
          to: userInfo.workerNo,
          tip: "语音消息已读回执，更新版本可屏蔽此消息",
          isUnreadable: false,
          custom: JSON.stringify({sessionId: item.to, msgServerId: item.idServer, type: "audio"})
        }).then(res => {
          if (!res.err) {
            remote.store.dispatch("setAudioTipMsg", [res.obj]).then(res => {
              // 通知更新消息
              updateMsg(item, true);
            });
          }
        });
      }
    }

    // 是否当前音频在播放
    function isPlayAudio(item) {
      if (item.idServer == remote.store.getters.getAudioObj.idServer) {
        return true;
      }
      return false;
    }

    // 重新编辑
    function reEditor(msg) {
      msg = deepClone(msg);
      // 存在引用消息
      if (msg.custom?.quoteType) {
        // 判断引用消息是否被撤回
        let param = {id: msg.sessionId, type: 2, limit: 10, beginTime: msg.custom.time, endTime: msg.custom.time + 1000};
        store.dispatch("setHistory", param).then(async res => {
          if (!res.err) {
            console.log("showQuoteDetail", msg.custom.serverID);
            let thisMsg = res.obj.msgs.find(item => {
              if (item.idServer == msg.custom.serverID) {
                return item;
              }
            });
            if (thisMsg) {
              remote.store.commit("setQuoteMsg", {id: msg.sessionId, msg: getQuoteMsg(thisMsg)});
            }
          }
        });
      }
      let text = buildEmoji(msgToHtml(msg));
      store.getters.getImEditor.dangerouslyPasteHTML(msg.sessionId, text);
      store.getters.getImEditor.activeEditor(currSessionInfo.value.id);
    }

    // 修改多选状态
    function changeMultipleSel(item) {
      if (!parentIsMultiple.value) {
        return;
      }
      if (!multipleSelMap.value[item.idServer]) {
        // 反垃圾、禁止、提示、抖动、通知禁止多选
        if (!isShowMsgOperate(4, item)) {
          return;
        }
        if (Object.values(multipleSelMap.value).length >= 30) {
          alert({content: "最多选择30条消息", showCancel: false});
          return;
        }
        multipleSelMap.value[item.idServer] = item;
      } else {
        delete multipleSelMap.value[item.idServer];
      }
    }

    // 多选操作
    async function multipleSel(type, selItem) {
      // 处理多选数据
      let multipleSelList = [];
      for (let key in multipleSelMap.value) {
        if (multipleSelMap.value[key]) {
          multipleSelList.push(multipleSelMap.value[key]);
        }
      }
      multipleSelList = multipleSelList.sort((a, b) => {return a.time - b.time});
      // 当前会话
      let thisSession = currSessionInfo.value;
      if (multipleSelList.length == 0) {
        alert({content: "请至少选择一条消息进行操作!", showCancel: false});
        return;
      }
      switch (type) {
        case 1:
        case 2:
          // 消息体
          let thisMsg = {};
          // 消息标题
          let thisTitle = "";
          // 消息文案
          let thisPushContent = "";
          // 具体消息
          let multipleList = [];
          let idServerList = [];
          let msgIndex = 0;
          let forwardTipsFlag = false;
          // 逐条/合并转发
          multipleSelList.map(item => {
            if (type == 1) {
              multipleList.push(item);
            }
            if (type == 2) {
              multipleList.push(item);
              // 非图文、纯图片、纯文本、语音、视频、文件、合并转发、乐文档提示
              if (!/^(text|image|file|audio|video)$/.test(item.type) && !(item.type == "custom" && /^(multi|9|8|7|5|3)$/.test(item.content.type))) {
                forwardTipsFlag = true;
              }
            }
            idServerList.push(item.idServer);
            if (msgIndex < 4) {
              thisPushContent += `${item.pushContent || store.getters.getPrimaryMsg({msg: item, primaryType: 2, nameFlag: true, notChange: true})}\n`;
            }
            msgIndex++;
          });
          let idServers = idServerList.join(",");
          thisTitle = `${thisSession.detailInfo.name}${thisSession.scene == "p2p" ? `与${userInfo.name}` : ""}的聊天记录`;
          thisMsg = {
            content: {
              data: {
                title: `[合并转发]${thisTitle}`,
                content: thisPushContent,
                sessionId: thisSession.id,
                sessionType: thisSession.type,
                source: "pc-im",
                url: `${config[config.env].jjsHome}/im/message/msgDetail?msgIds=${idServers}`,
                other: {messageIds: idServers}
              },
              type: 9
            },
            type: "custom",
            pushContent: thisPushContent,
            forwardType: type == 1 ? "item" : "merge",
            forwardTitle: thisTitle,
            multipleList: multipleList,
          }
          if (forwardTipsFlag) {
            alert({
              content: "合并转发消息存在无法解析消息类型，是否确认转发？",
              done: type => {
                if (type == 1) {
                  openForward(thisMsg);
                  props.changeMultiple(false);
                }
              }
            });
          } else {
            openForward(thisMsg);
            props.changeMultiple(false);
          }
          break;
        case 3:
          // 批量收藏消息
          multipleSelList.map(item => {
            collectMsg(item, selItem.id);
          });
          toast({title: "收藏完成", type: 1});
          props.changeMultiple(false);
          break;
        case 4:
          // 批量删除消息
          multipleSelList.map(item => {
            deleteMsg(item);
          });
          props.changeMultiple(false);
          break;
        case 5:
          // 批量保存图片
          let imgList = [];
          let isShowTips = false;
          multipleSelList.map(item => {
            if (item.type == "file" && item.file?.isImage) {
              imgList.push(item);
            } else if (item.type == "image") {
              imgList.push(item);
            } else if (item.content?.msgs?.findIndex(item1 => item1.type == "image") > -1) {
              item.content.msgs.map(item1 => {
                if (item1.type == "image") {
                  imgList.push(item1);
                }
              });
            } else {
              isShowTips = true;
            }
          });
          if (isShowTips) {
            alert({
              content: `只能保存图片到电脑，共选择 ${imgList.length} 张图片`,
              done: async type => {
                if (type == 1 && imgList.length > 0) {
                  store.commit("setEmit", {
                    type: "fileInput", value: {
                      nwdirectory: true,
                      done: files => {
                        multipleSave(imgList, files[0].path);
                        toast({title: "图片保存中", type: 1});
                        props.changeMultiple(false);
                      }
                    }
                  });
                }
              }
            });
          } else {
            store.commit("setEmit", {
              type: "fileInput", value: {
                nwdirectory: true,
                done: files => {
                  multipleSave(imgList, files[0].path);
                  toast({title: "图片保存中", type: 1});
                  props.changeMultiple(false);
                }
              }
            });
          }
          break;
        case 6:
          // 转发至特定人员
          let reportMsg = [];
          // 获取转发指定人员的消息体
          for (let i = 0; i < multipleSelList.length; i++) {
            let item = deepClone(multipleSelList[i]);
            let flag = false;
            let reportItem = {type: item.type, time: item.time, from: item.from};
            switch (item.type) {
              case "text":
                flag = true;
                reportItem.text = item.text;
                break;
              case "image":
              case "audio":
                if (item.type == "image" || item.type == "audio") {
                  flag = true;
                  dealFileField(item);
                  if (item.type == "audio") {
                    delete item.file.mp3Url;
                    delete item.file.sen;
                    delete item.file.force_upload;
                  }
                  reportItem.file = item.file;
                }
                break;
              case "custom":
                if (item.content?.type == "multi" && item.content?.msgs?.length > 0) {
                  flag = true;
                  item.content.msgs.map(msgItem => {
                    // 包含乐文档不支持
                    if (msgItem.type == "document") {
                      flag = false;
                    } else if (msgItem.type == "image") {
                      dealFileField(msgItem);
                    }
                  });
                  reportItem.content = item.content;
                }
                break;
            }
            // 只支持 文字、图片、文件类型图片、语音、图文（不包括乐文档）
            if (!flag) {
              continue;
            }
            reportMsg.push(reportItem);
          }
          let showMsg = {
            content: {
              data: {
                title: userInfo.workerName + "紧急重要消息播报",
                from: userInfo.workerNo,
                rank: 3,
                content: reportMsg,
              },
              type: "msg-report"
            },
            time: Date.now(),
            id: currSessionInfo.value.id
          }
          if (reportMsg.length == 0) {
            toast({title: "无符合要求的消息，请重选", type: 2});
            return;
          } else if (JSON.stringify(reportMsg).length > 3500) {
            alert({
              content: "当前转发内容，超过转发的限制（纯文本：3500字，纯语音/图片：10个），请精简下发送内容",
              showCancel: false,
              okText: "我知道了"
            });
            return;
          } else if (reportMsg.length != multipleSelList.length) {
            alert({
              content: "当前转发内容，仅支持图文、语音消息，其他消息将为您过滤发送，是否确定？",
              done: type => {
                if (type == 1) {
                  // 确认后转发
                  store.commit("setEmit", {type: "forwardReportMsg", value: showMsg});
                }
              }
            });
          } else {
            // 直接转发
            store.commit("setEmit", {type: "forwardReportMsg", value: showMsg});
          }
          break;
        case 7:
          // 批量bug反馈
          multiBugReport(multipleSelList);
          break;
      }
    }

    // 批量bug反馈
    function multiBugReport(list) {
      let p = [];
      let errorMsg = "";
      let loadingFlag = false;
      list.map(item => {
        // 图片类型文件、图片、文本、图文
        if ((item.type == "file" && item.file?.isImage) || item.type == "image" || item.type == "text" || (item.content?.type == "multi" && item.content?.msgs?.length > 0)) {
          p.push(new Promise(resolve => {
            resolve([deepClone(item)]);
          }));
        } else if (item.content?.type == 9 && item.content?.data?.url) {
          // 合并转发获取消息内容
          p.push(new Promise(async resolve => {
            if (!loadingFlag) {
              loadingFlag = true;
              loading();
            }
            let urlSearch = item.content.data.url.split("?")[1];
            let res = await findMsgByIdApi({
              msgIds: JSON.stringify((getUrlParams("msgIds", urlSearch) || "").split(",")),
              msgTimeList: JSON.stringify((getUrlParams("msgTimes", urlSearch) || "").split(",")),
              sessionId: getUrlParams("sessionId", urlSearch),
              sessionType: getUrlParams("sessionType", urlSearch),
            });
            let list = [];
            if (res.errorMsg) {
              errorMsg = res.errorMsg;
            } else {
              // 后台数据转成云信消息体后判断对应格式消息才渲染
              let convertList = convertMsg(res.data);
              for (let i = 0; i < convertList.length; i++) {
                let cItem = convertList[i];
                if ((cItem.type == "file" && cItem.file?.isImage) || cItem.type == "image" || cItem.type == "text" || (cItem.content?.type == "multi" && cItem.content?.msgs?.length > 0)) {
                  list.push(cItem);
                }
              }
            }
            resolve(list);
          }));
        }
      });
      if (errorMsg) {
        if (loadingFlag) {
          loading().hide();
        }
        toast({title: errorMsg, type: 1});
        return;
      }
      if (p.length == 0) {
        alert({content: "仅支持图文消息反馈", showCancel: false});
        if (loadingFlag) {
          loading().hide();
        }
        return;
      }
      Promise.all(p).then(async res => {
        try {
          let bugList = [];
          let textLength = 0;
          // 限制最多3000字
          let maxTextLength = 3000;
          for (let i = 0; i < res.length; i++) {
            let iItem = res[i];
            for (let j = 0; j < iItem.length; j++) {
              if (textLength >= maxTextLength) {
                break;
              }
              let jItem = iItem[j];
              if (jItem.type == "text") {
                // 文本超过最大长度切割
                if (textLength + jItem.text > maxTextLength) {
                  jItem.showText = jItem.text = jItem.text.slice(0, maxTextLength - textLength);
                } else if (textLength >= maxTextLength) {
                  break;
                }
                textLength += jItem.text.length;
              } else if (jItem.content?.type == "multi" && jItem.content?.msgs?.length > 0) {
                // 图文超过最大长度切割
                for (let k = 0; k < jItem.content.msgs.length; k++) {
                  let kItem = jItem.content.msgs[k];
                  if (kItem.type == "text") {
                    if (textLength + kItem.text > maxTextLength) {
                      kItem.showText = kItem.text = kItem.text.slice(0, maxTextLength - textLength);
                    } else if (textLength >= maxTextLength) {
                      jItem.content.msgs.length = k;
                      break;
                    }
                    textLength += kItem.text.length;
                  } else if (kItem.type == "document") {
                    let text = getDocJson({docId: kItem.file.docId, padId: kItem.file.docPadId, docName: kItem.file.docName, property: kItem.file.property}).url;
                    textLength += (text || "").length;
                  }
                  if (textLength >= maxTextLength) {
                    jItem.content.msgs.length = k + 1;
                    break;
                  }
                }
              }
              bugList = bugList.concat(jItem);
              if (textLength >= maxTextLength) {
                break;
              }
            }
          }
          if (bugList.length == 0) {
            alert({content: "仅支持图文消息反馈", showCancel: false});
          } else {
            await remote.store.dispatch("getPersons", {doneFlag: true, account: bugList.map(item => {return item.from})})
            store.commit("setEmit", {type: "multiBugItem", value: bugList});
            props.changeMultiple(false);
          }
        } catch (e) {
          toast({title: e, type: 1});
          console.log("multiBugReport", e);
        }
        if (loadingFlag) {
          loading().hide();
        }
      });
    }

    // 重新发送信息
    function resendMsg(msg) {
      remote.store.dispatch("resendMsg", deepClone(msg));
    }

    // 点击文档跳转
    function toDocLink(param) {
      let {item, isShare} = param;
      let urlJson = getDocJson(param);
      store.dispatch("setOpenWindow", [urlJson.url + (isShare && !isNaN(Number(item.from)) ? `&shareNo=${item.from}` : ""), urlJson.frameName]);
    }

    // 获取乐文档地址
    function getDocJson(param) {
      return store.getters.getLinkUrlJson({
        type: "doc",
        doc: {
          docId: param.docId,
          padId: param.padId,
          clearId: param.clearId,
          location: param.location,
          property: getDocType(param),
        }
      });
    }

    // 跳转消息平台
    function toMsgCenterLink(item) {
      store.dispatch("toMsgCenterLink", {item: item, isTips: true});
    }

    // 打开会话 key-1钻展立即沟通
    function openChat(id, key) {
      store.dispatch("setCurrentSession", {id: id, type: "open"});
      if (key == 1) {
        setJJSEvent("P82673408", JSON.stringify({
          workerId: userInfo.workerId,
          userId: userInfo.workerNo,
        }));
      }
    }

    // 设置集中销售倒计时, flag-ture为初始化false为定时器
    function setJZSellTimer(item, flag) {
      let timeObj = {}
      let diffTime = new Date(item.showContent.expireDate).getTime() - (Date.now() + store.getters.getDiffTime);
      if (diffTime > 0) {
        timeObj.start = true;
        timeObj.d = Math.floor(diffTime / 1000 / 60 / 60 / 24);
        timeObj.h = Math.floor(diffTime / 1000 / 60 / 60 % 24);
        timeObj.m = Math.floor(diffTime / 1000 / 60 % 60);
        timeObj.s = Math.floor(diffTime / 1000 % 60);
        if (timeObj.d > 0) {
          timeObj.time1 = timeObj.d;
          timeObj.time1Str = "天";
          timeObj.time2 = timeObj.h;
          timeObj.time2Str = "时";
        } else if (timeObj.h > 0) {
          timeObj.time1 = timeObj.h;
          timeObj.time1Str = "时";
          timeObj.time2 = timeObj.m;
          timeObj.time2Str = "分";
        } else if (timeObj.m > 0) {
          timeObj.time1 = timeObj.m;
          timeObj.time1Str = "分";
          timeObj.time2 = timeObj.s;
          timeObj.time2Str = "秒";
        } else if (timeObj.s > 0) {
          timeObj.time1 = timeObj.m;
          timeObj.time1Str = "分";
          timeObj.time2 = timeObj.s;
          timeObj.time2Str = "秒";
        }
      } else {
        timeObj.start = false;
      }
      // 倒计时结束、超过2小时不进行倒计时
      if (timeObj.start && timeObj.d * 24 + timeObj.h < 2 && flag) {
        msgTimerList.push(item);
      }
      item.showContent.timeObj = timeObj;
    }

    // 跳转卡片消息
    async function toCardLink(item, type) {
      let thisUrl = "";
      if (type == 1 || type == 3) {
        if (item.pcClickTips) {
          toast({title: item.pcClickTips, type: 3});
          return;
        }
        thisUrl = type == 1 ? item.pcJumpUrl : item.url;
      } else if (type == 2) {
        if (item.type == 1) {
          thisUrl = item.pcUrl;
        } else if (item.type == 4) {
          if (item.pcUrl) {
            if (postTimerMap["toCardLink"]) {
              toast({title: "请勿频繁操作", type: 2});
              return;
            }
            postTimerMap["toCardLink"] = setTimeout(() => {
              postTimerMap["toCardLink"] = "";
            }, 3000);
            let res = await postApi({url: item.pcUrl + `&msgType=${currSessionInfo.value.scene != "p2p" ? 1 : 0}&toId=${currSessionInfo.value.to}`});
            toast({title: res.success ? "操作成功" : res.errorInfo || res.errorMsg || "操作失败", type: res.success ? 1 : 2});
          }
          return;
        }
      }
      store.dispatch("setOpenWindow", [linkFormat(thisUrl), "login"]);
    }

    // 触发埋点跳转 type-1集中销售查看房源-2新房集中销售查看房源
    function setEvent(item, type) {
      getLinkUrl(item);
      if (type == 1) {
        setJJSEvent("*********", JSON.stringify({
          workerId: userInfo.workerId,
          sellid: item.content.data.jsonData.sellId,
          fhID: item.content.data.jsonData.fhId,
          time: dateFormat(Date.now(), "yyyy-MM-dd"),
        }));
      }
    }

    // 获取/更新当前消息
    function updateMsg(item, flag) {
      let currItem = "";
      // 通知更新消息
      for (let i = 0; i < currMsgList.value.length; i++) {
        if (currMsgList.value[i].idServer == item.idServer) {
          if (flag) {
            currMsgList.value[i].updateKey = (currMsgList.value[i].updateKey || 0) + 1;
          }
          currItem = currMsgList[i];
          break;
        }
      }
      return currItem;
    }

    // 判断是否fcw会话
    function isFcw() {
      return new RegExp(config.fcw).test(currSessionInfo.value.id);
    }

    // 判断是否ai会话
    function isAi() {
      return new RegExp(config.ai).test(currSessionInfo.value.to);
    }

    // 轻应用卡片图片加载失败
    function errorAppImage(type, e) {
      let thisElm = e.target;
      if (type == 1) {
        thisElm.setAttribute("src", `/img/index/msg/app_default.png`);
      } else {
        thisElm.setAttribute("src", `/img/index/msg/app_icon.png`);
      }
    }

    // 设置收藏事件
    function setCollectEvent(e, type) {
      let target = e.target;
      switch (type) {
        case "click":
        case "contextmenu":
          if (target.nodeName == "IMG") {
            selElm(target);
          }
          break;
        case "dblclick":
          if (target.nodeName == "IMG") {
            console.log("查看收藏大图", target.src);
            let imgList = [];
            let thisIndex = -1;
            e.currentTarget.querySelectorAll("img").forEach((item, index) => {
              if (thisIndex == -1 && target.isEqualNode(item)) {
                thisIndex = index;
              }
              imgList.push({src: item.src, dataSrc: item.src, w: item.naturalWidth, h: item.naturalHeight, ext: "png"});
            });
            openViewer(imgList, thisIndex, target.naturalWidth, target.naturalHeight);
          }
          break;
      }
    }

    // 房源图片压缩
    function getFangImage(src) {
      return `${src}${(src || "").indexOf("?") > -1 ? "&" : "?"}imageView2/0/format/webp/q/50`;
    }

    // 点击提示
    function showToast(text) {
      if (text) {
        toast({title: text, type: 3});
      }
    }

    // 生成对应类型二维码
    function createQrCodeItem(item) {
      try {
        if (item.content?.type && !item.qrcode) {
          let jsonData = item.showContent;
          switch (item.content.type) {
            case "imJZSell":
              // 集中销售
              // 不存在某个参数不生成二维码
              if (jsonData && jsonData.houseType && jsonData.fhId && jsonData.shareWorkerId && jsonData.sellId) {
                let qrJson = {houseType: jsonData.houseType, fhId: jsonData.fhId, shareWorkerId: jsonData.shareWorkerId, yzPrice: jsonData.yzPrice, sellId: jsonData.sellId};
                let qrText = `jjsHouse:${new Buffer(JSON.stringify(qrJson)).toString("base64")}`;
                createQrCode({text: qrText, width: 138}).then(res => {
                  item.qrcode = res;
                });
              }
              break;
            case "imNewHouseJZSell":
              // 新房集中销售
              // 不存在某个参数不生成二维码
              if (jsonData && jsonData.projectId && jsonData.sellId) {
                let qrJson = {projectId: jsonData.projectId, sellId: jsonData.sellId};
                let qrText = `jjsHouseNew:${new Buffer(JSON.stringify(qrJson)).toString("base64")}`;
                createQrCode({text: qrText, width: 138}).then(res => {
                  item.qrcode = res;
                });
              }
              break;
            case "imSomeHouseRecommend":
              // 批量房源-老
              // 生成分享二维码
              if (jsonData && jsonData.batchShareId) {
                let qrText = `jjsApp:${JSON.stringify({className: {ios: "RH_Batch_Share_PreviewView", android: "com.jjshome.chat.session.activity.ShareHousePreviewActivity"}, params: {shareId: jsonData.batchShareId, workerId: jsonData.workerId}})}`;
                createQrCode({text: qrText, width: 138}).then(res => {
                  item.qrcode = res;
                });
              }
              break;
            case "imHouseBatchShare":
              // 批量房源-新
              // 生成分享二维码
              if (jsonData.batchShareId) {
                let qrText = `jjsApp:${JSON.stringify({className: {ios: "RH_Batch_Share_PreviewView", android: "com.jjshome.chat.session.activity.ShareHousePreviewActivity"}, params: {shareId: jsonData.batchShareId, workerId: jsonData.workerId}})}`;
                createQrCode({text: qrText, width: 138}).then(res => {
                  item.qrcode = res;
                });
              }
              break;
            case "minApp_YLYK_Detail":
              // 有料有客
              if (item.content?.data) {
                let qrText = `jjsWebApp:${JSON.stringify({id: item.content.data.minAppId, path: item.content.data.minAppPage})}`;
                createQrCode({text: qrText, width: 138}).then(res => {
                  item.qrcode = res;
                });
              }
              break;
            case "imNews":
              // 资讯卡片
              if (item.content?.data) {
                // 乐办公二维码
                if (item.content?.data?.android?.appletId) {
                  let qrText = `jjsWebApp:${JSON.stringify({id: item.content.data.android.appletId, path: item.content.data.android.applet})}`;
                  createQrCode({text: qrText, width: 80}).then(res => {
                    item.qrcode = res;
                    item.qrcodeText = strToHtml(item.content.data.qrcodeText || "乐聊扫描右侧二维码，转发到朋友圈\n可获得个人专属海报");
                  });
                }
                // 微信公众号二维码
                if (item.content?.data?.weChatUrl) {
                  createQrCode({text: item.content.data.weChatUrl, width: 80}).then(res => {
                    item.wxQrcode = res;
                  });
                }
              }
              break;
          }
        }
      } catch (e) {}
    }

    // ai消息操作 1赞成 2反对 3显示消耗 4复制 5停止响应/重试 6切换应用
    async function aiMsgOperate(type, item, e) {
      let node, res;
      switch (type) {
        case 1:
        case 2:
          // 1赞成 2反对
          if (postTimerMap["aiMsgOperate"]) {
            toast({title: "请勿频繁操作", type: 2});
            return;
          }
          postTimerMap["aiMsgOperate"] = setTimeout(() => {
            postTimerMap["aiMsgOperate"] = "";
          }, 3000);
          res = await feedbackApi({
            sid: item.content?.sid,
            resultStatus: type
          });
          toast({title: res.success ? "操作成功" : res.errorInfo || res.errorMsg || "操作失败", type: res.success ? 1 : 2});
          break;
        case 3:
          // 显示消耗
          showTipsBox(e, `<div>本次消耗${item.content.spend.token}token，约${item.content.spend.coin}乐币。<a style="color:#FFF;border-bottom:1px solid #FFF;cursor:pointer" target="_blank" href="${aiObj.value.consumeUrl}">什么是消耗？</a></div>`)
          break;
        case 4:
          // 复制
          node = document.getElementById("copyRenderElm");
          node.innerHTML = msgUlRef.value.querySelector(`.msg-info-${item.idServer} .msg-content`).innerHTML;
          selElm(node);
          document.execCommand("copy");
          nextTick(() => {
            node.innerHTML = "";
          });
          toast({title: "复制成功！", type: 1});
          break;
        case 5:
          store.dispatch("activeImEditor", {id: currSessionInfo.value.id, active: true});
          if (item.resendFlag) {
            // 重试
            store.dispatch("setAiQuestion", {aiJson: item});
          } else {
            // 停止响应
            let sid = item.sid || item.sseId;
            if (sid) {
              res = await stopAnswerApi({
                sid: sid
              });
              if (res.success) {
                deleteSseMsg(item);
              } else {
                toast({title: res.errorInfo || res.errorMsg || "操作失败", type: 2});
              }
            } else if (item.axiosCancel) {
              try {
                item.axiosCancel.cancel();
              } catch (e) {}
              deleteSseMsg(item);
            }
          }
          break;
        case 6:
          // 切换应用
          store.commit("setEmit", {type: "scroll", value: "bottom"});
          remote.store.commit("setAiObj", {switchApp: true, currentApp: item, sendTo: currSessionInfo.value.to});
          break;
      }
    }

    // 显示悬浮框内容
    function showTipsBox(e, text, pos) {
      store.commit("setEmit", {
        type: "tipsBox", value: {type: 1, e: e, content: text, pos: pos}
      });
    }

    // 删除sse消息
    function deleteSseMsg(msg) {
      remote.store.dispatch("setSseListener", {type: "remove", id: msg.sid || msg.sseId || msg.idServer});
    }

    // 发送消息
    async function sendMsg(item) {
      let isPur = true;
      store.commit("setEmit", {type: "scroll", value: "bottom"});
      if (item.appId) {
        let thisItem = aiObj.value.commonAppList.find(item1 => {return item1.id == item.appId})
        // 存在应用
        if (thisItem?.id) {
          let param = {currentApp: deepClone(thisItem)};
          remote.store.commit("setAiObj", param);
        } else {
          // 非通用应用判断权限
          isPur = await getPurToChat({type: "custom", custom: {type: 1, to: currSessionInfo.value.to, appId: item.appId}}, true);
        }
      }
      if (isPur) {
        store.dispatch("doSendMsg", {sessionId: currSessionInfo.value.id, messages: [{type: "text", text: item.text}]});
      }
    }

    // icon加载失败
    function errorIcon(e) {
      e.target.src = "img/index/icon_ai_app.png";
      e.target.onerror = "";
    }

    // 设置ai消息卡片
    function setAiMsgCard() {
      if (props.msgType != 1) {
        return;
      }
      // 智能助理账号在最后一条消息插入主界面卡片
      let isAiObj = currSessionInfo.value.to == aiObj.value.workerNo;
      if (isAi() || isAiObj) {
        let msgs = store.getters.getMsgs({id: currSessionInfo.value.id});
        let msg = msgs?.[msgs?.length - 1];
        if (isAiObj) {
          // 智能助理
          if (!msg && currSessionInfo.value.noMsg && aiObj.value.initCard) {
            let content = {
              type: "ai-msg",
              begin: true,
              msgs: [
                {type: "text", text: `你好！终于见面啦，我是你的专属人工智能助理，\n你可为我 `},
                {type: "link", text: `自定义头像和昵称`, isOuterOpen: true, isWrap: false, linkType: "url", url: aiObj.value.userUrl},
                {type: "text", text: `，\n我既能为你解答业务和制度相关的问题，\n又能帮你完成特定的业务工作，\n现在，开启属于我们的合作之旅吧。`},
                {type: "button", text: `开始使用`, pc: {type: "custom", detailType: "appBegin"}},
                {type: "text", text: `对话或使用代表您同意并遵守`},
                {type: "link", text: `《AI应用平台用户协议》`, isOuterOpen: true, isWrap: false, color: "#2D91E6", linkType: "url", url: aiObj.value.protocolUrl},
              ]
            }
            if (aiObj.value.initCard) {
              remote.store.commit("setAiObj", {initCard: false});
            }
            store.commit("sendAiMsgCard", {content: content, sendTo: currSessionInfo.value.to});
            switchAiApp();
            store.commit("setEmit", {type: "scroll", value: "bottom"});
          } else if (msg) {
            if (!msg?.content?.begin && (aiObj.value.initCard || (msg?.content?.type != "ai-card" && Date.now() + store.getters.getDiffTime - msg?.time >= 30 * 60 * 1000))) {
              if (aiObj.value.initCard) {
                remote.store.commit("setAiObj", {initCard: false});
              }
              store.commit("sendAiMsgCard", {content: aiObj.value.cardCustom, sendTo: currSessionInfo.value.to});
            }
            switchAiApp();
            store.commit("setEmit", {type: "scroll", value: "bottom"});
          }
        } else {
          // 其他数字人
          if (currSessionInfo.value.detailInfo?.businessType == "AI_PL") {
            // ai陪练进入调用接口
            if (!remote.store.getters.getState("aiTempIdMap")?.[currSessionInfo.value.id]?.flag) {
              store.commit("setAiTempIdMap", {key: currSessionInfo.value.id, value: {id: "", flag: true}});
              aiSparringApi({stage: 1, fromAccid: currSessionInfo.value.to});
            }
          } else {
            // 其他数字人在没有消息记录的时候显示开场白
            if (!msg && currSessionInfo.value.noMsg && currSessionInfo.value.detailInfo?.prologue) {
              store.commit("sendAiMsgCard", {content: {type: "ai-msg", msgs: [{type: "text", text: currSessionInfo.value.detailInfo?.prologue}]}, sendTo: currSessionInfo.value.to});
            }
          }
        }
      }
    }

    // 调用切换应用
    function switchAiApp() {
      let switchAppTempItem = store.getters.getEmit["switchAppTempItem"];
      if (switchAppTempItem) {
        store.commit("setEmit", {type: "switchAppTempItem", value: ""});
        store.commit("setEmit", {type: "switchAppItem", value: switchAppTempItem});
      }
    }

    // ai消息按钮
    async function toAiMsgLink(item, item1, item2) {
      switch (item1.type) {
        case "link":
          if (item1.linkType == "url") {
            // 链接跳转
            if (!item1.openType || /pc/i.test(item1.openType)) {
              getLinkUrl(item, item1.url, item1.isOuterOpen ? 'login' : '');
            }
          } else if (item1.linkType == "text") {
            // 发送文本
            sendMsg(item1);
          } else if (item1.linkType == "file") {
            // 发送文件
            store.commit("setEmit", {type: "scroll", value: "bottom"});
            let msg = {
              type: "file",
              file: {url: item1.url, name: item1.name, ext: item1.ext, size: item1.size},
              sessionId: currSessionInfo.value.id,
              scene: currSessionInfo.value.scene,
              to: currSessionInfo.value.to,
              from: userInfo.workerNo,
              time: Date.now() + store.getters.getDiffTime,
              idServer: UUID.generate(),
              status: "success",
              forbidMsg: true
            };
            store.dispatch("sendMsgDone", {err: "", obj: msg});
          } else {
            toast({title: tipsObj.value.update, type: 2});
          }
          break;
        case "button":
          if (item1.pc.type == "url") {
            // 链接跳转
            getLinkUrl(item, item1.url, 'login')
          } else if (item1.pc.type == "custom") {
            if (item1.pc.detailType == "appBegin") {
              // 开始使用智能助理
              store.commit("setEmit", {type: "scroll", value: "bottom"});
              store.commit("sendAiMsgCard", {content: aiObj.value.cardCustom, tip: "智能助理初始化完成", sendTo: currSessionInfo.value.to});
            } else if (item1.pc.detailType == "aiApp" && item1.pc.custom?.appId) {
              // 打开创作应用弹窗
              loading();
              let res = await getAppPurApi({appId: item1.pc.custom.appId, sseId: item1.pc.custom.sseId});
              loading().hide();
              if (res?.success) {
                store.commit("setEmit", {type: "showAiApp", value: res.data});
              } else {
                toast({title: res?.errorMsg || '系统错误', type: 2});
              }
            } else {
              toast({title: tipsObj.value.update, type: 2});
            }
          } else {
            toast({title: tipsObj.value.update, type: 2});
          }
          break;
        case "column":
          // type:1调用ai陪练接口
          if (postTimerMap["toAiMsgLink"]) {
            toast({title: "请勿频繁操作", type: 2});
            return;
          }
          postTimerMap["toAiMsgLink"] = setTimeout(() => {
            postTimerMap["toAiMsgLink"] = "";
          }, 1000);
          if (item2.type == 1) {
            if (item2.ext?.appId) {
              // 直接触发ai对话
              store.commit("setAiTempIdMap", {key: currSessionInfo.value.id, value: {flag: true, temp: {id: item2.ext.appId, prologue: item2.ext.prologue}}});
            } else {
              // 默认触发下个阶段的ai陪练
              store.commit("setAiTempIdMap", {key: currSessionInfo.value.id, value: {flag: true, temp: {stage: parseInt(item2.ext.stage) + 1, businessId: item2.ext.id}}});
            }
            // 发送文本
            store.dispatch("doSendMsg", {sessionId: currSessionInfo.value.id, messages: [{type: "text", text: item2.text}]});
          }
          break;
        case "buttons":
          if (item2.pc.appId) {
            // 设置存在业务类型ai的临时appId
            store.commit("setAiTempIdMap", {key: currSessionInfo.value.id, value: {flag: true, temp: {id: item2.pc.appId, prologue: item2.pc.prologue, flag: true}}});
            // 发送文本
            if (item2.text) {
              store.dispatch("doSendMsg", {sessionId: currSessionInfo.value.id, messages: [{type: "text", text: item2.text}]});
            }
          }
          break;
      }
    }

    // 是否显示ai操作区域
    function isShowAiOperate(item) {
      // 兼容历史版本没有from字段
      let flag = props.msgType == 1 && (item.content.feedback || item.content.spend) && item.from != userInfo.workerNo && (!item.content.from || item.content.from == userInfo.workerNo);
      if (item.scene == "team" && flag) {
        let hait = item.content.msgs?.[0]?.custom?.hait;
        flag = hait && hait.indexOf(userInfo.workerNo) != -1;
      }
      return flag;
    }

    // 打开合作卡片详情
    function openCooperateLink(item) {
      if (item.content.data.fqfEmpNo == userInfo.workerNo || item.content.data.hzfEmpNo == userInfo.workerNo) {
        getLinkUrl("", item.content.data.fqfEmpNo == userInfo.workerNo ? item.content.data.pcShardDetailUrl : item.content.data.pcDetailUrl, "login");
      } else {
        toast({title: "非合作方不支持查看详情", type: 2});
      }
    }

    // 判断权限跳转会话和应用
    async function getPurToChat(item, notOpen) {
      switch (item.type) {
        case "url":
          // 打开页面
          getLinkUrl(item, item.value, 'login');
          break;
        case "custom":
          if (item.custom?.type) {
            if (item.custom.type == 1 && item.custom.to) {
              // 判断权限打开会话
              let openFlag = false;
              let appObj = {};
              let scene = item.custom.scene || "p2p";
              if (new RegExp(config.ai).test(item.custom.to)) {
                // 打开ai会话判断权限
                loading();
                let res = await getAppPurApi({appId: item.custom.appId});
                loading().hide();
                if (res?.success) {
                  openFlag = true;
                  appObj = res.data;
                } else {
                  openFlag = false;
                  toast({title: res?.errorMsg || '系统错误', type: 2});
                }
              } else {
                openFlag = true;
              }
              if (openFlag) {
                if (!notOpen) {
                  openChat(scene + "-" + item.custom.to);
                }
                // 切换对应应用
                if (item.custom.appId && aiObj.value.currentApp.id != item.custom.appId && item.custom.to == aiObj.value.workerNo) {
                  store.commit("setEmit", {type: "switchAppTempItem", value: appObj});
                }
              }
              return openFlag;
            } else {
              toast({title: tipsObj.value.update, type: 2});
            }
          }
          break;
      }
    }

    function stopPropagation() {}

    // 兼容老版本判断乐文档还是乐表格
    function getDocType(file) {
      let name = file.docName || "";
      return file.property || (/xlsx/.test(name.slice(name.lastIndexOf("."))) ? 2 : 1);
    }

    // 判断初始化ai业务应用id
    function initAiTempIdMap(id) {
      // 主窗口存在ai业务应用id
      if (id && remote.store.getters.getState("aiTempIdMap")?.[id]) {
        let thisId = remote.store.getters.getState("aiTempIdMap")[id]?.id;
        // 主窗口切换则重置id
        if (isMainWin()) {
          thisId = "";
        }
        store.commit("setAiTempIdMap", {key: id, value: {flag: false, temp: {id: thisId}}});
      }
    }

    return {
      msgUlRef,
      userInfo,
      currMsgList,
      multipleSelMap,
      currSessionInfo,
      parentIsMultiple,
      aiObj,
      showTag,
      showOperateId,

      getPerson,
      hideElm,
      showUserInfo,
      avatarError,
      fangError,
      clickImage,
      selElm,
      loadImage,
      errorImage,
      loadDefaultImage,
      dealMem,
      secToTime,
      strToHtml,
      linkFormat,
      buildEmoji,
      getFileIcon,
      transTime,
      getLinkUrl,
      getDocTitle,
      getScheduleTime,
      getNotification,
      dateFormat,
      cancelSendFile,
      openFile,
      openFolder,
      getShowMenu,
      setMenu,
      isShowMsgOperate,
      setMenuClick,
      getFocusMsg,
      getMsgReadStatus,
      openWindowShake,
      toViewer,
      viewerVideo,
      setScheduleInfo,
      setScheduleStatus,
      remindLater,
      postUrl,
      isAudioUnread,
      playAudio,
      isPlayAudio,
      reEditor,
      changeMultipleSel,
      multipleSel,
      resendMsg,
      toDocLink,
      getDocJson,
      openForward,
      toMsgCenterLink,
      openChat,
      toCardLink,
      setEvent,
      isFcw,
      isAi,
      errorAppImage,
      userWearPic,
      setCollectEvent,
      getFangImage,
      isSubOrSer,
      showToast,
      aiMsgOperate,
      sendMsg,
      showTipsBox,
      errorIcon,
      toAiMsgLink,
      isShowAiOperate,
      setSubSerHtml,
      openCooperateLink,
      getPurToChat,
      stopPropagation,
      getDocType,
    }
  }
}
</script>
<style scoped lang="scss">
.chat-msg {
  ::v-deep(.highlight) {
    color: $styleColor;
  }

  ::v-deep(.show-name-gray) {
    font-size: 12px;
    color: #999999;
  }

  .icon-ai {
    width: 14px;
    height: 14px;
    background-image: url("/img/index/icon_ai.png");
    background-repeat: no-repeat;
    background-size: 126px 14px;
    cursor: pointer;

    &.approve {
      background-position: 0 0;
    }

    &.oppose {
      background-position: -28px 0;
    }

    &.detail {
      background-position: -56px 0;
    }

    &.copy {
      background-position: -84px 0;
    }

    &:hover {
      &.approve {
        background-position: -14px 0;
      }

      &.oppose {
        background-position: -42px 0;
      }

      &.detail {
        background-position: -70px 0;
      }

      &.copy {
        background-position: -98px 0;
      }
    }
  }

  .file-name {
    display: flex;
    align-items: center;

    .name {
      max-width: 120px;
    }

    .ext {
      max-width: 40px;
      flex-shrink: 0;
    }
  }

  .msg-err-icon {
    width: 14px;
    height: 14px;
    background: url("/img/index/error_tips.png") no-repeat;
    background-size: 14px 14px;
    margin-right: 5px;
  }

  .msg-ul {
    cursor: default;

    &.collect-msg-ul,
    &.quote-msg-ul {
      .msg-time-box,
      .user-avatar-box,
      .msg-content:before,
      .msg-content:after {
        display: none !important;
      }

      .msg-info {
        padding: 0 !important;

        .msg-box {
          width: 100% !important;

          .msg-content:not(.msg-forbid-content) {
            min-height: 20px !important;
            background: #FFFFFF !important;
            padding: 0 !important;
          }
        }
      }
    }

    &.collect-msg-ul {

      .user-name-box,
      ::v-deep(.user-name-box) {
        display: none !important;
      }

      .msg-audio {
        position: relative;
        background: #F1F1F3 !important;
        padding: 5px 10px !important;
        border-radius: 4px;

        &:after {
          content: "";
          position: absolute;
          top: 50%;
          left: -6px;
          transform: translateY(-50%);
          border-width: 6px 6px 6px 0;
          border-style: solid;
          border-color: transparent #F1F1F3 transparent transparent;
        }
      }

      .msg-audio-point {
        display: none !important;
      }
    }

    .msg-time-box {
      display: flex;
      justify-content: center;

      .msg-time {
        display: inline-block;
        line-height: 20px;
        color: #fff;
        padding: 0 6px;
        background: #d8d8d8;
        border-radius: 4px;
        margin: 10px 0;
      }
    }

    .msg-sub-ser {
      padding: 0 16px;
    }

    .msg-tip-box {
      display: flex;
      justify-content: center;

      .msg-tip {
        display: inline-block;
        padding: 5px 10px;
        background: #dadada;
        color: #fff;
        line-height: 18px;
        word-break: break-all;
        max-width: 290px;
        margin-bottom: 10px;
        border-radius: 4px;

        .re-editor {
          color: $styleColor;
          cursor: pointer;
          margin-left: 5px;
        }
      }

      .msg-notification-box {
        width: 80%;
        margin-bottom: 10px;
        background: #FFFFFF;
        border: 1px solid #DDDDDD;
        border-radius: 5px;
        cursor: pointer;
        overflow: hidden;

        .msg-notification-content {
          display: flex;
          justify-content: space-between;
          padding: 10px;

          .msg-notification-text-box {
            width: 100%;

            &.has-img {
              width: calc(100% - 80px - 16px);
            }

            .msg-notification-title {
              font-size: 15px;
              font-weight: bold;
              margin-bottom: 10px;
            }

            .msg-notification-intr {
              width: 100%;
              font-size: 13px;
              color: #808080;
            }
          }

          .msg-notification-img-box {
            img {
              width: 80px;
              height: 60px;
              cursor: pointer;
              flex-shrink: 0;
              margin-left: 16px;
              border-radius: 4px;
            }
          }
        }

        .msg-notification-tips {
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 28px;
          border-top: 1px solid #DDDDDD;
          padding: 0 10px;
          color: #999999;
        }
      }
    }

    .msg-shake {
      text-align: center;
    }

    .msg-open {
      color: #333333;
      cursor: pointer;

      &:hover {
        color: $styleColor;
      }
    }

    .user-avatar-box,
    ::v-deep(.user-avatar-box) {
      margin-right: 10px;

      &.border {
        box-sizing: border-box !important;
      }
    }

    .user-name-box,
    ::v-deep(.user-name-box) {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .user-name {
        color: #9b9b9b;
        line-height: 12px;
        margin-bottom: 5px;
      }

      .msg-show-record {
        color: #169ADA;
        line-height: 12px;
        margin-bottom: 5px;
        cursor: pointer;
        white-space: nowrap;

        &:hover {
          color: #1079AB;
        }
      }
    }

    .msg-center-type {
      display: flex !important;

      .msg-center-box {
        width: 456px;
        max-width: 100%;
        flex-shrink: 0;
      }

      .msg-center-content {
        background: #FFFFFF;
        border-radius: 4px;
        position: relative;

        .msg-center-title {
          padding: 10px 10px 0 10px;
          margin-bottom: 8px;
          word-break: break-all;

          .msg-center-label {
            background: #FF9100;
            border-radius: 1px;
            font-size: 12px;
            padding: 1px 6px;
            color: #FFFFFF;
            display: inline !important;
            vertical-align: unset !important;
            margin-right: 6px;

            &.msg-center-label-3 {
              background: $styleColor;
            }
          }

          .msg-center-text {
            font-weight: 600;
            font-size: 16px;
            line-height: 22px;
            display: inline !important;
            vertical-align: baseline !important;
          }
        }

        .msg-center-content-box {
          padding: 0 10px;
          border-bottom: 1px solid transparent;
        }

        .msg-center-intr {
          font-size: 14px;
          color: #333333;
          line-height: 20px;
          margin-bottom: 8px;
          white-space: pre-wrap;
          word-break: break-all;
          text-align: justify;
        }

        .msg-center-img-box {
          width: 100%;
          display: flex;
          justify-content: center;
          margin-bottom: 8px;

          img {
            max-width: 100%;
          }
        }

        .msg-center-btn-box {
          display: flex;
          margin-bottom: 8px;

          .msg-center-btn {
            flex: 1;
            height: 30px;
            line-height: 28px;
            text-align: center;
            border-radius: 2px;
            border: 1px solid $styleColor;
            margin-right: 10px;
            color: $styleColor;
            font-size: 14px;
            cursor: pointer;

            &:hover {
              background: #FFF3F3;
            }

            &:last-child {
              margin-right: 0px;
            }
          }
        }

        .msg-center-remind-box {
          border-top: 1px solid #EEEEEE;
          height: 37px;
          line-height: 36px;
          padding: 0 10px;

          .msg-center-remind-intr {
            color: #666666;
          }

          .msg-center-remind-btn {
            margin-left: 10px;
            cursor: pointer;

            &:hover {
              color: $styleColor;
            }
          }
        }
      }
    }

    .msg-sub-ser {
      margin-bottom: 30px;

      .sub-ser-type {
        min-width: 500px;
        max-width: 750px;
        margin: 5px auto 16px;
        padding: 16px 16px 8px 16px;
        box-sizing: border-box;
        background: #FFFFFF;
        border-radius: 4px;
        border: 1px solid #EDEDED;
        transition: all 0.2s ease-in-out;

        &:hover {
          box-shadow: 0px 3px 10px 0px rgba(165, 165, 165, 0.5);
        }

        .sub-ser-title {
          max-width: 600px;
          font-size: 16px;
          margin-bottom: 8px;
          font-weight: bold;
        }

        .sub-ser-content {
          font-size: 14px;
          color: #666666;
          word-break: break-all;
        }

        .sub-ser-intr-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid #E0E0E0;
          margin-top: 10px;
          padding-top: 8px;

          .sub-ser-intr {
            color: #999999;
          }

          .sub-ser-btn-box {
            display: flex;
            align-items: center;

            .sub-ser-btn {
              padding: 4px 10px;
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              margin-left: 10px;
              color: #000000;

              &:hover {
                color: $styleColor;
                border: 1px solid $styleColor;
              }
            }
          }
        }
      }
    }

    .msg-info {
      display: flex;
      padding: 6px 16px 16px;
      position: relative;

      &.focus {
        background: rgb(255, 237, 196);
      }

      &.msg-multiple {
        padding-left: 40px;
      }

      &.msg-multiple-check {
        background: rgb(226, 226, 226);

        .multiple-sel-box {
          &:before {
            border: 1px solid $styleColor;
            background: $styleColor;
          }

          &:after {
            content: '';
            width: 9px;
            height: 4px;
            border: 2px solid white;
            border-top: transparent;
            border-right: transparent;
            text-align: center;
            display: block;
            position: absolute;
            top: 6px;
            left: 5px;
            vertical-align: middle;
            transform: rotate(-45deg);
          }
        }
      }

      .multiple-sel-modal {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 5;
      }

      .multiple-sel-box {
        position: absolute;
        top: 10px;
        left: 10px;
        width: 18px;
        height: 18px;

        &.disabled {
          &:before {
            background-image: url("/img/index/disabled_sel.png");
            background-repeat: no-repeat;
            background-size: 100%;
            border: 0;
          }
        }

        &:before {
          content: '';
          width: 18px;
          height: 18px;
          border: 1px solid #7D7D7D;
          display: inline-block;
          border-radius: 50%;
          vertical-align: middle;
        }
      }

      .msg-box {
        width: 72%;

        .msg-content-box,
        .msg-content,
        .msg-custom,
        .msg-content-type,
        .msg-multi-all-box,
        .msg-multi-box,
        .house-content-type5, {
          max-width: 100%;
        }

        .house-box {
          max-width: calc(100% - 80px);
        }

        .msg-content-box {
          display: flex;
          align-items: center;
          position: relative;

          &.msg-content-doc {
            .msg-img {
              height: auto !important;
            }
          }

          &.msg-ai-typing {
            align-items: flex-end;
          }

          &.msg-ai-loading {

            .msg-content {
              padding-left: 36px;
              position: relative;

              .icon-ai-loading {
                position: absolute;
                top: 50%;
                left: 10px;
                transform: translateY(-50%);
                width: 20px;
                height: 20px;

                &:before {
                  display: block;
                  content: "";
                  width: 100%;
                  height: 100%;
                  background-image: url("/img/index/icon_refresh.png");
                  background-repeat: no-repeat;
                  background-size: 60px 20px;
                  background-position: -40px 0 !important;
                  animation: myLoading 500ms linear infinite;
                }
              }
            }
          }

          .msg-content {
            display: inline-block;
            position: relative;
            word-break: break-all;
            min-width: 50px;
            min-height: 34px;
            line-height: 20px;
            background: #FFFFFF;
            border-radius: 4px;
            padding: 7px 10px;

            &:after {
              content: "";
              width: 0;
              height: 0;
              position: absolute;
              top: 12px;
              border-width: 6px;
              border-style: solid;
              left: -12px;
              border-color: transparent #FFFFFF transparent transparent;
            }

            &:hover {
              .msg-operate-ul {
                display: flex !important;
              }
            }

            .msg-multi-box {
              display: inline;

              .msg-text {
                display: inline;
              }
            }

            span {
              display: inline-block;
              vertical-align: bottom;
            }

            .msg-text {
              max-width: 100%;
              overflow: hidden;
              cursor: default;
              font-size: 14px;
              white-space: pre-wrap;
              word-wrap: break-word;

              &.bold {
                font-weight: bold;
                display: block !important;
              }

              &.msg-collect {
                ::v-deep(*) {
                  max-width: 100% !important;
                }
              }
            }

            .msg-typing {
              animation-duration: 0.7s;
              --tw-enter-opacity: 0.25;
              animation-name: msg-typing-keyframes;
            }

            @keyframes msg-typing-keyframes {
              0% {
                opacity: var(--tw-enter-opacity, 1);
                transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))
              }
            }

            .msg-img {
              min-width: 10px;
              min-height: 10px;
              max-width: 100%;
              vertical-align: text-bottom;
            }

            .ll-emoji {
              width: 120px;
              height: 120px;
            }

            .msg-audio {
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              max-width: 100%;

              .audio-box {
                display: flex;
                justify-content: flex-end;
                align-items: center;
                cursor: pointer;
                max-width: 100% !important;

                .audio-dur {
                  margin: 0 10px;
                }
              }

              .audio-text-box {
                position: relative;
                width: 100%;
                margin-top: 10px;
                padding-top: 10px;
                display: flex;
                word-break: break-all;

                &.no-text {
                  color: #7F7F7F;
                  justify-content: center;
                }

                .audio-loading {
                  width: 14px;
                  height: 14px;
                  animation: myLoading 800ms linear infinite;
                }

                &:after {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 1px;
                  background: rgba(0, 0, 0, .13);
                }
              }
            }

            .msg-video {
              width: 250px;
              height: 300px;
              max-width: 100%;
              position: relative;
              overflow: hidden;
              background: #000000;
              border-radius: 4px;

              .video-img {
                width: 250px;
                height: 300px;
                max-width: 100%;
              }

              .msg-video-info {
                width: 100%;
                padding: 10px;
                background: url("/img/index/msg/video_shadow.png") no-repeat 100%;
                position: absolute;
                left: 0;
                bottom: 0;
                font-size: 13px;
                color: #FFFFFF;
                display: flex;
                flex-direction: column;


                .video-info {
                  display: flex;
                  justify-content: space-between;

                  .file-size {
                    font-size: 12px;
                  }

                  .video-time {
                    font-size: 14px;
                  }
                }
              }

              .msg-video-operate {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                cursor: pointer;

                .msg-video-download {
                  width: 52px;
                  height: 50px;
                }

                .msg-video-progress {
                  width: 50px;
                  height: 50px;
                  box-shadow: inset 0 0 0 3px #3BD4F4;
                  border-radius: 50%;

                  .msg-video-left,
                  .msg-video-right,
                  .msg-video-circle {
                    position: absolute;
                    left: 0;
                    top: 0;
                    bottom: 0;
                    right: 0;
                  }

                  .msg-video-left {
                    border-radius: 50%;
                    border: 3px solid #ccc;
                    clip: rect(0, 25px, 50px, 0);
                  }

                  .msg-video-right {
                    border-radius: 50%;
                    border: 3px solid #ccc;
                    clip: rect(0, 50px, 50px, 25px);
                  }

                  .msg-video-circle {
                    width: 42px;
                    height: 42px;
                    background: rgba(0, 0, 0, .3);
                    border-radius: 50%;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                  }

                  .msg-video-cancel {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 40px;
                    height: 40px;
                    font-size: 14px;
                    color: #ccc;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                  }
                }
              }
            }

            .msg-file {
              max-width: 100%;
              overflow: hidden;

              .file-info-box {
                display: flex;
                padding: 10px;
                border-bottom: 1px solid #e8e8e8;
                width: 240px;
                max-width: 100%;

                .file-icon {
                  width: 42px;
                  height: 42px;
                  border-radius: 2px;
                  margin-right: 12px;
                }

                .file-content {
                  max-width: calc(100% - 54px);

                  .file-name {
                    color: #333333;
                  }

                  .file-size {
                    color: #999;
                  }
                }
              }

              progress {
                display: block;
                width: 100%;
                height: 3px;
                border: none;

                &::-webkit-progress-bar {
                  background-color: #FFFFFF;
                }

                &::-webkit-progress-value {
                  background-color: #B2E281;
                }
              }

              .file-status-box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 30px;
                padding: 0 10px;

                .file-status {
                  color: #999999;
                }

                .file-operate-box {
                  display: flex;
                  align-items: center;

                  .file-operate {
                    color: #333333;
                    margin-left: 15px;
                    cursor: pointer;

                    &:hover {
                      color: $styleColor;
                    }
                  }
                }
              }
            }

            .msg-geo {
              cursor: pointer;
            }

            .msg-doc {
              width: 282px;
              max-width: 100%;
              border-radius: 2px;
              border: 1px solid #D8D8D8;
              cursor: pointer;
              margin: 5px 0;
              background: #FFFFFF;

              .msg-doc-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #2D91E6;
                padding: 7px 10px;
                font-size: 14px;
                line-height: 20px;

                .msg-doc-text-box {
                  display: -webkit-box;

                  .msg-doc-icon {
                    display: inline-block;
                    width: 14px;
                    height: 20px;
                    background-image: url("/img/index/msg/doc/icon_doc.png");
                    background-repeat: no-repeat;
                    background-size: 14px 14px;
                    background-position: left 4px;
                    margin-right: 5px;
                    vertical-align: text-bottom;

                    &.msg-excel-icon {
                      background-image: url("/img/index/msg/doc/icon_excel.png");
                    }
                  }

                  .msg-doc-text {
                    display: inline;
                  }
                }

                .msg-doc-back {
                  width: 12px;
                  height: 12px;
                  background: url("/img/index/msg/doc/back.png") no-repeat;
                  background-size: 12px 12px;
                  flex-shrink: 0;
                }
              }

              .msg-doc-content {
                width: 100%;
                border-top: 1px solid #E1E1E1;
                display: flex;
                position: relative;

                img {
                  width: 100%;
                }

                .msg-doc-bg {
                  width: calc(100% - 8px);
                  height: calc(100% - 8px);
                  position: absolute;
                  top: 4px;
                  left: 4px;
                  background-size: 100% auto;
                  background-repeat: no-repeat;
                }
              }

              .msg-doc-footer {
                height: 34px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #999999;
                padding-left: 10px;
                border-top: 1px solid #E1E1E1;

                .msg-doc-pur {
                  position: relative;
                  color: #333333;
                  padding: 5px 20px 5px 10px;
                  font-weight: bold;

                  &:hover,
                  &.active {
                    color: $styleColor;

                    &:after {
                      border-color: $styleColor transparent transparent transparent;
                    }
                  }

                  &.active {
                    &:after {
                      transform: translateY(-50%) rotate(180deg);
                    }
                  }

                  &:after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    right: 10px;
                    transform: translateY(-50%);
                    width: 0;
                    height: 0;
                    border-width: 3px 3px 0 3px;
                    border-style: solid;
                    border-color: #000000 transparent transparent transparent;
                  }
                }
              }
            }

            .msg-robot {
              font-size: 14px;

              .robot-title {
                margin-top: 10px;
              }

              .robot-list-like {
                margin-bottom: 2px;

                .robot-list-link {
                  display: inline;
                  color: $styleLink;
                  cursor: pointer;
                  border-bottom: 1px solid $styleLink;
                }
              }

              .robot-comment-box {
                margin-top: 10px;

                .robot-title {
                  display: inline;
                  margin-right: 5px;
                }

                .robot-list-btn-box {
                  display: inline;

                  .robot-list-btn {
                    display: inline;
                    border: 1px solid #333333;
                    border-radius: 12px;
                    padding: 0 10px;
                    cursor: pointer;
                    line-height: 18px;
                    margin-left: 5px;

                    &:hover {
                      border: 1px solid #3888FF;
                      color: #3888FF;

                      .robot-comment-icon1 {
                        background-position: -30px 0;
                      }

                      .robot-comment-icon2 {
                        background-position: -45px 0;
                      }
                    }

                    .robot-comment-icon1,
                    .robot-comment-icon2 {
                      display: inline-block;
                      width: 12px;
                      height: 18px;
                      background-image: url("/img/index/msg/comment.png");
                      background-repeat: no-repeat;
                      background-position: 0 0;
                      background-size: 60px 14px;
                      vertical-align: middle;
                    }

                    .robot-comment-icon2 {
                      background-position: -15px 0;
                    }
                  }

                  .robot-list-link-btn {
                    color: #3888FF;
                    display: inline;
                    cursor: pointer;
                    margin-left: 5px;
                  }
                }
              }

              .robot-link {
                color: $styleLink;
                margin: 10px 10px 0 0;
                cursor: pointer;

                &:last-child {
                  margin-right: 0;
                }
              }
            }

            .msg-custom {
              .house-content-type5 {
                display: flex;
                cursor: pointer;

                .house-img {
                  width: 70px;
                  height: 70px;
                  border-radius: 6px;
                  background-color: #ccc;
                  margin-right: 10px;
                }

                .house-box {
                  display: flex;
                  flex-direction: column;

                  .house-title {
                    color: #000000;
                    font-size: 14px;
                  }

                  .house-intr {
                    color: #989898;
                  }

                  .house-price {
                    color: #FF2525;
                    font-size: 16px;
                  }
                }
              }

              .house-content-type7 {
                cursor: pointer;

                .house-img {
                  width: 220px;
                  max-width: 100%;
                  height: 155px;
                }

                .house-box {
                  max-width: 220px;
                  display: flex;
                  flex-direction: column;

                  .house-title {
                    color: #252525;
                    font-weight: bold;
                  }

                  .house-intr {
                    color: #7E7E7E;
                  }

                  .house-price-box {
                    display: flex;
                    align-items: center;
                    font-size: 14px;

                    .house-price1 {
                      color: $styleColor;
                    }

                    .house-price2 {
                      color: #7EFA00;
                      margin-left: 6px;
                      min-width: 0;
                      flex: 1;
                    }
                  }

                  .house-price3 {
                    color: #7E7E7E;
                  }
                }
              }

              .card-content-type {
                width: 320px;
                max-width: 100%;
                cursor: pointer;

                .card-box {
                  width: 100%;
                  padding: 10px;

                  .card-title {
                    width: 100%;
                    font-size: 14px;
                    color: #252525;
                  }

                  .card-content-box {
                    width: 100%;
                    height: 54px;
                    display: flex;

                    .card-img {
                      width: 58px;
                      height: 58px;
                      margin-right: 10px;
                      flex-shrink: 0;
                    }

                    .card-content {
                      flex: 1;
                      color: #999999;
                      line-height: 18px;
                      word-break: break-all;
                      -webkit-line-clamp: 3;
                      -webkit-box-orient: vertical;
                      white-space: normal;
                      display: -webkit-box;
                    }
                  }
                }

                .card-intr-box {
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  border-top: 1px solid #e8e8e8;
                  height: 30px;
                  padding: 0px 10px;
                  color: #999999;

                  .card-intr {
                    max-width: calc(100% - 30px);
                  }
                }
              }

              .msg-collect-type {
                .file-info-box {
                  cursor: pointer;
                }
              }

              .schedule-invite-type {
                width: 278px;
                max-width: 100%;
                border-radius: 4px;
                overflow: hidden;
                line-height: normal;

                .schedule-invite-title-box {
                  padding: 16px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #FFFFFF;
                  line-height: 22px;
                  background-color: $styleColor;
                  background-image: url(/img/schedule/smart_card_bg.png);
                  background-repeat: no-repeat;
                  background-size: 278px 74px;

                  .schedule-invite-title {
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    white-space: normal;
                    display: -webkit-box;

                    .schedule-update {
                      height: 18px;
                      line-height: 18px;
                      font-size: 12px;
                      font-weight: 500;
                      color: $styleColor;
                      background: #FFFFFF;
                      border-radius: 2px;
                      padding: 0 4px;
                      margin-right: 5px;
                      display: inline;
                      vertical-align: bottom;
                    }

                    .schedule-title {
                      display: inline;
                    }
                  }
                }

                .schedule-invite-content-box {
                  margin-top: 10px;

                  &.not-btn-box {
                    padding-bottom: 10px;
                  }

                  .schedule-invite-time {
                    padding-left: 42px;
                    background-image: url(/img/schedule/smart-card-time.png);
                    background-repeat: no-repeat;
                    background-size: 16px 16px;
                    background-position: 17px center;

                    .schedule-update {
                      line-height: 18px;
                      color: $styleColor;
                      background: #FEF4F3;
                      border-radius: 1px;
                      padding: 0 4px;
                      margin-left: 5px;
                      vertical-align: bottom;
                    }
                  }

                  .schedule-invite-conflict {
                    padding-left: 42px;
                    color: #FF9E00;
                    margin-top: 4px;
                  }

                  .schedule-invite-more {
                    display: inline-block;
                    padding: 0 10px 0 42px;
                    color: $styleColor;
                    margin-top: 5px;
                    cursor: pointer;
                    position: relative;

                    &::after {
                      position: absolute;
                      content: "";
                      width: 5px;
                      height: 5px;
                      border-top: 1px solid $styleColor;
                      border-right: 1px solid $styleColor;
                      right: 1px;
                      top: 50%;
                      transform: rotate(45deg) translateY(-50%);
                    }
                  }
                }

                .schedule-invite-intr-box {
                  margin-top: 10px;
                  border-top: 1px solid #EDEDED;
                  padding: 10px 16px;

                  .schedule-invite-intr-detail {
                    color: #666666;
                    margin-top: 4px;
                  }
                }

                .schedule-invite-btn-box {
                  margin: 16px;

                  .schedule-invite-btn-content {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .schedule-invite-status {
                      width: 78px;
                      height: 30px;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      border-radius: 2px;
                      border: 1px solid #E7E7E7;
                      cursor: pointer;
                      font-size: 13px;
                      box-sizing: border-box;
                      white-space: nowrap;

                      &.schedule-invite-accept.sel {
                        color: #2D91E6;
                        border: 1px solid #2D91E6;

                        i {
                          background-position: -72px 0;
                        }
                      }

                      &.schedule-invite-refuse.sel {
                        color: #EE3939;
                        border: 1px solid #EE3939;

                        i {
                          background-position: -84px 0;
                        }
                      }

                      &.schedule-invite-upcoming.sel {
                        color: #FF9100;
                        border: 1px solid #FF9100;

                        i {
                          background-position: -96px 0;
                        }
                      }

                      &:hover {
                        &.schedule-invite-accept {
                          color: #2D91E6;
                          border: 1px solid #2D91E6;
                        }

                        &.schedule-invite-refuse {
                          color: #EE3939;
                          border: 1px solid #EE3939;
                        }

                        &.schedule-invite-upcoming {
                          color: #FF9100;
                          border: 1px solid #FF9100;
                        }
                      }

                      i {
                        display: none;
                        width: 12px;
                        height: 12px;
                        margin-right: 6px;
                        background-image: url(/img/schedule/smart-card-status.png);
                        background-repeat: no-repeat;
                        background-size: 108px 12px;
                        background-position: 0 0;
                      }

                      &.sel {
                        i {
                          display: block;
                        }
                      }

                    }
                  }

                  .schedule-invite-btn-add {
                    border-radius: 2px;
                    border: 1px solid #E7E7E7;
                    line-height: 30px;
                    text-align: center;
                    cursor: pointer;
                  }
                }
              }

              .schedule-remind-type {
                width: 351px;
                max-width: 100%;

                .schedule-remind-box {
                  padding: 10px;

                  .schedule-remind-title {
                    font-size: 16px;
                    color: #333333;
                    line-height: 22px;
                    margin-bottom: 10px;
                  }

                  .schedule-remind-content-box {
                    display: flex;
                    margin-bottom: 4px;
                    line-height: normal;

                    .schedule-remind-content-title {
                      min-width: 52px;
                      text-align: right;
                      color: #666666;
                      margin-right: 10px;
                      flex-shrink: 0;
                      display: flex;
                      justify-content: space-between;
                    }
                  }

                  .schedule-remind-more {
                    display: inline-block;
                    padding-right: 10px;
                    margin-top: 6px;
                    color: $styleColor;
                    cursor: pointer;
                    position: relative;

                    &:after {
                      position: absolute;
                      content: "";
                      width: 5px;
                      height: 5px;
                      border-top: 1px solid $styleColor;
                      border-right: 1px solid $styleColor;
                      right: 1px;
                      top: 50%;
                      transform: rotate(45deg) translateY(-50%);
                    }
                  }
                }

                .schedule-remind-btn-box1,
                .schedule-remind-btn-box2 {
                  border-top: 1px solid #EEEEEE;
                  padding: 10px;
                  line-height: normal;

                  .schedule-remind-btn-intr {
                    color: #666666;
                  }

                  .schedule-remind-btn {
                    margin-left: 10px;
                    color: #000000;
                    cursor: pointer;

                    &:hover {
                      color: $styleColor;
                    }
                  }
                }

                .schedule-remind-btn-box2 {
                  color: #FF942D;
                }
              }

              .answer-content-type {
                font-size: 14px;

                .answer-title-bold {
                  margin: 5px 0;
                  font-weight: bold;
                }

                .answer-list {
                  margin-bottom: 2px;
                }
              }

              .doc-content-type {
                width: 278px;
                max-width: 100%;

                .doc-box {
                  padding: 10px;

                  .doc-title {
                    font-size: 16px;
                    line-height: 22px;
                  }

                  .doc-content {
                    font-size: 13px;
                    color: #666666;
                    line-height: 16px;
                    margin: 10px 0;

                    ::v-deep(.doc-highlight) {
                      color: #2D91E6;
                      cursor: pointer;
                    }
                  }

                  .doc-intr {
                    background: #F5F5F5;
                    font-size: 13px;
                    padding: 6px 8px;
                  }
                }

                .doc-btn {
                  font-size: 14px;
                  color: #2D91E6;
                  line-height: 36px;
                  text-align: center;
                  border-top: 1px solid #EEEEEE;
                  cursor: pointer;
                }
              }

              .sell-content-type {
                width: 456px;
                max-width: 100%;

                &.sell-content-type-style {
                  .sell-header {
                    background-image: url("/img/index/msg/sell_bg1.png");
                    color: #FFE4BB;

                    .sell-header-time-box {
                      align-items: flex-end;
                    }
                  }

                  .sell-content {
                    margin-top: -10px;
                  }
                }

                &.sell-new {
                  .sell-header {
                    background-image: url("/img/index/msg/sell_bg2.png");
                    color: #FFFFFF;
                  }

                  .sell-content {
                    background: linear-gradient(180deg, #FFEED9 0px, #FFFFFF 46px);
                  }

                  .sell-phone-content {
                    margin-bottom: 4px !important;
                  }

                  .sell-label-box {
                    max-width: 50% !important;
                  }
                }

                &.multi-house {
                  .sell-header {
                    height: 60px;
                    font-size: 24px;
                    line-height: 28px;
                    padding: 16px;
                    background-image: url("/img/index/msg/sell_bg3.png");
                    color: #FFFFFF;
                  }

                  .sell-content {
                    padding: 0 16px 16px;
                    margin-top: 0;
                    background: #FFFFFF;
                  }

                  .sell-info-box {
                    height: 64px !important;
                    overflow: visible !important;

                    .sell-info-content {
                      width: 50% !important;

                      .sell-phone-title {
                        margin-bottom: 4px !important;
                      }

                      &.sell-qrcode-box {
                        &:after {
                          height: 40px !important;
                        }

                        .sell-qrcode {
                          width: 44px !important;
                          height: 44px !important;
                          margin: 0 10px !important;
                        }

                        .sell-qrcode-tips {
                          color: #666666;
                        }

                        .sell-qrcode-img-box {
                          position: relative;

                          &:hover {
                            .sell-qrcode-img-modal {
                              display: block;
                            }
                          }

                          .sell-qrcode-img-modal {
                            display: none;
                            padding: 12px;
                            position: absolute;
                            top: 50%;
                            left: 64px;
                            transform: translateY(-50%);
                            box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
                            border: 1px solid #CCCCCC;
                            background: #FFFFFF;
                            z-index: 1;

                            &:before,
                            &:after {
                              content: "";
                              position: absolute;
                              left: -7px;
                              top: 50%;
                              transform: translateY(-50%);
                              width: 0;
                              height: 0;
                              border-width: 6px 6px 6px 0;
                              border-style: solid;
                              border-color: transparent #CCCCCC transparent transparent;
                            }

                            &:before {
                              left: -7px;
                              border-color: transparent #CCCCCC transparent transparent;
                            }

                            &:after {
                              left: -6px;
                              border-color: transparent #FFFFFF transparent transparent;
                            }

                            img {
                              width: 130px;
                              height: 130px;
                            }

                            .sell-qrcode-img-modal-text {
                              font-size: 12px;
                              color: #666666;
                              line-height: 17px;
                              margin-top: 8px;
                              text-align: center;
                            }
                          }
                        }
                      }
                    }
                  }
                }

                .sell-header {
                  width: 100%;
                  height: 90px;
                  padding: 20px 16px 0;
                  display: flex;
                  justify-content: space-between;
                  align-items: flex-start;
                  background-image: url("/img/index/msg/sell_bg.png");
                  background-repeat: no-repeat;
                  background-size: 456px 90px;
                  white-space: nowrap;
                  overflow: hidden;
                  color: #FFFFFF;
                  border-top-left-radius: 4px;
                  border-top-right-radius: 4px;

                  .sell-header-info-box {
                    display: flex;
                    align-items: flex-end;

                    .sell-title {
                      font-size: 28px;
                      line-height: 32px;
                      font-weight: bold;
                      margin-top: -2px;
                    }

                    .sell-icon {
                      width: 23px;
                      height: 23px;
                      background: url("/img/index/msg/sell_icon.png") no-repeat;
                      background-size: 100%;
                      margin: 0 2px 0 10px;
                    }

                    .sell-img-box {
                      display: flex;
                      align-items: flex-end;
                      flex-shrink: 0;

                      .user-avatar-box {
                        width: 40px !important;
                        height: 40px !important;
                        border-radius: 0 !important;
                        margin-right: 0 !important;;

                        @for $i from 0 to 4 {
                          &:nth-child(#{$i + 1}) {
                            z-index: #{4 - $i};
                          }
                        }

                        &:nth-child(n+2) {
                          margin-left: -10px !important;;
                        }

                        &:nth-child(n+3) {
                          width: 28px !important;
                          height: 28px !important;
                        }

                        .avatar-box {
                          border-radius: 2px !important;
                          border: 1px solid #FFFFFF;
                        }
                      }
                    }
                  }

                  .sell-header-time-box {
                    display: flex;
                    height: 40px;

                    .sell-header-time-start {
                      height: 100%;
                      display: flex;
                      align-items: flex-start;

                      .sell-header-time-start-content {
                        display: flex;
                        align-items: center;

                        .sell-header-time-num {
                          display: flex;
                          justify-content: center;
                          align-items: center;
                          flex-shrink: 0;
                          width: 26px;
                          height: 26px;
                          border-radius: 2px;
                          background: rgba(255, 228, 187, 0.4);
                          margin: 0 4px;
                          overflow: hidden;
                        }
                      }
                    }

                    .sell-header-time-end {
                      line-height: 28px;
                      opacity: 0.7;
                    }
                  }
                }

                .sell-content {
                  padding: 12px 16px 16px;
                  margin-top: -20px;
                  border-top-left-radius: 4px;
                  border-top-right-radius: 4px;
                  background: linear-gradient(180deg, #FFDED9 0px, #FFFFFF 46px);

                  .sell-house-list:not(:first-child) {
                    margin-top: 14px;
                    padding-top: 14px;
                    border-top: 1px solid #F4F4F4;
                  }

                  .sell-to-link {
                    cursor: pointer;
                  }

                  .sell-content-info-box {
                    width: 100%;
                    line-height: 22px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .sell-content-info {
                      width: calc(100% - 60px);
                      display: flex;
                      align-items: center;

                      .sell-content-info-title {
                        max-width: 50%;
                        font-size: 16px;
                        color: #333333;
                        font-weight: bold;
                        margin-right: 4px;
                      }

                      .sell-content-info-intr {
                        color: #666666;
                        line-height: 17px;
                        flex: 1;
                      }
                    }
                  }

                  .sell-content-info-more {
                    color: #3D5688;
                    position: relative;
                    padding-right: 10px;
                    flex-shrink: 0;
                    font-weight: bold;

                    &:after {
                      content: "";
                      position: absolute;
                      top: 52%;
                      right: 0;
                      width: 4px;
                      height: 4px;
                      border-top: 2px solid #3D5688;
                      border-right: 2px solid #3D5688;
                      transform: translateY(-50%) rotate(45deg);
                    }
                  }

                  .label-price-box {
                    display: flex;
                    align-items: center;
                    margin-top: 4px;

                    .sell-label-box {
                      margin-top: 0;
                      flex-shrink: 0;
                      max-width: 100%;
                    }

                    .sell-price-box {
                      color: $styleColor;
                      flex-shrink: 0;
                      flex: 1;
                    }
                  }

                  .sell-label-box {
                    display: flex;
                    align-items: flex-end;
                    margin-top: 4px;
                    overflow: hidden;

                    .sell-label {
                      color: $styleColor;
                      padding: 2px 5px;
                      background: #FFEBE9;
                      border-radius: 2px;
                      margin-right: 4px;
                      line-height: 17px;
                      white-space: nowrap;

                      &.sell-label-main {
                        background: linear-gradient(315deg, #FF2C3E 0%, #FE712D 100%);
                        color: #FFFFFF;
                        font-weight: bold;
                      }
                    }
                  }

                  .sell-audio {
                    height: 36px;
                    padding: 0 12px 0 8px;
                    display: inline-flex;
                    align-items: center;
                    font-size: 14px;
                    color: #333333;
                    background: #F9F9F9;
                    border-radius: 4px;
                    border: 1px solid #BFBFBF;
                    margin-top: 12px;
                    cursor: pointer;

                    .sell-audio-icon {
                      width: 20px;
                      height: 20px;
                      background-image: url(/img/index/msg/sell_icon_paly.png);
                      background-repeat: no-repeat;
                      background-size: 100%;
                      flex-shrink: 0;

                      &.stop {
                        background-image: url(/img/index/msg/sell_icon_stop.png);
                      }
                    }

                    span {
                      flex-shrink: 0;
                    }

                    .sell-audio-time {
                      margin: 0 4px 0 8px;
                    }
                  }

                  .sell-main-title {
                    font-size: 14px;
                    color: #333333;
                    font-weight: bold;
                    line-height: 20px;
                    margin-top: 8px;
                    padding-left: 6px;
                    position: relative;

                    &:after {
                      content: "";
                      position: absolute;
                      top: 4px;
                      left: 0;
                      width: 2px;
                      height: 12px;
                      background: $styleColor;
                    }
                  }

                  .sell-intr {
                    font-size: 14px;
                    color: #333333;
                    line-height: 20px;
                    margin-top: 10px;

                    &.sell-main-intr {
                      margin-top: 4px;
                    }
                  }

                  .sell-video-img-box {
                    width: 100%;
                    overflow: hidden;
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    margin-top: 2px;

                    &.sell-video-img-box-1 {
                      height: 254px;
                      margin-top: 10px;

                      .sell-video-img-content {
                        width: 100%;
                        margin: 0 !important;

                        .sell-img-box {
                          height: 254px;
                        }
                      }
                    }

                    .sell-video-img-content {
                      width: calc(50% - 4px);
                      display: flex;
                      justify-content: space-between;
                      flex-wrap: wrap;
                      margin-top: 8px;

                      &:nth-child(2n+1) {
                        margin-right: 8px;
                      }

                      .sell-img-box {
                        width: 100%;
                        height: 156px;
                        border-radius: 2px;
                        overflow: hidden;
                        position: relative;
                        flex-shrink: 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        background: url("/img/fang_default.png") no-repeat;
                        background-size: 100%;

                        .sell-img {
                          user-select: text !important;
                          width: 100%;
                          height: 100%;
                          object-fit: cover;
                        }

                        .sell-video-icon {
                          width: 52px;
                          height: 50px;
                          position: absolute;
                          top: 50%;
                          left: 50%;
                          transform: translate(-50%, -50%);
                          cursor: pointer;
                        }
                      }
                    }
                  }

                  .sell-info-box {
                    height: 110px;
                    position: relative;
                    display: flex;
                    align-items: center;
                    background: #F5F5F5;
                    border-radius: 2px;
                    margin-top: 10px;
                    font-size: 14px;
                    color: #666666;
                    line-height: 20px;
                    padding: 0 8px;
                    overflow: hidden;

                    .sell-info-content {

                      &.sell-phone-box {
                        width: 51.4%;

                        .sell-phone-title {
                          color: #666666;
                          margin-bottom: 2px;

                          &.sell-phone-title-1 {
                            margin-bottom: 8px;
                          }
                        }

                        .sell-phone-content {
                          display: flex;
                          align-items: center;
                          margin-bottom: 2px;

                          &:last-child {
                            margin-bottom: 0;
                          }

                          .sell-phone-type {
                            background: #E7E7E7;
                            border-radius: 1px;
                            margin-right: 10px;
                            max-width: 40px;
                            padding: 1px 2px;
                            font-size: 12px;
                          }

                          .sell-phone-name {
                            width: 56px;
                            color: #333333;
                          }

                          .sell-phone {
                            color: #3D5688;
                            margin-left: 6px;
                          }
                        }
                      }

                      &.sell-qrcode-box {
                        width: 48.6%;
                        display: flex;
                        align-items: center;
                        position: relative;

                        &:after {
                          content: "";
                          position: absolute;
                          top: 50%;
                          left: 0;
                          transform: translateY(-50%);
                          width: 1px;
                          height: 80px;
                          background: #E7E7E7;
                        }

                        .sell-qrcode {
                          width: 100px;
                          height: 100px;
                          margin: 0 4px 0 10px;
                          flex-shrink: 0;
                        }

                        .sell-qrcode-tips {
                          font-size: 13px;
                        }
                      }
                    }
                  }

                  .sell-share-box {
                    width: 100%;
                    height: 36px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 4px;
                    border: 1px solid #CCCCCC;
                    font-size: 14px;
                    font-weight: bold;
                    margin-top: 16px;
                    cursor: pointer;

                    .sell-share-icon {
                      width: 20px;
                      margin-right: 6px;
                    }
                  }

                  .sell-multi-house-tips {
                    font-size: 14px;
                    color: #999990;
                    line-height: 22px;
                    text-align: center;
                  }

                  .sell-multi-house-list {
                    position: relative;
                    padding: 10px 0;
                    border-bottom: 1px solid #F4F4F4;

                    &.last {
                      border-bottom: none;
                    }

                    .sell-multi-house-box {
                      display: flex;

                      .sell-multi-house-info-box {
                        width: 100%;

                        .sell-multi-house-info {
                          display: flex;
                          justify-content: space-between;
                          align-items: center;
                          min-width: 0;

                          .sell-multi-house-info-text {
                            display: flex;
                            align-items: center;
                            min-width: 0;

                            .sell-multi-house-title {
                              min-width: 50px;
                              padding-right: 10px;
                              font-size: 16px;
                              color: #333333;
                              font-weight: bold;
                              line-height: 20px;
                              flex: 1;
                            }

                            .sell-multi-house-intr {
                              color: #666666;
                              line-height: 17px;

                              span {
                                position: relative;
                                padding-right: 9px;

                                &:not(:last-child):after {
                                  content: "";
                                  position: absolute;
                                  top: 50%;
                                  right: 4px;
                                  transform: translateY(-50%);
                                  width: 1px;
                                  height: 10px;
                                  background: #D4D4D4;
                                }
                              }
                            }
                          }

                          .show-arrow {
                            flex-shrink: 0;

                            &:before {
                              border-color: transparent transparent transparent #999999;
                            }
                          }
                        }

                        .sell-multi-house-content {
                          font-size: 12px;
                          color: #333333;
                          line-height: 17px;
                          margin-top: 6px;
                        }
                      }
                    }

                    .sell-multi-house-modal {
                      position: absolute;
                      bottom: 0;
                      left: 0;
                      width: 100%;
                      height: 80px;
                      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%);
                    }
                  }

                  .sell-multi-share-box {
                    display: flex;
                    align-items: center;
                    margin-top: 10px;

                    .sell-multi-btn {
                      height: 36px;
                      flex: 1;
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      font-size: 14px;
                      color: #000000;
                      background: #FFFFFF;
                      border-radius: 4px;
                      border: 1px solid #CCCCCC;
                      cursor: pointer;

                      &:nth-child(2) {
                        color: $styleColor;
                        border: 1px solid $styleColor;
                        margin-left: 10px;
                      }
                    }
                  }
                }
              }

              .card-type-4,
              .card-type {
                width: 278px;
                max-width: 100%;
                border-radius: 4px;
                overflow: hidden;
                line-height: normal;

                .card-header {
                  padding: 16px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #FFFFFF;
                  line-height: 22px;
                  background-color: $styleColor;
                  background-image: url(/img/schedule/smart_card_bg.png);
                  background-repeat: no-repeat;
                  background-size: 278px 74px;
                }

                .card-content {
                  padding: 8px 16px;

                  &.card-content-max {
                    .card-info-title {
                      width: 64px !important;
                    }
                  }

                  .card-info-box {
                    margin-bottom: 4px;

                    &:last-child {
                      margin-bottom: 0;
                    }

                    .card-info-box1 {
                      display: flex;
                      line-height: 17px;

                      .card-info-title {
                        width: 52px;
                        color: #999999;
                        margin-right: 10px;
                        display: inline-block;
                        text-align: justify;
                        text-align-last: justify;
                        overflow: hidden;
                        white-space: nowrap;
                        flex-shrink: 0;
                      }

                      ::v-deep(.card-info-details) {
                        line-height: 17px;
                      }

                      .card-info-details-link {
                        color: #3188E8;
                        cursor: pointer;
                      }
                    }
                  }

                  .card-link {
                    display: inline-block;
                    padding-right: 10px;
                    line-height: 16px;
                    color: $styleColor;
                    cursor: pointer;
                    position: relative;

                    &:after {
                      position: absolute;
                      content: "";
                      width: 5px;
                      height: 5px;
                      border-top: 1px solid $styleColor;
                      border-right: 1px solid $styleColor;
                      right: 3px;
                      top: 50%;
                      transform: rotate(45deg) translateY(-50%);
                    }
                  }
                }

                .card-footer {
                  padding: 0px 16px 16px;
                  display: flex;
                  align-items: center;

                  .card-btn {
                    height: 30px;
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border: 1px solid #E7E7E7;
                    cursor: pointer;
                    margin-left: 6px;
                    border-radius: 2px;
                    overflow: hidden;

                    &:first-child {
                      margin-left: 0;
                    }

                    &:hover {
                      border: 1px solid #2D91E6;
                      color: #2D91E6;
                    }
                  }
                }
              }

              .ylyk-type {
                width: 278px;
                max-width: 100%;
                padding: 16px;

                .ylyk-title {
                  font-size: 16px;
                  font-weight: bold;
                  color: #333333;
                  line-height: 22px;
                }

                .ylyk-intr-box {
                  display: flex;
                  margin-top: 4px;
                  cursor: pointer;

                  .ylyk-intr-content {
                    position: relative;
                    flex: 1;
                    color: #666666;
                    line-height: 18px;

                    .ylyk-intr-label {
                      position: absolute;
                      top: 0;
                      left: 0;
                      width: 32px;
                      text-align: center;
                      color: #3D5688;
                      background: #F5F7FA;
                      border-radius: 2px;
                    }

                    .ylyk-intr-text {
                      text-indent: 33px;
                    }
                  }

                  .ylyk-intr-img {
                    width: 66px;
                    height: 50px;
                    margin-left: 9px;
                    border-radius: 4px;
                    flex-shrink: 0;
                  }
                }

                .ylyk-app-name-box {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-top: 8px;

                  .ylyk-app-name {
                    display: flex;
                    align-items: center;
                    color: #999999;

                    img {
                      width: 20px;
                      margin-right: 4px;
                    }
                  }

                  .ylyk-app-more {
                    color: #2D91E6;
                    cursor: pointer;
                  }
                }

                .ylyk-qrcode-box {
                  display: flex;
                  align-items: center;
                  height: 96px;
                  line-height: 18px;
                  padding: 0 8px;
                  background: #F4F4F4;
                  border-radius: 2px;
                  color: #999999;
                  margin-top: 12px;

                  .ylyk-qrcode {
                    width: 80px;
                    height: 80px;
                    margin-right: 8px;
                    flex-shrink: 0;
                  }

                  .ylyk-qrcode-text {
                    height: 80px;
                  }
                }
              }

              .im-news-type {
                width: 456px;
                max-width: 100%;

                .im-news-title-box {
                  display: flex;
                  justify-content: space-between;
                  padding: 12px 16px;
                  background-image: url("/img/index/msg/bg_news.png");
                  background-repeat: no-repeat;
                  background-size: 100% 100%;
                  border-top-left-radius: 4px;
                  border-top-right-radius: 4px;

                  .im-news-title {
                    flex: 1;
                    font-size: 28px;
                    font-weight: bold;
                    line-height: 40px;
                    color: #FFFFFF;
                  }

                  .im-news-btn {
                    flex-shrink: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 80px;
                    height: 30px;
                    background: #FFEBE9;
                    border-radius: 2px;
                    font-size: 14px;
                    color: $styleColor;
                    margin-top: 5px;
                    cursor: pointer;
                  }
                }

                .im-news-content-box {
                  padding-bottom: 16px;

                  .im-news-content {
                    position: relative;

                    &:after {
                      content: "";
                      position: absolute;
                      bottom: 0;
                      left: 0;
                      width: 100%;
                      height: 30px;
                      background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255) 100%);
                    }

                    .im-news-info-box {
                      .im-news-img-box {
                        width: 100%;
                        max-height: 520px;
                        overflow: hidden;

                        .im-news-img {
                          user-select: text !important;
                          width: 100%;
                          height: 100%;
                          object-fit: cover;
                        }
                      }

                      .im-news-text {
                        padding: 16px 16px 0;
                        font-size: 14px;
                        color: #333333;
                        line-height: 20px;
                      }
                    }
                  }

                  .im-news-qrcode-box {
                    position: relative;
                    display: flex;
                    align-items: center;
                    height: 96px;
                    background: #F5F5F5;
                    border-radius: 2px;
                    margin: 8px 16px 0;

                    &.im-news-qrcode-box-wx {
                      &:after {
                        content: "";
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 1px;
                        height: 70px;
                        background: #E7E7E7;
                      }
                    }

                    .im-news-qrcode-content {
                      width: 50%;
                      height: 100%;
                      display: flex;
                      align-items: center;

                      &.im-news-qrcode-content-old {
                        width: 100%;

                        .im-news-qrcode-img-box {
                          margin: 0 16px;
                        }

                        .im-news-qrcode-text-box {
                          flex: 1;
                          max-height: 100%;
                          font-size: 16px;
                          color: #666666;
                          line-height: 22px;
                          margin-left: 16px;
                          overflow: hidden;
                        }
                      }

                      .im-news-qrcode-img-box {
                        position: relative;
                        flex-shrink: 0;
                        width: 80px;
                        height: 80px;
                        margin: 0 8px;

                        .im-news-qrcode-img {
                          width: 100%;
                        }

                        .im-news-qrcode-icon {
                          position: absolute;
                          top: 50%;
                          left: 50%;
                          transform: translate(-50%, -50%);
                          width: 20px;
                          height: 20px;
                          background-image: url(/img/index/msg/qrcode_icon.png);
                          background-repeat: no-repeat;
                          background-size: 40px 20px;

                          &.icon-lbg {
                            background-position: -20px 0;
                          }
                        }
                      }
                    }

                    .im-news-qrcode-text-box {
                      .im-news-qrcode-title {
                        font-size: 14px;
                        font-weight: bold;
                        color: #333333;
                        line-height: 20px;
                      }

                      .im-news-qrcode-intr {
                        color: #666666;
                        line-height: 17px;
                        margin-top: 2px;
                      }
                    }

                  }

                  .im-news-share-box {
                    display: flex;
                    justify-content: center;
                    margin: 12px 16px 0;

                    .im-news-btn {
                      display: flex;
                      justify-content: center;
                      align-items: center;
                      width: 102px;
                      height: 36px;
                      margin-right: 10px;
                      border-radius: 4px;
                      border: 1px solid $styleColor;
                      color: $styleColor;
                      font-size: 14px;
                      cursor: pointer;

                      &:last-child {
                        width: 312px;
                        margin-right: 0;
                      }
                    }
                  }
                }
              }

              .ai-card-type {
                padding: 10px;

                .ai-title {
                  font-size: 16px;
                  font-weight: bold;
                  color: #252525;
                }

                .ai-intr {
                  font-size: 14px;
                  color: #333333;
                  margin-top: 8px;
                }

                .ai-app-box {
                  position: relative;
                  display: flex;
                  align-items: center;
                  border-radius: 4px;
                  width: calc(50% - 4px);
                  border: 1px solid #E0E0E0;
                  padding: 8px 6px;
                  margin-top: 8px;
                  cursor: pointer;

                  &:hover {
                    background: #F8F8F8;
                  }

                  &:nth-child(2n) {
                    margin-left: 8px;
                  }

                  .ai-app-icon-box {
                    position: relative;
                    width: 36px;
                    height: 36px;
                    margin-right: 8px;

                    .ai-app-icon {
                      width: 100%;
                      height: 100%;
                      object-fit: cover;
                    }
                  }

                  .ai-app-info {
                    flex: 1;
                    min-width: 0;

                    .ai-app-title {
                      font-size: 14px;
                      font-weight: bold;
                      color: #000000;
                      line-height: 20px;
                    }

                    .ai-app-intr {
                      color: #999999;
                      line-height: 17px;
                    }
                  }
                }

                .ai-app {
                  display: flex;
                  flex-wrap: wrap;
                  margin-bottom: 8px;

                  .ai-app-box {
                    margin-bottom: 8px;
                  }
                }

                .ai-other {
                  border-bottom: 1px solid transparent;

                  &:last-child {
                    border-bottom: 0;
                  }

                  &.ai-other-last {
                    .ai-other-ul {
                      &:last-child {
                        .ai-other-msg {
                          margin-bottom: 0;
                        }
                      }
                    }
                  }

                  .ai-other-title {
                    position: relative;
                    font-size: 14px;
                    color: #333333;
                    line-height: 20px;
                    margin: 8px 0;
                  }

                  .ai-other-ul {

                    &.ai-other-app {
                      display: flex;
                      flex-wrap: wrap;

                      .ai-app-box {
                        padding-right: 25px;

                        .arrow-right {
                          position: absolute;
                          top: 50%;
                          right: 4px;
                          transform: translateY(-50%);

                          &:before {
                            border-color: transparent transparent transparent #666666;
                          }

                          &:before,
                          &:after {
                            border-width: 5px 0 5px 5px;
                          }
                        }
                      }
                    }

                    &:last-child {
                      .ai-other-msg {
                        margin-bottom: 7px
                      }
                    }

                    .ai-other-msg {
                      position: relative;
                      display: inline-flex;
                      flex: 1;
                      min-width: 0;
                      color: #333333;
                      line-height: 18px;
                      margin-bottom: 8px;
                      padding: 0 14px 0 9px;
                      cursor: pointer;

                      .ai-other-text {
                        font-size: 13px;
                      }

                      .arrow-right {
                        position: absolute;
                        top: 50%;
                        right: 0;
                        transform: translateY(-50%);

                        &:before {
                          border-color: transparent transparent transparent #686868;
                        }

                        &:before,
                        &:after {
                          border-width: 5px 0 5px 5px;
                        }
                      }

                      &:hover {
                        color: $styleColor;

                        .arrow-right {
                          &:before {
                            border-color: transparent transparent transparent $styleColor;
                          }
                        }
                      }
                    }
                  }
                }

                .ai-msg-ul {
                  display: flex;
                  flex-wrap: wrap;

                  .ai-msg-li {
                    line-height: 16px;
                    padding: 4px 12px;
                    border-radius: 4px;
                    border: 1px solid $styleColor;
                    color: $styleColor;
                    margin: 10px 10px 0 0;
                    cursor: pointer;

                    &:hover {
                      background: #FFF3F3;
                    }

                    &:last-child {
                      margin-right: 0;
                    }
                  }
                }
              }

              .ai-msg-type {
                padding: 7px 10px;
                position: relative;
                min-height: 34px;

                span {
                  display: unset;
                }

                .ai-msg-intr {
                  display: block;
                  font-size: 14px;
                  color: #999999;
                  margin: 4px 0;
                }

                .ai-msg-link-box-wrap {
                  display: block;
                  margin: 4px 0;
                }

                .ai-msg-link {
                  font-size: 14px;
                  cursor: pointer;
                  color: $styleColor;
                }

                .ai-msg-link-wrap {
                  display: inline;
                  font-size: 14px;
                  color: #2D91E6;
                  border-bottom: 1px solid #2D91E6;
                  cursor: pointer;
                }

                .ai-msg-btn {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-size: 13px;
                  height: 30px;
                  border-radius: 2px;
                  border: 1px solid $styleColor;
                  color: $styleColor;
                  cursor: pointer;
                }

                .ai-option-box {

                  &.ai-option-box-0 {
                    .ai-option-title {
                      margin-top: 0px;
                    }
                  }

                  .ai-option-title {
                    display: block;
                    margin-top: 10px;
                    font-size: 14px;
                  }

                  .ai-option-text {
                    display: flex;
                    align-items: center;
                    margin-top: 8px;
                    font-size: 14px;
                    cursor: pointer;

                    &:hover {
                      color: $styleColor;

                      .show-arrow {
                        &:before {
                          border-color: transparent transparent transparent $styleColor;
                        }
                      }
                    }

                    .show-arrow {
                      &:before,
                      &:after {
                        border-width: 5px 0 5px 5px;
                      }
                    }
                  }
                }

                .ai-btn-box {
                  display: flex;
                  align-items: center;

                  .ai-btn {
                    flex: 1;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    font-size: 13px;
                    height: 30px;
                    margin: 10px 0 0 10px;
                    padding: 0 12px;
                    border-radius: 2px;
                    border: 1px solid $styleColor;
                    color: $styleColor;
                    cursor: pointer;

                    &:hover {
                      background: #FFF3F3;
                    }

                    &:nth-child(1) {
                      margin-left: 0;
                    }
                  }
                }

                .ai-video {
                  display: block;
                  font-size: 14px;
                  margin-top: 5px;
                }
              }

              .ai-app-card-type {
                width: 278px;
                max-width: 100%;
                border-radius: 4px;
                overflow: hidden;

                .card-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 16px;
                  font-weight: 600;
                  font-size: 16px;
                  color: #FFFFFF;
                  line-height: 22px;
                  background: linear-gradient(315deg, #7181FD 0%, #9E71FF 100%);

                  .user-avatar-box {
                    width: 28px !important;
                    height: 28px !important;
                    margin: 0 0 0 10px !important;

                    .avatar-box {
                      cursor: default !important;
                    }
                  }
                }

                .card-content {
                  padding: 4px 16px 16px;

                  .card-ul {
                    .card-li {
                      display: flex;
                      margin-top: 4px;

                      .intr {
                        flex-shrink: 0;
                        margin-right: 10px;
                        color: #666666;
                      }

                      .detail {

                      }
                    }
                  }

                  .card-btn {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    height: 30px;
                    background: #FFFFFF;
                    color: #333333;
                    border-radius: 2px;
                    border: 1px solid #E7E7E7;
                    margin-top: 8px;
                    cursor: pointer;
                  }
                }
              }

              .cooperate-type {
                width: 350px;
                max-width: 100%;
                padding: 10px;

                .cooperate-main-title {
                  font-weight: bold;
                  font-size: 16px;
                  color: #252525;
                  line-height: 22px;
                  margin-bottom: 4px;
                }

                .cooperate-main-intr {
                  color: #666666;
                  line-height: 17px;
                }

                .cooperate-detail-ul {
                  font-size: 14px;
                  margin-top: 10px;

                  .cooperate-detail-li {
                    margin-bottom: 4px;
                    display: flex;
                    line-height: 20px;

                    .cooperate-intr {
                      display: flex;
                      justify-content: space-between;
                      width: 60px;
                      margin-right: 10px;
                      flex-shrink: 0;
                      color: #999999;
                    }

                    .cooperate-detail {
                      color: #333333;
                    }

                    &:last-child {
                      margin-bottom: 0;
                    }
                  }
                }

                .cooperate-intr-text {
                  color: #999999;
                  margin-top: 10px;
                  line-height: 17px;
                }

                .cooperate-btn {
                  width: 100%;
                  height: 30px;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-top: 10px;
                  border-radius: 2px;
                  border: 1px solid $styleColor;
                  font-size: 13px;
                  color: $styleColor;
                  cursor: pointer;
                }
              }

              .lxt-signup-type {
                width: 278px;
                max-width: 100%;
                background: #FFFFFF;
                border-radius: 4px;

                .lxt-signup-title-box {
                  padding: 16px;
                  font-size: 16px;
                  font-weight: 600;
                  color: #FFFFFF;
                  line-height: 22px;
                  background-color: $styleColor;
                  background-image: url(/img/schedule/smart_card_bg.png);
                  background-repeat: no-repeat;
                  background-size: 278px 74px;
                }

                .lxt-signup-ul {
                  padding: 8px 16px 0;
                  line-height: 17px;

                  .lxt-signup-li {
                    display: flex;
                    margin-bottom: 4px;

                    .lxt-sign-flex-1 {
                      display: flex;
                      width: 50%;
                    }

                    .lxt-sign-intr {
                      color: #666666;
                      margin-right: 8px;
                      flex-shrink: 0;
                    }
                  }
                }

                .lxt-signup-btn {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex: 1;
                  height: 30px;
                  background: #FFFFFF;
                  border-radius: 2px;
                  border: 1px solid $styleColor;
                  color: $styleColor;
                  margin: 10px 16px 12px;
                  cursor: pointer;
                }
              }

            }

            .msg-tying-input {
              animation: blink 1s infinite;
              @keyframes blink {
                0% {
                  opacity: 0;
                }
                50% {
                  opacity: 1;
                }
                100% {
                  opacity: 0;
                }
              }
            }

            .msg-operate-box {
              position: absolute;
              padding: 0 2px;
              left: -100px;
              bottom: 0;
              z-index: 1;

              &.show-fixed {
                left: 0;
                bottom: -24px;
              }

              .msg-operate-ul {
                display: none;

                &.show {
                  display: flex;
                }

                .msg-operate-li {
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  flex-shrink: 0;
                  width: 24px;
                  height: 24px;
                  background: #FFFFFF;
                  border: 1px solid #E0E0E0;
                  border-right: 0;
                  position: relative;

                  &:first-child {
                    border-top-left-radius: 2px;
                    border-bottom-left-radius: 2px;
                  }

                  &:last-child {
                    border-top-right-radius: 2px;
                    border-bottom-right-radius: 2px;
                    border-right: 1px solid #E0E0E0;
                  }

                  .icon-msg-operate {
                    width: 14px;
                    height: 14px;
                    background-image: url("/img/index/msg/icon_msg_operate.png");
                    background-repeat: no-repeat;
                    background-size: 252px 14px;
                    cursor: pointer;

                    &.audio {
                      background-position: 0 0;
                    }

                    &.approve {
                      background-position: -28px 0;
                    }

                    &.oppose {
                      background-position: -56px 0;
                    }

                    &.detail {
                      background-position: -84px 0;
                    }

                    &.copy {
                      background-position: -112px 0;
                    }

                    &.quote {
                      background-position: -140px 0;
                    }

                    &.forward {
                      background-position: -168px 0;
                    }

                    &.multi {
                      background-position: -196px 0;
                    }

                    &.more {
                      background-position: -224px 0;
                    }

                    &:hover {
                      &.audio {
                        background-position: -14px 0;
                      }

                      &.approve {
                        background-position: -42px 0;
                      }

                      &.oppose {
                        background-position: -70px 0;
                      }

                      &.detail {
                        background-position: -98px 0;
                      }

                      &.copy {
                        background-position: -126px 0;
                      }

                      &.quote {
                        background-position: -154px 0;
                      }

                      &.forward {
                        background-position: -182px 0;
                      }

                      &.multi {
                        background-position: -210px 0;
                      }

                      &.more {
                        background-position: -238px 0;
                      }
                    }
                  }
                }
              }
            }
          }

          .msg-icon-position {
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
          }

          .msg-audio-point {
            width: 6px;
            height: 6px;
            background: #f74b32;
            border-radius: 50%;
            display: inline-block;
            flex-shrink: 0;
            margin-left: 10px;
          }

          .msg-read {
            height: 100%;
            display: flex;
            flex-shrink: 0;
            align-items: flex-end;
            white-space: nowrap;
            color: #999999;
            top: auto;
            bottom: 0px;
            transform: none;
          }

          .msg-loading {
            flex-shrink: 0;
            width: 18px;
            height: 18px;
            animation: myLoading 800ms linear infinite;
            background: url("/img/index/msg/loading.png") no-repeat center center;
            background-size: 18px 18px;
            margin-top: -9px;
          }

          .msg-stop {
            display: flex;
            align-items: center;
            border-radius: 4px;
            color: #666666;
            line-height: 18px;
            padding: 4px 8px;
            background-color: #E7E7E7;
            margin-left: 8px;
            cursor: pointer;

            &:hover {
              background-color: #DFDFDF;
            }

            .icon-ai {
              background-position: -112px 0;
              margin-right: 4px;
              flex-shrink: 0;
            }
          }
        }

        .msg-quote-box {
          display: inline-flex;
          background: #E7E7E7;
          border-radius: 2px;
          font-size: 12px;
          line-height: 17px;
          font-weight: 400;
          color: #666666;
          padding: 7px 10px;
          margin-top: 4px;
          cursor: pointer;
          position: relative;
          max-width: 60%;
          border-radius: 4px;

          .quote-text {
            font-size: 13px;
            word-break: break-all;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            white-space: normal;
            display: -webkit-box;
            flex: 1;
          }

          .quote-img-box {
            display: flex;
            align-items: center;

            .quote-img {
              max-width: 48px;
              max-height: 36px;
              margin-left: 4px;
              flex-shrink: 0;
            }

            .quote-more {
              width: 14px;
              height: 14px;
              background-image: url(/img/index/msg/quote_icon.png);
              background-repeat: no-repeat;
              background-size: 38px 14px;
              background-position: -10px 0;
              flex-shrink: 0;
            }
          }
        }

        .msg-err {
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #999;
          margin-top: 10px;

          .resend {
            color: $styleColor;
            cursor: pointer;
          }
        }
      }

      &.msg-me {
        flex-direction: row-reverse;

        .msg-audio {
          &.play {
            .audio-icon {
              background-image: url("/img/index/msg/audio_right_animated.png");
            }
          }

          .audio-icon {
            width: 12px;
            height: 17px;
            background-image: url("/img/index/msg/audio_right.png");
            background-repeat: no-repeat;
            background-size: 12px 17px;
          }
        }

        .user-avatar-box,
        ::v-deep(.user-avatar-box) {
          margin-right: 0;
          margin-left: 10px;
        }

        .msg-box {
          display: flex;
          flex-direction: column;
          align-items: flex-end;

          .user-name {
            display: none;
          }

          .msg-content-box {
            flex-direction: row-reverse;

            .msg-content {
              background: $styleMsg;

              &:after {
                left: auto;
                right: -12px;
                border-color: transparent transparent transparent $styleMsg;
              }
            }
          }
        }
      }

      &.msg-you {
        .msg-audio {
          align-items: flex-start !important;

          .audio-box {
            flex-direction: row-reverse;
          }

          &.play {
            .audio-icon {
              background-image: url("/img/index/msg/audio_left_animated.png");
            }
          }

          .audio-icon {
            width: 12px;
            height: 17px;
            background-image: url("/img/index/msg/audio_left.png");
            background-repeat: no-repeat;
            background-size: 12px 17px;
          }
        }

        .msg-operate-box {
          left: auto !important;
          right: -100px;

          &.msg-operate-box-1 {
            right: -28px
          }

          &.msg-operate-box-2 {
            right: -52px
          }

          &.msg-operate-box-3 {
            right: -76px
          }

          &.show-fixed {
            right: 0;
            bottom: -24px;
          }
        }
      }
    }

    .msg-li-custom-3,
    .msg-li-custom-5,
    .msg-li-custom-6,
    .msg-li-custom-7,
    .msg-li-custom-8,
    .msg-li-custom-9,
    .msg-li-custom-collect,
    .msg-li-custom-multi-file,
    .msg-li-custom-multi-robot,
    .msg-li-custom-problemList,
    .msg-li-custom-leToFangUser,
    .msg-li-custom-schedule_invite,
    .msg-li-custom-schedule_del,
    .msg-li-custom-schedule_remind,
    .msg-li-custom-document_remind,
    .msg-li-custom-imJZSell,
    .msg-li-custom-imNewHouseJZSell,
    .msg-li-custom-card-4,
    .msg-li-custom-jzxs-approval-notice,
    .msg-li-custom-minApp_YLYK_Detail,
    .msg-li-custom-imCourseShare,
    .msg-li-custom-imSomeHouseRecommend,
    .msg-li-custom-imHouseBatchShare,
    .msg-li-custom-imNews,
    .msg-li-custom-ai-card,
    .msg-li-custom-ai-msg,
    .msg-li-custom-cooperate,
    .msg-li-custom-lxt_signup,
    .msg-li-custom-monad-card,
    .msg-li-custom-ai-app-card,
    .msg-li-file,
    .msg-li-custom-quickActing,
    .msg-li-geo {

      &.msg-li-custom-8:not(.msg-li-zb),
      &.msg-li-custom-9,
      &.msg-li-custom-collect,
      &.msg-li-custom-multi-file,
      &.msg-li-custom-schedule_invite,
      &.msg-li-custom-schedule_del,
      &.msg-li-custom-schedule_remind,
      &.msg-li-custom-document_remind,
      &.msg-li-custom-imJZSell,
      &.msg-li-custom-imNewHouseJZSell,
      &.msg-li-custom-card-4,
      &.msg-li-custom-jzxs-approval-notice,
      &.msg-li-custom-minApp_YLYK_Detail,
      &.msg-li-custom-imCourseShare,
      &.msg-li-custom-imSomeHouseRecommend,
      &.msg-li-custom-imHouseBatchShare,
      &.msg-li-custom-imNews,
      &.msg-li-custom-ai-card,
      &.msg-li-custom-ai-msg,
      &.msg-li-custom-cooperate,
      &.msg-li-custom-lxt_signup,
      &.msg-li-custom-monad-card,
      &.msg-li-custom-ai-app-card,
      &.msg-li-file:not(.msg-forbid),
      &.msg-li-custom-quickActing,
      &.msg-li-geo {
        .msg-content {
          padding: 0 !important;
        }
      }

      .msg-content {
        background: #FFFFFF !important;
        border: 1px solid #EDEDED;
      }

      .msg-me {
        .msg-content {
          &:after {
            border-color: transparent transparent transparent #FFFFFF !important;
          }

          &:before {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            top: 11px;
            border-width: 7px;
            border-style: solid;
            right: -14px;
            border-color: transparent transparent transparent #E2E2E2;
          }
        }
      }

      &.msg-li-file-img {
        .msg-me {
          .msg-content {
            &:after {
              border-color: transparent transparent transparent $styleMsg !important;
            }
          }
        }
      }

      .msg-you {
        .msg-content {
          &:before {
            content: "";
            width: 0;
            height: 0;
            position: absolute;
            top: 11px;
            border-width: 7px;
            border-style: solid;
            left: -14px;
            border-color: transparent #E2E2E2 transparent transparent;
          }
        }
      }

      &.msg-li-custom-card-4,
      &.msg-li-custom-jzxs-approval-notice,
      &.msg-li-custom-schedule_invite,
      &.msg-li-custom-quickActing,
      &.msg-li-custom-lxt_signup {
        .msg-me {
          .msg-content {
            border-radius: 4px;

            &:after {
              border-color: transparent transparent transparent $styleColor !important;
            }
          }
        }

        .msg-you {
          .msg-content {
            border-radius: 4px;

            &:after {
              border-color: transparent #EC4A4E transparent transparent !important;
            }
          }
        }
      }


      &.msg-li-custom-ai-app-card {
        .msg-me {
          .msg-content {
            border-radius: 4px;

            &:after {
              border-color: transparent transparent transparent #9E71FF !important;
            }
          }
        }

        .msg-you {
          .msg-content {
            border-radius: 4px;

            &:after {
              border-color: transparent #9E71FF transparent transparent !important;
            }
          }
        }
      }

    }
  }
}
</style>
