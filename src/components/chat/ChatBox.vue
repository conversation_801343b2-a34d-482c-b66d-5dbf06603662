<template>
  <div class="chat-box" ref="chatBoxRef">
    <Layout>
      <!--聊天列表-->
      <template v-slot:layout-left>
        <div class="layout-left-box">
          <!--分组模式列表-->
          <div v-if="resizeObj.tabType==1" class="chat-classify-box">
            <div class="chat-classify-header">
              <div class="chat-classify-title-box">
                <div class="classify-icon slide-in show-text-hover" content="精简模式" @click="changeTabType(0)"></div>
                <div>分组模式</div>
              </div>
              <div class="chat-classify-sort" @click="addGroupClassify(2)">新增</div>
            </div>
            <ul class="chat-classify-content">
              <!--分组模式提示-->
              <div v-if="tabObj.showTips" class="tab-tips">
                <span>长按并拖动更换排序</span>
                <i class="icon-close" @click="closeTabTips()"></i>
              </div>
              <!--分组列表-->
              <li :class="['chat-classify-li',item.child?'chat-classify-child':'']" v-show="item.pc!==false&&!item.child"
                  v-for="(item, key) in tabObj.showList" :key="item.newKey" @click="selSessionTab(item.newKey,true,'',true,item.uuid,item)"
                  :draggable="true" @dragstart="itemDrag($event,1,item)" @dragenter="itemDrag($event,2,item)" @dragover="itemDrag($event,3,item)" @dragend="itemDrag($event,4,item)">
                <div :class="['chat-classify-li-content',item.newKey==tabObj.key?'sel':'']" @contextmenu.prevent="setMenu($event,item,key)" @dblclick="scrollList()">
                  <div class="move-icon" v-if="item.key!='all'"></div>
                  <div class="chat-classify-li-name textEls">{{ item.name }}</div>
                  <div v-show="item.num" class="badge">{{ item.num > 99 ? "99+" : item.num }}</div>
                  <div v-if="item.child" class="chat-classify-operate show-text-hover" @click.stop="changeItemShow(item.classifyKey,!item.show)" :content="item.show?'收起':'展开'">
                    <span v-show="item.show" class="classify-arrow-icon arrow-up"></span>
                    <span v-show="!item.show" class="classify-arrow-icon arrow-down"></span>
                  </div>
                </div>
              </li>
            </ul>
            <div class="chat-classify-footer">
              <span class="chat-classify-footer-btn" @click="toQuitTeam(1)">自动退群</span>
              <span class="chat-classify-footer-btn" @click="toQuitTeam()">手动退群</span>
            </div>
            <!--拖拽线-->
            <div class="resize-line" @mousedown="lineResize($event,1,1)" @mouseup="lineResize($event,3,1)"></div>
          </div>
          <!--会话列表-->
          <div class="chat-list-box">
            <div v-if="nimInfo.type" :class="['chat-list-tips-box',nimInfo.type]">
              <i class="tips-icon"></i>
              <span class="textEls">
                <span>{{ nimInfo.type == 'error' ? nimInfo[nimInfo.type] : nimInfo[nimInfo.type][nimInfo.infoType] }}</span>
                <span v-if="nimInfo.type=='info'&&nimInfo.infoType==0">{{ nimInfo.tips }}</span>
              </span>
              <span v-show="nimInfo.type=='err'" class="tips-btn" @click="reloadNim()">请重试</span>
            </div>
            <div class="chat-list-tab" ref="chatListTab">
              <div v-if="resizeObj.tabType==0" class="chat-list-ul-box">
                <div class="classify-icon slide-out show-text-hover" content="分组模式" @click="changeTabType(1)"></div>
                <ul class="chat-list-ul" @wheel="scrollSessionTab">
                  <li :class="['tab',item.newKey==tabObj.key?'sel':'']" v-for="(item, key) in tabObj.showList" :key="item.key" @click="selSessionTab(item.newKey,true,'',true)">
                    {{ item.name }}{{ item.num > 0 ? "(" + (item.num > 99 ? "99+" : item.num) + ")" : "" }}
                  </li>
                </ul>
              </div>
              <div v-else-if="resizeObj.tabType==1&&tabObj.listMap[tabObj.key]" class="chat-list-name-box">
                <div class="chat-list-name textEls" :title="tabObj.listMap[tabObj.key].name||tabObj.listMap[tabObj.key]||''">{{ tabObj.listMap[tabObj.key].name || tabObj.listMap[tabObj.key] || "" }}</div>
                <span v-if="(tabObj.key||'').indexOf('-')>-1&&(tabObj.key||'').indexOf('groupType-')==-1" class="classify-icon add-group show-text-hover" @click="addClassifyGroup(tabObj.listMap[tabObj.key])"
                      :content="'添加'+(tabObj.listMap[tabObj.key].type == 2 ? '讨论组/群' : tabObj.listMap[tabObj.key].type == 3 ? '同事' : tabObj.listMap[tabObj.key].type == 4 ? '客户' : '')"></span>
              </div>
            </div>
            <ChatList :class="['comp-chat-list',nimInfo.type?'has-tip':'']" :sessionType="sessionType" :changeTabInfo="true"></ChatList>
            <!--拖拽线-->
            <div class="resize-line" @mousedown="lineResize($event,1,2)" @mouseup="lineResize($event,3,2)"></div>
          </div>
        </div>
      </template>
      <!--聊天内容-->
      <template v-slot:layout-right>
        <!--群助手/订阅号二级列表-->
        <div class="chat-list-box" v-if="sessionInfo.id&&sessionType!=11" v-show="isSessionList(sessionInfo)">
          <div class="chat-list-header">{{ sessionInfo.detailInfo?.name }}</div>
          <!--群助手-->
          <ChatList class="comp-chat-list" v-if="sessionInfo.id=='p2p-'+config.helperAccount" sessionType="7"></ChatList>
          <!--群通知-->
          <TeamNotify v-else-if="sessionInfo.id=='p2p-'+config.systemMessage"></TeamNotify>
          <!--订阅号-->
          <ChatList class="comp-chat-list" v-else-if="sessionInfo.id=='p2p-'+config.subscribe" sessionType="8" :isSession="true"></ChatList>
          <!--客户咨询-->
          <ChatList class="comp-chat-list" v-else-if="sessionInfo.id=='p2p-'+config.customerAccount" sessionType="5"></ChatList>
        </div>
        <div class="chat-content-box" v-show="!sessionInfo.id||!isSessionList(sessionInfo)">
          <!--聊天内容-->
          <div class="chat-all" v-show="sessionInfo.detailInfo">
            <ChatContent class="comp-chat-content" :sessionType="sessionType"></ChatContent>
          </div>
          <!--无会话信息-->
          <div class="chat-none" v-show="!isSessionList(sessionInfo)&&!sessionInfo.detailInfo">
            <!-- 网络提示 -->
            <NetTips></NetTips>
            <div class="none-tips-box">
              <img src="/img/index/none_logo.png" alt="">
              <span>暂无会话</span>
            </div>
          </div>
        </div>
      </template>
    </Layout>

  </div>
</template>
<script>
import {watch, ref, nextTick, onMounted} from "vue";
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {debounce, deepClone, isSessionList, reloadMainWin, showMenu, filterArr, getBounding} from "@utils";
import {getHotAppListApi} from "@utils/net/api";
import ChatList from "@comp/chat/ChatList";
import ChatContent from "@comp/chat/ChatContent";
import TeamNotify from "@comp/chat/TeamNotify"
import LyDialog from "@comp/ui/comps/LyDialog";
import NetTips from "@comp/ui/comps/NetTips";
import Layout from "@comp/layout/index.vue";
import {toast, loading} from "@comp/ui";

export default {
  name: "ChatBox",
  props: {
    // 会话类型-utils.js/index.js的getSessionType
    sessionType: {
      type: String,
      default: "-1",
    }
  },
  components: {ChatList, ChatContent, TeamNotify, LyDialog, Layout, NetTips},
  setup(props, ctx) {
    const router = useRouter();
    const store = useStore();
    // 配置文件
    let config = store.getters.getConfig.config;
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();
    // 会话框元素
    let chatBoxRef = ref();
    // 列表标签元素
    let chatListTab = ref();
    // 分组列表展开转台
    let sessionTabClassifyShow = store.getters.getSessionTabClassifyShow;
    // tab标签列表对象
    let tabObj = ref({
      listMap: {},
      list: [
        {id: 0, sort: -1, name: "全部", key: "all", newKey: "all", num: 0},
        {id: 1, sort: 10, name: "客户", key: setClassifyKey(1).key, newKey: setClassifyKey(1).newKey, num: 0},
        {id: 2, sort: 3, name: "稍后处理", key: setClassifyKey(2).key, newKey: setClassifyKey(2).newKey, num: 0},
        {id: 3, sort: 1, name: "@我", key: setClassifyKey(3).key, newKey: setClassifyKey(3).newKey, num: 0},
        {id: 4, sort: 8, name: "乐文档", key: setClassifyKey(4).key, newKey: setClassifyKey(4).newKey, num: 0},
        {id: 7, sort: 6, name: "讨论组/群分组", key: setClassifyKey(7).key, newKey: setClassifyKey(7).newKey, num: 0, classifyKey: "groupClassify", classifyType: 2, show: sessionTabClassifyShow["groupClassify"], child: []},
        {id: 8, sort: 12, name: "同事", key: setClassifyKey(8).key, newKey: setClassifyKey(8).newKey, num: 0, classifyKey: "colleagueClassify", classifyType: 3, show: sessionTabClassifyShow["colleagueClassify"], child: []},
        {id: 9, sort: 9, name: "数字人", key: setClassifyKey(9).key, newKey: setClassifyKey(9).newKey, num: 0},
        {id: 10, sort: 2, name: "单聊", key: setClassifyKey(10).key, newKey: setClassifyKey(10).newKey, num: 0},
        {id: 11, sort: 4, name: "重要标记", key: setClassifyKey(11).key, newKey: setClassifyKey(11).newKey, num: 0},
        {id: 12, sort: 5, name: "我的直属", key: setClassifyKey(12).key, newKey: setClassifyKey(12).newKey, num: 0},
        {id: 13, sort: 7, name: "业务讨论组分组", key: setClassifyKey(13).key, newKey: setClassifyKey(13).newKey, num: 0, classifyKey: "groupType", child: []},
        {id: 14, sort: 11, name: "我的任务", key: setClassifyKey(14).key, newKey: setClassifyKey(14).newKey, num: 0, pc: false},
      ],
      showList: [],
      sortList: [],// 1客户-2稍后处理-3@我-4乐文档-5群-6讨论组分组-7同事-8数字人-9单聊-10标记-11我的直属-12业务讨论组分组-13我的任务
      key: store.getters.getSessionTab,
      tempKey: "",// 新增分组/添加讨论组后选中key标识
      loading: false,// 加载状态
      classifyMoveItem: {},// 分组拖拽排序对象
      dragFlag: false,// 分组拖拽排序状态
      showTips: false,// 分组是否显示拖拽提示
    });
    getTabShowTips();
    tabObj.value.sortList = getTabSortList();
    // 用户信息
    let userInfo = store.getters.getUserInfo;
    // 上个会话id
    let preId = ref(store.getters.getCurrentSession.id);
    // 当前会话信息
    let sessionInfo = ref(preId.value ? store.getters.getSessions({id: preId.value}) : {});
    // 云信加载数据
    let nimInfo = ref(store.getters.getNimInfo);
    // 拖拽对象
    let resizeOtherWidth = 60 + 2 + 1;// 60tab、2+1border
    let windowSizeType = store.getters.getWindowSizeType;
    let resizeObj = ref({
      key: -1,// 1拖拽分组模式2拖拽会话列表
      tempWidth: 0,// 拖拽框拖拽宽度
      tempClassifyWidth: 0,// 拖拽分组框宽度
      screenX: 0,// 当前拖拽位置
      minClassify: 160,// 最小分组模式宽度
      minList: 239,// 最小会话列表宽度
      minContent: 678,// 最小内容宽度
      showWidth: 0,// 最大显示宽度
      classifyWidth: windowSizeType.classifyWidth,// 分组宽度
      listWidth: windowSizeType.listWidth,// 会话列表宽度
      contentWidth: 678,// 内容宽度
      tabType: windowSizeType.tabType || 0,//1分组模式
    });
    // 获取热门ai
    let hotAiObj = {
      timer: false,
      flag: false
    }

    // 初始化分组模式
    initShowTabList();

    onMounted(() => {
      if (!store.getters.getCurrentSession.tempId) {
        store.commit("setEmit", {type: "scrollCurrentSession", value: Date.now()});
      }
      changeWidth();
    });

    // 切换会话
    watch(() => store.state.currentSession.id,
      (newValue, oldValue) => {
        if (newValue) {
          if (newValue != oldValue) {
            sessionInfo.value = store.getters.getSessions({id: newValue});
          }
        } else {
          sessionInfo.value = {};
        }
      }, {
        deep: true
      }
    )
    // 监听会话状态
    watch(() => store.state.updateSessionId,
      (newValue, oldValue) => {
        if (newValue && newValue == sessionInfo.value.id) {
          sessionInfo.value = store.getters.getSessions({id: sessionInfo.value.id});
        }
      }, {
        deep: true
      }
    );
    watch(() => store.state.sessionsTemp[sessionInfo.value.id],
      (newValue, oldValue) => {
        if (newValue && newValue != oldValue) {
          sessionInfo.value = store.getters.getSessions({id: sessionInfo.value.id});
        }
      }, {
        deep: true
      }
    );
    // 监听群状态
    watch(() => store.state.teams[sessionInfo.value.to],
      (newValue, oldValue) => {
        if (newValue && newValue.teamId == sessionInfo.value.to) {
          sessionInfo.value.detailInfo = newValue;
        }
      }, {
        deep: true
      }
    );
    // 云信状态变化
    watch(() => store.state.nimInfo,
      (newValue, oldValue) => {
        if (newValue) {
          nimInfo.value.type = newValue.type;
          nimInfo.value.infoType = newValue.infoType;
          nimInfo.value.tips = newValue.progress != null ? `${newValue.progress}%` : "";
        }
      }, {
        deep: true
      }
    );
    // 乐聊分组模式
    watch(() => store.state.windowSizeType,
      (newValue, oldValue) => {
        if (resizeObj.value.classifyWidth != newValue.classifyWidth || resizeObj.value.listWidth != newValue.listWidth || resizeObj.value.tabType != newValue.tabType) {
          resizeObj.value.tabType = newValue.tabType;
          resizeObj.value.classifyWidth = newValue.classifyWidth;
          resizeObj.value.listWidth = newValue.listWidth;
          changeWidth();
        }
      }, {
        deep: true
      }
    );
    // 讨论组分组显示状态
    watch(() => store.state.sessionTabClassifyShow,
      (newValue, oldValue) => {
        for (let key in newValue) {
          for (let i = 0; i < tabObj.value.list.length; i++) {
            let item = tabObj.value.list[i];
            // 设置对应分组的展开状态
            if (item.classifyKey == key) {
              item.show = newValue[key];
            }
          }
        }
        initShowTabList();
      }, {
        deep: true
      }
    );
    // 分组模式排序
    watch(() => store.state.settings[config.settings.type16],
      (newValue, oldValue) => {
        tabObj.value.sortList = getTabSortList();
        initShowTabList();
      }, {
        deep: true
      }
    );
    // 分组模式提示
    watch(() => store.state.settings[config.settings.type17],
      (newValue, oldValue) => {
        getTabShowTips();
      }, {
        deep: true
      }
    );
    // 监听全局点击
    watch(() => store.state.emit.mousemove,
      (newValue, oldValue) => {
        lineResize(newValue, 2, resizeObj.value.key);
      }, {
        deep: true
      }
    );
    // 监听全局鼠标释放
    watch(() => store.state.emit.mouseup,
      (newValue, oldValue) => {
        lineResize(newValue, 3);
      }, {
        deep: true
      }
    );
    watch(() => store.state.emit.resize,
      (newValue, oldValue) => {
        changeWidth();
      }, {
        deep: true
      }
    );
    // 更新会话tab选中
    watch(() => store.state.emit.changeTabInfo,
      (newValue, oldValue) => {
        changeTabInfo(newValue);
      }, {
        deep: true
      }
    );
    // 业务讨论组变化
    watch(() => store.state.groupTypeList,
      (newValue, oldValue) => {
        initShowTabList();
      }, {
        deep: true
      }
    );


    // 切换tba列表 click点击事件
    function selSessionTab(key, scrollTop, update, click, hasChild, classifyItem) {
      // 设置当前选中分组，属于当前分组点击不滚动到顶部
      if (key == tabObj.value.key && scrollTop) {
        scrollTop = false;
      }
      store.commit("setEmit", {type: "selSessionTab", value: {key: key, scrollTop: scrollTop, update: update, click: click, classifyItem: deepClone(classifyItem), value: Date.now()}});
      // 选中ai需要请求接口插入ai会话
      if (key == "ai" && tabObj.value.key != "ai") {
        if (hotAiObj.timer) {
          return;
        }
        hotAiObj.timer = setTimeout(() => {
          hotAiObj.timer = "";
        }, 1000);
        if (hotAiObj.flag) {
          return;
        }
        hotAiObj.flag = true;
        getHotAppListApi().then(res => {
          hotAiObj.flag = false;
          if (res?.data) {
            res.data.map(item => {
              if (item.nimAccid) {
                if (!store.getters.getSessions({id: "p2p-" + item.nimAccid}).id) {
                  remote.store.dispatch("insertLocalSession", {scene: "p2p", to: item.nimAccid});
                }
              }
            });
          }
        });
      } else if (key == "subordinate" && tabObj.value.key != "subordinate") {
        // 插入直接下属会话
        store.dispatch("setFindSubordinate").then(res => {
          for (let key in res) {
            if (!store.getters.getSessions({id: "p2p-" + key}).id) {
              remote.store.dispatch("insertLocalSession", {scene: "p2p", to: key});
            }
          }
        });
      }
    }

    // 修改tab会话类型数量
    function changeTabInfo(obj) {
      tabObj.value.key = obj.key;
      tabObj.value.list.map(item => {
        switch (item.key) {
          case "all":
            item.num = obj.allCount;
            break;
          case "customer":
            item.num = obj.customerUnread;
            break;
          case "hait":
            item.num = obj.haitCount;
            break;
          case "remind":
            item.num = obj.remindCount;
            break;
          case "team":
            item.num = obj.teamCount;
            break;
          case "groupClassify":
            item.num = obj.groupAndTeamCount;
            item.child = obj.groupChildList;
            break;
          case "p2p":
            item.num = obj.colleagueCount;
            break;
          case "colleagueClassify":
            item.num = obj.colleagueCount;
            item.child = obj.colleagueChildList;
            break;
          case "ai":
            item.num = obj.aiCount;
            break
          case "mark":
            item.num = obj.markCount;
            break
          case "subordinate":
            item.num = obj.subordinateCount;
            break
          case "groupType":
            item.child = obj.groupTypeList;
            break;
        }
      });
      initShowTabList();
    }

    // tab标签左右滚动
    function scrollSessionTab(e) {
      e.preventDefault();
      chatListTab.value.querySelector(".chat-list-ul").scrollBy({
        left: e.deltaY < 0 ? -30 : 30
      });
    }

    // 重新初始化云信
    function reloadNim() {
      loading();
      reloadMainWin();
    }

    // 初始化显示tab列表
    function initShowTabList() {
      let list = deepClone(tabObj.value.list);
      let newList = [];
      if (resizeObj.value.tabType == 0) {
        // 精简模式只设置3个
        newList = list.slice(0, 3);
      } else {
        // 分组模式排序
        if (tabObj.value.dragFlag) {
          return;
        }
        for (let i = 0; i < list.length; i++) {
          let item = list[i];
          // 对应二级分组展开
          switch (item.newKey) {
            case "groupClassify":
            case "colleagueClassify":
            case "groupType":
              if (item.child?.length > 0) {
                list.splice(i, 1);
                let type = item.newKey == "groupType" ? 1 : 2;
                item.child.map((cItem, cKey) => {
                  if (type == 1) {
                    cItem.name += "讨论组";
                  } else if (type == 2) {
                    cItem.sortIndex = cItem.sort;
                    delete cItem.sort;
                  }
                  let newItem = {...item, uuid: cItem.uuid, name: cItem.name, num: cItem.num, sortIndex: cItem.sortIndex, newKey: item.key + "-" + cItem.uuid};
                  delete newItem.child;
                  list.splice(i + cKey, 0, newItem);
                });
                i--;
              } else if (item.child?.length == 0) {
                list.splice(i, 1);
                i--;
              }
              break;
          }
        }
        if (tabObj.value.sortList.length > 1) {
          // 根据服务端列表排序
          let sortList = deepClone(tabObj.value.sortList);
          for (let i = 0; i < sortList.length; i++) {
            let itemI = sortList[i];
            let itemFlag = false;
            for (let j = 0; j < list.length; j++) {
              let itemJ = list[j];
              if (itemI.newKey == itemJ.newKey) {
                itemI.num = itemJ.num;
                itemI.name = itemJ.name;
                itemFlag = true
                break;
              }
            }
            // 服务端的排序被删除了
            if (!itemFlag) {
              sortList.splice(i, 1);
              i--;
            }
          }
          newList = filterArr(sortList.concat(list), "newKey");
        } else {
          newList = list.sort((a, b) => {
            return a.sort - b.sort;
          });
        }
        for (let i = 0; i < newList.length; i++) {
          let itemI = newList[i];
          // 不存在直接下属删除
          if (itemI.newKey == "subordinate" && Object.keys(store.getters.getState("subordinateMap")).length == 0) {
            newList.splice(i, 1);
            i--;
            continue;
          }
          // 接口返回不显示同事分组
          if (itemI.key == "colleagueClassify" && !store.getters.getState("collDutyType")) {
            newList.splice(i, 1);
            i--;
            continue;
          }
        }
      }
      tabObj.value.showList = newList;
      setListMap();
    }

    // 设置分组名
    function setListMap() {
      let tempItem = {};
      let listMap = {};
      tabObj.value.showList.map(item => {
        listMap[item.newKey] = item;
        // 判断是否存在临时切换分组
        if (tabObj.value.tempKey && tabObj.value.tempKey == item.newKey) {
          tempItem = item;
          nextTick(() => {
            let classifyElm = chatBoxRef.value.querySelector(".chat-classify-content");
            if (classifyElm) {
              let bounds = getBounding(classifyElm.querySelector(".chat-classify-li-content.sel"));
              classifyElm.scrollTop = bounds.top + bounds.height;
            }
            // 新增分组上传排序
            store.dispatch("setClassifyList", {list: tabObj.value.showList});
          });
        }
      });
      tabObj.value.listMap = listMap;
      // 切换到指定分组
      if (tempItem.newKey) {
        tabObj.value.tempKey = "";
        selSessionTab(tempItem.newKey, true, '', true, '', tempItem);
      }
    }

    // 变更精简/分组模式
    function changeTabType(type) {
      // 分组模式改为精简模式，不是精简模式的三个分组则切换到全部显示
      if (type == 0 && tabObj.value.key != "all" && tabObj.value.key != "customer" && tabObj.value.key != "remind") {
        selSessionTab(tabObj.value.showList[0].key, true, '', true, tabObj.value.showList[0])
      }
      resizeObj.value.tabType = type;
      nextTick(() => {
        initShowTabList();
        changeWidth();
        modifyClassify();
        store.commit("setWindowSizeInfo", {initMin: true, changeTabType: true, currentWindow: currentWindow.cWindow.id});
      });
    }

    // 设置窗口大小和分组模式
    function modifyClassify() {
      // 设置配置信息
      let windowInfo = {tabType: resizeObj.value.tabType, width: currentWindow.getBounds().width, height: currentWindow.getBounds().height, classifyWidth: resizeObj.value.classifyWidth, listWidth: resizeObj.value.listWidth}
      if (currentWindow.isFullscreen) {
        let windowSize = store.getters.getWindowSizeType;
        windowInfo.width = windowSize.width;
        windowInfo.height = windowSize.height;
      }
      store.commit("setWindowSizeType", windowInfo);
      // 拖拽结束5s后再同步到服务器
      debounce({
        timerName: "resizeWin",
        time: 5 * 1000,
        fnName: function () {
          store.commit("setWindowSizeType", {...store.getters.getWindowSizeType, width: currentWindow.getBounds().width, height: currentWindow.getBounds().height});
          store.dispatch("setModifySettings", {type: 1, key: config.settings.type12, value: store.getters.getWindowSizeType});
        }
      });
    }

    // 创建讨论组分组
    function addGroupClassify(classifyType) {
      store.commit("setEmit", {
        type: "initClassify", value: {
          type: 1,
          classifyType: classifyType,
          time: Date.now(),
          done: res => {
            if (res?.success) {
              // 添加成功后选中分组
              nextTick(() => {
                tabObj.value.tempKey = (classifyType == 2 ? "groupClassify-" : classifyType == 3 ? "colleagueClassify-" : classifyType == 4 ? "customerClassify-" : "") + res?.data?.data?.uuid;
              });
            }
          }
        }
      });
    }

    // 分组添加讨论组
    function addClassifyGroup(item) {
      let uuid = store.getters.getSessionTabClassifyItem.uuid;
      let name = store.getters.getSessionTabClassifyItem.name;
      if (item.uuid) {
        uuid = item.uuid;
        name = item.name;
      }
      let classifyType = item.classifyType || item.type;
      store.commit("setEmit", {
        type: "inviteBox", value: {
          type: 3,
          classifyType: classifyType,
          uuid: uuid,
          name: name,
          itemMap: store.getters.getClassifyList({uuid: uuid, valueMap: true}),
          time: Date.now(),
          notSendCustomSysMsg: true,
          done: (res, groupList) => {
            // 添加成功后选中分组
            tabObj.value.tempKey = item.newKey;
            // 发送通知给自己和别人(用于多端同步)
            let customParam = {};
            if (classifyType == 2) {
              customParam.groupList = groupList;
            } else {
              customParam.personList = groupList;
            }
            store.dispatch("sendCustomSysMsgByGroups", customParam);
          }
        }
      });
    }

    // 变更讨论组分组显示状态
    function changeItemShow(key, value) {
      store.commit("setSessionTabClassifyShow", {key: key, value: value});
    }

    // 拖拽分组模式宽度/聊天会话宽度 type1拖拽开始2拖拽中3拖拽结束 key1拖拽分组模式2拖拽会话列表
    function lineResize(e, type, key) {
      switch (type) {
        case 1:
          // 拖拽开始
          // 去除原有选中内容
          window.getSelection().removeAllRanges();
          resizeObj.value.key = key;
          resizeObj.value.screenX = e.screenX;
          resizeObj.value.tempWidth = key == 1 ? resizeObj.value.classifyWidth : resizeObj.value.listWidth;
          resizeObj.value.tempClassifyWidth = resizeObj.value.classifyWidth;
          changeWidth();
          store.commit("setEmit", {type: "resizeObj", value: resizeObj.value});
          break;
        case 2:
          // 拖拽中
          if (resizeObj.value.key != -1) {
            let originResizeWidth = e.screenX + resizeObj.value.tempWidth - resizeObj.value.screenX;
            let resizeWidth = originResizeWidth;
            let minWidth = key == 1 ? resizeObj.value.minClassify : resizeObj.value.minList;
            let maxWidth = resizeObj.value.showWidth - (key == 1 ? resizeObj.value.minList : resizeObj.value.minClassify) - resizeObj.value.minContent;
            // 判断拖拽最大值和最小值
            resizeWidth < minWidth ? resizeWidth = minWidth : "";
            resizeWidth > maxWidth ? resizeWidth = maxWidth : "";
            // 设置分组/列表宽度
            if (key == 1) {
              if (resizeWidth < resizeObj.value.classifyWidth) {
                // 缩小分组放大列表
                resizeObj.value.listWidth += resizeObj.value.classifyWidth - resizeWidth;
              } else if (resizeWidth > resizeObj.value.classifyWidth && resizeObj.value.listWidth - (resizeWidth - resizeObj.value.classifyWidth) > resizeObj.value.minList) {
                // 放大分组先缩小列表
                resizeObj.value.listWidth -= resizeWidth - resizeObj.value.classifyWidth;
              }
              resizeObj.value.classifyWidth = resizeWidth;
            } else {
              if (resizeObj.value.tabType == 1) {
                // 分组模式下列表可拖拽宽度
                let canResizeWidth = resizeObj.value.showWidth - resizeObj.value.classifyWidth - resizeObj.value.minContent;
                resizeWidth > canResizeWidth ? resizeWidth = canResizeWidth : "";
                // 缩小列表同时缩小分组
                if (originResizeWidth < resizeObj.value.minList &&
                  resizeObj.value.tempClassifyWidth - (resizeObj.value.listWidth - originResizeWidth) > resizeObj.value.minClassify) {
                  resizeObj.value.classifyWidth = resizeObj.value.tempClassifyWidth - (resizeObj.value.listWidth - originResizeWidth);
                }
              }
              resizeObj.value.listWidth = resizeWidth;
            }
            changeWidth();
          }
          break;
        case 3:
          // 拖拽结束
          if (resizeObj.value.key != -1) {
            resizeObj.value.key = -1;
            store.commit("setEmit", {type: "resizeObj", value: resizeObj.value});
            modifyClassify();
          }
          break;
      }
    }

    // 变更窗口分组/列表/内容宽度
    function changeWidth() {
      resizeObj.value.showWidth = currentWindow.getBounds().width - resizeOtherWidth;
      // 设置服务器配置信息
      if (chatBoxRef?.value) {
        // 错误数据校正
        if (resizeObj.value.classifyWidth < resizeObj.value.minClassify) {
          resizeObj.value.classifyWidth = resizeObj.value.minClassify;
        }
        if (resizeObj.value.listWidth < resizeObj.value.minList) {
          resizeObj.value.listWidth = resizeObj.value.minList;
        }
        if (resizeObj.value.contentWidth < resizeObj.value.minContent) {
          resizeObj.value.contentWidth = resizeObj.value.minContent;
        }
        // 拖拽差距宽度
        let diffWidth = resizeObj.value.showWidth - resizeObj.value.classifyWidth - resizeObj.value.listWidth - resizeObj.value.contentWidth
        if (diffWidth < 0) {
          // 窗口变化先缩小内容
          if (resizeObj.value.contentWidth + diffWidth > resizeObj.value.minContent) {
            resizeObj.value.contentWidth += diffWidth;
          } else {
            let diffWidth1 = resizeObj.value.contentWidth - resizeObj.value.minContent + diffWidth;
            // 内容不足继续缩小列表
            resizeObj.value.contentWidth = resizeObj.value.minContent
            if (resizeObj.value.listWidth + diffWidth1 > resizeObj.value.minList) {
              resizeObj.value.listWidth += diffWidth1;
            } else {
              let diffWidth2 = resizeObj.value.listWidth - resizeObj.value.minList + diffWidth1;
              // 列表不足继续缩小分组
              resizeObj.value.listWidth = resizeObj.value.minList
              if (resizeObj.value.classifyWidth + diffWidth2 > resizeObj.value.minClassify) {
                resizeObj.value.classifyWidth += diffWidth2;
              } else {
                resizeObj.value.classifyWidth = resizeObj.value.minClassify
              }
            }
          }
        }

        if (resizeObj.value.tabType == 1) {
          chatBoxRef.value.querySelector(".im-content-left").style.width = `${resizeObj.value.classifyWidth + resizeObj.value.listWidth + 1}px`;
          chatBoxRef.value.querySelector(".im-content-right").style.width = `calc(100% - ${resizeObj.value.classifyWidth}px - ${resizeObj.value.listWidth}px - 1px)`;
          chatBoxRef.value.querySelector(".im-content-left .chat-list-box").style.width = `${resizeObj.value.listWidth}px`;
          let classifyElm = chatBoxRef.value.querySelector(".im-content-left .chat-classify-box");
          if (classifyElm) {
            classifyElm.style.width = `${resizeObj.value.classifyWidth}px`;
          }
        } else {
          chatBoxRef.value.querySelector(".im-content-left").style.width = `${resizeObj.value.listWidth + 1}px`;
          chatBoxRef.value.querySelector(".im-content-right").style.width = `calc(100% - ${resizeObj.value.listWidth + 1}px)`;
          chatBoxRef.value.querySelector(".im-content-left .chat-list-box").style.width = `${resizeObj.value.listWidth}px`;
        }
      }
    }

    // 分组模式分组列表右键
    function setMenu(e, item, key) {
      let menu = {};
      let menuList = [];
      let labelName = item.classifyType == 2 ? "分组" : "标签";
      let itemName = item.classifyType == 2 ? "讨论组/群" : item.classifyType == 3 ? "同事" : item.classifyType == 4 ? "客户" : "";

      // 群/讨论组分组和同事分组允许操作
      if ((item.id == 7 || item.id == 8) && item.uuid) {
        menuList.push({
          label: `添加${itemName}`, click: function () {
            addClassifyGroup(item);
          }
        });

        menuList.push({
          label: `重命名${labelName}`, click: function () {
            updateClassify({type: 2, classifyType: item.classifyType, uuid: item.uuid, name: item.name});
          }
        });

        menuList.push({
          label: `删除${labelName}`, click: async function () {
            let res = await updateClassify({type: 3, classifyType: item.classifyType, uuid: item.uuid, name: item.name});
            if (res.success) {
              let changeIndex = tabObj.value.showList.findIndex(lItem => {return lItem.newKey == item.newKey});
              let changeItem = tabObj.value.showList[changeIndex > 0 ? changeIndex - 1 : 0];
              selSessionTab(changeItem.newKey, true, '', true, "", changeItem);
            }
          }
        });
      }

      if (menuList.length > 0) {
        menu = showMenu(menuList);
        menu.popup(e.x, e.y);
      }
      e.stopPropagation();
      e.preventDefault();
      return false;
    }

    // 1创建分组2重命名分组3删除分组4移动至分组5添加至分组6移出至分组
    async function updateClassify(info, notTips) {
      return new Promise(resolve => {
        store.commit("setEmit", {
          type: "initClassify", value: {
            classifyType: info.classifyType,
            type: info.type,
            classifyName: info.name,
            classifyUUID: info.uuid,
            classifyValue: info.value,
            notTips: notTips,
            time: Date.now(),
            done: res => {
              resolve(res);
            }
          }
        });
      });
    }

    // 显示排序菜单
    function showSortMenu(e) {
      let menuList = [{
        label: `模块排序`, click: function () {
          showSortDialog(-1, true);
        }
      }, {
        label: `客户排序`, click: function () {
          showSortDialog(4, true);
        }
      }, {
        label: `同事排序`, click: function () {
          showSortDialog(3, true);
        }
      }, {
        label: `讨论组/群分组排序`, click: function () {
          showSortDialog(2, true);
        }
      }];
      let menu = showMenu(menuList);
      menu.popup(e.x, e.y);
    }

    // 切换显示弹窗 1群2讨论组3同事4客户-1模块
    function showSortDialog(type) {
      //  分组模式排序
      let showList = [];
      let showName = "";
      switch (type) {
        case -1:
          // 设置排序列表-去除全部
          showList = deepClone(tabObj.value.showList)
          showList.splice(0, 1);
          break;
        case 2:
          showName = "讨论组/群分组";
          showList = deepClone(tabObj.value.showList.find(item => {return item.classifyKey == "groupClassify"})?.child) || [];
          break;
        case 3:
          showName = "同事标签";
          // 同事标签过滤直接下属标签
          showList = deepClone(tabObj.value.showList.find(item => {return item.classifyKey == "colleagueClassify"})?.child) || [];
          break;
        case 4:
          showName = "客户标签";
          showList = deepClone(tabObj.value.showList.find(item => {return item.classifyKey == "customerClassify"})?.child) || [];
          break;
      }

      if (showList.length == 0) {
        toast({title: `暂无${showName}可排序`, type: 2})
        return;
      }
      store.commit("setEmit", {
        type: "initClassify", value: {
          type: 7,
          classifyType: type,
          classifySortList: showList,
          time: Date.now(),
          done: res => {

          }
        }
      });
    }

    // 移动分组 type-1开始2进入3过程4结束
    function itemDrag(e, type, item) {
      switch (type) {
        case 1:
          if (item.key == "all") {
            e.stopPropagation();
            e.preventDefault();
            return false;
          }
          tabObj.value.classifyMoveItem = item;
          tabObj.value.dragFlag = true;
          break;
        case 2:
          if (tabObj.value.classifyMoveItem !== item) {
            if (item.key == "all") {
              e.stopPropagation();
              e.preventDefault();
              return false;
            }
            let oldIndex = tabObj.value.showList.indexOf(tabObj.value.classifyMoveItem);
            let newIndex = tabObj.value.showList.indexOf(item);
            let newItems = [...tabObj.value.showList];
            // 删除老节点
            newItems.splice(oldIndex, 1);
            // 添加新节点
            newItems.splice(newIndex, 0, tabObj.value.classifyMoveItem);
            tabObj.value.showList = [...newItems];
          }
          e.preventDefault();
          break;
        case 3:
          // 去除禁止光标
          e.preventDefault();
          break;
        case 4:
          tabObj.value.classifyMoveItem = {};
          tabObj.value.dragFlag = false;
          // 拖拽结束上传排序
          store.dispatch("setClassifyList", {list: tabObj.value.showList});
          break;
      }
    }

    // 获取服务端排序列表对象
    function getTabSortList() {
      try {
        let list = deepClone(store.getters.getSettings[config.settings.type16] || []);
        if (list[0]?.key != "all") {
          list.unshift(deepClone(tabObj.value.list[0]));
        }
        list.map(item => {
          item.key = setClassifyKey(item.id, item.uuid).key;
          item.newKey = setClassifyKey(item.id, item.uuid).newKey;
          if (item.id == 7) {
            item.classifyType = 2;
            item.type = 2;
          } else if (item.id == 8) {
            item.classifyType = 3;
            item.type = 3;
          }
        });
        return list;
      } catch (e) {
        return [];
      }
    }

    // 设置分组id对应key和newKey key:1key,2newKey
    function setClassifyKey(id, uuid) {
      let key, newKey;
      switch (id) {
        case 0:
          key = "all";
          newKey = "all";
          break;
        case 1:
          key = "customer";
          newKey = "customer";
          break;
        case 2:
          key = "remind";
          newKey = "remind";
          break;
        case 3:
          key = "hait";
          newKey = "hait";
          break;
        case 4:
          key = "doc";
          newKey = "doc";
          break;
        case 5:
          key = "team";
          newKey = "team";
          break;
        case 6:
          key = "group";
          newKey = "group";
          break;
        case 7:
          key = "groupClassify";
          newKey = `groupClassify${uuid ? "-" + uuid : ""}`;
          break;
        case 8:
          key = "colleagueClassify";
          newKey = `colleagueClassify${uuid ? "-" + uuid : ""}`;
          break;
        case 9:
          key = "ai";
          newKey = "ai";
          break;
        case 10:
          key = "p2p";
          newKey = "p2p";
          break;
        case 11:
          key = "mark";
          newKey = "mark";
          break;
        case 12:
          key = "subordinate";
          newKey = "subordinate";
          break;
        case 13:
          key = "groupType";
          newKey = `groupType${uuid ? "-" + uuid : ""}`;
          break;
        case 14:
          key = "task";
          newKey = "task";
          break;
      }
      return {key, newKey};
    }

    // 跳转退群页面 type:1自动退群
    function toQuitTeam(type) {
      if (type == 1) {
        store.commit("setEmit", {type: "showQuitQrcode", value: Date.now()});
      }
      router.push({path: "/index/mailList"});
    }

    // 双击滚动到对应列表未读数位置，默认为顶部
    function scrollList() {
      store.commit("setEmit", {type: "scrollSessionList", value: new Date().getTime()});
    }

    // 判断是否显示分组拖拽提示
    function getTabShowTips() {
      let flag = false;
      if (store.getters.getSettings[config.settings.type17] == 0) {
        flag = true;
      }
      tabObj.value.showTips = flag;
    }

    // 关闭拖拽提示
    async function closeTabTips() {
      tabObj.value.showTips = false;
      let res = await store.dispatch("setModifySettings", {type: 3, key: config.settings.type17, value: 1});
      if (!res?.success) {
        toast({title: res?.errorMsg || "服务器异常请重试"});
        tabObj.value.showTips = true;
      }
    }

    return {
      chatBoxRef,
      sessionInfo,
      config,
      chatListTab,
      tabObj,
      nimInfo,
      resizeObj,

      isSessionList,
      scrollSessionTab,
      selSessionTab,
      reloadNim,
      changeTabType,
      addGroupClassify,
      addClassifyGroup,
      changeItemShow,
      lineResize,
      setMenu,
      showSortMenu,
      itemDrag,
      toQuitTeam,
      scrollList,
      closeTabTips,
    }
  }
}
</script>
<style scoped lang="scss">
.chat-box {
  width: 100%;
  height: 100%;
  display: flex;

  :deep(.layout-left) {
    overflow: visible !important;
  }

  .resize-line {
    position: absolute;
    top: 0;
    right: 0;
    width: 3px;
    height: 100%;
    z-index: 1;
    cursor: e-resize;
  }

  .layout-left {
    .layout-left-box {
      width: 100%;
      height: 100%;
      display: flex;

      .classify-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
        font-size: 14px;
        background-image: url("/img/index/icon_classify.png");
        background-size: 216px 20px;
        background-repeat: no-repeat;
        cursor: pointer;

        &.add {
          background-position: 0px 0;

          &:hover {
            background-position: -16px 0;
          }
        }

        &.arrow-up {
          background-position: -32px 0;

          &:hover {
            background-position: -48px 0;
          }
        }

        &.arrow-down {
          background-position: -64px 0;

          &:hover {
            background-position: -80px 0;
          }
        }

        &.slide-out {
          width: 20px;
          height: 20px;
          background-position: -96px 0;

          &:hover {
            background-position: -116px 0;
          }
        }

        &.slide-in {
          width: 20px;
          height: 20px;
          background-position: -136px 0;

          &:hover {
            background-position: -156px 0;
          }
        }

        &.add-group {
          width: 20px;
          height: 20px;
          background-position: -176px 0;

          &:hover {
            background-position: -196px 0;
          }
        }
      }

      .show-text-hover {
        &:after {
          top: 24px;
          left: 0;
          transform: none;
        }
      }

      .chat-classify-box {
        position: relative;
        width: 150px;
        height: 100%;
        flex-shrink: 0;
        border-right: 1px solid #E0E0E0;
        background: $styleBg1;

        .chat-classify-header {
          width: 100%;
          height: 45px;
          line-height: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #333333;
          padding: 0 8px;
          border-bottom: 1px solid #E0E0E0;

          .chat-classify-title-box {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: bold;

            .classify-icon {
              margin-right: 5px;
            }
          }

          .chat-classify-sort {
            cursor: pointer;

            &:hover {
              color: $styleColor;
            }
          }
        }

        .chat-classify-content {
          position: relative;
          width: 100%;
          height: calc(100% - 45px - 30px);
          overflow-y: auto;

          .tab-tips {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: absolute;
            top: 0;
            left: 0;
            padding: 7px 8px;
            width: 150px;
            background: #000000;
            color: #FFFFFF;
            border-radius: 4px;
            z-index: 1;
            line-height: 17px;
            opacity: 0.8;

            &:after {
              content: "";
              position: absolute;
              left: 11px;
              top: 31px;
              width: 0;
              height: 0;
              border-width: 6px 6px 0 6px;
              border-style: solid;
              border-color: #000000 transparent transparent transparent;
            }

            .icon-close {
              width: 14px;
              height: 14px;
              background: url("/img/index/icon_close_hover.png") no-repeat center;
              background-size: 10px;
              cursor: pointer;
            }
          }

          .chat-classify-li {
            position: relative;
            width: 100%;
            height: 30px;

            &.chat-classify-child {
              height: auto;
              display: flex;
              flex-direction: column;

              .chat-classify-li-content {
                height: 30px;
                flex-shrink: 0;
                padding-left: 24px;
              }
            }

            .chat-classify-li-content {
              position: relative;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              padding: 0 10px;
              font-size: 13px;

              &.sel,
              &:hover {
                background: $styleBg2;
                color: $styleColor;
              }

              &.sel {
                font-weight: bold;

                &:after {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0;
                  width: 2px;
                  height: 30px;
                  background: $styleColor;
                }
              }

              .move-icon {
                width: 16px;
                height: 16px;
                background-image: url("/img/workbench/icon_move.png");
                background-repeat: no-repeat;
                background-size: 100%;
                margin-right: 8px;
                flex-shrink: 0;
              }

              .chat-classify-li-name {
                white-space: nowrap;
                flex: 1;
              }

              .badge {
                flex-shrink: 0;
                line-height: 15px;
                font-weight: normal;
                border: none;
                box-shadow: none;
              }

              .chat-classify-operate {
                position: absolute;
                top: 0;
                left: 0;
                width: 27px;
                height: 100%;
                display: flex;
                align-items: center;
                z-index: 1;
                cursor: pointer;

                &:hover {
                  .classify-arrow-icon {
                    &.arrow-up {
                      background-position: -14px 0;
                    }

                    &.arrow-down {
                      background-position: -42px 0;
                    }
                  }
                }

                .classify-arrow-icon {
                  position: absolute;
                  top: 50%;
                  left: 7px;
                  transform: translateY(-50%);
                  width: 14px;
                  height: 14px;
                  flex-shrink: 0;
                  font-size: 14px;
                  background-image: url("/img/index/icon_classify_arrow.png");
                  background-size: 56px 14px;
                  background-repeat: no-repeat;

                  &.arrow-up {
                    background-position: 0 0;
                  }

                  &.arrow-down {
                    background-position: -28px 0;
                  }
                }
              }
            }

            .chat-classify-child {
              flex: 1;
              overflow-y: overlay;

              .chat-classify-li-content {
                padding-left: 24px;
                font-size: 12px;
              }
            }
          }
        }

        .chat-classify-footer {
          height: 30px;
          display: flex;
          border-top: 1px solid #E0E0E0;
          cursor: pointer;

          .chat-classify-footer-btn {
            flex: 1;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;

            &:hover {
              color: $styleColor;
            }

            &:first-child {
              position: relative;

              &:before {
                width: 1px;
                height: 16px;
                position: absolute;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                content: "";
                background: #E0E0E0;
              }
            }
          }
        }
      }

      .chat-list-box {
        position: relative;
        width: 239px;
        height: 100%;
        flex-shrink: 0;

        .chat-list-tips-box {
          height: 36px;
          color: #313131;
          display: flex;
          align-items: center;
          padding: 0 10px;
          border-bottom: 1px solid #E0E0E0;
          border-top-left-radius: 4px;

          &.err {
            background: #FFEDED;

            .tips-icon {
              background-image: url("/img/login/tips.png");
            }
          }

          &.info {
            background: #F9F5D5;

            .tips-icon {
              background-image: url("/img/waitting.gif");
            }
          }

          .tips-icon {
            width: 14px;
            height: 14px;
            flex-shrink: 0;
            background-size: 14px 14px;
            background-repeat: no-repeat;
            margin-right: 8px;
          }

          .tips-btn {
            color: $styleColor;
            cursor: pointer;
            flex-shrink: 0;
          }
        }

        .chat-list-tab {
          width: 100%;
          height: 45px;
          padding: 0 8px;
          display: flex;
          align-items: center;
          background: $styleBg1;
          border-top-left-radius: 4px;
          border-bottom: 1px solid #E0E0E0;

          .chat-list-ul-box {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;

            .chat-list-ul {
              width: calc(100% - 20px);
              height: 100%;
              display: flex;
              align-items: center;
              overflow-x: auto;

              .tab {
                display: flex;
                align-items: center;
                height: 100%;
                line-height: 100%;
                padding: 0 8px;
                position: relative;
                color: #666666;
                flex-shrink: 0;
                cursor: pointer;
                font-size: 13px;

                &:first-child {
                  margin-left: 0;
                }

                &:hover {
                  color: $styleColor;
                }

                &.sel {
                  color: $styleColor;
                  font-weight: bold;

                  &:after {
                    content: "";
                    width: 12px;
                    height: 3px;
                    background: $styleColor;
                    position: absolute;
                    left: 50%;
                    bottom: 0;
                    transform: translateX(-50%);
                  }
                }
              }
            }
          }

          .chat-list-name-box {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;

            .chat-list-name {
              flex: 1;
              padding-left: 2px;
              font-size: 14px;
              font-weight: bold;
              line-height: 20px;
              color: #333333;
            }
          }
        }

        .comp-chat-list {
          width: 100%;
          height: calc(100% - 45px);

          &.has-tip {
            height: calc(100% - 45px - 36px);
          }
        }
      }
    }
  }

  .layout-right {
    .chat-list-box {
      width: 100%;
      height: 100%;

      .chat-list-header {
        width: 100%;
        height: 45px;
        display: flex;
        align-items: center;
        padding: 0 16px;
        font-size: 16px;
        cursor: pointer;
        border-bottom: 1px solid rgba(216, 216, 216, 0.4);
        color: #333333;
        font-weight: bold;
      }

      .comp-chat-list {
        height: calc(100% - 45px);
      }
    }

    .chat-content-box {
      width: 100%;
      height: 100%;

      .chat-none,
      .chat-all {
        position: relative;
        width: 100%;
        height: 100%;

        &.chat-none {
          display: flex;
          justify-content: center;

          .none-tips-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: #666666;
            line-height: 17px;

            img {
              width: 80px;
              margin: 230px 0 10px;
            }
          }
        }
      }
    }
  }
}
</style>