<!-- 快速行动卡片 -->
<template>
  <div class="card">
    <div class="head" :title="body?.title || '快速行动' ">
      <div class="title line-clamp">
        {{ body?.title || '快速行动' }}
      </div>
    </div>
    <div class="body">
      <div class="item">
        <label class="label">截止时间：</label>
        <span class="value">{{ body?.stopTime || '' }}</span>
      </div>
      <div class="item"  :title=" body?.actionDescription || '' ">
        <label class="label">行动说明：</label>
        <span class="value line-clamp8">{{ body?.actionDescription || '暂无行动说明'}}</span>
      </div>

      <div class="item"  v-if="body?.taskFrom?.length">
        <label class="label">本次任务：</label>
        <div class="value" >
          <div v-for="(item,index) in body.taskFrom">
           {{index+1}}、{{ item.taskName }}
          </div>
        </div>
      </div>

    </div>
    <div class="footer">
      <div class="button" @click="onLookProgress">查看进度</div>
      <div class="button" @click="onOpenQuickActing">数据填报</div>
    </div>
  </div>

</template>
<script setup>
import {toRefs} from "vue";
import {emitMsg, getFreeLoginUrl, linkFormat, openAppUrl} from "@/utils";
import {quickActingSearchFilling} from "@utils/net/api";
import {toast} from "@comp/ui";
const props = defineProps({
  body: Object,
})
const {body} = toRefs(props)
// 查看进度
async function onLookProgress(){
  const url = await getFreeLoginUrl(`${linkFormat(body.value.pcProgressUrl)}/${body.value.id}`,'jjsHome')
  emitMsg("msg", {
    type: "window", newWin: 1, name: "lookProgress", width: 1200, height: 800, changePath: true, frame: true,resizable:false,
    key:"imWebview",
    path: url,
  });

}
// 打开数据填报
async function onOpenQuickActing(){
  const { success,errorInfo} = await quickActingSearchFilling({batchNo: body.value.id,isLook:1});
  if(!success){
    toast({title: errorInfo, type: 3});
    return;
  }
  let thisUserInfo = store.getters.getUserInfo;
  const url = await getFreeLoginUrl(`${linkFormat(body.value.pcFillInUrl)}?batchNo=${body.value.id}&empNumber=${thisUserInfo.workerId}`,'jjsHome')
  emitMsg("msg", {
    type: "window", newWin: 1, name: "openQuickActing", width: 410, height: 450, minWidth: 410, minHeight: 450, changePath: true, frame: true,resizable:false,
    key:"imWebview",
    path: url,
  });
}
</script>
<style lang="scss" scoped>
.card{
  width: 278px;
  max-width: 100%;
  border-radius: 4px;
  overflow: hidden;
  line-height: normal;

  .head{
    padding: 16px;
    background-color: $styleColor;
    background-image: url(/img/schedule/smart_card_bg.png);
    background-repeat: no-repeat;
    background-size: 278px 74px;

    .title{
      font-size: 16px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 22px;
    }
  }

  .body{
    padding: 8px 16px;

    .item{
      margin-bottom: 8px;
      display: flex;
      justify-content: flex-start;

      .label{
        min-width: 60px;
        font-size: 12px;
        color: #666666;
      }
      .value{
        font-size: 12px;
        color: #000000;
      }
    }
  }
  .footer{
    display: flex;
    justify-content: space-around;
    //margin-bottom: 8px;
    padding:0 16px 12px 16px;

    .button{
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      height: 32px;
      border-radius: 2px;
      border: 1px solid $styleColor;
      color: $styleColor;
      cursor: pointer;
      user-select: none;
      flex:1;
    }
    .button + .button{
      margin-left: 10px;
    }
  }

  .line-clamp{
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    white-space: normal;
    overflow: hidden;
    display: -webkit-box;
  }

  .line-clamp8{
    -webkit-line-clamp: 8;
    -webkit-box-orient: vertical;
    white-space: normal;
    overflow: hidden;
    display: -webkit-box;
  }
}
</style>
