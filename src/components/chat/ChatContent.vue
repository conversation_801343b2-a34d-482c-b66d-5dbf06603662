<template>
  <div class="chat-content">
    <!--会话信息-->
    <div class="chat-detail">
      <!--头部-->
      <div class="chat-header">
        <div class="session-info">
          <div class="info-box">
            <div class="info">
              <span v-show="isShowBack()" class="back" @click="backList()"></span>
              <span v-show="!isInputObj.name1" class="name" :class="[sessionInfo.detailInfo?.empStatus==4?'resign':'']"
                    :title="sessionInfo.detailInfo?.userShowName || sessionInfo.detailInfo?.name"
                    @click="showUserInfo(sessionInfo, $event)">{{ sessionInfo.detailInfo?.userShowName || sessionInfo.detailInfo?.name }}</span>
              <img v-show="false" class="avatar win-no-drag session-avatar" :src="sessionInfo.detailInfo?.avatar" alt=""
                   :onerror="avatarError.bind(this, sessionInfo.scene, sessionInfo.to, '')">
              <span v-show="sessionInfo.detailInfo?.levelName&&sessionInfo.detailInfo?.levelName!='-'" class="user-label-level">{{ sessionInfo.detailInfo?.levelName }}</span>
              <div class="user-label-box" v-show="userWearPic(sessionInfo.detailInfo)||sessionInfo.detailInfo?.wearType==2">
                <img v-show="sessionInfo.detailInfo?.wearType!=2&&userWearPic(sessionInfo.detailInfo)" :src="userWearPic(sessionInfo.detailInfo)" class="user-label-icon" :onerror="hideElm">
                <div v-show="sessionInfo.detailInfo?.wearType==2" class="user-label-text" :title="sessionInfo.detailInfo?.wearName">{{ sessionInfo.detailInfo?.wearName }}</div>
              </div>
              <div v-show="isAi(sessionInfo.to)" class="user-label-box-ai">{{ sessionInfo.detailInfo?.lable || "数字人" }}</div>
              <input v-show="isInputObj.name1" class="name input-team-name" ref="teamNameRef" type="text" v-model="inputName" maxlength="25"
                     @blur="changeInputName(2,1)" @keydown.enter="changeInputName(3,1)" @keydown.esc.stop="changeInputName(5,1)">
              <span v-show="isFcwList(sessionInfo)&&sessionInfo.to==fcwObj.to&&fcwObj.zsLabel" class="fcw-zs-label" content="该客户将您设置为了专属经纪人，您已拥有该用户的专属进线">专属</span>
              <span v-show="isFcwList(sessionInfo)&&sessionInfo.to==fcwObj.to&&fcwObj.url" class="fcw-link" @click="openLink(fcwObj.url)">客户详情&gt;</span>
              <span v-show="sessionInfo.detailInfo?.serverCustom?.typeName" :class="['team-type-box',sessionInfo.detailInfo?.serverCustom?.status==2?'status1':'']" @click="changeTeamType()">
                {{ sessionInfo.detailInfo?.serverCustom?.typeName }}{{ sessionInfo.detailInfo?.serverCustom?.typeStatus ? "(" + sessionInfo.detailInfo.serverCustom.typeStatus + ")" : "" }}
              </span>
            </div>
            <div class="intr">
              <div v-show="sessionInfo.scene=='p2p'&&sessionInfo.to==aiObj.workerNo" class="intr-text textEls">
                <span>我是你的24小时小帮手，随时解答业务知识，人事考勤等问题、帮你找资料，让你工作又快又轻松！</span>
              </div>
              <div v-show="sessionInfo.scene=='p2p'&&sessionInfo.to!=aiObj.workerNo" class="intr-text textEls"
                   :title="userInfo.workerNo == sessionInfo.to ? '发送给自己，可以同步到手机。' : sessionInfo.detailInfo?.selfIntro">
                {{ userInfo.workerNo == sessionInfo.to ? "发送给自己，可以同步到手机。" : sessionInfo.detailInfo?.selfIntro }}
              </div>
              <div v-show="sessionInfo.scene!='p2p'&&userTeamInfo.hasRobot" class="intr-text textEls">您可以通过"@小乐+问题"的形式咨询问题</div>
            </div>
          </div>
        </div>
        <div class="tab-type">
          <!--智能助理tab-->
          <div v-show="sessionInfo.to==aiObj.workerNo&&aiObj.currentApp?.id==1" class="gpt-tips" @click="modifyAiSettings(3,1)">
            <i class="icon-question show-text-hover"></i>
            <span>使用GPT4.0</span>
            <i :class="['icon-switch',aiObj.gpt4?'show':'']"></i>
          </div>
          <!--非客户tab-->
          <ul v-show="sessionInfo.to!=aiObj.workerNo&&!isFcwList(sessionInfo)" class="tab-ul">
            <li @click="showTagClick(1)" :class="{'tab-li':true,'curr':showTag==1||showTag==5}">{{ isSubOrSer(sessionInfo.id) ? "首页" : "聊天" }}</li>
            <li @click="showTagClick(2)" v-show="sessionInfo.scene!='p2p'&&!sessionInfo.detailInfo?.notInTeam"
                :class="{'tab-li':true,'curr':showTag==2,'unread':noticeUnread.indexOf(sessionInfo.to)>-1}">公告
            </li>
            <li @click="showTagClick(3)" v-show="!isSubOrSer(sessionInfo.id)&&!sessionInfo.detailInfo?.notInTeam" :class="{'tab-li':true,'curr':showTag==3}">文件</li>
            <li @click="showTagClick(4)" v-show="sessionInfo.scene!='p2p'&&!sessionInfo.detailInfo?.notInTeam" :class="{'tab-li':true,'curr':showTag==4}">成员</li>
            <li @click="showTagClick(8)" v-show="sessionInfo.scene!='p2p'&&userTeamInfo.type&&userTeamInfo.type!='normal'&&!sessionInfo.detailInfo?.notInTeam"
                :class="{'tab-li':true,'curr':showTag==8}">群应用
            </li>
            <li @click="showTagClick(6)" v-show="isSubOrSer(sessionInfo.id)" :class="{'tab-li':true,'curr':showTag==6}">账号信息</li>
          </ul>
          <!--客户tab-->
          <ul v-show="sessionInfo.to!=aiObj.workerNo&&isFcwList(sessionInfo)" class="tab-ul">
            <li class="tab-li" v-show="sessionInfo.scene=='p2p'" @click="showLyDialog(1)">备注</li>
            <li class="tab-li" v-show="sessionInfo.scene=='p2p'||sessionInfo.detailInfo?.groupType==2" @click="showLyDialog(2)">举报</li>
            <li @click="showTagClick(1)" v-show="sessionInfo.scene!='p2p'" :class="{'tab-li':true,'curr':showTag==1||showTag==5||showTag==7}">聊天</li>
            <li @click="showTagClick(4)" v-show="sessionInfo.scene!='p2p'" :class="{'tab-li':true,'curr':showTag==4}">成员</li>
          </ul>
        </div>
      </div>

      <!-- 主体 -->
      <ul class="chat-body">
        <!-- 网络提示 -->
        <NetTips></NetTips>
        <!-- 聊天 -->
        <li class="chat-body-li chat-content-box" v-show="showTag==1||showTag==4||showTag==5||showTag==7||showTag==8" ref="chatContentRef">
          <!--主内容框-->
          <div class="chat-content-main" :class="[showTag==4||showTag==5?'resize':'','resize-'+showTag]">
            <!--顶部提示区-->
            <div class="top-tips textEls notCopy" v-show="sessionInfo.to==aiObj.workerNo&&aiObj.gpt4Tips&&aiObj.currentApp?.id==1">
              <span>GPT4.0将以额外的消耗为成本，为您带来更智能的问答体验</span>
              <i class="icon-close" @click="modifyAiSettings(3,2)"></i>
            </div>
            <!--聊天内容-->
            <div id="contentBox" class="msg-content-box" ref="msgBoxRef" @scroll="msgScroll($event,1)" @dragover.prevent
                 :style="'height:calc(100%'+(isSubOrSer(sessionInfo.id)?'':' - 150px')+((aiToolsType||quickActing.show )&&teamToolShowType==1? ' - 44px':lastMsgTipsFlag?' - 30px':'')+' - '+editorResizeObj.height+'px)'"
                 @paste="doEditor($event, 1)" @drop="doEditor($event, 2)">
              <div class="chat-not-more" v-show="msgList.length>=100&&!isSubOrSer(sessionInfo.id)">
                <span>更多消息请在消息记录中查阅，</span>
                <span class="chat-tips" @click="showTagClick(5)">打开消息记录</span>
              </div>
              <div class="chat-not-more gray" v-show="isSubOrSer(sessionInfo.id)&&sessionInfo.notMore&&msgList.length>0">—————— 没有更多了 ——————</div>
              <ChatMsg ref="msgRef" :toNotice="toNotice" msgType="1" :changeMultiple="changeMultiple" :showQuoteDetail="showQuoteDetail" :showDocOp="showDocOp"></ChatMsg>
              <div class="no-msg" v-show="isSubOrSer(sessionInfo.id)&&msgList.length==0">
                <img src="/img/content_none.png" width="180" height="110"/>
                <p>暂无相关信息哦</p>
              </div>
            </div>
            <div :class="['msg-bottom-box',lastMsgTipsFlag?'show-last-msg':'']" v-show="aiToolsType==2||lastMsgTipsFlag||teamToolShowType||quickActing.show">
              <div class="last-msg-tips textEls notCopy" v-show="lastMsgTipsFlag" v-html="getPrimaryMsg(3)" @click="setAutoScroll('bottom')"></div>
              <ul class="ai-tools-ul" v-show="(aiToolsType==2 || quickActing.show ) && teamToolShowType==1 ">
                <li  v-show="aiToolsType==2" class="ai-tools-li ai-icon textEls notCopy" @click="haitTeamAi(teamAiObj.aiInfo)">问答助手</li>
                <li  v-show="quickActing.show" class="ai-tools-li quick-acting-icon textEls notCopy" @click="onOpenQuickActing">快速行动</li>
              </ul>
              <div class="toggle-show-team-ai textEls notCopy" v-show="teamToolShowType" @click="toggleTeamToolShowType(2)">
                <span>{{ teamToolShowType == 1 ? "收起" : "展开" }}</span>
                <span :class="['show-arrow', teamToolShowType == 1 ? 'arrow-bottom' : 'arrow-top']"></span>
              </div>
            </div>

            <!--底部显示区-->
            <div class="editor-show-box notCopy" :style="'height: '+(150+editorResizeObj.height)+'px'" v-show="!isSubOrSer(sessionInfo.id)">
              <!--拖拽条-->
              <div class="editor-resize" @mousedown="editorResize($event,1)" @mouseup="editorResize($event,3)"></div>
              <!--编辑框-->
              <div class="editor-box" v-show="!isMultiple&&showEditorFlag">
                <!--工具栏-->
                <div class="tool-box">
                  <ul class="tool-ul">
                    <li v-show="!isAi(sessionInfo.to,true)" class="tool-li icon-editor emoji tool-arrow-sel-box" title="选择表情" @click.stop="showToolSel(1)">
                      <!--表情弹窗-->
                      <Emoji :isShow="showToolType==1" :bottom="emojiObj.bottom" :left="emojiObj.left" :selEmoji="selEmoji" :showEmojiDone="showEmojiDone" ref="emojiRef"></Emoji>
                    </li>
                    <li class="tool-li icon-editor pic" title="选择图片" @click="selFile(1)"></li>
                    <li class="tool-li icon-editor file" title="选择文件" @click="selFile(2)"></li>
                    <li class="tool-li icon-editor screenshot" :class="{'hover':showToolType==2}" title="新增马赛克、多种标记功能" @click="openJt()">
                      <div class="tool-arrow-box tool-arrow-sel-box" @click.stop="showToolSel(2)">
                        <span class="tool-arrow" :class="{'hover':showToolType==2}"></span>
                        <ul class="tool-sel-ul" v-show="showToolType==2">
                          <li class="tool-sel-li" :class="settings.type5==1||!settings.type5?'sel':''" @click.stop="modifySettings(5,1)">内测：屏幕截图</li>
                          <li class="tool-sel-li" :class="settings.type5==2?'sel':''" @click.stop="modifySettings(5,2)">内测：截图时隐藏当前窗口</li>
                          <li class="tool-sel-li" :class="settings.type5==3?'sel':''" @click.stop="modifySettings(5,3)">
                            屏幕截图 {{ settings["type6"] }}{{ settings.jtConflict ? " (冲突)" : "" }}
                          </li>
                          <li class="tool-sel-li" :class="settings.type5==4?'sel':''" @click.stop="modifySettings(5,4)">截图时隐藏当前窗口</li>
                        </ul>
                      </div>
                    </li>
                    <li v-show="sessionInfo.scene=='p2p'&&!isAi(sessionInfo.to,true)" class="tool-li icon-editor shake" title="发送窗口抖动" @click="windowShake()"></li>
                    <!--群提醒-->
                    <li class="tool-li icon-editor tool-arrow-sel-box" @click.stop="showToolSel(3)"
                        :class="[sessionInfo.isNoTip||sessionInfo.isHelper?'no-notify':'notify',showToolType==3?'hover':'']"
                        v-show="sessionInfo.scene!='p2p'&&!(sessionInfo.detailInfo&&isDisabledGroup(sessionInfo))">
                      <div class="tool-arrow-box">
                        <span class="tool-arrow" :class="{'hover':showToolType==3}"></span>
                        <ul class="tool-sel-ul" v-show="showToolType==3">
                          <li class="tool-sel-li" :class="!sessionInfo.isNoTip&&!sessionInfo.isHelper?'sel':''" @click.stop="modifySettings(1,1)">接收并提醒</li>
                          <li class="tool-sel-li" :class="sessionInfo.isNoTip?'sel':''" @click.stop="modifySettings(1,2)">接收但不提醒</li>
                          <li class="tool-sel-li" :class="sessionInfo.isHelper?'sel':''" @click.stop="modifySettings(1,3)">收入群助手且不提醒</li>
                        </ul>
                      </div>
                    </li>
                    <!--ai当前应用提示-->
                    <li v-show="sessionInfo.to==aiObj.workerNo&&aiObj.currentApp.id&&aiObj.currentApp.id!=aiObj.detailAppId" class="tool-ai-tips textEls">
                      <span>当前正与“</span>
                      <span class="highlight">({{ aiObj.currentApp.isFree == 1 ? "免费" : "付费" }}){{ aiObj.currentApp.name }}</span>
                      <span>”进行对话</span>
                      <i class="icon-close" @click.stop="modifyAiSettings(4)"></i>
                    </li>
                  </ul>
                  <div class="toggle-slide-box">
                    <div class="record notCopy" :class="showTag==7?'active':''" @click="showTagClick(7)" v-show="isFcwList(sessionInfo)">
                      <i class="icon-editor icon-reply"></i>
                      <span class="record-text" v-show="showTag!=5">快捷回复</span>
                    </div>
                    <div class="record notCopy" :class="showTag==5?'active':''" @click="showTagClick(5)">
                      <i class="icon-editor icon-record"></i>
                      <span class="record-text" v-show="showTag!=7">消息记录</span>
                    </div>
                  </div>
                </div>
                <!--输入框-->
                <div class="editor-content-box" @click="activeEditor(true)">
                  <div id="editor" ref="editorRef"></div>
                </div>
              </div>
              <!--禁言、离职、不在群面板-->
              <div class="editor-tips-box" v-show="!isMultiple&&!showEditorFlag">
                <div class="record notCopy" :class="showTag==5?'active':''" @click="showTagClick(5)">
                  <i class="icon-editor icon-record"></i>
                  <span class="record-text">消息记录</span>
                </div>
                <div class="editor-tips-text" v-if="sessionInfo.scene=='p2p'&&sessionInfo.detailInfo?.empStatus==4">此人已离职，不能聊天了！</div>
                <div class="editor-tips-text blacklist" v-else-if="isBlacklist(sessionInfo.to)">
                  <div>{{ getPerson(sessionInfo.to).workerName }}已被您加入黑名单，是否需要解除黑名单</div>
                  <div class="btn" @click="removeBlacklist(sessionInfo.to)">解除</div>
                </div>
                <div class="editor-tips-text" v-else-if="sessionInfo.scene!='p2p'&&sessionInfo.detailInfo?.custom?.isMute==1||sessionInfo.detailInfo?.mute">
                  全员禁言中，请联系管理员！
                </div>
                <div class="editor-tips-text" v-else-if="sessionInfo.scene!='p2p'&&userTeamInfo.mute">您已被禁言，请联系管理员！</div>
                <div class="editor-tips-text blacklist" v-else-if="sessionInfo.scene!='p2p'&&sessionInfo.detailInfo?.notInTeam">
                  <div>您暂未进群，只能查看聊天记录！</div>
                  <div class="btn" @click="joinTeam(sessionInfo.to)">加入群聊</div>
                </div>
              </div>
              <!--多选-->
              <div class="multiple-box" v-show="isMultiple">
                <ul class="multiple-ul">
                  <li class="multiple-li" @click="multipleSel(1)">
                    <i class="multiple-icon icon1"></i>
                    <span>逐条转发</span>
                  </li>
                  <li class="multiple-li" @click="multipleSel(2)" title="直接转发给智能助理&#13;就能快速生成总结哦">
                    <i class="multiple-icon icon2"></i>
                    <span>合并转发</span>
                  </li>
                  <li v-if="purMap.reportMsg" class="multiple-li" @click="multipleSel(6)">
                    <i class="multiple-icon icon6"></i>
                    <span>霸屏转发</span>
                  </li>
                  <li class="multiple-li" @click="multipleSel(3)">
                    <i class="multiple-icon icon3"></i>
                    <span>收藏</span>
                  </li>
                  <li class="multiple-li" @click="multipleSel(4)">
                    <i class="multiple-icon icon4"></i>
                    <span>删除</span>
                  </li>
                  <li class="multiple-li" @click="multipleSel(5)">
                    <i class="multiple-icon icon5"></i>
                    <span>下载</span>
                  </li>
                  <li class="multiple-li" @click="multipleSel(7)">
                    <i class="multiple-icon icon7"></i>
                    <span>bug/好点子</span>
                  </li>
                </ul>
                <i class="multiple-close" @click="changeMultiple(false)"></i>
              </div>
            </div>
            <div class="operate-msg-box" v-show="!isMultiple&&showEditorFlag&&!isSubOrSer(sessionInfo.id)">
              <div class="operate-left-box">
                <!--新话题-->
                <div class="ai-refresh" v-show="isAi(sessionInfo.to, true)" @click="newTopics(false)">
                  <i :class="['icon-refresh',aiObj.refresh?'icon-refresh-loading':'']"></i>
                  <span class="ai-refresh-text">重新对话</span>
                </div>
                <!--引用消息-->
                <div class="quote-msg-box" v-show="quoteMsg">
                  <div class="quote-msg-content">
                    <div class="textEls2">{{ quoteMsg?.quoteTxt }}</div>
                  </div>
                  <i class="quote-msg-close" @click="delQuoteMsg()"></i>
                </div>
              </div>
              <!--发送/关闭-->
              <div class="operate-right-box">
                <span class="btn notCopy" @click="closeChat">关闭</span>
                <span :class="['btn btn-send notCopy',!sendMessageFlag?'disabled':'']" @click="sendMsg">发送</span>
                <span :class="['send-sel-tool tool-arrow-sel-box',showToolType==4?'hover':'',!sendMessageFlag?'disabled':'']" @click.stop="showToolSel(4)">
                    <ul class="tool-sel-ul" v-show="showToolType==4">
                      <li class="tool-sel-li" :class="settings['type4']==1?'sel':''" @click.stop="modifySettings(4,1)">按Enter键发送消息</li>
                      <li class="tool-sel-li" :class="settings['type4']!=1?'sel':''" @click.stop="modifySettings(4,2)">按Ctrl+Enter键发送消息</li>
                    </ul>
                  </span>
              </div>
            </div>
            <!--@人员/文档-->
            <div class="hait-sel-box notCopy" v-show="haitInfo.haitFlag" @click.stop="activeEditor(true)" ref="haitSelRef">
              <div class="hait-header between">
                <ul class="hait-tab-box">
                  <li v-show="sessionInfo.scene!='p2p'" class="hait-tab" :class="!haitInfo.type?'curr':''" @click="selHaitType(0)">人员</li>
                  <li class="hait-tab" :class="haitInfo.type==1?'curr':''" @click="selHaitType(1)">乐文档</li>
                </ul>
                <div v-show="!haitInfo.type" class="hait-more" @click="showDialog(7)">多选</div>
              </div>
              <div class="hait-content">
                <!--@人员-->
                <ul v-show="sessionInfo.scene!='p2p'" class="hait-detail-box hait-person-box" :class="!haitInfo.type?'curr':''" ref="haitMemberRef">
                  <li v-if="haitMemberList.length == 0" class="hait-loading">
                    <img :class="memberLoading?'loading':''" :src="'/img/'+(memberLoading?'loading_gray.png':'index/msg/doc/none.png')" alt="">
                    <span>{{ memberLoading ? "正在加载" : teamMembersError ? teamMembersError : "暂无相关检索结果" }}</span>
                  </li>
                  <li v-else class="hait-detail textEls" :class="haitInfo.memberIndex==key?'curr':''" v-for="(item, key) in haitMemberList" :key="item.time"
                      @click="confirmHaitInfo(key)" @mouseenter="selHaitInfo(key)">
                    <span class="hait-name textEls">{{ item.name }}</span>
                    <span v-if="isAi(item.workerNo)" class="user-label">{{ item.lable || "数字人" }}</span>
                  </li>
                </ul>
                <!--@文档-->
                <ul class="hait-detail-box hait-doc-box" :class="haitInfo.type==1?'curr':''" ref="haitDocRef">
                  <li v-if="haitDocList.length == 0" class="hait-loading">
                    <img :class="docLoading?'loading':''" :src="'/img/'+(docLoading?'loading_gray.png':'index/msg/doc/none.png')" alt="">
                    <span>{{ docLoading ? "正在加载" : "暂无相关检索结果" }}</span>
                  </li>
                  <li v-else class="hait-detail textEls" :class="haitInfo.docIndex==key?'curr':''" v-for="(item, key) in haitDocList" :key="item.time"
                      @click="confirmHaitInfo(key)" @mouseenter="selHaitInfo(key)" :title="item.title">
                    <img class="doc-icon" :src="`/img/index/msg/doc/${item.property==2?'excel':'doc'}.png`" alt="">
                    <span class="doc-title">{{ item.title }}</span>
                    <span class="doc-intr">{{ item.empName }}({{ item.deptName }})</span>
                  </li>
                </ul>
              </div>
            </div>
            <!--call应用-->
            <div class="hait-sel-box call-sel-box notCopy" v-show="haitInfo.callFlag" @click.stop="activeEditor(true)" ref="callSelRef">
              <ul class="hait-header">
                <li class="hait-tab" :class="haitInfo.type==3?'curr':''" @click="selHaitType(3)">AI应用</li>
                <li class="hait-tab" :class="haitInfo.type==2?'curr':''" @click="selHaitType(2)">业务工具</li>
              </ul>
              <div class="hait-content call-content">
                <!--@ai-->
                <ul class="hait-detail-box call-ai-box" :class="haitInfo.type==3?'curr':''">
                  <ul class="hait-content-box" ref="callAiRef">
                    <li v-if="callAiList.length==0" :class="['hait-loading',callAiLoading?'':'none']">
                      <img :class="callAiLoading?'loading':''" :src="'/img/'+(callAiLoading?'loading_gray.png':'index/msg/doc/none.png')" alt="">
                      <span>{{ callAiLoading ? "正在加载" : "暂无相关检索结果" }}</span>
                    </li>
                    <li v-else class="hait-detail textEls" :class="haitInfo.aiIndex==key?'curr':''" v-for="(item, key) in callAiList" :key="item.id"
                        @click="confirmHaitInfo(key)" @mouseenter="selHaitInfo(key)" :title="item.name">
                      <span :class="['hait-label',item.isFree==1?'hait-label-1':'']">{{ item.isFree == 1 ? "免费" : "付费" }}</span>
                      <span class="doc-title" v-html="getHighlight(htmlEscapeAll(item.name), haitInfo.haitText?.slice(1))"></span>
                    </li>
                  </ul>
                  <li v-if="!callAiLoading" class="hait-detail textEls hait-ai-more" @click="openLink(aiObj.moreApp)">
                    <span>创建更多应用</span>
                    <i class="show-arrow arrow-right"></i>
                  </li>
                </ul>
                <!--@应用-->
                <ul class="hait-detail-box call-app-box" :class="haitInfo.type==2?'curr':''" ref="callAppRef">
                  <li v-if="callAppList.length==0" :class="['hait-loading',callAppLoading?'':'none']">
                    <img :class="callAppLoading?'loading':''" :src="'/img/'+(callAppLoading?'loading_gray.png':'index/msg/doc/none.png')" alt="">
                    <span>{{ callAppLoading ? "正在加载" : "暂无相关检索结果" }}</span>
                  </li>
                  <li v-else class="hait-detail textEls" :class="haitInfo.appIndex==key?'curr':''" v-for="(item, key) in callAppList" :key="item.id"
                      @click="confirmHaitInfo(key)" @mouseenter="selHaitInfo(key)" :title="item.title">
                    <span class="doc-title" v-html="getHighlight(htmlEscapeAll(item.title), haitInfo.haitText?.slice(1))"></span>
                  </li>
                </ul>
              </div>
            </div>
            <!--特别关心/@内容-->
            <div class="float-msg-box selNone" v-show="sessionInfo.scene != 'p2p'">
              <div class="float-content-box" v-show="haitMsgList.length>0">
                <div class="float-hait" :class="haitActive?'active':''" v-show="!showHaitFlag" @click="showFloat(1)">有人@我</div>
                <div class="float-content" :class="showHaitMsgFlag?'':'hide'" v-show="showHaitFlag">
                  <div class="float-label-box" @click="toggleFloatMsg(1)">
                    <span>有人@我</span>
                    <span class="icon-box">
                    <i class="icon-toggle"></i>
                    <i class="icon-close" @click.stop="removeFloatMsg(1)"></i>
                  </span>
                  </div>
                </div>
                <!--详细信息-->
                <ul class="float-detail" v-show="showHaitFlag&&showHaitMsgFlag">
                  <li class="float-li" v-for="(item,key) in haitMsgList" :key="item.time" @click.stop="scrollMsg(msgBoxRef,item.idServer)">
                    <div class="avatar-box">
                      <img class="avatar" :src="getPerson(item.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.from, '')">
                    </div>
                    <div class="float-msg-content">
                      <div class="user-name">{{ getPerson(item.from).name }}</div>
                      <div class="msg-intr textEls3" v-html="getPrimaryMsg(1, item)"></div>
                    </div>
                  </li>
                </ul>
              </div>

              <div class="float-content-box" v-show="concernMsgList.length>0" @click="showFloat(2)">
                <div class="float-concern" v-show="!showConcernFlag">{{ concernMsgList.length }}个特别关心</div>
                <div class="float-content" :class="showConcernMsgFlag?'':'hide'" v-show="showConcernFlag">
                  <div class="float-label-box" @click="toggleFloatMsg(2)">
                    <span class="concern-tips">特别关心</span>
                    <span class="icon-box">
                    <i class="icon-toggle"></i>
                    <i class="icon-close" @click.stop="removeFloatMsg(2)"></i>
                  </span>
                  </div>
                </div>
                <!--详细信息-->
                <ul class="float-detail" v-show="showConcernFlag&&showConcernMsgFlag">
                  <li class="float-li" v-for="(item,key) in concernMsgList" :key="item.time" @click.stop="scrollMsg(msgBoxRef,item.idServer)">
                    <div class="avatar-box">
                      <img class="avatar" :src="getPerson(item.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.from, '')">
                    </div>
                    <div class="float-msg-content">
                      <div class="user-name">{{ getPerson(item.from).name }}</div>
                      <div class="msg-intr textEls3" v-html="getPrimaryMsg(1, item)"></div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <!--引用详细详情弹窗-->
            <div class="msg-quote-detail-box selAll" :class="{'left':quoteDetailObj.leftFlag,'show':quoteDetailObj.flag}" ref="quoteDetailRef" @click.stop="stopClick()">
              <div class="quote-arrow" :style="'top:'+(quoteDetailObj.top?(quoteDetailObj.top+'px'):'50%')"></div>
              <div class="quote-scroll-box">
                <ChatMsg :sessionInfo="sessionInfo" :searchList="quoteDetailObj.msg" msgType="2" :showDocOp="showDocOp" :notScroll="true"></ChatMsg>
              </div>
            </div>
            <!--文档权限操作弹窗-->
            <ul class="doc-msg-op-box" :class="{'show':docOpObj.flag}" ref="docOpRef">
              <li :class="{'curr': docOpObj.docPur==1}" @click="changeDocPur(1)">可阅读</li>
              <li :class="{'curr': docOpObj.docPur==2}" @click="changeDocPur(2)">可编辑</li>
              <li :class="{'curr': docOpObj.docPur==3}" @click="changeDocPur(3)">移除权限</li>
            </ul>
          </div>
          <!--成员、消息记录、主内容切换区-->
          <div class="chat-content-slide" v-if="sessionInfo.scene!='p2p'&&showTag==1" v-show="!sessionInfo.detailInfo?.notInTeam">
            <i class="slide" @click="showTagClick(4)"></i>
          </div>
          <!--成员-->
          <div class="chat-content-member" v-if="sessionInfo.scene!='p2p'&&sessionInfo.detailInfo&&showTag==4">
            <i class="slide" @click="showTagClick(1)"></i>
            <div class="chat-team-info">
              <div class="chat-team-title">
                <span>{{ sessionInfo.detailInfo?.detailType == "group" ? "讨论组" : "群" }}名称</span>
                <div class="chat-team-edit-box" v-show="!isDisabledGroup(sessionInfo)">
                  <i v-if="!isInputObj.name2" v-show="sessionInfo.detailInfo?.detailType=='group'" class="icon-member chat-team-update"
                     @click="changeInputName(1,2)"></i>
                  <span v-else class="chat-team-update-save" @click="changeInputName(2,2)">保存</span>
                  <i class="icon-member chat-team-qrcode" @click="showTeamQrcode(true)"></i>
                </div>
              </div>
              <div v-show="!isInputObj.name2" class="chat-team-name textEls" :title="sessionInfo.detailInfo?.name">{{ sessionInfo.detailInfo?.name }}</div>
              <input v-show="isInputObj.name2" class="chat-team-name input-team-name" type="text" v-model="inputName" maxlength="25" @keydown.esc.stop="changeInputName(5,2)">
              <div class="chat-team-title">创建者</div>
              <div class="chat-team-owner textEls">{{ getPerson(sessionInfo.detailInfo?.owner).name }}</div>
            </div>
            <div class="chat-team-member" :class="teamMembersTotal!=1?'show-page':''">
              <div class="chat-member-title">
                <span>
                  {{ sessionInfo.detailInfo?.detailType == 'group' ? "讨论组" : "群" }}成员({{ sessionInfo.detailInfo?.memberNum }}/{{ sessionInfo.detailInfo?.level }})
                </span>
                <div class="chat-member-icon">
                  <i class="icon-member chat-member-mute" v-show="userTeamInfo.type!='normal'" @click="muteTeam()"
                     :class="sessionInfo.detailInfo?.custom?.isMute==1||sessionInfo.detailInfo?.mute?'muted':''"
                     :title="sessionInfo.detailInfo?.custom?.isMute==1||sessionInfo.detailInfo?.mute?'解除全体禁言':'点击开启全员禁言，只有群主和管理员才能发言'"></i>
                  <i class="icon-member chat-member-search" :class="memberSearchFlag?'active':''" @click="toggleMemberSearchFlag()" title="搜索人员"></i>
                </div>
              </div>
              <div class="chat-member-search-box" v-show="memberSearchFlag">
                <input class="search-member-input" type="text" placeholder="请搜索群成员" v-model="searchTeamMemberObj.text" @input="searchTeamMember()"
                       @keyup.enter="confirmSearchMember()" @keydown.up.prevent="selSearchMember('pre')" @keydown.down.prevent="selSearchMember('next')"
                       @keydown.esc.prevent="hideSearchMember()">
                <i class="search-member-close" v-show="searchTeamMemberObj.text.length>0" @click="searchTeamMember(1)"></i>
                <!--搜索结果-->
                <div class="search-member-box" v-show="searchTeamMemberObj.show">
                  <div class="search-member-none" v-if="searchTeamMemberObj.list.length==0">无数据</div>
                  <ul class="search-member-ul" v-else>
                    <li class="search-member-li textEls" :class="searchTeamMemberObj.index==key?'curr':''" v-for="(item,key) in searchTeamMemberObj.list"
                        :key="item.account" :title="item.detailInfo.name+'('+item.detailInfo.cityName+')'" @click="confirmSearchMember()"
                        @mouseenter="selSearchMember(key)">
                      <div class="avatar-box">
                        <img class="avatar" :src="item.detailInfo.avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.account, '')">
                      </div>
                      <span class="user-name textEls">{{ item.detailInfo.name }}({{ item.detailInfo.cityName }})</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="chat-member-loading" v-show="teamMembersLoading"></div>
              <!--群员列表-->
              <ul class="chat-member-ul" :class="memberSearchFlag?'show-search':''" v-show="!teamMembersLoading&&showTag==4">
                <!--群成员获取错误提示-->
                <li class="chat-member-err" v-show="teamMembersError&&teamMembersPageList.length==0">{{ teamMembersError }}</li>
                <li class="chat-member-li" :class="item.curr?'curr':''" v-for="(item,key) in teamMembersPageList" :key="item.account"
                    @click="showCurr(key)" @dblclick="openChat('p2p',item.account)" @contextmenu="setMenu($event, item)"
                    :title="item.detailInfo?(item.detailInfo.userShowName||item.detailInfo.name)+(item.detailInfo.cityName?('('+item.detailInfo.cityName+')'):''):''">
                  <div class="chat-member-box" v-if="item.detailInfo">
                    <div class="avatar-box">
                      <img class="avatar notCopy" :src="item.detailInfo.avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.account, '')">
                    </div>
                    <span class="user-name textEls selAll">{{ (item.detailInfo.userShowName || item.detailInfo.name) + (item.detailInfo.cityName ? ('(' + item.detailInfo.cityName + ')') : '') }}</span>
                    <i class="user-icon" @click="removeMember(item)" :class="item.type=='owner'?'icon-member owner':'',item.mute&&item.type=='normal'?'icon-member muted':'',
                     userTeamInfo.type!='normal'&&item.type=='normal'&&!isDisabledGroup(sessionInfo)?'del':''"></i>
                  </div>
                </li>
              </ul>
            </div>
            <div class="chat-team-operate">
              <!--分页-->
              <div class="chat-team-page-box" v-show="teamMembersTotal!=1">
                <div class="chat-team-page" @click="toggleMemberPageFlag()">{{ teamMembersPage }}/{{ teamMembersTotal }}</div>
                <div class="chat-team-page-btn">
                  <i class="chat-team-page-icon left" @click="changeMemberPage('pre')"></i>
                  <i class="chat-team-page-icon right" @click="changeMemberPage('next')"></i>
                </div>
                <ul class="chat-team-page-ul" v-show="memberPageFlag">
                  <li class="chat-team-page-li" v-for="item in teamMembersTotal" :key="item" @click="changeMemberPage(item)">
                    {{ item }}/{{ teamMembersTotal }}
                  </li>
                </ul>
              </div>
              <div class="chat-team-operate-detail" v-show="!isDisabledGroup(sessionInfo)">
                <span class="chat-team-invite" @click="openInvite" v-show="userTeamInfo.type!='normal'||sessionInfo.detailInfo?.detailType=='group'">
                  <span class="chat-team-invite-text">邀请好友</span>
                </span>
                <span class="chat-team-leave" @click="leaveOrDismissTeam()"
                      v-show="(userTeamInfo.type=='normal'&&sessionInfo.detailInfo?.detailType!='group')||sessionInfo.detailInfo?.detailType=='group'">{{
                    sessionInfo.detailInfo?.owner == userInfo.workerNo && sessionInfo.detailInfo?.detailType == "group" ? "解散" : "退出"
                  }}{{
                    sessionInfo.detailInfo?.detailType == "group" ? "讨论组" : "该群"
                  }}</span>
              </div>
            </div>
          </div>
          <!--消息记录-->
          <div class="record-box" v-if="showTag==5">
            <i class="slide" @click="showTagClick(1)"></i>
            <!--消息记录-->
            <div class="record-msg-box" :class="{'show-search':searchMsgObj.flag,'no-show-back':!searchMsgObj.back}" v-show="!searchMsgObj.show">
              <div class="search-msg-tip" v-show="searchMsgObj.back">
                <span class="search-msg-text">
                  {{ searchMsgObj.total > 0 ? "已搜索到" + searchMsgObj.total + "条消息记录" : "搜索找不到结果" }}
                </span>
                <span class="search-msg-back" @click="backSearchMsg()">返回</span>
              </div>
              <div class="search-msg-content" ref="recordMsgRef" @scroll="msgScroll($event,3)">
                <ChatMsg :toNotice="toNotice" :searchList="recordList" :sessionInfo="sessionInfo" msgType="3"
                         :showQuoteDetail="showQuoteDetail" :showDocOp="showDocOp" :scrollElm="recordMsgRef"></ChatMsg>
              </div>
            </div>
            <!--搜索的消息记录-->
            <div class="search-msg-box" v-show="searchMsgObj.show">
              <div class="search-msg-tip">
                <span class="search-msg-text" v-show="searchMsgObj.search">
                  {{ searchMsgObj.total > 0 ? "已搜索到" + searchMsgObj.total + "条消息记录" : "搜索找不到结果" }}
                </span>
                <span class="search-msg-back" @click="toggleSearchMsg(true)">返回</span>
              </div>
              <div class="search-msg-content" ref="searchMsgRef">
                <ChatMsg :toNotice="toNotice" :searchList="searchMsgObj.list" :sessionInfo="sessionInfo" msgType="4"
                         :strKey="searchMsgObj.searchText" :showRecord="showRecord" :showQuoteDetail="showQuoteDetail" :showDocOp="showDocOp" :scrollElm="searchMsgRef">
                </ChatMsg>
              </div>
            </div>
            <!--消息记录搜索-->
            <div class="record-search-box" v-show="searchMsgObj.flag">
              <input class="record-search-input" type="text" placeholder="输入关键字" v-model="searchMsgObj.text" @keyup.enter="queryCloudMessage(1, true)">
              <span class=" record-search-btn" @click="queryCloudMessage(1, true)">确定</span>
            </div>
            <!--消息记录操作-->
            <div class="record-operate">
              <div class="record-operate-set">
                <i class="record-search-icon" @click="toggleSearchMsg()"></i>
                <span class="record-time" @click.stop="showCalendar()">{{ dateFormat(selSearchDate, "yyyy-MM-dd") }}</span>
                <div class="calendar-box" v-show="calendarFlag" @click.stop="stopClick()">
                  <Calendar ref="calendarRef" :setCurrentDay="setCurrentDay"></Calendar>
                </div>
                <div class="record-clear highlight" v-if="purMap.clearMessage" @click="clearMessage">清理全部记录</div>
              </div>
              <div class="record-page">
                <i class="record-btn left" @click="getMsgHistory('pre');"></i>
                <i class="record-btn right" @click="getMsgHistory('next');"></i>
              </div>
            </div>
          </div>
          <!--快捷回复-->
          <div class="quick-box selAll" v-if="showTag==7">
            <div class="reply-box">
              <dt class="reply-title" @click="showComponents()">
                <span class="reply-content-detail">话术库</span>
                <span class="new-label">new</span>
                <i class="icon-arrow arrow-right"></i>
              </dt>
            </div>
            <dl class="reply-box" :class="{'hide':item.hide}" v-for="(item,key) in quickReplyList" :key="item.id">
              <dt class="reply-title" @click="toggleQuickList(key)">
                <span class="reply-content-detail">{{ item.name }}</span>
                <i class="icon-arrow"></i>
              </dt>
              <dl class="reply-content-box" v-show="!item.hide" v-for="(item1,key1) in item.contentList" :key="item1.id" @click="addQuickReplyContent(item1)">
                <dd class="reply-content">
                  <span class="reply-content-detail">{{ item1.content }}</span>
                  <i class="icon-send"></i>
                </dd>
              </dl>
            </dl>
          </div>
          <!--顶部扩展工具栏-->
          <div :class="['ai-manager-box',teamAiObj.searchInfo.loading?'show-loading-modal':'']" :content="teamAiObj.searchInfo.loadingText" v-if="showTag==8">
            <i class="slide" @click="showTagClick(1)"></i>
            <!--群ai开关和介绍-->
            <div class="ai-show-box ai-show-list" v-if="userTeamInfo.type&&userTeamInfo.type!='normal'&&!sessionInfo.detailInfo?.notInTeam" v-show="toolObj.showType==1">
              <div class="ai-invite-box">
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" :src="teamAiObj.aiInfo.avatar||'-'" :onerror="avatarError.bind(this, 'p2p', teamAiObj.aiInfo.to, 'teamAi')">
                  </div>
                </div>
                <div class="user-detail-box">
                  <div class="ai-invite-name-box">
                    <div class="textEls">{{ teamAiObj.aiInfo.name }}</div>
                    <div :class="['switch-box',aiToolsType==2?'on':'']" @click="toggleTeamAi"></div>
                  </div>
                  <div class="ai-invite-intr-box">
                    <div class="ai-invite-intr-content textEls3" :title="teamAiObj.aiInfo.prologue">
                      <span class="ai-invite-intr-edit" v-show="aiToolsType==2" @click="teamAiIntrEdit">编辑简介</span>
                      {{ teamAiObj.aiInfo.prologue }}
                    </div>
                    <div :class="['ai-invite-to',aiToolsType!=2?'no-show':'']" @click="showTeamAiKnowledge">
                      <div v-show="aiToolsType==2" class="show-arrow arrow-right"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 快速行动和介绍 -->
            <div class="ai-show-box ai-show-list" v-show="toolObj.showType==1">
              <div class="ai-invite-box">
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" src="/img/index/icon_quick_acting.png" :onerror="avatarError.bind(this, 'p2p', teamAiObj.aiInfo.to, 'teamAi')">
                  </div>
                </div>
                <div class="user-detail-box">
                  <div class="ai-invite-name-box">
                    <div class="textEls">快速行动</div>
                    <div :class="['switch-box',quickActing.show?'on':'']"  @click="onEnableQuickActing" ></div>
                  </div>
                  <div class="ai-invite-intr-box">
                    <div class="ai-invite-intr-content textEls3" title="群管理员可以快速发起行动任务，并指定参与人员完成进度填报">
                      <span class="ai-invite-intr-edit"  @click="onOpenQuickActingRename">使用说明</span>
                      群管理员可以快速发起行动任务，并指定参与人员完成进度填报
                    </div>
                    <div class="blank"></div>
                  </div>
                </div>
              </div>
            </div>


            <!--群ai存在且权限操作知识库-->
            <div class="ai-show-box" v-if="aiToolsType==2&&userTeamInfo.type&&userTeamInfo.type!='normal'&&!sessionInfo.detailInfo?.notInTeam" v-show="toolObj.showType==2">
              <div class="ai-manager-back-box">
                <span class="ai-manager-back" @click="toolObj.showType=1">
                  <span class="show-arrow arrow-left"></span>
                  <span>返回</span>
                </span>
              </div>
              <div class="ai-manager-info-box">
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" :src="teamAiObj.aiInfo.avatar||'-'" :onerror="avatarError.bind(this, 'p2p', teamAiObj.aiInfo.to, 'teamAi')">
                  </div>
                </div>
                <div class="ai-manager-info-text-box">
                  <div class="ai-manager-info-name">{{ teamAiObj.aiInfo.name }}</div>
                  <div class="ai-manager-info-detail">
                    <span class="ai-manager-info-detail-title">已绑定的知识库：</span>
                    <span class="ai-manager-info-detail-text textEls" :title="sessionInfo.detailInfo?.name+'知识库'">{{ sessionInfo.detailInfo?.name }}知识库</span>
                  </div>
                </div>
              </div>
              <div class="ai-manager-knowledge-box" @drop="dropKnowledge">
                <div class="ai-manager-knowledge-title-box">
                  <div class="ai-manager-knowledge-title-left">
                    <span>我已学习到的知识</span>
                    <i class="icon-question" @click="openLink(aiObj.teamAiUrl)"></i>
                  </div>
                  <div :class="['ai-manager-knowledge-title-right',teamAiObj.showAdd?'hover':'']" @mouseenter="showAddKnowledge(1)" @mouseleave="showAddKnowledge(3)">
                    <span>添加</span>
                    <ul class="ai-manager-add-ul" v-show="teamAiObj.showAdd">
                      <li class="ai-manager-add-li" @click="addKnowledgeType(1)">添加群文件</li>
                      <li class="ai-manager-add-li" @click="addKnowledgeType(2)">上传本地文件</li>
                      <li class="ai-manager-add-li" @click="addKnowledgeType(3)">选择乐文档</li>
                    </ul>
                  </div>
                </div>
                <div class="ai-manager-knowledge-list-box" v-show="teamAiObj.searchInfo.total>0">
                  <div class="ai-manager-knowledge-list-search-box">
                    <input class="search-input" type="text" placeholder="搜索知识库文件" maxlength="50" v-model.trim="teamAiObj.searchInfo.key" @input="searchKnowledgeList(true)">
                    <span class="icon-close" v-show="teamAiObj.searchInfo.key" @click="resetKnowledgeList"></span>
                  </div>
                  <ul class="ai-manager-knowledge-list-ul" v-show="teamAiObj.searchInfo.list.length>0" @scroll="scrollKnowledge">
                    <li class="ai-manager-knowledge-list-li" v-for="(item,key) in teamAiObj.searchInfo.list" :key="item.id">
                      <div class="ai-manager-knowledge-list-li-left">
                        <img class="file-icon" :src="getFileIcon(item.ext)" alt="">
                        <div class="file-name textEls" :title="item.name">{{ item.name }}</div>
                      </div>
                      <div class="ai-manager-knowledge-list-li-right">
                        <span v-show="item.itemStatus!=1" :class="['file-status',item.itemStatus==1||item.itemStatus==2?'':'file-status-loading']">{{ item.itemStatus == 1 ? "" : item.itemStatus == 2 ? "学习失败" : "学习中" }}</span>
                        <i class="file-close" @click.stop="removeKnowledgeList(item, key)"></i>
                      </div>
                    </li>
                  </ul>
                  <div class="ai-manager-knowledge-list-none" v-show="teamAiObj.searchInfo.list.length==0">
                    <img v-show="!teamAiObj.searchInfo.loading" src="/img/content_search.png" width="160"/>
                    <div v-show="!teamAiObj.searchInfo.loading" class="ai-manager-knowledge-title-text">暂无查询结果</div>
                  </div>
                  <div class="ai-manager-knowledge-tips-box">
                    <div>{{ teamAiObj.fileTips }}</div>
                    <div>{{ teamAiObj.fileTips1 }}</div>
                  </div>
                </div>
                <div class="ai-manager-knowledge-list-none" v-show="teamAiObj.searchInfo.total==0&&!teamAiObj.searchInfo.loading">
                  <img src="/img/content_none.png" width="160"/>
                  <div class="ai-manager-knowledge-title-text">群知识暂无可学习内容</div>
                  <div class="ai-manager-knowledge-intr-text">
                    <div>{{ teamAiObj.fileTips }}</div>
                    <div>{{ teamAiObj.fileTips1 }}</div>
                  </div>
                </div>
                <div class="ai-manager-upload-loading" v-show="teamAiObj.searchInfo.updateLoading">文件上传中，上传完成后将自动开始学习</div>
              </div>
            </div>
          </div>
        </li>
        <!-- 通知 -->
        <li class="chat-body-li" v-if="showTag==2&&showFlag">
          <ChatNotice ref="chatNotice" :sessionInfo="sessionInfo" :setAutoScroll="setAutoScroll"></ChatNotice>
        </li>
        <!-- 文件 -->
        <li class="chat-body-li" v-if="showTag==3&&showFlag">
          <ChatFile :selFile="selFile"></ChatFile>
        </li>
        <!--账号信息-->
        <li class="chat-body-li account-box-li selAll" v-if="showTag==6">
          <div class="account-box">
            <div class="account-content-box">
              <div class="account-content">
                <div class="account-title">{{ sessionInfo.detailInfo?.name }}</div>
                <div class="account-intr-box">
                  <span class="account-intr">所属部门：</span>
                  <span class="account-details">{{ sessionInfo.detailInfo?.deptName }}</span>
                </div>
              </div>
              <div class="user-avatar-box">
                <div class="avatar-box">
                  <img class="avatar" :src="sessionInfo.detailInfo?.avatar" alt="" :onerror="avatarError.bind(this, sessionInfo.scene, sessionInfo.to, '')">
                </div>
              </div>
            </div>
            <div class="account-content-box" v-if="sessionInfo.detailInfo?.admins">
              <div class="account-intr-box">
                <span class="account-intr">管理人员：</span>
                <span class="account-details">
                  <span v-for="(item,key) in sessionInfo.detailInfo?.admins" :key="item.id">
                    {{ item.empName }}{{ key != sessionInfo.detailInfo?.admins.length - 1 ? "，" : "" }}
                  </span>
                </span>
              </div>
            </div>
            <div class="account-content-box" v-if="sessionInfo.detailInfo?.introduce">
              <div class="account-intr-box">
                <span class="account-intr">账号备注：</span>
                <span class="account-details">{{ sessionInfo.detailInfo?.introduce }}</span>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
    <!--群二维码-->
    <div class="team-qrcode-modal win-drag win-no-resize" ref="teamQrcodeRef" v-show="teamQrcodeObj.flag">
      <div class="team-qrcode-box">
        <div class="team-qrcode-title" :title="sessionInfo.detailInfo?.name">
          <div class="team-qrcode-text textEls">{{ sessionInfo.detailInfo?.name }}（二维码）</div>
          <img class="team-qrcode-close" src="/img/close.png" alt="" @click="showTeamQrcode(false)">
        </div>
        <div class="team-qrcode-detail">
          <img class="qrcode-img" :src="teamQrcodeObj.qrcode" alt="">
          <button class="team-qrcode-btn" @click="forwardQrcode()">转发二维码</button>
        </div>
        <img v-show="false" class="team-avatar" :src="sessionInfo.detailInfo?.avatar" :onerror="avatarError.bind(this, sessionInfo.scene, sessionInfo.to, '')">
      </div>
    </div>
    <!-- 切换讨论组类型 -->
    <TeamEditor ref="teamEditorRef"></TeamEditor>
    <!--批量选择bug反馈-->
    <ChatMsg id="multiBugItem" v-show="false" :sessionInfo="sessionInfo" :searchList="multiBugItem" msgType="2" :notScroll="true"></ChatMsg>

    <!--收藏分组弹窗-->
    <LyDialog newClass="dialog-manger-box dialog-move-box" title="选择分组" :width="396" :height="400" :closeOnClickModal="true" :visible="dialogObj.type==1"
              @close="dialogOperate(1,1)" @confirm="dialogOperate(1,2)">
      <div class="main-dialog-box">
        <div class="move-content-box">
          <div class="add-classify-box">
            <span class="add-classify" @click="showDialog(2,1,1)">新建分组</span>
          </div>
          <ul class="move-content-ul">
            <li v-for="(item,key) in dialogObj.moveList" :key="key" @click="dialogObj.moveSelItem=item">
              <span :class="['sel-box',String(dialogObj.moveSelItem.id)==String(item.id)?'sel':'']"></span>
              <span class="textEls">{{ item.name }}</span>
            </li>
          </ul>
        </div>
      </div>
    </LyDialog>
    <!--新建分组弹窗-->
    <LyDialog title="新建分组" :width="383" :height="146" :zIndex="301" :closeOnClickModal="true" :visible="dialogObj.type1==1"
              @close="dialogOperate(2,1)" @confirm="dialogOperate(2,2)" @mouseup.stop="stopPropagation">
      <div class="main-dialog-box">
        <div class="ly-dialog-default-box">
          <label class="ly-dialog-default-label">
            <span class="ly-dialog-default-tips">*</span>
            <span>分组名称：</span>
          </label>
          <div class="ly-dialog-default-detail">
            <input ref="lyDialogInputRef" class="ly-dialog-default-input" v-model.trim="dialogObj.classifyName" maxlength="30" placeholder="最多输入30字"/>
          </div>
        </div>
      </div>
    </LyDialog>
    <!--霸屏转发弹窗-->
    <LyDialog newClass="dialog-forward-report-box" title="霸屏转发"  :width="620" :closeOnClickModal="true" :visible="dialogObj.type==3"
              @close="dialogOperate(3,1)" @confirm="dialogOperate(3,2)" @mouseup.stop="stopPropagation">
      <div class="main-dialog-box" ref="forwardReportRef">
        <div class="forward-report-content-box">
          <div class="forward-report-title-box">
            <span class="tips">*</span>
            <span class="title">人员行为</span>
            <span class="intr-box">
          <span class="intr">将转发至管控范围下当日存在对应行为的下属</span>
        </span>
          </div>
          <div class="forward-report-sel-box">
            <div class="sel-content-box" v-for="(item,key) in dialogObj.reportGroup" :key="key" @click="item.sel=!item.sel">
              <span class="sel-box">
                <i :class="['sel-box-i',item.sel?'sel':'']"></i>
              </span>
              <span>{{ item.label }}</span>
            </div>
          </div>
          <div class="forward-report-title-box">
            <span class="title">转发效果预览</span>
            <span class="intr-box point" @click="showDialog(4)">
              <span class="intr">示例</span>
              <i class="icon-question"></i>
            </span>
          </div>
          <div class="forward-report-msg-box" :style="[dialogObj.reportMsgHeight?'height:'+dialogObj.reportMsgHeight +'px':'']">
            <MsgReport :type="3" :item="dialogObj.reportMsg" :clickImage="clickImage"></MsgReport>
          </div>
        </div>
      </div>
    </LyDialog>
    <!--ai创作应用弹窗-->
    <LyDialog newClass="dialog-ai-app-box" :title="dialogObj.aiAppObj.showName" :width="458" :closeOnClickModal="true" :visible="dialogObj.type==4"
              @close="dialogOperate(4,1)" @confirm="dialogOperate(4,2)" @closeAll="dialogOperate(4,3)" @mouseup.stop="stopPropagation" cancelText="清空所选" okText="开始创作">
      <div :class="['main-dialog-box',dialogObj.aiAppObj.appJson?.length>5?'overflow':'']">
        <div class="dialog-intr textEls3" :title="dialogObj.aiAppObj.prologue">{{ dialogObj.aiAppObj.prologue }}</div>
        <ul class="dialog-content-ul">
          <template v-for="(item,key) in dialogObj.aiAppObj.appJson" :key="key">
            <!--单选框-->
            <li v-if="item.type=='radio'" :class="['dialog-content-li']">
              <label class="dialog-content-li-label">
                <span v-if="item.request" class="dialog-content-li-tips">*</span>
                <span>{{ item.valName }}:</span>
              </label>
              <div :class="['dialog-content-li-details','dialog-content-li-single-sel-box',dialogObj.aiAppShowSelId==item.id?'sel':'']" @click.stop="dialogAiOperate(1,item)">
                <span :class="['dialog-content-li-single-sel-text',item.value?'sel':'']">{{ item.value || item.placeholder || "请选择" }}</span>
                <span class="search-arrow"></span>
                <ul class="dialog-content-li-single-sel" v-show="dialogObj.aiAppShowSelId==item.id" @click.stop="stopPropagation">
                  <li v-for="(item1,key1) in item.options" :key="key1" :class="[item1==item.value?'sel':'']" @click="dialogAiOperate(2,item,item1)">{{ item1 }}</li>
                </ul>
              </div>
            </li>
            <!--输入框-->
            <li v-if="item.type=='input'" class="dialog-content-li">
              <label class="dialog-content-li-label">
                <span v-if="item.request" class="dialog-content-li-tips">*</span>
                <span>{{ item.valName }}:</span>
              </label>
              <div class="dialog-content-li-details dialog-content-li-details-input">
                <input type="text" class="dialog-content-li-input" :placeholder="item.placeholder||'请输入'" :maxlength="item.maxLength||100" v-model.trim="item.value">
              </div>
            </li>
            <!--富文本框-->
            <li v-if="item.type=='textarea'" class="dialog-content-li">
              <label class="dialog-content-li-label">
                <span v-if="item.request" class="dialog-content-li-tips">*</span>
                <span>{{ item.valName }}:</span>
              </label>
              <div class="dialog-content-li-details dialog-content-li-details-textarea">
                <textarea class="dialog-content-li-textarea" :placeholder="item.placeholder||'请输入'" :maxlength="item.maxLength||300" v-model.trim="item.value"></textarea>
                <div class="textarea-intr">{{ (item.value || "").length }}/{{ item.maxLength || 300 }}</div>
              </div>
            </li>
          </template>
        </ul>
      </div>
    </LyDialog>
    <!--群知识上传-->
    <LyDialog newClass="dialog-team-file-box" :title="dialogObj.teamFileObj.type==1?'群知识上传':'添加乐文档'" :width="dialogObj.teamFileObj.type==1?704:530" :height="420"
              :closeOnClickModal="true" :visible="dialogObj.type==5" @close="dialogOperate(5,1)" @confirm="dialogOperate(5,2)" @mouseup.stop="stopPropagation">
      <div :class="['main-dialog-box','team-file-box',dialogObj.teamFileObj.type==1?'':'doc-box']" ref="teamFileRef">
        <div class="team-file-header">
          <div class="team-file-input-box">
            <input class="search-input" type="text" :placeholder="'搜索'+(dialogObj.teamFileObj.type==1?'文件':'乐文档')+'名称'" maxlength="50" v-model.trim="dialogObj.teamFileObj.key" @input="searchTeamFile(true)">
            <span class="icon-close" v-show="dialogObj.teamFileObj.key" @click="resetTeamFile"></span>
          </div>
          <div v-if="dialogObj.teamFileObj.type==1">仅展示10M内的乐文档、docx、pdf、xlsx格式的文件</div>
        </div>
        <div :class="['team-file-content',dialogObj.teamFileObj.loading?'show-loading-modal':'']">
          <ul class="team-file-list-header team-file-list-ul">
            <li class="team-file-list-li">
              <div class="team-file-cell team-file-name">文件名称</div>
              <div class="team-file-cell team-file-user">上传人</div>
              <div v-if="dialogObj.teamFileObj.type==1" class="team-file-cell team-file-size">大小</div>
              <div v-if="dialogObj.teamFileObj.type==1" class="team-file-cell team-file-time">更新时间</div>
            </li>
          </ul>
          <ul class="team-file-list-content team-file-list-ul team-file-list-scroll" v-show="dialogObj.teamFileObj.list.length>0" @scroll="scrollTeamFile">
            <li class="team-file-list-li" v-for="(item,key) in dialogObj.teamFileObj.list" :key="item.msgidServer+'-'+key" @click="selTeamFile(item)">
              <div class="team-file-cell team-file-name">
                <span :class="['sel-box-i',dialogObj.teamFileObj.selMap[item.msgidServer]?'sel':'']"></span>
                <img class="file-icon" :src="getFileIcon(item.attach.ext)" alt="">
                <div class="file-name textEls" :title="item.attach.name">{{ item.attach.name }}</div>
              </div>
              <div class="team-file-cell team-file-user">
                <span class="textEls">{{ item.userInfo.name }}</span>
              </div>
              <div v-if="dialogObj.teamFileObj.type==1" class="team-file-cell team-file-size">{{ item.attach.size ? dealMem(item.attach.size, 0) : "-" }}</div>
              <div v-if="dialogObj.teamFileObj.type==1" class="team-file-cell team-file-time">
                <span class="textEls">{{ dateFormat(parseInt(item.msgTimestamp), "yyyy-MM-dd HH:mm") }}</span>
              </div>
            </li>
          </ul>
          <ul class="team-file-none" v-show="dialogObj.teamFileObj.list.length==0&&!dialogObj.teamFileObj.loading">
            <img src="/img/content_none.png" v-show="!dialogObj.teamFileObj.key" width="160"/>
            <img src="/img/content_search.png" v-show="dialogObj.teamFileObj.key" width="160"/>
            <div>{{ !dialogObj.teamFileObj.key ? '暂无' + (dialogObj.teamFileObj.type == 1 ? '群文件' : '乐文档') : "暂无查询结果" }}</div>
          </ul>
        </div>
        <div class="team-file-footer">
          <span>已选择：</span>
          <span class="highlight">{{ dialogObj.teamFileObj.selMapLength }}</span>
          <span>项</span>
        </div>
      </div>
    </LyDialog>
    <!--编辑简介-->
    <LyDialog newClass="dialog-team-ai-intr-editor-box" title="编辑简介" :width="370" :height="230" :closeOnClickModal="true" :visible="dialogObj.type==6"
              @close="dialogOperate(6,1)" @confirm="dialogOperate(6,2)">
      <div class="main-dialog-box">
        <div class="ly-dialog-default-box">
          <label class="ly-dialog-default-label">
            <span class="ly-dialog-default-tips">*</span>
            <span>简介：</span>
          </label>
          <div class="ly-dialog-default-detail">
            <div class="ly-dialog-default-textarea-box">
              <textarea class="ly-dialog-default-textarea" :placeholder="dialogObj.teamAiIntrObj.placeholder" :maxlength="dialogObj.teamAiIntrObj.max" v-model.trim="dialogObj.teamAiIntrObj.text"></textarea>
            </div>
          </div>
        </div>
      </div>
    </LyDialog>
    <!--快速行动弹窗-->
    <LyDialog newClass="quick-acting-box"
              title="发起快速行动"
              :width="570"
              :height="460"
              cancelText="完成创建"
              okText="创建并转发到此群"
              :overflowHidden="false"
              :closeOnClickModal="true"
              :visible="quickActing.showDialog"
              @close="(type)=>{type=='buttom'?quickActingConfirm(1):closeQuickActingDialog() }"
              @confirm="quickActingConfirm(2)">
      <QuickActing :data="quickActing.form" :oldData="quickActing.oldData" ref="quickActingRef"></QuickActing>
    </LyDialog>
    <!--@人员多选-->
    <LyDialog newClass="dialog-hait-box" title="请选择要@的人" :width="611" :height="570" :closeOnClickModal="true" :visible="dialogObj.type==7"
              @close="dialogOperate(7,1)" @confirm="dialogOperate(7,2)">
      <div class="main-dialog-box dialog-hait-content" ref="dialogHaitRef">
        <div class="user-left-box">
          <div class="search-box">
            <input type="text" placeholder="请输入人员名称" maxlength="50" v-model="dialogObj.haitObj.text" @input="searchMoreHait()">
            <span class="icon-close" v-show="dialogObj.haitObj.text" @click="searchMoreHait(true)"></span>
          </div>
          <!--搜索列表-->
          <ul class="sel-user-ul">
            <li :class="[!dialogObj.haitObj.text&&!item.detailInfo?'sel-initial':'']" :data-key="item.initial" v-show="!memberLoading&&item.initial"
                v-for="(item,key) in dialogObj.haitObj.showList" :key="item.account" @click="selMoreHait(item)">
              <template v-if="item.detailInfo">
                <i :class="['sel-box-i',dialogObj.haitObj.selMap[item.account]?'sel':'']"></i>
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, 'p2p', item.account, '')">
                  </div>
                </div>
                <span class="textEls">{{ item.detailInfo.name }}</span>
              </template>
              <template v-else-if="!dialogObj.haitObj.text">{{ item.initial }}</template>
            </li>
            <li v-show="!memberLoading&&dialogObj.haitObj.showList.length==0" class="user-none">没有找到相关人员</li>
            <li v-show="memberLoading" class="user-none">
              <img class="loading-img" src="/img/waitting.gif" alt="" width="32">
            </li>
          </ul>
          <ul class="sel-user-initial" v-show="!dialogObj.haitObj.text">
            <li v-for="(item,key) in dialogObj.haitObj.initialList" :key="item" @click="haitScrollToInitial(item)">{{ item }}</li>
          </ul>
        </div>
        <div class="user-right-box">
          <div class="tips-box">会话@的人建议不超过20个，否则会发送失败</div>
          <div class="sel-user-tips">
            <span class="sel-user-text">
              <span>已选择</span><span class="highlight">{{ Object.keys(dialogObj.haitObj.selMap).length }}</span><span>人</span>
            </span>
          </div>
          <ul class="sel-user-ul">
            <li class="close-li" v-for="(item,key) in dialogObj.haitObj.selMap" :key="item.account">
              <template v-if="item.detailInfo">
                <div class="user-avatar-box">
                  <div class="avatar-box">
                    <img class="avatar" :src="item.detailInfo.avatar" :onerror="avatarError.bind(this, 'p2p', item.account, '')">
                  </div>
                </div>
                <span class="textEls">{{ item.detailInfo.name }}</span>
                <i class="close" @click="selMoreHait(item)"></i>
              </template>
            </li>
            <li v-show="Object.keys(dialogObj.haitObj.selMap).length==0" class="user-none-tips"></li>
          </ul>
        </div>
      </div>
    </LyDialog>
  </div>
</template>
<script>
import QuickActing from "@comp/quickActing/QuickActing.vue";

let path = remote.require("path");
import UUID from '@utils/uuid.js';
import {ref, watch, nextTick, onMounted, computed, provide} from "vue";
import {useStore} from "vuex";
import {useRouter} from 'vue-router'
import {
  avatarError, debounce, selElm, getSessionType, isSessionList, getLocalFile, deepClone, strToImg, getOffset, showMenu, sortTeamMembers,
  dateFormat, regReplace, isRegistered, createQrCode, hideElm, openForward, isFcwList, userWearPic, emitMsg, isDisabledGroup, linkFormat, scrollLi,
  isMainWin, isSubOrSer, getHighlight, htmlEscapeAll, userLocalStorage, toViewerMethod, openViewer, getFileIcon, getBounding, getFileExt, dealMem, MD5,
  getParents, setUserBaseInfo,
} from "@utils";
import {fileToDataURL} from "@utils/imgCompress";
import Emoji from "@comp/ui/comps/Emoji";
import {alert, loading, toast} from "@comp/ui";
import ChatMsg from "@comp/chat/ChatMsg";
import ChatFile from "@comp/chat/ChatFile";
import ChatNotice from "@comp/chat/ChatNotice";
import Calendar from "@comp/ui/comps/Calendar";
import NetTips from "@comp/ui/comps/NetTips";
import TeamEditor from "@comp/ui/comps/TeamEditor";
import LyDialog from "@comp/ui/comps/LyDialog";
import MsgReport from "@comp/msg/MsgReport";
import {
  searchDocApi, searchDocCustomerApi, comeInAndGoOutGroupApi, handOverCreatorApi, addPurDocListApi, delPurDocApi, fcwQueryQuickReplyListApi, addCollectClassifyApi,
  editCollectStoryClassifyApi, getAppListApi, newTopicsApi, sendBroadcastingApi, clearMessageApi, queryCloudMessageApi, searchDatasetDocumentListApi, updateDocumentApi,
  removeDatasetDocumentByIdApi, updateTeamAiInfoApi, quickActingInfo, quickActingUpStatus, aiSparringApi,
} from "@utils/net/api";

export default {
  name: "ChatContent",
  components: {QuickActing, ChatMsg, ChatFile, ChatNotice, Calendar, Emoji, NetTips, TeamEditor, LyDialog, MsgReport},
  props: {
    // 会话类型-utils.js/index.js的getSessionType
    sessionType: {
      type: String,
      default: "-1",
    },
    // 返回
    backMethods: {
      type: Function,
    }
  },
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    // 将选中的文件数据代理给父级，选中的文件数据要求在tag改变后依旧选中
    let allSelectFile = ref([])
    provide("allSelectFile", allSelectFile)
    // 配置文件
    let config = store.getters.getConfig.config;
    let userInfo = store.getters.getUserInfo;
    // 服务器配置信息
    let settings = ref({});
    // 显示的工具栏选项类型
    let showToolType = ref(-1);
    // 编辑框
    let editorRef = ref();
    // 群二维码
    let teamQrcodeRef = ref();
    // 切换讨论组状态
    let teamEditorRef = ref();
    // 是否显示编辑器
    let showEditorFlag = ref(true);
    // tag显示当前页1.聊天，2.通知，3.文件，4.成员
    let showTag = ref(1);
    provide("showTag", showTag);
    // @闪动状态
    let haitActive = ref(false);
    let haitActiveTimer = "";
    // 显示@消息内容状态
    let showHaitFlag = ref(false);
    // 显示@消息内容列表状态
    let showHaitMsgFlag = ref(true);
    // 显示特别关心内容状态
    let showConcernFlag = ref(false);
    // 显示特别关心内容列表状态
    let showConcernMsgFlag = ref(true);
    // 获取最后一条消息，用于判断是否显示新消息提示
    let preLastMsg = ref({});
    // 是否显示底部消息提示
    let lastMsgTipsFlag = ref(false);
    // 消息记录
    let recordList = ref([]);
    // 消息记录元素
    let recordMsgRef = ref();
    // 搜索消息记录元素
    let searchMsgRef = ref();
    // 搜索框日历元素
    let calendarRef = ref();
    // 选择的日期
    let selSearchDate = ref();
    // 显示日期选择器
    let calendarFlag = ref(false);
    // 是否可编辑群名
    let isInputObj = ref({
      name1: false,
      name2: false,
    });
    // 编辑的群名
    let inputName = ref("");
    // 群名元素
    let teamNameRef = ref();
    // 群二维码
    let teamQrcodeObj = ref({
      flag: false,
      qrcode: "",
    });
    // 编辑器拖拽对象
    let editorResizeObj = ref({
      winHeight: 0,
      screenY: 0,
      flag: false,
      height: 0,
      tempHeight: 0,
    });
    // 引用消息详情元素
    let quoteDetailRef = ref();
    // 引用消息详情对象
    let quoteDetailObj = ref({
      flag: false,
      msg: [],
      leftFlag: false,
      top: 0,
    });
    // 文档操作元素
    let docOpRef = ref();
    // 文档操作对象
    let docOpObj = ref({
      flag: false,
      docPur: -1,
    });
    // 当前会话信息
    let sessionInfo = ref(remote.store.getters.getSessions({id: store.getters.getCurrentSession.id}));
    // @我消息列表
    let haitMsgList = ref(store.getters.getHaitMsgMap({id: sessionInfo.value.id, sort: true}));
    // 引用消息
    let quoteMsg = ref(remote.store.getters.getQuoteMsg[sessionInfo.value.id]);
    // @特别关心列表
    let concernMsgList = ref(store.getters.getConcernMsgMap({id: sessionInfo.value.id, sort: true}));
    // 群成员获取状态
    let teamMembersMap = {};
    // 群员信息加载状态
    let teamMembersLoading = ref(false);
    // 群成员信息加载失败
    let teamMembersError = ref("");
    // 群成员分页
    let teamMembersPageList = ref([]);
    // 群成员当前分页
    let teamMembersPage = ref(1);
    // 是否显示所有分页
    let memberPageFlag = ref(false);
    // 是否显示群成员搜索
    let memberSearchFlag = ref(false);
    // 群成员总分页
    let teamMembersTotal = ref(1);
    // 搜索群员状态
    let searchTeamMemberObj = ref({
      index: 0,// 选中的搜索结果下标
      text: "",// 搜索文本
      show: false,// 是否显示搜索结果
      list: [],// 搜索结果列表
    });
    // 搜索聊天记录状态
    let searchMsgObj = ref({
      text: "",// 搜索内容
      searchText: "",// 点击确认搜索的内容
      total: 0,// 搜索到的总条数
      page: 1,// 搜索页数
      back: false,// 是否能返回搜索
      search: false,// 搜索请求状态
      show: false,// 是否显示搜索结果
      flag: false,// 是否显示搜索
      list: [],// 搜索结果列表
      notInTeamPage: 1,// 查询不在群消息页数
    });
    // 聊天框元素
    let chatContentRef = ref();
    // 群员信息
    let teamMembers = ref([]);
    // 当前成员在群信息
    let userTeamInfo = ref({});
    // 智能助理对象
    let aiObj = ref(deepClone(remote.store.getters.getState("aiObj")));
    let aiSearchObj = ref({
      key: "",
      flag: false,// 搜索状态
      height: setAiAppHeight(),
      refreshTimer: "",//新话题定时器
      appElmObj: {
        bottom: 150,
        defaultBottom: 27,
        left: 0,
      },
      appChildElmObj: {
        bottom: 150,
        defaultBottom: 27,
        left: 0,
      },
    });

    // 多选状态
    let isMultiple = ref(false);
    provide("isMultiple", isMultiple);
    // 表情元素
    let emojiRef = ref();
    // 表情对象
    let emojiObj = ref({
      bottom: 150,
      defaultBottom: 36,
      left: 0,
    });
    // 快捷回复
    let quickReplyList = ref([]);
    // 客户跳转链接对象
    let fcwObj = ref({
      to: "",
      url: "",
      zsLabel: false,
    });
    // @选择框
    let haitSelRef = ref();
    // call选择框
    let callSelRef = ref();
    // @群员元素
    let haitMemberRef = ref();
    // @文档元素
    let haitDocRef = ref();
    // 唤起应用元素
    let callAppRef = ref();
    // 唤起ai应用元素
    let callAiRef = ref();
    // @群员列表
    let haitMemberList = ref([]);
    // @群员加载状态
    let memberLoading = ref(false);
    // @文档列表
    let haitDocList = ref([]);
    // @文档加载状态
    let docLoading = ref(false);
    // 唤起应用默认列表
    let callAppDefaultList = [{id: 1, title: "录盘", type: 1, url: "/hsl/entrust/entrust-add"}, {id: 2, title: "录客", type: 1, url: "/ky/customer/toAdd"}, {id: 3, title: "录成交报告", type: 1, url: "/jjscj/index?openType=1"}, {id: 4, title: "写备忘", type: 1, url: "/attend/main/index"}, {id: 5, title: "发起日程", type: 2, key: "smartSchedule"}];
    // 唤起应用列表
    let callAppList = ref([]);
    // 唤起应用加载状态
    let callAppLoading = ref(false);
    // 唤起ai列表
    let callAiList = ref([]);
    // 唤起ai加载状态
    let callAiLoading = ref(false);
    // @弹窗
    let haitInfo = ref(store.getters.getHaitInfo);
    // 触发重新渲染
    let showFlag = ref(true);
    // 是否滚动到最底部
    let isScrollBottom = ref(true);
    // 消息内容元素
    let msgBoxRef = ref();
    // 消息组件元素
    let msgRef = ref();
    // 加载前消息数
    let preLoadMsgLength = "";
    // 单击双击判断定时器
    let showUserInfoTimer = "";
    // 当前会话消息列表
    let msgList = ref([]);
    provide("msgList", msgList);
    // 跳转到公告
    let chatNotice = ref(null);
    // 弹窗对象
    let dialogObj = ref({
      type: -1,// 1选择分组
      type1: -1,// 1新建分组
      classifyName: "",// 新建分组名
      moveList: [],// 显示的分组列表
      moveSelItem: {},// 移动至选择的对象
      tempKey: 1,// 新增分组当前的类型 1收藏2话术
      done: "",// 回调
      reportMsg: {},// 消息播报对象
      reportGroup: [],// 消息播报人群
      reportMsgHeight: 0,// 消息播报人群高度
      aiAppObj: {},//ai创作应用弹窗数据
      aiAppShowSelId: "",// ai创作应用显示当前正在选择的下拉框id
      teamFileObj: {
        type: 1,// 1群文件2乐文档
        key: "",// 搜索文件词
        list: [],// 搜索文件列表
        page: 1,
        pageSize: 50,
        hasMore: true,// 是否还有更多
        selMap: {},// 选择文件对象
        selMapLength: 0,// 选择文件数量
        selMapSize: 0,// 选择文件大小
        loading: false,// 请求状态
      },// 群文件弹窗对象
      teamAiIntrObj: {
        max: 200,
        text: "",
        placeholder: "填写自我介绍，方便群成员更好的了解助手的能力",
      },// 群ai简介对象
      haitObj: {
        text: "",// @人员搜索词
        selMap: {},// @人员选择对象
        list: [],// @人员列表
        showList: [],// @人员搜索列表
        initialList: [],// @人员首字母
      },// @人员搜索
    });
    // @多选弹窗对象
    let dialogHaitRef = ref();
    // 弹窗输入框对象
    let lyDialogInputRef = ref();
    // 霸屏转发弹窗对象
    let forwardReportRef = ref();
    // 群知识上传弹窗对象
    let teamFileRef = ref();
    // 转发指定人权限
    let purMap = ref(remote.store.getters.getState("purMap"));
    // 批量选择bug反馈
    let multiBugItem = ref([]);
    // 快速行动ref
    let quickActingRef= ref(null);
    // 快速行动
    let quickActing = ref({
      // 是否开启快速行动模式
      enable: false,
      // 是否显示快速行动按钮
      show: true,
      // 是否显示快速行动弹窗
      showDialog: false,
      // 默认表单数据
      oldData: null,
      // 提交数据
      form:{
        // 当前任务id，为空代表新创建
        id: null,
        // 当前会话
        session: sessionInfo.value,
      }
    })
    // 是否显示底部ai工具 0不存在1召唤2问答助手
    let aiToolsType = ref(0);
    // 是否显示底部ai工具展开收起 0不显示1收起2展开
    let teamToolShowType = ref(0);
    let teamAiLocal = getSessionType({scene: "p2p", to: "", detailInfo: {detailType: "teamAi"}});
    // 群工具对象
    let toolObj = ref({
      showType: 1 // 1显示开关2显示知识库3显示快速行动
    });
    // 群文档助手对象
    let teamAiObj = ref({
      aiInfo: {
        datasetId: "",// 群ai数据集id
        to: "",// 群ai账号
        avatar: "",// 群ai头像
        name: "",// 群ai名
        prologue: "",// 群ai介绍
        ...teamAiLocal,
      },
      searchInfo: {
        loading: false,// 加载中
        loadingText: "",// 加载提示文案
        studying: false,// 存在学习状态的知识
        studyingTime: 0,// 重新获取学习状态列表时间
        key: "",// 群ai知识库搜索词
        list: [],// 群ai知识库
        page: 1,
        limit: 50,
        total: 0,// 总知识列表数
        hasMore: true,
        updateLoading: false,// 上传loading
      },
      fileTips: "仅支持10M以内的乐文档，docx、pdf、xlsx格式文档",
      fileTips1: "(注：目前AI不支持学习带图的表格文件)",
      loadingMap: {},
      loadingCancelMap: {},
      showAdd: false,// 添加知识按钮显示选项
      showAddTimer: "",
    });
    // 发送状态
    let sendMessageFlag = ref(true);

    // 设置当前会话消息
    if (sessionInfo.value.id) {
      msgList.value = store.getters.getMsgs({id: sessionInfo.value.id});
    }
    // 群会话获取当前用户在群信息
    if (sessionInfo.value.to && sessionInfo.value.scene != "p2p") {
      getUserTeamInfo();
    }
    setAutoScroll("bottom");
    isShowEditor();
    // 打开页面聚焦窗口到输入框
    onMounted(() => {
      nextTick(() => {
        store.dispatch("setImEditor", editorRef.value);
        nextTick(() => {
          // 触发聚焦
          if (sessionInfo.value.id) {
            store.dispatch("activeImEditor", {id: sessionInfo.value.id});
          }
        });
        // 处理快速行动状态
        setQuickActing();
      });
    });

    // 输入框聚焦完成
    watch(() => store.state.emit.activeImEditor,
      (newValue, oldValue) => {
        if (sessionInfo.value.id && newValue.id == sessionInfo.value.id) {
          setAiPlaceholder();
        }
      }, {
        deep: true
      }
    );

    // 监听会话状态
    watch(() => store.state.updateSessionId,
      (newValue, oldValue) => {
        if (newValue && newValue == sessionInfo.value.id) {
          sessionInfo.value = remote.store.getters.getSessions({id: sessionInfo.value.id});
          isShowEditor();
          if (showEditorFlag.value) {
            activeEditor();
          }
        }
      }, {
        deep: true
      }
    );
    // 监听群状态
    watch(() => store.state.teams[sessionInfo.value.to],
      (newValue, oldValue) => {
        if (newValue && newValue.teamId == sessionInfo.value.to) {
          sessionInfo.value.detailInfo = newValue;
          // 群ai变更
          if ((newValue.serverCustom?.ai1 && !teamAiObj.value.aiInfo?.to) || (!newValue.serverCustom?.ai1 && teamAiObj.value.aiInfo?.to)) {
            setAiToolsType();
          }
        }
      }, {
        deep: true
      }
    );
    // 会话切换
    watch(() => store.state.currentSession.id,
      (newValue, oldValue) => {
        allSelectFile.value = [];
        if (!editorResizeObj.value.flag) {
          editorResizeObj.value.height = store.getters.getEditorHeightMap[newValue] || 0;
        }
        if (newValue != oldValue) {
          sessionInfo.value = remote.store.getters.getSessions({id: newValue});
          isShowEditor();
          nextTick(() => {
            // 初始化发送状态
            if (store.getters.getState("sseDisableSendMap")[sessionInfo.value.id]) {
              sendMessageFlag.value = false;
            } else {
              sendMessageFlag.value = true;
            }
            isMultiple.value = false;
            lastMsgTipsFlag.value = false;
            // 切换会话隐藏群二维码和@人
            showTeamQrcode();
            hideHait();
            // 初始化默认设置人员信息
            if (sessionInfo.value.scene != "p2p" && sessionInfo.value.to) {
              teamMembers.value = [];
              userTeamInfo.value = {};
              getUserTeamInfo();
            } else if (new RegExp(config.fcw).test(sessionInfo.value.to)) {
              // 房产网客户判断是否存在跳转链接
              let fcwAccount = sessionInfo.value.to;
              fcwObj.value = {};
              store.dispatch("setFcwLink", fcwAccount).then(res => {
                if (fcwAccount == sessionInfo.value.to) {
                  fcwObj.value.to = fcwAccount;
                  fcwObj.value.url = res.url;
                }
              });
              // 房产网客户判断是否是专属经纪人绑定
              store.dispatch("setFcwZsLabel", fcwAccount).then(res => {
                if (fcwAccount == sessionInfo.value.to) {
                  fcwObj.value.to = fcwAccount;
                  fcwObj.value.zsLabel = res.zsLabel;
                }
              });
            }
            // 触发聚焦
            store.dispatch("activeImEditor", {id: sessionInfo.value.id});
            // 切换会话滚动到底部
            store.commit("setEmit", {type: "scroll", value: "bottom"});
            // 切换会话清空图片加载定时器
            store.commit("setImageLoadTimer", {type: "clear"});
            // 切换会话重新获取引用消息
            quoteMsg.value = remote.store.getters.getQuoteMsg[sessionInfo.value.id];
            // 修改tag
            showTagClick(1);
            // 修改群信息
            if (sessionInfo.value.scene != "p2p") {
              // 切换@闪动状态
              toggleHaitActive();
            }
            setAiToolsType();
            setQuickActing();
            // 切换会话重置滚动会话状态
            store.commit("setScrollSessionState", true);
            setAiPlaceholder();
          });
        }
      }, {
        deep: true
      }
    );
    // 切换到其他页面临时会话
    watch(() => store.state.sessionsTemp[sessionInfo.value.id],
      (newValue, oldValue) => {
        if (newValue && oldValue && sessionInfo.value.id == newValue.id && (newValue.id != oldValue.id || newValue.notMore && !sessionInfo.value.notMore)) {
          sessionInfo.value = remote.store.getters.getSessions({id: sessionInfo.value.id, temp: true});
        }
      }, {
        deep: true
      }
    );
    // @消息列表
    watch(() => store.state.haitMsgMap[sessionInfo.value.id],
      (newValue, oldValue) => {
        haitMsgList.value = store.getters.getHaitMsgMap({id: sessionInfo.value.id, sort: true});
      }, {
        deep: true
      }
    );
    // 特别关心列表
    watch(() => store.state.concernMsgMap[sessionInfo.value.id],
      (newValue, oldValue) => {
        concernMsgList.value = store.getters.getConcernMsgMap({id: sessionInfo.value.id, sort: true});
      }, {
        deep: true
      }
    );
    // 引用消息
    watch(() => store.state.quoteMsg,
      (newValue, oldValue) => {
        quoteMsg.value = newValue[sessionInfo.value.id];
      }, {
        deep: true
      }
    );
    // 群成员变更
    watch(() => store.state.updateMemberTeamInfo.time,
      (newValue, oldValue) => {
        if (sessionInfo.value.to && sessionInfo.value.scene != "p2p" && store.getters.getUpdateMemberTeamInfo.id == sessionInfo.value.to) {
          teamMembers.value = [];
          getShowTeamMember();
        }
      }, {
        deep: true
      }
    );
    // 自己群信息变更
    watch(() => store.state.userTeamInfos,
      (newValue, oldValue) => {
        let thisUserTeamInfo = store.getters.getUserTeamInfo[sessionInfo.value.to];
        if (sessionInfo.value.to && sessionInfo.value.scene != "p2p" && thisUserTeamInfo) {
          userTeamInfo.value = thisUserTeamInfo;
          isShowEditor();
          if (showEditorFlag.value) {
            activeEditor();
          }
          setAiToolsType();
          // setQuickActing();
        }
      }, {
        deep: true
      }
    );
    // @选择
    watch(() => store.state.haitInfo,
      (newValue, oldValue) => {
        // 回车键选择
        if (newValue.enter) {
          confirmHaitInfo(newValue.type == 1 ? newValue.docIndex : newValue.type == 2 ? newValue.appIndex : newValue.type == 3 ? newValue.aiIndex : newValue.memberIndex);
          hideHait();
          return;
        }
        haitInfo.value = deepClone(newValue);
      }, {
        deep: true
      }
    );
    // @文本变化
    watch(() => store.state.haitInfo.haitText,
      (newValue, oldValue) => {
        // 不存在@内容或者不是@开头关闭@状态，兼容全角状态＠＃
        if (!newValue || (newValue.indexOf("@") != 0 && newValue.indexOf("＠") != 0 && haitInfo.value.haitFlag) || (newValue.indexOf("#") != 0 && newValue.indexOf("＃") != 0 && haitInfo.value.callFlag)) {
          if (haitInfo.value.haitFlag || haitInfo.value.callFlag) {
            hideHait();
          }
          return
        }
        // 重置选中位置
        selHaitInfo(0);
        // 输入@计算@弹窗位置
        if (newValue == "@" || newValue == "#") {
          let bounds = store.getters.getImEditor.getBounds(sessionInfo.value.id, haitInfo.value.haitCursor.index, haitInfo.value.haitCursor.length);
          let offset = getOffset(editorRef.value);
          let computeStyle = window.getComputedStyle(editorRef.value);
          let textPaddingLeft = parseInt(computeStyle["padding-left"].replace(/px/g, ""));
          // 文字距离左侧位置
          let textLeft = bounds.left + textPaddingLeft;
          // 计算距离底部和左侧位置
          let positionBottom = document.body.clientHeight - offset.top - bounds.top;
          // 顶部拖拽区域无法点击
          if (offset.top < 250 + 60) {
            positionBottom -= (250 + 60 - offset.top);
          }
          let positionLeft = document.body.clientWidth - textLeft - offset.left - textPaddingLeft > 382 ? textLeft : (textLeft - 382 < 10 ? 10 : textLeft - 382);
          if (newValue == "@") {
            haitSelRef.value.style.bottom = positionBottom + "px";
            haitSelRef.value.style.left = positionLeft + "px";
          } else if (newValue == "#") {
            callSelRef.value.style.bottom = positionBottom + "px";
            callSelRef.value.style.left = positionLeft + "px";
          }
        }
        // 如果是群同时获取群成员,如果存在记录则不请求
        if (!haitInfo.value.type) {
          if (newValue == "@") {
            if (sessionInfo.value.scene != "p2p") {
              selHaitType(0);
            } else {
              selHaitType(1);
            }
          } else if (newValue == "#") {
            selHaitType(3);
          }
        }
        haitInfo.value.haitText = newValue;
        haitMemberList.value = [];
        haitDocList.value = [];
        callAppList.value = [];
        memberLoading.value = true;
        docLoading.value = true;
        callAppLoading.value = true;
        searchHait();
      }, {
        deep: true
      }
    );
    // @人员选中变化
    watch(() => store.state.haitInfo.memberIndex,
      (newValue, oldValue) => {
        if (newValue != oldValue) {
          let memberIndex = newValue;
          if (newValue < 0) {
            memberIndex = haitMemberList.value.length - 1;
          } else if (!haitMemberList.value[newValue]) {
            memberIndex = 0;
          }
          selHaitInfo(memberIndex, 1);
        }
      }, {
        deep: true
      }
    );
    // @文档选中变化
    watch(() => store.state.haitInfo.docIndex,
      (newValue, oldValue) => {
        if (newValue != oldValue) {
          let docIndex = newValue;
          if (newValue < 0) {
            docIndex = haitDocList.value.length - 1;
          } else if (!haitDocList.value[newValue]) {
            docIndex = 0;
          }
          selHaitInfo(docIndex, 1);
        }
      }, {
        deep: true
      }
    );
    // 唤起应用选中变化
    watch(() => store.state.haitInfo.appIndex,
      (newValue, oldValue) => {
        if (newValue != oldValue) {
          let appIndex = newValue;
          if (newValue < 0) {
            appIndex = callAppList.value.length - 1;
          } else if (!callAppList.value[newValue]) {
            appIndex = 0;
          }
          selHaitInfo(appIndex, 1);
        }
      }, {
        deep: true
      }
    );
    // 唤起ai应用选中变化
    watch(() => store.state.haitInfo.aiIndex,
      (newValue, oldValue) => {
        if (newValue != oldValue) {
          let aiIndex = newValue;
          if (newValue < 0) {
            aiIndex = callAiList.value.length - 1;
          } else if (!callAiList.value[newValue]) {
            aiIndex = 0;
          }
          selHaitInfo(aiIndex, 1);
        }
      }, {
        deep: true
      }
    );
    // 监控会话信息变更
    watch(() => store.state.msgs[sessionInfo.value.id],
      (newValue, oldValue) => {
        if (sessionInfo.value.id) {
          msgList.value = store.getters.getMsgs({id: sessionInfo.value.id});
          // 收到消息判断是否滚动到底部
          store.commit("setEmit", {type: "scroll", value: "msg"});
        }
      }, {
        deep: true
      }
    );
    // 会话消息滚动
    watch(() => store.state.emit.scroll,
      (newValue, oldValue) => {
        // 监听消息滚动
        setAutoScroll(newValue);
        // 重置滚动标识
        store.commit("setEmit", {type: "scroll", value: ""});
      }, {
        deep: true
      }
    );
    // 监听全局点击
    watch(() => store.state.emit.click,
      (newValue, oldValue) => {
        if (newValue && newValue.target) {
          dialogObj.value.aiAppShowSelId = "";
          // 重置当前会话选中状态
          store.dispatch("setCurrentSession", {id: sessionInfo.value.id, type: "click"});
          // 去除元素聚焦
          store.commit("setFocusMsg", "");
          // 点击文档跳转
          if (/im-image-hait-doc/.test(newValue.target.className)) {
            if (newValue.target.dataset.property == 2) {
              store.dispatch("setOpenWindow", [`${config[config.env].jjsHome}/lyj-front/sheet/index.html?id=${newValue.target.dataset.haitKey}&appName=pc-im`, "login"]);
            } else {
              store.dispatch("setOpenWindow", [`${config[config.env].jjsHome}/lyjEtherpad/p/${newValue.target.dataset.haitKey}?appName=pc-im`, "login"]);
            }
          }
          // 判断隐藏工具弹窗
          if (showToolType.value != -1) {
            showToolSel(-1);
          }
          // 隐藏日历弹窗
          if (calendarFlag.value) {
            calendarFlag.value = false;
          }
          // 隐藏引用详情
          hideQuoteDetail();
          // 点击输入框不隐藏@弹窗
          if (/editor-content-box/.test(newValue.target.className) || newValue.path.some(elm => /editor-content-box/.test(elm.className)) ||
            /ql-container/.test(newValue.target.className) || newValue.path.some(elm => /ql-container/.test(elm.className))
          ) {
            return;
          }
          // 判断隐藏@弹窗
          if (store.getters.getHaitInfo.haitFlag || store.getters.getHaitInfo.callFlag) {
            hideHait();
          }
        } else if (newValue && newValue.clickOpen && newValue.id == sessionInfo.value.id) {
          // 打开会话
          showTagClick(1);
          // 隐藏人员信息弹窗
          store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
        }
      }, {
        deep: true
      }
    );
    // 监听全局点击
    watch(() => store.state.emit.mousemove,
      (newValue, oldValue) => {
        editorResize(newValue, 2);
        // 聚焦状态且非重置未读数状态，移动鼠标重置当前未读数
        let currId = sessionInfo.value.id;
        if (isMainWin() && !store.getters.getCurrentSession.timer && store.getters.getCurrentWindow().isFocused) {
          debounce({
            timerName: "mousemoveSetCurrentSession",
            time: 100,
            fnName: function () {
              if (currId == sessionInfo.value.id && router.currentRoute.value.path == "/index/chat") {
                store.dispatch("setCurrentSession", {id: sessionInfo.value.id});
              }
            }
          });
        }
      }, {
        deep: true
      }
    );
    // 监听全局鼠标释放
    watch(() => store.state.emit.mouseup,
      (newValue, oldValue) => {
        editorResize(newValue, 3);
      }, {
        deep: true
      }
    );
    // 监听收藏新增分组弹窗
    watch(() => store.state.emit.collectAddClassifyDialog,
      (newValue, oldValue) => {
        showDialog(newValue.type, 1, newValue.key, newValue.done);
      }, {
        deep: true
      }
    );
    // 离开群回调通讯录更新会话判断是否在群切换到聊天界面
    watch(() => store.state.emit.leaveTeam,
      (newValue, oldValue) => {
        if (newValue.id == sessionInfo.value.id) {
          if (router.currentRoute.value.path != "/index/chat" && !store.state.teams[sessionInfo.value.to]) {
            backList();
          }
        }
      }, {
        deep: true
      }
    );
    // 监听霸屏弹窗
    watch(() => store.state.emit.forwardReportMsg,
      (newValue, oldValue) => {
        showDialog(3, newValue);
      }, {
        deep: true
      }
    );
    // 转发指定人员权限
    watch(() => store.state.purMap,
      (newValue, oldValue) => {
        purMap.value = newValue;
      }, {
        deep: true
      }
    );
    // 监听智能助理变化
    watch(() => store.state.aiObj,
      (newValue, oldValue) => {
        aiObj.value = deepClone(newValue);
        setAiPlaceholder();
      }, {
        deep: true
      }
    );
    // 切换ai应用
    watch(() => store.state.emit.switchAppItem,
      (newValue, oldValue) => {
        if (newValue) {
          modifyAiSettings(1, newValue);
          store.commit("setEmit", {type: "switchAppItem", value: ""});
        }
      }, {
        deep: true
      }
    );
    // 显示创作应用弹窗
    watch(() => store.state.emit.showAiApp,
      (newValue, oldValue) => {
        if (newValue) {
          let data = deepClone(newValue);
          dialogObj.value.aiAppObj.aiApp = data;
          // 设置弹窗标题
          let labelStyle = "display: inline-block;line-height: 16px;padding: 0 4px; font-size: 11px;border-radius: 2px;margin-right:8px;font-weight: normal;";
          if (data.isFree == 1) {
            labelStyle += "border: 1px solid #BFBFBF;color: #999999";
          } else {
            labelStyle += "border: 1px solid #FC9D03;color: #FC9D03";
          }
          dialogObj.value.aiAppObj.showName = `<span><span class="highlight" style="${labelStyle}">${data.isFree == 1 ? "免费" : "付费"}</span><span>${data.name}</span></span>`;
          // 设置弹窗内容
          dialogObj.value.aiAppObj.prologue = data.prologue;
          try {
            dialogObj.value.aiAppObj.appJson = JSON.parse(data.diyJson);
          } catch (e) {
            dialogObj.value.aiAppObj.appJson = [];
          }
          // 应用参数被修改重置内容
          if (data.diyJsonIsUpdated) {
            toast({title: "应用已修改，请您重新输入", type: 2});
          }
          dialogObj.value.type = 4;
        }
      }, {
        deep: true
      }
    );
    // 新建话题请求接口成功回调
    watch(() => store.state.emit.sendAiMsgCardTime,
      (newValue, oldValue) => {
        if (dialogObj.value.aiAppObj.setAiQuestion) {
          let msg = deepClone(dialogObj.value.aiAppObj.setAiQuestion)
          msg.time = Date.now() + store.getters.getDiffTime;
          dialogObj.value.aiAppObj.setAiQuestion = "";
          store.dispatch("setAiQuestion", {aiJson: msg});
        }
      }, {
        deep: true
      }
    );
    // 监听多选bug反馈
    watch(() => store.state.emit.multiBugItem,
      (newValue, oldValue) => {
        multiBugItem.value = deepClone(newValue);
        nextTick(() => {
          // 记录原来选中的range
          let oldRange = window.getSelection().getRangeAt(0);
          // 创建一个选择框
          let range = new Range();
          // 选中更改过的对象
          setTimeout(function () {
            range.selectNode(document.getElementById("multiBugItem"));
            window.getSelection().removeAllRanges();
            window.getSelection().addRange(range);
            let node = getRangContent(2);
            store.dispatch("setOpenWindow", [`${config[config.env].jjsHome}/lyj-front/tapd-manage/?hideSideBar=true#/createOrder?source=11&orderFeedbackContent=${encodeURIComponent(node.outerHTML)}`, "jjsHome", "demandOrBug"]);
            setTimeout(function () {
              // 移除所有的选区
              window.getSelection().removeAllRanges();
              // 选中原来的值
              if (oldRange && oldRange.rangeCount > 0) {
                window.getSelection().addRange(oldRange);
              }
              multiBugItem.value = [];
            }, 0);
          }, 0);
        });
      }, {
        deep: true
      }
    );

    // 全局定时器
    watch(() => store.state.emit.globalTimer,
      (newValue, oldValue) => {
        // 查看知识库列表且存在学习状态的知识5s重新获取一次知识库列表
        if (showTag.value == 8 && teamAiObj.value.searchInfo.studying && Date.now() + store.getters.getDiffTime - teamAiObj.value.searchInfo.studyingTime > 5000) {
          teamAiObj.value.searchInfo.studyingTime = 9999999999999;
          searchKnowledgeList(false, true, true);
        }
      }, {
        deep: true
      }
    );

    // ai流会话发送监听
    watch(() => store.state.sseDisableSendMap,
      (newValue, oldValue) => {
        if (newValue[sessionInfo.value.id]) {
          sendMessageFlag.value = false;
        } else {
          sendMessageFlag.value = true;
        }
      }, {
        deep: true
      }
    );

    // 监听徽标变化
    const noticeUnread = computed(() => {
      return store.getters.getNoticeUnread;
    });

    //切换tag页签-1聊天-2通知-3文件-4成员-5消息记录-6账号信息-7快捷回复-8ai管理
    function showTagClick(val) {
      if ((showTag.value == 4 || showTag.value == 5 || showTag.value == 7) && showTag.value == val) {
        val = 1;
      }
      if (showTag.value == 3 && val == 1) {
        // 刷新消息-获取是否有文件正在下载
        store.commit("setEmit", {type: "reloadMsg", value: {id: store.getters.getCurrentSession.id, time: Date.now()}});
      } else if (showTag.value == 2 && val == 1) {
        // 发公告后回到聊天界面判断是否在底部滚动回底部
        if (isScrollBottom.value) {
          nextTick(() => {
            setAutoScroll("bottom");
          });
        }
      }
      showTag.value = Number(val);
      switch (showTag.value) {
        case 2:
        case 3:
          // 2通知 3文件
          showFlag.value = false;
          setTimeout(() => {
            showFlag.value = true;
          }, 0)
          break;
        case 4:
          // 获取群成员信息
          teamMembersLoading.value = true;
          teamMembersPage.value = 1;
          teamMembersTotal.value = 1;
          memberPageFlag.value = false;
          memberSearchFlag.value = false;
          isInputObj.value.name2 = false;
          resetSearchTeamMember();
          break;
        case 5:
          toggleSearchMsg(true);
          // 初始化日历
          setCurrentDay();
          // 获取消息记录
          getMsgHistory();
          break;
        case 7:
          // 快捷回复
          fcwQueryQuickReplyList();
          break;
        case 8:
          // ai管理
          toolObj.value.showType = 1;
          // 判断是否正在召唤loading
          if (teamAiObj.value.loadingMap[sessionInfo.value.to] || teamAiObj.value.loadingCancelMap[sessionInfo.value.to]) {
            teamAiObj.value.searchInfo.loading = true;
            teamAiObj.value.searchInfo.loadingText = "正在创建群助手...";
          } else {
            teamAiObj.value.searchInfo.loading = false;
            teamAiObj.value.searchInfo.loadingText = "";
          }
          break;
      }
      setAutoScroll("msg");
    }

    // 获取展示的群员列表
    function getShowTeamMember(account) {
      setTeamMembers().then(() => {
        let list = teamMembers.value;
        if (list.length > 200) {
          // 群成员大于200个分页
          teamMembersTotal.value = Math.ceil(list.length / 200);
          list = list.slice((teamMembersPage.value - 1) * 200, teamMembersPage.value * 200);
        }
        list = deepClone(list);
        // 聚焦当前元素
        if (account) {
          list.map(item => {
            delete item.curr;
            if (item.account == account) {
              item.curr = true;
            }
          });
        }
        teamMembersPageList.value = list;
        teamMembersLoading.value = false;
        // 滚动到聚焦元素
        if (account) {
          nextTick(() => {
            let firstElmTop = chatContentRef.value.querySelector(".chat-member-ul .chat-member-li").offsetTop;
            let currElmTop = chatContentRef.value.querySelector(".chat-member-ul .curr").offsetTop;
            chatContentRef.value.querySelector(".chat-member-ul").scrollTop = currElmTop - firstElmTop;
          });
        }
      });
    }

    // 消息滚动
    function msgScroll(e, msgType) {
      if (e.target.clientHeight + e.target.scrollTop + 1 >= e.target.scrollHeight) {
        isScrollBottom.value = true;
        store.commit("setEmit", {type: "isScrollBottom", value: true});
      } else {
        isScrollBottom.value = false;
        store.commit("setEmit", {type: "isScrollBottom", value: false});
      }
      debounce({
        timerName: "msgScroll",
        time: 100,
        fnName: doScroll.bind(this, e, msgType)
      });
      hideQuoteDetail();
    }


    // 隐藏引用详情和乐文档权限操作弹窗
    function hideQuoteDetail() {
      // 隐藏引用详情
      if (quoteDetailObj.value.flag) {
        quoteDetailObj.value.flag = false;
        quoteDetailObj.value.msg = [];
      }
      docOpObj.value.flag = false;
    }

    // 防抖滚动
    function doScroll(e, msgType) {
      // 判断是否显示底部消息提示
      showLastMsgTips();
      store.commit("setEmit", {type: "scrollMsg", value: {time: Date.now(), msgType: msgType}});
      // 滚动到顶部加载更多,服务号和公众号不限制消息数
      if (msgType == 1 && e.target && !sessionInfo.value.notMore &&
        (msgList.value.length < 100 || isSubOrSer(sessionInfo.value.id)) && e.target.scrollTop == 0 && e.target.clientHeight != e.target.scrollHeight) {
        debounce({
          timerName: "msgScrollSetHistory",
          time: 200,
          fnName: function () {
            preLoadMsgLength = e.target.querySelectorAll(".msg-li").length;
            msgBoxRef.value.scrollTop = 30;
            remote.store.dispatch("setHistory", {id: sessionInfo.value.id, type: 1, endTime: msgList.value[0] ? msgList.value[0].time : undefined, notEnd: true, page: ((msgList.value[0].page || 0) + 1)});
          }
        })
      }
    }

    // 设置自动滚动位置
    function setAutoScroll(pos) {
      nextTick(() => {
        if (msgBoxRef.value && pos) {
          // 收到消息，如果是底部状态则滚动到底部
          if (pos == "msg") {
            if (!preLoadMsgLength && !isScrollBottom.value) {
              showLastMsgTips();
              return;
            }
            pos = "bottom";
            if (preLoadMsgLength) {
              let msgElm = msgBoxRef.value.querySelectorAll(".msg-li");
              if (preLoadMsgLength == msgElm.length) {
                pos = 30;
              } else {
                let preLoadElm = msgElm[msgElm.length - preLoadMsgLength];
                // 计算滚动后上次滚动前第一个元素位置
                if (preLoadElm) {
                  pos = preLoadElm.offsetTop - msgElm[0].offsetTop;
                } else {
                  pos = 30;
                }
              }
              preLoadMsgLength = 0;
            }
          }
          // 滚动到底部
          if (pos == "bottom") {
            isScrollBottom.value = true;
            store.commit("setEmit", {type: "isScrollBottom", value: true});
            pos = msgBoxRef.value.scrollHeight;
          }
          msgBoxRef.value.scrollTop = pos;
          // 判断是否显示底部消息提示
          showLastMsgTips();
        }
      });
    }

    // 是否黑名单
    function isBlacklist(id) {
      return !!remote.store.getters.getBlacklist({id: id});
    }

    // 解除名单
    function removeBlacklist(id) {
      remote.store.dispatch("setNimBlacklist", {account: id, flag: false});
    }

    // 是否显示编辑器
    function isShowEditor() {
      let flag = true;
      if (sessionInfo.value.scene == "p2p" || (sessionInfo.value.scene == "team" && sessionInfo.value.detailInfo.notInTeam)) {
        if (sessionInfo.value.detailInfo.notInTeam || sessionInfo.value.detailInfo.empStatus == 4 || isBlacklist(sessionInfo.value.to)) {
          // 离职/黑名单/不在群成员不显示编辑器
          flag = false;
        }
      } else {
        let teamInfo = store.getters.getTeams({id: sessionInfo.value.to});
        if (teamInfo) {
          let custom = deepClone(teamInfo.custom);
          let isMute = (custom && custom.isMute == 1) || teamInfo.mute;
          if (userTeamInfo.value.type == "normal" && (isMute || userTeamInfo.value.mute)) {
            // 全体禁言/个人被不显示编辑器
            flag = false;
          }
        }
      }
      showEditorFlag.value = flag;
    }

    // 是否订阅号或群助手
    function isSubOrHelper() {
      return getSessionType(sessionInfo.value).type == 8 || sessionInfo.value.isHelper;
    }

    // 返回会话二级列表
    function backList() {
      if (props.backMethods) {
        props.backMethods();
        return;
      }
      let id = "p2p-" + config.helperAccount;
      let sessionType = getSessionType(sessionInfo.value).type;
      if (sessionType == 8) {
        id = "p2p-" + config.subscribe;
      }
      store.dispatch("setCurrentSession", {id: id});
    }

    // 显示选择工具弹窗 type-1表情-2截图-3群消息提醒-4发送消息-5ai应用-6ai应用子项
    function showToolSel(type) {
      if (showToolType.value == type) {
        showToolType.value = -1;
        return;
      }
      switch (type) {
        case 1:
          break;
        case 2:
          getServerSetting(5);
          getServerSetting(6);
          break;
        case 3:
          getServerSetting(1);
          getServerSetting(2);
          break;
        case 4:
          getServerSetting(4);
          break;
        case 5:
          aiSearchObj.value.key = "";
          aiObj.value.showAppList = deepClone(aiObj.value.defaultAppList);
          doSearchAi();
          nextTick(() => {
            chatContentRef.value.querySelector(".tool-search input").focus();
            calcToolBox(chatContentRef.value.querySelector(".tool-app .tool-sel-ul"), aiSearchObj.value.appElmObj);
          });
          break;
        case 6:
          nextTick(() => {
            calcToolBox(chatContentRef.value.querySelector(".tool-app-child .tool-sel-ul"), aiSearchObj.value.appChildElmObj);
          });
          break;
      }
      showToolType.value = type;
    }

    // 计算快捷选择框位置
    function calcToolBox(elm, obj) {
      // 计算表情选择框位置 52为顶部区域高度 输入框默认高度150
      if (editorResizeObj.value.height + 150 + elm.clientHeight > document.body.clientHeight - 52) {
        obj.bottom = -(elm.clientHeight - (document.body.clientHeight - (editorResizeObj.value.height + 150) - 52));
        obj.left = elm.parentElement.clientWidth;
      } else {
        obj.bottom = obj.defaultBottom;
        obj.left = 0;
      }
      // 除了表情选择框都设置元素位置
      if (elm.className != "emoji-sel-box") {
        elm.style.bottom = obj.bottom + "px";
        elm.style.left = obj.left + "px";
      }
    }

    // 隐藏@人员/文档
    function hideHait() {
      store.commit("setHaitInfo", {});
    }

    // 表情加载完成
    function showEmojiDone() {
      calcToolBox(emojiRef.value.emojiBoxRef, emojiObj.value);
    }

    // 选择表情发送/插入输入框
    function selEmoji(item, emojiKey) {
      if (emojiKey == "jjs_emoji" || emojiKey == "emoji") {
        // 插入输入框
        store.getters.getImEditor.insertEmoji(item.key, `/img/emoji/${emojiKey}/${item.value}${item.ext}`, sessionInfo.value.id);
        store.dispatch("activeImEditor", {id: sessionInfo.value.id, active: true});
      } else {
        // 发送自定义消息
        let content = {type: 3, data: {catalog: emojiKey, chartlet: item.value}};
        let msg = {type: "custom", content: content, pushContent: "[贴图]"};
        remote.store.dispatch("sendMsg", {sessionId: sessionInfo.value.id, messages: [msg]});
      }
      showToolSel(-1);
    }

    // 选择文件发送type-1图片-2文件
    async function selFile(type, callback) {
      store.commit("setEmit", {
        type: "fileInput", value: {
          multiple: type == 1 ? false : true,
          accept: type == 1 ? "image/gif,image/jpeg,image/png,image/jpg,image/bmp" : "*",
          done: files => {
            let p = [];
            for (let i = 0; i < files.length; i++) {
              let item = files[i].path;
              let param = {
                path: path.dirname(item) + "\\",
                name: path.basename(item)
              };
              p.push(getLocalFile(param));
            }
            Promise.all(p).then(files => {
              if (type == 1) {
                // 发送图片
                let file = files[0];
                if (!config.imgTypeReg.test(file.name)) {
                  toast({title: "请选择图片", type: 2});
                } else if (file.size == 0) {
                  toast({title: "不能发送空文件", type: 2});
                  return;
                } else if (file.size / 1024 / 1024 > 2) {
                  alert({
                    content: "图片大于2M，将以文件方式发送。",
                    done: (type) => {
                      if (type == 1) {
                        store.getters.getImEditor.pasteFile(files);
                      }
                    }
                  });
                  return;
                } else {
                  fileToDataURL(file).then((data) => {
                    store.getters.getImEditor.insertImage(data, store.getters.getCurrentSession.id);
                  });
                }
              } else if (type == 2) {
                // 发送文件
                if (callback) {
                  for (let i = 0; i < files.length; i++) {
                    // 设置消息关联
                    files[i].uniqueSign = MD5(`sign_${sessionInfo.value.scene}_${userInfo.workerNo}_${Date.now()}_${i}`);
                  }
                  callback(files);
                }
                store.getters.getImEditor.pasteFile(files);
              }
            }).catch(err => {
              toast({title: "文件加载错误请重试" + err, type: 2})
            })
          }
        }
      });
    }

    // 关闭当前会话
    function closeChat() {
      if (router.currentRoute.value.path == "/child/childChat") {
        store.commit("setWindowClose", store.getters.getCurrentWindow().cWindow.id);
        return;
      }
      store.dispatch("isRemoveSession", sessionInfo.value.id);
    }

    // 发送消息
    function sendMsg() {
      store.dispatch("inputSend");
    }

    // 删除引用消息
    function delQuoteMsg() {
      remote.store.commit("setQuoteMsg", {type: "del", id: sessionInfo.value.id});
    }

    // 选择@人员/文档 1文档 2唤起应用 3唤起ai
    function selHaitType(key) {
      haitInfo.value.type = key;
      // 不存在默认选中项，选中第一个
      if ((haitInfo.value.type == 0 && haitInfo.value.memberIndex == null)
        || (haitInfo.value.type == 1 && haitInfo.value.docIndex == null)
        || (haitInfo.value.type == 2 && haitInfo.value.appIndex == null)
        || (haitInfo.value.type == 3 && haitInfo.value.aiIndex == null)
      ) {
        selHaitInfo(0);
      }
      store.commit("setHaitInfo", {changeType: true, ...haitInfo.value});
    }

    // 当前选中的@信息,code-1为按键触发
    function selHaitInfo(key, code) {
      if (haitInfo.value.type == 1) {
        haitInfo.value.docIndex = key;
        if (code == 1) {
          nextTick(() => {
            scrollLi(haitDocRef.value, ".hait-detail");
          });
        }
      } else if (haitInfo.value.type == 2) {
        haitInfo.value.appIndex = key;
        if (code == 1) {
          nextTick(() => {
            scrollLi(callAppRef.value, ".hait-detail");
          });
        }
      } else if (haitInfo.value.type == 3) {
        haitInfo.value.aiIndex = key;
        if (code == 1) {
          nextTick(() => {
            scrollLi(callAiRef.value, ".hait-detail");
          });
        }
      } else {
        haitInfo.value.memberIndex = key;
        if (code == 1) {
          nextTick(() => {
            scrollLi(haitMemberRef.value, ".hait-detail");
          });
        }
      }
      store.commit("setHaitInfo", {changeType: true, ...haitInfo.value});
    }

    // 确定选中@信息
    function confirmHaitInfo(key) {
      let item = {};
      let {haitCursor, currCursor} = haitInfo.value;
      let cursorInfo = {
        index: haitCursor.index,
        length: (currCursor ? currCursor.index : haitCursor.index) - haitCursor.index
      };
      if (cursorInfo.length == 0) {
        cursorInfo.length = 1;
      }
      if (haitInfo.value.type == 1) {
        // @文档
        item = haitDocList.value[key];
        if (item) {
          let param = {
            name: "doc",
            empNumber: item.empNumber,
            key: item.padId,
            docId: item.id,
            docImg: item.imgPath,
            ext: item.suffix || (item.property == 2 ? ".xlsx" : ".docx"),
            property: item.property,
            id: sessionInfo.value.id,
            range: cursorInfo,
          };
          if (item.title.length > 20) {
            param.value = item.title.slice(0, 20) + "..." + item.suffix;
          } else {
            param.value = item.title + item.suffix;
          }
          param.value = `@${param.value} `;
          param.img = strToImg(param.value, "", {underline: true, type: "doc"});
          setTimeout(() => {
            store.dispatch("sendFileAndDoc", {type: "doc", id: sessionInfo.value.id, to: sessionInfo.value.to, param: param});
          }, 100);
        }
      } else if (haitInfo.value.type == 2) {
        // 唤起应用
        item = callAppList.value[key];
        switch (item.key) {
          case "smartSchedule":
            // 跳转智能日程并打开弹窗
            remote.store.dispatch("toScheduleModal", "");
            break;
          default:
            // 跳转url
            store.dispatch("setOpenWindow", [config[config.env].jjsHome + item.url, "login"]);
            break;
        }
        // 删除唤起词
        store.getters.getImEditor.deleteText(sessionInfo.value.id, cursorInfo.index, cursorInfo.length);
      } else if (haitInfo.value.type == 3) {
        // 唤起ai
        item = callAiList.value[key];
        // 打开对应会话
        if (sessionInfo.value.to != aiObj.value.workerNo) {
          store.dispatch("setCurrentSession", {id: "p2p-" + aiObj.value.workerNo, type: "open"});
        }
        // 切换对应应用
        if (item.id && aiObj.value.currentApp.id != item.id) {
          store.commit("setEmit", {type: "switchAppTempItem", value: item});
        }
        // 删除唤起词
        store.getters.getImEditor.deleteText(sessionInfo.value.id, cursorInfo.index, cursorInfo.length);
      } else {
        // @人员
        item = haitMemberList.value[key];
        if (item) {
          let haitName = `@${item.name} `
          setTimeout(() => {
            store.getters.getImEditor.insertHaitImage(item.workerNo, haitName, `${strToImg(haitName)}`, sessionInfo.value.id, cursorInfo);
          }, 100);
        }
      }
      hideHait();
    }

    // 搜素@信息
    function searchHait() {
      if (!haitInfo.value.haitText) {
        return;
      }
      let text = regReplace(haitInfo.value.haitText.slice(1));
      if (haitInfo.value.haitFlag) {
        // 获取成员信息
        if (sessionInfo.value.scene != "p2p") {
          setTeamMembers().then(() => {
            // 当前会话聊天成员
            let msgAccountList = [];
            // 聊天会话成员表
            let msgAccountMap = {};
            let thisMsgList = deepClone(msgList.value).reverse();
            thisMsgList.map(item => {
              // 非提示消息和通知消息
              if (item.type != "tip" && item.type != "notification") {
                let itemPersonInfo = teamMembers.value.filter(item1 => {return item.from == item1.account})[0];
                // 聊天消息的人员-需要是群成员
                if (itemPersonInfo && (new RegExp(text, "i").test(itemPersonInfo.workerSpell) || new RegExp(text, "i").test(itemPersonInfo.userName) || !text)) {
                  if (!msgAccountMap[item.from]) {
                    msgAccountList.push(itemPersonInfo.detailInfo);
                    msgAccountMap[item.from] = true;
                  }
                }
              }
            });
            // 搜索出来的群主列表
            let ownerList = [];
            // 搜索出来的管理员列表
            let managerList = []
            // 搜索出来的普通成员列表
            let normalList = [];
            // 是否能@全员
            let allFlag = false;
            // 全体成员
            let allList = [];
            // 成员信息
            // 最多显示50人
            let maxLength = 50 - msgAccountList.length;
            for (let i = 0; i < teamMembers.value.length; i++) {
              if (maxLength < 0) {
                break;
              }
              let item = teamMembers.value[i];
              if (!allFlag && item.account == userInfo.workerNo && (item.type == "owner" || item.type == "manager")) {
                allFlag = true;
              }
              // 搜索到的人员-去除最近聊天成员
              if (!msgAccountMap[item.account] && (new RegExp(text, "i").test(item.detailInfo.workerSpell) || new RegExp(text, "i").test(item.detailInfo.name) || !text)) {
                if (item.type == "owner") {
                  ownerList.push(item.detailInfo);
                } else if (item.type == "manager") {
                  managerList.push(item.detailInfo);
                } else {
                  normalList.push(item.detailInfo);
                }
                maxLength--;
              }
            }
            if (allFlag && ('all'.indexOf(text) > -1
              || '所有人'.indexOf(text) > -1
              || '全体成员'.indexOf(text) > -1
              || '全员'.indexOf(text) > -1
              || 'qy'.indexOf(text) > -1
              || 'syr'.indexOf(text) > -1
              || 'qtcy'.indexOf(text) > -1
              || 'quanyuan'.indexOf(text) > -1
              || 'suoyouren'.indexOf(text) > -1
              || 'quantichengyuan'.indexOf(text) > -1)) {
              allList.push({workerNo: "all", nick: " 全员", name: " 全员"});
            }
            // 排序-全体-最近聊天成员-群主-管理员-其他成员
            haitMemberList.value = [].concat(allList, msgAccountList, ownerList, managerList, normalList);
            memberLoading.value = false;
          });
        }
        // 获取文档信息
        debounce({
          timerName: "searchDocApi",
          time: 300,
          fnName: async () => {
            let res = {};
            if (new RegExp(config.fcw).test(sessionInfo.value.id)) {
              //  客户会话
              res = await searchDocCustomerApi({title: text, rows: 50});
            } else {
              res = await searchDocApi({title: text, rows: 50, property: -1});
            }
            docLoading.value = false;
            if (res && res.data && res.data.list) {
              haitDocList.value = res.data.list;
            }
          }
        });
      } else if (haitInfo.value.callFlag) {
        // 唤起应用
        let list = [];
        let defaultList = deepClone(callAppDefaultList);
        if (text) {
          defaultList.map(item => {
            if (new RegExp(text, "i").test(item.title)) {
              list.push(item);
            }
          });
        } else {
          list = defaultList;
        }
        callAppList.value = list;
        callAppLoading.value = false;
        // 获取ai应用信息
        callAiList.value = [];
        callAiLoading.value = true;
        doSearchAi(text);
      }
    }

    // 聚焦当前聊天框
    function activeEditor(active) {
      store.dispatch("activeImEditor", {id: sessionInfo.value.id, active: active});
    }

    // 切换@闪动状态
    function toggleHaitActive() {
      debounce({
        timerName: "toggleHaitActive",
        time: 100,
        fnName: function () {
          if (haitActiveTimer) {
            clearTimeout(haitActiveTimer);
          }
          // 初始化@状态
          haitActive.value = true;
          showHaitFlag.value = false;
          showHaitMsgFlag.value = true;
          showConcernFlag.value = false;
          showConcernMsgFlag.value = true;
          haitActiveTimer = setTimeout(() => {
            haitActive.value = false;
            haitActiveTimer = "";
          }, 2900);
        }
      });
    }

    // 切换显示@内容/特别关心
    function showFloat(type) {
      if (type == 1) {
        showHaitFlag.value = true;
      } else if (type == 2) {
        showConcernFlag.value = true;
      }
    }

    // 切换显示@内容/特别关心列表状态 type-1@我-2特别关心
    function toggleFloatMsg(type) {
      if (type == 1) {
        showHaitMsgFlag.value = !showHaitMsgFlag.value;
      } else if (type == 2) {
        showConcernMsgFlag.value = !showConcernMsgFlag.value;
      }
    }

    // 移除@内容 type-1@我-2特别关心
    function removeFloatMsg(type) {
      if (type == 1) {
        store.commit("setHaitMsgMap", {type: "remove", id: sessionInfo.value.id});
      } else if (type == 2) {
        store.commit("setConcernMsgMap", {type: "remove", id: sessionInfo.value.id});
      }
    }

    // 滚动消息聚焦-type1会话消息
    function scrollMsg(msgRef, idServer, type) {
      if (idServer) {
        let thisElm = msgRef.querySelector(".msg-info-" + idServer);
        if (thisElm) {
          msgRef.scrollTop = thisElm.offsetTop - 100;
          store.commit("setFocusMsg", {idServer: idServer, type: type || 1});
        } else {
          toast({title: "更多消息请在消息记录中查询"});
        }
      }
    }

    // 获取用户信息
    function getPerson(account) {
      return store.getters.getPersons(account);
    }

    // 获取消息简要
    function getPrimaryMsg(type, msg) {
      if (type == 1) {
        return store.getters.getPrimaryMsg({msg: msg, primaryType: type});
      } else {
        return store.getters.getPrimaryMsg({msg: preLastMsg, primaryType: type});
      }
    }

    // 显示用户详情弹窗
    function showUserInfo(item, e) {
      let isFcw = new RegExp(config.fcw).test(item.to);
      if (item.scene != "p2p" || isFcw) {
        // 双击清除定时器
        if (showUserInfoTimer) {
          clearTimeout(showUserInfoTimer);
          showUserInfoTimer = "";
          changeInputName(1, 1)
          return;
        }
        // 单击显示群成员
        showUserInfoTimer = setTimeout(() => {
          showUserInfoTimer = "";
          if (!isInputObj.value["name1"] && !isFcw && !sessionInfo.value.detailInfo.notInTeam) {
            showTagClick(4);
          }
        }, 300);
      } else {
        let account = item ? item.to : "";
        store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: account, selUserElm: e}});
        // 公众号点击名字支持返回
        if (isShowBack()) {
          backList();
        }
      }
    }

    // 切换显示分页
    function toggleMemberPageFlag(flag) {
      if (flag != null) {
        memberPageFlag.value = flag;
      } else {
        memberPageFlag.value = !memberPageFlag.value;
      }
    }

    // 选择分页
    function changeMemberPage(page) {
      switch (page) {
        case "pre":
          if (teamMembersPage.value - 1 > 0) {
            teamMembersPage.value--;
          }
          break;
        case "next":
          if (teamMembersPage.value + 1 <= teamMembersTotal.value) {
            teamMembersPage.value++;
          }
          break;
        default:
          teamMembersPage.value = page;
          break;
      }
      getShowTeamMember();
      toggleMemberPageFlag(false);
    }

    // 打开会话
    async function openChat(scene, to) {
      if (new RegExp(config.ai).test(to)) {
        // 打开ai会话判断权限
        loading();
        let personInfo = await remote.store.dispatch("getPersons", {doneFlag: true, account: [to]});
        loading().hide();
        if (personInfo[to]?.hasPermission) {
          store.dispatch("setCurrentSession", {id: scene + "-" + to});
        } else {
          toast({title: personInfo[to]?.hasPermission != null ? "您暂无该应用的权限" : "系统错误", type: 2});
        }
      } else {
        // 问答助手不支持私聊
        if (to == teamAiObj.value.aiInfo?.to) {
          return;
        }
        store.dispatch("setCurrentSession", {id: scene + "-" + to});
      }
    }

    // 切换搜索状态
    function toggleMemberSearchFlag() {
      memberSearchFlag.value = !memberSearchFlag.value;
      if (memberSearchFlag.value) {
        nextTick(() => {
          // 自动聚焦
          chatContentRef.value.querySelector(".search-member-input").focus();
        });
      } else {
        // 重置搜索状态
        resetSearchTeamMember()
      }
    }

    // 禁言群
    function muteTeam() {
      let teamInfo = store.getters.getTeams({id: sessionInfo.value.to});
      let custom = deepClone(teamInfo.custom);
      let isMute = (custom && custom.isMute == 1) || teamInfo.mute;
      alert({
        content: `您确认要${isMute ? "解除" : "开启"}全体禁言吗?`,
        done: (type) => {
          if (type == 1) {
            let teamType = teamInfo.detailType == "group" ? "group" : "superGroup";
            if (custom) {
              custom.type = teamType;
              custom.operatorType = "mute";
              custom.isMute = isMute ? "0" : "1";
              custom.operatorNo = userInfo.workerNo;
              custom.operatorDate = (new Date()).valueOf();
            } else {
              custom = {
                type: teamType,
                operatorType: "mute",
                isMute: isMute ? "0" : "1",
                operatorNo: userInfo.workerNo,
                operatorDate: (new Date()).valueOf()
              };
            }
            // 兼容老版本-全体禁言先调用老api。解除禁言先调用新api
            if (isMute) {
              // 解除全体禁言
              remote.store.getters.getNim.updateTeamMute({mute: false, teamId: sessionInfo.value.to}).then(res => {
                if (res.error) {
                  toast({title: `抱歉,解除全体禁言失败,请稍后再试!` + res.error, type: 2});
                  return;
                }
                remote.store.getters.getNim.updateTeam({custom: JSON.stringify(custom), teamId: sessionInfo.value.to}).then(res => {
                  if (res.error) {
                    toast({title: `抱歉,解除全体禁言失败,请稍后再试!` + res.error, type: 2});
                    return;
                  }
                  toast({title: `解除全体禁言成功`, type: 1});
                });
              });
            } else {
              // 全体禁言
              remote.store.getters.getNim.updateTeam({custom: JSON.stringify(custom), teamId: sessionInfo.value.to}).then(res => {
                if (res.error) {
                  toast({title: `抱歉,全体禁言失败,请稍后再试!` + res.error, type: 2});
                  return;
                }
                // 新增调用
                remote.store.getters.getNim.updateTeamMute({mute: true, teamId: sessionInfo.value.to});
                toast({title: `全体禁言成功`, type: 1});
              });
            }
          }
        }
      })
    }

    // 踢出群成员-进线客户讨论组不能踢人
    function removeMember(item) {
      if (userTeamInfo.value.type != 'normal' && item.type == 'normal' && !isDisabledGroup(sessionInfo.value)) {
        alert({
          content: `确定移出该成员?`,
          done: (type) => {
            if (type == 1) {
              let teamInfo = store.getters.getTeams({id: sessionInfo.value.to});
              loading();
              comeInAndGoOutGroupApi({
                msgBody: JSON.stringify({
                  type: 2,
                  teamType: teamInfo.detailType == "group" ? "2" : "1",
                  tid: sessionInfo.value.to,
                  workerNo: item.account
                })
              }).then(function (res) {
                loading().hide();
                if (!res.success) {
                  alert({content: (res.errorMsg || "抱歉,操作失败,请稍后再试!") + res.errorCode, showCancel: false, okText: "我知道了"});
                }
              });
            }
          }
        });
      }
    }

    // 退出/解散群
    function leaveOrDismissTeam() {
      if (userTeamInfo.value.type == 'owner') {
        // 群主解散群
        store.dispatch("dismissTeam", {id: sessionInfo.value.to});
      } else {
        // 非群主退出群
        store.dispatch("leaveTeam", {id: sessionInfo.value.to});
      }
    }

    // 群人员右键菜单
    function setMenu(e, item) {
      let teamInfo = store.getters.getTeams({id: sessionInfo.value.to});
      let menuList = [];
      // @TA
      if (showEditorFlag.value) {
        menuList.push({
          label: "@TA", click: function () {
            let haitName = `@${item.detailInfo.empType == 2 ? item.detailInfo.userShowName || item.detailInfo.name : item.detailInfo.name} `;
            store.getters.getImEditor.insertHaitImage(item.detailInfo.workerNo, haitName, `${strToImg(haitName)}`, sessionInfo.value.id);
          }
        });
      }
      if (userInfo.workerNo != item.account && !isDisabledGroup(sessionInfo.value)) {
        // 群主可移交给非禁言的管理员,讨论组都可以移交（进线客户讨论组不能移交）
        if (userTeamInfo.value.type == "owner" && ((teamInfo.detailType != "group" && item.type == "manager") || teamInfo.detailType == "group") && !item.mute) {
          let teamTypeName = "讨论组";
          if (teamInfo.detailType != "group") {
            teamTypeName = "高级群";
            if (teamInfo.detailType == "superTeam") {
              teamTypeName = "超大群";
            }
          }
          menuList.push({
            label: "移交为创建人", click: function () {
              alert({
                content: `您确认要将该讨论组的创建人移交给 ${item.detailInfo.name} 吗?`,
                done: (type) => {
                  if (type == 1) {
                    handOverCreatorApi({
                      msgBody: JSON.stringify({
                        type: teamInfo.detailType == "group" ? "4" : "5",
                        tid: sessionInfo.value.to,
                        oldOwner: userInfo.workerNo,
                        newOwner: item.account
                      })
                    }).then(res => {
                      if (!res.success) {
                        toast({title: (res.errorMsg || "抱歉,操作失败,请稍后再试!") + res.errorCode, type: 2});
                      } else {
                        toast({title: `该${teamTypeName}的创建人已移交给${item.detailInfo.name}`, type: 1});
                      }
                    });
                  }
                }
              })
            }
          });
        }
        // 群主/管理员可禁言普通成员
        if (userTeamInfo.value.type != "normal" && item.type == "normal") {
          // 禁言/解除禁言
          menuList.push({
            label: `${item.mute ? "解除" : ""}禁言`, click: function () {
              remote.store.getters.getNim.updateMuteStateInTeam({teamId: sessionInfo.value.to, accounts: [item.account], mute: !item.mute}).then(res => {
                if (res.err) {
                  alert({content: (res.err.message || "操作失败请重试") + res.err.code});
                  return;
                }
                remote.store.commit("updateTeamMembers", {updateType: "update", team: {teamId: res.obj.teamId}, members: [{id: `${res.obj.teamId}-${item.account}`, mute: res.obj.mute}]});
              });
            }
          });
        }
      }
      showMenu(menuList).popup(e.x, e.y);
    }

    // 判断是否显示底部消息提示
    function showLastMsgTips() {
      if (isScrollBottom.value) {
        if (msgList.value.length > 0) {
          preLastMsg = msgList.value[msgList.value.length - 1];
          lastMsgTipsFlag.value = false;
        }
      } else {
        if (msgList.value.length > 0 && msgList.value[msgList.value.length - 1].time != preLastMsg.time && msgList.value[msgList.value.length - 1].from != userInfo.workerNo) {
          lastMsgTipsFlag.value = true;
        }
      }
    }

    // 设置当前群员的信息
    function setTeamMembers() {
      return new Promise(resolve => {
        setTimeout(() => {
          // 存在群成员直接返回
          if (teamMembers.value.length > 0) {
            resolve(teamMembers.value);
            return;
          }
          let thisId = sessionInfo.value.to;
          // 请求状态返回
          if (teamMembersMap[thisId] instanceof Promise) {
            resolveTeamMembers(thisId, resolve);
            return;
          } else if (teamMembersMap[thisId] && teamMembersMap[thisId].length == 0) {
            resolve(teamMembersMap[thisId]);
            return;
          }
          teamMembersMap[thisId] = store.dispatch("getTeamMembers", {id: thisId});
          resolveTeamMembers(thisId, resolve);
        }, 0)
      });
    }

    // 返回群成员信息
    function resolveTeamMembers(thisId, resolve) {
      teamMembersMap[thisId].then(res => {
        teamMembersMap[thisId] = "";
        if (thisId != sessionInfo.value.to) {
          teamMembers.value = [];
          return;
        }
        // 加载群信息出错
        teamMembersError.value = res.err;
        if (!teamMembersError.value) {
          // 群主、管理员、普通成员排序
          let list = sortTeamMembers(res.obj);
          // 获取群员详情
          store.dispatch("getPersons", {doneFlag: true, account: list.map(item => {return item.account})}).then(personInfo => {
            list.map(item => {
              item.detailInfo = setUserBaseInfo(personInfo[item.account]);
              if (new RegExp(config.fcw).test(item.account)) {
                item.detailInfo.name = `${item.detailInfo.name} (乐有家网客户)`;
              } else if (item.detailInfo.empType == 2) {
                item.detailInfo.name = item.detailInfo.userShowName;
              }
            });
            if (thisId == sessionInfo.value.to) {
              teamMembers.value = list;
            }
            resolve(teamMembers.value);
          });
          // 获取当前成员在群信息
          if (!userTeamInfo.value.type) {
            for (let i = 0; i < list.length; i++) {
              if (list[i].account == userInfo.workerNo) {
                userTeamInfo.value = list[i];
                isShowEditor();
                break;
              }
            }
          }
        } else {
          resolve(teamMembers.value);
        }
      });
    }

    // 显示当前群员聚焦
    function showCurr(index) {
      teamMembersPageList.value.map((item, key) => {
        delete item.curr;
        if (index == key) {
          item.curr = true;
        }
      })
    }

    // 搜索群成员-type1清空
    function searchTeamMember(type) {
      debounce({
        timerName: "searchTeamMember",
        time: 500,
        fnName: function () {
          if (type == 1) {
            searchTeamMemberObj.value.text = "";
          }
          let text = searchTeamMemberObj.value.text;
          let list = [];
          // 存在搜索内容才查询
          if (text && teamMembers.value.length > 0) {
            text = regReplace(text);
            teamMembers.value.map(item => {
              if (text && new RegExp(text, "i").test(item.detailInfo.workerSpell) || new RegExp(text, "i").test(item.detailInfo.name)) {
                list.push(item);
              }
            });
            searchTeamMemberObj.value.list = list;
          }
          searchTeamMemberObj.value.index = 0;
          // 不存在搜索内容隐藏
          if (text) {
            searchTeamMemberObj.value.show = true;
          } else {
            searchTeamMemberObj.value.show = false;
          }
        }
      });
    }

    // 确定搜索群员
    function confirmSearchMember() {
      let account = searchTeamMemberObj.value.list[searchTeamMemberObj.value.index].account;
      let index = teamMembers.value.findIndex(item => {return item.account == account});
      if (teamMembersTotal.value > 1) {
        // 超大群200个成员分页
        teamMembersPage.value = Math.floor(index / 200 + 1);
      }
      getShowTeamMember(account);
      hideSearchMember();
      searchTeamMemberObj.value.text = "";
    }

    // 重置搜索
    function resetSearchTeamMember() {
      searchTeamMemberObj.value = {
        index: 0,
        text: "",
        show: false,
        list: [],
      }
      getShowTeamMember();
    }

    // 当前选中的搜索结果
    function selSearchMember(key) {
      let index = searchTeamMemberObj.value.index;
      let total = searchTeamMemberObj.value.list.length - 1;
      switch (key) {
        case "pre":
          if (index - 1 < 0) {
            index = total;
          } else {
            index--;
          }
          break;
        case "next":
          if (index + 1 > total) {
            index = 0;
          } else {
            index++;
          }
          break;
        default:
          index = key;
          break;
      }
      searchTeamMemberObj.value.index = index;
      nextTick(() => {
        scrollLi(chatContentRef.value.querySelector(".search-member-ul"), ".search-member-li");
      });
    }

    // 隐藏搜索结果
    function hideSearchMember() {
      searchTeamMemberObj.value.show = false;
    }

    // 获取服务器配置信息，4发送快捷键-5截图类型-6截图快捷键
    async function getServerSetting(type) {
      let serverSettings = remote.store.getters.getSettings;
      // 子窗口会话设置信息
      if (router.currentRoute.value.path == "/child/childChat") {
        store.commit("setSetting", {child: true, settings: serverSettings});
      }
      let key = `type${type}`;
      if (serverSettings[config.settings[key]]) {
        settings.value[key] = serverSettings[config.settings[key]];
      }
      if (type == 6) {
        settings.value["jtConflict"] = jtConflictStatus(settings.value[key]);
      }
    }

    // 获取快捷键是否冲突
    function jtConflictStatus(key) {
      return !isRegistered(key);
    }

    // 变更配置
    function modifySettings(type, value) {
      console.log("modifySettings", type, value);
      showToolSel(-1);
      let param = {}
      if (type == 4 || type == 5) {
        // type-4发送快捷键-5截图类型
        param.key = config.settings[`type${type}`];
        param.value = String(value);
      } else if (type == 1) {
        // type-1群提示
        param.modify = true;
        param.type = 1;
        param.value = sessionInfo.value.id;
        param.detailType = sessionInfo.value.detailInfo.detailType;
        let isNoTip = sessionInfo.value.isNoTip;
        let isHelper = sessionInfo.value.isHelper;
        if (value == 1) {
          param.remark = "接收且提醒";
          param.key = config.settings[`type${isNoTip ? 1 : 2}`];
          param.operation = "delete";
        } else if (value == 2) {
          param.remark = "接收但不提醒";
          param.key = config.settings.type1;
          param.operation = "add";
          if (isHelper) {
            param.operation = "update";
            param.oldKey = config.settings.type2;
          }
        } else if (value == 3) {
          param.remark = "接收且提醒";
          param.key = config.settings.type2;
          param.operation = "add";
          if (isNoTip) {
            param.operation = "update";
            param.oldKey = config.settings.type1;
          }
        }
      }
      remote.store.dispatch("setModifySettings", param).then(res => {
        // 重新获取主窗口配置信息
        getServerSetting(type);
      });
    }

    // 变更ai配置-type1应用2子应用3gpt模式4切换到默认应用
    function modifyAiSettings(type, item, childItem) {
      let param;
      switch (type) {
        case 1:
          remote.store.commit("setAiObj", {currentApp: item});
          newTopics(true, false, item);
          showToolSel(-1);
          break;
        case 2:
          param = {currentApp: deepClone(item)};
          remote.store.commit("setAiObj", param);
          showToolSel(-1);
          break;
        case 3:
          // item1切换gpt模式，2关闭gpt提示
          let param = {};
          if (item == 1) {
            param.gpt4 = !aiObj.value.gpt4;
            if (param.gpt4) {
              param.gpt4Tips = true;
            } else {
              param.gpt4Tips = false;
            }
          } else {
            param.gpt4Tips = false;
          }
          newTopics(true, true);
          remote.store.commit("setAiObj", param);
          break;
        case 4:
          item = aiObj.value.commonAppList.find(item => {return item.isDefault == 1})
          remote.store.commit("setAiObj", {currentApp: item});
          newTopics(true, false, item);
          break;
      }
    }

    // 打开截图
    function openJt() {
      console.log("点击截图");
      store.dispatch("setOpenJt", {isFocus: true, key: 1});
    }

    // 窗口抖动
    function windowShake() {
      remote.store.dispatch("windowShake", {
        id: sessionInfo.value.id,
        scene: sessionInfo.value.scene,
        to: sessionInfo.value.to,
      });
    }

    // 查看前后消息-前后25条消息
    async function showRecord(time, idServer) {
      let recordInfo = await store.dispatch("showRecord", {time: time, sessionId: sessionInfo.value.id});
      let {list, p} = recordInfo;
      if (list.length > 0) {
        Promise.all(p).then(res => {
          list = list.sort((a, b) => {
            return a.time - b.time
          });
          // 查询服务端前后消息有重复数据，去重
          for (let i = 0; i < list.length; i++) {
            for (let j = i + 1; j < list.length; j++) {
              if ((list[i].idServer && list[i].idServer === list[j].idServer) || (list[i].idClient && list[i].idClient === list[j].idClient)) {
                list.splice(j, 1);
                j--;
              }
            }
          }
          recordList.value = deepClone(list);
          searchMsgObj.value.show = false;
          searchMsgObj.value.back = true;
          nextTick(() => {
            list.map(item => {
              if (item.idServer == idServer) {
                setTimeout(() => {
                  scrollMsg(recordMsgRef.value, item.idServer, 3);
                }, 500)
              }
            });
          });
        })
      } else {
        toast({title: "暂无聊天记录", type: 2});
      }
    }

    // 获取消息记录
    function getMsgHistory(type) {
      if (searchMsgObj.value.show) {
        // 搜索聊天记录翻页
        let page = searchMsgObj.value.page;
        switch (type) {
          case "pre":
            if (page * 50 < searchMsgObj.value.total) {
              page++;
            } else {
              toast({title: "没有更多记录了哦", type: 1});
              return;
            }
            break;
          case "next":
            if (page > 1) {
              page--;
            } else {
              toast({title: "没有更多记录了哦", type: 1});
              return;
            }
            break;
          default:
            return;
            break;
        }
        queryCloudMessage(page);
      } else {
        // 历史记录翻页
        let param = {id: sessionInfo.value.id, type: 2, limit: 50};
        switch (type) {
          case "pre":
            param.endTime = recordList.value.length > 0 ? recordList.value[0].time : undefined;
            param.page = 1;
            if (searchMsgObj.value.notInTeamPage) {
              param.endTime && (param.endTime = param.endTime - 1000);
            }
            break;
          case "next":
            param.beginTime = recordList.value.length > 0 ? recordList.value[recordList.value.length - 1].time + 1 : undefined;
            param.page = 1;
            if (searchMsgObj.value.notInTeamPage) {
              param.beginTime && (param.beginTime = param.beginTime + 999);
            }
            param.reverse = true;
            break;
          case "time":
            param.beginTime = selSearchDate.value.getTime();
            param.reverse = true;
            break;
          default:
            recordList.value = [];
            break;
        }
        loading();
        store.dispatch("setHistory", param).then(res => {
          loading().hide();
          if (!res.err && res.obj && res.obj.msgs) {
            if (res.obj.msgs.length > 0) {
              let list = res.obj.msgs.sort((a, b) => {return a.time - b.time});
              let p = [];
              list.map(item => {
                p.push(store.dispatch("setMsgField", {item: item}));
              });
              Promise.all(p).then(res => {
                recordList.value = deepClone(list);
                nextTick(() => {
                  if (type == "time" || type == "next") {
                    recordMsgRef.value.scrollTop = 0;
                  } else {
                    recordMsgRef.value.scrollTop = recordMsgRef.value.querySelector(".chat-msg").clientHeight;
                  }
                });
              });
              searchMsgObj.value.notInTeamPage = res.obj.page;
            } else {
              toast({title: "暂无聊天记录", type: 2});
            }
          }
        });
      }
    }

    // 搜索聊天记录
    async function queryCloudMessage(page, flag) {
      if (flag) {
        if (!searchMsgObj.value.text.trim()) {
          toast({title: "请输入关键字", type: 2});
          return;
        }
        searchMsgObj.value.searchText = searchMsgObj.value.text.trim();
      }
      searchMsgObj.value.page = page;
      searchMsgObj.value.search = false;
      let msgInfo = await store.dispatch("queryCloudMessage", {text: searchMsgObj.value.searchText, scene: sessionInfo.value.scene, to: sessionInfo.value.to, page: searchMsgObj.value.page});
      searchMsgObj.value.search = true;
      searchMsgObj.value.show = true;
      if (msgInfo) {
        let {list, p, total} = msgInfo;
        Promise.all(p).then(res => {
          searchMsgObj.value.list = deepClone(list);
          nextTick(() => {
            searchMsgRef.value.scrollTop = searchMsgRef.value.querySelector(".chat-msg").clientHeight;
          });
        });
        searchMsgObj.value.total = total;
      }
    }

    // 切换显示搜索消息
    function toggleSearchMsg(flag) {
      if (flag || searchMsgObj.value.flag) {
        searchMsgObj.value = {
          text: "",
          searchText: "",
          total: 0,
          page: 1,
          back: false,
          search: false,
          show: false,
          flag: false,
          list: [],
        }
      } else {
        searchMsgObj.value.flag = !searchMsgObj.value.flag;
        if (searchMsgObj.value.flag) {
          nextTick(() => {
            // 自动聚焦
            chatContentRef.value.querySelector(".record-search-input").focus();
          });
        }
      }
    }

    // 返回搜索结果
    function backSearchMsg() {
      searchMsgObj.value.back = false;
      searchMsgObj.value.show = true;
    }

    // 设置当前时间
    function setCurrentDay(res) {
      selSearchDate.value = res && res.date ? res.date : new Date(new Date().getTime() + store.getters.getDiffTime);
      if (res && res.date) {
        getMsgHistory("time");
        calendarFlag.value = false;
      }
    }

    // 显示日历
    function showCalendar() {
      if (calendarFlag.value) {
        calendarFlag.value = false;
        return;
      }
      let maxDate = Date.now() + store.getters.getDiffTime;
      let minDate = maxDate - 365 * 24 * 60 * 60 * 1000;
      calendarRef.value.getCalendar({date: selSearchDate.value, minDate: minDate, maxDate: maxDate});
      calendarFlag.value = true;
    }

    // 阻止点击穿透
    function stopClick() {}

    function toNotice(item) {
      showTag.value = 2
      setTimeout(() => {
        let version = ''
        try {
          version = item.attach.team.announcement.version;
        } catch (error) {}
        chatNotice.value.activeItem({...item, acJson: {version: version}}, 1)
      }, 0)
    }

    // 改变群名可编辑状态
    function changeInputName(type, id) {
      let isFcw = new RegExp(config.fcw).test(sessionInfo.value.id);
      // 讨论组(进线客户讨论组除外)、房产网用户可以修改群名
      if ((sessionInfo.value.detailInfo.detailType == "group" && !isDisabledGroup(sessionInfo.value)) || isFcw) {
        if (type == 1) {
          // 编辑状态
          inputName.value = sessionInfo.value.detailInfo.name;
          isInputObj.value["name" + id] = true;
          setTimeout(() => {
            teamNameRef.value.focus();
          }, 0);
        } else if (type == 2) {
          // 失去焦点
          let oldName = sessionInfo.value.detailInfo.name;
          let name = inputName.value;
          if (!name) {
            name = oldName;
          }
          if (name != oldName) {
            if (isFcw) {
              // 备注
              store.dispatch("fcwRemark", {accid: sessionInfo.value.to, remark: name}).then(res => {
                if (!res?.success) {
                  toast({title: res.errorMsg || "系统错误", type: 2});
                }
              });
            } else {
              // 讨论组敏感词
              let hitList = [];
              let groupKeyTextList = remote.store.getters.getState("groupKeyTextList");
              groupKeyTextList.map(item => {
                if (new RegExp(regReplace(item)).test(name)) {
                  hitList.push(item);
                }
              })
              if (hitList.length > 0) {
                isInputObj.value["name" + id] = false;
                return toast({title: `群名称包含敏感词“${hitList.join("；")}”`, type: 2});
              }
              // 更新群名
              remote.store.getters.getNim.updateTeam({teamId: sessionInfo.value.to, name: name}).then(res => {
                if (res.err) {
                  alert({content: (res.err.message || "操作失败请重试") + res.err.code});
                }
              });
            }
          }
          isInputObj.value["name" + id] = false;
        } else if (type == 3) {
          // 回车确定
          teamNameRef.value.blur();
        } else if (type == 5) {
          // 取消编辑
          inputName.value = sessionInfo.value.detailInfo.name;
          isInputObj.value["name" + id] = false;
        }
      }
    }

    // 创建群二维码
    async function showTeamQrcode(flag) {
      teamQrcodeObj.value.flag = flag;
      if (flag) {
        let text = "jjsoa:";
        if (sessionInfo.value.detailInfo.detailType == "superTeam") {
          text = "jjsoaSuper:"
        }
        teamQrcodeObj.value.qrcode = await createQrCode({text: text + sessionInfo.value.to, width: 150});
      }
    }

    // 拖拽编辑器高度
    function editorResize(e, type) {
      switch (type) {
        case 1:
          // 去除原有选中内容
          window.getSelection().removeAllRanges();
          // 拖拽开始
          editorResizeObj.value.screenY = e.screenY;
          editorResizeObj.value.flag = true;
          editorResizeObj.value.tempHeight = editorResizeObj.value.height;
          editorResizeObj.value.winHeight = store.getters.getCurrentWindow().getBounds().height;
          break;
        case 2:
          // 拖拽中
          if (editorResizeObj.value.flag) {
            let resizeHeight = editorResizeObj.value.screenY + editorResizeObj.value.tempHeight - e.screenY;
            let maxHeight = editorResizeObj.value.winHeight - 52 - 45 - 150 - 100;
            resizeHeight < 0 ? resizeHeight = 0 : "";
            resizeHeight > maxHeight ? resizeHeight = maxHeight : "";
            editorResizeObj.value.height = resizeHeight;
            setAutoScroll("msg");
          }
          break;
        case 3:
          // 拖拽结束
          if (editorResizeObj.value.flag) {
            editorResizeObj.value.flag = false;
            store.commit("setEditorHeightMap", {id: sessionInfo.value.id, height: editorResizeObj.value.height});
            setAutoScroll("msg");
          }
          break;
      }
    }

    // 显示引用详情
    function showQuoteDetail(e, msg) {
      let target = e.currentTarget;
      let id = sessionInfo.value.id;
      let param = {id: id, type: 2, limit: 10, beginTime: msg.custom.time, endTime: msg.custom.time + 1000};
      store.dispatch("setHistory", param).then(async res => {
        if (!res.err) {
          console.log("showQuoteDetail", msg.custom.serverID);
          let thisMsg = res.obj.msgs.find(item => {
            if (item.idServer == msg.custom.serverID) {
              return item;
            }
          });
          if (thisMsg) {
            await store.dispatch("setMsgField", {item: thisMsg});
            quoteDetailObj.value.msg = [thisMsg];
            setTimeout(() => {
              let offset = target.getBoundingClientRect();
              quoteDetailObj.value.leftFlag = false;
              quoteDetailObj.value.top = 0;
              let left = offset.left + target.clientWidth;
              let top = offset.top - (quoteDetailRef.value.clientHeight) / 2 + (target.clientHeight) / 2;
              // 超过右侧显示到左侧
              if (left + quoteDetailRef.value.clientWidth > document.body.clientWidth) {
                left = offset.left - quoteDetailRef.value.clientWidth;
                quoteDetailObj.value.leftFlag = true;
              }
              // 超过顶部或底部显示贴边
              if (top < 0) {
                quoteDetailObj.value.top = top + quoteDetailRef.value.clientHeight / 2;
                top = 2;
              } else if (top + quoteDetailRef.value.clientHeight > document.body.clientHeight) {
                quoteDetailObj.value.top = top - document.body.clientHeight + quoteDetailRef.value.clientHeight / 2 * 3;
                top = document.body.clientHeight - quoteDetailRef.value.clientHeight - 2;
              }
              quoteDetailRef.value.style.left = left + "px";
              quoteDetailRef.value.style.top = top + "px";
              quoteDetailObj.value.flag = true;
            }, 100);
          } else {
            store.commit("setQuoteDeleteMap", {id: id, idServer: msg.idServer});
            // 通知更新消息
            store.commit("setEmit", {type: "reloadMsg", value: {id: store.getters.getCurrentSession.id, idServer: msg.idServer, type: "quote"}});
          }
        }
      });
    }

    // 显示文档操作
    function showDocOp(e, item) {
      let target = e.currentTarget;
      let offset = target.getBoundingClientRect();
      let left = offset.left - (docOpRef.value.clientWidth - target.clientWidth);
      let top = offset.top + target.clientHeight;
      if (top + docOpRef.value.clientHeight > document.body.clientHeight) {
        top = document.body.clientHeight - docOpRef.value.clientHeight - 5;
      }
      docOpRef.value.style.left = left + "px";
      docOpRef.value.style.top = top + "px";
      docOpObj.value.flag = false;
      item.flag = true;
      docOpObj.value = item;
    }

    // 改变文档权限
    async function changeDocPur(key) {
      let scene = sessionInfo.value.scene;
      let param = {
        fileId: docOpObj.value.docId,
        fileType: 1,
        name: scene == "p2p" ? "" : sessionInfo.value.detailInfo.name,
        number: scene == "p2p" ? getPerson(sessionInfo.value.to).workerId : sessionInfo.value.to,
        operateLevel: key,
        type: scene == "p2p" ? 1 : 5
      };
      let res = {};
      if (key == 1 || key == 2) {
        // 添加权限
        loading();
        res = await addPurDocListApi([param]);
        loading().hide()
      } else if (key == 3) {
        // 移除权限
        delete param.operateLevel;
        loading();
        res = await delPurDocApi(param);
        loading().hide()
      }
      let isSuccess = false;
      if (res.success) {
        if (key == 1 || key == 2) {
          if (res.data && res.data[0] && res.data[0].success) {
            isSuccess = true;
            toast({title: "授权成功", type: 1});
          } else {
            toast({title: res.data[0].errorMsg || "操作失败请重试", type: 2});
          }
        } else if (key == 3) {
          isSuccess = true;
          toast({title: "移除权限成功", type: 1});
        }
      } else {
        toast({title: res.errorMsg || "操作失败请重试", type: 2});
      }
      if (isSuccess) {
        // 发送通知给自己和别人(用于多端同步)
        remote.store.getters.getNim.sendCustomSysMsg({
          scene: "p2p",
          to: userInfo.workerNo,
          content: JSON.stringify({
            type: "updateDocument",
            account: sessionInfo.value.to,
            id: docOpObj.value.docId,
            key: key == 3 ? "del" : key,
            from: 1//1-pc 2-ios 3-android
          }),
          done: function () {}
        });
      }
    }

    // 修改多选状态
    function changeMultiple(flag) {
      isMultiple.value = flag;
    }

    // 多选点击-1逐条转发，2批量转发，3收藏，4删除，5保存，6转发至特定人员，7bug反馈
    function multipleSel(type) {
      if (type == 3) {
        showDialog(1);
      } else {
        msgRef.value.multipleSel(type);
      }
    }

    // 转发群二维码
    function forwardQrcode() {
      let canvas = document.createElement("canvas");
      let ctx = canvas.getContext("2d");

      let title = sessionInfo.value.detailInfo.name;
      let type = sessionInfo.value.detailInfo.detailType == "group" ? "讨论组" : "群";

      // 二维码宽高
      let qrcodeW = 175;
      let qrcodeH = 175;
      // 二维码左边距
      let qrcodeMarginLeft = 50;
      // 上边距
      let marginTop = 42;
      // 描边上边距
      let borderMarginTop = 43;
      // 描边下边距
      let borderMarginBottom = 20;
      // 下边距
      let marginBottom = 20;
      // 直线Y轴位置
      let lineY = marginTop + qrcodeH + borderMarginTop;
      // 群头像左边距
      let iconMarginLeft = 15;
      // 群头像宽高半径和Y轴位置
      let iconW = 40;
      let iconH = 40;
      let iconRadius = iconW / 2;
      let iconY = lineY + borderMarginBottom;
      // 文字X轴位置
      let textX = iconMarginLeft + iconW + 10;
      // 画图大小
      let canvasW = qrcodeW + qrcodeMarginLeft * 2;
      let canvasH = iconY + iconH + marginBottom;

      // 设置画图宽高
      canvas.setAttribute("width", canvasW);
      canvas.setAttribute("height", canvasH);
      ctx.fillStyle = "#FFFFFF";
      ctx.fillRect(0, 0, canvasW, canvasH);

      // 画二维码
      ctx.drawImage(teamQrcodeRef.value.querySelector(".qrcode-img"), qrcodeMarginLeft, marginTop, qrcodeW, qrcodeH);
      ctx.save();

      // 画直线
      ctx.strokeStyle = "#E7E7E7";
      ctx.beginPath();
      ctx.moveTo(iconMarginLeft, lineY);
      ctx.lineTo(canvasW - iconMarginLeft, lineY);
      ctx.stroke();
      ctx.save();

      // 画群头像
      ctx.beginPath();
      ctx.arc(iconMarginLeft + iconRadius, iconY + iconRadius, iconRadius, 0, 2 * Math.PI);
      ctx.clip();
      ctx.drawImage(teamQrcodeRef.value.querySelector(".team-avatar"), iconMarginLeft, iconY, iconW, iconH);
      ctx.restore();
      ctx.save();

      // 计算文字大小位置
      let maxTextW = canvasW - iconMarginLeft * 2 - iconW - 10;
      ctx.font = "bold 14px/20px Microsoft YaHei";
      ctx.fillStyle = "#333333";
      let titleW = ctx.measureText(title).width;
      if (titleW > maxTextW) {
        let singleStrW = titleW / title.length;
        let interceptLen = Math.floor(maxTextW / singleStrW);
        title = title.substr(0, interceptLen) + "...";
      }
      ctx.beginPath();
      ctx.fillText(title, textX, iconY + 17, maxTextW);

      ctx.font = "12px/17px Microsoft YaHei";
      ctx.fillStyle = "#999999";
      let desc = "扫一扫二维码，加入该" + type;
      let descW = ctx.measureText(desc).width;
      if (descW > maxTextW) {
        let singleDescStrW = descW / title.length;
        let interceptDescLen = Math.floor(maxTextW / singleDescStrW - 1);
        desc = desc.substr(0, interceptDescLen) + "...";
      }
      ctx.fillText(desc, textX, iconY + 17 + 3 + 15, maxTextW);
      ctx.save();

      let thisImg = canvas.toDataURL("image/png");
      let thisMsg = {
        msgBody: [{
          type: "image",
          url: thisImg,
          width: canvasW,
          height: canvasH,
          file: {
            url: thisImg
          }
        }],
        forwardType: "sendMsg",
        forwardContent: `<div style="display: flex;align-items: center"><img src="${thisImg}" style="max-width: 36px;margin-right: 10px;">${title}(二维码)</div>`,
      }
      openForward(thisMsg);
    }

    // 拖拽和粘贴文件
    function doEditor(e, type) {
      if (!isSubOrSer(sessionInfo.value.id)) {
        if (type == 1) {
          // 粘贴
          store.getters.getImEditor.pasteFile(e.clipboardData.files);
        } else if (type == 2) {
          // 拖入
          // 判断是否存在空文件
          if (e.dataTransfer && e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            for (let i = 0; i < e.dataTransfer.files.length; i++) {
              if (e.dataTransfer.files[i].size == 0) {
                toast({title: "不能发送空文件", type: 2});
                break;
              }
            }
          }
          store.getters.getImEditor.doDrop(e);
        }
      }
    }

    // 是否显示返回
    function isShowBack() {
      return props.backMethods || (isSubOrHelper() && !isSessionList(sessionInfo.value) && props.sessionType != 11 && router.currentRoute.value.path != "/child/childChat");
    }

    // 显示乐聊弹窗
    function showLyDialog(type) {
      let account = sessionInfo.value.to;
      let name = sessionInfo.value.detailInfo.name;
      let teamId = "";
      // 举报客户群被举报的人为groupTypeId
      if (type == 2 && sessionInfo.value.detailInfo && sessionInfo.value.detailInfo.groupType == 2 && sessionInfo.value.detailInfo.serverCustom &&
        sessionInfo.value.detailInfo.serverCustom.groupTypeId) {
        account = sessionInfo.value.detailInfo.serverCustom.groupTypeId;
        teamId = sessionInfo.value.to;
      }
      store.commit("setEmit", {type: "customerDialog", value: {type: type, account: account, name: name, teamId: teamId, time: Date.now()}});
    }

    // 快捷回复数据
    async function fcwQueryQuickReplyList() {
      loading();
      let res = await fcwQueryQuickReplyListApi({
        msgBody: JSON.stringify({
          accid: userInfo.workerNo,
          oldCityCode: userInfo.cityId,
          size: 50
        })
      });
      loading().hide();
      if (res.data && res.data.data && res.data.data.list) {
        quickReplyList.value = res.data.data.list;
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 切换显示快捷回复列
    function toggleQuickList(key) {
      quickReplyList.value[key].hide = !quickReplyList.value[key].hide;
    }

    // 快捷回复添加到输入框
    function addQuickReplyContent(item) {
      let msg = `<p>${item.content}</p>`;
      if (store.getters.getImEditor.hasContents(sessionInfo.value.id)) {
        msg = "<br>" + msg;
      }
      store.getters.getImEditor.dangerouslyPasteHTML(sessionInfo.value.id, msg);
    }

    // 打开链接
    function openLink(url) {
      store.dispatch("setOpenWindow", [linkFormat(url), "login"]);
    }

    // 快捷回复-打开搜索窗口
    async function showComponents() {
      emitMsg('msg', {type: 'window', name: 'collect', newWin: 1, width: 780, height: 524, path: `child/collect?scene=${sessionInfo.value.scene}&to=${sessionInfo.value.to}&userName=${sessionInfo.value.detailInfo.name}`});
    }

    // 获取当前用户在群信息
    function getUserTeamInfo() {
      // 获取当前用户在群信息
      let thisTeamId = sessionInfo.value.to;
      // 需要在群
      if (!sessionInfo.value.detailInfo?.notInTeam) {
        remote.store.dispatch("getUserTeamInfo", {id: thisTeamId}).then(res => {
          if (thisTeamId == sessionInfo.value.to) {
            userTeamInfo.value = res;
            isShowEditor();
            setAiToolsType();
          }
        });
      }
    }

    // 打开邀请群成员
    function openInvite() {
      store.commit("setEmit", {type: "inviteBox", value: {type: 2, sessionInfo: deepClone(sessionInfo.value), teamMembers: deepClone(teamMembers.value), time: Date.now()}});
    }

    // 打开切换讨论组状态
    function changeTeamType() {
      teamEditorRef.value.initTeamInfo(sessionInfo.value.to, sessionInfo.value.detailInfo);
    }

    // 加入群聊
    async function joinTeam(teamId) {
      loading();
      let res = await comeInAndGoOutGroupApi({
        msgBody: JSON.stringify({
          type: 1,
          teamType: "2",
          tid: teamId,
          workerNo: userInfo.workerNo
        })
      })
      loading().hide();
      if (res.success) {
        toast({title: "加入成功"});
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 弹窗操作-type:1选择分组2新建分组3霸屏转发4ai应用弹窗5群知识上传,key:1关闭2确认
    async function dialogOperate(type, key) {
      switch (type) {
        // 选择分组
        case 1:
          if (key == 1) {
            dialogObj.value.type = -1;
          } else {
            msgRef.value.multipleSel(3, dialogObj.value.moveSelItem);
            dialogObj.value.type = -1;
          }
          break;
        // 新建分组
        case 2:
          if (key == 1) {
            dialogObj.value.type1 = -1;
            dialogObj.value.done && dialogObj.value.done({});
          } else if (key == 2) {
            if (!dialogObj.value.classifyName) {
              toast({title: "请输入分组名称", type: 2});
              return;
            }
            let res = {};
            let thisItem = {};
            loading();
            if (dialogObj.value.tempKey == 1) {
              // 添加我的收藏分组
              res = await addCollectClassifyApi({
                msgBody: JSON.stringify({
                  empNo: userInfo.workerNo,
                  name: dialogObj.value.classifyName
                })
              });
            } else if (dialogObj.value.tempKey == 2) {
              // 添加我的话术分组
              res = await editCollectStoryClassifyApi({
                msgBody: JSON.stringify({
                  groupName: dialogObj.value.classifyName
                })
              });
            }
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            // 更新分组缓存
            if (dialogObj.value.tempKey == 1) {
              thisItem = res.data.data;
              store.commit("setCollectMap", {type: "collect", list: store.getters.getCollectMap.list.concat(thisItem)});
            } else if (dialogObj.value.tempKey == 2) {
              thisItem = {gid: res.data.data, gname: dialogObj.value.classifyName, speechNum: "0"};
              store.commit("setCollectMap", {type: "story", list: store.getters.getCollectMap.storyList.concat(thisItem)});
            }
            let newItem = deepClone(thisItem);
            dialogObj.value.moveList.push(newItem);
            dialogObj.value.type1 = -1;
            toast({title: "新建分组成功", type: 1});
            res.item = newItem;
            dialogObj.value.done && dialogObj.value.done(res);
          }
          break;
        // 霸屏转发
        case 3:
          if (key == 1) {
            dialogObj.value.type = -1;
            dialogObj.value.reportMsgHeight = 0;
          } else {
            let groupTypeList = dialogObj.value.reportGroup.filter(item => {return item.sel}).map(item => {return item.value});
            if (groupTypeList.length == 0) {
              toast({title: "请选择人员行为"});
              return;
            }
            loading();
            let res = await sendBroadcastingApi({
              msgBody: JSON.stringify({
                workerId: userInfo.workerId,
                workerNo: userInfo.workerNo,
                buttonNum: 0,
                rank: 3,
                groupTypeList: groupTypeList,
                contentJson: dialogObj.value.reportMsg.content.data.content,
                title: dialogObj.value.reportMsg.content.data.title
              })
            });
            loading().hide();
            if (res.success) {
              dialogObj.value.type = -1;
              toast({title: res?.data?.tipMsg || "操作成功", type: 1});
              changeMultiple(false);
              dialogObj.value.reportMsgHeight = 0;
            } else {
              toast({title: res.errorMsg || "系统错误", type: 2});
            }
          }
          break;
        // ai应用弹窗
        case 4:
          if (key == 1) {
            // 清空选项
            for (let i = 0; i < dialogObj.value.aiAppObj.appJson.length; i++) {
              let itemI = dialogObj.value.aiAppObj.appJson[i];
              delete itemI.value;
            }
          } else if (key == 2) {
            // 开始创作
            let aiParam = {
              questionType: 5,
              questionJson: [],
              empNo: userInfo.workerNo,
              empNumber: userInfo.workerId,
              aiAccid: sessionInfo.value.to,
              toTager: sessionInfo.value.to,
              source: 1,
              appTypeId: dialogObj.value.aiAppObj.aiApp.id,
            };
            let fromFlag = true;
            for (let i = 0; i < dialogObj.value.aiAppObj.appJson.length; i++) {
              let itemI = dialogObj.value.aiAppObj.appJson[i];
              // 必填提示
              if (itemI.request && itemI.value) {
                toast({title: (itemI.type == "radio" ? "请选择" : "请输入") + itemI.valName, type: 2});
                break;
              }
              if (itemI.value) {
                aiParam.questionJson.push(itemI);
              }
            }
            if (!fromFlag) {
              return;
            }
            aiParam.questionJson = JSON.stringify(aiParam.questionJson);
            // 开始提问
            let msg = {
              scene: sessionInfo.value.scene,
              from: sessionInfo.value.to,
              to: userInfo.workerNo,
              sessionId: sessionInfo.value.id,
              time: Date.now() + store.getters.getDiffTime,
              type: "text",
              aiLoading: true,
              notSetAiLoading: true,
              idServer: UUID.generate(),
              status: "success",
              forbidMsg: true,
              aiParam: aiParam,
              isSseMsg: true,
            }
            store.commit("setEmit", {type: "scroll", value: "bottom"});
            // 切换到创作应用
            if (aiObj.value.currentApp.id != dialogObj.value.aiAppObj.aiApp.id) {
              // 新建话题成功后再发消息
              dialogObj.value.aiAppObj.setAiQuestion = msg;
              store.commit("setEmit", {type: "switchAppTempItem", value: dialogObj.value.aiAppObj.aiApp});
            } else {
              store.dispatch("setAiQuestion", {aiJson: msg});
            }
            dialogObj.value.type = -1;
          } else if (key == 3) {
            dialogObj.value.type = -1;
          }
          break;
        // 群知识上传
        case 5:
          if (key == 1) {
            dialogObj.value.type = -1;
          } else {
            if (dialogObj.value.teamFileObj.selMapLength == 0) {
              toast({title: "请选择群文件上传"});
              return;
            }
            uploadKnowledge(Object.values(dialogObj.value.teamFileObj.selMap));
            dialogObj.value.type = -1;
          }
          break;
        // 编辑群ai简介
        case 6:
          if (key == 1) {
            dialogObj.value.type = -1;
          } else {
            let to = sessionInfo.value.to;
            if (!dialogObj.value.teamAiIntrObj.text) {
              toast({title: "请" + dialogObj.value.teamAiIntrObj.placeholder});
              return;
            }
            loading();
            let res = await updateTeamAiInfoApi({
              msgBody: JSON.stringify({
                tid: to,
                aiType: 1,
                describeText: dialogObj.value.teamAiIntrObj.text
              })
            });
            loading().hide();
            if (!res?.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            // 更新群ai简介
            toast({title: "操作成功", type: 1});
            dialogObj.value.type = -1;
            teamAiObj.value.aiInfo.prologue = dialogObj.value.teamAiIntrObj.text;
            store.dispatch("setTeamAiMap", {id: to, type: "update", value: {prologue: teamAiObj.value.aiInfo.prologue}});
          }
          break;
        // @人员选择
        case 7:
          if (key == 1) {
            dialogObj.value.type = -1;
          } else {
            // 确定@人员
            if (Object.keys(dialogObj.value.haitObj.selMap).length == 0) {
              toast({title: "请选择会话@的人"});
              return;
            }
            dialogObj.value.type = -1;
            for (let selKey in dialogObj.value.haitObj.selMap) {
              let item = dialogObj.value.haitObj.selMap[selKey];
              if (item?.detailInfo) {
                let haitName = `@${item.detailInfo.name} `
                store.getters.getImEditor.insertHaitImage(item.account, haitName, `${strToImg(haitName)}`, sessionInfo.value.id);
              }
            }
          }
          break;
      }
    }

    // 显示弹窗type:1选择分组2新增分组名
    function showDialog(type, type1, tempKey, done) {
      dialogObj.value.done = done;
      switch (type) {
        case 1:
          // 选择分组
          dialogObj.value.type = type;
          dialogObj.value.moveList = deepClone(store.getters.getCollectMap.list);
          dialogObj.value.moveSelItem = dialogObj.value.moveList[0] || {};
          // 更新分组
          store.dispatch("getCollectClassify").then(res => {
            if (res.success) {
              store.commit("setCollectMap", {type: "collect", list: res.data.data});
              dialogObj.value.moveList = deepClone(res.data.data);
              dialogObj.value.moveSelItem = dialogObj.value.moveList[0] || {};
            }
          });
          break;
        case 2:
          // 新增分组
          dialogObj.value.type1 = type1;
          dialogObj.value.classifyName = "";
          dialogObj.value.tempKey = tempKey;
          // 打开新建分组弹窗
          nextTick(() => {
            // 聚焦到输入框
            lyDialogInputRef.value.focus();
          });
          break;
        case 3:
          // 霸屏消息
          dialogObj.value.type = type;
          dialogObj.value.reportMsg = type1;
          dialogObj.value.reportGroup = deepClone(purMap.value.reportMsgGroup);
          nextTick(() => {
            let reportGroupHeight = forwardReportRef.value.querySelector(".forward-report-sel-box").clientHeight;
            let reportMsgMaxHeight = 620 - 34 - 48 - 16 * 2 - 25 * 2 - 10 - reportGroupHeight;
            let reportMsgHeight = forwardReportRef.value.querySelector(".msg-report").clientHeight;
            if (reportMsgHeight >= reportMsgMaxHeight) {
              dialogObj.value.reportMsgHeight = reportMsgMaxHeight;
            } else {
              dialogObj.value.reportMsgHeight = reportMsgHeight;
            }
          });
          break;
        case 4:
          // 霸屏消息示例
          openViewer([{src: "/img/index/msg/img_report.png", dataSrc: "/img/index/msg/img_report.png", w: 940, h: 1090, ext: "png"}], 0, 940, 1090);
          break;
        case 5:
          // 上传群文件
          dialogObj.value.type = type;
          dialogObj.value.teamFileObj.type = type1;
          break;
        case 6:
          // 编辑群ai简介
          dialogObj.value.type = type;
          break;
        case 7:
          // @人员多选
          // 删除@符号
          let {haitCursor, currCursor} = haitInfo.value;
          let cursorInfo = {
            index: haitCursor.index,
            length: (currCursor ? currCursor.index : haitCursor.index) - haitCursor.index
          };
          if (cursorInfo.length == 0) {
            cursorInfo.length = 1;
          }
          store.getters.getImEditor.deleteText(sessionInfo.value.id, cursorInfo.index, cursorInfo.length);
          dialogObj.value.type = type;
          dialogObj.value.haitObj.list = [];
          dialogObj.value.haitObj.listMap = {};
          dialogObj.value.haitObj.selMap = {};
          dialogObj.value.haitObj.text = "";
          setTeamMembers().then(() => {
            let defaultList = deepClone(teamMembers.value);
            let list = [];
            let initialMap = {};
            defaultList.map(item => {
              // 按照首字母大写排序
              let initial = (item.detailInfo?.workerSpell || "").slice(0, 1).toUpperCase();
              item.initial = initial + "1";
              if (!initialMap[initial]) {
                initialMap[initial] = true;
                list.push({initial: initial});
              }
              list.push(item);
            });
            list = list.sort((a, b) => {return a.initial.localeCompare(b.initial)});
            dialogObj.value.haitObj.list = list;
            dialogObj.value.haitObj.showList = list;
            dialogObj.value.haitObj.initialList = Object.keys(initialMap).sort((a, b) => {return a.localeCompare(b)});
          });
          break;
      }
    }

    // 点击图片
    function clickImage(e) {
      selElm(e.target);
    }

    // 搜索ai应用
    function doSearchAi(keyword) {
      if (aiObj.value.defaultAppList.length == 0) {
        aiSearchObj.value.flag = true;
        callAiLoading.value = true;
      }
      debounce({
        timerName: "doSearchAi",
        time: 300,
        fnName: async function () {
          if (keyword) {
            callAiLoading.value = true;
            aiSearchObj.value.flag = true;
            aiObj.value.showAppList = [];
          }
          let param = {
            pageNum: 1,
            pageSize: keyword ? 50 : 9,
            empNumber: userInfo.workerId,
            keyword: keyword
          }
          if (!keyword) {
            param.commonUse = 1;
          }
          let res = await getAppListApi(param);
          aiSearchObj.value.flag = false;
          callAiLoading.value = false;
          if (res?.data?.records) {
            let aiObjParam = {};
            let recordList = res.data.records.filter(item => {return item.isDefault != 1});
            if (!keyword) {
              aiObjParam.defaultAppList = deepClone(recordList);
            }
            aiObjParam.showAppList = deepClone(recordList);
            callAiList.value = deepClone(recordList);
            // 查找智问智答应用判断知识库更新
            let aiItem = aiObjParam.showAppList.find(item => {return item.id == 1});
            if (aiObj.value.currentApp.id == 1 && aiItem?.id) {
              // 更新智问智答数据库和显示
              let currentApp = deepClone(aiObj.value.currentApp);
              aiObjParam.currentApp = currentApp;
            }
            remote.store.commit("setAiObj", aiObjParam);
          }
        }
      });
    }

    // 设置应用高度
    function setAiAppHeight() {
      let num = aiObj.value.defaultAppList.length || 3;
      if (num < 3) {
        num = 3;
      } else if (num > 9) {
        num = 9;
      }
      return num * 26;
    }

    // 新话题
    async function newTopics(flag, notTip, item) {
      if (aiSearchObj.value.refreshTimer) {
        if (!flag) {
          toast({title: "请勿频繁操作", type: 2});
        }
        return;
      }
      aiSearchObj.value.refreshTimer = setTimeout(() => {
        aiSearchObj.value.refreshTimer = "";
      }, 1000);
      if (aiObj.value.refresh) {
        return;
      }
      // 更新账号信息
      remote.store.dispatch("updatePersons", {account: sessionInfo.value.to});
      remote.store.commit("setAiObj", {refresh: true});
      let currentApp = item || aiObj.value.currentApp;
      let res = await newTopicsApi({
        aiAccid: sessionInfo.value.to,
        appId: sessionInfo.value.to == aiObj.value.workerNo ? currentApp.id : sessionInfo.value.detailInfo.appTypeId
      });
      remote.store.commit("setAiObj", {refresh: false});
      if (sessionInfo.value.to == aiObj.value.workerNo) {
        // 智能助理调用切换当前应用
        if (res?.success && res?.data) {
          // 重新对话重新赋值应用信息
          updateAiUserInfo(currentApp, res.data);
        }
        remote.store.commit("setAiObj", {notSwitchReq: true, notTip: notTip, switchApp: true, currentApp: currentApp, sendTo: sessionInfo.value.to});
      } else {
        // 数字人发送消息
        if (sessionInfo.value?.detailInfo?.businessType == "AI_PL") {
          // ai陪练重新发送请求
          let param = {tip: "新建话题", sendTo: sessionInfo.value.to};
          remote.store.commit("sendAiMsgCard", param);
          store.commit("setAiTempIdMap", {key: sessionInfo.value.id, value: {id: "", flag: true}});
          aiSparringApi({stage: 1, fromAccid: sessionInfo.value.to});
        } else {
          if (res?.success && res?.data) {
            // 重新对话重新赋值应用信息和保存本地缓存
            aiObj.value.currentApp = res.data;
            // 初始化更新本地数据库
            if (sessionInfo.value?.detailInfo) {
              updateAiUserInfo(sessionInfo.value.detailInfo, res.data);
            }
          }
          let param = {tip: "新建话题", sendTo: sessionInfo.value.to};
          let prologue = sessionInfo.value?.detailInfo?.prologue;
          if (prologue) {
            param.content = {type: "ai-msg", msgs: [{type: "text", text: prologue}]};
          }
          remote.store.commit("sendAiMsgCard", param);
        }
      }
      nextTick(() => {
        setAutoScroll("bottom");
      });
    }

    // 更新ai信息
    function updateAiUserInfo(obj, updateObj) {
      if (sessionInfo.value.to == aiObj.value.workerNo) {
        // 个人助理更新当前应用信息
      } else {
        // 其他应用更新名字头像
        obj.name = updateObj.name;
        obj.headImg = updateObj.headImg;
      }
      obj.briefIntr = updateObj.briefIntr;
      obj.prologue = updateObj.prologue;
      obj.diyJson = updateObj.diyJson;
      obj.isFree = updateObj.isFree;
    }

    // icon加载失败
    function errorIcon(e) {
      e.target.src = "img/index/icon_ai_app.png";
    }

    // 是否是数字人 flag包含智能助理
    function isAi(account, flag) {
      if (flag) {
        return new RegExp(config.ai).test(account);
      } else {
        return new RegExp(config.ai).test(account) && account != aiObj.value.workerNo;
      }
    }

    // 显示悬浮框内容
    function showTipsBox(e, text, pos) {
      store.commit("setEmit", {
        type: "tipsBox", value: {type: 1, e: e, content: text, pos: pos}
      });
    }

    // ai应用弹窗操作 type1显示下拉2下拉选择
    function dialogAiOperate(type, item, item1) {
      switch (type) {
        case 1:
          if (dialogObj.value.aiAppShowSelId == item.id) {
            dialogObj.value.aiAppShowSelId = "";
            return;
          }
          dialogObj.value.aiAppShowSelId = item.id;
          break;
        case 2:
          dialogObj.value.aiAppShowSelId = "";
          item.value = item1;
          break;
      }
    }

    // 设置ai对话提示
    function setAiPlaceholder() {
      // 判断是智能助理会话设置提示文案
      if (sessionInfo.value.id == "p2p-" + aiObj.value.workerNo && store.getters.getImEditor) {
        store.getters.getImEditor.setPlaceholder(sessionInfo.value.id, "直接跟我对话，或输入#唤起不同应用");
      }
    }

    // 清除历史记录
    function clearMessage() {
      alert({
        content: "清理完成后，该记录无法再找回，是否确认清理？",
        done: async (type) => {
          if (type == 1) {
            let scene = sessionInfo.value.scene;
            let to = sessionInfo.value.to;
            loading();
            let res = await clearMessageApi({
              msgBody: JSON.stringify({
                scene: scene,
                toTarget: to,
              })
            });
            if (!res.success) {
              loading().hide();
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            // 清除云信记录
            let nimRes = await remote.store.getters.getNim.clearServerHistoryMsgsWithSync({
              scene: scene,
              to: to,
            });
            if (nimRes.err) {
              toast({title: nimRes.err.message || "云信错误", type: 2});
              loading().hide();
              return;
            }
            // 发送多端同步消息
            let tipsRes = await remote.store.getters.getNim.sendTipMsg({
              scene: "p2p",
              to: userInfo.workerNo,
              tip: "系统通知消息，更新版本可屏蔽此消息",
              isUnreadable: false,
              custom: JSON.stringify({key: "clearServerHistoryMsgs", value: {scene: scene, id: to, time: Date.now() + store.getters.getDiffTime}, type: "hide"})
            });
            if (tipsRes.err) {
              toast({title: tipsRes.err.message || "云信消息错误", type: 2});
              loading().hide();
              return;
            }
            loading().hide();
            // 清空本地记录
            remote.store.dispatch("setMsgs", {id: scene + "-" + to, type: "clear"});
            recordList.value = [];
            searchMsgObj.value.list = [];
            searchMsgObj.value.total = 0;
            toast({title: res.errorMsg || "操作成功", type: 1});
          }
        }
      });
    }

    // 获取群ai类型
    function setAiToolsType() {
      let aiToolsNo = sessionInfo.value.detailInfo?.serverCustom?.ai1;
      let to = sessionInfo.value.to;
      let type = sessionInfo.value?.scene == "p2p" ? 0 : aiToolsNo ? 2 : userTeamInfo.value?.type && userTeamInfo.value.type != "normal" ? 1 : 0;
      let scrollFlag = isScrollBottom.value;
      if (type == 2) {
        // 存在群助手还没获取到信息
        aiToolsType.value = 0;
        setTeamAiMap();
      } else {
        aiToolsType.value = type;
        // 存在群助手才显示展开收起
        if (aiToolsType.value == 2) {
          toggleTeamToolShowType(1);
        } else {
          toolObj.value.showType = 1;
        }
        // 群助手被踢出后删除本地缓存
        if (!aiToolsNo && store.getters.getState("teamAiMap")[to]) {
          store.dispatch("setTeamAiMap", {id: to, type: "del"});
        }
        teamAiObj.value.aiInfo = {
          datasetId: "",
          ...teamAiLocal
        }
      }
      toggleTeamToolShowType(1);
      // 群状态变更关闭ai管理面板
      if (showTag.value == 8 && userTeamInfo.value.type == "normal") {
        showTag.value = 1;
      }
      nextTick(() => {
        if (scrollFlag) {
          store.commit("setEmit", {type: "scroll", value: "bottom"});
        }
      });
    }

    // 初始化快速行动
    function setQuickActing() {
      if(sessionInfo.value?.scene === "team"){
        // 调用接口。取状态
        quickActing.value.show = false;
        let param = {tid: sessionInfo.value.to};
        quickActingInfo(param).then(({data})=>{
          if (param.tid == sessionInfo.value.to && data == 1) {
            quickActing.value.show = true
            toggleTeamToolShowType(1);
          }
        })
      }else{
        quickActing.value.show = false;
      }
    }

    // 召唤/提出群ai
    async function toggleTeamAi() {
      let to = sessionInfo.value.to;
      if (aiToolsType.value == 1) {
        teamAiObj.value.searchInfo.loading = true;
        teamAiObj.value.searchInfo.loadingText = "正在查询群助手...";
        setTeamAiMap("init");
      } else {
        // 提出群ai
        if (teamAiObj.value.loadingMap[to] || teamAiObj.value.loadingCancelMap[to]) {
          return;
        }
        let teamInfo = store.getters.getTeams({id: to});
        teamAiObj.value.loadingCancelMap[to] = true;
        teamAiObj.value.searchInfo.loading = true;
        teamAiObj.value.searchInfo.loadingText = "正在移除群助手...";
        let res = await comeInAndGoOutGroupApi({
          msgBody: JSON.stringify({
            type: 2,
            teamType: teamInfo.detailType == "group" ? "2" : "1",
            tid: sessionInfo.value.to,
            workerNo: teamAiObj.value.aiInfo.to
          })
        });
        teamAiObj.value.loadingCancelMap[to] = false;
        teamAiObj.value.searchInfo.loading = false;
        teamAiObj.value.searchInfo.loadingText = "";
        if (to == sessionInfo.value.to && showTag.value == 8) {
          toast({title: res?.success ? "操作成功" : res?.errorMsg || "系统异常", type: res?.success ? 1 : 2});
        }
      }
    }

    // 打开快速行动使用说明
    function onOpenQuickActingRename(){
      store.dispatch("setOpenWindow", ["https://i.leyoujia.com/lyjEtherpad/p/3ffded02-82f7-4b6f-bbfb-3d29243d5fb0?docId=21597514&shareNo=428300"]);
    }
    // 打开快速行动弹窗
    function onOpenQuickActing(){
      const res = userLocalStorage({key: "quickActingFormData",value:null}, 2);
      if(res?.length){
        try{
          quickActing.value.oldData = JSON.parse(res);
        }catch (e){
          console.error("onOpenQuickActing error",e)
        }
      }else{
        quickActing.value.oldData = null
      }
      quickActing.value.showDialog = true;
    }

    // 查询群ai助手信息
    async function setTeamAiMap(type) {
      let to = sessionInfo.value.to;
      if (type == "init") {
        // 召唤群助手
        if (teamAiObj.value.loadingMap[to]) {
          return;
        }
        teamAiObj.value.loadingMap[to] = true;
      }
      let teamAiInfo = await store.dispatch("setTeamAiMap", {id: to, type: type});
      teamAiObj.value.loadingMap[to] = false;
      teamAiObj.value.searchInfo.loading = false;
      teamAiObj.value.searchInfo.loadingText = "";
      if (!teamAiInfo || to != sessionInfo.value.to) {
        return;
      }
      teamAiObj.value.aiInfo = deepClone(teamAiInfo);
      aiToolsType.value = 2;
      toggleTeamToolShowType(1);
      if (type == "init" && showTag.value == 8) {
        toast({title: "召唤成功", type: 1});
        toggleTeamToolShowType(2, 0);
      }
      nextTick(() => {
        if (isScrollBottom.value) {
          store.commit("setEmit", {type: "scroll", value: "bottom"});
        }
      });
    }

    // 重置搜索
    function resetKnowledgeList() {
      teamAiObj.value.searchInfo.key = "";
      searchKnowledgeList(true);
    }

    // 删除知识
    function removeKnowledgeList(item, index) {
      alert({
        content: "是否确认删除该知识库文件？",
        done: async type => {
          if (type == 1) {
            teamAiObj.value.searchInfo.loading = true;
            let res = await removeDatasetDocumentByIdApi({
              datasetId: teamAiObj.value.aiInfo.datasetId,
              docId: item.id
            });
            teamAiObj.value.searchInfo.loading = false;
            if (!res?.success) {
              toast({title: res?.errorMsg || "系统错误"});
              return;
            }
            toast({title: "文件删除成功"});
            teamAiObj.value.searchInfo.list.splice(index, 1);
            if (!teamAiObj.value.searchInfo.key) {
              teamAiObj.value.searchInfo.total = teamAiObj.value.searchInfo.list.length;
            }
            scrollKnowledge(chatContentRef.value.querySelector(".ai-manager-box .ai-manager-knowledge-list-ul"));
            searchKnowledgeList(false, true, true);
          }
        }
      })
    }

    // 显示群知识列表
    function showTeamAiKnowledge() {
      if (aiToolsType.value != 2) {
        return;
      }
      toolObj.value.showType = 2;
      teamAiObj.value.searchInfo.key = "";
      teamAiObj.value.searchInfo.total = 0;
      searchKnowledgeList(true);
    }
    // 切换开关群快速行动功能
    function onEnableQuickActing(){
      let to = sessionInfo.value.to
      quickActing.value.show = !quickActing.value.show
      toggleTeamToolShowType(1);
      quickActingUpStatus({tid:to,status: quickActing.value.show?1:0}).then((res)=>{
        if (to != sessionInfo.value.to) {
          return;
        }
        if (!res?.success) {
          quickActing.value.show = !quickActing.value.show;
        }
        toggleTeamToolShowType(2, 0);
      });
    }

    // 搜索知识库列表
    function searchKnowledgeList(resetFlag, noLoading, updateTime) {
      // 重置条件
      if (resetFlag) {
        teamAiObj.value.searchInfo.list = [];
        teamAiObj.value.searchInfo.page = 1;
        teamAiObj.value.searchInfo.hasMore = true;
      }
      if (!noLoading) {
        teamAiObj.value.searchInfo.loading = true;
        // 重置加载只是列表回到顶部
        if (teamAiObj.value.searchInfo.page == 1) {
          let listElm = chatContentRef.value.querySelector(".ai-manager-box .ai-manager-knowledge-list-ul");
          if (listElm) {
            listElm.scrollTop = 0;
          }
        }
      }
      debounce({
        timerName: "searchKnowledgeList",
        time: 300,
        fnName: function () {
          searchDatasetDocumentList(updateTime);
        }
      });
    }

    // 滚动群知识库列表加载更多
    function scrollKnowledge(e) {
      let target = e.target || e;
      if (target.scrollHeight - target.scrollTop - target.clientHeight < 10) {
        if (teamAiObj.value.searchInfo.hasMore && !teamAiObj.value.searchInfo.loading) {
          teamAiObj.value.searchInfo.page++;
          searchDatasetDocumentList();
        }
      }
    }

    // 搜索知识库列表
    async function searchDatasetDocumentList(updateTime) {
      let key = teamAiObj.value.searchInfo.key;
      let datasetId = teamAiObj.value.aiInfo.datasetId;
      let param = {
        page: teamAiObj.value.searchInfo.page,
        limit: teamAiObj.value.searchInfo.limit,
        datasetId: datasetId,
        keyword: key,
      };
      // 定时更新列表状态需要获取当前所有列表数据
      if (updateTime) {
        param.page = 1;
        param.limit = teamAiObj.value.searchInfo.page * teamAiObj.value.searchInfo.limit
      }
      let res = await searchDatasetDocumentListApi(param);
      // 切换会话不渲染
      if (datasetId != teamAiObj.value.aiInfo.datasetId || key != teamAiObj.value.searchInfo.key) {
        return;
      }
      if (updateTime) {
        teamAiObj.value.searchInfo.studyingTime = Date.now() + store.getters.getDiffTime;
      }
      teamAiObj.value.searchInfo.loading = false;
      if (res?.success) {
        if (res.data?.data) {
          // 判断是否存在学习中的知识
          teamAiObj.value.searchInfo.studying = false;
          res.data.data.map(item => {
            item.itemStatus = item.indexing_status == "completed" ? 1 : item.indexing_status == "error" || item.indexing_status == "paused" ? 2 : 3;
            if (item.itemStatus == 3) {
              teamAiObj.value.searchInfo.studying = true;
            }
            item.ext = getFileExt(item.name);
            if (item.ext == "docx" && item.data_source_detail_dict?.upload_file?.extension == "md") {
              item.ext = "document";
            }
          });
          if (!updateTime) {
            teamAiObj.value.searchInfo.list = teamAiObj.value.searchInfo.list.concat(res.data.data);
          } else {
            teamAiObj.value.searchInfo.list = res.data.data;
          }
          // 判断是否还有更多
          if (res.data.total <= teamAiObj.value.searchInfo.list.length) {
            teamAiObj.value.searchInfo.hasMore = false;
          }
        }
      }
      // 设置总知识库列表数
      if (!key && (teamAiObj.value.searchInfo.total == 0 || res?.data?.total != null)) {
        teamAiObj.value.searchInfo.total = res?.data?.total || 0;
      }
    }

    // 重置搜索群文件
    function resetTeamFile() {
      dialogObj.value.teamFileObj.key = "";
      searchTeamFile(true);
    }

    // 搜索群文件
    function searchTeamFile(resetFlag) {
      if (resetFlag) {
        dialogObj.value.teamFileObj.page = 1;
        dialogObj.value.teamFileObj.list = [];
        dialogObj.value.teamFileObj.hasMore = true;
        dialogObj.value.teamFileObj.selMapSize = 0;
      }
      dialogObj.value.teamFileObj.loading = true;
      debounce({
        timerName: "searchTeamFile",
        time: 300,
        fnName: async function () {
          if (dialogObj.value.teamFileObj.type == 1) {
            getTeamFileList(sessionInfo.value.to);
          } else {
            getDocList(sessionInfo.value.to);
          }
        }
      });
    }

    // 滚动群文件列表加载更多
    function scrollTeamFile(e) {
      if (e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight < 10) {
        if (dialogObj.value.teamFileObj.hasMore && !dialogObj.value.teamFileObj.loading) {
          dialogObj.value.teamFileObj.page++;
          searchTeamFile();
        }
      }
    }

    // 获取群文件
    async function getTeamFileList(teamId, preList) {
      let key = dialogObj.value.teamFileObj.key;
      let res = await queryCloudMessageApi({
        msgBody: JSON.stringify({
          keyWord: key,
          toTarget: teamId,
          msgType: "FILE,DOCUMENT",
          page: dialogObj.value.teamFileObj.page,
          rows: dialogObj.value.teamFileObj.pageSize,
          joinTime: userTeamInfo.value.joinTime
        })
      });
      // 非当前群显示状态不处理
      if (sessionInfo.value.to != teamId || dialogObj.value.type != 5 || key != dialogObj.value.teamFileObj.key) {
        return;
      }
      if (!res?.success) {
        dialogObj.value.teamFileObj.loading = false;
        toast({title: res?.errorMsg || "系统错误", type: 2});
        return;
      }
      let dataList = res.data?.data || [];
      let defaultLength = dataList.length || 0;
      // 没有更多了
      if (res.data?.data && (res.data.data.length == 0 || dialogObj.value.teamFileObj.page >= res.data.pages)) {
        dialogObj.value.teamFileObj.hasMore = false;
      }
      let list = [];
      let userList = [];
      // 遍历符合条件的文件
      for (let i = 0; i < dataList.length; i++) {
        let item = dataList[i];
        // 10M内的文件或者乐文档
        if ((config.teamAiFileReg.test(item.msgJson?.attach?.ext) && item.msgJson.msgType == "FILE" && (item.msgJson?.attach?.size || 0) / 1024 / 1024 < 10) ||
          (/.docx$/.test(item.msgJson?.attach?.name) && item.msgJson.msgType == "DOCUMENT")) {
          let listItem = item.msgJson;
          if (listItem.msgType == "DOCUMENT") {
            item.msgJson.attach.ext = "document";
          }
          listItem.userInfo = getPerson(listItem.fromAccount);
          // 本地不存在数据的用户请求接口
          if (!listItem.userInfo.workerId) {
            userList.push(listItem.fromAccount);
          }
          list.push(listItem);
        }
      }
      let personInfo = await store.dispatch("getPersons", {doneFlag: true, account: userList});
      if (Object.keys(personInfo).length > 0) {
        // 请求到数据重新赋值
        list.map(item => {
          if (personInfo[item.fromAccount]) {
            item.userInfo = personInfo[item.fromAccount];
          }
        })
      }
      if (preList) {
        list = list.concat(preList);
      }
      // 最少渲染10个,还有更多数据继续请求
      if (defaultLength > 0 && list.length < 8 && dialogObj.value.teamFileObj.hasMore) {
        dialogObj.value.teamFileObj.page++;
        getTeamFileList(teamId, list);
        return;
      }
      dialogObj.value.teamFileObj.loading = false;
      dialogObj.value.teamFileObj.list = dialogObj.value.teamFileObj.list.concat(list);
      // 重置加载列表回到顶部
      if (dialogObj.value.teamFileObj.page == 1) {
        nextTick(() => {
          teamFileRef.value.querySelector(".team-file-list-scroll").scrollTop = 0;
        });
      }
    }

    // 选择群文件
    function selTeamFile(item) {
      if (dialogObj.value.teamFileObj.type == 1) {
        // 选择群文件
        let itemSize = (item.attach.size || 0) / 1024 / 1024;
        if (dialogObj.value.teamFileObj.selMap[item.msgidServer]) {
          delete dialogObj.value.teamFileObj.selMap[item.msgidServer];
          itemSize = -itemSize;
        } else {
          if (dialogObj.value.teamFileObj.selMapLength >= 5 || dialogObj.value.teamFileObj.selMapSize + itemSize > 20) {
            toast({title: "每次仅允许上传5个，总大小不得超过20M"});
            return;
          }
          dialogObj.value.teamFileObj.selMap[item.msgidServer] = item;
        }
        // 计算文件大小
        dialogObj.value.teamFileObj.selMapSize += itemSize;
      } else {
        // 选择乐文档
        if (dialogObj.value.teamFileObj.selMap[item.msgidServer]) {
          delete dialogObj.value.teamFileObj.selMap[item.msgidServer];
        } else {
          if (dialogObj.value.teamFileObj.selMapLength >= 5) {
            toast({title: "每次仅允许上传5个"});
            return;
          }
          dialogObj.value.teamFileObj.selMap[item.msgidServer] = item;
        }
      }
      // 计算文件数量
      dialogObj.value.teamFileObj.selMapLength = Object.keys(dialogObj.value.teamFileObj.selMap).length;
    }

    // 获取乐文档
    function getDocList(teamId) {
      // 获取文档信息
      debounce({
        timerName: "getDocList",
        time: 300,
        fnName: async () => {
          let key = dialogObj.value.teamFileObj.key;
          let res = await searchDocApi({title: key, rows: 50});
          // 非当前群显示状态不处理
          if (sessionInfo.value.to != teamId || key != dialogObj.value.teamFileObj.key) {
            return;
          }
          if (!res?.success) {
            dialogObj.value.teamFileObj.loading = false;
            toast({title: res.errorMsg || "系统错误", type: 2});
            return;
          }
          dialogObj.value.teamFileObj.loading = false;
          dialogObj.value.teamFileObj.hasMore = false;
          let list = [];
          let dataList = res?.data?.list || [];
          for (let i = 0; i < dataList.length; i++) {
            let item = dataList[i];
            list.push({
              msgidServer: item.id,
              msgType: "DOCUMENT",
              userInfo: {
                name: item.empName + (item.deptName ? `(${item.deptName})` : "")
              },
              attach: {
                name: item.title,
                docId: item.id,
                ext: "document",
              }
            })
          }
          dialogObj.value.teamFileObj.list = dialogObj.value.teamFileObj.list.concat(list);
          // 重置加载列表回到顶部
          if (dialogObj.value.teamFileObj.page == 1) {
            nextTick(() => {
              teamFileRef.value.querySelector(".team-file-list-scroll").scrollTop = 0;
            });
          }
        }
      });
    }

    // @群ai
    function haitTeamAi(item) {
      let haitName = `@${item.name} `;
      store.getters.getImEditor.insertHaitImage(item.to, haitName, `${strToImg(haitName)}`, sessionInfo.value.id);
    }

    // 显示添加群只是方式 1移入2移出3延迟移除
    function showAddKnowledge(type) {
      if (type == 1) {
        // 移入存在延迟则清除延迟
        if (teamAiObj.value.showAddTimer) {
          clearTimeout(teamAiObj.value.showAddTimer);
        }
        teamAiObj.value.showAdd = true;
      } else if (type == 2) {
        // 移除存在延迟则清除延迟
        if (teamAiObj.value.showAddTimer) {
          clearTimeout(teamAiObj.value.showAddTimer);
        }
        teamAiObj.value.showAdd = false;
      } else if (type == 3) {
        teamAiObj.value.showAddTimer = debounce({
          timerName: "showAddKnowledge",
          time: 100,
          fnName: function () {
            teamAiObj.value.showAdd = false;
          }
        })
      }
    }

    // 添加知识库
    function addKnowledgeType(type) {
      if (type == 1 || type == 3) {
        // 添加群文件
        showDialog(5, type == 1 ? 1 : 2);
        // 初始化数据
        dialogObj.value.teamFileObj.key = "";
        dialogObj.value.teamFileObj.selMap = {};
        dialogObj.value.teamFileObj.selMapLength = 0;
        searchTeamFile(true);
      } else if (type == 2) {
        // 上传本地文件
        store.commit("setEmit", {
          type: "fileInput", value: {
            multiple: true,
            accept: ".pdf,.docx,.xlsx",
            done: files => {
              setUploadKnowledge(files);
            }
          }
        });
      }
      showAddKnowledge(2);
    }

    // 拖拽上传群文件
    function dropKnowledge(e) {
      if (e.dataTransfer?.files?.length > 0) {
        setUploadKnowledge(e.dataTransfer.files);
      }
    }

    // 设置上传群知识库文件
    function setUploadKnowledge(files) {
      let tips = "";
      let showTips = "每次仅允许上传5个，总大小不得超过20M，且单个文件不得超过10M";
      let fileSize = 0;
      if (files.length > 5) {
        tips = showTips;
      } else {
        for (let i = 0; i < files.length; i++) {
          let file = files[i];
          fileSize += (file.size || 0) / 1024 / 1024;
          // 文件格式限制
          if (!config.teamAiFileReg.test(getFileExt(file.name))) {
            tips = teamAiObj.value.fileTips;
            break;
          }
          // 单文件限制
          if (file.size / 1024 / 1024 > 10) {
            tips = showTips;
            break;
          }
        }
        // 文件总大小限制
        if (fileSize > 20) {
          tips = showTips;
        }
      }
      if (tips) {
        alert({
          content: tips,
          showCancel: false
        });
        return;
      }
      uploadKnowledge(files);
    }

    // 上传群知识库
    async function uploadKnowledge(selList) {
      let to = sessionInfo.value.to;
      let formData = new FormData();
      for (let i = 0; i < selList.length; i++) {
        let selItem = selList[i];
        let selType = selItem instanceof File ? 1 : (selItem.msgType == "DOCUMENT" ? 3 : 2);
        formData.append(`difyUpdateDocumentForm[${i}].index`, i);
        formData.append(`difyUpdateDocumentForm[${i}].datasetId`, teamAiObj.value.aiInfo.datasetId);
        formData.append(`difyUpdateDocumentForm[${i}].type`, selType);
        if (selType == 1) {
          formData.append(`difyUpdateDocumentForm[${i}].file`, selItem);
        } else if (selType == 2) {
          formData.append(`difyUpdateDocumentForm[${i}].imFileObj`, JSON.stringify(selItem.attach));
        } else {
          formData.append(`difyUpdateDocumentForm[${i}].docId`, selItem.attach.docId);
        }
      }
      toast({title: "文件已提交，即将上传"});
      teamAiObj.value.searchInfo.updateLoading = true;
      let res = await updateDocumentApi(formData);
      if (res.success) {
        let errorNum = 0;
        // 获取上传失败数量
        for (let i = 0; i < res.data?.length; i++) {
          if (!res.data[i].success) {
            errorNum++;
          }
        }
        // 不在当前会话ai管理页面不提示
        if (errorNum && to == sessionInfo.value.to && showTag.value == 8) {
          toast({title: `您上传的${errorNum}个文件失败，请重试`});
        }
      }
      teamAiObj.value.searchInfo.updateLoading = false;
      searchKnowledgeList(false, true, true);
    }

    // 编辑群ai简介
    function teamAiIntrEdit() {
      dialogObj.value.teamAiIntrObj.text = teamAiObj.value.aiInfo.prologue;
      showDialog(6);
    }

    // 切换群ai显示状态 type:1获取2切换设置 value设置的值
    function toggleTeamToolShowType(type, value) {
      if (type == 1) {
        teamToolShowType.value = userLocalStorage({key: "teamToolShowType", value: 1}, 2);
        if (!(quickActing.value.show || aiToolsType.value == 2)) {
          teamToolShowType.value = 0;
        }
      } else if (type == 2) {
        if (value != null) {
          if (value === 0) {
            // 关闭需要判断是否所有工具都关闭了
            if (quickActing.value.show || aiToolsType.value == 2) {
              teamToolShowType.value = 1;
            } else {
              teamToolShowType.value = value;
            }
          } else {
            teamToolShowType.value = value;
          }
        } else {
          teamToolShowType.value = teamToolShowType.value == 1 ? 2 : 1;
          userLocalStorage({key: "teamToolShowType", value: teamToolShowType.value}, 1);
        }
      }
      if (sessionInfo.value.scene == "p2p") {
        teamToolShowType.value = 0;
      }
      nextTick(() => {
        if (isScrollBottom.value) {
          store.commit("setEmit", {type: "scroll", value: "bottom"});
        }
      });
    }

    // 创建快速行动
    // type 1:完成并且打开发现弹窗 2:完成并且发到此群
    async function quickActingConfirm(type){
      const status = await quickActingRef.value?.submit(type)
      if(status) quickActing.value.showDialog = false;
      userLocalStorage({key: "quickActingFormData",value:null}, 1);
    }

    // 关闭快速行动弹窗
    // type 1:完成并且打开发现弹窗 2:完成并且发到此群
    async function closeQuickActingDialog(){
      userLocalStorage({key: "quickActingFormData",value:JSON.stringify({...quickActingRef.value.formData,selectEmpOrGroup:quickActingRef.value.selectEmpOrGroup})}, 1);
      quickActing.value.showDialog=false;
    }


    // 阻止点击穿透
    function stopPropagation() {}

    // 多选@人员搜索
    function searchMoreHait(reset) {
      if (reset) {
        dialogObj.value.haitObj.text = "";
      }
      if (dialogObj.value.haitObj.text) {
        let valReg = new RegExp(regReplace(dialogObj.value.haitObj.text), "i");
        dialogObj.value.haitObj.showList = dialogObj.value.haitObj.list.filter(item => {
          return valReg.test(item?.account || "") || valReg.test(item.detailInfo?.name || "") || valReg.test(item.detailInfo?.workerSpell || "")
        });
      } else {
        dialogObj.value.haitObj.showList = deepClone(dialogObj.value.haitObj.list);
      }
    }

    // 多选@选择人员
    function selMoreHait(item) {
      if (!item.account) {
        return;
      }
      if (dialogObj.value.haitObj.selMap[item.account]) {
        delete dialogObj.value.haitObj.selMap[item.account];
        return;
      }
      if (Object.keys(dialogObj.value.haitObj.selMap).length >= 20) {
        toast({title: "最多选择20个人员"});
        return;
      }
      dialogObj.value.haitObj.selMap[item.account] = item;
    }

    // 多选@滚动到的对应首字母
    function haitScrollToInitial(item) {
      let initialElm = dialogHaitRef.value.querySelector(`.sel-initial[data-key="${item}"]`);
      let scrollBox = getParents(initialElm, "sel-user-ul");
      let scrollBoxFirstElm = scrollBox.firstElementChild;
      if (!getBounding(scrollBoxFirstElm).height) {
        scrollBoxFirstElm = scrollBoxFirstElm.nextElementSibling;
      }
      if (initialElm && scrollBox && scrollBoxFirstElm) {
        scrollBox.scrollTop = initialElm.getBoundingClientRect().top - scrollBoxFirstElm.getBoundingClientRect().top;
      }
    }

    return {
      noticeUnread,
      chatNotice,
      toNotice,
      showFlag,
      settings,
      showTag,
      showTagClick,
      userInfo,
      editorRef,
      teamQrcodeRef,
      teamEditorRef,
      avatarError,
      msgBoxRef,
      msgRef,
      selElm,
      isScrollBottom,
      showToolType,
      haitMsgList,
      concernMsgList,
      quoteMsg,
      haitInfo,
      teamMembers,
      userTeamInfo,
      aiObj,
      aiSearchObj,
      teamMembersLoading,
      teamMembersError,
      teamMembersPage,
      memberPageFlag,
      teamMembersTotal,
      teamMembersPageList,
      haitMemberList,
      memberLoading,
      haitDocList,
      docLoading,
      callAppList,
      callAppLoading,
      callAiList,
      callAiLoading,
      haitSelRef,
      callSelRef,
      haitMemberRef,
      haitDocRef,
      callAppRef,
      callAiRef,
      haitActive,
      showHaitFlag,
      showHaitMsgFlag,
      showConcernFlag,
      showConcernMsgFlag,
      getPrimaryMsg,
      memberSearchFlag,
      aiToolsType,
      teamToolShowType,
      lastMsgTipsFlag,
      searchTeamMemberObj,
      searchMsgObj,
      chatContentRef,
      recordList,
      recordMsgRef,
      searchMsgRef,
      calendarRef,
      selSearchDate,
      calendarFlag,
      dateFormat,
      isInputObj,
      inputName,
      teamNameRef,
      teamQrcodeObj,
      editorResizeObj,
      quoteDetailRef,
      quoteDetailObj,
      isMultiple,
      docOpRef,
      docOpObj,
      emojiRef,
      emojiObj,
      quickReplyList,
      isFcwList,
      msgList,
      fcwObj,
      sessionInfo,
      showEditorFlag,
      getHighlight,
      htmlEscapeAll,
      dialogObj,
      dialogHaitRef,
      lyDialogInputRef,
      forwardReportRef,
      teamFileRef,
      purMap,
      multiBugItem,
      toViewerMethod,
      clickImage,
      getFileIcon,
      dealMem,
      toolObj,
      teamAiObj,
      sendMessageFlag,

      stopClick,
      isBlacklist,
      removeBlacklist,
      isSubOrSer,
      msgScroll,
      isSessionList,
      isSubOrHelper,
      backList,
      showToolSel,
      selEmoji,
      showEmojiDone,
      selFile,
      closeChat,
      sendMsg,
      delQuoteMsg,
      selHaitType,
      selHaitInfo,
      confirmHaitInfo,
      activeEditor,
      toggleHaitActive,
      showFloat,
      toggleFloatMsg,
      removeFloatMsg,
      getPerson,
      scrollMsg,
      showUserInfo,
      toggleMemberPageFlag,
      changeMemberPage,
      openChat,
      toggleMemberSearchFlag,
      muteTeam,
      removeMember,
      leaveOrDismissTeam,
      setMenu,
      setAutoScroll,
      searchTeamMember,
      confirmSearchMember,
      resetSearchTeamMember,
      selSearchMember,
      showCurr,
      hideSearchMember,
      modifySettings,
      modifyAiSettings,
      openJt,
      windowShake,
      showRecord,
      getMsgHistory,
      queryCloudMessage,
      toggleSearchMsg,
      backSearchMsg,
      setCurrentDay,
      showCalendar,
      changeInputName,
      showTeamQrcode,
      editorResize,
      hideElm,
      showQuoteDetail,
      showDocOp,
      changeDocPur,
      changeMultiple,
      multipleSel,
      forwardQrcode,
      doEditor,
      isShowBack,
      showLyDialog,
      userWearPic,
      toggleQuickList,
      addQuickReplyContent,
      showComponents,
      isDisabledGroup,
      openLink,
      openInvite,
      changeTeamType,
      joinTeam,
      dialogOperate,
      showDialog,
      doSearchAi,
      newTopics,
      errorIcon,
      isAi,
      showTipsBox,
      dialogAiOperate,
      clearMessage,
      toggleTeamAi,
      setTeamAiMap,
      selTeamFile,
      searchTeamFile,
      resetTeamFile,
      scrollTeamFile,
      haitTeamAi,
      resetKnowledgeList,
      removeKnowledgeList,
      showTeamAiKnowledge,
      searchKnowledgeList,
      scrollKnowledge,
      showAddKnowledge,
      addKnowledgeType,
      dropKnowledge,
      teamAiIntrEdit,
      toggleTeamToolShowType,
      stopPropagation,
      quickActing,
      quickActingRef,
      onEnableQuickActing,
      quickActingConfirm,
      closeQuickActingDialog,
      onOpenQuickActingRename,
      onOpenQuickActing,
      searchMoreHait,
      selMoreHait,
      haitScrollToInitial,
    }
  }
}
</script>
<style scoped lang="scss">
.chat-content {
  width: 100%;
  height: 100%;
  background: $styleBg1Hover;

  .icon-member {
    background-image: url("/img/index/member/icon_member.png");
    background-repeat: no-repeat;
    background-size: 160px 16px;
  }

  .icon-editor {
    background-image: url("/img/index/icon_editor.png");
    background-repeat: no-repeat;
    background-size: 360px 20px;
  }

  .icon-refresh {
    width: 20px;
    height: 20px;
    background-image: url("/img/index/icon_refresh.png");
    background-repeat: no-repeat;
    background-size: 60px 20px;

    &.icon-refresh-loading {
      background-position: 0 0 !important;
      animation: myLoading 300ms linear infinite;
    }
  }

  .input-team-name {
    padding-left: 6px;
    border-radius: 4px;
    border: 1px solid #333333;
  }

  .chat-detail {
    width: 100%;
    height: 100%;

    .chat-header {
      width: 100%;
      height: 45px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      padding: 0 10px 0 16px;
      border-bottom: 1px solid rgba(216, 216, 216, .4);
      background: #f5f5f5;

      .session-info {
        height: 100%;
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;

        .info-box {
          display: flex;
          justify-items: center;
          flex-direction: column;
          width: 100%;

          .back {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            background: url('/img/back.png') left center no-repeat;
            background-size: 16px 16px;
            cursor: pointer;
          }

          .info {
            display: flex;
            align-items: center;

            .avatar {
              width: 28px;
              height: 28px;
              margin-right: 7px;
            }

            .name {
              font-size: 16px;
              line-height: 22px;
              cursor: pointer;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              color: #333333;
              font-weight: bold;

              &.resign {
                color: #F74B32;
              }
            }

            .user-label-level {
              background: #FFEBE9;
              color: $styleColor;
              margin-left: 10px;
              font-weight: bold;
              padding: 0 4px;
              height: 16px;
              border-radius: 1px;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .user-label-box {
              position: relative;
              display: flex;
              align-items: center;
              margin-left: 10px;

              .user-label-icon {
                max-width: 46px;
                height: 12px;
              }

              .user-label-text {
                min-width: 46px;
                max-width: 106px;
                height: 14px;
                line-height: 14px;
                padding: 0 3px;
                font-size: 10px;
                color: #834107;
                text-align: center;
                background: linear-gradient(180deg, #FDE5C4 0%, #F4D8B3 100%);
                border-radius: 1px;
              }
            }

            .user-label-box-ai {
              height: 16px;
              font-size: 11px;
              color: $styleColor;
              background: #FFF0F1;
              padding: 0 5px;
              margin-left: 8px;
            }

            .team-type-box {
              position: relative;
              display: flex;
              align-items: center;
              flex-shrink: 0;
              height: 18px;
              line-height: 16px;
              font-size: 11px;
              color: $styleColor;
              border: 1px solid #FFA4A7;
              background: #FFF5F5;
              margin-left: 10px;
              padding: 0 13px 0 3px;
              border-radius: 2px;
              cursor: pointer;

              &.status1 {
                background: $styleBg1;
                border: 1px solid #DEDEDE;
                color: #666666;

                &:before {
                  border-color: transparent transparent transparent $styleBg1;
                }

                &:after {
                  border-color: transparent transparent transparent #666666;
                }
              }

              &:before {
                content: "";
                position: absolute;
                top: 50%;
                right: 5px;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 0 4px 4px;
                border-style: solid;
                border-color: transparent transparent transparent #FFF5F5;
                z-index: 1;
              }

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 4px;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 0 4px 4px;
                border-style: solid;
                border-color: transparent transparent transparent $styleColor;
              }
            }
          }

          .intr {
            max-width: 100%;
            color: #999999;
            display: inline-flex;
            font-size: 11px;
            margin-top: 1px;

            .intr-text {
              max-width: 100%;
            }
          }

          .fcw-zs-label {
            position: relative;
            flex-shrink: 0;
            width: 30px;
            height: 14px;
            line-height: 14px;
            font-size: 10px;
            color: #FFFFFF;
            text-align: center;
            background: linear-gradient(135deg, #FF816C 0%, #EC382A 100%);
            border-radius: 1px;
            margin-left: 10px;

            &:hover:before {
              content: "";
              position: absolute;
              top: 18px;
              left: 50%;
              transform: translateX(-50%);
              width: 0;
              height: 0;
              border-width: 0 10px 10px 10px;
              border-style: solid;
              border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
            }

            &:hover:after {
              content: attr(content);
              position: absolute;
              top: 28px;
              left: -36px;
              line-height: 18px;
              padding: 10px;
              color: #FFFFFF;
              background: #000000;
              white-space: nowrap;
              border-radius: 4px;
              font-size: 12px;
              opacity: 0.8;
              z-index: 1;
            }
          }

          .fcw-link {
            flex-shrink: 0;
            cursor: pointer;
            color: $styleColor;
            margin-left: 10px;
          }
        }
      }

      .tab-type {
        height: 100%;
        display: flex;
        align-items: flex-end;
        flex-shrink: 0;

        .gpt-tips {
          height: 100%;
          padding-right: 6px;
          display: flex;
          align-items: center;
          color: #333333;

          .icon-question {
            width: 16px;
            height: 16px;
            background-image: url("/img/workbench/icon_helper.png");
            background-repeat: no-repeat;
            background-size: 32px 16px;
            margin-right: 1px;
            cursor: pointer;

            &:hover {
              background-position: -16px 0;

              &:after {
                content: "开启后进行问答，将为您带来更智能\A的体验，但会产生额外消耗（更贵）";
                top: 18px;
                white-space: pre
              }
            }
          }

          .icon-switch {
            width: 34px;
            height: 18px;
            background-image: url("/img/icon_switch.png");
            background-repeat: no-repeat;
            background-size: 68px 18px;
            margin-left: 8px;
            cursor: pointer;

            &.show {
              background-position: -34px 0;
            }
          }
        }

        .tab-ul {
          display: inline-flex;

          .tab-li {
            color: #999;
            padding: 10px;
            font-size: 13px;
            cursor: pointer;
            white-space: nowrap;
            position: relative;

            &:first-child {
              margin-left: 0;
            }

            &:hover {
              color: $styleColor;
            }

            &.curr {
              color: $styleColor;
              font-weight: bold;

              &:after {
                content: "";
                width: 12px;
                height: 3px;
                background: $styleColor;
                position: absolute;
                left: 50%;
                bottom: 0;
                transform: translateX(-50%);
              }
            }

            &.unread {
              position: relative;

              &::before {
                content: '';
                position: absolute;
                right: 3px;
                top: 8px;
                z-index: 10;
                background-color: $styleColor;
                width: 7px;
                height: 7px;
                border-radius: 50%;
              }
            }
          }
        }
      }
    }

    .chat-body {
      position: relative;
      width: 100%;
      height: calc(100% - 45px);

      .chat-body-li {
        width: 100%;
        height: 100%;

        &.chat-content-box {
          display: flex;

          .chat-content-main {
            position: relative;
            width: 100%;

            &.resize {
              .quote-msg-box {
                max-width: 45%;
              }
            }

            &.resize-4 {
              width: calc(100% - 216px);
            }

            &.resize-5,
            &.resize-7,
            &.resize-8 {
              width: 50%;
            }

            .tool-sel-ul {
              position: absolute;
              top: 20px;
              left: -20px;
              width: 200px;
              background: #FFFFFF;
              color: #333333;
              padding: 1px;
              z-index: 2;
              box-shadow: 0px 4px 8px 0px rgba(169, 169, 169, 0.7);
              border: 1px solid #D8D8D8;
              border-radius: 4px;
              overflow: hidden;

              .tool-sel-li {
                display: flex;
                align-items: center;
                position: relative;
                height: 26px;
                line-height: 26px;
                z-index: 1;
                padding: 0 30px 0 10px;

                &:hover {
                  background: $styleBg1Hover;
                }

                &.sel {
                  color: $styleColor;

                  &:after {
                    content: "";
                    position: absolute;
                    top: 7px;
                    right: 10px;
                    width: 8px;
                    height: 4px;
                    border-left: 2px solid $styleColor;
                    border-bottom: 2px solid $styleColor;
                    -webkit-transform: rotate(-45deg);
                    transform: rotate(-45deg);
                  }
                }

                &.show {
                  .arrow-right {
                    width: 16px;
                    height: 16px;

                    &:before,
                    &:after {
                      border-width: 5px 0 5px 5px;
                    }
                  }
                }
              }

              .tool-search {
                height: 32px;
                border-bottom: 1px solid #E0E0E0;
                margin: 0 10px;
                cursor: default;

                input {
                  width: 100%;
                  height: 100%;
                  padding-right: 20px;
                  background-image: url(/img/search/icon_search.png);
                  background-repeat: no-repeat;
                  background-size: 16px 16px;
                  background-position: right center;
                }
              }

              .search-ul {
                height: 100%;

                .tool-loading-li {
                  height: 100%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  cursor: default;

                  .loading-img {
                    width: 32px;
                  }
                }
              }

              .search-list {
                overflow-y: auto;
              }
            }

            .top-tips {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 36px;
              padding: 0 10px 0 16px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              background: #E4F1FF;
              color: #313131;
              z-index: 1;

              .icon-close {
                width: 30px;
                height: 30px;
                background: url("/img/index/tips_close.png") no-repeat center center;
                background-size: 30px 30px;
                cursor: pointer;
              }
            }

            .msg-content-box {
              height: calc(100% - 150px);
              min-height: 8%;
              overflow-y: scroll;
              position: relative;

              .chat-not-more {
                margin: 10px 0;
                text-align: center;

                &.gray {
                  color: #999999;
                }

                .chat-tips {
                  color: #169ada;
                  cursor: pointer;
                }
              }

              .no-msg {
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                background: #F5F5F5;

                > img {
                  margin-top: -60px;
                }

                > p {
                  font-size: 14px;
                  margin-top: 10px;
                  color: #666666;
                }
              }

              &::-webkit-scrollbar {
                width: 10px;
              }
            }

            .msg-bottom-box {
              position: relative;

              &.show-last-msg {
                min-height: 30px;
              }

              .last-msg-tips {
                width: 100%;
                height: 30px;
                line-height: 30px;
                position: absolute;
                bottom: 0;
                background-color: #DCF4FF;
                color: #3E4245;
                padding: 0 20px;
                z-index: 1;

                ::v-deep(.im-emoji) {
                  width: 15px;
                  height: 15px;
                  vertical-align: middle;
                }
              }

              .ai-tools-ul {
                padding: 0 8px;
                height: 44px;
                display: flex;
                align-items: center;

                .ai-tools-li {
                  position: relative;
                  display: flex;
                  align-items: center;
                  margin-right: 10px;
                  border-radius: 4px;
                  border: 1px solid #E0E0E0;
                  line-height: 16px;
                  padding: 5px 8px 5px 28px;
                  cursor: pointer;

                  &.loading {
                    &:after {
                      animation: myLoading 800ms linear infinite;
                      background-image: url("/img/index/icon_refresh.png");
                      background-size: 42px 14px;
                      background-position: -28px 0;
                    }
                  }

                  &.ai-icon:after {
                    content: "";
                    position: absolute;
                    top: 7px;
                    left: 8px;
                    width: 14px;
                    height: 14px;
                    background-image: url("/img/index/icon_msg_tools.png");
                    background-repeat: no-repeat;
                    background-size: 14px 14px;
                  }

                  &.quick-acting-icon:after {
                    content: "";
                    position: absolute;
                    top: 7px;
                    left: 8px;
                    width: 14px;
                    height: 14px;
                    background-image: url("/img/index/icon_quick_acting_mini.png");
                    background-repeat: no-repeat;
                    background-size: 14px 14px;
                  }

                  &:hover {
                    background-color: #DCDCDC;
                  }
                }
              }

              .toggle-show-team-ai {
                display: flex;
                align-items: center;
                position: absolute;
                bottom: 10px;
                right: 16px;
                background: $styleBg1Hover;
                border-radius: 2px;
                border: 1px solid #E0E0E0;
                font-size: 10px;
                color: #666666;
                padding: 3px 6px;
                cursor: pointer;

                &:hover {
                  background: #DCDCDC;

                  .show-arrow {
                    &.arrow-top {
                      &:after {
                        border-color: transparent transparent #DCDCDC transparent;
                      }
                    }

                    &.arrow-bottom {
                      &:after {
                        border-color: #DCDCDC transparent transparent transparent;
                      }
                    }
                  }
                }

                .show-arrow {
                  width: 10px;
                  height: 10px;
                  margin-left: 3px;

                  &.arrow-top {
                    &:before {
                      border-color: transparent transparent #999999 transparent;
                    }

                    &:before,
                    &:after {
                      border-width: 0 4px 4px 4px;
                    }

                    &:after {
                      border-color: transparent transparent $styleBg1Hover transparent;
                    }
                  }

                  &.arrow-bottom {
                    &:before {
                      border-color: #999999 transparent transparent transparent;
                    }

                    &:before,
                    &:after {
                      border-width: 4px 4px 0 4px;
                    }

                    &:after {
                      border-color: $styleBg1Hover transparent transparent transparent;
                    }
                  }
                }
              }
            }

            .editor-show-box {
              height: 150px;
              max-height: 92%;
              position: relative;
              border-top: 1px solid #E0E0E0;

              .editor-resize {
                width: 100%;
                height: 4px;
                position: absolute;
                top: -3px;
                z-index: 2;
                cursor: s-resize;
              }

              .toggle-slide-box {
                display: flex;
                align-items: center;
                flex-shrink: 0;
              }

              .record {
                display: flex;
                align-items: center;
                cursor: pointer;
                height: 20px;
                line-height: 20px;
                color: #333333;

                &:first-child {
                  margin-right: 14px;
                }

                .icon-editor {
                  width: 20px;
                  height: 20px;
                }

                .icon-record {
                  background-position: -320px 0;
                }

                .icon-reply {
                  background-position: -280px 0;
                }

                .record-text {
                  margin-left: 4px;
                }

                &:hover,
                &.active {
                  color: $styleColor;

                  .icon-record {
                    background-position: -340px 0;
                  }

                  .icon-reply {
                    background-position: -300px 0;
                  }
                }
              }

              .editor-box {
                height: 100%;
                background: #FFFFFF;
                position: relative;

                .tool-box {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  padding: 12px 16px;
                  color: #666;

                  .tool-ul {
                    display: flex;
                    align-items: center;
                    flex: 1;
                    min-width: 0;

                    .tool-li {
                      width: 20px;
                      height: 20px;
                      margin-right: 14px;
                      flex-shrink: 0;
                      position: relative;
                      cursor: pointer;

                      &.emoji {
                        background-position: 0 0;

                        &:hover {
                          background-position: -20px 0;
                        }
                      }

                      &.pic {
                        background-position: -40px 0;

                        &:hover {
                          background-position: -60px 0;
                        }
                      }

                      &.file {
                        background-position: -80px 0;

                        &:hover {
                          background-position: -100px 0;
                        }
                      }

                      &.screenshot {
                        margin-right: 20px;
                        background-position: -120px 0;

                        &.hover,
                        &:hover {
                          background-position: -140px 0;
                        }
                      }

                      &.shake {
                        background-position: -160px 0;

                        &:hover {
                          background-position: -180px 0;
                        }
                      }

                      &.notify {
                        margin-right: 20px;
                        background-position: -200px 0;

                        &.hover,
                        &:hover {
                          background-position: -220px 0;
                        }
                      }

                      &.no-notify {
                        margin-right: 20px;
                        background-position: -240px 0;

                        &.hover,
                        &:hover {
                          background-position: -260px 0;
                        }
                      }

                      &.tool-text-box {
                        display: flex;
                        align-items: center;
                        width: unset;
                        line-height: 20px;
                        margin-right: 20px;
                        color: #333333;
                        min-width: 0;
                        flex-shrink: 1;

                        &.tool-app {
                          margin-left: 5px;
                        }

                        &.tool-app-child {
                          .tool-sel-ul {
                            width: 170px;
                          }
                        }

                        .tool-icon-box {
                          width: 14px;
                          height: 14px;
                          margin-right: 4px;
                          flex-shrink: 0;

                          .tool-icon {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                          }
                        }

                        .tool-text {
                          margin-right: 4px;
                          max-width: 100px;
                        }

                        .tool-sel-ul {
                          top: unset;
                          bottom: 27px;
                          left: 0;
                        }
                      }

                      &.hover,
                      &:hover {
                        &.tool-text-box {
                          color: $styleColor;
                        }

                        .tool-arrow-box {
                          .tool-arrow {
                            border-color: $styleColor transparent transparent transparent;
                          }
                        }
                      }

                      .tool-arrow-box {
                        position: absolute;
                        top: 0;
                        right: -10px;
                        width: 10px;
                        height: 100%;

                        .tool-arrow {
                          position: absolute;
                          top: 50%;
                          left: 50%;
                          transform: translate(-50%, -50%);
                          width: 0;
                          height: 0;
                          border-width: 4px 4px 0 4px;
                          border-style: solid;
                          border-color: #999999 transparent transparent transparent;

                          &.hover {
                            border-width: 0 4px 4px 4px;
                            border-color: transparent transparent $styleColor transparent;
                          }
                        }
                      }
                    }

                    .tool-ai-tips {
                      display: flex;
                      align-items: center;
                      height: 20px;
                      padding: 0 4px 0 6px;
                      margin-right: 16px;
                      background: $styleBg2;
                      color: #333333;

                      .icon-close {
                        width: 14px;
                        height: 14px;
                        background: url("/img/index/icon_close.png") no-repeat center center;
                        background-size: 8px 8px;
                        margin-left: 6px;
                        cursor: pointer;
                      }
                    }
                  }
                }

                .editor-content-box {
                  height: calc(100% - 46px);

                  #editor {
                    height: calc(100% - 45px);
                    padding: 0 16px;
                  }
                }
              }

              .editor-tips-box {
                height: 100%;
                position: relative;
                background: #F5F5F5;
                display: flex;
                justify-content: center;
                align-items: center;

                .record {
                  position: absolute;
                  top: 12px;
                  right: 20px;
                }

                .blacklist {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                  color: #999999;

                  .btn {
                    margin-top: 10px;
                    width: 69px;
                    height: 24px;
                    text-align: center;
                    line-height: 24px;
                    font-size: 13px;
                    border-radius: 2px;
                    cursor: pointer;
                    background: $styleColor;
                    color: #ffffff;
                  }
                }
              }

              .multiple-box {
                height: 100%;
                position: relative;
                background: #F5F5F5;
                display: flex;
                justify-content: center;
                align-items: center;

                .multiple-ul {
                  width: 100%;
                  display: flex;
                  justify-content: space-around;
                  align-items: center;
                  padding: 0 5%;

                  .multiple-li {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    padding: 10px 3px;
                    cursor: pointer;

                    .multiple-icon {
                      width: 50px;
                      height: 50px;
                      background-image: url("/img/index/multiple.png");
                      background-size: 402px 50px;
                      background-position: -250px 0;
                      background-repeat: no-repeat;
                      margin-bottom: 10px;

                      &.icon2 {
                        background-position: -350px 0;
                      }

                      &.icon3 {
                        background-position: -150px 0;
                      }

                      &.icon4 {
                        background-position: -200px 0;
                      }

                      &.icon5 {
                        background-position: -250px 0;
                      }

                      &.icon6 {
                        background-position: -100px 0;
                      }

                      &.icon7 {
                        background-position: 0 0;
                      }
                    }
                  }
                }

                .multiple-close {
                  width: 20px;
                  height: 20px;
                  position: absolute;
                  top: 12px;
                  right: 10px;
                  cursor: pointer;
                  background: url("/img/index/icon_close.png") no-repeat center;
                  background-size: 50%
                }
              }
            }

            .operate-msg-box {
              position: absolute;
              bottom: 2px;
              left: 16px;
              width: calc(100% - 32px);
              display: flex;
              justify-content: space-between;
              align-items: flex-end;

              .operate-left-box {
                display: flex;
                align-items: flex-end;
                flex: 1;
                margin-right: 20px;

                .ai-refresh {
                  display: flex;
                  align-items: center;
                  margin-right: 16px;
                  padding-bottom: 11px;
                  color: #333333;
                  cursor: pointer;

                  &:hover {
                    color: $styleColor;

                    .icon-refresh {
                      background-position: -20px 0;
                    }
                  }

                  .ai-refresh-text {
                    margin-left: 4px;
                  }
                }

                .quote-msg-box {
                  max-height: 46px;
                  line-height: 18px;
                  overflow: hidden;
                  display: flex;
                  align-items: center;
                  border: 1px solid transparent;

                  .quote-msg-content {
                    max-width: 345px;
                    background: #E7E7E7;
                    border-radius: 4px;
                    padding: 4px 8px;
                    color: #666666;
                  }

                  .quote-msg-close {
                    width: 14px;
                    height: 14px;
                    margin-left: 10px;
                    background-image: url(/img/index/msg/quote_icon.png);
                    background-size: 38px 14px;
                    background-position: -24px 0;
                    background-repeat: no-repeat;
                    flex-shrink: 0;
                    cursor: pointer;
                  }
                }
              }

              .operate-right-box {
                display: flex;
                align-items: center;
                padding-bottom: 8px;

                .btn {
                  width: 54px;
                  height: 26px;
                  line-height: 24px;
                  text-align: center;
                  color: #333333;
                  border-radius: 4px;
                  border: 1px solid #BEBEBE;

                  &:hover {
                    color: #FFFFFF;
                    border-color: $styleColor;
                    background-color: $styleColor;
                  }

                  &.btn-send {
                    margin-left: 6px;
                    border-right: 0;
                    border-radius: 4px 0 0 4px;
                  }
                }

                .send-sel-tool {
                  position: relative;
                  width: 27px;
                  height: 26px;
                  line-height: 24px;
                  border: 1px solid #c8c8c8;
                  border-left: 0;
                  border-radius: 0 4px 4px 0;

                  &.hover,
                  &:hover {
                    border-color: $styleColor;
                    background-color: $styleColor;

                    &:after {
                      border-color: #FFFFFF transparent transparent transparent !important;
                    }
                  }

                  &.hover {
                    &:after {
                      border-width: 0 4px 4px 4px;
                      border-color: transparent transparent #FFFFFF transparent !important;
                    }
                  }

                  &:before {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    width: 1px;
                    height: 12px;
                    background: #c8c8c8;
                  }

                  &:after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 0;
                    height: 0;
                    border-width: 4px 4px 0 4px;
                    border-style: solid;
                    border-color: #999 transparent transparent transparent;
                  }

                  .tool-sel-ul {
                    top: auto;
                    left: auto;
                    bottom: 26px;
                    right: 0;
                  }
                }

                .disabled {
                  background-color: #BEBEBE !important;
                  border-color: #BEBEBE !important;
                  color: #FFFFFF !important;

                  &.send-sel-tool {
                    &:after {
                      border-color: #FFFFFF transparent transparent transparent;
                    }
                  }
                }
              }
            }

            .hait-sel-box {
              position: absolute;
              z-index: 3;
              left: 0;
              bottom: 150px;
              width: 382px;
              height: 250px;
              background: #FFFFFF;
              box-shadow: 0px 2px 10px 0px rgba(165, 165, 165, 0.5);
              border-radius: 6px;

              &.call-sel-box {
                width: 280px;
                height: 185px;

                .hait-content {
                  max-height: 140px;

                  .doc-title {
                    max-width: calc(100% - 26px) !important;
                    margin-right: 0 !important;
                  }

                  .hait-detail-box {
                    padding-right: 0;
                    max-height: 140px;
                  }
                }
              }

              .hait-header {
                display: flex;
                align-items: center;
                border-bottom: 1px solid #EBEBEB;
                margin-bottom: 4px;

                &.between {
                  justify-content: space-between;
                }

                .hait-tab {
                  height: 36px;
                  line-height: 36px;
                  font-size: 13px;
                  margin-left: 20px;
                  color: #333333;
                  cursor: pointer;

                  &.curr {
                    color: $styleColor;
                    font-weight: 600;
                    position: relative;

                    &:after {
                      content: "";
                      position: absolute;
                      bottom: 0;
                      left: 50%;
                      transform: translateX(-50%);
                      width: 16px;
                      height: 3px;
                      background: $styleColor;
                    }
                  }
                }

                .hait-tab-box {
                  display: flex;
                  align-items: center;
                }

                .hait-more {
                  color: $styleColor;
                  cursor: pointer;
                  padding-right: 10px;
                }
              }

              .hait-content {
                width: 100%;
                height: calc(100% - 37px - 8px);
                flex-shrink: 0;
                font-size: 13px;

                .hait-detail-box {
                  width: 100%;
                  height: 100%;
                  overflow-y: auto;
                  flex-shrink: 0;
                  display: none;

                  &.call-ai-box {
                    .hait-content-box {
                      height: calc(140px - 28px);
                      overflow-y: auto;
                    }

                    .hait-ai-more {
                      color: #333333;

                      .arrow-right {
                        &:before {
                          border-color: transparent transparent transparent #333333;
                        }

                        &:before,
                        &:after {
                          border-width: 5px 0 5px 5px;
                        }
                      }
                    }
                  }

                  &.curr {
                    display: block;
                  }

                  .hait-detail {
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    width: 100%;
                    height: 28px;
                    padding: 0 10px;
                    line-height: 18px;

                    &.curr {
                      background-color: $styleBg1Sel;
                    }

                    .doc-icon {
                      width: 20px;
                      height: 20px;
                      margin-right: 6px;
                      border-radius: 50%;
                    }

                    .doc-title {
                      max-width: 210px;
                      font-size: 13px;
                      color: #333333;
                      line-height: 20px;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      margin-right: 6px;
                    }

                    .doc-intr {
                      color: #999999;
                      line-height: 16px;
                      white-space: nowrap;
                      overflow: hidden;
                      text-overflow: ellipsis;
                    }

                    .user-label {
                      padding: 0 5px;
                      line-height: 16px;
                      font-size: 11px;
                      border-radius: 2px;
                      color: $styleColor;
                      background: #FFF0F1;
                      margin-left: 8px;
                      flex-shrink: 0;
                    }

                    .hait-label {
                      margin-right: 8px;
                      line-height: 16px;
                      padding: 0 4px;
                      font-size: 11px;
                      border-radius: 2px;
                      color: #FC9D03;
                      border: 1px solid #FC9D03;

                      &.hait-label-1 {
                        color: #999999;
                        border: 1px solid #BFBFBF;
                      }
                    }
                  }

                  .hait-loading {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    color: #999999;

                    img {
                      width: 14px;
                      height: 14px;
                      margin-right: 4px;

                      &.loading {
                        animation: myLoading 800ms linear infinite;
                      }
                    }
                  }
                }
              }
            }

            .float-msg-box {
              position: absolute;
              top: 0;
              right: 20px;
              z-index: 2;
              display: flex;
              flex-direction: column;
              align-items: flex-end;

              .float-content-box {
                margin-top: 5px;
                flex-shrink: 0;

                .float-content {
                  background: #FFFFFF;
                  box-shadow: 0 1px 9px 0 rgba(175, 175, 175, 0.50);
                  width: 100%;
                  min-width: 100px;
                  overflow: auto;
                  color: #3888FF;

                  &.hide {
                    .float-label-box {
                      .icon-box {
                        .icon-toggle {
                          transform: rotate(180deg);
                        }
                      }
                    }
                  }

                  .float-label-box {
                    padding: 6px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    cursor: pointer;

                    .concern-tips {
                      color: #FE7801;
                      font-weight: 600;
                    }

                    .icon-box {
                      display: flex;
                      align-items: center;

                      .icon-toggle {
                        width: 14px;
                        height: 14px;
                        background: url("/img/index/hait_arrow.png") no-repeat center center;
                        background-size: 10px 10px;
                      }

                      .icon-close {
                        width: 14px;
                        height: 14px;
                        background: url("/img/index/icon_close.png") no-repeat center center;
                        background-size: 8px 8px;
                        margin-left: 5px;
                      }
                    }
                  }
                }

                .float-detail {
                  background: #FFFFFF;
                  width: 250px;
                  max-height: 170px;
                  overflow: auto;

                  .float-li {
                    display: flex;
                    padding: 7px 10px 14px;
                    position: relative;

                    &:hover {
                      background: rgba(195, 195, 196, 0.3);
                    }

                    &:before {
                      content: "";
                      position: absolute;
                      bottom: 0;
                      width: 200%;
                      height: 1px;
                      left: -50%;
                      transform: scale(0.5);
                      background: #DCDCDC;
                    }

                    .avatar-box {
                      width: 26px;
                      height: 26px;
                      overflow: hidden;
                      flex-shrink: 0;
                      margin-right: 5px;

                      .avatar {
                        width: 100%;
                      }
                    }

                    .float-msg-content {
                      .user-name {
                        color: #9B9B9B;
                      }
                    }
                  }
                }
              }

              .float-hait {
                padding: 6px 10px;
                background: #3888FF;
                box-shadow: 0 1px 9px 0 #3888FF;
                font-weight: 600;
                color: #FFFFFF;
                cursor: pointer;
                text-align: center;

                &.active {
                  animation: twinkling 1s infinite ease-in-out;
                }
              }

              @-webkit-keyframes twinkling {
                0% {
                  background: #FFFFFF;
                  box-shadow: 0 1px 9px 0 rgba(175, 175, 175, 0.50);
                  color: #3888FF;
                }
                100% {
                  background: #3888FF;
                  box-shadow: 0 1px 9px 0 rgba(56, 136, 255, 0.50);
                  color: #FFFFFF;
                }
              }

              .float-concern {
                padding: 6px 10px;
                background: linear-gradient(135deg, #FD9645 0%, #FF7926 100%);
                box-shadow: 0px 5px 10px 0px #FFA35E;
                font-weight: 600;
                color: #FFFFFF;
                cursor: pointer;
                text-align: center;
              }
            }


            .msg-quote-detail-box {
              position: fixed;
              background: #FFFFFF;
              box-shadow: 0px 3px 16px 0px rgba(153, 153, 153, 0.45);
              border: 1px solid #E7E7E7;
              border-radius: 4px;
              cursor: default;
              z-index: -1;

              &.show {
                z-index: 20;
              }

              &.left {
                .quote-arrow {
                  left: auto;
                  right: -20px;
                  border-color: transparent transparent transparent #FFFFFF;
                }
              }

              .quote-arrow {
                left: -20px;
                border-color: transparent #FFFFFF transparent transparent;
                content: "";
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                border-width: 10px;
                border-style: solid;
              }

              .quote-scroll-box {
                max-width: 272px;
                max-height: 410px;
                padding: 12px;
                overflow-y: scroll;
                word-break: break-all;
                user-select: text;
              }
            }

            .doc-msg-op-box {
              width: 80px;
              position: fixed;
              background: #FFFFFF;
              box-shadow: 0px 4px 8px 0px rgba(169, 169, 169, 0.7);
              border: 1px solid #D8D8D8;
              color: #333333;
              z-index: -1;
              border-radius: 4px;

              &.show {
                z-index: 20;
              }

              &:before {
                content: "";
                position: absolute;
                top: -7px;
                right: 8px;
                width: 0;
                height: 0;
                border-width: 0 6px 6px 6px;
                border-style: solid;
                border-color: transparent transparent #D8D8D8 transparent;
              }

              &:after {
                content: "";
                position: absolute;
                top: -6px;
                right: 8px;
                width: 0;
                height: 0;
                border-width: 0 6px 6px 6px;
                border-style: solid;
                border-color: transparent transparent #FFFFFF transparent;
              }

              li {
                height: 30px;
                padding: 7px;
                box-sizing: border-box;
                display: flex;
                align-items: center;
                cursor: pointer;
                position: relative;

                &:hover {
                  background: $styleBg1;
                }

                &.curr {
                  color: $styleColor;

                  &:after {
                    content: '';
                    width: 15px;
                    height: 9px;
                    border: 3px solid $styleColor;
                    border-top: transparent;
                    border-right: transparent;
                    text-align: center;
                    display: block;
                    position: absolute;
                    top: 50%;
                    right: 6px;
                    vertical-align: middle;
                    transform: translateY(-50%) rotate(-45deg) scale(.5);
                    box-sizing: border-box;
                  }
                }
              }
            }
          }

          .slide {
            width: 13px;
            height: 50px;
            position: absolute;
            top: 50%;
            left: -13px;
            transform: translateY(-50%);
            background-repeat: no-repeat;
            background-size: 52px 50px;
            background-position: 0 0;
            cursor: pointer;

            &:hover {
              background-image: url("/img/index/member/icon_slide.png");
              background-position: -13px 0;
            }
          }

          .chat-content-slide {
            height: 100%;
            width: 0;
            position: relative;
          }

          .chat-content-member {
            height: calc(100% - 1px);
            width: 216px;
            flex-shrink: 0;
            border-left: 1px solid #E0E0E0;
            background: #FFFFFF;
            position: relative;

            &:hover {
              .slide {;
                background-image: url("/img/index/member/icon_slide.png");
                background-position: -26px 0;

                &:hover {
                  background-position: -39px 0;
                }
              }
            }

            .chat-team-info {
              border-bottom: 1px solid #E0E0E0;
              padding: 12px;
              line-height: 22px;
              height: 117px;
              color: #333333;

              .chat-team-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #999999;

                .chat-team-edit-box {
                  display: flex;
                  align-items: center;
                }

                .chat-team-update {
                  width: 16px;
                  height: 16px;
                  margin-left: 6px;
                  cursor: pointer;

                  &:hover {
                    background-position: -16px 0;
                  }
                }

                .chat-team-update-save {
                  margin-left: 6px;
                  cursor: pointer;

                  &:hover {
                    color: $styleColor;
                  }
                }

                .chat-team-qrcode {
                  width: 16px;
                  height: 16px;
                  margin-left: 6px;
                  cursor: pointer;
                  background-position: -32px 0;

                  &:hover {
                    background-position: -48px 0;
                  }
                }
              }

              .chat-team-name {
                width: 100%;
                height: 26px;
                line-height: 26px;
                font-weight: bold;

                &.input-team-name {
                  font-weight: normal;
                }
              }

              .chat-team-owner {
                font-weight: bold;
              }
            }

            .chat-team-member {
              height: calc(100% - 118px - 35px);

              &.show-page {
                max-height: calc(100% - 118px - 34px - 34px);
              }

              .chat-member-title {
                color: #333333;
                padding: 12px 12px 8px 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                line-height: 17px;
                font-weight: bold;

                .chat-member-icon {
                  display: flex;
                  align-items: center;

                  .chat-member-mute {
                    width: 16px;
                    height: 16px;
                    background-position: -112px 0;
                    cursor: pointer;

                    &.muted {
                      background-position: -128px 0;

                      &:hover {
                        background-position: -128px 0;
                      }
                    }

                    &:hover {
                      background-position: -128px 0;
                    }
                  }

                  .chat-member-search {
                    width: 16px;
                    height: 16px;
                    margin-left: 10px;
                    background-position: -64px 0;
                    cursor: pointer;

                    &.active {
                      background-position: -96px 0;
                    }

                    &:hover {
                      background-position: -80px 0;
                    }
                  }
                }
              }

              .chat-member-search-box {
                margin: 0 12px 5px;
                display: flex;
                align-items: center;
                position: relative;
                line-height: 26px;
                border-radius: 4px;
                border: 1px solid #333333;
                padding: 0 6px;

                input {
                  flex: 1;
                  height: 26px;
                  line-height: 26px;
                  padding-right: 6px;
                }

                .search-member-close {
                  width: 12px;
                  height: 12px;
                  background-image: url("/img/index/member/icon_del.png");
                  cursor: pointer;
                  background-size: 24px 12px;
                }

                .search-member-box {
                  padding: 4px 0;
                  position: absolute;
                  left: -1px;
                  top: 28px;
                  width: calc(100% + 2px);
                  background-color: #FFFFFF;
                  border: 1px solid #E0E0E0;
                  box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
                  border-radius: 4px;

                  .search-member-none {
                    text-align: center;
                    line-height: 26px;
                  }

                  .search-member-ul {
                    max-height: 162px;
                    overflow-y: auto;

                    .search-member-li {
                      display: flex;
                      align-items: center;
                      height: 26px;
                      padding: 0 8px;

                      &.curr {
                        background: $styleBg1Hover;
                      }

                      .avatar-box {
                        width: 16px;
                        height: 16px;
                        overflow: hidden;
                        flex-shrink: 0;
                        border-radius: 50%;

                        .avatar {
                          width: 100%;
                        }
                      }

                      .user-name {
                        flex: 1;
                        margin-left: 5px;
                      }
                    }
                  }
                }
              }

              .chat-member-loading {
                width: 100%;
                height: 100%;
                position: relative;

                &:after {
                  content: "";
                  position: absolute;
                  top: 10px;
                  left: 50%;
                  transform: translateX(50%);
                  width: 20px;
                  height: 20px;
                  background: url("/img/loading_gray.png") no-repeat center;
                  background-size: 20px;
                  animation: myLoading 800ms linear infinite;
                }
              }

              .chat-member-ul {
                max-height: calc(100% - 38px);
                overflow-y: overlay;

                &.show-search {
                  max-height: calc(100% - 38px - 30px);
                }

                .chat-member-err {
                  text-align: center;
                }

                .chat-member-li {
                  padding: 0 12px;
                  height: 26px;

                  &.curr {
                    background-color: $styleBg1Hover;
                  }

                  &:hover {
                    background-color: $styleBg1Hover;

                    .user-icon {
                      &.del {
                        width: 12px;
                        height: 12px;
                        background-image: url("/img/index/member/icon_del.png");
                        cursor: pointer;
                        background-size: 24px 12px;
                        background-position: 0 0;

                        &:hover {
                          background-position: -12px 0;
                        }
                      }
                    }
                  }

                  .chat-member-box {
                    height: 100%;
                    display: flex;
                    align-items: center;
                  }

                  .avatar-box {
                    width: 16px;
                    height: 16px;
                    overflow: hidden;
                    flex-shrink: 0;
                    border-radius: 50%;

                    .avatar {
                      width: 100%;
                    }
                  }

                  .user-name {
                    flex: 1;
                    margin: 0 5px;
                  }

                  .user-icon {
                    width: 16px;
                    height: 16px;
                    flex-shrink: 0;
                    background-repeat: no-repeat;

                    &.owner {
                      background-position: -144px 0;
                    }

                    &.muted {
                      background-position: -128px 0;
                    }
                  }
                }
              }
            }

            .chat-team-operate {
              .chat-team-page-box {
                position: relative;
                line-height: 34px;
                height: 34px;
                text-align: center;
                border-top: 1px solid #E0E0E0;
                background: #FFFFFF;

                .chat-team-page {
                  display: inline-flex;
                  justify-content: center;
                  position: absolute;
                  top: 50%;
                  left: 12px;
                  transform: translateY(-50%);
                  cursor: pointer;
                  padding-right: 12px;
                  letter-spacing: 2px;

                  &:after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    right: 0;
                    width: 5px;
                    height: 5px;
                    border: 1px solid #000;
                    border-left: none;
                    border-bottom: none;
                    transform: translateY(-50%) rotate(135deg);
                  }
                }

                .chat-team-page-btn {
                  position: absolute;
                  top: 50%;
                  right: 10px;
                  transform: translateY(-50%);
                  display: flex;

                  .chat-team-page-icon {
                    width: 20px;
                    height: 20px;
                    background-image: url("/img/index/member/icon_arrow.png");
                    background-repeat: no-repeat;
                    background-size: 80px 20px;
                    margin-left: 2px;
                    cursor: pointer;

                    &.left {
                      &:hover {
                        background-position: -20px 0;
                      }
                    }

                    &.right {
                      background-position: -40px 0;

                      &:hover {
                        background-position: -60px 0;
                      }
                    }
                  }
                }

                .chat-team-page-ul {
                  position: absolute;
                  bottom: 33px;
                  left: 8px;
                  max-height: 140px;
                  overflow-y: auto;
                  background: #fff;
                  border: 1px solid #E0E0E0;
                  cursor: pointer;
                  color: #999999;
                  line-height: 34px;

                  .chat-team-page-li {
                    padding: 0 30px 0 10px;
                  }
                }
              }

              .chat-team-operate-detail {
                display: flex;
                line-height: 34px;
                text-align: center;
                border-top: 1px solid #E0E0E0;

                .chat-team-invite {
                  flex: 1;
                  color: #333333;
                  cursor: pointer;
                }

                .chat-team-leave {
                  flex: 1;
                  border-left: 1px solid #E0E0E0;
                  color: $styleColor;
                  cursor: pointer;
                }
              }
            }
          }

          .record-box {
            position: relative;
            width: 50%;
            height: 100%;
            border-left: 1px solid #E0E0E0;
            background: #EFEFEF;
            flex-shrink: 0;

            &:hover {
              .slide {;
                background-image: url("/img/index/member/icon_slide.png");
                background-position: -26px 0;

                &:hover {
                  background-position: -39px 0;
                }
              }
            }

            .record-msg-box {
              height: calc(100% - 30px);
              overflow-y: auto;

              &.show-search {
                height: calc(100% - 30px - 40px);
              }
            }

            .search-msg-box {
              height: calc(100% - 30px - 40px);
            }

            .record-msg-box,
            .search-msg-box {

              .search-msg-tip {
                height: 24px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background-color: rgb(209, 233, 252);
                padding: 0 10px;

                .search-msg-back {
                  padding: 2px 2px 2px 18px;
                  background: url("/img/index/member/back.png") 2px center no-repeat;
                  border: 1px solid transparent;
                  border-radius: 2px;
                  cursor: pointer;

                  &:hover {
                    border-color: #BBBBBB;
                    background-color: rgb(220, 240, 252);
                  }
                }
              }

              .search-msg-content {
                height: calc(100% - 24px);
                overflow-y: auto;
              }

              &.no-show-back {
                .search-msg-content {
                  height: 100%;
                }
              }
            }

            .record-search-box {
              height: 40px;
              display: flex;
              align-items: center;
              background-color: #ebf2f9;
              border-top: 1px solid #DDDDDD;

              .record-search-input {
                width: 110px;
                border: 1px solid #CCCCCC;
                padding: 3px 5px;
                line-height: 16px;
                margin: 0 5px;
                background-color: #FFFFFF;
                border-radius: 3px;
              }

              .record-search-btn {
                line-height: 24px;
                text-align: center;
                padding: 0 6px;

                &:hover {
                  background-color: #DDDDDD;
                }
              }
            }

            .record-operate {
              height: 30px;
              background: #EBF2F9;
              border-top: 1px solid #E0E0E0;
              display: flex;
              justify-content: space-between;
              align-items: center;

              .record-operate-set {
                display: flex;
                align-items: center;
                position: relative;

                .record-search-icon {
                  width: 24px;
                  height: 24px;
                  background: url("/img/index/member/search.png") no-repeat center center;
                  background-size: 15px;
                  margin-left: 5px;

                  &:hover {
                    background-color: #DDDDDD;
                  }
                }

                .record-time {
                  line-height: 24px;
                  padding: 0 5px 0 25px;
                  background: url("/img/index/member/cal.png") no-repeat 5px center;
                  background-size: 16px;

                  &:hover {
                    background-color: #DDDDDD;
                  }
                }

                .calendar-box {
                  width: 250px;
                  position: absolute;
                  left: 5px;
                  bottom: 30px;
                  background: #FFFFFF;
                  padding: 10px;
                  border: 1px solid #CCCCCC;
                }
              }

              .record-page {
                display: flex;

                .record-btn {
                  width: 24px;
                  height: 24px;
                  background: url("/img/index/member/right_gray.png") no-repeat center center;
                  background-size: 15px;
                  margin-right: 5px;

                  &:hover {
                    background-color: #DDDDDD;
                  }

                  &.left {
                    transform: rotate(180deg);
                    margin-right: 0;
                  }
                }
              }

              .record-clear {
                margin-left: 10px;
                cursor: pointer;
              }
            }
          }

          .quick-box {
            position: relative;
            width: 50%;
            height: 100%;
            overflow-y: auto;
            border-left: 1px solid #E0E0E0;
            flex-shrink: 0;

            .reply-box {
              position: relative;
              width: 100%;
              background: #FFFFFF;
              margin-bottom: 8px;

              &.hide {
                .reply-title {
                  .icon-arrow {
                    transform: rotate(0deg);
                  }
                }
              }

              .reply-title {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 40px;
                font-size: 15px;
                font-weight: 400;
                color: #252525;
                padding: 0 20px 0 14px;
                border-bottom: 1px solid #E7E7E7;

                .new-label {
                  position: absolute;
                  top: 50%;
                  left: 70px;
                  transform: translateY(-50%);
                  width: 30px;
                  height: 16px;
                  font-size: 12px;
                  line-height: 12px;
                  text-align: center;
                  color: #FFFFFF;
                  background: $styleColor;
                  border-radius: 2px;

                  &:after {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: -4px;
                    transform: translateY(-50%);
                    width: 0;
                    height: 0;
                    border-width: 4px 4px 4px 0;
                    border-style: solid;
                    border-color: transparent $styleColor transparent transparent;
                  }
                }

                .icon-arrow {
                  width: 10px;
                  height: 10px;
                  background: url(/img/index/hait_arrow.png) no-repeat;
                  background-size: 10px 10px;
                  transform: rotate(180deg);

                  &.arrow-right {
                    transform: rotate(-90deg);
                  }
                }
              }

              .reply-content-box {

                .reply-content {
                  margin: 0 8px;
                  padding: 8px 14px 8px 6px;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;

                  &:hover {
                    background: #EBEBEB;

                    .icon-send {
                      background-position: -14px 0;
                    }
                  }

                  &:first-child {
                    margin-top: 8px;
                  }

                  .reply-content-detail {
                    position: relative;
                    flex: 1;
                    padding: 8px 14px;
                    margin-left: 7px;
                    font-size: 14px;
                    font-weight: 400;
                    color: #666666;
                    line-height: 20px;
                    background: #F5F5F5;
                    word-break: break-all;

                    &:after {
                      content: "";
                      position: absolute;
                      top: 9px;
                      left: -7px;
                      width: 0;
                      height: 0;
                      border-width: 7px 7px 7px 0px;
                      border-style: solid;
                      border-color: transparent #F5F5F5 transparent transparent;
                    }
                  }

                  .icon-send {
                    width: 14px;
                    height: 14px;
                    margin-left: 14px;
                    background-image: url("/img/index/msg/reply_send.png");
                    background-repeat: no-repeat;
                    background-position: 0 0;
                    background-size: 28px 14px;
                  }
                }
              }
            }
          }

          .ai-manager-box {
            display: flex;
            flex-direction: column;
            height: calc(100% - 1px);
            width: 50%;
            flex-shrink: 0;
            border-left: 1px solid #E0E0E0;
            background: #FFFFFF;
            position: relative;

            &:hover {
              .slide {
                background-image: url("/img/index/member/icon_slide.png");
                background-position: -26px 0;

                &:hover {
                  background-position: -39px 0;
                }
              }
            }

            .ai-show-box {
              display: flex;
              flex-direction: column;
              width: 100%;
              height: 100%;

              &.ai-show-list {
                height: auto;
              }
            }

            .ai-manager-back-box {
              .ai-manager-back {
                display: inline-flex;
                align-items: center;
                padding: 10px 0 0 10px;
                cursor: pointer;

                &:hover {
                  color: $styleColor;

                  .arrow-left {
                    &:before {
                      border-color: transparent $styleColor transparent transparent;
                    }
                  }
                }

                .arrow-left {
                  &:before,
                  &:after {
                    border-width: 5px 5px 5px 0;
                  }

                  &:before {
                    border-color: transparent #333333 transparent transparent;
                  }
                }
              }
            }

            .ai-manager-info-box {
              display: flex;
              flex-shrink: 0;
              padding: 10px 10px 16px 16px;
              border-bottom: 1px solid #E0E0E0;

              .user-avatar-box {
                flex-shrink: 0;
                margin-right: 10px;
              }

              .ai-manager-info-text-box {
                color: #999999;
                line-height: 17px;
                flex: 1;
                min-width: 0;

                .ai-manager-info-name {
                  color: #333333;
                  font-weight: bold;
                  font-size: 15px;
                  line-height: 21px;
                  margin-bottom: 4px;
                }

                .ai-manager-info-intr {
                  max-height: 51px;
                  word-break: break-all;
                  overflow-y: auto;
                  margin-bottom: 1px;

                  &::-webkit-scrollbar-thumb {
                    min-height: auto;
                  }
                }

                .ai-manager-info-detail {
                  display: flex;

                  .ai-manager-info-detail-title {
                    flex-shrink: 0;
                  }

                  .ai-manager-info-detail-text {
                    color: #333333;
                  }
                }
              }
            }

            .ai-manager-knowledge-box {
              display: flex;
              flex-direction: column;
              flex: 1;
              min-height: 0;
              padding-top: 14px;

              .ai-manager-knowledge-title-box {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 16px;

                .ai-manager-knowledge-title-left {
                  display: flex;
                  align-items: center;
                  font-weight: bold;
                  font-size: 13px;
                  color: #333333;
                  line-height: 18px;

                  .icon-question {
                    position: relative;
                    width: 16px;
                    height: 16px;
                    margin-left: 2px;
                    background-image: url("/img/workbench/icon_helper.png");
                    background-repeat: no-repeat;
                    background-size: 32px 16px;
                    cursor: pointer;

                    &:hover {
                      background-position: -16px 0;

                      &:before {
                        content: "";
                        width: 0;
                        height: 0;
                        position: absolute;
                        top: 50%;
                        transform: translateY(-50%);
                        left: 10px;
                        border-width: 6px;
                        border-style: solid;
                        border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
                      }

                      &:after {
                        content: "点击了解知识库文档小技巧";
                        position: absolute;
                        top: 50%;
                        left: 22px;
                        transform: translateY(-50%);
                        width: 86px;
                        border-radius: 4px;
                        background: rgba(0, 0, 0, 0.8);
                        padding: 4px 10px;
                        color: #FFFFFF;
                        font-size: 12px;
                        font-weight: normal;
                        line-height: 17px;
                      }
                    }
                  }
                }

                .ai-manager-knowledge-title-right {
                  position: relative;
                  padding: 3px 6px 3px 22px;
                  background-image: url("/img/more/icon_add.png");
                  background-repeat: no-repeat;
                  background-position: 6px center;;
                  background-size: 14px 14px;
                  border-radius: 2px;
                  color: $styleColor;
                  cursor: pointer;

                  &.hover {
                    background-color: $styleBg1Hover;
                  }

                  .ai-manager-add-ul {
                    position: absolute;
                    top: 26px;
                    right: 0;
                    width: 130px;
                    box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
                    border-radius: 4px;
                    border: 1px solid #E0E0E0;
                    background: #FFFFFF;
                    color: #000000;
                    z-index: 1;

                    .ai-manager-add-li {
                      line-height: 26px;
                      padding: 0 10px;

                      &:hover {
                        background: $styleBg1Hover;
                      }
                    }
                  }
                }
              }

              .ai-manager-knowledge-list-box {
                min-height: 0;
                flex: 1;
                display: flex;
                flex-direction: column;
                padding-top: 10px;

                .ai-manager-knowledge-list-search-box {
                  display: flex;
                  align-items: center;
                  height: 30px;
                  border-radius: 4px;
                  border: 1px solid #E0E0E0;
                  padding-left: 26px;
                  font-size: 13px;
                  background: #FFFFFF url("/img/search/icon_search.png") no-repeat 7px center;
                  background-size: 16px 16px;
                  margin: 0 16px 4px 16px;
                  flex-shrink: 0;

                  &:focus-within,
                  &:hover {
                    border: 1px solid #000000;
                  }

                  .search-input {
                    flex: 1;
                  }

                  .icon-close {
                    width: 12px;
                    height: 12px;
                    margin: 0 10px;
                    background-image: url("/img/search/close.png");
                    background-repeat: no-repeat;
                    background-size: 12px 12px;
                    cursor: pointer;
                  }
                }

                .ai-manager-knowledge-list-ul {
                  min-height: 0;
                  flex: 1;
                  overflow: auto;

                  .ai-manager-knowledge-list-li {
                    display: flex;
                    align-items: center;
                    height: 30px;
                    font-size: 13px;
                    padding: 0 16px;

                    &:hover {
                      background: $styleBg1Hover;
                    }

                    .ai-manager-knowledge-list-li-left,
                    .ai-manager-knowledge-list-li-right {
                      display: flex;
                      align-items: center;
                    }

                    .ai-manager-knowledge-list-li-left {
                      min-width: 0;
                      flex: 1;
                    }

                    .file-icon {
                      width: 16px;
                      margin-right: 8px;
                      flex-shrink: 0;
                    }

                    .file-status {
                      flex-shrink: 0;
                      color: $styleColor;
                      margin-left: 20px;
                      line-height: 16px;
                      font-size: 12px;

                      &.file-status-loading {
                        position: relative;
                        padding-left: 20px;
                        color: #999999;

                        &:before {
                          content: "";
                          position: absolute;
                          top: 0;
                          left: 0;
                          width: 16px;
                          height: 16px;
                          background: url("/img/icon_loading.png") no-repeat;
                          background-size: 16px;
                          animation: myLoading 800ms linear infinite;
                        }
                      }
                    }

                    .file-close {
                      flex-shrink: 0;
                      width: 16px;
                      height: 16px;
                      background-image: url("/img/close.png");
                      background-repeat: no-repeat;
                      background-position: center;
                      background-size: 9px;
                      cursor: pointer;
                      margin-left: 20px;

                      &:hover {
                        background-image: url("/img/close_hover.png");
                      }
                    }
                  }
                }
              }

              .ai-manager-knowledge-list-none {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;
                color: #999999;
                line-height: 18px;
                padding: 80px 16px 0 16px;

                .ai-manager-knowledge-title-text {
                  font-size: 13px;
                  color: #000000;
                  margin: 6px 0 4px;
                }

                .ai-manager-knowledge-intr-text {
                  text-align: center;
                }
              }

              .ai-manager-knowledge-tips-box {
                width: 100%;
                height: 46px;
                display: flex;
                flex-direction: column;
                justify-content: center;
                line-height: 17px;
                padding-left: 16px;
                margin-top: 4px;
                color: #999999;
                border-top: 1px solid #E7E7E7;
              }

              .ai-manager-upload-loading {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                height: 46px;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 13px;
                line-height: 46px;
                color: #666666;
                border-top: 1px solid #E7E7E7;
                background: #FFFFFF;
                z-index: 1;

                &:before {
                  content: "";
                  width: 14px;
                  height: 14px;
                  background: url("/img/index/icon_loading.png") no-repeat;
                  background-size: 14px;
                  animation: myLoading 500ms linear infinite;
                  margin: 4px;
                }
              }
            }

            .ai-invite-box {
              display: flex;
              padding: 16px 0 16px 16px;
              border-bottom: 1px solid #E7E7E7;

              .user-avatar-box {
                width: 40px !important;
                height: 40px !important;
                cursor: default !important;
                margin-right: 10px;
              }

              .user-detail-box {
                flex: 1;

                .ai-invite-name-box {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  padding-right: 16px;
                  font-weight: bold;
                  font-size: 15px;
                  color: #000000;
                  line-height: 21px;
                  margin-bottom: 4px;

                  .switch-box {
                    position: relative;
                    width: 24px;
                    height: 14px;
                    margin-left: 6px;
                    border-radius: 10px;
                    background: #CCCCCC;
                    flex-shrink: 0;
                    cursor: pointer;

                    &:after {
                      content: "";
                      position: absolute;
                      top: 2px;
                      left: 2px;
                      width: 10px;
                      height: 10px;
                      background: #FFFFFF;
                      border-radius: 50%;
                    }

                    &.on {
                      background: $styleColor;

                      &:after {
                        left: unset;
                        right: 2px;
                      }
                    }
                  }
                }

                .ai-invite-intr-box {
                  display: flex;
                  color: #999999;
                  line-height: 17px;

                  .ai-invite-intr-content {
                    position: relative;
                    flex: 1;

                    &::before {
                      content: " ";
                      float: right;
                      width: 0px;
                      height: calc(100% - 17px);
                    }

                    .ai-invite-intr-edit {
                      float: right;
                      clear: both;
                      color: #333333;
                      padding-left: 3px;
                      cursor: pointer;

                      &:hover {
                        color: $styleColor;
                      }
                    }
                  }

                  .ai-invite-to {
                    position: relative;
                    width: 44px;
                    padding: 0 16px 0 10px;
                    cursor: pointer;

                    &.no-show {
                      cursor: default;
                    }

                    .arrow-right {
                      position: absolute;
                      top: 50%;
                      left: 50%;
                      transform: translate(-50%, -50%);

                      &:before {
                        border-color: transparent transparent transparent #999999;
                      }

                      &:hover {
                        &:before {
                          border-color: transparent transparent transparent $styleColor;
                        }
                      }

                      &:before,
                      &:after {
                        border-width: 7px 0 7px 7px;
                      }
                    }
                  }
                  .blank{
                    width: 44px;
                  }
                }
              }
            }
          }
        }

        &.account-box-li {
          height: 100%;
          overflow-y: auto;

          .account-box {
            width: 520px;
            margin: 16px auto;
            padding: 24px 70px;
            background: #FFFFFF;
            border-radius: 4px;

            .account-content-box {
              width: 100%;
              padding: 16px 0;
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-top: 1px solid #E0E0E0;

              &:first-child {
                border-top: 0;
              }

              .account-title {
                font-size: 22px;
                font-weight: bold;
                color: #333333;
                line-height: 30px;
                margin-bottom: 10px;
              }

              .account-intr-box {
                display: flex;

                .account-intr {
                  color: #999999;
                }

                .account-details {
                  flex: 1;
                  word-break: break-all;
                  line-height: 17px;
                }
              }
            }

            .user-avatar-box {
              width: 52px !important;
              height: 52px !important;
              border-radius: 50%;
              flex-shrink: 0;

              .avatar-box {
                cursor: default !important;
              }
            }
          }
        }
      }
    }
  }

  .team-qrcode-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 52;

    .team-qrcode-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 340px;
      height: 342px;
      background: #FFFFFF;
      border: 1px solid #C7C7C7;
      box-shadow: 0 0 6px 0 rgba(95, 95, 95, 0.50);
      display: flex;
      flex-direction: column;
      border-radius: 4px;
      overflow: hidden;

      .team-qrcode-title {
        width: 100%;
        line-height: 34px;
        padding-left: 15px;
        color: #000000;
        background: #F0F0F0;
        position: relative;
        font-weight: bold;

        .team-qrcode-text {
          max-width: 90%;
        }

        .team-qrcode-close {
          position: absolute;
          top: 50%;
          right: 5px;
          transform: translateY(-50%);
          cursor: pointer;
          width: 21px;
          height: 21px;
          padding: 6px;
        }
      }

      .team-qrcode-detail {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        flex: 1;

        .team-qrcode-btn {
          width: 100px;
          height: 32px;
          line-height: 32px;
          margin-top: 30px;
          color: #FFFFFF;
          text-align: center;
          background: $styleColor;
          border-radius: 4px;
          cursor: pointer;
        }

        .team-avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
        }
      }
    }
  }

  :deep(.dialog-manger-box) {
    .content {
      padding: 10px 0 !important;
    }

    footer {
      padding: 12px 0 !important;
      box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05), inset 0px 1px 0px 0px #DDDDDD;
    }
  }

  :deep(.dialog-forward-report-box) {
    .content {
      max-height: calc(620px - 34px - 48px);
      padding: 16px !important;
    }
  }

  :deep(.dialog-ai-app-box) {
    .ly-dialog {
      overflow: unset;
    }

    .content {
      padding: 10px 0 16px !important;

      .main-dialog-box {
        &.overflow {
          max-height: 460px;
          overflow-y: auto;
        }
      }

      .dialog-intr {
        padding: 0 16px;
      }

      .dialog-content-li-label {
        width: 76px;
        text-align: right;
      }

      .dialog-content-li-textarea {
        height: 48px;
      }
    }
  }

  :deep(.dialog-team-file-box) {
    .content {
      padding: 16px 16px 10px !important;
    }
  }

  :deep(.dialog-team-ai-intr-editor-box) {
    .content {
      padding: 16px !important;
    }
  }

  :deep(.quick-acting-box){
    .content{
      max-height: 560px;
      overflow: scroll;
    }
  }

  :deep(.dialog-hait-box) {
    .content {
      padding: 16px;
    }
  }

  .main-dialog-box {
    .add-classify-box {
      margin: 0 16px;

      .add-classify {
        padding-left: 16px;
        color: $styleColor;
        background: url("/img/workbench/icon_add.png") no-repeat left center;
        background-size: 12px 12px;
        line-height: 16px;
        cursor: pointer;
      }
    }

    .sel-classify-ul {
      margin: 10px 0;
      height: 230px;
      overflow-y: auto;

      .sel-classify-li {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        padding: 6px 16px;
        position: relative;

        &:hover {
          background: $styleBg1Hover;
        }

        &.move {
          box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.18) !important;
        }

        &.move-status {
          &:hover {
            box-shadow: none;
            background: none;
          }
        }

        &.sel {
          .li-sel-icon {
            background-image: url("/img/mail/check_1.png");
          }
        }

        .li-sel-icon {
          width: 14px;
          height: 14px;
          background-image: url("/img/mail/check_0.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-right: 10px;
          flex-shrink: 0;
        }

        .li-img-box {
          width: 20px;
          height: 20px;
          overflow: hidden;
          margin-right: 8px;
          flex-shrink: 0;
          background: #F2F2F2;
          border: 1px solid #D7D9DA;
          border-radius: 2px;
          position: relative;

          img {
            width: calc(100% + 2px);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }

        .li-name {
          flex: 1;
        }

        .li-move-icon {
          width: 16px;
          height: 16px;
          background-image: url("/img/workbench/icon_move.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .li-edit-icon {
          width: 16px;
          height: 16px;
          background-image: url("/img/workbench/icon_edit.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-right: 10px;
          flex-shrink: 0;
          cursor: pointer;
        }

        .li-close-icon {
          width: 16px;
          cursor: pointer;
        }
      }
    }

    .ly-dialog-default-box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;

      .ly-dialog-default-label {
        color: #666666;
        line-height: 30px;
        flex-shrink: 0;

        .ly-dialog-default-tips {
          color: #EE3939;
          margin-right: 5px;
        }
      }

      .ly-dialog-default-detail {
        width: 282px;
        position: relative;

        .ly-dialog-default-input {
          width: 100%;
          line-height: 28px;
          padding-left: 10px;
          border: 1px solid #CCCCCC;
          border-radius: 4px;

          &::placeholder {
            color: #999999;
          }

          &:focus {
            border: 1px solid #333333;
          }
        }

        .ly-dialog-default-textarea-box {
          border: 1px solid #E0E0E0;
          border-radius: 2px;
          padding: 8px 0 8px 10px;

          &:hover,
          &:focus-within {
            border: 1px solid #333333;
          }

          .ly-dialog-default-textarea {
            width: 100%;
            height: 99px;
            background: #FFFFFF;
            border-radius: 2px;
            padding-right: 10px;
          }

          .ly-dialog-default-textarea-text {
            position: absolute;
            right: 10px;
            bottom: 10px;
          }
        }
      }
    }

    .move-content-box {
      .ly-dialog-default-tab {
        display: flex;
        padding: 0 6px;
        border-bottom: 1px solid #E0E0E0;
        margin-bottom: 10px;

        li {
          position: relative;
          padding: 10px;
          cursor: pointer;

          &.sel {
            color: $styleColor;
            font-weight: bold;

            &:after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 12px;
              height: 3px;
              background: $styleColor;
            }
          }
        }
      }

      .move-content-tips {
        display: flex;
        align-items: center;
        height: 34px;
        padding-left: 36px;
        color: #666666;
        background: url("/img/collect/icon_tips.png") no-repeat 15px center;
        background-size: 14px 14px;
        border-bottom: 1px solid #E0E0E0;
        margin-bottom: 10px;
      }

      .move-content-ul {
        margin: 7px 0;
        height: 216px;
        overflow-y: auto;

        li {
          display: flex;
          align-items: center;
          height: 30px;
          padding: 0 16px;

          &:hover {
            background: $styleBg1Hover;
          }

          .sel-box {
            display: inline-block;
            width: 14px;
            height: 14px;
            position: relative;
            border: 1px solid #DDDDDD;
            border-radius: 50%;
            flex-shrink: 0;
            margin-right: 6px;

            &.sel {
              border: 1px solid $styleColor;
              background: $styleColor;

              &:after {
                content: "";
                width: 6px;
                height: 3px;
                border: 2px solid #FFFFFF;
                border-top: transparent;
                border-right: transparent;
                position: absolute;
                top: 3px;
                left: 2px;
                transform: rotate(-45deg);
              }
            }
          }
        }
      }
    }

    .forward-report-content-box {
      .forward-report-title-box {
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 8px;

        .tips {
          position: absolute;
          top: 50%;
          left: -7px;
          transform: translateY(-50%);
          color: $styleColor;
        }

        .title {
          margin-right: 8px;
          font-size: 13px;
          color: #333333;
          font-weight: bold;
          line-height: 17px;
        }

        .intr-box {
          display: flex;
          align-items: center;

          &.point {
            cursor: pointer;

            &:hover {
              .intr {
                color: $styleColor;
              }

              .icon-question {
                background-position: -12px 0;
              }
            }
          }

          .intr {
            color: #999999;
            line-height: 17px;
          }

          .icon-question {
            display: block;
            width: 12px;
            height: 12px;
            background-image: url("/img/mail/icon_question.png");
            background-repeat: no-repeat;
            background-size: 36px 12px;
            margin-left: 2px;
          }
        }
      }

      .forward-report-sel-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 10px;
        max-height: 40px;
        overflow-y: auto;

        .sel-content-box {
          display: flex;
          align-items: center;
          margin: 0 20px 6px 0;
          cursor: pointer;

          .sel-box {
            display: flex;
            flex-shrink: 0;

            .sel-box-i {
              flex-shrink: 0;
              width: 14px;
              height: 14px;
              position: relative;
              border: 1px solid #DDDDDD;
              border-radius: 2px;
              margin-right: 6px;

              &.sel {
                border: 1px solid $styleColor;
                background: $styleColor;

                &:after {
                  content: "";
                  width: 8px;
                  height: 3px;
                  border: 2px solid #FFFFFF;
                  border-top: transparent;
                  border-right: transparent;
                  position: absolute;
                  top: 2px;
                  left: 1px;
                  transform: rotate(-45deg);
                }
              }
            }
          }
        }
      }

      .forward-report-msg-box {
        min-height: 0;
        flex: 1;
        overflow-y: auto;
      }
    }

    .dialog-intr {
      color: #666666;
      line-height: 18px;
    }

    .dialog-content-ul {
      padding: 0 16px 0 10px;

      .dialog-content-li {
        display: flex;
        align-items: flex-start;
        margin-top: 10px;

        .dialog-content-li-label {
          flex-shrink: 0;
          line-height: 18px;
          color: #666666;
          padding-top: 6px;

          .dialog-content-li-tips {
            color: $styleColor;
            margin-right: 2px;
          }
        }

        .dialog-content-li-details {
          position: relative;
          flex: 1;
          margin-left: 10px;
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          padding: 6px 10px;
          line-height: 19px;

          &.dialog-content-li-details-input {
            padding: 0 10px;
          }

          &.dialog-content-li-details-textarea {
            position: relative;
            padding: 6px 0 6px 10px;

            .dialog-content-li-textarea {
              padding-right: 10px;
            }

            .textarea-intr {
              line-height: 10px;
              font-size: 12px;
              padding-right: 6px;
              color: #999999;
              text-align: right;
            }
          }

          &:focus-within,
          &:hover {
            border: 1px solid #000000;
          }

          &.dialog-content-li-single-sel-box {
            display: flex;
            align-items: center;

            &:hover {
              .search-arrow {
                &:after {
                  border-color: #000000 transparent transparent transparent;
                }
              }
            }

            &.sel {
              border: 1px solid #000000;

              .dialog-content-li-single-sel-text {
                color: #000000;
              }

              .search-arrow {
                &:after {
                  border-width: 0 4px 4px 4px;
                  border-color: transparent transparent #000000 transparent;
                }
              }
            }

            .dialog-content-li-single-sel-text {
              flex: 1;
              color: #999999;

              &.sel {
                color: #000000;
              }
            }

            .search-arrow {
              position: relative;
              width: 12px;
              height: 100%;
              flex-shrink: 0;

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 0;
                height: 0;
                border-width: 4px 4px 0 4px;
                border-style: solid;
                border-color: #999999 transparent transparent transparent;
              }
            }
          }

          .dialog-content-li-input,
          .dialog-content-li-textarea {
            width: 100%;

            &::placeholder {
              color: #999999;
            }
          }

          .dialog-content-li-input {
            line-height: 27px;
          }

          .dialog-content-li-single-sel {
            position: absolute;
            top: 32px;
            left: 0;
            width: 100%;
            max-height: 120px;
            overflow-y: auto;
            background: #FFFFFF;
            box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            z-index: 1;

            li {
              position: relative;
              height: 30px;
              line-height: 30px;
              padding-left: 9px;

              &:hover {
                background: #F5F5F5;
              }

              &.sel {
                color: $styleColor;
                font-weight: bold;

                &:after {
                  content: "";
                  position: absolute;
                  top: 10px;
                  right: 10px;
                  width: 8px;
                  height: 4px;
                  border-left: 2px solid $styleColor;
                  border-bottom: 2px solid $styleColor;
                  transform: rotate(-45deg);
                }
              }
            }
          }
        }
      }
    }

    &.team-file-box {
      &.doc-box {
        .team-file-user {
          width: calc(100% - 300px) !important;
        }
      }

      .team-file-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #999999;
        margin-bottom: 12px;

        .team-file-input-box {
          display: flex;
          align-items: center;
          width: 281px;
          height: 29px;
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          padding-left: 26px;
          font-size: 13px;
          background: #FFFFFF url("/img/search/icon_search.png") no-repeat 7px center;
          background-size: 16px 16px;

          &:focus-within,
          &:hover {
            border: 1px solid #000000;
          }

          .search-input {
            flex: 1;
          }

          .icon-close {
            width: 12px;
            height: 12px;
            margin: 0 10px;
            background-image: url("/img/search/close.png");
            background-repeat: no-repeat;
            background-size: 12px 12px;
            cursor: pointer;
          }
        }
      }

      .team-file-content {
        height: 216px;
        border: 1px solid #E0E0E0;

        .team-file-list-ul {
          font-size: 13px;

          &.team-file-list-header {
            height: 32px;
            font-size: 13px;
            font-weight: bold;
            background: #F0F0F0;

            .team-file-list-li {
              height: 100%;
            }
          }

          &.team-file-list-content {
            height: calc(100% - 32px);
            overflow-y: auto;

            .team-file-list-li {
              cursor: pointer;
            }
          }

          .team-file-list-li {
            display: flex;
            align-items: center;
            height: 28px;

            .team-file-cell {
              display: flex;
              align-items: center;
              height: 100%;
              border-right: 1px solid #E0E0E0;
              border-bottom: 1px solid #E0E0E0;
              padding: 0 10px;

              &:last-child {
                border-right: none;
              }
            }

            .team-file-name {
              flex-shrink: 0;
              width: 300px;

              .sel-box-i {
                flex-shrink: 0;
                width: 14px;
                height: 14px;
                position: relative;
                border: 1px solid #BCBCBC;
                border-radius: 2px;
                margin-right: 8px;

                &.sel {
                  border: 1px solid $styleColor;
                  background: $styleColor;

                  &:after {
                    content: "";
                    width: 8px;
                    height: 3px;
                    border: 2px solid #FFFFFF;
                    border-top: transparent;
                    border-right: transparent;
                    position: absolute;
                    top: 2px;
                    left: 1px;
                    transform: rotate(-45deg);
                  }
                }
              }

              .file-icon {
                width: 16px;
                margin-right: 8px;
                flex-shrink: 0;
              }

              .file-name {
                flex: 1;
                min-width: 0;
              }
            }

            .team-file-user {
              flex-shrink: 0;
              width: 160px;
            }

            .team-file-size {
              flex-shrink: 0;
              width: 70px;
            }

            .team-file-time {
              flex-shrink: 0;
              width: calc(100% - 300px - 160px - 70px);
            }
          }
        }

        .team-file-none {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: calc(100% - 32px);
        }
      }

      .team-file-footer {
        display: flex;
        align-items: center;
        font-size: 13px;
        margin-top: 10px;

        .highlight {
          margin-right: 4px;
        }
      }
    }

    &.dialog-hait-content {
      display: flex;
      width: 100%;
      height: 442px;
      border: 1px solid #E0E0E0;
      border-radius: 4px;

      .sel-box-i {
        flex-shrink: 0;
        width: 14px;
        height: 14px;
        position: relative;
        border: 1px solid #BCBCBC;
        border-radius: 2px;
        margin-right: 10px;

        &.sel {
          border: 1px solid $styleColor;
          background: $styleColor;

          &:after {
            content: "";
            width: 8px;
            height: 3px;
            border: 2px solid #FFFFFF;
            border-top: transparent;
            border-right: transparent;
            position: absolute;
            top: 2px;
            left: 1px;
            transform: rotate(-45deg);
          }
        }

        &.disabled {
          border: 1px solid #DDDDDD;
          background: $styleBg1Hover;

          &:after {
            content: "";
            width: 8px;
            height: 3px;
            border: 2px solid #DDDDDD;
            border-top: transparent;
            border-right: transparent;
            position: absolute;
            top: 2px;
            left: 1px;
            transform: rotate(-45deg);
          }
        }
      }

      .user-avatar-box {
        height: 20px !important;
        width: 20px !important;
        margin-right: 6px;

        .avatar-box {
          cursor: default;
        }
      }

      .sel-user-box {
        overflow: auto;
        flex: 1;
        margin: 6px 0;

        .sel-user-ul {
          margin: 0;
        }
      }

      .sel-user-ul {
        position: relative;
        width: 100%;
        flex: 1;
        overflow: auto;
        margin: 6px 0;
        font-size: 13px;

        li {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
          height: 30px;
          padding: 0 32px 0 16px;

          &.close-li {
            padding-right: 31px;

            .close {
              position: absolute;
              top: 50%;
              right: 11px;
              transform: translateY(-50%);
              background: url("/img/close.png") no-repeat center;
              background-size: 10px 10px;
              height: 100%;
              width: 20px;
              cursor: pointer;
            }
          }

          &:not(.user-none,.user-none-tips,.sel-initial):hover {
            background: $styleBg1Hover;
          }

          &.user-none {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;

            .loading-img {
              width: 32px;
            }
          }

          &.user-none-tips {
            margin-top: 88px;
            height: 67px;
            background: url("/img/index/hait_sel_tips.png") no-repeat center;
            background-size: 166px 67px;
          }

          &.sel-initial {
            font-size: 12px;
            height: 25px;
            align-items: flex-end;
            color: #666666;
          }
        }
      }

      .sel-user-initial {
        position: absolute;
        top: 64px;
        right: 4px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        width: 42px;
        height: calc(100% - 80px);
        padding-left: 4px;

        li {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          font-size: 10px;
          color: #999999 !important;

          &:hover {
            color: $styleColor !important;
          }
        }
      }

      .user-left-box,
      .user-right-box {
        position: relative;
        width: 50%;
      }

      .user-left-box {
        display: flex;
        flex-direction: column;
        border-right: 1px solid #E0E0E0;

        .search-box {
          position: relative;
          margin: 16px 16px 0;

          input {
            width: 100%;
            height: 30px;
            padding: 0 30px;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            background-image: url("/img/search/icon_search.png");
            background-repeat: no-repeat;
            background-size: 16px 16px;
            background-position: 7px;

            &:hover,
            &:focus {
              border: 1px solid #666666;
            }
          }

          .icon-close {
            position: absolute;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: url("/img/search/close.png") no-repeat;
            background-size: 12px;
            cursor: pointer;
          }
        }
      }

      .user-right-box {
        .tips-box {
          display: grid;
          align-items: center;
          width: 100%;
          height: 17px;
          color: #999999;
          margin: 16px 0 13px;
          padding: 0 16px;
        }

        .sel-user-tips {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 16px;
          color: #333333;
          font-weight: bold;
          line-height: 17px;

          &.show {
            margin-top: 30px;
          }

          .sel-user-text {
            display: flex;
            align-items: center;

            .highlight {
              margin: 0 4px;
            }
          }
        }

        .sel-user-ul {
          height: calc(100% - 17px - 29px - 17px - 12px);
        }
      }
    }
  }

  .quick-acting-box{
    :deep(.content){
      padding: 10px 16px;
    }
  }

  .quick-acting-box{
    :deep(.content){
      padding: 10px 16px;
    }
  }
}
</style>
