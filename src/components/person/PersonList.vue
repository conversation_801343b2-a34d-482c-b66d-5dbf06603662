<template>
  <div class="person-list">
    <div class="combo-select" @click="openDropDown(true)">
      <select tabindex="-1">
        <option
          :value="item.compayId"
          v-for="item in informationList"
          :key="item.compayId"
        >
          {{ item.compayName }}
        </option>
      </select>
      <div
        :class="{ 'combo-arrow': true, 'show-dropdown': showDropdown }"
        @click.stop="openDropDown(!showDropdown)"
      ></div>
      <ul class="combo-dropdown" v-show="showDropdown">
        <li
          v-for="item in informationList"
          :key="item.compayId"
          class="option-item option-selected option-hover"
          @click.stop="clickOption(item)"
        >
          {{ item.compayName }}
        </li>
      </ul>
      <input
        type="text"
        placeholder=""
        v-model="qkey"
        class="combo-input text-input"
        @input="qkeyInput"
      />
    </div>
    <div class="find-people-list">
      <dl
        v-for="(item, index) in clickItem.groupList"
        :key="index"
        :class="{
          active:
            openItem[clickItem.compayId] &&
            openItem[clickItem.compayId].indexOf(index) > -1,
        }"
      >
        <dt @click="openItemFunc(index)"><i></i>{{ item.deptName }}</dt>
        <dd
          :class="{
            curr:
              openItem[clickItem.compayId] &&
              openItem[clickItem.compayId].indexOf(index) > -1,
            active:
              clickItem.compayId &&
              choiceItem[clickItem.compayId] &&
              choiceItem[clickItem.compayId].id === item1.id,
          }"
          v-for="item1 in item.spawVoList"
          :key="item1.id"
          @click="choiceItemFunc(item1)"
          @dblclick="toChat(item1)"
        >
          <div class="find-person">
            <div class="find-person-img-box">
              <div class="find-person-img">
                <img
                  :src="item1.headPic"
                  :onerror="avatarError.bind(this, 'p2p', item1.workerNo, '')"
                />
              </div>
              <img class="user-label" :src="$store.getters.getPersons(item1.workerNo).medalUrl"  :onerror="hideElm"/>
            </div>
            <div class="find-person-txt">
              <p class="friend-name">{{ item1.nickname }}</p>
              <p class="friend-sign">{{ item1.obligation }}</p>
            </div>
          </div>
        </dd>
      </dl>
    </div>
  </div>
</template>
<script>
import { computed, ref, watch, inject } from "vue";
import { useStore } from "vuex";
import { avatarError,hideElm } from "@utils";
export default {
  setup() {
    const store = useStore();
    const findPersonList = computed(() => {
      return store.getters.getFindPersons();
    });
    let qkey = ref("");
    let showDropdown = ref(false);
    let informationList = ref(findPersonList.value.information);
    let clickItem = inject("clickItem"); //当前选择的东西
    let choiceItem = inject("choiceItem"); //所有页签选中的人
    qkey.value =
      informationList.value && Array.isArray(informationList.value)
        ? informationList.value[0].compayName
        : "";
    clickItem.value =
      informationList.value && Array.isArray(informationList.value)
        ? informationList.value[0]
        : { groupList: [] };

    let openItem = ref({}); //打开的页签
    //监听全局点击
    watch(
      () => store.state.emit.click,
      (newValue, oldValue) => {
        if (
          newValue &&
          newValue.target &&
          (/combo-select/.test(newValue.target.className) ||
            newValue.path.some((elm) => /combo-select/.test(elm.className)))
        ) {
          return;
        }
        showDropdown.value = false;
      },
      {
        deep: true,
      }
    );
    function clickOption(item) {
      qkey.value = item.compayName;
      clickItem.value = item;
      showDropdown.value = false;
    }
    function qkeyInput() {
      informationList.value = findPersonList.value.information.filter((i) => {
        return i.compayName.indexOf(qkey.value) > -1;
      });
    }
    function openDropDown(val) {
      showDropdown.value = val;
      if (val) {
        informationList.value = findPersonList.value.information;
      }
    }
    //打开页签-关闭页签
    function openItemFunc(index) {
      let t_index = Array.isArray(openItem.value[clickItem.value.compayId])
        ? openItem.value[clickItem.value.compayId].indexOf(index)
        : -2;
      if (t_index > -1) {
        openItem.value[clickItem.value.compayId].splice(t_index, 1);
      } else if (t_index === -2) {
        openItem.value[clickItem.value.compayId] = [index];
      } else {
        openItem.value[clickItem.value.compayId].push(index);
      }
    }
    //选中某个人
    function choiceItemFunc(item) {
      let res = store.getters.getPersons(item.workerNo);
      item["selfIntro"] = res.selfIntro;
      choiceItem.value[clickItem.value.compayId] = item;
    }
    //前往聊天
    function toChat(item) {
      store.dispatch("setCurrentSession", { id: "p2p-" + item.workerNo });
    }
    return {
      avatarError,
      toChat,
      choiceItem,
      choiceItemFunc,
      openItemFunc,
      openItem,
      qkeyInput,
      qkey,
      clickOption,
      findPersonList,
      showDropdown,
      informationList,
      openDropDown,
      clickItem,
      hideElm
    };
  },
};
</script>
<style lang="scss" scoped>
.person-list {
  height: 100%;
  .combo-select {
    border-bottom: 1px solid #dfe1e4;
    border-top: 1px solid #dfe1e4;
    border-left: none;
    border-radius: 0;
    background-color: #fff;
    font-size: 12px;
    position: relative;
    float: left;
    width: 100%;
    height: 29px;
    select {
      left: -1px;
      top: -1px;
      width: 0;
      height: 0;
      margin: 0;
      position: absolute;
      opacity: 0;
    }
    .combo-arrow {
      background-color: transparent;
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      cursor: pointer;
      text-align: center;
      font-size: 14px;
      width: 40px;
      color: #999999;
      &.show-dropdown::before {
        border-bottom: 5px solid #555;
        border-top: 0;
      }
      &::before {
        content: " ";
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-top: 5px solid #555;
        display: block;
        width: 0;
        height: 0;
        top: 0;
        right: 15px;
        bottom: 0;
        position: absolute;
        margin: auto 0;
      }
    }
    .combo-dropdown {
      margin-top: -1px;
      margin-left: 0;
      max-height: 400px;
      font-size: 14px;
      border-radius: 0;
      position: absolute;
      z-index: 5;
      top: 100%;
      left: 0;
      min-width: 100%;
      width: 100%;
      padding: 0;
      overflow-y: auto;
      overflow-x: hidden;
      background: #f8f8f8;
      border: 1px solid #c0c0c0;
      box-sizing: border-box;
      .option-item {
        background: #f8f8f8;
        padding-left: 14px;
        line-height: 40px;
        color: #000;
        list-style: none;
        padding: 0px 5px;
        margin: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: list-item;
        &:hover {
          background: #cacdd3;
        }
      }
    }
    .combo-input {
      margin-bottom: 0;
      padding-left: 14px;
      font-size: 14px;
      border: none;
      box-shadow: none;
      background-color: transparent;
      height: 100%;
      cursor: auto;
      -webkit-user-select: text;
      color: #000;
    }
  }
  .find-people-list {
    width: 100%;
    overflow: auto;
    height: calc(100% - 30px);
    dl {
      &.active {
        dt {
          i {
            transform: rotate(90deg);
          }
        }
      }
      dt {
        font-weight: 400;
        font-size: 15px;
        color: #000;
        line-height: 44px;
        padding-left: 13px;
        cursor: default;
        i {
          float: left;
          width: 16px;
          height: 16px;
          margin: 14px 4px 0 0;
          background: url(/img/people_icon.png) no-repeat;
          background-size: 16px 16px;
          transition: all 0.2s;
        }
      }
      dd {
        display: none;
        &.curr {
          display: block;
        }
        &.active {
          background: #c4c4c5 !important;
        }
        &:hover {
          background: #dfdedd;
        }
        .find-person {
          display: flex;
          height: 40px;
          padding: 9.5px 0 9.5px 13px;
          overflow: hidden;
          box-sizing: content-box;
          .find-person-img-box {
            width: 38px;
            height: 38px;
            position: relative;
            margin-right: 10px;
            .find-person-img {
              width: 38px;
              height: 38px;
              overflow: hidden;
              margin-right: 10px;
              border-radius: 3px;
              box-shadow: 0 0 3px #a8a79f;
              img {
                width: 40px;
                position: relative;
              }
            }
          }
          .user-label {
            max-width: 46px;
            height: 12px;
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
          }
          .find-person-txt {
            .friend-name {
              font-size: 15px;
              line-height: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #000;
            }
            .friend-sign {
              font-size: 12px;
              color: #818589;
              line-height: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
}
</style>