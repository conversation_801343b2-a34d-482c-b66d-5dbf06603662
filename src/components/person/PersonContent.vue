<template>
  <div class="person-content">
    <div class="buscard" v-if="showData.workerNo">
      <div class="card-box">
        <div class="card-right">
          <img
            id="cardPics"
            :src="showData.headPic"
            :onerror="avatarError.bind(this, 'p2p', showData.workerNo, '')"
          />
        </div>
        <div class="card-left0">
          <strong>{{ showData.nickname }}</strong>
          <ul class="per_mas_list">
            <li style="display: list-item">
              中心：<span class="con">{{ showData.centreName }}</span>
            </li>
            <li style="display: list-item">
              职责：<span class="con">{{ showData.obligation }}</span>
            </li>
          </ul>
        </div>
      </div>

      <div class="clearfix card-bottom">
        <span>描述：</span
        ><span class="con">{{
          showData.selfIntro ? showData.selfIntro : "暂无职责说明"
        }}</span>
      </div>
      <div class="clearfix">
        <span
          class="open-chat"
          style="cursor: default"
          @click="toChat(showData)"
          >发起聊天</span
        >
      </div>
    </div>
    <div class="tip-txt" v-else>
      <img src="/img/buscard-tip.png" width="200" height="132" />
      <p>
        有咨询请先对接您的店长/主任 ，仍无法解决的，<br />由店长/主任对接您无法查找到的总部人员。
      </p>
    </div>
  </div>
</template>
<script>
import { computed, inject } from "vue";
import { useStore } from "vuex";
import { avatarError } from "@utils";
export default {
  setup() {
    const store = useStore();
    let clickItem = inject("clickItem"); //当前选择的东西
    let choiceItem = inject("choiceItem"); //所有页签选中的人
    let showData = computed(() => {
      return choiceItem.value[clickItem.value.compayId] || {};
    });
    //前往聊天
    function toChat(item) {
      store.dispatch("setCurrentSession", { id: "p2p-" + item.workerNo });
    }
    return { clickItem, choiceItem, showData, toChat, avatarError };
  },
};
</script>
<style lang="scss" scoped>
.person-content {
  height: 100%;
  width: 100%;
  .buscard {
    width: 324px;
    margin: 79px auto;
    position: relative;
    .card-box {
      padding-bottom: 20px;
      overflow: hidden;
      border-bottom: 1px solid #cfcfcf;
      position: relative;
      padding-right: 100px;
      .card-right {
        position: absolute;
        top: 0;
        right: 0;
        width: 80px;
        height: 80px;
        border-radius: 3px;
        overflow: hidden;
        border: 1px solid #ddd;
        img {
          display: block;
          width: 82px;
        }
      }
      .card-left0 {
        width: 224px;
        strong {
          font-weight: 400;
          display: block;
          font-size: 22px;
          line-height: 30px;
          color: #000;
        }
        .per_mas_list {
          margin-top: 15px;
          margin-left: 0px;
          li {
            width: 100%;
            line-height: 18px;
            padding-bottom: 3px;
            color: #999;
            letter-spacing: 1px;
          }
        }
      }
    }
    .card-bottom {
      display: flex;
      float: left;
      width: 100%;
      line-height: 18px;
      margin-top: 15px;
      color: #999;
      overflow: hidden;
      height: 88px;
      > span:first-child {
        flex: 1.2;
      }
      > span:last-child {
        flex: 9;
        overflow: auto;
      }
    }
    .open-chat {
      font-size: 16px;
      display: block;
      width: 140px;
      height: 40px;
      margin: 120px auto 20px;
      line-height: 40px;
      color: #fff;
      text-align: center;
      border-radius: 2px;
      background: #169ada;
      &:hover {
        background: #1079ab;
      }
    }
  }
  .tip-txt {
    font-size: 14px;
    height: 100%;
    line-height: 22px;
    color: #000000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    img {
      display: block;
      margin: 0 auto 22px;
    }
    p {
      margin-bottom: 60px;
    }
  }
}
</style>