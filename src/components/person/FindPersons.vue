<template>
  <Layout>
    <template v-slot:layout-left>
      <PersonList></PersonList>
    </template>
    <template v-slot:layout-right>
      <PersonContent></PersonContent>
    </template>
  </Layout>
</template>
<script>
import Layout from "../layout/index.vue";
import PersonList from "./PersonList";
import PersonContent from "./PersonContent";
import { provide, ref } from "vue";
export default {
  components: { Layout, PersonList, PersonContent },

  setup() {
    let clickItem = ref({});
    let choiceItem = ref({});
    provide("clickItem", clickItem);
    provide("choiceItem", choiceItem);
    return {};
  },
};
</script>
<style lang="scss" scoped>
</style>