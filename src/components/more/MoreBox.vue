<template>
  <div class="more">
    <div class="more-title">工作台</div>
    <ul class="classify-tab-ul" @wheel="scrollSessionTab">
      <li class="classify-tab-li" :class="{'sel':workObj.mainTabKey==0}" @click="toggleKey(1,0)">常用应用</li>
      <li class="classify-tab-li" :class="{'sel':workObj.mainTabKey==1}" @click="toggleKey(1,1)">乐聊工具</li>
    </ul>
    <div class="more-btn-box">
      <span class="more-btn-helper" @click="toAppUrl({requestUrl: 'https://i.leyoujia.com/lyjEtherpad/p/3cbc7b07-4e85-451d-9dfc-b6377921dd14?docId=7710999'})">帮助</span>
      <span class="more-btn-add" v-if="workObj.allFlag" @click="toggleShowAppModal(true,1)">添加应用</span>
    </div>
    <ul class="more-box-ul">
      <!-- 网络提示 -->
      <NetTips></NetTips>
      <!--常用应用-->
      <li v-show="workObj.mainTabKey==0" v-if="workObj.allFlag" class="more-box-li">
        <div class="normal-box classify-box">
          <ul v-if="workObj.normalList.length > 0" class="classify-box-ul">
            <li class="classify-box-li" :class="{'hover':item.textmenuEnter}" v-for="(item, key) in workObj.normalList" key="item.id" @click="toAppUrl(item)">
              <div class="classify-icon-box">
                <img class="classify-icon" :src="getAppIcon(item)" :onerror="errorIcon" alt="">
              </div>
              <div class="classify-text textEls" :title="item.name">{{ item.name.slice(0, 5) }}{{ item.name.length > 5 ? "..." : "" }}</div>
              <i class="edit" @click.stop="showEdit($event, item, 0, true)" @mouseenter="showEdit($event, item, 0, true)" @mouseleave="showEdit($event, item, 0, false)"></i>
            </li>
          </ul>
          <div v-else class="none-tips">暂无数据，点击<span class="highlight" @click="toggleShowAppModal(true,1)">【添加常用】</span>添加常用应用吧</div>
        </div>
        <div class="more-box-title" v-if="workObj.defaultList.length>0">全部应用</div>
        <div class="common-box" v-if="workObj.defaultList.length>0">
          <ul class="classify-tab-ul" @wheel="scrollSessionTab">
            <li class="classify-tab-li" :class="{'sel':key==workObj.appTabKey}" v-for="(item, key) in workObj.defaultList" :key="item.id" @click="toggleKey(2,key)">{{ item.name }}</li>
          </ul>
          <div class="classify-box" v-for="(item, key) in workObj.defaultList" :key="item.id" v-show="key==workObj.appTabKey">
            <ul v-if="item.childList?.length>0" class="classify-box-ul">
              <li class="classify-box-li" :class="{'hover':item.textmenuEnter}" v-for="(item,key) in item.childList" :key="item.id" @click="toAppUrl(item)">
                <div class="classify-icon-box">
                  <img class="classify-icon" :src="getAppIcon(item)" :onerror="errorIcon" alt="">
                </div>
                <div class="classify-text textEls" :title="item.name">{{ item.name.slice(0, 5) }}{{ item.name.length > 5 ? "..." : "" }}</div>
                <i class="edit" @click.stop="showEdit($event, item, 1, true)" @mouseenter="showEdit($event, item, 1, true)" @mouseleave="showEdit($event, item, 1, false)"></i>
              </li>
            </ul>
            <div v-else class="none-tips">
              <span>暂无数据</span>
            </div>
          </div>
        </div>
      </li>
      <li v-show="workObj.mainTabKey==0" v-if="!workObj.allFlag" class="more-box-li">
        <div v-show="!workObj.normalListLoading" class="error-data-box">
          <img class="error-data-img" src="/img/workbench/error_data.png" alt="">
          <div class="error-data-text">数据加载失败，请重新加载</div>
          <div class="error-data-btn" @click="initAppDataApi()">重新加载</div>
        </div>
        <div v-show="workObj.normalListLoading" class="loading-box">
          <img class="loading-img" src="/img/waitting.gif" alt="">
        </div>
      </li>
      <!--乐聊工具-->
      <li v-show="workObj.mainTabKey==1" class="more-box-li">
        <ul class="classify-box-ul">
          <li class="classify-box-li" @click="openTools(1)">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_remote.png" alt="">
            </div>
            <div class="classify-text">远程工具</div>
            <div class="progress-box" v-if="downloadToolsObj[1]&&downloadToolsObj[1].percentage">
              <i class="progress-close" @click.stop="cancelTools(1)">×</i>
              <progress max="100" :value="downloadToolsObj[1].percentage"></progress>
            </div>
          </li>
          <li class="classify-box-li" @click="openTools(2)">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_clear.png" alt="">
            </div>
            <div class="classify-text">文件清理</div>
          </li>
          <li class="classify-box-li" @click="clockIn(1)" v-if="userInfo.empType==1">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_clock.png" alt="">
            </div>
            <div class="classify-text">打卡</div>
          </li>
          <li class="classify-box-li" @click="attendance(true)" v-if="userInfo.empType==1">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_attend.png" alt="">
            </div>
            <div class="classify-text">考勤登记</div>
          </li>
          <li class="classify-box-li" @click="openChildWin('netConnectNew')" v-if="userInfo.empType==1">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_net.png" alt="">
            </div>
            <div class="classify-text">公司电脑认证</div>
          </li>
          <li class="classify-box-li" @click="openChildWin('netConnectTemp')" v-if="workObj.isShowNetTemp">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_net_temp.png" alt="">
            </div>
            <div class="classify-text">外部访问申请</div>
          </li>
          <li class="classify-box-li" @click="downloadPrint()">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_print.png" alt="">
            </div>
            <div class="classify-text">打印控件</div>
          </li>
          <li class="classify-box-li" @click="changeVpnFlag()" v-if="workObj.vpnInfo.isShowVpn">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" :src="'/img/workbench/tools_vpn_'+(workObj.vpnInfo.vpnFlag?'on':'off')+'.png'" alt="">
            </div>
            <div class="classify-text">切换{{ workObj.vpnInfo.vpnFlag ? "正常" : "专属" }}线路</div>
          </li>
          <li class="classify-box-li" @click="openChildWin('netDetect')">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_net_detect.png" alt="">
            </div>
            <div class="classify-text">网络检测</div>
          </li>
          <li class="classify-box-li" @click="showDialog(1)">
            <div class="classify-icon-box no-border">
              <img class="classify-icon" src="/img/workbench/tools_record.png" alt="">
            </div>
            <div class="classify-text">聊天记录提取</div>
          </li>
        </ul>
      </li>
    </ul>

    <!--考勤登记弹窗-->
    <div class="attendance-modal" v-show="attendanceObj.flag">
      <div class="attendance-box">
        <img class="attendance-close" src="/img/close.png" @click="attendance(false)">
        <div class="attendance-title-box">
          <i class="attendance-title-icon"></i>
          <span>考勤登记</span>
        </div>
        <div class="attendance-time">当前时间：{{ attendanceObj.time }}</div>
        <div class="attendance-user-box">
          <div class="attendance-user-info-box">
            <label class="attendance-user-label" for="attendanceName"><span>员</span><span>工</span></label>
            <span>：</span>
            <div class="attendance-user-input-box">
              <input id="attendanceName" type="text" placeholder="请输入姓名检索..." @keyup.enter="selUser(attendanceObj.searchIndex)" @keydown.up.prevent="selPre" @keydown.down.prevent="selNext"
                     autocomplete="off" v-model.trim="attendanceObj.name" @input="nameInput" @focus="nameFocus">
              <div class="search-box" v-show="attendanceObj.name.length > 0 && !attendanceObj.searchFlag && !attendanceObj.searchReg" ref="searchBoxRef">
                <ul v-if="attendanceObj.searchList && attendanceObj.searchList.length > 0">
                  <li @mousedown="selUser(key, 1)" v-for="(item,key) in attendanceObj.searchList" :key="item.workerNo" :class="attendanceObj.searchIndex==key?'curr':''"
                      :title="item.deptName+'---'+item.workerName">
                    {{ item.deptName }}---{{ item.workerName }}
                  </li>
                </ul>
                <ul v-else>
                  <li>没有数据</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="attendance-user-info-box">
            <span class="attendance-user-label" for="attendancePwd"><span>登</span><span>录</span><span>密</span><span>码</span></span>
            <span>：</span>
            <div class="attendance-user-input-box attendance-user-input-box-pwd">
              <input id="attendancePwd" :type="showPwdFlag?'text':'password'" v-model="attendanceObj.pwd" placeholder="请输入密码" maxlength="20" ref="attendancePwdRef">
              <div class="show-pwd icon-pwd" :class="showPwdFlag?'show':''" @click="toggleShowPwd()"></div>
            </div>
          </div>
        </div>
        <div class="attendance-btn-box">
          <div class="attendance-btn" @click="attendance(false)">退出</div>
          <div class="attendance-btn-group">
            <div class="attendance-btn" @click="attendanceReset()">重置</div>
            <div class="attendance-btn attendance-save" @click="attendanceSave()">保存</div>
          </div>
        </div>
      </div>
    </div>

    <!--添加应用弹窗-->
    <div class="add-app-modal win-drag win-no-resize" v-show="appObj.flag">
      <div class="add-app-box win-no-drag">
        <img class="add-app-close" src="/img/close.png" @click="toggleShowAppModal(false,1)">
        <div class="add-app-title">添加应用</div>
        <div class="add-app-tab-box">
          <ul class="add-app-tab-ul">
            <li class="add-app-tab-li" :class="{'sel':appObj.key==0}" @click="toggleKey(3,0)">常用应用</li>
            <li class="add-app-tab-li" :class="{'sel':appObj.key==2}" @click="toggleKey(3,2)">顶部栏应用</li>
          </ul>
          <div class="add-app-tab-right">
            <div class="add-app-default-add" v-show="appObj.key==0" @click="setAddAppType(5)">添加自定义链接</div>
          </div>
        </div>
        <ul class="add-app-content-ul">
          <!--常用应用-->
          <li class="add-app-content-li" v-show="appObj.key==0">
            <div class="add-app-normal-box add-app-normal-left">
              <div class="add-app-normal-header">
                <div class="default-search-box">
                  <input class="default-search" type="text" placeholder="请输入应用名称" v-model.trim="appObj.val" @input="searchApp">
                  <span class="icon-close" v-show="appObj.val.length>0" @click="searchApp('reset')"></span>
                </div>
              </div>
              <div class="add-app-normal-content">
                <!--请求状态-->
                <div v-if="appObj.diffLoading" class="loading-img-box">
                  <img class="loading-img" src="/img/waitting.gif" alt="">
                </div>
                <!--请求结束-->
                <ul v-else-if="appObj.showList.length>0" class="app-sel-ul">
                  <li class="app-sel-li" :class="{'sel':appObj.selMap[item.code]}" v-for="(item,key) in appObj.showList" :key="item.id" @click="toggleSelApp(item, 'sel')">
                    <i class="app-sel-icon"></i>
                    <div class="app-img-box">
                      <img :src="getAppIcon(item)" :onerror="errorIcon" alt="">
                    </div>
                    <span class="app-name textEls" v-html="getHighlight(htmlEscapeAll(item.name), appObj.val)"></span>
                  </li>
                </ul>
                <div v-else-if="appObj.showList.length==0" class="show-text">暂无搜索结果，<br>换个条件试试吧</div>
              </div>
            </div>
            <div class="add-app-normal-box add-app-normal-right">
              <div class="add-app-normal-header">
                <div class="add-app-normal-info-box">
                  <span class="add-app-normal-info-title">我常用的</span>
                  <span v-show="appObj.list.length>0">已选 <span class="highlight">{{ appObj.list.length }}</span>  个</span>
                </div>
              </div>
              <div class="add-app-normal-content">
                <ul v-if="appObj.list.length>0" class="app-sel-ul">
                  <li class="app-sel-li" :class="{'move':item.code==appObj.moveItem.code,'move-status':appObj.moveItem.code}"
                      v-for="(item,key) in appObj.list" :key="item.id"
                      :draggable="true" @dragstart="appDrag($event,1,item)" @dragenter="appDrag($event,2,item)" @dragover="appDrag($event,3,item)" @dragend="appDrag($event,4,item)">
                    <i class="app-move-icon"></i>
                    <div class="app-img-box">
                      <img :src="getAppIcon(item)" :onerror="errorIcon" alt="">
                    </div>
                    <span class="app-name app-name1 textEls">{{ item.name }}</span>
                    <img class="app-close-icon" src="/img/workbench/icon_close.png" @click="toggleSelApp(item, 'selDel')">
                  </li>
                </ul>
                <div v-else class="show-text">点击选择左侧应用后，<br>即可加入我常用的应用</div>
              </div>
            </div>
          </li>
          <!--自定义、顶部栏、侧边栏、公共应用-->
          <li class="add-app-content-li" v-show="appObj.key==1||appObj.key==2||appObj.key==3||appObj.key==4">
            <div class="add-app-default-box">
              <div class="add-app-default-header" :class="{'add-app-default-header-border':appObj.key==2||appObj.key==3}">
                <div class="add-app-default-search-box default-search-box">
                  <input class="default-search" type="text" :placeholder="appObj.key==1?'搜索已添加的自定义应用':'请输入应用名称'" v-model.trim="appObj.val" @input="searchApp">
                  <span class="icon-close" v-show="appObj.val.length>0" @click="searchApp('reset')"></span>
                </div>
                <div class="add-app-default-add" @click="toggleShowAppModal(true,2)">添加{{ appObj.key == 1 ? "自定义" : "" }}应用</div>
              </div>
              <!--请求状态-->
              <div v-if="appObj.loading" class="add-app-default-content">
                <div class="loading-img-box">
                  <img class="loading-img" src="/img/waitting.gif" alt="">
                </div>
              </div>
              <!--请求结束-->
              <div v-if="!appObj.loading" class="add-app-default-content" v-show="appObj.key!=4">
                <div v-if="appObj.list.length>0" class="add-app-default-ul-box">
                  <div class="add-app-default-ul-title">当前已添加的应用：</div>
                  <ul class="app-sel-ul">
                    <li class="app-sel-li" :class="{'move':item.id==appObj.moveItem.id,'move-status':appObj.moveItem.id}"
                        v-for="(item,key) in appObj.list" :key="item.id"
                        :draggable="true" @dragstart="appDrag($event,1,item)" @dragenter="appDrag($event,2,item)" @dragover="appDrag($event,3,item)" @dragend="appDrag($event,4,item)">
                      <i class="app-move-icon" v-show="!appObj.val"></i>
                      <div class="app-img-box">
                        <img :src="getAppIcon(item)" :onerror="errorIcon" alt="">
                      </div>
                      <span class="app-name app-name1 textEls" v-html="getHighlight(htmlEscapeAll(item.name), appObj.val)"></span>
                      <i class="app-edit-icon" @click="editApp(item, appObj.key)"></i>
                      <img class="app-close-icon" src="/img/workbench/icon_close.png" @click="removeSelApp(item, appObj.key)">
                    </li>
                  </ul>
                </div>
                <div v-else-if="appObj.val&&appObj.list.length==0" class="show-text">暂无搜索结果，换个条件试试吧</div>
                <div v-else-if="!appObj.val&&appObj.list.length==0" class="none-tips">
                  暂无数据，点击<span class="highlight" @click="toggleShowAppModal(true,2)">【添加应用】</span>添加{{ appObj.key == 1 ? "自定义" : appObj.key == 2 ? "顶部栏" : "侧边栏" }}应用
                </div>
              </div>
              <!--公共应用-->
              <div v-if="!appObj.loading" class="add-app-default-content" v-show="appObj.key==4">
                <div v-if="workObj.publicList.length>0" class="public-app-box">
                  <ul class="public-app-left">
                    <li class="public-app-list textEls" :class="{'sel':key==appObj.selKey}" v-for="(item, key) in appObj.publicList" :key="item.id" @click="toggleKey(4,key)">
                      {{ item.name }}
                    </li>
                  </ul>
                  <div class="public-app-right">
                    <!--请求状态-->
                    <div v-if="appObj.diffLoading" class="loading-img-box">
                      <img class="loading-img" src="/img/waitting.gif" alt="">
                    </div>
                    <!--请求结束-->
                    <ul v-else-if="appObj.list.length>0" class="app-sel-ul">
                      <li class="app-sel-li" :class="{'move':item.id==appObj.moveItem.id,'move-status':appObj.moveItem.id}"
                          v-for="(item,key) in appObj.list" :key="item.id"
                          :draggable="true" @dragstart="appDrag($event,1,item)" @dragenter="appDrag($event,2,item)" @dragover="appDrag($event,3,item)" @dragend="appDrag($event,4,item)">
                        <i class="app-move-icon" v-show="!appObj.val"></i>
                        <div class="app-img-box">
                          <img :src="getAppIcon(item)" :onerror="errorIcon" alt="">
                        </div>
                        <span class="app-name app-name1 textEls" v-html="getHighlight(htmlEscapeAll(item.name), appObj.val)"></span>
                        <i class="app-edit-icon" @click="editApp(item, appObj.key)"></i>
                        <img class="app-close-icon" src="/img/workbench/icon_close.png" @click="removeSelApp(item, appObj.key)">
                      </li>
                    </ul>
                    <div v-else-if="appObj.val&&appObj.list.length==0" class="show-text">暂无搜索结果，换个条件试试吧</div>
                    <div v-else-if="!appObj.val&&appObj.list.length==0" class="none-tips" @click="toggleShowAppModal(true,2)">暂无数据，点击<span class="highlight">【添加应用】</span>添加公共应用</div>
                  </div>
                </div>
                <div v-else class="none-tips">网络异常请<span class="highlight">重试</span></div>
              </div>
            </div>
          </li>
        </ul>
        <div class="add-app-btn-box">
          <div class="add-app-btn" @click="toggleShowAppModal(false,1)">取消</div>
          <div class="add-app-btn add-app-btn-save" @click="confirmApp(1)">保存</div>
        </div>
      </div>
    </div>

    <!--添加单个应用弹窗-->
    <div class="add-app-modal win-drag win-no-resize" v-show="appObj.addAppType==1||appObj.addAppType==2||appObj.addAppType==3||appObj.addAppType==4||appObj.addAppType==5">
      <div class="add-app-box add-app-box1 win-no-drag">
        <img class="add-app-close" src="/img/close.png" @click="toggleShowAppModal(false, 2)">
        <div class="add-app-title">添加{{
            appObj.addAppType == 1 ? "自定义应用" : appObj.addAppType == 2 ? "顶部栏应用" : appObj.addAppType == 3 ?
              "侧边栏应用" : appObj.addAppType == 4 ? "公用应用" : appObj.addAppType == 5 ? "自定义链接" : ""
          }}
        </div>
        <!--添加自定义、顶部、侧边应用-->
        <div class="add-app-info-box" v-show="appObj.addAppType==1||appObj.addAppType==2||appObj.addAppType==3">
          <div class="add-app-info-content">
            <span class="add-app-info-title">应用名称:</span>
            <div class="add-app-info-sel-box" @click.stop="showSelType(1,true)">
              <input class="add-app-info-input" ref="appNameRef" type="text" placeholder="请输入新系统菜单名称" maxlength="5" v-model.trim="appObj.addAppName"
                     @input="doSearchApp" @blur="doSearchApp('blur')" @focus="doSearchApp('focus')">
              <div class="add-app-info-sel-ul-box" v-show="appObj.selType==1&&appObj.addAppName">
                <ul v-if="!appObj.searchAppNameFlag&&appObj.searchAppNameList.length>0" class="add-app-info-sel-ul">
                  <li class="add-app-info-sel-li" v-for="(item, key) in appObj.searchAppNameList" :key="item.id" @click.stop="selTypeMethod(item, 1)"
                      @mousedown="appNameFocus()" v-html="getHighlight(htmlEscapeAll(item.name), appObj.addAppName)">
                  </li>
                </ul>
                <div v-else-if="!appObj.searchAppNameFlag&&appObj.searchAppNameList.length==0" class="show-text">暂无搜索结果，换个条件试试吧</div>
                <div v-else class="loading-img-box">
                  <img class="loading-img" src="/img/waitting.gif" alt="">
                </div>
              </div>
            </div>
          </div>
          <div class="add-app-info-content">
            <span class="add-app-info-title">应用图标:</span>
            <div class="add-app-icon-img-box">
              <div class="add-app-icon-img-content" :class="{'sel':appObj.selIcon==key+1}" v-for="(item, key) in appObj.iconList" :key="key" @click="appObj.selIcon=key+1">
                <img class="add-app-icon-img" :src="item" :onerror="errorIcon" alt="">
                <i class="add-app-icon-sel" v-show="appObj.selIcon==key+1"></i>
              </div>
            </div>
          </div>
        </div>
        <!--添加公共应用-->
        <div class="add-app-info-box" v-show="appObj.addAppType==4">
          <div class="add-app-info-content">
            <span class="add-app-info-title">应用名称:</span>
            <input class="add-app-info-input" type="text" placeholder="请输入新系统菜单名称" maxlength="5" v-model.trim="appObj.addAppName">
          </div>
          <div class="add-app-info-content">
            <span class="add-app-info-title">应用分类:</span>
            <div class="add-app-info-sel-box arrow" @click.stop="showSelType(2)">
              <input class="add-app-info-input" type="text" placeholder="请选择应用分类" disabled="disabled" v-model.trim="appObj.addAppClassifyName">
              <div class="add-app-info-sel-ul-box" v-show="appObj.selType==2">
                <ul class="add-app-info-sel-ul">
                  <li class="add-app-info-sel-li" v-for="(item, key) in appObj.publicList" :key="item.id" @click.stop="selTypeMethod(item, 2)">{{ item.name }}</li>
                </ul>
              </div>
            </div>
          </div>
          <div class="add-app-info-content">
            <span class="add-app-info-title">地址:</span>
            <input class="add-app-info-input" type="text" placeholder="请输入地址" maxlength="1000" v-model.trim="appObj.addAppUrl">
          </div>
          <div class="add-app-info-content">
            <span class="add-app-info-title">应用图标:</span>
            <div class="add-app-icon-box">
              <div class="add-app-icon-content">
                <input class="add-app-icon-add-input" type="file" accept="image/gif,image/jpeg,image/png,image/jpg,image/bmp" @change="fileChange" ref="fileChangeRef">
                <div class="add-app-icon-add" @click="showFileChange(true)" v-if="!appObj.selFile.path"></div>
                <img class="add-app-icon-img" :src="appObj.selFile.path" @load="loadSelFile" :onerror="errorIcon" v-if="appObj.selFile.path" alt="">
                <i class="add-app-icon-close" v-if="appObj.selFile.path" @click="showFileChange(false)"></i>
              </div>
              <div class="add-app-icon-tips">尺寸：80*80px</div>
            </div>
          </div>
        </div>
        <!--添加自定义链接-->
        <div class="add-app-info-box" v-show="appObj.addAppType==5">
          <div class="add-app-info-content">
            <span class="add-app-info-title width-auto">名称:</span>
            <input class="add-app-info-input" type="text" placeholder="请输入名称" maxlength="6" v-model.trim="appObj.addAppName" @input="inputLimit(1)">
          </div>
          <div class="add-app-info-content">
            <span class="add-app-info-title width-auto">地址:</span>
            <input class="add-app-info-input" type="text" placeholder="请输入地址" maxlength="500" v-model.trim="appObj.addAppUrl" @input="inputLimit(2)">
          </div>
          <div class="add-app-info-content">
            <span class="add-app-info-title width-auto">图标:</span>
            <div class="add-app-icon-img-box">
              <div class="add-app-icon-img-content" :class="{'sel':appObj.selIcon==key+1}" v-for="(item, key) in appObj.defaultIconList" :key="key" @click="appObj.selIcon=key+1">
                <img class="add-app-icon-img" :src="item" :onerror="errorIcon" alt="">
                <i class="add-app-icon-sel" v-show="appObj.selIcon==key+1"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="add-app-btn-box">
          <div class="add-app-btn" @click="toggleShowAppModal(false, 2)">取消</div>
          <div class="add-app-btn add-app-btn-save" @click="confirmApp(2)">保存</div>
        </div>
      </div>
    </div>

    <!--聊天记录验证码弹窗-->
    <LyDialog class="code-record-dialog" title="聊天记录提取" :width="334" :height="228" :closeOnClickModal="false" :visible="dialogObj.type==1"
              @close="dialogOperate(1,1)" @confirm="dialogOperate(1,2)" @mouseup.stop="stopPropagation">
      <div class="ly-dialog-default-box center">
        <label for="recordCode" class="ly-dialog-default-label">
          <span class="ly-dialog-default-tips">*</span>
          <span>验证码：</span>
        </label>
        <div class="ly-dialog-default-detail">
          <input class="ly-dialog-default-input" type="text" id="recordCode" placeholder="请输入提取验证码" maxlength="20" v-model.trim="dialogObj.recordCode">
        </div>
      </div>
    </LyDialog>
  </div>
</template>
<script>
let path = remote.require("path");
let fs = remote.require("fs");
let cp = remote.require("child_process");
import {nextTick, ref, watch} from "vue";
import {useRouter} from "vue-router"
import {
  showMenu, loadCache, getAppPath, transTime, debounce, MD5, deepClone, regReplace, uploadToQiNiu, openAppUrl, getHighlight, htmlEscapeAll, openLocalFile, openChildWin,
} from "@utils";
import {alert, loading, toast} from "@comp/ui";
import {
  queryEmpApi, checkWorkApi, checkWorkSetApi, queryMacApi, get7NTokenApi, editWorkBenchApi, deleteWorkBenchApi, sortNimWorkBenchApi, tempConIsShowApi,
  getLoginMenuByEmpApi, resetIndexAppApi, queryMessageByCodeApi,
} from "@utils/net/api.js";
import NetTips from "@comp/ui/comps/NetTips";
import LyDialog from "@comp/ui/comps/LyDialog";

export default {
  name: "more",
  components: {NetTips, LyDialog},
  setup(props, ctx) {
    const router = useRouter();
    // 用户信息
    let userInfo = store.getters.getUserInfo;
    // 配置文件
    let config = store.getters.getConfig.config;
    // 考勤登记对象
    let attendanceObj = ref({
      pwd: "",//密码
      name: "",//显示选择用户名
      workerNo: "",//选择用户工号
      workerId: "",//选择用户工号
      workerName: "",//选择用户名
      deptNumber: "",//选择用户部门编号
      deptName: "",//选择用户部门名
      flag: false,//显示考勤弹窗
      time: transTime(Date.now()),//打开考勤弹窗时间
      searchList: [],//搜索列表
      searchIndex: -1,//选择的下标
      searchFlag: false,//搜索状态
      searchReg: false,//是否发起搜索
    });
    // 考勤登记搜索框元素
    let searchBoxRef = ref();
    // 考勤登记密码框对象
    let attendancePwdRef = ref();
    // 选择文件对象
    let fileChangeRef = ref();
    // app名对象
    let appNameRef = ref();
    // 弹窗对象
    let dialogObj = ref({
      type: -1,// 1聊天记录提取二维码
      recordCode: "",// 聊天记录验证码
    });
    let showPwdFlag = ref(false);

    // 工作台对象
    let workObj = ref({
      mainTabKey: 0,// 主菜单key
      appTabKey: 0,// 全部应用key
      normalList: [],// 常用应用列表
      maxNormal: 36,// 最大常用应用数
      defaultList: [],// 非常用应用列表
      customList: [],// 自定义应用列表
      topList: store.getters.getState("tabMap").topList,// 顶部应用列表
      leftList: [],// 侧边栏应用列表
      publicList: [],// 全部应用列表
      allList: [],// 全部应用数据列表
      allAppList: store.getters.getState("allAppList"),// 全部应用列表
      classifyList: [],// 全部分类列表
      allListFlag: false, // 请求状态，必须4个接口都成功在渲染
      normalListFlag: false,
      normalListLoading: true,
      purviewFlag: false,
      allFlag: false,
      isShowNetTemp: false,// 是否显示临时电脑授权
      vpnInfo: {
        isShowVpn: false,// 是否有VPN权限
        vpnFlag: false,// 是否开启了VPN
        vpnObj: {},// vpn开启关闭内容对象
      },// vpn对象
      isCompany: store.getters.getState("deviceObj").isCompany,
    });
    // 应用对象
    let appObj = ref({
      flag: false,// 添加应用弹窗标识
      loading: false,// 请求接口加载状态
      diffLoading: false,// 请求接口局部加载状态
      val: "",// 搜索关键词
      key: 0,// 选择分类key
      moveItem: {},// 移动对象
      list: [],// 选中分类的列表
      selKey: 0,// 公共应用选则的分类
      selMap: {},// 常用应用选择的对象
      selList: [],// 常用应用选择的列表
      showList: [],// 常用应用显示的列表
      addAppType: "",// 添加单个应用弹窗:1自定义应用2顶部3侧边4公用5自定义链接
      selFile: {},// 选择的文件对象
      selIcon: 1,// 选择icon下标
      selType: "",// 显示选择分类-1应用名-2分类
      AddAppSelId: "",// 编辑应用的id
      AddAppSelType: "",// 编辑应用的类型
      AddAppSelBenchTypeId: "",// 编辑应用的父对象id
      addAppName: "",// 添加应用名
      addAppClassify: "",// 添加应用分类
      addAppClassifyName: "",// 添加应用分类名
      addAppUrl: "",// 添加应用地址
      iconList: [],// 显示选择的icon列表
      searchAppNameList: [],// 搜索添加应用名列表
      searchAppNameFlag: false,// 搜索标识
      publicList: [],// 全部应用列表-去除自定义
      appNameTimer: "",// 搜索app名定时器
      hoverItem: {},// 鼠标悬浮对象
      hoverTimer: "",// 鼠标悬浮定时器
      defaultIconList: [],// 自定义链接默认图标列表
    });

    for (let i = 0; i < 12; i++) {
      appObj.value.defaultIconList[i] = config[config.env].jjsHome + `/lyj-front/lyj-web/images/icons/icon${i + 1}.png`;
    }

    watch(() => router.currentRoute.value.path,
      (newValue, oldValue) => {
        if (newValue == "/index/more") {
          if (router.currentRoute.value.query.type == "top") {
            toggleShowAppModal(true, 1, {key: 2});
          } else {
            workObj.value.allListFlag = false;
            workObj.value.normalListFlag = false;
            initAppDataApi();
          }
        }
      }, {
        deep: true
      }
    )

    // 监听全局点击
    watch(() => store.state.emit.allClick,
      (newValue, oldValue) => {
        appObj.value.selType = "";
      }, {
        deep: true,
      }
    );

    // 监听全局移动
    let oldTextmenuEnter = global.textmenuEnter;
    watch(() => store.state.emit.mousemove,
      (newValue, oldValue) => {
        if (oldTextmenuEnter != global.textmenuEnter) {
          oldTextmenuEnter = global.textmenuEnter
          appObj.value.hoverItem.textmenuEnter = global.textmenuEnter;
        }
      }, {
        deep: true
      }
    );

    // 监听vpn权限
    watch(() => store.state.jjsProxy.vpnInfo,
      (newValue, oldValue) => {
        workObj.value.vpnInfo = {...newValue};
      }, {
        deep: true
      }
    );

    // 监听电脑类型变更
    watch(() => store.state.deviceObj,
      (newValue, oldValue) => {
        workObj.value.isCompany = newValue.isCompany;
        initAppDataApi(true);
      }, {
        deep: true
      }
    );

    // 监听全部应用
    watch(() => store.state.allAppList,
      (newValue, oldValue) => {
        workObj.value.allAppList = newValue;
      }, {
        deep: true
      }
    );

    // 监听顶部应用
    watch(() => store.state.tabMap,
      (newValue, oldValue) => {
        workObj.value.topList = newValue.topList;
      }, {
        deep: true
      }
    );


    // 下载对象
    let downloadToolsObj = ref({});

    // 打开工具
    function openTools(type) {
      if (type == 1) {
        // 向日葵 查询本机是否安装,向日葵在注册表中找不到，默认先写死，后面看有没有办法解决
        let localPath_64 = "C:/Program Files (x86)/Oray/SunLogin/SunloginClient/SunloginClient.exe";
        let localPath_32 = "C:/Program Files/Oray/SunLogin/SunloginClient/SunloginClient.exe";
        if (fs.existsSync(localPath_64)) {
          // 打开向日葵
          openLocalFile(localPath_64);
        } else {
          if (fs.existsSync(localPath_32)) {
            // 打开向日葵
            openLocalFile(localPath_32);
          } else {
            // 下载安装
            if (downloadToolsObj.value[type] && downloadToolsObj.value[type].download) {
              // 正在下载
              return;
            }
            let downloadPath = "https://front.leyoujia.com/leyoujiaIm/SunloginClient.exe";
            downloadToolsObj.value[type] = {
              download: true
            };
            // 开始下载
            loadCache({
              url: downloadPath, ext: "exe", fileName: "SunloginClient.exe", fileDB: store.getters.getFileDB, isDownLoad: true, done: res => {
                if (res.state == "process" || res.state == "start") {
                  downloadToolsObj.value[type].percentage = res.percentage;
                } else {
                  if (res.state == "success") {
                    setTimeout(() => {
                      // 打开向日葵
                      openLocalFile(res.path + res.name);
                    }, 1000)
                  }
                  downloadToolsObj.value[type] = {};
                }
              }
            }).then(res => {
              if (res.downloadFileObj) {
                downloadToolsObj.value[type].downloadFileObj = res.downloadFileObj;
              } else {
                downloadToolsObj.value[type] = {};
                // 打开向日葵
                openLocalFile(res.url);
              }
            });
          }
        }
      } else if (type == 2) {
        alert({
          content: `<div style="text-align: left"><div style="color:red">运行本程序，将会删除电脑所有文档、表格、PPT、图片，如有重要文件请先备份，且需重安装乐聊，是否继续运行</div><div>注：如有360安全卫士弹出提示，请点允许</div></div>`,
          done: (type) => {
            if (type == 1) {
              let execPath = getAppPath(`${config.env == "online" ? "\\tools\\clear.exe" : "\\public\\tools\\clear.txt"}`);
              console.log("exec-clear", execPath);
              openLocalFile(execPath);
            }
          }
        });
      }
    }

    // 取消下载
    function cancelTools(type) {
      if (downloadToolsObj.value[type]) {
        downloadToolsObj.value[type].downloadFileObj.then(res => {
          res.clientRequest.destroy();
        });
      }
    }

    // 打卡-1打卡-2考勤登记
    async function clockIn(type) {
      // 打卡逻辑
      let macAllStr = store.getters.getComputerInfo.Mac;
      // 获取老系统mac
      let macRes = await queryMacApi({
        msgBody: JSON.stringify({
          macs: macAllStr,
        }),
      });
      if (macRes && macRes.data && macRes.data.data) {
        // 打卡
        let res = await checkWorkSetApi({
          msgBody: JSON.stringify({
            empNumber: type == 1 ? userInfo.workerId : attendanceObj.value.workerId,
            empName: type == 1 ? userInfo.workerName : attendanceObj.value.workerName,
            deptNumber: type == 1 ? userInfo.deptNumber : attendanceObj.value.deptNumber,
            deptName: type == 1 ? userInfo.deptName : attendanceObj.value.deptName,
            type: 1,
            operatorMac: macRes.data.data,
            operatorNumber: userInfo.workerId,
            operatorName: userInfo.workerName,
            operatorDeptNumber: userInfo.deptNumber,
            operatorDeptName: userInfo.deptName,
          }),
        });
        if (res.success) {
          attendance(false);
          alert({
            content: "登记上班成功！登记时间为：" + res.data.data[0].workHour,
            showCancel: false,
          });
        } else {
          toast({title: res.errorMsg || "系统错误", type: 2});
        }
      } else {
        toast({title: "抱歉,您当前网络环境无法登录新系统哦!", type: 2});
      }
    }

    // 显示/隐藏考勤登记
    function attendance(flag) {
      attendanceObj.value.time = transTime(Date.now() + store.getters.getDiffTime);
      attendanceObj.value.flag = flag;
      attendanceReset();
    }

    // 输入搜索人员
    function nameInput(e) {
      attendanceObj.value.searchList = [];
      attendanceObj.value.searchFlag = true;
      attendanceObj.value.searchReg = false;
      // 改变清空工号
      attendanceObj.value.workerNo = "";
      if (attendanceObj.value.name == "" || /---/.test(attendanceObj.value.name)) {
        attendanceObj.value.searchReg = true;
        return;
      }
      debounce({
        timerName: "doQueryEmpApi",
        time: 500,
        e: e,
        fnName: doQueryEmpApi
      });
    }

    // 查询员工api
    async function doQueryEmpApi() {
      attendanceObj.value.searchIndex = -1;
      let msg = await queryEmpApi({
        serviceCode: "40002",
        methodCode: "50009",
        msgBody: JSON.stringify({
          name: attendanceObj.value.name,
        }),
      });
      attendanceObj.value.searchFlag = false;
      if (msg.success) {
        attendanceObj.value.searchList = msg.data.data || [];
      } else {
        toast({title: msg.errorMsg || "系统错误", type: 2});
      }
    }

    // 选择用户key为数组下标
    function selUser(key) {
      let item = attendanceObj.value.searchList[key];
      attendanceObj.value.name = item.deptName + "---" + item.workerName;
      attendanceObj.value.workerNo = item.workerNo;
      attendanceObj.value.workerId = item.workerId;
      attendanceObj.value.workerName = item.workerName;
      attendanceObj.value.deptName = item.deptName;
      attendanceObj.value.deptNumber = item.deptNumber;
      attendanceObj.value.searchList = [];
      attendanceObj.value.searchReg = true;
      attendancePwdRef.value.focus();
    }

    // 搜索人员输入框聚焦
    function nameFocus() {
      if (attendanceObj.value.name == "" || /---/.test(attendanceObj.value.name)) {
        attendanceObj.value.searchReg = true;
      }
    }

    // 选择下一个搜索人员
    function selPre() {
      if (attendanceObj.value.searchIndex > 0) {
        attendanceObj.value.searchIndex--;
      }
      scrollLi();
    }

    // 选择下一个搜索人员
    function selNext() {
      if (attendanceObj.value.searchIndex < attendanceObj.value.searchList.length - 1) {
        attendanceObj.value.searchIndex++;
      }
      scrollLi();
    }

    // 重置考勤登记
    function attendanceReset() {
      attendanceObj.value.pwd = "";
      attendanceObj.value.name = "";
      attendanceObj.value.workerNo = "";
      attendanceObj.value.searchIndex = -1;
    }

    // 保存考勤登记
    async function attendanceSave() {
      if (!attendanceObj.value.workerNo) {
        toast({title: "请正确选择人员！", type: 2});
        return;
      }
      if (!attendanceObj.value.pwd) {
        toast({title: "请输入密码！", type: 2});
        return;
      }
      if (attendanceObj.value.workerNo == userInfo.workerNo) {
        toast({title: "不能是自己！", type: 2});
        return;
      }
      let res = await checkWorkApi({
        msgBody: JSON.stringify({
          username: attendanceObj.value.workerNo,
          password: MD5(attendanceObj.value.pwd.toLowerCase()),
          passwordv2: MD5(attendanceObj.value.pwd),
        }),
      });
      if (res.success) {
        clockIn(2);
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 滚动到可视区域搜索列表
    function scrollLi() {
      nextTick(() => {
        let searchBox = searchBoxRef.value;
        let currLiElm = Array.prototype.find.call(searchBox.firstChild.children, item => {return /curr/.test(item.className)});
        // 判断可视区域
        if (currLiElm) {
          if (currLiElm.offsetTop >= searchBox.scrollTop + searchBox.clientHeight) {
            searchBoxRef.value.scrollTop += currLiElm.offsetHeight;
          } else if (currLiElm.offsetTop + currLiElm.offsetHeight <= searchBox.scrollTop) {
            searchBoxRef.value.scrollTop -= currLiElm.offsetHeight;
          }
        }
      })
    }

    // 跳转应用url
    function toAppUrl(item) {
      openAppUrl(item.requestUrl || item.url);
    }

    // 显示编辑菜单 type-0为常用应用
    function showEdit(e, item, type, flag) {
      appObj.value.hoverItem = item;
      if (flag == false) {
        item.textmenuEnter = false;
        appObj.value.hoverTimer = debounce({
          timerName: "showEdit",
          time: 100,
          fnName: function () {
            if (!global.textmenuEnter) {
              showMenu([]).hide();
            }
          }
        })
        return;
      }
      clearTimeout(appObj.value.hoverTimer);
      item.textmenuEnter = true;
      let menuList = [];
      if (type != 0) {
        menuList.push({
          label: `设为常用`, click: function () {
            if (workObj.value.normalList.length >= workObj.value.maxNormal) {
              toast({title: `常用设置不得超过${workObj.value.maxNormal}个`});
              return;
            }
            if (workObj.value.normalList.findIndex(lItem => {return lItem.code == item.code}) > -1) {
              toast({title: `该常用应用已存在`});
              return;
            }
            let list = deepClone(workObj.value.normalList);
            list.push(item);
            sortWorkBenchUsed(list);
          }
        });
      } else {
        menuList.push({
          label: `移除${item.benchTypeId != 1 && type != 0 ? "（公共）" : ""}`, click: function () {
            removeSelApp(item, type == 0 ? 0 : workObj.value.publicList[workObj.value.appTabKey].id == 1 ? 1 : 4);
          }
        });
      }
      if (menuList.length > 0) {
        showMenu(menuList).popup(e.x, e.y, true);
      }
    }

    // tab标签左右滚动
    function scrollSessionTab(e) {
      e.preventDefault();
      e.currentTarget.scrollBy({
        left: e.deltaY < 0 ? -30 : 30
      });
    }

    // 添加应用弹窗 type-1为切换对应新增tab-2为关闭添加应用弹窗
    function toggleShowAppModal(flag, type, item) {
      if (type == 1) {
        toggleKey(3, item && item.key ? item.key : 0, item && item.childKey ? item.childKey : 0);
        appObj.value.flag = flag;
      } else if (type == 2) {
        if (!flag) {
          appObj.value.addAppType = "";
          return;
        }
        // 新增
        if (!item || !item.id) {
          if (appObj.value.key == 2 && appObj.value.list.length >= 6) {
            toast({title: "最多添加6个应用，请移除后再添加", type: 2});
            return;
          } else if (appObj.value.key == 3 && appObj.value.list.length >= 2) {
            toast({title: "最多添加2个应用，请移除后再添加", type: 2});
            return;
          }
        }
        appObj.value.addAppType = appObj.value.key;
        appObj.value.addAppSelType = appObj.value.addAppType;
        let iconList = [];
        switch (appObj.value.key) {
          case 1:
            for (let i = 1; i <= 12; i++) {
              iconList.push(`/img/workbench/icon_app${i}.png`);
            }
            break;
          case 2:
            for (let i = 1; i <= 9; i++) {
              iconList.push(`/img/workbench/tab_top${i}.png`);
            }
            break;
          case 3:
            for (let i = 1; i <= 4; i++) {
              iconList.push(`/img/workbench/tab_left${i}.png`);
            }
            break;
        }
        // 重置状态
        appObj.value.selFile = {};
        appObj.value.selIcon = 1;
        appObj.value.AddAppSelId = "";
        appObj.value.AddAppSelBenchTypeId = appObj.value.addAppSelType == 4 ? workObj.value.publicList[appObj.value.selKey].id : "";
        appObj.value.AddAppSelType = "";
        appObj.value.addAppName = "";
        appObj.value.addAppClassify = "";
        appObj.value.addAppClassifyName = "";
        appObj.value.addAppUrl = "";
        appObj.value.iconList = iconList;
        fileChangeRef.value.value = "";
      }
    }

    // 设置addAppType
    function setAddAppType(key) {
      if (key == 5 && Object.keys(appObj.value.selMap).length >= workObj.value.maxNormal) {
        toast({title: `常用设置不得超过${workObj.value.maxNormal}个`});
        return;
      }
      appObj.value.addAppType = key;
    }

    // 移动应用 type-1开始2进入3过程4结束
    function appDrag(e, type, item) {
      switch (type) {
        case 1:
          // 搜索状态非常用应用禁止拖拽排序
          if (appObj.value.key != 0 && appObj.value.val) {
            e.stopPropagation();
            e.preventDefault();
            return false;
          }
          appObj.value.moveItem = item;
          break;
        case 2:
          if (appObj.value.moveItem !== item) {
            let oldIndex = appObj.value.list.indexOf(appObj.value.moveItem);
            let newIndex = appObj.value.list.indexOf(item);
            let newItems = [...appObj.value.list];
            // 删除老节点
            newItems.splice(oldIndex, 1);
            // 添加新节点
            newItems.splice(newIndex, 0, appObj.value.moveItem);
            appObj.value.list = [...newItems];
            if (appObj.value.key == 0) {
              appObj.value.selList = appObj.value.list;
            }
          }
          e.preventDefault();
          break;
        case 3:
          // 去除禁止光标
          e.preventDefault();
          break;
        case 4:
          appObj.value.moveItem = {};
          break;
        default:
          break;
      }
    }

    // 获取应用icon
    function getAppIcon(item) {
      let src = item.iconUrl || "-";
      switch (String(item.position)) {
        case "2":
          src = `/img/workbench/tab_top${item.img}.png`;
          break;
        case "3":
          src = `/img/workbench/tab_left${item.img}.png`;
          break;
        default:
          if (!/https?:/.test(src)) {
            src = config[config.env].jjsHome + src;
          }
          break;
      }
      return src;
    }

    // icon加载失败
    function errorIcon(e) {
      e.target.src = "/img/workbench/icon_app_default_new.png";
      e.target.onerror = "";
    }

    // 切换显示全部内容 type-1主菜单-2主应用列表-3弹窗列表-4公共应用列表 childKey-弹窗列表的字列表
    function toggleKey(type, key, childKey) {
      switch (type) {
        case 1:
          workObj.value.mainTabKey = key;
          if (key == 1) {
            // 判断是否显示临时电脑授权
            store.dispatch("getSecretEnCrypt", {param: {}}).then((secret) => {
              tempConIsShowApi({msgBody: JSON.stringify({secret: secret})}).then(res => {
                workObj.value.isShowNetTemp = res?.data?.show || false;
              });
            });
            // 判断是否有vpn权限
            store.dispatch("getProxyTool");
          }
          break;
        case 2:
          workObj.value.appTabKey = key;
          break;
        case 3:
          // 切换显示列表重置搜索
          if (key == appObj.value.key && appObj.value.flag) {
            return;
          }
          appObj.value.key = key;
          appObj.value.val = "";
          switch (key) {
            case 0:
              // 常用应用
              appObj.value.showList = deepClone(workObj.value.allAppList);
              // 初始化列表
              appObj.value.selList = deepClone(workObj.value.normalList);
              let selMap = {};
              appObj.value.selList.map(item => {
                selMap[item.code] = item;
              });
              appObj.value.selMap = selMap;
              appObj.value.list = appObj.value.selList;
              setTimeout(() => {
                document.querySelector(".add-app-normal-content").scrollTop = 0;
              }, 100);
              break;
            case 1:
              appObj.value.list = deepClone(workObj.value.customList);
              break;
            case 2:
              appObj.value.list = deepClone(workObj.value.topList);
              break;
            case 3:
              appObj.value.list = deepClone(workObj.value.leftList);
              break;
            case 4:
              // 公共应用选中第一个列表
              toggleKey(4, childKey || 0);
              break;
          }
          break;
        case 4:
          appObj.value.selKey = key;
          if (workObj.value.publicList[key]) {
            appObj.value.list = deepClone(workObj.value.publicList[key].list);
          }
          setTimeout(() => {
            let appLeftElm = document.querySelector(".public-app-left");
            let appLeftTop = document.querySelector(".public-app-left .sel").offsetTop - appLeftElm.offsetTop;
            if (appLeftTop > appLeftElm.clientHeight || appLeftTop < appLeftElm.scrollTop) {
              document.querySelector(".public-app-left").scrollTop = appLeftTop;
            }
          }, 100);
          if (appObj.value.val) {
            searchApp();
          }
          break;
      }
    }

    // 切换选择应用,type添加删除
    function toggleSelApp(item, type) {
      let index = -1;
      switch (type) {
        case "add":
          appObj.value.list.push(item);
          break;
        case "del":
          index = appObj.value.list.findIndex(item1 => item1.id == item.id);
          appObj.value.list.splice(index, 1);
          break;
        case "sel":
          if (appObj.value.selMap[item.code]) {
            delete appObj.value.selMap[item.code];
            index = appObj.value.selList.findIndex(item1 => item1.code == item.code);
            appObj.value.selList.splice(index, 1);
          } else {
            if (Object.keys(appObj.value.selMap).length >= workObj.value.maxNormal) {
              toast({title: `常用设置不得超过${workObj.value.maxNormal}个`});
              return;
            }
            item = deepClone(item);
            delete item.id;
            appObj.value.selMap[item.code] = item;
            appObj.value.selList.push(item);
          }
          break
        case "selDel":
          delete appObj.value.selMap[item.code];
          index = appObj.value.selList.findIndex(item1 => item1.code == item.code);
          appObj.value.selList.splice(index, 1);
          break;
      }
    }

    // 请求服务器删除应用
    function removeSelApp(item, appType) {
      alert({
        content: `移除后，将无法在${appType == 0 ? "常用应用" : appType == 1 ? "自定义应用" : appType == 2 ? "顶部栏应用" : appType == 3 ? "侧边栏应用" : appType == 4 ? "公共应用" : "工作台"}找到该应用，<br>确认移除？`,
        done: type => {
          if (type == 1) {
            if (appType == 0) {
              // 删除常用
              let list = deepClone(workObj.value.normalList);
              for (let i = 0; i < list.length; i++) {
                if (item.code == list[i].code) {
                  list.splice(i, 1);
                  break;
                }
              }
              sortWorkBenchUsed(list);
            } else {
              // 删除所有app对应数据
              deleteWorkBench(item);
            }
          }
        }
      })
    }

    // 搜索app type-reset重置搜索 key:1搜索添加顶部应用
    function searchApp(type, key) {
      if (type == "reset") {
        appObj.value.val = "";
      }
      let value = key == 1 ? appObj.value.addAppName : appObj.value.val;
      // 根据应用名搜索
      let list = store.getters.getAppList({value: value});
      if (key == 1) {
        appObj.value.searchAppNameList = list;
      } else {
        if (appObj.value.key == 0) {
          // 常用应用修改显示列表
          appObj.value.showList = list;
        } else {
          appObj.value.list = list;
        }
      }
    }

    // 选择icon图片
    function fileChange(e) {
      let file = e.target.files[0];
      if (file.type.indexOf("image/") === -1) {
        toast({title: "请选择图片", type: 2});
        return;
      }
      if (file.size > 1 * 1024 * 1024) {
        toast({title: "图片不能大于1M", type: 2});
        return;
      }
      appObj.value.selFile = file;
    }

    function loadSelFile(e) {
      if (appObj.value.selFile.path && !/(https?):\/\//i.test(appObj.value.selFile.path) && e.target.naturalWidth != 80 && e.target.naturalHeight != 80) {
        toast({title: "请选择80*80的图片", type: 2});
        appObj.value.selFile = {};
      }
    }

    // 显示文件选择
    function showFileChange(flag) {
      if (flag) {
        fileChangeRef.value.click();
      } else {
        appObj.value.selFile = {};
        appObj.value.selIcon = 1;
        fileChangeRef.value.value = "";
      }
    }

    // 显示选择 type-1应用名-2分类
    function showSelType(type, flag) {
      if (flag != null) {
        appObj.value.selType = flag ? type : "";
      } else {
        appObj.value.selType = type == appObj.value.selType ? "" : type
      }
    }

    // 选择应用分类 type-1搜索应用-2搜索分类
    function selTypeMethod(item, type) {
      if (type == 1) {
        appObj.value.addAppName = item.name;
        appObj.value.addAppUrl = item.url;
      } else if (type == 2) {
        appObj.value.addAppClassify = item.id;
        appObj.value.AddAppSelBenchTypeId = item.id;
        appObj.value.addAppClassifyName = item.name;
      }
      showSelType(type, false);
    }

    // 选择app名聚焦
    function appNameFocus() {
      debounce({
        timerName: "appNameFocus",
        time: 100,
        fnName: function () {
          appNameRef.value.focus();
        }
      })
    }

    // 查询引用 type-blur失去焦点
    function doSearchApp(type) {
      if (type == "focus") {
        clearTimeout(appObj.value.appNameTimer);
        return;
      } else if (type == "blur") {
        appObj.value.appNameTimer = debounce({
          timerName: "doSearchAppBlur",
          time: 300,
          fnName: function () {
            if (!appObj.value.addAppUrl) {
              appObj.value.addAppName = "";
            }
          }
        });
        return;
      }
      appObj.value.addAppUrl = "";
      searchApp("", 1);
    }

    // 编辑应用
    function editApp(item, key) {
      appObj.value.key = key != null ? key : (item.benchTypeId == 1 ? 1 : 4);
      toggleShowAppModal(true, 2, item);
      appObj.value.AddAppSelId = item.id;
      appObj.value.AddAppSelBenchTypeId = item.benchTypeId;
      appObj.value.addAppName = item.name;
      appObj.value.addAppUrl = item.requestUrl;
      appObj.value.selIcon = item.img;
      if (item.benchTypeId) {
        for (let i = 0; i < workObj.value.classifyList.length; i++) {
          if (workObj.value.classifyList[i].id == item.benchTypeId && item.benchTypeId != 1) {
            appObj.value.addAppClassifyName = workObj.value.classifyList[i].name;
            appObj.value.addAppClassify = item.benchTypeId;
            appObj.value.selFile.path = item.img;
          }
        }
      }
    }

    // 保存 type-1保存排序-2保存添加
    function confirmApp(type) {
      if (type == 1) {
        let list = [];
        if (appObj.value.key == 0) {
          if (appObj.value.list.length == 0) {
            toast({title: "请选择您的常用应用"});
            return;
          }
          // 保存常用应用（新增+删除+排序）
          sortWorkBenchUsed(appObj.value.list);
        } else {
          for (let i = 0; i < appObj.value.list.length; i++) {
            let item = appObj.value.list[i];
            list.push({
              id: item.id,
              empNumber: item.empNumber,
              empNo: item.empNo,
              empName: item.empName,
              position: item.position,
              benchTypeId: item.benchTypeId,
              type: item.type,
              name: item.name,
              requestUrl: item.requestUrl,
              img: item.img
            });
          }
          // 保存其他应用排序
          if (list.length == 0) {
            toggleShowAppModal(false, 1);
          } else {
            sortNimWorkBench(list);
          }
        }
      } else if (type == 2) {
        if (appObj.value.addAppType == 5) {
          // 添加自定义应用
          if (!appObj.value.addAppName) {
            toast({title: "请输入应用名称", type: 2});
            return;
          }
          if (!appObj.value.addAppUrl) {
            toast({title: "请输入应用地址", type: 2});
            return;
          }
          let code = `add_${Date.now()}`;
          appObj.value.selList.push({
            id: code,
            appType: 2,
            code: code,
            iconUrl: appObj.value.defaultIconList[appObj.value.selIcon - 1],
            name: appObj.value.addAppName,
            url: appObj.value.addAppUrl,
          });
          toggleShowAppModal(false, 2);
          appObj.value.selIcon = 1;
          appObj.value.addAppName = "";
          appObj.value.addAppUrl = "";
          toast({title: "操作成功", type: 1});
        } else {
          editWorkBench();
        }
      }
    }

    // 初始化数据
    if (workObj.value.isCompany != null) {
      initAppDataApi(true);
    }

    // 初始化应用数据-4个接口判断
    function initAppDataApi(flag) {
      let p = [];
      p.push(getLoginMenuByEmp());
      Promise.all(p).then(res => {
        if (workObj.value.normalListFlag) {
          workObj.value.allFlag = true;
          if (flag && router.currentRoute.value.query.type == "top") {
            toggleShowAppModal(true, 1, {key: 2});
          }
        } else {
          workObj.value.allFlag = false;
        }
      });
    }

    // 查询工作台菜单（新）
    async function getLoginMenuByEmp() {
      // 初始化时需要loading
      if (!workObj.value.normalListFlag) {
        workObj.value.normalListLoading = true;
      }
      let res = await getLoginMenuByEmpApi({
        msgBody: JSON.stringify({
          isPrivate: workObj.value.isCompany ? 2 : 1
        })
      });
      workObj.value.normalListLoading = false;
      if (res?.success) {
        workObj.value.normalListFlag = true;
        let normalList = [];
        let defaultList = [];
        res.data?.data?.map(item => {
          if (item.id == -1) {
            normalList = item.childList;
          } else {
            defaultList.push(item);
          }
        });
        workObj.value.normalList = normalList;
        workObj.value.defaultList = defaultList;
      } else {
        workObj.value.normalListFlag = false;
      }
    }

    // 添加/编辑应用api
    async function editWorkBench() {
      // 校验
      if (appObj.value.addAppName.length < 2) {
        toast({title: "应用名称为2-5个字", type: 2});
        return;
      }
      if (appObj.value.key == 4) {
        if (!appObj.value.addAppClassify) {
          toast({title: "请选择应用分类", type: 2});
          return;
        }
        if (!appObj.value.addAppUrl) {
          toast({title: "请输入应用地址", type: 2});
          return;
        }
        // 公共应用上传图标
        if (appObj.value.key == 4 && appObj.value.selIcon == 1) {
          if (!appObj.value.selFile.path) {
            toast({title: "请上传应用图标", type: 2});
            return;
          }
          // 上传文件
          let res = await get7NToken();
          if (!res.success) {
            toast({title: res.msg, type: 2});
            return;
          }
          appObj.value.selIcon = res.domain + res.key;
        }
      } else {
        if (!appObj.value.addAppUrl) {
          toast({title: "请选择添加的应用", type: 2});
          return;
        }
      }
      loading();
      let param = {
        id: appObj.value.AddAppSelId,
        empNumber: userInfo.workerId,
        empNo: userInfo.workerNo,
        empName: userInfo.workerName,
        position: appObj.value.addAppSelType == 2 ? 2 : appObj.value.addAppSelType == 3 ? 3 : 1,//1中间-2顶部-3侧边
        type: appObj.value.addAppSelType == 3 || appObj.value.addAppSelType == 4 ? 1 : 2,//公共和侧边为1
        benchTypeId: appObj.value.addAppSelType == 1 ? 1 : appObj.value.AddAppSelBenchTypeId,// 自定义id为1
        name: appObj.value.addAppName,
        requestUrl: appObj.value.addAppUrl,
        img: appObj.value.selIcon,
      }
      let res = await editWorkBenchApi({
        msgBody: JSON.stringify(param)
      });
      loading().hide();
      toast({title: res.success ? "设置成功" : res.errorMsg, type: res.success ? 1 : 2});
      if (res.success) {
        toggleShowAppModal(false, 2);
        if (res.data && res.data.data) {
          // 添加应用
          if (!param.id) {
            workObj.value.allList.push(res.data.data);
            appObj.value.list.push(res.data.data);
          } else {
            // 编辑应用
            for (let i = 0; i < appObj.value.list.length; i++) {
              if (appObj.value.list[i].id == param.id) {
                if (appObj.value.list[i].benchTypeId != res.data.data.benchTypeId) {
                  // 公共应用修改分组
                  appObj.value.list.splice(i, 1);
                } else {
                  appObj.value.list[i] = res.data.data;
                }
                break;
              }
            }
          }
          updateTopAppList();
        } else {
          toast({title: "数据异常,请重新进入工作台查看", type: 2});
        }
      }
    }

    // 移除应用api
    async function deleteWorkBench(item) {
      loading();
      let res = await deleteWorkBenchApi({
        msgBody: JSON.stringify({
          id: item.id,
          type: item.type
        })
      });
      loading().hide();
      toast({title: res.success ? "设置成功" : res.errorMsg, type: res.success ? 1 : 2});
      if (res.success) {
        // 重置工作台显示应用
        toggleSelApp(item, "del")
        updateTopAppList();
      }
    }

    // 更新顶部应用
    function updateTopAppList() {
      if (appObj.value.key == 2) {
        // 顶部应用更新
        workObj.value.topList = appObj.value.list;
        // 修改左侧、顶部tab
        store.dispatch("setWorkBench", {topList: workObj.value.topList});
      }
    }

    // 应用排序api
    async function sortNimWorkBench(list) {
      loading();
      let res = await sortNimWorkBenchApi({
        msgBody: JSON.stringify({
          workBenchList: list
        })
      });
      loading().hide();
      if (res.success) {
        if (res.data && res.data.data) {
          toast({title: "设置成功", type: 1});
          if (appObj.value.key == 2) {
            updateTopAppList();
          } else {
            // 去除原有排序
            for (let i = 0; i < res.data.data.length; i++) {
              for (let j = 0; j < workObj.value.allList.length; j++) {
                if (workObj.value.allList[j].id == res.data.data[i].id) {
                  workObj.value.allList.splice(j, 1);
                  break;
                }
              }
            }
            workObj.value.allList = workObj.value.allList.concat(res.data.data);
          }
          toggleShowAppModal(false, 1);
        } else {
          toast({title: "数据异常,请重新进入工作台查看", type: 2});
        }
      } else {
        alert({
          content: "操作失败。" + res.errorMsg,
        });
      }
    }

    // 常用应用排序api
    async function sortWorkBenchUsed(orginList) {
      let list = [];
      orginList.map((item, key) => {
        list.push({
          appType: item.appType || 1,
          code: item.code,
          iconUrl: item.iconUrl,
          indexUrl: item.indexUrl,
          name: item.name,
          url: item.url,
          sort: key + 1
        });
      });
      loading();
      let res = await resetIndexAppApi({
        msgBody: JSON.stringify({
          indexAppUseList: list
        })
      });
      loading().hide();
      if (res.success) {
        toast({title: "设置成功", type: 1});
        workObj.value.normalList = list;
        toggleShowAppModal(false, 1);
        // 重新查询常用列表
        initAppDataApi();
      } else {
        alert({
          content: "操作失败。" + res.errorMsg,
        });
      }
    }

    // 将图片上传至七牛api
    async function get7NToken() {
      return new Promise(async resolve => {
        let res = await get7NTokenApi({
          msgJson: JSON.stringify({workerId: userInfo.workerId}),
        });
        if (res.success) {
          if (res.data && res.data.token) {
            let fileRes = await uploadToQiNiu(appObj.value.selFile, res.data.token);
            if (fileRes && fileRes.key) {
              resolve({success: true, domain: res.data.domain, ...fileRes});
            } else {
              resolve({success: false, msg: "上传文件失败"});
            }
          } else {
            resolve({success: false, msg: "获取上传token失败"});
          }
        } else {
          resolve(res);
        }
      })
    }

    // 下载打印控件
    function downloadPrint() {
      let url = "https://download.obs.cn-sz1.ctyun.cn/tools/lyj_safe_setup_1.0.6_x32.exe";
      if (store.getters.getComputerInfo.OSArchitecture.indexOf("64") > -1) {
        url = "https://download.obs.cn-sz1.ctyun.cn/tools/lyj_safe_setup_1.0.6_x64.exe";
      }
      store.dispatch("setOpenWindow", [url]);
    }

    // 切换vpn开关
    function changeVpnFlag() {
      store.dispatch("changeVpnFlag");
    }

    // 限制输入表情
    function inputLimit(type) {
      switch (type) {
        case 1:
          appObj.value.addAppName = appObj.value.addAppName.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, "");
          break;
        case 2:
          appObj.value.addAppUrl = appObj.value.addAppUrl.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g, "");
          break;
      }
    }

    // 显示聊天记录提取弹窗
    function showDialog(type) {
      switch (type) {
        case 1:
          // 聊天记录提取
          dialogObj.value.recordCode = "";
          break
      }
      dialogObj.value.type = type;
    }

    // 弹窗操作-type:聊天记录提取,key:1关闭2确认
    async function dialogOperate(type, key) {
      switch (type) {
        // 聊天记录提取
        case 1:
          if (key == 1) {
            dialogObj.value.type = -1;
          } else {
            if (dialogObj.value.recordCode.length == 0) {
              toast({title: "请输入提取验证码"});
              return;
            }
            loading();
            let res = await queryMessageByCodeApi({
              msgBody: JSON.stringify({
                msgCode: dialogObj.value.recordCode,
                page: 1,
              })
            });
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误"});
              return;
            }
            if (!res.data?.data?.list?.length) {
              toast({title: res.errorMsg || "消息内容为空"});
              return;
            }
            store.commit("setState", {key: "codeRecordMsgObj", value: {code: dialogObj.value.recordCode, list: res.data.data.list, hasMore: res.data.data.pages > 1}, child: "codeRecord"});
            dialogObj.value.type = -1;
            openChildWin('codeRecord');
          }
          break;
      }
    }

    // 切换考勤登记乐聊密码显示
    function toggleShowPwd() {
      showPwdFlag.value = !showPwdFlag.value;
    }

    // 阻止点击穿透
    function stopPropagation() {}

    return {
      userInfo,
      dialogObj,
      workObj,
      appObj,
      downloadToolsObj,
      attendanceObj,
      searchBoxRef,
      attendancePwdRef,
      fileChangeRef,
      appNameRef,
      showPwdFlag,

      scrollSessionTab,
      toAppUrl,
      showEdit,
      openTools,
      cancelTools,
      clockIn,
      attendance,
      openChildWin,
      nameInput,
      selUser,
      nameFocus,
      selPre,
      selNext,
      attendanceReset,
      attendanceSave,
      setAddAppType,
      appDrag,
      toggleShowAppModal,
      getAppIcon,
      errorIcon,
      toggleKey,
      toggleSelApp,
      removeSelApp,
      searchApp,
      fileChange,
      loadSelFile,
      showFileChange,
      showSelType,
      selTypeMethod,
      appNameFocus,
      doSearchApp,
      confirmApp,
      editApp,
      initAppDataApi,
      getHighlight,
      htmlEscapeAll,
      downloadPrint,
      changeVpnFlag,
      inputLimit,
      showDialog,
      dialogOperate,
      stopPropagation,
      toggleShowPwd,
    };
  },
};
</script>
<style scoped lang="scss">
.more {
  width: 100%;
  height: 100%;
  padding: 16px 0;
  position: relative;
  background: #FFFFFF !important;
  font-size: 13px;

  input {
    color: #000000;

    &:focus {
      border: 1px solid #666666 !important;
    }
  }

  ::-webkit-input-placeholder { /* WebKit browsers */
    color: #999999;
  }

  .none-tips {
    color: #000000;
    line-height: 18px;
    margin: 7px 0 36px;
    padding: 0 16px;
  }

  .show-text {
    color: #999999;
    line-height: 17px;
    padding: 0 16px;
  }

  .loading-img {
    width: 24px;
  }

  .highlight {
    cursor: pointer;
  }

  .default-search-box {
    position: relative;

    .default-search {
      position: relative;
      width: 100%;
      height: 30px;
      line-height: 30px;
      padding: 0 26px;
      border-radius: 4px;
      border: 1px solid #E0E0E0;
      color: #000000;
      background-size: 16px 16px;
      background: #FFFFFF url("/img/search/icon_search.png") no-repeat 7px center;
      background-size: 16px;
      font-size: 13px;
    }

    .icon-close {
      position: absolute;
      top: 50%;
      right: 10px;
      transform: translateY(-50%);
      width: 12px;
      height: 12px;
      background: url("/img/search/close.png") no-repeat;
      background-size: 12px;
      cursor: pointer;
    }
  }

  .app-sel-ul {
    padding-top: 1px;

    .app-sel-li {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      padding: 5px 16px;
      position: relative;

      &:hover {
        background: $styleBg1Hover;
      }

      &.move {
        box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.18) !important;
      }

      &.move-status {
        &:hover {
          box-shadow: none;
          background: none;
        }
      }

      &.sel {
        .app-sel-icon {
          background-image: url("/img/mail/check_1.png");
        }
      }

      .app-sel-icon {
        width: 14px;
        height: 14px;
        background-image: url("/img/mail/check_0.png");
        background-repeat: no-repeat;
        background-size: 100%;
        margin-right: 10px;
        flex-shrink: 0;
      }

      .app-img-box {
        width: 20px;
        height: 20px;
        overflow: hidden;
        margin-right: 8px;
        flex-shrink: 0;
        background: #F2F2F2;
        border-radius: 2px;
        position: relative;

        img {
          width: calc(100% + 2px);
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .app-name {
        flex: 1;
        color: #333333;

        &.app-name1 {
          padding-right: 16px;
        }
      }

      .app-move-icon {
        width: 16px;
        height: 16px;
        background-image: url("/img/workbench/icon_move.png");
        background-repeat: no-repeat;
        background-size: 100%;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .app-edit-icon {
        width: 16px;
        height: 16px;
        background-image: url("/img/workbench/icon_edit.png");
        background-repeat: no-repeat;
        background-size: 100%;
        margin-right: 10px;
        flex-shrink: 0;
        cursor: pointer;
      }

      .app-close-icon {
        width: 16px;
        cursor: pointer;
      }
    }
  }


  .more-title {
    font-size: 16px;
    color: #333333;
    font-weight: bold;
    line-height: 22px;
    margin-bottom: 2px;
    padding: 0 16px;
  }

  .more-btn-box {
    position: absolute;
    top: 16px;
    right: 16px;
    display: flex;
    align-items: center;

    .more-btn-helper {
      color: #666666;
      padding-left: 18px;
      line-height: 16px;
      cursor: pointer;

      &:hover {
        color: $styleColor;

        &:before {
          background-position: -16px 0;
        }
      }

      &:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        width: 16px;
        height: 16px;
        transform: translateY(-50%);
        background-image: url("/img/workbench/icon_helper.png");
        background-repeat: no-repeat;
        background-size: 32px 16px;
      }
    }

    .more-btn-add {
      padding: 4px 12px;
      color: $styleColor;
      border: 1px solid $styleColor;
      border-radius: 4px;
      margin-left: 16px;
      cursor: pointer;
    }
  }

  .classify-tab-ul {
    border-bottom: 1px solid #E0E0E0;
    display: flex;
    width: calc(100% - 32px);
    overflow-x: overlay;
    margin: 0 16px 8px;

    .classify-tab-li {
      color: #666666;
      line-height: 18px;
      padding: 10px 0;
      margin-right: 20px;
      cursor: pointer;
      flex-shrink: 0;

      &.sel {
        color: $styleColor;
        position: relative;
        font-weight: bold;

        &:after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 12px;
          height: 3px;
          background: $styleColor;
        }
      }
    }
  }

  .classify-box-ul {
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    padding: 0 6px;

    .classify-box-li {
      width: 80px;
      height: 78px;
      background: #FFFFFF;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      position: relative;
      color: #333333;
      line-height: 17px;
      margin-bottom: 8px;
      border: 1px solid transparent;

      &.hover,
      &:hover {
        box-shadow: 0px 3px 9px 0px rgba(0, 0, 0, 0.15);
        border-radius: 4px;
        border: 1px solid #E0E0E0;

        .edit {
          display: block;
        }

        &.hover {
          .edit {
            background-position: -22px 0;
          }
        }
      }

      .classify-icon-box {
        width: 36px;
        height: 36px;
        position: relative;
        overflow: hidden;
        border-radius: 4px;
        margin-bottom: 6px;
        background: #F2F2F2;

        &.no-border {
          border: none;
        }

        .classify-icon {
          width: calc(100% + 2px);
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .classify-text {
        width: 100%;
        font-size: 12px;
        text-align: center;
      }

      .edit {
        display: none;
        position: absolute;
        width: 22px;
        height: 22px;
        top: 0px;
        right: 0px;
        background-image: url("/img/workbench/icon_more.png");
        background-repeat: no-repeat;
        background-size: 44px 22px;
        cursor: pointer;

        &:hover {
          background-position: -22px 0;
        }
      }
    }

    .progress-box {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, .5);

      .progress-close {
        font-size: 25px;
        color: #f7f1f1;
        float: right;
        top: 0;
        line-height: 20px;
        border-radius: 50%;
        margin-right: 3px;
        margin-top: 3px;
      }

      progress {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 1;
        float: none;
        margin: 0;
        width: 100%;
        height: 5px;
        border: none;
        background: #ccc;

        &::-webkit-progress-bar {
          background: #b8bcc2;
        }

        &::-webkit-progress-value {
          background-color: #3888FF;
        }
      }
    }
  }

  .more-box-ul {
    position: relative;
    display: flex;
    width: 100%;
    height: calc(100% - 64px);
    overflow-y: overlay;

    .more-box-li {
      width: 100%;
      height: 100%;

      .classify-box,
      .common-box {
        width: 100%;
        border-top: 1px solid transparent;
      }

      .more-box-title {
        color: #333333;
        font-weight: bold;
        line-height: 18px;
        margin: 20px 0 2px;
        padding: 0 16px;
      }
    }
  }

  .error-data-box {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .error-data-img {
      width: 180px;
    }

    .error-data-text {
      color: #333333;
      margin: 10px 0 20px;
    }

    .error-data-btn {
      border-radius: 4px;
      border: 1px solid $styleColor;
      padding: 4px 12px;
      color: $styleColor;
      cursor: pointer;
    }
  }

  .loading-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .attendance-modal {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    .attendance-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 384px;
      transition: all .2s ease-in-out;
      box-shadow: 0 10px 28px 0 hsl(0deg 0% 45% / 30%);
      border: 1px solid #c9c9c9;
      border-radius: 5px;
      overflow: hidden;
      background: #FFFFFF;
      margin-top: -26px;
      margin-left: -30px;

      .attendance-close {
        width: 20px;
        position: absolute;
        top: 4px;
        right: 4px;
        cursor: pointer;
        padding: 4px;
      }

      .attendance-title-box {
        height: 68px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #F6F6F6;
        border-bottom: 1px solid #D4D4D4;
        color: #979797;

        .attendance-title-icon {
          width: 20px;
          height: 20px;
          background-image: url("/img/more/time.png");
          background-repeat: no-repeat;
          background-size: 100% 100%;
          margin-bottom: 6px;
        }
      }

      .attendance-time {
        padding: 6px 18px;
        border-bottom: 1px solid #DDDDDD;
        background-color: #F0F0F0;
        color: grey;
      }

      .attendance-user-box {
        height: 180px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .attendance-user-info-box {
          display: flex;
          align-items: center;
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .attendance-user-label {
            display: flex;
            justify-content: space-between;
            width: 50px;
            flex-shrink: 0;
          }

          .attendance-user-input-box {
            width: 150px;
            height: 22px;
            line-height: 20px;
            border-radius: 2px;
            outline: none;
            position: relative;

            &.attendance-user-input-box-pwd {
              input {
                padding-right: 25px;
              }
            }

            input {
              padding: 0 10px;
              width: 100%;
              height: 100%;
              border: 1px solid #DDDDDD;

              &:focus + .search-box {
                display: block;
              }
            }

            .search-box {
              display: none;
              position: absolute;
              top: 22px;
              left: -1px;
              background: #ffffff;
              z-index: 2;
              border: 1px solid #cfcecb;
              max-height: 96px;
              width: 150px;
              overflow-y: auto;

              li {
                line-height: 24px;
                padding-left: 10px;
                color: #000;
                position: relative;
                max-width: 100%;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;

                &.curr,
                &:hover {
                  background: $styleBg1Hover;

                  .del {
                    display: block;
                  }
                }

                .del {
                  position: absolute;
                  display: none;
                  right: 5px;
                  top: 3px;
                  cursor: pointer;
                }
              }
            }

            .icon-pwd {
              width: 15px;
              height: 15px;
              background-image: url("/img/login/pwd_toggle.png");
              background-repeat: no-repeat;
              background-size: 60px 15px;
              cursor: pointer;

              &:hover {
                background-position: -15px 0;
              }

              &.show {
                background-position: -30px 0;

                &:hover {
                  background-position: -45px 0;
                }
              }
            }

            .show-pwd {
              position: absolute;
              top: 50%;
              right: 8px;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }
        }
      }

      .attendance-btn-box {
        height: 40px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #979797;
        background-color: #F6F6F6;
        border-top: 1px solid #DDDDDD;
        padding: 0 18px;

        .attendance-btn-group {
          display: flex;
          align-items: center;
        }

        .attendance-btn {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 62px;
          height: 24px;
          color: grey;
          text-align: center;
          border-radius: 4px;
          border: 1px solid #bebebe;
          cursor: pointer;

          &.attendance-save {
            background-color: #028FCF;
            color: #FFFFFF;
            margin-left: 10px;
            border: none;
          }
        }
      }
    }
  }

  .add-app-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20;
    background: rgba(0, 0, 0, 0.5);

    .add-app-box {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 591px;
      transform: translate(-50%, -50%);
      background: #FFFFFF;
      border-radius: 4px;
      overflow: hidden;

      &.add-app-box1 {
        width: 412px;
        overflow: visible;
      }

      .add-app-close {
        width: 20px;
        position: absolute;
        top: 5px;
        right: 12px;
        cursor: pointer;
        padding: 4px;
      }

      .add-app-title {
        height: 30px;
        line-height: 30px;
        padding-left: 16px;
        background: #F0F0F0;
        color: #333333;
        font-weight: bold;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
      }

      .add-app-default-add {
        flex-shrink: 0;
        color: $styleColor;
        padding-left: 20px;
        background: url("/img/workbench/icon_add.png") no-repeat left center;
        background-size: 16px;
        line-height: 16px;
        cursor: pointer;
      }

      .add-app-tab-box {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .add-app-tab-ul {
          display: flex;
          padding: 0 16px;

          .add-app-tab-li {
            padding: 12px 0;
            margin-right: 16px;
            cursor: pointer;
            color: #666666;

            &.sel {
              color: $styleColor;
              position: relative;
              font-weight: bold;

              &:after {
                content: "";
                position: absolute;
                bottom: 5px;
                left: 50%;
                transform: translateX(-50%);
                width: 12px;
                height: 3px;
                background: $styleColor;
              }
            }
          }
        }

        .add-app-tab-right {
          padding-right: 16px;
        }

      }

      .add-app-content-ul {
        width: calc(100% - 32px);
        height: 340px;
        margin: 0 16px;
        border: 1px solid #E0E0E0;
        border-radius: 4px;

        .add-app-content-li {
          width: 100%;
          height: 100%;
          display: flex;

          .add-app-normal-box {
            width: 50%;
            height: 100%;
            flex-shrink: 0;

            &.add-app-normal-left {
              border-right: 1px solid #E0E0E0;
            }

            .add-app-normal-header {
              padding: 12px 16px 7px;

              .add-app-normal-info-box {
                height: 30px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                color: #666666;

                .add-app-normal-info-title {
                  color: #333333;
                  font-weight: bold;
                }
              }
            }

            .add-app-normal-content {
              width: 100%;
              height: calc(100% - 50px);
              overflow-y: auto;

              .loading-img-box,
              .show-text {
                display: flex;
                justify-content: center;
                padding-top: 106px;
              }
            }
          }

          .add-app-default-box {
            width: 100%;
            height: 100%;
            padding-bottom: 12px;

            .add-app-default-header {
              display: flex;
              align-items: center;
              padding: 12px 16px 0;
              height: 42px;

              &.add-app-default-header-border {
                border-bottom: 1px solid #D8D8D8;
                padding: 0;
                margin: 0 16px;

                .default-search-box {
                  display: none;
                }
              }

              .add-app-default-search-box {
                flex: 1;
                margin-right: 14px;
              }
            }

            .add-app-default-content {
              width: 100%;
              height: calc(100% - 44px);

              .add-app-default-ul-box {
                width: 100%;
                height: 100%;

                .add-app-default-ul-title {
                  margin: 12px 0 6px;
                  color: #999999;
                  line-height: 18px;
                  padding: 0 16px;
                }

                .app-sel-ul {
                  width: 100%;
                  height: calc(100% - 21px);
                  overflow-y: auto;
                }
              }

              .loading-img-box,
              .show-text,
              .none-tips {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 116px;
              }

              .public-app-box {
                width: 100%;
                height: 100%;
                display: flex;
                border-top: 1px solid #E0E0E0;
                margin-top: 12px;

                .public-app-left {
                  width: 140px;
                  height: 100%;
                  flex-shrink: 0;
                  border-right: 1px solid #E0E0E0;
                  overflow-y: overlay;
                  padding-top: 4px;

                  .public-app-list {
                    padding: 6px 16px;
                    color: #666666;

                    &.sel {
                      color: $styleColor;
                      background: #FFF4F4;
                      font-weight: bold;
                    }

                    &:hover {
                      color: $styleColor;
                      background: #FFF4F4;
                    }
                  }
                }

                .public-app-right {
                  width: calc(100% - 140px);
                  height: 100%;
                  flex-shrink: 0;
                  overflow-y: auto;
                  padding-top: 4px;
                }
              }
            }
          }
        }
      }

      .add-app-btn-box {
        height: 62px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;

        .add-app-btn {
          padding: 6px 16px;
          background: #FFFFFF;
          border-radius: 4px;
          border: 1px solid #E0E0E0;
          cursor: pointer;

          &.add-app-btn-save {
            background: $styleColor;
            border: none;
            color: #FFFFFF;
            margin-left: 16px;
          }
        }
      }

      .add-app-info-box {
        padding: 20px 16px 0;

        .add-app-info-content {
          display: flex;
          font-size: 12px;
          color: #666666;
          margin-bottom: 10px;

          &:last-child {
            margin-bottom: 0;
          }

          .add-app-info-title {
            width: 57px;
            line-height: 30px;
            margin-right: 10px;
            text-align: right;
            flex-shrink: 0;

            &.width-auto {
              width: auto;
            }

            &:before {
              content: "*";
              color: $styleColor;
            }
          }

          .add-app-info-input {
            flex: 1;
            height: 30px;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            padding: 0 10px;
          }

          .add-app-info-sel-box {
            flex: 1;
            position: relative;
            display: flex;

            &.arrow {
              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 10px;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 4px 0 4px;
                border-style: solid;
                border-color: #999 transparent transparent transparent;
              }
            }

            .add-app-info-input {
              padding-right: 24px;
            }

            .add-app-info-sel-ul-box {
              position: absolute;
              top: 31px;
              width: 100%;
              max-height: 158px;
              background: #FFFFFF;
              box-shadow: 0px 2px 8px 0px rgba(51, 51, 51, 0.25);
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              padding: 4px 0;
              z-index: 1;
              overflow-y: overlay;
              display: flex;
              justify-content: center;
              color: #000000;

              .add-app-info-sel-ul {
                width: 100%;

                .add-app-info-sel-li {
                  height: 30px;
                  line-height: 30px;
                  padding: 0 10px;

                  &:hover {
                    background: $styleBg1Hover;
                  }
                }
              }

              .show-text {
                padding: 6px 0;
              }

              .loading-img-box {
                padding: 1px 0;
              }
            }
          }

          .add-app-icon-box {
            .add-app-icon-content {
              width: 60px;
              height: 60px;
              border-radius: 8px;
              border: 1px solid #E0E0E0;
              position: relative;
              overflow: hidden;
              display: flex;
              justify-content: center;
              align-items: center;
              position: relative;

              .add-app-icon-add-input {
                display: none;
              }

              .add-app-icon-add {
                cursor: pointer;
                width: 100%;
                height: 100%;
                background: url("/img/workbench/icon_add_gray.png") no-repeat center center;
                background-size: 16px;
              }

              .add-app-icon-img {
                width: calc(100% + 2px);
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              }

              .add-app-icon-close {
                width: 16px;
                height: 16px;
                background: url("/img/workbench/icon_sel.png") no-repeat -16px 0;
                background-size: 32px 16px;
                position: absolute;
                top: 0;
                right: 0;
                cursor: pointer;
              }
            }

            .add-app-icon-tips {
              color: #999999;
              margin-top: 8px;
            }
          }

          .add-app-icon-img-box {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            padding: 5px;

            .add-app-icon-img-content {
              width: 44px;
              height: 44px;
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              position: relative;
              overflow: hidden;
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 4px;
              margin: 5px;

              &:hover,
              &.sel {
                border: 1px solid $styleColor;
              }

              .add-app-icon-img {
                width: 100%;
              }

              .add-app-icon-sel {
                width: 16px;
                height: 16px;
                background: url("/img/workbench/icon_sel.png") no-repeat;
                background-size: 32px 16px;
                position: absolute;
                top: 0;
                right: 0;
              }
            }

          }
        }
      }
    }
  }

  :deep(.code-record-dialog .content) {
    padding: 16px;
  }

  .ly-dialog-default-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 10px;

    &.center {
      align-items: center;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .ly-dialog-default-label {
      font-size: 13px;
      color: #666666;
      line-height: 30px;
      width: 80px;
      text-align: right;

      .ly-dialog-default-tips {
        color: #EE3939;
        margin-right: 5px;
      }
    }

    .ly-dialog-default-detail {
      width: 252px;
      position: relative;

      .ly-dialog-default-sel-text,
      .ly-dialog-default-input {
        width: 100%;
        line-height: 28px;
        padding-left: 10px;
        border: 1px solid #E0E0E0;
        border-radius: 4px;

      }

      .ly-dialog-default-sel-text {
        color: #545454;

        &:after {
          content: "";
          position: absolute;
          top: 50%;
          right: 10px;
          transform: translateY(-50%);
          width: 0;
          height: 0;
          border-width: 4px 4px 0;
          border-style: solid;
          border-color: #000000 transparent transparent transparent;
        }

        &.show {
          color: #000000;
        }

        &.sel {
          &:after {
            border-width: 0 4px 4px 4px;
            border-color: transparent transparent #000000 transparent;
          }
        }
      }

      .ly-dialog-default-sel-ul {
        width: 100%;
        position: absolute;
        top: 30px;
        left: 0;
        box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
        border-radius: 2px;
        border: 1px solid #CCCCCC;
        background: #FFFFFF;
        z-index: 1;

        .ly-dialog-default-sel-li {
          width: 100%;
          line-height: 30px;
          padding-left: 10px;

          &:hover {
            background: $styleBg1Hover;
          }
        }
      }

      .ly-dialog-default-textarea {
        width: 100%;
        height: 110px;
        background: #FFFFFF;
        border-radius: 2px;
        border: 1px solid #E0E0E0;
        padding: 10px;
      }

      .ly-dialog-default-textarea-text {
        position: absolute;
        right: 10px;
        bottom: 10px;
      }

      .red {
        color: $styleColor;
      }
    }
  }
}
</style>
