<template>
  <div class="quick-acting">
    <ly-form ref="formRef" :formData="formData" :rules="formDataRules">
      <div class="tip-text">
        <span class="text">"快速行动"管理工具使用说明</span>
        <span class="btu" @click="onOpenQuickRename">查看<img src="/img/schedule/right_icon.png"> </span>
      </div>
      <ly-form-item required prop="batchName" label="行动代号:">
        <ly-form-input class="title-input"  v-model="formData.batchName" placeholder="请输入行动代号" :maxLength="20" >
        </ly-form-input>
      </ly-form-item>
      <ly-form-item required prop="templateId" label="任务模板:">
        <ly-form-select :options="taskTemplateOptions" v-model="selectTaskTemplate" keyValue="id" :filter="false" placeholder="请选择任务模板" @change="onSelectTaskTemplateChange"/>
      </ly-form-item>
      <ly-form-item required prop="subTaskFormList" label="任务名称:">
        <div class="flex-vcenter g-mr-10 g-mb-6" v-for="(subTask,index) in formData.subTaskFormList">
          <ly-form-input v-model="subTask.taskName" placeholder="请输入任务名称"  width="140px" :maxLength="15"/>
          <ly-form-radio class="ml20" v-model="subTask.taskType" :label="1">做完打勾</ly-form-radio>
            <ly-form-radio class="ml16_im" v-model="subTask.taskType" :label="2">进行填报</ly-form-radio>
            <template v-if="subTask.taskType===2">
              <div class="font ml16_im">
                目标
              </div>
              <ly-form-input v-model="subTask.taskNumTarget" placeholder="输入目标" :maxLength="20" width="80px" :regularInput="onTaskNumRegularInput"></ly-form-input>
            </template>
          <i class="close-ico margin-right"  @click="onTaskTemplateDel(subTask,index)"></i>
        </div>
        <div class="text-btu mt6" @click="addSubTaskFormList()" v-show="formData.subTaskFormList.length<20">
          <i class="add-icon"></i>
          新增任务项（{{formData.subTaskFormList.length}}/20）
        </div>
      </ly-form-item>

      <ly-form-item required prop="stopTime" label="截止时间:">
        <div class="flex-vcenter g-mr-10">
          <button @click.stop="checkStopDate('12:00')" :class="{'time-btn btn':true,'by_now':isStopDate('12:00')}">今天12:00</button>
          <button @click.stop="checkStopDate('18:30')" :class="{'time-btn btn':true,'by_now':isStopDate('18:30')}">今天18:30</button>
          <button @click.stop="checkStopDate('23:30')" :class="{'time-btn btn ':true,'by_now':isStopDate('23:30')}">今天23:30</button>
          <ly-form-calendar ref="calendarRef" v-model="formData.stopDate" placeholder="请选择截止日期" width="110px" :minDateDisabled="minDateDisabled"></ly-form-calendar>
          <ly-form-select :options="timeOptions" :filter="false" v-model="formData.stopTime" placeholder="时间" width="82px">
            <template #rightIcon>
              <i class="time-icon"></i>
            </template>
          </ly-form-select>

        </div>
      </ly-form-item>

      <ly-form-item prop="actionDescription" label="行动说明:" class="ml10">
        <ly-form-input type="textarea" autoHeight :rows="4" v-model="formData.actionDescription" placeholder="请输入行动说明（选填）" :lengthFilterTrim="false" :max-length="300" />
      </ly-form-item>

      <ly-form-item required prop="manTypeOption" label="参与人员:"  class="no-line-height" :style="{ 'align-items':formData.manTypeOption===2?'center':'inherit'}">
        <div class="flex-vcenter g-mr-10 ">
          <ly-form-radio  v-model="formData.manTypeOption" :label="1">指定人员</ly-form-radio>
          <ly-form-radio class="ml16_im" v-model="formData.manTypeOption" :label="2">谁都能填</ly-form-radio>
        </div>
        <template v-if="formData.manTypeOption === 1" >
          <div class="flex-vcenter g-mr-10 mt10">
            <ly-form-emp-or-group-search v-model="selectEmpOrGroup" placeholder="添加人员或关联群" width="400px" />
            <div class="text-btu ml10" style="margin-left: 13px;" @click="selectEmpOrGroup=[]">
              清空所选
            </div>
          </div>
          <!-- 标签 -->
          <div class="mt8" v-if="groupTag.length>0">
            <div class="tag-box">
              <div class="label">标签：</div>
              <div class="tag-item" :style="toggleTagStatus?'':'height:24px;overflow:hidden;'"  >
                <div class="tag" v-for="item of groupTag" @click="onClickTag(item)"> {{ item.label }}</div>
              </div>
              <div class="toggle-box" @click="toggleTagStatus=!toggleTagStatus" v-if="groupTag.length>1">
                <span class="toggle-label">{{ toggleTagStatus?'收起':'展开' }}</span>
                <img  class="toggle-icon" :src="toggleTagStatus?'/img/form/up.png':'/img/form/down.png'">
              </div>
            </div>
          </div>
          <!-- 已选人 -->
          <div class="mt8 w-275" v-if="selectEmpOrGroup.length!==0" style="">
            <div v-for="(item,index) in selectEmpOrGroup" class="g-mt6">
              <div class="flex-bet flex-vcenter " style="width: 460px">
                <div class="emp-info">
                  <span class="font-sm">{{item.type==='empList'?item.data.empName:item.data.userName }} </span>
                  <span class="font-rename">{{item.type==='empList'?item.data.deptName:item.data.userName}} </span>
                </div>
                <i class="close-ico" @click="onDelSelectEmpOrGroup(item,index)"></i>
              </div>
            </div>
          </div>
        </template>
      </ly-form-item>
      <div v-if="formData.manTypeOption !== 1" >
        <div class="mt8 font-rename">
          <p>任何人都可以参加本行动的填报</p>
          <p>您可以将它转发给任何人</p>
        </div>
      </div>
      <ly-form-item  prop="remindFormList"  label="提醒时间:" class="ml10 mb-0-important" v-show="formData.manTypeOption===1">
        <div class="flex-vcenter g-mb-6" v-for="(remind,index) in formData.remindFormList" :key="'remind_'+index">
          <ly-form-select  :filter="false"  :options="selectTimeOptions" key-value="num" v-model="remind.num" placeholder="请选择提醒时间" :optionDisabled="selectTimeOptionsDisabled"/>
          <i class="close-ico ml10" @click="onDelRemindForm(remind,index)"></i>
        </div>
        <div class="text-btu mt6 " @click="onAddRemindForm">
          <i class="add-icon"></i>
          添加提醒
        </div>
      </ly-form-item>
      <ly-form-item  prop="remindFormList"  label="更多设置:" class="ml10 mb-0-important" >
        <div class="toggle-box mt-7" @click="toggleSettingStatus=!toggleSettingStatus">
          <span class="toggle-label">{{ toggleSettingStatus?'收起':'展开' }}</span>
          <img  class="toggle-icon" :src="toggleSettingStatus?'/img/form/up.png':'/img/form/down.png'">
        </div>
      </ly-form-item>

      <template v-if="toggleSettingStatus">
        <ly-form-item  prop="remindFormList"  label="填报要求:" class="ml10 mb-0-important">
          <div class="flex-vcenter g-mr-10 mt-7">
            <ly-form-check  v-model="formData.screenMustBeUploaded" >截图必传</ly-form-check>
            <ly-form-check class="ml16_im" v-model="formData.remarkMustBeUploaded">备注必传</ly-form-check>
          </div>
        </ly-form-item>
        <ly-form-item  prop="remindFormList"  label="进度查看:" class="ml10 mb-0-important">
          <div class="flex-vcenter g-mr-10 mt-7">
            <ly-form-radio  v-model="formData.publicViewing" :label="1">公开，参与人员均可查看进度</ly-form-radio>
            <ly-form-radio class="ml16_im" v-model="formData.publicViewing" :label="2">隐藏，仅创建人可见进度</ly-form-radio>
          </div>
        </ly-form-item>
      </template>
    </ly-form>
  </div>
</template>
<script setup>
import LyForm from '@/components/form/LyForm.vue'
import LyFormItem from '@/components/form/LyFormItem.vue'
import {computed, defineProps, nextTick, onMounted, ref, watch} from "vue";
import LyFormInput from "@comp/form/LyFormInput.vue";
import LyFormSelect from "@comp/form/LyFormSelect.vue";
import LyFormRadio from "@comp/form/LyFormRadio.vue";
import LyFormCalendar from "@comp/form/LyFormCalendar.vue";
import lyFormEmpOrGroupSearch from "@comp/form/LyFormEmpOrGroupSearch.vue";
import {addTaskBatchApi, searchTemplateByTypeApi} from '@utils/net/api';
import {alert, toast,loading} from "@comp/ui";
import {dateFormat as dateFormatUtils, dateFormat, openForward, sortTeamMembers, userLocalStorage} from "@utils";
import {useStore} from "vuex";

import config from "/config.js"
import state from "@store/state";
import LyFormCheck from "@comp/form/LyFormCheck.vue";

let env = config[config.env];

// 标签在展开状态
const toggleTagStatus = ref(false);

// 更多设置展开状态
const toggleSettingStatus = ref(false);

const store = useStore();
// 当前会话信息
let sessionInfo = ref(remote.store.getters.getSessions({id: store.getters.getCurrentSession.id}));

// 任务字段模版
const baseFieldTemplate = {
  // 任务类型 1选择型 2输入型
  taskType: 1,
  // 任务名称
  taskName: "",
  // 完成任务目标值
  taskNumTarget: null,
}

const now = new Date();
now.setDate(now.getDate()-1);

const props = defineProps({
  data:{
    type: Object,
    default: {
      // 当前任务id
      id:null,
    }
  },
  oldData:{
    type: Object,
    default:{}
  },
  // 非群下打开快速行动
  nonSwarm:{
    type: Boolean,
    default: false
  }
})

// 当前代号迭代
const batchNum = ref(1);
// 最小选择时间
const minDateDisabled = ref(now.getTime());
// 表单ref
const formRef = ref(null);
// 日期ref
const calendarRef = ref(null);
// 表单数据
const formData = ref({
  // 类型 1快速行动
  batchType: 1,
  // 行动代号
  batchName:"",
  // 模版id
  templateId:null,
  // 行动说明
  actionDescription:"",
  // 指定参与人员 1指定人员 2谁都能填
  manTypeOption: 1,
  // 进度查看 1公开 2隐藏
  publicViewing: 1,
  // 截图是否必传
  screenMustBeUploaded:false,
  // 备注是否必传
  remarkMustBeUploaded:false,
  // 截止日期
  stopDate: null,
  // 截止时间
  stopTime: null,
  // 子任务列表
  subTaskFormList:[],
  // 提醒集合
  remindFormList:[

  ],
})
watch(()=>formData.value.manTypeOption,()=>{
  if(formData.value.manTypeOption === 2){
    selectEmpOrGroup.value = []
  }
})

watch(()=>toggleSettingStatus.value,(val)=>{
  if(val){
    nextTick(()=>{
      document.querySelector(".quick-acting").scrollIntoView({ behavior: 'smooth', block: 'end'})
    })
  }
})
// 表单校验规则
const formDataRules = {
  batchName:[
    {type: "required", message: '请输入行动代号'},
    {type: "notEmpty", message: '请输入行动代号'},
    {maxLength: 20, message: '行动代号不能超出20个字符'},
  ],
  templateId:[
    {type: "required", message: '请选择任务模版'},
  ],
  subTaskFormList:[
    {
      type:"validator",
      message:"任务名称不能为空",
      validator:()=>{
        return formData.value.subTaskFormList.find(item => !item.taskName || item.taskName.trim().length === 0) == null;
      }
    },
    // 20241127需求： 可以为空，但是不能为0
    {
      type:"validator",
      message:"任务目标不能为0，若不需要目标值可为空",
      validator:()=>{
        return formData.value.subTaskFormList.find(item => item.taskType === 2 && item.taskNumTarget!== "" && item.taskNumTarget===0) == null;
      }
    }
  ],
  stopTime:[
    {type: "required", message: '请选择截止时间'},
    {
      type:"validator",
      message:"请选择截止时间",
      validator:()=>{
        return formData.value.stopDate && formData.value.stopTime;
      }
    }
  ],
  manTypeOption:[
    {
      type:"validator",
      message:"请选择参与人员",
      validator:()=>{
        if(formData.value.manTypeOption===1){
          return selectEmpOrGroup.value.length!==0
        }
        return true;
      }
    }
  ]
}

// 任务模版列表
const taskTemplateOptions = ref([])
// 选择的模版
const selectTaskTemplate = ref(null)
// 选择的人与群
const selectEmpOrGroup = ref([])
// 提醒时间
const selectTimeOptions = ref([
  {
    label:"提前5分钟",
    value: 5,
  },
  {
    label:"提前15分钟",
    value: 15,
  },
  {
    label:"提前30分钟",
    value: 30,
  },
  {
    label:"提前1小时",
    value: 60,
  },
])

// 时间选项
const timeOptions = ref([
  ...Array.from({length: 32}, (_, i) => {
    let hours = Math.floor(i / 2) + 8; // 从8点开始
    let minutes = i % 2 === 0 ? '00' : '30';
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  }),
  ...Array.from({length: 16}, (_, i) => {
    let hours = Math.floor(i / 2);
    let minutes = i % 2 === 0 ? '00' : '30';
    return `${hours.toString().padStart(2, '0')}:${minutes}`;
  })
].map(item => ({
  label: item,
  key: item,
  value: item
})));

// 可选择时间
function selectTimeOptionsDisabled(option){
   return formData.value.remindFormList.some(remind => remind.num === option.value)
}
// 切换停止时间
function checkStopDate(time){
  const date = new Date()
  calendarRef.value.selItemDate({
    year: date.getFullYear(),
    month: date.getMonth() + 1,
    day: date.getDate()
  })
  formData.value.stopTime = time;
}
// 判断是否为指定今天的某个时间段
function isStopDate(time){
  return formData.value.stopDate === dateFormatUtils(new Date(), "yyyy-MM-dd") && formData.value.stopTime === time;
}


// 初始化模版
async function initSearchTemplateByType(){
  // 业务端 1房源 2客源 3人事 4项目 5资产 6成交 7快速行动
  const { success,data } = await searchTemplateByTypeApi({templateType:7})
  if(success){
    taskTemplateOptions.value = data.map(item => ({
      label: item.templateName,
      key:item.id,
      value: item
    }))

    const defaults = taskTemplateOptions.value.find((v)=>{
      return v.label === "通用快速行动"
    })
    if(defaults){
      onSelectTaskTemplateChange(defaults.value)
    }
  }
}

// 添加子任务
function addSubTaskFormList(subTask=null){
  if(formData.value.subTaskFormList.length+1>20){
    return;
  }
  if(subTask){
    formData.value.subTaskFormList.push(subTask)
  }else{
    formData.value.subTaskFormList.push(JSON.parse(JSON.stringify(baseFieldTemplate)))
  }
}
// 删除子任务
function onTaskTemplateDel(subTask,index){
  if(formData.value.subTaskFormList.length<=1){
    return toast({title: "至少添加一个"});
  }
  formData.value.subTaskFormList.splice(index,1)
}
// 选择任务模版
function onSelectTaskTemplateChange(option){
  formData.value.templateId = option.id
  selectTaskTemplate.value = option;
  formData.value.subTaskFormList = [];
  option?.fieldList?.forEach(v=>{
    if(v.fieldType===2){
      let taskType = 1;
      let name =  v.name;
      if(v.name.endsWith("#1")){
        name = v.name.replace("#1","");
        taskType = 2;
      }
      addSubTaskFormList({
        // 任务类型 1选择型 2输入型
        taskType: taskType,
        // 任务名称
        taskName: name,
        // 完成任务目标值
        taskNumTarget: null,
      })
    }
  })
}
// 限制输入
function onTaskNumRegularInput(value){
  return value.replace(/[^\d]/g, '').replace(/^(\d{10})\d+$/, '$1');
}
// 删除某个人员
function onDelSelectEmpOrGroup(option,index){
    selectEmpOrGroup.value.splice(index,1)
}
// 添加时间提醒
function onAddRemindForm(){
  formData.value.remindFormList.push({
    num:null,
    taskType: 1
  })
}
// 删除时间提醒
function onDelRemindForm(option,index){
  formData.value.remindFormList.splice(index,1)
}

async function submit(type){
  if(!formRef.value.validate()){
    return false;
  }
  formData.value.subTaskFormList = formData.value.subTaskFormList.map(v=>{
    if(v.taskType===1){
      v.taskNumTarget = 1;
    }
    return v;
  })
  loading()
  const {success,errorInfo,data} = await addTaskBatchApi({
    form: JSON.stringify({
      listFrom:selectEmpOrGroup.value.map(v=>{
        return {
          templateId: formData.value.templateId,
          taskName: formData.value.batchName,
          taskDesc: formData.value.actionDescription,

          taskExecutorNumber: v.data.empNumber || v.data.workerId,
          taskExecutorName: v.data.empName  || v.data.workerName,
          taskExecutorDeptName: v.data.deptName,
          taskStopTimeStr: dateFormat(new Date(`${formData.value.stopDate} ${formData.value.stopTime}`),"yyyy-MM-dd HH:mm:ss"),
        }
      }),
      batchInfoForm:{
        subTaskFormList: formData.value.subTaskFormList,
        stopTime: new Date(`${formData.value.stopDate} ${formData.value.stopTime}`).getTime(),
        actionDescription: formData.value.actionDescription,
        manTypeOption: formData.value.manTypeOption,
        batchType: formData.value.batchType || "1",
        templateId: formData.value.templateId,
        batchName: formData.value.batchName,
        remindFormList: formData.value.remindFormList.map(v=>{
          return {
            taskType:1,
            num: v.num
          }
        }),
        screenMustBeUploaded: formData.value.screenMustBeUploaded,
        remarkMustBeUploaded: formData.value.remarkMustBeUploaded,
        publicViewing: formData.value.publicViewing,
      }
    })
  })
  if(success){
    userLocalStorage({key: "batchNum",value:Number(batchNum.value)+1}, 1);
    sendToForward(data,formData.value,type)
    loading().hide()
    return true;
  }else{
    toast({title: errorInfo})
    loading().hide()
    return false;

  }
}

/**
 * 发送消息
 * @param id 批次号
 * @param formData 表单数据
 * @param type 转发类型 1：打开转发窗口 2：转发到此群
 */
function sendToForward(id,formData,type){
  /**
   * 这里的卡片需要跟另一个地方保持一致
   * @see /src/components/schedule/QuickActingModel.vue#onClickShare
   */
  const date = new Date(`${formData.stopDate} ${formData.stopTime}`);
  const msg = {
    type:'custom',
    content:{
      type: "quickActing",   // 固定消息体类型
      data:{
        "id":id,                // 批次号
        "bodyHeight": 108,      // 内容体高度
        "titleHeight": 23,      // 标题高度

        "title":formData.batchName,  // 标题
        "stopTime": dateFormat(date,"yyyy-MM-dd HH:mm"),    // 截止时间文本的形式
        "stopTimeStamp" : date.getTime(),  // 截止时间戳
        "actionDescription":formData.actionDescription,
        "taskFrom":formData.subTaskFormList.map(v=>{
          return {
            taskName:v.taskName,
            taskType: v.taskType,
            taskNumTarget:  v.taskNumTarget || "",
          }
        }),
        "pcFillInUrl":`${env.jjsHome}/lyj-front/lbgapp/quickActing.html`,
        "appFillInUrl":"/addQuickActingTaskApp.html",
        "pcProgressUrl":`${env.jjsHome}/lyj-front/mission-center/#/progress`,
        "appProgressUrl":"/progressQuickActingApp.html",
      }
    }
  }
  if(type===1){
    openForward(msg)
  }else{
    store.dispatch("doSendMsg", {sessionId: sessionInfo.value.id, messages: [msg]});
  }
}
function initBatchName(){
  // 行动代号从缓存中拿
  batchNum.value = userLocalStorage({key: "batchNum",value:1}, 2);
  formData.value.batchName = store.getters.getUserInfo.workerName + dateFormat(new Date(),"yyyyMMdd") + "行动" + batchNum.value + "号令"
}


function initSelectEmpOrGroup(list) {
  toggleTagStatus.value = false;
  const uniqueAccounts = new Set();
  // 去重复
  selectEmpOrGroup.value = list.reduce((accumulator, item) => {
    if (/^\d{6}$/.test(item.account) && !uniqueAccounts.has(item.account)) {
      uniqueAccounts.add(item.account); // 添加到 Set 中以便后续去重
      let res = item.detailInfo;
      res.empName = res.userName;
      accumulator.push({
        type: "empList",
        data: res
      });
    }
    return accumulator;
  }, []);
}
// 标签列表
const groupTag = computed(()=>{
  let tags = [];
  if(!props.nonSwarm){
    tags.push({
      // 标签类型 1本群全员  2讨论组标签 3同事标签
      type:'1',
      label: "本群全员",
      value:[]
    })
  }

  state.groupSettings.map(v=>{
    if( v.type === 3){
      tags.push({
        type:'3',
        label: v.name,
        value:v.value
      })
    }
    // else if(v.type === 2){
    //   tags.push({
    //     type:'2',
    //     label: v.name,
    //     value:v.value
    //   })
    // }
  })
  return tags;
})

async function onClickTag(tag){
  let resList = [];
  if(tag.type === '1'){
    let thisId = sessionInfo.value.to;
    let { err,obj} = await store.dispatch("getTeamMembers", {id: thisId})
    if(err){
      toast({title: "获取群信息失败", type: 2});
    }
    // 群主、管理员、普通成员排序
    resList = sortTeamMembers(obj);
  }else if(tag.type === '2'){
    // 讨论组
    for(let v of tag.value.split(",")){
      let res = await store.dispatch("getTeamMembers", {id: v})
      if(res.err){
        toast({title: "获取群信息失败", type: 2});
        resList = [];
        break;
      }
      // 获取群员详情
      const personInfo = await store.dispatch("getPersons", {doneFlag: true, account: res.obj.map(item => {return item.account}).filter(item => /^\d{6}$/.test(item))});
      resList = [...resList,...Object.values(personInfo).map(v=>{
        return {
          account: v.workerNo,
          detailInfo: v
        }
      })];
    }
  }else if(tag.type === '3'){
    const personInfo = await store.dispatch("getPersons", {doneFlag: true, account:  tag.value.split(",").filter(item => /^\d{6}$/.test(item)) });
    resList = Object.values(personInfo).map(v=>{
      return {
        account: v.workerNo,
        detailInfo: v
      }
    });
  }
  if(selectEmpOrGroup.value.length>0){
    alert({
      content: `将覆盖您已选的人员，是否继续`,
      done: (type) => {
        type === 1 && initSelectEmpOrGroup(resList)
      }
    });
  }else{
    initSelectEmpOrGroup(resList)
  }
}
// 打开快速行动说明
function onOpenQuickRename(){
  store.dispatch("setOpenWindow", ["https://i.leyoujia.com/lyjEtherpad/p/3ffded02-82f7-4b6f-bbfb-3d29243d5fb0?docId=********"]);
}

onMounted(async ()=>{

  addSubTaskFormList()
  await initSearchTemplateByType();
  initBatchName();
  formData.value.remindFormList.push( {
    label:"提前5分钟",
    num:5,
    taskType: 1
  })
  if(props.oldData){
    formData.value = props.oldData;
    if(props.oldData.selectEmpOrGroup){
      selectEmpOrGroup.value = props.oldData.selectEmpOrGroup;
    }
    if(![1,2].includes(formData.value.manTypeOption)){
      formData.value.manTypeOption = 1;
    }
    if(!formData.value.batchName?.trim()){
      initBatchName()
    }
    const find = taskTemplateOptions.value.find(v=>v.key === props.oldData.templateId);
    if(find){
      selectTaskTemplate.value = find.value;
    }else{
      const defaults = taskTemplateOptions.value.find((v)=>{
        return v.label === "通用快速行动"
      })
      if(defaults){
        onSelectTaskTemplateChange(defaults.value)
      }
    }


  }
})

defineExpose({
  submit,formData,selectEmpOrGroup
})

</script>
<style lang="scss" >
.quick-acting{
  .tip-text{
    font-size: 12px;
    padding: 4px 10px;
    color: #999999;
    background: #EFF4FF;
    border-radius: 4px;
    margin-bottom: 12px;
    display: flex;

    .text{
      font-weight: 400;
      font-size: 12px;
      color: #3D5688;
    }
    .btu{
      margin-left: 16px;
      font-weight: 500;
      font-size: 12px;
      color: #3D5688;
      cursor: pointer;

      display: flex;
      align-items: center;

      img{
        width: 14px;
        height: 14px;
      }
    }
  }
  .title-input{
    .input{
      font-weight: 600;
      font-size: 14px;
      color: #000000;
      line-height: 22px;
    }
  }
  .close-ico{
    background: url(/img/schedule/delete.png) no-repeat;
    width: 10px;
    height: 10px;
    cursor: pointer;
    background-size: 8px 8px;
  }
  .font{
    font-size: 12px;
    line-height: 1;
  }
  .text-btu{
    display: inline-block;
    font-weight: 400;
    font-size: 12px;
    color: #3D5688;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    cursor: pointer;
  }
  .add-icon{
    background: url("/img/schedule/add_left.png") no-repeat;
    width: 14px;
    height: 14px;
    background-size: 14px 14px;
    display: inline-block;
    transform: translateY(3px);
  }
  .time-btn{
    background: #FFFFFF;
    color: #000000;
    padding: 6px 9px;
    border-radius: 4px;
    overflow: hidden;
    line-height: 17px;
    font-size: 12px;
    border: 1px solid #E0E0E0;
    cursor: pointer;
  }
  .by_now{
    border: 1px solid #E03236;
    color: #E03236;
  }
  .time-icon{
    background: url("/img/form/icon-time.png") no-repeat;
    width: 17px;
    height: 16px;
    background-size: 16px 16px;
  }
  .mt2{
    margin-top: 2px;
  }
  .mt6{
    margin-top: 6px;
  }
  .mt8{
    margin-top: 8px;
  }
  .ml25{
    margin-left: 25px;
  }
  .ml15{
    margin-left: 15px;
  }
  .ml20{
    margin-left: 20px;
  }
  .mb14{
    margin-bottom: 14px;
  }
  .g-mt14 + .g-mt14{
    margin-top: 4px;
  }
  .w-275{
    width: 275px;
  }
  .font-rename{
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    text-align: center;
    font-style: normal;
  }
  .emp-info{

    .font-sm{
      font-weight: 400;
      font-size: 12px;
      color: #000000;
      line-height: 16px;
      text-align: left;
      font-style: normal;
    }
    .font-rename {
      margin-left: 8px;
      font-size: 12px;
      color: #999999;
      line-height: 17px;
      text-align: left;
      font-style: normal;
    }
  }
  .no-line-height{
    .label{
      line-height: unset;
    }
  }
}

.toggle-box{
  display: flex;
  align-items: center;
  margin-top: 4px;
  width: fit-content;

  .toggle-label{
    font-weight: 400;
    font-size: 12px;
    color: #3D5688;
    margin-right: 4px;
  }
  .toggle-icon{
    width: 10px;
    height: 10px;
  }
}

.tag-box{
  display: flex;
  .label{
    padding-top:3px;
    color: #666666;
    margin-right: 6px;
  }
  .toggle-box{
    height: fit-content;
    margin-left: 28px;
  }
  .tag-item{
    flex:1;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .tag{
      padding: 2px 8px 3px 8px;
      font-size: 12px;
      color: #58595B;;
      background: #F4F4F4;
      border-radius: 27px;
      border: 1px solid #D8D8D8;
      margin-bottom: 8px;
      margin-right: 8px;
    }
  }
  .tag-item + .tag-item{
    margin-left: 8px;
  }
}
.ml16_im{
  margin-left: 16px !important;
}
.g-mr-10{
  & > * + * {
    margin-left: 6px;
  }
}

.mt-7{
  margin-top: 7px;
}
.g-mb-6 + .g-mb-6{
    margin-top: 6px;
}
.g-mt6 + .g-mt6{
  margin-top: 6px;
}
.margin-right{
  margin-left:auto;
}

.mb-0-important{
  margin-bottom:0px !important;
}
</style>
