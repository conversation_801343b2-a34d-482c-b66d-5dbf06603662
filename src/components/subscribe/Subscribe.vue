<template>
  <div class="subscribe">
    <ChatBox sessionType="11"></ChatBox>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import ChatBox from "@comp/chat/ChatBox";

export default {
  name: "Subscribe",
  components: {ChatBox},
  setup(props, ctx) {
    const store = useStore();
    // 更新数据
    store.dispatch("setSubAndSer");
    return {}
  }
}
</script>
<style scoped lang="scss">

</style>