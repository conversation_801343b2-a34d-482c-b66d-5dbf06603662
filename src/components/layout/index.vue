<template>
  <div class="layout-main">
    <div class="im-content-left">
      <div class="layout-left">
        <slot name="layout-left"></slot>
      </div>
    </div>
    <div class="im-content-right">
      <div class="layout-right">
        <slot name="layout-right"></slot>
      </div>
    </div>
    <slot name="other"></slot>
  </div>
</template>
<script>
import chatSearch from "../search/chatSearch";

export default {
  components: {chatSearch},
};
</script>
<style lang="scss" scoped>
.layout-main {
  width: 100%;
  height: 100%;
  display: flex;

  .im-content-left {
    position: relative;
    width: 240px;
    border-right: 1px solid #E0E0E0;

    .layout-left {
      width: 100%;
      height: 100%;
      overflow: auto;
    }
  }

  .im-content-right {
    width: calc(100% - 240px);
    height: 100%;

    .layout-right {
      width: 100%;
      height: 100%;
      overflow: auto;
    }
  }
}
</style>