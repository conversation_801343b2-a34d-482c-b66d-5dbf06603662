<template>
  <div class="layout-chat">
    <!--聊天列表-->
    <div class="list" v-show="level==1">
      <div class="list-header">{{ sessionName }}（{{ sessionList.length }}）</div>
      <ChatList class="comp-chat-list" :sessionList="sessionList" :type="sessionType" :sessionType="sessionType" :openChatMethods="openChat"
                :showClose="false" showType="1"></ChatList>
    </div>
    <!--聊天内容-->
    <div class="content" v-if="sessionInfo.detailInfo">
      <ChatContent v-show="level!=1" class="comp-chat-content" :sessionType="sessionType" :backMethods="back"></ChatContent>
    </div>
  </div>
</template>
<script>
import {ref, watch} from "vue";
import {useStore} from "vuex";
import ChatList from "@comp/chat/ChatList";
import ChatContent from "@comp/chat/ChatContent";
import {debounce} from "@utils";

export default {
  name: "layoutChat",
  components: {ChatList, ChatContent},
  props: {
    // 会话信息
    sessionType: {
      type: String,
      default: "-1",
    },
    // 会话名
    sessionName: {
      type: String,
      default: "",
    },
  },
  setup(props, ctx) {
    const store = useStore();
    // 配置文件
    let config = store.getters.getConfig.config;
    // 层级1列表2聊天内容
    let level = ref(1);
    let sessionInfo = ref({});

    let sessionList = ref([]);
    setSessionList();
    watch(() => store.state.updateSessionsTime,
      (newValue, oldValue) => {
        setSessionList();
      }, {
        deep: true
      }
    );

    // 设置会话列表
    function setSessionList() {
      if (props.sessionType == 8 || props.sessionType == 9) {
        // 订阅号服务号获取
        // 订阅号列表
        let subList = [];
        // 服务号列表
        let serList = [];
        let allList = store.getters.getSessions({sort: 11})["11"];
        allList.map(item => {
          if (new RegExp(config.subscribe).test(item.id)) {
            subList.push(item);
          } else if (new RegExp(config.serveNumber).test(item.id)) {
            serList.push(item);
          }
        });
        if (props.sessionType == 8) {
          sessionList.value = subList;
        } else {
          sessionList.value = serList;
        }
      } else {
        sessionList.value = store.getters.getSessions({sort: props.sessionType})[props.sessionType];
      }
    }

    // 点击打开会话
    function openChat(item, type) {
      sessionInfo.value = item;
      level.value = 2;
      store.dispatch("setCurrentSession", {id: item.id, notInsert: true});
    }

    // 返回显示列表
    function back() {
      level.value = 1;
      sessionInfo.value = {};
    }

    return {
      sessionInfo,
      sessionList,
      level,

      openChat,
      back,
    }
  }
}
</script>
<style scoped lang="scss">
.layout-chat {
  width: 100%;
  height: 100%;

  .list {
    width: 100%;
    height: 100%;

    .list-header {
      height: 45px;
      line-height: 44px;
      padding-left: 16px;
      font-size: 16px;
      border-bottom: 1px solid #E0E0E0;
      background: #FFFFFF;
      color: #333333;
      font-weight: bold;
    }

    .comp-chat-list {
      height: calc(100% - 45px);
    }
  }

  .content {
    width: 100%;
    height: 100%;
  }
}
</style>