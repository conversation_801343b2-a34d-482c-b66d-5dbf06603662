<template>
  <div class="code-record">
    <div class="child-win-nav win-drag">消息记录</div>
    <div class="child-win-content" @scroll="msgScroll" ref="msgContentRef">
      <ChatMsg :sessionInfo="sessionInfo" msgType="5"></ChatMsg>
      <div class="msg-loading" v-show="msgLoading"></div>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, provide, nextTick} from "vue";
import {useStore} from "vuex";
import {queryMessageByCodeApi} from "@utils/net/api";
import {convertMsg, debounce} from "@utils"
import ChatMsg from "@comp/chat/ChatMsg";
import {alert, toast, loading} from "@comp/ui";

export default {
  name: "codeRecord",
  components: {ChatMsg},
  setup(props, ctx) {
    const store = useStore();
    let msgContentRef = ref();
    let sessionInfo = ref({});
    let codeRecordMsgObj = remote.store.state.codeRecordMsgObj;
    // 第一页消息code和list
    let page = ref(1);
    let msgCode = ref(codeRecordMsgObj.code);
    let msgList = ref(codeRecordMsgObj.list);
    let msgHasMore = ref(codeRecordMsgObj.hasMore);
    let msgLoading = ref(false);
    // 显示的消息列表
    let showMsgList = ref([]);
    provide("msgList", showMsgList);
    initMsgList();
    watch(() => store.state.codeRecordMsgObj,
      (newValue, oldValue) => {
        loading();
        page.value = 1;
        showMsgList.value = [];
        msgCode.value = newValue.code;
        msgList.value = newValue.list;
        msgHasMore.value = newValue.hasMore;
        msgLoading.value = false;
        showMsgList.value = [];
        initMsgList();
        loading().hide();
      }, {
        deep: true
      }
    );

    // 初始化消息
    async function initMsgList() {
      if (msgLoading.value || (!msgHasMore.value && page.value != 1)) {
        return;
      }
      let thisMsgList = [];
      if (page.value == 1) {
        // 第一页从弹窗获取
        thisMsgList = msgList.value;
      } else {
        // 第二页开始loading加载
        msgLoading.value = true;
        let reqParam = {
          msgCode: msgCode.value,
          page: page.value,
        }
        let msgRes = await queryMessageByCodeApi({
          msgBody: JSON.stringify(reqParam)
        });
        if (page.value != reqParam.page || msgCode.value != reqParam.msgCode) {
          return;
        }
        msgLoading.value = false;
        if (!msgRes.success) {
          toast({title: msgRes.errorMsg || "系统错误"});
          page.value--;
          return;
        }
        if (!msgRes.data?.data?.list) {
          toast({title: msgRes.errorMsg || "数据异常"});
          page.value--;
          return;
        }
        thisMsgList = msgRes.data.data.list;
        // 判断是否还有更多数据
        if (page.value >= msgRes.data.data.pages) {
          msgHasMore.value = false;
        } else {
          msgHasMore.value = true;
        }
      }
      thisMsgList = convertMsg(thisMsgList);
      let p = [];
      thisMsgList.map(item => {
        p.push(store.dispatch("setMsgField", {item: item}));
      });
      Promise.all(p).then(res => {
        remote.store.dispatch("getPersons", {doneFlag: true, account: thisMsgList.map(item => {return item.from})}).then(personInfo => {
          showMsgList.value = showMsgList.value.concat(thisMsgList);
          // 加载第一页获取消息的会话信息
          if (page.value == 1 && showMsgList.value.length > 0) {
            let thisMsg = showMsgList.value[0];
            let sessionParam = {scene: thisMsg.scene, to: thisMsg.to, id: thisMsg.scene + "-" + thisMsg.to};
            if (thisMsg.scene == "p2p") {
              sessionParam.detailInfo = {workerName: remote.store.getters.getPersons(thisMsg.to).name};
            }
            sessionInfo.value = sessionParam;
          }
        });
      });
    }

    // 消息滚动
    function msgScroll(e) {
      debounce({
        timerName: "msgScroll",
        time: 300,
        fnName: function () {
          if (msgHasMore.value && !msgLoading.value && msgContentRef.value.clientHeight + msgContentRef.value.scrollTop >= msgContentRef.value.scrollHeight) {
            page.value++;
            initMsgList();
            // 加入loading滚动到底部
            nextTick(() => {
              msgContentRef.value.scrollTop += msgContentRef.value.querySelector(".msg-loading").clientHeight;
            });
          }
        }
      });
    }

    return {
      msgContentRef,
      msgList,
      msgHasMore,
      msgLoading,
      showMsgList,
      sessionInfo,

      msgScroll,
    }
  }
}
</script>
<style scoped lang="scss">
.code-record {
  width: 100%;
  height: 100%;
  background: #F3F3F3 !important;
}

.msg-loading {
  width: 100%;
  height: 52px;
  position: relative;

  &:after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: url("/img/icon_loading.png") no-repeat center;
    animation: msgLoading 800ms linear infinite;
    background-size: 100%;
  }

  @keyframes msgLoading {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }

    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }
}
</style>