<template>
  <div class="net-connect" @click="toggleComputerTypeFlag(false)">
    <h5 class="title win-drag">申请连接</h5>
    <div class="net-content selAll" v-show="netInfo.status!=-1">
      <!--认证状态-->
      <div class="net-result" v-show="netInfo.status==1">
        <div class="main-title">{{ netInfo.errorMsg }}</div>
        <div class="btn-box">
          <button @click="winClose(true)">确认</button>
          <button @click="netInfo.status=0" v-if="netInfo.errorCode!=50002">重新申请</button>
        </div>
      </div>
      <!--填写认证信息-->
      <div class="net-apply" v-show="netInfo.status==0">
        <div class="apply-content">
          <span class="input-box">
            <label for="workerName">申请人姓名:</label>
            <span class="input-search-box">
              <input type="text" id="workerName" v-model.trim="personInfo[1].val" placeholder="申请人需要有下属" autocomplete="off"
                     @input="doSearch(1)" @blur="blurSearch(1)" @keyup.enter="selPerson(1,personInfo[1].list[personIndex])"
                     @keydown.up.prevent="selIndex(1,'pre')" @keydown.down.prevent="selIndex(1,'next')" @keydown.esc.prevent="blurSearch(1)">
              <ul class="show-ul" v-show="personInfo[1].val&&personInfo[1].list.length>0" ref="search1Ref">
                <li class="textEls" :class="{'curr': personIndex == key}" v-for="(item, key) in personInfo[1].list" :key="item.empNumber"
                    :title="item.empName + (item.deptName ? `(${item.deptName})` : '')"
                    @mousedown="selPerson(1,item)" @mouseenter="selIndex(1,key)">{{ item.empName + (item.deptName ? `(${item.deptName})` : "") }}</li>
              </ul>
            </span>
          </span>
          <span class="input-box">
            <label for="workerPwd">申请人乐聊登录密码:</label>
            <span class="input-search-box">
              <input type="password" v-model="applyInfo.workerPwd" id="workerPwd" maxlength="50">
            </span>
          </span>
        </div>

        <div class="apply-content">
          <span class="input-box w100">
            <label>电脑注册名称:</label>
            <span class="input-search-box computer-name">{{ applyInfo.pcName }}</span>
          </span>
        </div>

        <div class="apply-content">
          <span class="input-box">
            <label for="computerDept">电脑归属部门:</label>
            <span class="input-search-box">
               <input type="text" id="computerDept" v-model="personInfo['dept'].val" @input="doSearch('dept')" @blur="blurSearch('dept')" autocomplete="off"
                      @keyup.enter="selPerson('dept',personInfo['dept'].list[personIndex])"
                      @keydown.up.prevent="selIndex('dept','pre')" @keydown.down.prevent="selIndex('dept','next')" @keydown.esc.prevent="blurSearch('dept')">
              <ul class="show-ul" v-show="personInfo['dept'].val&&personInfo['dept'].list.length>0" ref="searchDeptRef">
                <li class="textEls" :class="{'curr': personIndex == key}" v-for="(item, key) in personInfo['dept'].list" :key="key+'-'+item.name"
                    :title="item.name" @mousedown="selPerson('dept',item)" @mouseenter="selIndex('dept',key)">{{ item.name }}</li>
              </ul>
            </span>
          </span>
          <span class="input-box">
            <label>电脑类别:</label>
            <div class="show-text">
              <div :class="{'default':applyInfo.pcTypeItem.value==0,'sel':computerTypeFlag}" @click.stop="toggleComputerTypeFlag()">
                {{ applyInfo.pcTypeItem.text }}
              </div>
               <ul class="show-ul" v-show="computerTypeFlag">
                 <li class="textEls" v-for="(item, key) in computerTypeList" :key="item.value" @click="selComputerType(item)">{{ item.text }}</li>
               </ul>
            </div>
          </span>
        </div>

        <div v-show="applyInfo.pcTypeItem.value==1">
          <div class="tips">提醒:若该分行为首次申请网络连接，授权通过后机构状态将变为营业中，请确认归属部门是否正确</div>
          <div class="apply-content">
          <span class="input-box">
            <label for="netAccount">宽带账号:</label>
            <span class="input-search-box">
              <input type="text" id="netAccount" maxlength="50" v-model="applyInfo.adlsAccount" autocomplete="off">
            </span>
          </span>
            <span class="input-box">
            <label for="netPwd">宽带密码:</label>
            <span class="input-search-box">
              <input type="text" id="netPwd" maxlength="50" v-model="applyInfo.adlsPwd">
            </span>
          </span>
          </div>
        </div>

        <div class="apply-content" v-show="applyInfo.pcTypeItem.value==3||applyInfo.pcTypeItem.value==4">
          <span class="input-box w100">
            <label for="workerSelf">电脑使用者:</label>
            <span class="input-search-box">
              <input type="text" id="workerSelf" v-model.trim="personInfo[2].val" @input="doSearch(2)" @blur="blurSearch(2)" autocomplete="off"
                     @keyup.enter="selPerson(2,personInfo[2].list[personIndex])"
                     @keydown.up.prevent="selIndex(2,'pre')" @keydown.down.prevent="selIndex(2,'next')" @keydown.esc.prevent="blurSearch(2)">
              <ul class="show-ul" v-show="personInfo[2].val&&personInfo[2].list.length>0" ref="search2Ref">
                <li class="textEls" :class="{'curr': personIndex == key}" v-for="(item, key) in personInfo[2].list" :key="item.empNumber"
                    :title="item.empName + (item.deptName ? `(${item.deptName})` : '')"
                    @mousedown="selPerson(2,item)" @mouseenter="selIndex(2,key)">{{ item.empName + (item.deptName ? `(${item.deptName})` : "") }}</li>
              </ul>
            </span>
            <span class="intr">(私人电脑的主人或总部电脑的固定使用者)</span>
          </span>
        </div>

        <div class="apply-content">
          <span class="input-box w100">
            <label for="witness1">证明人1:</label>
            <span class="input-search-box">
              <input type="text" id="witness1" v-model.trim="personInfo[3].val" @input="doSearch(3)" @blur="blurSearch(3)" autocomplete="off"
                     @keyup.enter="selPerson(3,personInfo[3].list[personIndex])"
                     @keydown.up.prevent="selIndex(3,'pre')" @keydown.down.prevent="selIndex(3,'next')" @keydown.esc.prevent="blurSearch(3)">
              <ul class="show-ul" v-show="personInfo[3].val&&personInfo[3].list.length>0" ref="search3Ref">
                <li class="textEls" :class="{'curr': personIndex == key}" v-for="(item, key) in personInfo[3].list" :key="item.empNumber"
                    :title="item.empName + (item.deptName ? `(${item.deptName})` : '')"
                    @mousedown="selPerson(3,item)" @mouseenter="selIndex(3,key)">{{ item.empName + (item.deptName ? `(${item.deptName})` : "") }}</li>
              </ul>
            </span>
            <span class="intr">(分行请选区域主任，其他可选直接上级)</span>
          </span>
        </div>

        <div class="apply-content">
          <span class="input-box w100">
            <label for="witness2">证明人2:</label>
            <span class="input-search-box">
              <input type="text" id="witness2" v-model.trim="personInfo[4].val" @input="doSearch(4)" @blur="blurSearch(4)" autocomplete="off"
                     @keyup.enter="selPerson(4,personInfo[4].list[personIndex])"
                     @keydown.up.prevent="selIndex(4,'pre')" @keydown.down.prevent="selIndex(4,'next')" @keydown.esc.prevent="blurSearch(4)">
              <ul class="show-ul" v-show="personInfo[4].val&&personInfo[4].list.length>0" ref="search4Ref">
                <li class="textEls" :class="{'curr': personIndex == key}" v-for="(item, key) in personInfo[4].list" :key="item.empNumber"
                    :title="item.empName + (item.deptName ? `(${item.deptName})` : '')"
                    @mousedown="selPerson(4,item)" @mouseenter="selIndex(4,key)">{{ item.empName + (item.deptName ? `(${item.deptName})` : "") }}</li>
              </ul>
            </span>
            <span class="intr">(您的直接上级或者其他管理人员)</span>
          </span>
        </div>

        <div class="apply-content">
          <span class="input-box w100">
            <label>申请人备注:</label>
            <textarea id="workerRemark" placeholder="请录入原因,200字以内" v-model="applyInfo.remark"></textarea>
          </span>
        </div>

        <div class="apply-content">
          <span class="input-box w100">
            <label></label>
            <div class="intr-box">
              <div class="intr-title">说明：</div>
              <ul class="intr-ul">
                <li>1、非法申请和虚假证明的,一经证实将被降级或者辞退!</li>
                <li>2、同一分行的小电脑只需申请一次</li>
                <li>3、提交申请后,会由证明人1和证明人2审批</li>
                <li>4、证明人全部审批同意后,管理员会在2天内核实属实后发放连接授权</li>
              </ul>
            </div>
          </span>
        </div>

        <div class="btn-box">
          <button class="btn btn-apply" @click="doApply()">申请提交</button>
          <button class="btn btn-close" @click="winClose()">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {nextTick, ref, watch} from "vue";
import {useStore} from "vuex";
import {checkPcApi, getPcTypeListApi, getPcPersonApi, getPcNameApi, getDeptApi, applyNetApi} from '@utils/net/api.js';
import {alert, loading, toast} from '@comp/ui';
import {getNetConnectInfo, debounce, MD5} from '@utils';

export default {
  name: "netConnect",
  setup(props, ctx) {
    const store = useStore();
    let userInfo = remote.store.getters.getUserInfo;
    let baseComputerInfo = store.getters.getBaseComputerInfo;
    let netComputerInfo = ref({});
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();
    store.commit("setEmit", {type: "closeWindow", value: -1});
    // 监听窗口关闭事件
    watch(() => store.state.emit.winCloseClick,
      (newValue, oldValue) => {
        winClose();
      }, {
        deep: true
      }
    );
    // 网络连接状态
    let netInfo = ref({
      status: -1
    });
    // 选择人员信息
    let personInfo = ref({
      dept: {val: "", list: []},
      1: {val: "", list: []},
      2: {val: "", list: []},
      3: {val: "", list: []},
      4: {val: "", list: []},
    })
    // 搜索元素
    let search1Ref = ref();
    let search2Ref = ref();
    let search3Ref = ref();
    let search4Ref = ref();
    let searchDeptRef = ref();
    let personIndex = ref(0);
    // 申请提交信息
    let applyInfo = ref({
      workerPwd: "",
      pcName: "",
      remark: "",
      adlsAccount: "",
      adlsPwd: "",
      pcTypeItem: {abbreviation: "", text: "请选择", value: 0},
      deptItem: {deptName: "", deptNumber: ""},
    });
    // 电脑类别列表
    let computerTypeList = ref([applyInfo.value.pcTypeItem]);
    // 显示选择电脑类别
    let computerTypeFlag = ref(false);

    // 获取网络连接数据
    getNetConnectInfo().then(res => {
      // 初始化
      netComputerInfo.value = res;
      if (!netComputerInfo.value.SerialNumber || !netComputerInfo.value.Model) {
        netComputerInfo.value.SerialNumber = netComputerInfo.value.Model = `lyjxnj-${userInfo.workerId}-${Date.now()}`;
      } else if (netComputerInfo.value.Mac.length == 0) {
        toast({title: "获取不到MAC地址", type: 2});
        return;
      }
      init();
    });

    // 初始化数据
    async function init() {
      loading();
      let secret = await store.dispatch("getSecretEnCrypt", {param: {}});
      // 获取网络连接情况
      let netRes = await checkPcApi({
        "msgBody": JSON.stringify({
          "hdSerialNumber": netComputerInfo.value.SerialNumber,
          "hdModelNumber": netComputerInfo.value.Model,
          "computerId": netComputerInfo.value.ComputerId,
          "macAddress": netComputerInfo.value.Mac[0],
          "netIds": netComputerInfo.value.Mac[1] || netComputerInfo.value.Mac[0],
          "computerInfo": secret
        })
      });
      loading().hide();
      if (netRes && (netRes.errorCode == 50001 || netRes.errorCode == 50002 || netRes.success)) {
        if (netRes.data && !netRes.data.data) {
          netRes.data.data = {
            status: ""
          };
        }
        netRes.data.data.errorCode = netRes.errorCode;
        netRes.data.data.errorMsg = netRes.errorMsg;
        netInfo.value = netRes.data.data;
      } else {
        alert({
          content: (netRes.errorMsg || "服务异常!") + (netRes.errorCode || ""),
          done: () => {
            winClose(true);
          }
        });
      }
      // 获取电脑类别列表
      let computerTypeListRes = await getPcTypeListApi({
        "msgBody": JSON.stringify({})
      });
      computerTypeList.value = [applyInfo.value.pcTypeItem].concat(computerTypeListRes.data.data);
    }

    // 选择电脑类别
    function selComputerType(item) {
      applyInfo.value.pcTypeItem = item;
      getPcName();
      toggleComputerTypeFlag(false);
    }

    // 切换显示选择电脑类别
    function toggleComputerTypeFlag(flag) {
      if (flag != null) {
        computerTypeFlag.value = flag;
      } else {
        computerTypeFlag.value = !computerTypeFlag.value;
      }
    }

    // 输入关键词搜索
    function doSearch(type) {
      // 重置选择内容
      personInfo.value[type].list = [];
      personInfo.value[type].empName = "";
      personInfo.value[type].empNumber = "";
      personInfo.value[type].deptName = "";
      personInfo.value[type].deptNumber = "";
      personIndex.value = 0;
      if (type == 1) {
        setDeptInfo({});
      }
      // 防止热加载执行多次
      debounce({
        timerName: "doSearch",
        time: 300,
        fnName: function () {
          if (!personInfo.value[type].val) {
            return;
          }
          if (type == "dept") {
            getDept();
          } else {
            getPcPerson(type);
          }
        }
      });
    }

    // 失去焦点
    function blurSearch(type) {
      if (!personInfo.value[type].deptNumber) {
        personInfo.value[type].val = "";
      }
      personInfo.value[type].list = [];
      // 选择申请人、电脑归属部门、使用人获取电脑名
      if (type == 1 || type == 2 || type == "dept") {
        getPcName();
      }
    }

    // 选择人员
    function selPerson(type, item) {
      if (type == "dept" && item.number) {
        setDeptInfo(item);
        getPcName();
      } else if (type != "dept" && item.empNumber) {
        personInfo.value[type].empName = item.empName;
        personInfo.value[type].empNumber = item.empNumber;
        personInfo.value[type].deptName = item.deptName;
        personInfo.value[type].deptNumber = item.deptNumber;
        personInfo.value[type].val = item.empName + (item.deptName ? `(${item.deptName})` : "");
        personInfo.value[type].list = [];
        // 填充电脑归属部门
        if (type == 1) {
          setDeptInfo(item);
        }
        // 选择申请人、电脑归属部门、使用人获取电脑名
        if (type == 1 || type == 2) {
          getPcName();
        }
      }
    }

    // 设置电脑归属部门
    function setDeptInfo(item) {
      personInfo.value["dept"].deptName = item.name || item.deptName;
      personInfo.value["dept"].deptNumber = item.number || item.deptNumber;
      personInfo.value["dept"].val = item.name || item.deptName;
      applyInfo.value.deptItem.deptName = item.name || item.deptName;
      applyInfo.value.deptItem.deptNumber = item.number || item.deptNumber;
      personInfo.value["dept"].list = [];
    }

    // 选择人员
    function selIndex(type, key) {
      switch (key) {
        case "pre":
          if (personIndex.value - 1 >= 0) {
            personIndex.value--;
          }
          break;
        case "next":
          if (personIndex.value + 1 < personInfo.value[type].list.length) {
            personIndex.value++;
          }
          break;
        default:
          personIndex.value = key;
          break;
      }
      nextTick(() => {
        scrollLi(type);
      });
    }

    // 滚动到可视区域搜索列表
    function scrollLi(type) {
      nextTick(() => {
        let searchRef = type == 1 ? search1Ref : type == 2 ? search2Ref : type == 3 ? search3Ref : type == 4 ? search4Ref : type == "dept" ? searchDeptRef : "";
        if (!searchRef) {
          return;
        }
        let searchBox = searchRef.value;
        let currLiElm = Array.prototype.find.call(searchBox.querySelectorAll("li"), item => {return /curr/.test(item.className)});
        // 判断可视区域
        if (currLiElm) {
          if (currLiElm.offsetTop + currLiElm.offsetHeight >= searchBox.scrollTop + searchBox.clientHeight) {
            searchRef.value.scrollTop = currLiElm.offsetTop + currLiElm.offsetHeight - searchBox.clientHeight;
          } else if (currLiElm.offsetTop <= searchBox.scrollTop) {
            searchRef.value.scrollTop = currLiElm.offsetTop;
          }
        }
      });
    }

    // 申请提交
    async function doApply() {
      if (!personInfo.value[1].empNumber) {
        toast({title: "请选择申请人", type: 2});
        return;
      } else if (!applyInfo.value.workerPwd) {
        toast({title: "请填写申请人登录密码", type: 2});
        return;
      } else if (!personInfo.value["dept"].deptNumber) {
        toast({title: "请选择电脑归属部门", type: 2});
        return;
      } else if (!applyInfo.value.pcTypeItem.value) {
        toast({title: "请选择电脑类别", type: 2});
        return;
      } else if ((applyInfo.value.pcTypeItem.value == 3 || applyInfo.value.pcTypeItem.value == 4) && !personInfo.value[2].empNumber) {
        toast({title: "请选择电脑使用者", type: 2});
        return;
      } else if (!personInfo.value[3].empNumber) {
        toast({title: "请选择证明人1", type: 2});
        return;
      } else if (!personInfo.value[4].empNumber) {
        toast({title: "请选择证明人2", type: 2});
        return;
      }
      let secret = await store.dispatch("getSecretEnCrypt", {param: {}});
      let param = {
        "applyNameInput": personInfo.value[1].empName + (personInfo.value[1].deptName ? `(${personInfo.value[1].deptName})` : ''),
        "applyEmpName": personInfo.value[1].empName,
        "applyEmpNumber": personInfo.value[1].empNumber,
        "applyDeptName": personInfo.value[1].deptName,
        "applyDeptNumber": personInfo.value[1].deptNumber,
        "applyPassword": MD5(applyInfo.value.workerPwd.toLowerCase()),
        "pcName": applyInfo.value.pcName,
        "attachNameInput": personInfo.value["dept"].deptName,
        "attachNumber": personInfo.value["dept"].deptNumber,
        "attachName": personInfo.value["dept"].deptName,
        "attachCategory": "",
        "type": applyInfo.value.pcTypeItem.value,
        "empNameInput1": personInfo.value[3].empName + (personInfo.value[3].deptName ? `(${personInfo.value[3].deptName})` : ''),
        "empName1": personInfo.value[3].empName,
        "empNumber1": personInfo.value[3].empNumber,
        "empDept1": personInfo.value[3].deptName,
        "empDeptNumber1": personInfo.value[3].deptNumber,
        "empNameInput2": personInfo.value[4].empName + (personInfo.value[4].deptName ? `(${personInfo.value[4].deptName})` : ''),
        "empName2": personInfo.value[4].empName,
        "empNumber2": personInfo.value[4].empNumber,
        "empDept2": personInfo.value[4].deptName,
        "empDeptNumber2": personInfo.value[4].deptNumber,
        "remark": applyInfo.value.remark,
        "hdSerialNumber": netComputerInfo.value.SerialNumber,
        "hdModelNumber": netComputerInfo.value.Model,
        "computerId": netComputerInfo.value.ComputerId,
        "macAddress": netComputerInfo.value.Mac[0],
        "netIds": netComputerInfo.value.Mac[1] || netComputerInfo.value.Mac[0],
        "computerInfo": secret
      }
      if (param.type == 1) {
        param.adlsAccount = applyInfo.value.adlsAccount;
        param.adlsPwd = applyInfo.value.adlsPwd;
      } else if (param.type == 3 || param.type == 4) {
        param.pcUserInput = personInfo.value[2].empName + (personInfo.value[2].deptName ? `(${personInfo.value[2].deptName})` : '');
        param.pcUser = personInfo.value[2].empName;
        param.pcUserNumber = personInfo.value[2].empNumber;
        param.pcUserDept = personInfo.value[2].deptName;
        param.pcUserDeptNumber = personInfo.value[2].deptNumber;
      }

      loading();
      let res = await applyNetApi({"msgBody": JSON.stringify(param)});
      loading().hide();
      if (res.success || (res.data && res.data.data)) {
        alert({
          content: res.errorMsg || "提交成功，请等待审核!",
          showCancel: false,
          done: () => {
            winClose(true);
          }
        });
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 网络连接申请人搜索api,type-1申请人-2使用者-3证明人1-4证明人2
    async function getPcPerson(type) {
      let res = await getPcPersonApi({
        "msgBody": JSON.stringify({
          "qryType": type,
          "keyword": personInfo.value[type].val
        })
      });
      if (res.data && res.data.data) {
        if (res.data.data.length == 0) {
          personInfo.value[type].list = [{empName: "无数据"}];
        } else {
          personInfo.value[type].list = res.data.data;
        }
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 网络连接电脑名api
    async function getPcName() {
      if (applyInfo.value.pcTypeItem.value) {
        let res = await getPcNameApi({
          "msgBody": JSON.stringify({
            "type": applyInfo.value.pcTypeItem.value,
            "attachNumber": personInfo.value["dept"].deptNumber || personInfo.value[1].deptNumber || "",
            "pcUserNumber": personInfo.value[2].empNumber || "",
          })
        });
        if (res.data && res.data.data) {
          applyInfo.value.pcName = res.data.data;
        } else {
          applyInfo.value.pcName = "";
          toast({title: res.errorMsg || "系统错误", type: 2});
        }
      }
    }

    // 网络连接电脑归属部门
    async function getDept() {
      if (personInfo.value[1].empNumber) {
        let res = await getDeptApi({
          "msgBody": JSON.stringify({
            "keyword": personInfo.value["dept"].val.trim(),
            "workId": personInfo.value[1].empNumber,
          })
        });
        if (res.data && res.data.data) {
          if (res.data.data.length == 0) {
            personInfo.value["dept"].list = [{name: "无数据"}];
          } else {
            personInfo.value["dept"].list = res.data.data;
          }
        } else {
          toast({title: res.errorMsg || "系统错误", type: 2});
        }
      } else {
        personInfo.value["dept"].list = [{name: "请选择申请人，在选择归属部门"}];
      }
    }

    // 关闭窗口
    function winClose(flag) {
      if (flag) {
        store.commit("setEmit", {type: "closeWindow", value: ""});
        store.commit("setWindowClose", currentWindow.cWindow.id);
      } else {
        alert({
          content: "确定取消申请网络连接吗?",
          done: (type) => {
            if (type == 1) {
              store.commit("setEmit", {type: "closeWindow", value: ""});
              store.commit("setWindowClose", currentWindow.cWindow.id);
            } else {
              store.commit("setEmit", {type: "closeWindow", value: -1});
            }
          }
        })
      }
    }

    return {
      computerTypeList,
      computerTypeFlag,
      netInfo,
      personInfo,
      search1Ref,
      search2Ref,
      search3Ref,
      search4Ref,
      searchDeptRef,
      personIndex,
      applyInfo,

      selComputerType,
      toggleComputerTypeFlag,
      winClose,
      doSearch,
      blurSearch,
      selPerson,
      selIndex,
      doApply,
    }
  }
}
</script>
<style scoped lang="scss">
.net-connect {
  width: 100%;
  height: 100%;

  .title {
    font-size: 14px;
    color: #999999;
    padding: 8px 10px;
    font-weight: normal;
    background: #f6f6f6;
  }

  .net-content {
    min-width: 700px;
    max-width: 700px;
    margin: 0 auto;
    padding: 25px;

    .net-result {
      .main-title {
        font-size: 20px;
        line-height: 28px;
        text-align: center;
      }

      .btn-box {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;

        button {
          width: 100px;
          line-height: 20px;
          padding: 3px 12px;
          border-radius: 2px;
          background-color: #3188e8;
          border-color: #3188e8;
          color: #FFFFFF;
          cursor: pointer;

          &:hover {
            background-color: #1B75C3;
            border-color: #1B75C3;
          }

          &:first-child {
            margin-right: 5px;
          }
        }
      }
    }

    .net-apply {
      .tips {
        color: red;
        line-height: 28px;
        margin-bottom: 5px;
      }

      .apply-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;

        .input-box {
          width: 50%;
          display: flex;
          line-height: 28px;

          &.w100 {
            width: 100%;
          }

          label {
            width: 120px;
            text-align: right;
          }

          .show-ul {
            position: absolute;
            top: 100%;
            left: -1px;
            width: 150px;
            max-height: 215px;
            line-height: 26px;
            overflow-y: auto;
            border: 1px solid #ccc;
            border-radius: 2px;
            margin-top: 1px;
            background: #FFFFFF;
            z-index: 10;

            li {
              padding: 0px 5px;

              &.curr,
              &:hover {
                background-color: #474752;
                color: #FFFFFF;
              }
            }
          }

          .input-search-box {
            width: 150px;
            height: 28px;
            position: relative;
            margin: 0 5px;

            &.computer-name {
              width: 40%;
              border: none;
              background: #f6f6f6;
              padding: 0 5px;
            }
          }

          input {
            width: 100%;
            height: 100%;
            padding: 2px 5px;
            border: 1px solid #CCCCCC;
            border-radius: 2px;
          }

          .show-text {
            width: 150px;
            height: 28px;
            line-height: 22px;
            padding: 2px 5px;
            border: 1px solid #CCCCCC;
            position: relative;
            margin: 0 5px;

            .default {
              color: #7e7e7e;

              &.sel {
                &:after {
                  border-width: 0 4px 4px 4px;
                  border-color: transparent transparent #000000 transparent;
                }
              }

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 10px;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 4px 0;
                border-style: solid;
                border-color: #000000 transparent transparent transparent;
              }
            }
          }
        }

        .intr {
          color: #7e7e7e;
        }

        textarea {
          padding: 5px;
          min-height: 75px;
          border: 1px solid #ccc;
          border-radius: 2px;
          width: 60%;
          resize: none;
          margin: 0 5px;
        }

        .intr-box {
          margin-top: 10px;
          line-height: 20px;

          .intr-title {
            font-weight: 600;
          }
        }
      }

      .btn-box {
        margin-top: 30px;
        display: flex;
        justify-content: center;

        .btn {
          line-height: 20px;
          padding: 5px 25px;
          color: #FFFFFF;
          background-color: #3188e8;
          border-color: #3188e8;
          border-radius: 2px;
          cursor: pointer;

          &.btn-apply {
            margin-right: 5px;

            &:hover {
              background-color: #1B75C3;
              border-color: #1B75C3;
            }
          }

          &.btn-close {
            background-color: #DDDDDD;
            color: #000000;

            &:hover {
              color: #333333;
            }
          }
        }
      }
    }
  }
}
</style>