<template>
  <div class="search-msg">
    <div class="search-nav win-drag">查看聊天记录</div>
    <div class="search-header">
      <div class="search-input-box">
        <input type="text" placeholder="搜索" v-model="searchMsgObj.text" @keydown.enter="doSearch()">
        <i class="search-close" @click="searchMsgObj.text=''"></i>
        <i class="search-btn" @click="doSearch()"></i>
      </div>
    </div>
    <div class="search-content">
      <div class="search-left">
        <div class="left-tab" :class="{'show':listData.showType==1}">
          <div class="left-tab-title" @click="changeShowType(1)">私聊</div>
          <ul class="left-tab-ul" v-if="listData.p2pFlag" v-show="listData.showType==1">
            <li :class="{'curr': listData.selIndex==key}" v-for="(item, key) in listData['p2p']" @click="selItem(1,key)">
              <div class="user-avatar-box">
                <div class="avatar-box">
                  <img class="avatar" :src="getPerson(item.id).avatar" :onerror="avatarError.bind(this, 'p2p', item.id, '')">
                </div>
              </div>
              <div class="content-box">
                <div class="name-box">
                  <span class="name" :class="getPerson(item.id).empStatus==4?'resign':''">{{ getPerson(item.id).name }}</span>
                </div>
                <div class="content">{{ item.row }}条相关记录</div>
              </div>
            </li>
          </ul>
        </div>
        <div class="left-tab" :class="{'show':listData.showType==2}">
          <div class="left-tab-title" @click="changeShowType(2)">群聊</div>
          <ul class="left-tab-ul" v-if="listData.teamFlag" v-show="listData.showType==2">
            <li :class="{'curr': listData.selIndex==key}" v-for="(item, key) in listData['team']" @click="selItem(2,key)">
              <div class="user-avatar-box">
                <div class="avatar-box">
                  <img class="avatar" :src="teamMap[item.id].avatar" :onerror="avatarError.bind(this, 'team', item.id, '')">
                </div>
              </div>
              <div class="content-box">
                <div class="name-box">
                  <span class="name">{{ teamMap[item.id].name }}</span>
                </div>
                <div class="content">{{ item.row }}条相关记录</div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <div class="search-right" v-if="sessionInfo&&sessionInfo.id" v-show="searchMsgObj.list.length!=0">
        <div class="search-msg-tip">
          <span class="search-msg-text">{{ searchMsgObj.show > 0 ? "已搜索到" + searchMsgObj.total + "条消息记录" : "" }}</span>
          <span class="search-msg-back" @click="toItem()">{{ searchMsgObj.show ? "进入聊天" : "返回" }}</span>
        </div>
        <!--消息记录-->
        <div class="record-msg-box" ref="recordMsgRef" v-show="!searchMsgObj.show" @scroll="msgScroll($event, 1)">
          <ChatMsg :searchList="recordList" :sessionInfo="sessionInfo" msgType="3"></ChatMsg>
        </div>
        <!--搜索的消息记录-->
        <div class="search-msg-box" ref="searchMsgRef" v-show="searchMsgObj.show" @scroll="msgScroll($event, 2)">
          <ChatMsg :searchList="searchMsgObj.list" :sessionInfo="sessionInfo" msgType="4" :scrollElm="searchMsgRef"
                   :strKey="searchMsgObj.text" :showRecord="showRecord" :notScroll="searchMsgObj.page!=1?true:false"></ChatMsg>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {nextTick, ref} from "vue";
import {useStore} from "vuex";
import {useRouter} from 'vue-router'
import {queryCloudAllMessageApi} from '@utils/net/api'
import {alert, loading, toast} from '@comp/ui';
import {avatarError, debounce} from "@utils";
import ChatMsg from "@comp/chat/ChatMsg";

export default {
  name: "SearchMsg",
  components: {ChatMsg},
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    store.dispatch("setDiffTime");
    // 群列表
    let teamMap = ref(remote.store.getters.getTeams());
    let userInfo = store.getters.getUserInfo;
    // 人员列表
    let personMap = ref({});
    let listData = ref({
      p2p: [],
      team: [],
      showType: -1,
      selIndex: -1,
      p2pFlag: false,
      teamFlag: false,
    });
    // 选中的会话
    let sessionInfo = ref({});
    // 消息记录列表
    let recordList = ref([]);
    // 搜索聊天记录状态
    let searchMsgObj = ref({
      text: router.currentRoute.value.query.val,// 搜索内容
      page: 1, // 搜索当前页
      total: 0,// 搜索到的总条数
      search: false,// 搜索请求状态
      show: false,// 是否显示搜索结果
      list: [],// 搜索结果列表
    });
    // 消息元素
    let recordMsgRef = ref();
    let searchMsgRef = ref();

    doSearch();

    // 获取人员信息
    async function getPeronInfo(personList) {
      let list = await store.dispatch("getPersons", {doneFlag: true, account: personList});
      return list;
    }

    // 获取用户信息
    function getPerson(account) {
      return store.getters.getPersons(account);
    }

    // 切换显示列表类型
    function changeShowType(type) {
      if (listData.value.showType == type) {
        listData.value.showType = -1
      } else {
        listData.value.showType = type;
      }
    }

    // 选择查看对应消息记录
    function selItem(type, key) {
      listData.value.showType = type;
      listData.value.selIndex = key;
      let to = "";
      let scene = "";
      if (type == 1) {
        to = listData.value["p2p"][key].id;
        scene = "p2p";
        sessionInfo.value = {scene: scene, to: to, id: scene + "-" + to, detailInfo: {workerName: getPerson(to).name}};
      } else {
        to = listData.value["team"][key].id;
        scene = teamMap.value[to].detailType == "superTeam" ? "superTeam" : "team";
        sessionInfo.value = {scene: scene, to: to, id: scene + "-" + to};
      }
      searchMsgObj.value.page = 1;
      searchMsgObj.value.list = [];
      queryMsg();
    }

    // 返回搜索消息列表/进入聊天
    function toItem() {
      if (searchMsgObj.value.show) {
        store.dispatch("setCurrentSession", {id: sessionInfo.value.id});
      } else {
        toggleSearchMsg(true);
      }
    }

    // 搜素
    async function doSearch() {
      loading();
      listData.value.p2pFlag = false;
      listData.value.teamFlag = false;
      // 重置聊天记录页数和内容
      searchMsgObj.value.page = 1;
      searchMsgObj.value.list = [];
      let res = await queryCloudAllMessageApi({
        msgBody: JSON.stringify({
          empNo: userInfo.workerNo,
          keyWord: searchMsgObj.value.text,
          groupNumbers: Object.keys(teamMap.value).join(",")
        })
      })
      loading().hide();
      if (res.data && res.data.data) {
        listData.value = Object.assign(listData.value, res.data.data);
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }

      // 获取私聊列表人员数据
      getPeronInfo(listData.value["p2p"].map(item => item.id)).then(res => {
        personMap.value = res;
        listData.value.p2pFlag = true;
      });

      // 获取群聊列表数据
      listData.value["team"] = listData.value["team"].filter(item => teamMap.value[item.id]);
      listData.value.teamFlag = true;

      // 默认选中第一个
      if (listData.value["p2p"].length > 1) {
        selItem(1, 0)
      } else if (listData.value["p2p"].length > 1) {
        selItem(2, 0)
      } else {
        listData.value.showType = -1;
      }
    }

    // 加载更多
    function msgScroll(e, type) {
      debounce({
        timerName: "msgScroll",
        time: 100,
        fnName: function () {
          if (type == 1) {
            if (e.target.clientHeight != e.target.scrollHeight) {
              if (e.target.scrollTop == 0) {
                // 向上滚动加载更多前消息记录
                getMsgHistory("pre");
              } else if (e.target.scrollHeight == e.target.scrollTop + e.target.clientHeight) {
                // 向上滚动加载更多后消息记录
                getMsgHistory("next");
              }
            }
          } else if (type == 2) {
            if (searchMsgObj.value.list.length < searchMsgObj.value.total && e.target.scrollTop == 0 && e.target.clientHeight != e.target.scrollHeight) {
              // 向上滚动加载更多搜索列表
              searchMsgObj.value.page++;
              queryMsg(true);
            }
          }
        }
      });
    }

    // 切换显示搜索消息
    function toggleSearchMsg(flag) {
      if (flag != null) {
        searchMsgObj.value.show = flag;
      } else {
        searchMsgObj.value.show = !searchMsgObj.value.show;
      }
    }

    // 查看前后消息-前后25条消息
    async function showRecord(time, idServer) {
      recordList.value = [];
      let recordInfo = await store.dispatch("showRecord", {time: time, sessionId: sessionInfo.value.id});
      let {list, p} = recordInfo;
      if (list.length > 0) {
        Promise.all(p).then(res => {
          recordList.value = list.sort((a, b) => {
            return a.time - b.time
          });
          searchMsgObj.value.show = false;
          nextTick(() => {
            list.map(item => {
              if (item.idServer == idServer) {
                setTimeout(() => {
                  scrollMsg(recordMsgRef.value, item.idServer, 3);
                }, 500)
              }
            });
          });
        })
      } else {
        toast({title: "暂无聊天记录", type: 2});
      }
    }

    // 滚动消息聚焦-type1会话消息
    function scrollMsg(msgRef, idServer, type) {
      if (idServer) {
        let thisElm = msgRef.querySelector(".msg-info-" + idServer);
        if (thisElm) {
          msgRef.scrollTop = thisElm.offsetTop - 96 - 36 - 24;
          store.commit("setFocusMsg", {idServer: idServer, type: type || 1});
        }
      }
    }

    // 获取消息记录
    function getMsgHistory(type) {
      let param = {id: sessionInfo.value.id, type: 2, limit: 50};
      switch (type) {
        case "pre":
          param.endTime = recordList.value.length > 0 ? recordList.value[0].time : undefined;
          break;
        case "next":
          param.beginTime = recordList.value.length > 0 ? recordList.value[recordList.value.length - 1].time + 1 : undefined;
          param.reverse = true;
          break;
        default:
          recordList.value = [];
          break;
      }
      loading();
      store.dispatch("setHistory", param).then(res => {
        loading().hide();
        if (!res.err && res.obj && res.obj.msgs) {
          if (res.obj.msgs.length > 0) {
            let list = res.obj.msgs.sort((a, b) => {return a.time - b.time});
            let p = [];
            list.map(item => {
              p.push(store.dispatch("setMsgField", {item: item}));
            });
            Promise.all(p).then(res => {
              switch (type) {
                case "pre":
                  let preListLength = recordList.value.length;
                  recordList.value = list.concat(recordList.value);
                  // 加载更多滚动到对应位置
                  let msgElm = recordMsgRef.value.querySelectorAll(".msg-li");
                  let pos = 0;
                  if (preListLength == msgElm.length) {
                    pos = recordMsgRef.value.scrollTop + 30;
                  } else {
                    let preLoadElm = msgElm[msgElm.length - preListLength];
                    // 计算滚动后上次滚动前第一个元素位置
                    pos = preLoadElm.offsetTop - msgElm[0].offsetTop;
                  }
                  recordMsgRef.value.scrollTop = pos;
                  break;
                case "next":
                  recordList.value = recordList.value.concat(list);
                  break;
                default:
                  recordList.value = list;
                  break;
              }
            });
          } else {
            toast({title: "暂无聊天记录", type: 2});
          }
        }
      });
    }

    //获取对应会话消息记录
    async function queryMsg(scroll) {
      if (searchMsgObj.value.search) {
        return;
      }
      searchMsgObj.value.search = true;
      let msgInfo = await store.dispatch("queryCloudMessage", {
        text: searchMsgObj.value.text,
        scene: sessionInfo.value.scene,
        to: sessionInfo.value.to,
        page: searchMsgObj.value.page
      });
      searchMsgObj.value.search = false;
      searchMsgObj.value.show = true;
      if (msgInfo) {
        let {list, p, total} = msgInfo;
        Promise.all(p).then(res => {
          let preListLength = searchMsgObj.value.list.length;
          searchMsgObj.value.list = list.concat(searchMsgObj.value.list);
          // 加载更多滚动到对应位置
          if (scroll) {
            let msgElm = searchMsgRef.value.querySelectorAll(".msg-li");
            let pos = 0;
            if (preListLength == msgElm.length) {
              pos = searchMsgRef.value.scrollTop + 30;
            } else {
              let preLoadElm = msgElm[msgElm.length - preListLength];
              // 计算滚动后上次滚动前第一个元素位置
              pos = preLoadElm.offsetTop - msgElm[0].offsetTop;
            }
            searchMsgRef.value.scrollTop = pos;
          }
        });
        searchMsgObj.value.total = total;
      }
    }

    return {
      teamMap,
      personMap,
      listData,
      searchMsgObj,
      recordMsgRef,
      searchMsgRef,
      recordList,
      sessionInfo,

      getPerson,
      avatarError,
      changeShowType,
      doSearch,
      selItem,
      toItem,
      showRecord,
      toggleSearchMsg,
      msgScroll,
    }
  }
}
</script>
<style scoped lang="scss">
.search-msg {
  width: 100%;
  height: 100%;
  background: #f5f5f5;

  .search-nav {
    padding-left: 14px;
    height: 36px;
    line-height: 36px;
    background: #F6F6F6;
    font-size: 14px;
    color: #999999;
  }

  .search-header {
    width: 100%;
    height: 96px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #D8D8D8;
    background: #FFFFFF;

    .search-input-box {
      position: relative;
      width: 274px;
      padding: 0 35px;
      background: url("/img/index/member/search.png") $styleBg1Hover 10px center no-repeat;
      background-size: 15px;
      border-radius: 4px 0 0 4px;

      input {
        width: 100%;
        height: 36px;
        border: none;
        font-size: 14px;
        color: #000000;
        background: $styleBg1Hover;
        outline: none;
      }

      .search-close {
        position: absolute;
        top: 0;
        right: 0;
        width: 36px;
        height: 36px;
        background: url("/img/search/close.png") center no-repeat;
        background-size: 15px;
        cursor: pointer;
      }

      .search-btn {
        position: absolute;
        top: 0;
        right: -36px;
        width: 36px;
        height: 36px;
        background: url("/img/search/search.png") #4A90E2 center no-repeat;
        background-size: 30px;
        cursor: pointer;
        border-radius: 0 4px 4px 0;
      }
    }
  }

  .search-content {
    width: 100%;
    height: calc(100% - 36px - 96px);
    display: flex;
    padding: 0 5px 5px 5px;

    .search-left {
      width: 229px;
      height: 100%;
      flex-shrink: 0;
      border-right: 1px solid #EEEEEE;

      .left-tab {
        width: 100%;

        &.show {
          height: calc(100% - 34px);

          .left-tab-title {
            &:before {
              transform: rotate(180deg) !important;
            }
          }
        }

        .left-tab-title {
          width: 100%;
          height: 34px;
          line-height: 34px;
          font-size: 14px;
          cursor: pointer;
          border-bottom: 1px solid #D8D8D8;

          &:before {
            display: inline-block;
            content: "";
            width: 10px;
            height: 10px;
            background: url(/img/index/hait_arrow.png) no-repeat;
            background-size: 10px 10px;
            margin: 0 5px;
          }
        }

        .left-tab-ul {
          max-height: calc(100% - 34px);
          overflow-y: auto;
          background: linear-gradient(-180deg, #EEEBE9 0%, #E6E5E5 62%, #F0F0F0 100%);

          li {
            width: 100%;
            height: 64px;
            display: flex;
            align-items: center;
            padding: 12px 9px 12px 14px;
            position: relative;
            border-bottom: 1px solid #D8D8D8;
            cursor: pointer;

            &:hover, {
              background: rgba(195, 195, 196, .3);
            }

            &.curr {
              background: linear-gradient(-180deg, #c7c6c6 0%, #c4c4c5 62%, #cac6c6 100%);
            }

            .content-box {
              flex-shrink: 0;
              width: calc(100% - 50px);
              margin-left: 10px;

              .name-box {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .name {
                  font-size: 15px;
                  flex: 1;
                  color: #000;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;

                  &.resign {
                    color: #F74B32;
                  }
                }
              }

              .content {
                height: 16px;
                line-height: 16px;
                margin-top: 5px;
                color: #999;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }
    }

    .search-right {
      width: calc(100% - 230px);
      height: 100%;
      flex-shrink: 0;

      .search-msg-tip {
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: rgb(209, 233, 252);
        padding: 0 10px;

        .search-msg-back {
          padding: 2px 2px 2px 18px;
          background: url("/img/index/member/back.png") 2px center no-repeat;
          border: 1px solid transparent;
          border-radius: 2px;
          cursor: pointer;

          &:hover {
            border-color: #BBBBBB;
            background-color: rgb(220, 240, 252);
          }
        }
      }

      .record-msg-box,
      .search-msg-box {
        width: 100%;
        height: calc(100% - 24px);
        overflow-y: auto;
      }
    }
  }
}
</style>