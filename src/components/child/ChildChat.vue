<template>
  <div class="child-chat">
    <!--聊天内容-->
    <ChatContent ref="ChatContent" class="comp-chat-content" :sessionType="sessionType"></ChatContent>
  </div>
</template>
<script>
import {ref, watch, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import ChatContent from "@comp/chat/ChatContent";
import {useRouter} from 'vue-router'
import {htmlUnEscapeAll, strToHtml} from '@utils'
import {deepClone} from '../../utils'

export default {
  name: "ChildChat",
  props: {
    // 会话类型-utils.js/index.js的getSessionType
    sessionType: {
      type: String,
      default: "-1",
    }
  },
  components: {ChatContent},
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    // 初始化乐聊通知徽标
    store.commit("setNoticeUnread");
    // 获取收藏话术权限
    store.dispatch("getCollectStoryPur");
    // 用户信息
    let userInfo = store.getters.getUserInfo;
    let sessionId = router.currentRoute.value.query.id;
    let scene = sessionId.split("-")[0];
    let to = sessionId.split("-")[1];
    // 当前会话信息
    let sessionInfo = ref({
      scene: scene,
      to: to,
      id: sessionId,
      detailInfo: {}
    });
    // 获取会话信息
    store.commit("setSessions", {child: true, sessions: deepClone(remote.store.getters.getSessions())});
    sessionInfo.value = store.getters.getSessions({id: sessionId});
    // 设置窗口名
    document.title = sessionInfo.value.detailInfo.name;
    if (scene != "p2p") {
      store.commit("setTeams", {teamList: [remote.store.getters.getTeams({id: to})]});
    }

    // 消息列表
    store.dispatch("setMsgs", {id: sessionId, msgs: remote.store.getters.getMsgs({id: sessionId})});

    // 获取编辑器内容
    let editorMsg = remote.store.getters.getEditorContentMsg({id: sessionId, del: true});
    if (editorMsg) {
      let text = "";
      for (let i = 0; i < editorMsg.length; i++) {
        if (editorMsg[i].type == "text") {
          text += htmlUnEscapeAll(strToHtml(editorMsg[i].text, "", {...editorMsg[i], notSelf: true, custom: {...editorMsg[i]}}, "", true));
        } else if (editorMsg[i].type == "image") {
          text += `<img src="${editorMsg[i].file.url}">`;
        } else if (editorMsg[i].type == "document") {
          text += htmlUnEscapeAll(strToHtml(editorMsg[i].text, "", editorMsg[i], "", true));
        }
      }
      store.commit("setChildEditorHtml", text);
      // 设置当前会话
      store.dispatch("setCurrentSession", {id: sessionId});
      nextTick(() => {
        store.dispatch("activeImEditor", {id: sessionId, active: true});
      });
    }

    // 初始化引用
    if (remote.store.state.quoteMsg[sessionId]) {
      remote.store.commit("setQuoteMsg", {id: sessionId, msg: remote.store.state.quoteMsg[sessionId]});
    }

    return {}
  }
}
</script>
<style scoped lang="scss">
.child-chat {
  width: 100%;
  height: 100%;
}
</style>