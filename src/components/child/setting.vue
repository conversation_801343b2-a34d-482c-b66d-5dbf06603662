<template>
  <div class="setting">
    <div class="titleBox">
      设置
    </div>
    <div class="con-set">
      <div class="left-sel">
        <ul>
          <li :class="[tabIndex == item.value?'curr':'',item.point&&isUpdate?'point':'']" v-for="(item,index) in tabArr" @click="tabNextPrev(item)">{{ item.label }}</li>
        </ul>
      </div>
      <div class="right-show">
        <!-- 账号设置 -->
        <div class="sel-box" v-if="tabIndex == 1">
        <span class="sel-img">
          <img id="userPic" :src="headImg" :onerror="avatarError.bind(this, 'p2p', userSelf.workerNo, '')"></span>
          <p class="tc mt15 font16" id="userName">{{ headName }}</p>
          <p class="tc mt25"><a class="sel-btn" @click="closeWin">退出登录</a></p>
          <p class="tc mt25"><a class="sel-btn" @click="refreshEmpHeadImg">更新头像</a></p>
        </div>
        <!-- 通用设置 -->
        <div class="sel-box setting-box" v-else-if="tabIndex == 2">
          <ul class="setting-ul">
            <li class="setting-li">
              <span class="setting-title">气泡通知</span>
              <div class="setting-content" @click.stop="showSelBox(1)">
                <div class="setting-details arrow" :class="{'sel': selKey==1}">{{ type7 == 1 ? "开" : "关" }}</div>
                <ul class="setting-sel-ul" v-show="selKey==1">
                  <li :class="['setting-sel-li',type7==1?'sel':'']" @click="changeData('msg_bubble_setting','1')">开</li>
                  <li :class="['setting-sel-li',type7==2?'sel':'']" @click="changeData('msg_bubble_setting','2')">关</li>
                </ul>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">抖动设置</span>
              <div class="setting-content" @click.stop="showSelBox(2)">
                <div class="setting-details arrow" :class="{'sel': selKey==2}">{{ type8 == 0 ? "开" : "关" }}</div>
                <ul class="setting-sel-ul" v-show="selKey==2">
                  <li :class="['setting-sel-li',type8==0?'sel':'']" @click="changeData('shake_shield','0')">开</li>
                  <li :class="['setting-sel-li',type8==1?'sel':'']" @click="changeData('shake_shield','1')">关</li>
                </ul>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">开机自启</span>
              <div class="setting-content" @click.stop="showSelBox(3)">
                <div class="setting-details arrow" :class="{'sel': selKey==3}">{{ autoBootStatus == 1 ? "开" : "关" }}</div>
                <ul class="setting-sel-ul" v-show="selKey==3">
                  <li :class="['setting-sel-li',autoBootStatus==1?'sel':'']" @click="autoBoot('1')">开</li>
                  <li :class="['setting-sel-li',autoBootStatus==0?'sel':'']" @click="autoBoot('0')">关</li>
                </ul>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">视频设置</span>
              <div class="setting-content" @click.stop="showSelBox(4)">
                <div class="setting-details arrow" :class="{'sel': selKey==4}">{{ type10 == 0 ? "乐聊播放" : "本地播放" }}</div>
                <ul class="setting-sel-ul" v-show="selKey==4">
                  <li :class="['setting-sel-li',type10==0?'sel':'']" @click="changeData('video_type','0')">乐聊播放</li>
                  <li :class="['setting-sel-li',type10==1?'sel':'']" @click="changeData('video_type','1')">本地播放</li>
                </ul>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">文件管理</span>
              <div class="setting-content">
                <input class="setting-details setting-path" :value="fileCachePath" disabled="disabled">
              </div>
              <div class="setting-btn ml8" @click="openFileCachePath">打开</div>
              <div class="setting-btn ml8" @click="changeFileCachePath">修改</div>
            </li>
            <li class="setting-li" v-if="(env=='onlinetest'||env=='online')&&isProxy">
              <span class="setting-title">线路查看</span>
              <div class="setting-content">
                <input class="setting-details setting-path" :value="proxyInfo.proxyCurrent.name" disabled="disabled">
              </div>
              <div class="setting-btn ml8" @click="showProxy">查看</div>
            </li>
            <li class="setting-li">
              <span class="setting-title">乐聊缓存</span>
              <div class="setting-btn" @click="clearSession">清除缓存</div>
            </li>
          </ul>
        </div>
        <!-- 快捷设置 -->
        <div class="sel-box setting-box" v-else-if="tabIndex == 3">
          <ul class="setting-ul">
            <li class="setting-li">
              <span class="setting-title">发送消息</span>
              <div class="setting-content" @click.stop="showSelBox(5)">
                <div class="setting-details arrow" :class="{'sel': selKey==5}">{{ type4 == 1 ? "Enter" : "Ctrl+Enter" }}</div>
                <ul class="setting-sel-ul" v-show="selKey==5">
                  <li :class="['setting-sel-li',type4==1?'sel':'']" @click="changeData('msg_send_setting','1')">Enter</li>
                  <li :class="['setting-sel-li',type4==2?'sel':'']" @click="changeData('msg_send_setting','2')">Ctrl+Enter</li>
                </ul>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">截取屏幕</span>
              <div class="setting-content" @click.stop="showSelBox(6)">
                <div class="setting-details arrow" :class="{'sel': selKey==6}">{{ type6 }}</div>
                <p class="setting-tips" v-show="jtConflict">快捷键冲突,请重新选择</p>
                <ul class="setting-sel-ul" v-show="selKey==6">
                  <li :class="['setting-sel-li',type6==config.shortcut.jt1?'sel':'']" @click="changeData('screenshot_key_setting',config.shortcut.jt1)">{{ config.shortcut.jt1 }}</li>
                  <li :class="['setting-sel-li',type6==config.shortcut.jt2?'sel':'']" @click="changeData('screenshot_key_setting',config.shortcut.jt2)">{{ config.shortcut.jt2 }}</li>
                </ul>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">显示隐藏</span>
              <div class="setting-content">
                <div class="setting-details">{{ config.shortcut.toggleIm }}</div>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">锁定乐聊</span>
              <div class="setting-content">
                <div class="setting-details">{{ config.shortcut.lockIm }}</div>
              </div>
            </li>
            <li class="setting-li">
              <span class="setting-title">图片线路</span>
              <div class="setting-content" @click.stop="showSelBox(100)">
                <div class="setting-details arrow" :class="{'sel': selKey==100}">线路{{ imgHostType }}</div>
                <ul class="setting-sel-ul" v-show="selKey==100">
                  <li :class="['setting-sel-li',imgHostType==1?'sel':'']" @click="changeImgHostType(1)">线路1</li>
                  <li :class="['setting-sel-li',imgHostType==2?'sel':'']" @click="changeImgHostType(2)">线路2</li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
        <!-- 关于乐聊 -->
        <div class="sel-box setting-box" v-else-if="tabIndex == 4">
          <ul class="about-ul">
            <li class="about-li">
              <span class="about-title line30">当前版本</span>
              <div class="about-content">
                <span>{{ version }}</span>
                <span v-if="isUpdate" class="about-update" @click="checkUpdate()">升级到{{ serverVersion }}</span>
              </div>
            </li>
            <li class="about-li">
              <span class="about-title">升级设置</span>
              <div class="about-content">
                <ul class="update-sel-ul">
                  <li class="update-sel-li" :class="{'sel': isAutoUpdate}" @click="changeAutoUpdate(true)">
                    <div class="update-sel-title">有更新时自动为我安装（推荐）</div>
                    <div class="update-sel-intr">有更新时，自动完成更新下载与安装，省心省力</div>
                  </li>
                  <li class="update-sel-li" :class="{'sel': !isAutoUpdate}" @click="changeAutoUpdate(false)">
                    <div class="update-sel-title">有更新时不要安装，但提醒我</div>
                    <div class="update-sel-intr">此方式不影响安全补丁等重要更新的自动安装</div>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </div>
        <!--主题设置-->
        <div class="sel-box setting-box" v-else-if="tabIndex == 5">
          <div class="style-title">导航栏配色</div>
          <ul class="style-ul">
            <li class="style-li" @click="changeData('style_type', '0')">
              <i class="icon-style icon-style1"></i>
              <div class="style-intr" :class="{'sel':type11==0}">简约灰</div>
            </li>
            <li class="style-li" @click="changeData('style_type', '1')">
              <i class="icon-style icon-style2"></i>
              <div class="style-intr" :class="{'sel':type11==1}">经典蓝</div>
            </li>
          </ul>
        </div>
      </div>

      <Update type="3" ref="updateRef"></Update>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import {refreshEmpHeadApi} from "@utils/net/api.js";
import {defineComponent} from "vue";
import {emitMsg, getGlobal, getAppPath, isRegistered, avatarError, compareVersion, openLocalFile, getFileCachedPath, setLocalUpdateObj, getIconvDecode, setLocalStorage} from '@utils'
import {alert, loading, toast} from "@comp/ui";
import Update from "@comp/update/Update.vue";

const cp = remote.require("child_process");

export default defineComponent({
  name: "setting",
  components: {Update},
  setup(props) {
    const store = useStore();
    const router = useRouter();
    let currentWindow = store.getters.getCurrentWindow();
    let config = ref(store.getters.getConfig.config);
    let env = ref(config.value.env);
    //冲突提示
    let jtConflict = ref(false);
    //当前登录人信息
    let userSelf = ref(store.getters.getUserInfo)
    //文件管理
    let type4 = ref('') //发送消息msg_send_setting
    let type6 = ref('') //截取屏幕screenshot_key_setting
    let type10 = ref('') //视频设置video_type
    let type8 = ref('') //抖动设置shake_shield
    let type7 = ref('') //气泡设置msg_bubble_setting
    let type11 = ref('') //风格设置style_type
    let imgHostType = ref(localStorage.getItem("imgHostType") || 1);
    let autoBootStatus = ref(autoBootData() ? 1 : 0);
    let fileCachePath = ref(getFileCachedPath({account: userSelf.value.workerNo, type: 4})) //文件管理
    let version = ref(store.getters.getConfig.version) // 当前版本号
    let serverVersion = ref(store.getters.getConfig.serverVersion) // 最新版本号
    let isUpdate = ref(false);
    let isAutoUpdate = ref(true);
    // 更新组件
    let updateRef = ref();
    // 默认请求接口获取最新版本号
    store.dispatch("getUpdate", {type: 3});
    getUpdate();

    //下拉选择请求数据
    function changeData(id, val) {
      let param = {type: 3, key: id, value: val};
      remote.store.dispatch("setModifySettings", param).then(res => {
        getSettings();
      });
      if (id == config.value.settings.type11) {
        type11.value = val;
      }
    }

    getSettings();

    //头像，名称
    let headImg = ref(userSelf.value.headPic)
    let headName = ref(userSelf.value.workerName)
    //tab切换索引
    let tabArr = reactive([{
      label: '账号设置',
      value: 1
    }, {
      label: '通用设置',
      value: 2
    }, {
      label: '快捷设置',
      value: 3
    }, {
      label: '主题设置',
      value: 5
    }, {
      label: '关于乐聊',
      value: 4,
      point: true
    }])
    let tabIndex = ref(router.currentRoute.value.query.tabIndex || 1)

    function tabNextPrev(item) {
      tabIndex.value = item.value
    }

    //退出登录
    function closeWin() {
      remote.store.commit("setLogout", {type: 1})
    }

    //清除缓存
    function clearSession() {
      emitMsg("msg", {type: "clear", clear: 2});
    }

    //打开地址
    function openFileCachePath() {
      openLocalFile(fileCachePath.value);
    }

    // 修改缓存路径
    function changeFileCachePath() {
      store.commit("setEmit", {
        type: "fileInput", value: {
          nwdirectory: true,
          done: files => {
            setLocalStorage("fileCachePath", files[0].path);
            fileCachePath.value = getFileCachedPath({account: userSelf.value.workerNo, type: 4});
          }
        }
      });
    }

    //更新头像
    async function refreshEmpHeadImg() {
      let res = await refreshEmpHeadApi()
      console.log(res)
      if (res.success) {
        headImg.value = res.data.headPic
        toast({title: '头像更新成功', type: 1})
      } else {
        toast({title: res.errorMsg, type: 2})
      }
    }

    // 是否有更新
    function getUpdate() {
      isUpdate.value = compareVersion(version.value, serverVersion.value);
      isAutoUpdate.value = localStorage.getItem("isAutoUpdate") == "false" ? false : true;
    }

    watch(() => store.state.config.serverVersion,
      (newValue, oldValue) => {
        serverVersion.value = newValue;
        getUpdate();
      }, {
        deep: true
      }
    );

    watch(() => store.state.emit.closeWindow,
      (newValue, oldValue) => {
        if (newValue == 1) {
          store.commit("setEmit", {type: "closeWindow", value: -1});
        }
      }, {
        deep: true
      }
    );

    // 修改开机自启
    function autoBoot(status) {
      autoBootStatus.value = status;
      autoBootData(status);
    }

    // 检查更新
    function checkUpdate() {
      updateRef.value.getUpdate(true);
    }

    // 改变自动更新
    function changeAutoUpdate(flag) {
      setLocalStorage("isAutoUpdate", flag);
      isAutoUpdate.value = flag;
      // 重置下载失败次数
      setLocalUpdateObj({times: 0});
    }

    // 获取设置信息
    function getSettings() {
      let settingInfo = remote.store.getters.getSettings;
      type4.value = settingInfo[config.value.settings.type4] || '2'
      type6.value = settingInfo[config.value.settings.type6] || config.value.shortcut.jt1
      type10.value = settingInfo[config.value.settings.type10] || '0';
      type8.value = settingInfo[config.value.settings.type8] || '0'
      type7.value = settingInfo[config.value.settings.type7] || '1'
      type11.value = settingInfo[config.value.settings.type11] || '0'
      jtConflict.value = !isRegistered(type6.value);
    }

    // 开机自启 状态type-0关闭-1启动-其他查询
    function autoBootData(type) {
      var appName = remote.App.manifest.name;
      var exePath = remote.process.execPath;
      var cmd, bf, buffer;
      if (type === '0') {
        // 删除
        cmd = 'reg delete "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "' + appName + '" /f';
        bf = cp.execSync(cmd);
        buffer = new Buffer(bf);
        bf = getIconvDecode(buffer, 'gbk');
        return bf.indexOf('成功') > -1;
      } else if (type === '1') {
        // 写入
        try {
          cmd = 'reg add "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "' + appName + '" /t "REG_SZ" /d "' + exePath + '" /f';
          bf = cp.execSync(cmd);
          buffer = new Buffer(bf);
          bf = getIconvDecode(buffer, 'gbk');
          return bf.indexOf('成功') > -1;
        } catch (e) {
          // 已经写入了,无法再次写入
          if (!(e && e.stdout)) {
            console.error(e);
          }
          return;
        }
      } else {
        // 删除老乐聊自启
        try {
          cmd = 'reg delete "HKEY_CURRENT_USER\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "JJS_IM" /f';
          bf = cp.execSync(cmd);
        } catch (e) {}
        // 读取
        try {
          cmd = 'reg query "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "' + appName + '"';
          bf = cp.execSync(cmd);
          buffer = new Buffer(bf);
          bf = getIconvDecode(buffer, 'gbk');
          return bf.split('\n')[2].split(/\s{4}/)[3];
        } catch (e) {
          if (!(e && e.stdout)) {
            // 非读取不到数据的错误
            console.error(e);
          }
          return;
        }
      }
    }

    let selKey = ref(-1);

    // 切换显示选择框
    function showSelBox(key) {
      if (selKey.value != key) {
        selKey.value = key;
      } else {
        selKey.value = -1;
      }
    }

    // 监听全局点击
    watch(() => store.state.emit.allClick,
      (newValue, oldValue) => {
        selKey.value = -1;
      }, {
        deep: true,
      }
    );

    // 切换图片加载线路
    function changeImgHostType(type) {
      alert({
        content: "<span class='highlight'>注意：切换线路可能导致图片无法加载，默认为线路1，是否确认切换图片线路<span>",
        done: (confirmType) => {
          if (confirmType == 1) {
            setLocalStorage("imgHostType", type);
            imgHostType.value = type;
          }
        }
      });
    }

    let proxyInfo = ref(remote.store.getters.getJJsProxy);
    watch(() => store.state.jjsProxy.key,
      (newValue, oldValue) => {
        proxyInfo.value = {};
        proxyInfo.value = remote.store.getters.getJJsProxy;
      }, {
        deep: true
      }
    )

    // 是否开启代理功能
    let isProxy = ref(remote.store.getters.getJJsProxy.open);

    // 显示代理线路
    function showProxy() {
      store.commit("setEmit", {type: "showComponents", value: {type: 3, key: 1}});
    }

    return {
      closeWin,
      refreshEmpHeadImg,
      headImg,
      headName,
      tabArr,
      tabIndex,
      tabNextPrev,
      type4,
      type6,
      type7,
      type8,
      type10,
      type11,
      clearSession,
      fileCachePath,
      version,
      serverVersion,
      changeData,
      jtConflict,
      openFileCachePath,
      config,
      env,
      userSelf,
      avatarError,
      autoBootStatus,
      autoBoot,
      checkUpdate,
      compareVersion,
      isUpdate,
      changeAutoUpdate,
      isAutoUpdate,
      changeFileCachePath,
      selKey,
      showSelBox,
      updateRef,
      imgHostType,
      changeImgHostType,
      proxyInfo,
      isProxy,
      showProxy,
    };
  },
});
</script>
<style lang="scss">
.setting {
  width: 100%;
  height: 100%;
}

.titleBox {
  padding-left: 14px;
  height: 30px;
  line-height: 30px;
  background: #F6F6F6;
  font-weight: bold;
  color: #333333;
}

.con-set {
  font-size: 14px;
  display: flex;
  width: 100%;
  height: calc(100% - 30px - 20px);
  padding-top: 20px;
  box-sizing: border-box;

  .left-sel {
    float: left;
    width: 99px;
    height: 100%;
    border-right: 1px solid #E0E0E0;

    ul {
      margin: 0;
      padding: 0;

      li {
        font-size: 14px;
        line-height: 32px;
        background: transparent;
        display: block;
        width: 100%;
        text-align: center;
        color: #999999;
        cursor: pointer;
        position: relative;

        &.point {
          &:before {
            content: "";
            position: absolute;
            top: 5px;
            right: 14px;
            width: 6px;
            height: 6px;
            background: #f74b32;
            border-radius: 50%;
          }
        }

        &:hover,
        &.curr {
          color: $styleColor;
        }

        &.curr {
          position: relative;
          font-weight: bold;

          &::after {
            position: absolute;
            right: -1px;
            top: 9px;
            content: '';
            width: 2px;
            height: 14px;
            background-color: $styleColor;
          }
        }
      }
    }
  }

  .right-show {
    flex: 1;

    .sel-img {
      display: block;
      width: 100px;
      height: 100px;
      margin: 0px auto;
      border-radius: 50%;
      overflow: hidden;
      border: 1px solid #E7E7E7;

      img {
        display: block;
        width: 100%;
      }
    }

    .sel-btn {
      font-size: 13px;
      display: inline-block;
      height: 26px;
      line-height: 24px;
      padding: 0px 24px;
      color: #000;
      border: 1px solid #E0E0E0;
      border-radius: 4px;
      cursor: pointer;
    }

    .sel-box {
      .setting-ul {
        .setting-li {
          margin-bottom: 20px;
          display: flex;

          .setting-title {
            width: 80px;
            font-size: 14px;
            line-height: 30px;
            color: #000000;
          }

          .setting-content {
            position: relative;
            font-size: 12px;

            .setting-details {
              position: relative;
              width: 130px;
              height: 30px;
              line-height: 28px;
              background: #FFFFFF;
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              padding: 0 10px;

              &.arrow {
                &:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  right: 10px;
                  transform: translateY(-50%);
                  width: 0;
                  height: 0;
                  border-width: 4px 4px 0 4px;
                  border-style: solid;
                  border-color: #000000 transparent transparent transparent;
                }
              }

              &.setting-path {
                width: 190px;
                color: #000000;
              }
            }

            .setting-sel-ul {
              position: absolute;
              top: 31px;
              width: 100%;
              max-height: 158px;
              background: #fff;
              box-shadow: 0 2px 8px 0 rgba(51, 51, 51, .25);
              border-radius: 4px;
              border: 1px solid #e0e0e0;
              padding: 4px 0;
              z-index: 1;
              overflow-y: overlay;

              .setting-sel-li {
                position: relative;
                height: 30px;
                line-height: 30px;
                padding: 0 10px;

                &:hover {
                  background: $styleBg1Hover;
                }

                &.sel {
                  color: $styleColor;

                  &:after {
                    content: "";
                    position: absolute;
                    top: 11px;
                    right: 10px;
                    width: 8px;
                    height: 4px;
                    border-left: 2px solid $styleColor;
                    border-bottom: 2px solid $styleColor;
                    transform: rotate(-45deg);
                  }
                }
              }
            }

            .setting-tips {
              color: $styleColor;
            }
          }

          .setting-btn {
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            white-space: nowrap;
            cursor: pointer;

            &:hover {
              border: 1px solid $styleColor;
              color: $styleColor;
            }
          }
        }
      }

      &.setting-box {
        padding: 0 32px;

        .icon-style {
          display: block;
          width: 182px;
          height: 98px;
          background-image: url("/img/more/icon_style.png");
          background-size: 364px 98px;
          background-repeat: no-repeat;

          &.icon-style2 {
            background-position: -182px 0;
          }
        }

        .style-title {
          font-size: 14px;
          line-height: 32px;
          margin-bottom: 10px;
        }

        .style-ul {
          display: flex;
          justify-content: space-between;

          .style-li {
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            overflow: hidden;
            cursor: pointer;

            &:hover {
              border: 1px solid $styleColor;
            }

            .style-intr {
              height: 32px;
              line-height: 32px;
              font-size: 13px;
              color: #000000;
              border-top: 1px solid #E0E0E0;
              padding-left: 34px;
              position: relative;

              &:before {
                content: "";
                position: absolute;
                top: 50%;
                left: 10px;
                transform: translateY(-50%);
                width: 16px;
                height: 16px;
                border-radius: 50%;
                border: 1px solid #E0E0E0;
              }

              &.sel {
                &:before {
                  background: $styleColor;
                  border: 1px solid $styleColor;
                }

                &:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: 16px;
                  transform: translateY(-50%);
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  background: #FFFFFF;
                }
              }
            }
          }
        }

        .about-ul {
          .about-li {
            display: flex;
            margin-bottom: 40px;
            line-height: 20px;

            .about-title {
              width: 86px;
              flex-shrink: 0;

              &.line30 {
                line-height: 30px;
              }
            }

            .about-content {
              display: flex;
              align-items: center;

              .about-update {
                font-size: 13px;
                line-height: 18px;
                padding: 3px 10px;
                color: #FFFFFF;
                background: $styleColor;
                border-radius: 4px;
                overflow: hidden;
                cursor: pointer;
                margin-left: 16px;
                margin-top: -2px;
              }

              .update-sel-ul {
                .update-sel-li {
                  padding-left: 24px;
                  margin-bottom: 20px;
                  position: relative;
                  cursor: pointer;

                  &:before {
                    content: "";
                    position: absolute;
                    top: 2px;
                    left: 0;
                    width: 14px;
                    height: 14px;
                    border-radius: 50%;
                    border: 1px solid #E0E0E0;
                  }

                  &.sel {
                    &:before {
                      background: $styleColor;
                      border: 1px solid $styleColor;
                    }

                    &:after {
                      content: "";
                      position: absolute;
                      top: 7px;
                      left: 5px;
                      width: 6px;
                      height: 6px;
                      border-radius: 50%;
                      background: #FFFFFF;
                    }
                  }

                  .update-sel-intr {
                    font-size: 12px;
                    line-height: 17px;
                    color: #999999;
                    margin-top: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.mr20 {
  margin-right: 20px;
}

.mt20 {
  margin-top: 20px;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.mt15 {
  margin-top: 15px;
}

.font16 {
  font-size: 16px;
}

.mt25 {
  margin-top: 25px;
}

.tc {
  text-align: center;
}

.txtEllipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>