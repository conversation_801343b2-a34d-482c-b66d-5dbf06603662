<template>
  <div class="net-connect" ref="netRef" @click="toggleComputerTypeFlag(false)">
    <div class="child-win-nav win-drag">公司电脑认证</div>
    <div class="net-content selAll" v-show="netInfo.status!=-1">
      <!--认证状态-->
      <div class="net-result" v-show="netInfo.status!=0">
        <div class="main-title">{{ netInfo.existTips }}</div>
        <div class="btn-box">
          <button class="btn btn-close" @click="winClose(true)">{{ netInfo.status == 3 ? "关闭" : "确认" }}</button>
          <button class="btn btn-close" @click="setStatus(0)" v-if="netInfo.status!=1&&netInfo.status!=3">重新申请</button>
          <button class="btn btn-close" @click="winReload()" v-if="netInfo.status==3">重新加载</button>
        </div>
      </div>
      <!--填写认证信息-->
      <div :class="['net-apply','net-apply-'+applyInfo.selType]" v-show="netInfo.status==0">
        <!--设备归属-->
        <div class="apply-content radio-box">
          <label>设备归属:</label>
          <ul class="apply-radio">
            <li :class="{'sel':applyInfo.selType==1}" @click="selTypeItem(1)">公司电脑</li>
            <li v-if="false" :class="{'sel':applyInfo.selType==2,'disabled': applyInfo.disabledSel}" @click="selTypeItem(2)">私人电脑</li>
          </ul>
        </div>
        <!--申请人-->
        <div class="apply-content">
          <span class="input-box">
            <label for="workerName">申请人:</label>
            <span class="input-search-box">
              <input type="text" id="workerName" v-model.trim="personInfo[1].val" placeholder="请输入申请人" autocomplete="off" maxlength="50"
                     @input="doSearch(1)" @blur="blurSearch(1)" @keyup.enter="selPerson(1,personInfo[1].list[personIndex])"
                     @keydown.up.prevent="selIndex(1,'pre')" @keydown.down.prevent="selIndex(1,'next')" @keydown.esc.prevent="blurSearch(1)">
              <ul class="show-ul" v-show="personInfo[1].val&&personInfo[1].list.length>0" ref="search1Ref">
                <li class="textEls" :class="{'curr': personIndex == key}" v-for="(item, key) in personInfo[1].list" :key="item.empNumber"
                    :title="item.empName + (item.deptName ? `(${item.deptName})` : '')"
                    @mousedown="selPerson(1,item)" @mouseenter="selIndex(1,key)">{{ item.empName + (item.deptName ? `(${item.deptName})` : "") }}</li>
              </ul>
            </span>
          </span>
          <span class="input-box">
            <label for="workerPwd">申请人登录密码:</label>
            <span class="input-search-box input-search-box-pwd">
              <input :type="showPwdFlag?'text':'password'" v-model="applyInfo.workerPwd" id="workerPwd" maxlength="50" placeholder="请输入申请人登录密码">
              <div class="show-pwd icon-pwd" :class="showPwdFlag?'show':''" @click="toggleShowPwd()"></div>
            </span>
          </span>
        </div>
        <!--电脑类型-->
        <div v-show="applyInfo.selType==1||applyInfo.selType==-1" class="apply-content">
          <span class="input-box">
            <label>电脑类别:</label>
            <div class="show-text">
              <div :class="{'default':applyInfo.pcTypeItem.value==0,'sel':computerTypeFlag}" @click.stop="toggleComputerTypeFlag()">
                {{ applyInfo.pcTypeItem.text }}
              </div>
               <ul class="show-ul" v-show="computerTypeFlag">
                 <li class="textEls" v-for="(item, key) in computerTypeList" :key="item.value" @click="selComputerType(item)">{{ item.text }}</li>
               </ul>
            </div>
          </span>
          <span class="input-box">
            <label>注册名称:</label>
            <span class="input-search-box computer-name textEls" :title="applyInfo.pcName">{{ applyInfo.pcName }}</span>
          </span>
        </div>
        <!--mac地址-->
        <div v-show="applyInfo.selType==1||applyInfo.selType==-1" class="apply-content">
          <span class="input-box">
            <label>资产编码:</label>
            <span class="input-search-box computer-name textEls" :title="netInfo.assetNo">{{ netInfo.assetNo }}</span>
          </span>
        </div>
        <!--宽带-->
        <div v-show="applyInfo.selType==1&&applyInfo.pcTypeItem.value==1&&applyInfo.canInputAdlsNumber">
          <div class="apply-content">
            <span class="input-box">
              <label for="netAccount">宽带账号:</label>
              <span class="input-search-box">
                <input type="text" id="netAccount" maxlength="50" v-model="applyInfo.adlsAccount" autocomplete="off" placeholder="请输入宽带账号">
              </span>
            </span>
            <span class="input-box">
              <label for="netPwd">宽带密码:</label>
              <span class="input-search-box">
                <input type="text" id="netPwd" maxlength="50" v-model="applyInfo.adlsPwd" placeholder="请输入宽带密码">
              </span>
            </span>
          </div>
        </div>
        <!--联系人-->
        <div v-show="(applyInfo.selType==1||applyInfo.selType==-1)&&applyInfo.showTips" class="apply-content contact-box">
          <div>{{ applyInfo.showTips }}，请联系资产部</div>
          <div class="apply-contact" v-for="(item,key) in netInfo.engineers" :key="item.empNo" @click="toChat('p2p',item.empNo)">
            <span>{{ key != 0 ? "/" : "" }}</span>
            <span class="to-chat">{{ item.empName }}</span>
          </div>
          <div>处理</div>
        </div>
        <!--授权时间-->
        <div v-show="applyInfo.selType==2" class="apply-content time-box">
          <span class="input-box">
            <label>授权时间:</label>
            <div class="apply-time">
              <div :class="{'show-text':true,'sel':applyInfo.timeItem.startTime,'disabled':!applyInfo.timeFlag}" @click.stop="showCalendar($event,new Date(),1)">
                {{ !applyInfo.timeFlag ? "" : applyInfo.timeItem.startTime ? dateFormat(applyInfo.timeItem.startTime, "yyyy-MM-dd") : "开始时间" }}
              </div>
              <div class="time-line">—</div>
              <div :class="{'show-text':true,'sel':applyInfo.timeItem.endTime,'disabled':!applyInfo.timeFlag}" @click.stop="showCalendar($event,new Date(),2)">
                {{ !applyInfo.timeFlag ? "" : applyInfo.timeItem.endTime ? dateFormat(applyInfo.timeItem.endTime, "yyyy-MM-dd") : "结束时间" }}
              </div>
              <div class="apply-time-sel" @click="applyInfo.timeFlag=!applyInfo.timeFlag">
                <i :class="['sel-box-i', !applyInfo.timeFlag?'sel':'']"></i>
                <span>不限时间</span>
              </div>
            </div>
          </span>
          <span class="input-box">
            <label>注册名称:</label>
            <span class="input-search-box computer-name textEls" :title="applyInfo.pcName">{{ applyInfo.pcName }}</span>
          </span>
        </div>
        <!--申请原因-->
        <div v-show="applyInfo.selType==2" class="apply-content area-box">
          <label>申请原因:</label>
          <textarea id="workerRemark" placeholder="请输入原因,200字以内" v-model.trim="applyInfo.remark" maxlength="200"></textarea>
          <span class="area-num">{{ applyInfo.remark.length }}/200</span>
        </div>
        <!--说明-->
        <div v-show="applyInfo.selType==2" class="apply-content intr-box">
          <label>说明:</label>
          <div class="apply-intr">
            1、非法申请和虚假证明的,一经证实将被降级或者辞退!<br>
            2、私人电脑需连接使用公司系统的,由本人提交【公司电脑认证】逐级审批至所在部门最高负责人<br>
            3、证明人全部审批同意后,管理员会在2天内核实属实后发放连接授权
          </div>
        </div>
        <!--日历-->
        <div class="calendar" v-show="calendarFlag" ref="calendarBoxRef" @click.stop="stopPropagation()">
          <Calendar ref="calendarRef" :setCurrentDay="setCurrentDay" :setChangeDay="setChangeDay"></Calendar>
        </div>

        <div class="btn-box">
          <button class="btn btn-close" @click="winClose()">取消</button>
          <button class="btn btn-apply" @click="doApply()">提交</button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {nextTick, ref, watch} from "vue";
import {useStore} from "vuex";
import {searchUsersApi, getNewNetCheckApi, getNewNetNameApi, applyNewNetApi, sendNoticeApi} from "@utils/net/api.js";
import {alert, loading, toast} from "@comp/ui";
import {getNetConnectInfo, debounce, MD5, getOffset, dateFormat} from "@utils";
import Calendar from "@comp/ui/comps/Calendar";

export default {
  name: "netConnect",
  components: {Calendar},
  setup(props, ctx) {
    const store = useStore();
    let computerInfo = remote.store.getters.getComputerInfo;
    let netComputerInfo = ref(remote.store.getters.getNetComputerInfo);
    let userInfo = store.getters.getUserInfo;
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();
    store.commit("setEmit", {type: "closeWindow", value: -1});
    // 网络连接元素
    let netRef = ref();
    // 是否显示日历
    let calendarFlag = ref(false);
    let showPwdFlag = ref(false);
    // 日历元素
    let calendarRef = ref();
    // 日历元素框
    let calendarBoxRef = ref();
    // 监听窗口关闭事件
    watch(() => store.state.emit.winCloseClick,
      (newValue, oldValue) => {
        winClose();
      }, {
        deep: true
      }
    );
    // 网络连接状态
    let netInfo = ref({
      status: -1
    });
    // 选择人员信息
    let personInfo = ref({
      1: {val: "", list: []},
    });
    // 加载选择用户状态
    let personInfoFlag = false;
    // 搜索元素
    let search1Ref = ref();
    let personIndex = ref(0);
    // 申请提交信息
    let applyInfo = ref({
      timeFlag: true,// 私人电脑是否需要选择授权时间
      disabledSel: false,// 存在资产编码不允许选择私人电脑
      selType: -1,// 1公司电脑 2私人电脑
      canInputAdlsNumber: false,// 是否需要输入宽带
      showTips: "",// 设备部门不对/不存在资产编码
      workerPwd: "",
      pcName: "",
      remark: "",
      adlsAccount: "",
      adlsPwd: "",
      pcTypeItem: {abbreviation: "", text: "请选择", value: 0},
      privateTypeItem: {abbreviation: "私", text: "私人电脑", value: 4},
      timeItem: {startTime: "", endTime: "", type: -1},
    });
    // 电脑类别列表
    let computerTypeList = ref([applyInfo.value.pcTypeItem].concat([{abbreviation: "分", text: "分行电脑", value: 1}, {abbreviation: "总", text: "总部电脑", value: 3}]));
    // 显示选择电脑类别
    let computerTypeFlag = ref(false);

    // 补偿机制，存在电脑id，硬盘型号和序列号获取不到，但是通过wmic获取到了则替代
    if (netComputerInfo.value.ComputerId && (!netComputerInfo.value.SerialNumber || !netComputerInfo.value.Model) && computerInfo.diskdriveSerialnumber && computerInfo.diskdriveCaption) {
      netComputerInfo.value.SerialNumber = computerInfo.diskdriveSerialnumber;
      netComputerInfo.value.Model = computerInfo.diskdriveCaption;
    }
    if (!netComputerInfo.value.SerialNumber || !netComputerInfo.value.Model) {
      toast({title: "获取不到硬盘ID", type: 2});
    } else if (netComputerInfo.value.Mac.length == 0) {
      toast({title: "获取不到MAC地址", type: 2});
    } else {
      init();
    }


    // 初始化数据
    async function init() {
      loading();
      let secret = await store.dispatch("getSecretEnCrypt", {param: {}});
      // 获取网络连接情况
      let netRes = await getNewNetCheckApi({
        "hdSerialNumber": netComputerInfo.value.SerialNumber,
        "hdModelNumber": netComputerInfo.value.Model,
        "computerId": netComputerInfo.value.ComputerId,
        "macAddress": netComputerInfo.value.Mac[0],
        "netIds": netComputerInfo.value.Mac[1] || netComputerInfo.value.Mac[0],
        "computerInfo": secret,
      });
      loading().hide();
      if (netRes?.success) {
        let netData = netRes.data || {status: 0};
        if (netData.status === null) {
          netData.status = 0;
        } else if (netData.existApply && netData.status != 1) {
          netData.status = 2;
        }
        if (!netData.assetNo) {
          applyInfo.value.showTips = "资产系统找不到电脑MAC地址";
        } else {
          applyInfo.value.selType = 1;
          applyInfo.value.disabledSel = true;
        }
        netInfo.value = netData;
        nextTick(() => {
          setWinWH(1);
        });
      } else {
        netInfo.value = {existTips: (netRes.errorMsg || "服务异常!") + netRes.errorCode, status: 3};
      }
    }

    // 选择电脑类别
    function selComputerType(item) {
      applyInfo.value.pcTypeItem = item;
      getPcName();
      toggleComputerTypeFlag(false);
    }

    // 切换显示选择电脑类别
    function toggleComputerTypeFlag(flag) {
      if (flag != null) {
        computerTypeFlag.value = flag;
      } else {
        computerTypeFlag.value = !computerTypeFlag.value;
      }
      showCalendar();
    }

    // 输入关键词搜索
    function doSearch(type) {
      // 重置选择内容
      personInfo.value[type].list = [];
      personInfo.value[type].empName = "";
      personInfo.value[type].empNumber = "";
      personInfo.value[type].deptName = "";
      personInfo.value[type].deptNumber = "";
      personIndex.value = 0;
      // 防止热加载执行多次
      debounce({
        timerName: "doSearch",
        time: 300,
        fnName: function () {
          if (!personInfo.value[type].val) {
            return;
          }
          getPcPerson(type);
        }
      });
    }

    // 失去焦点
    function blurSearch(type) {
      if (!personInfo.value[type].deptNumber) {
        personInfo.value[type].val = "";
      }
      personInfo.value[type].list = [];
      getPcName();
    }

    // 选择人员
    function selPerson(type, item) {
      personInfo.value[type].empName = item.empName;
      personInfo.value[type].empNumber = item.empNumber;
      personInfo.value[type].deptName = item.deptName;
      personInfo.value[type].deptNumber = item.deptNumber;
      personInfo.value[type].val = item.empName + (item.deptName ? `(${item.deptName})` : "");
      personInfo.value[type].list = [];
      getPcName();
    }

    // 选择人员
    function selIndex(type, key) {
      switch (key) {
        case "pre":
          if (personIndex.value - 1 >= 0) {
            personIndex.value--;
          }
          break;
        case "next":
          if (personIndex.value + 1 < personInfo.value[type].list.length) {
            personIndex.value++;
          }
          break;
        default:
          personIndex.value = key;
          break;
      }
      nextTick(() => {
        scrollLi();
      });
    }

    // 滚动到可视区域搜索列表
    function scrollLi() {
      nextTick(() => {
        let searchBox = search1Ref.value;
        let currLiElm = Array.prototype.find.call(searchBox.querySelectorAll("li"), item => {return /curr/.test(item.className)});
        // 判断可视区域
        if (currLiElm) {
          if (currLiElm.offsetTop + currLiElm.offsetHeight >= searchBox.scrollTop + searchBox.clientHeight) {
            search1Ref.value.scrollTop = currLiElm.offsetTop + currLiElm.offsetHeight - searchBox.clientHeight;
          } else if (currLiElm.offsetTop <= searchBox.scrollTop) {
            search1Ref.value.scrollTop = currLiElm.offsetTop;
          }
        }
      });
    }

    // 申请提交
    async function doApply() {
      if (applyInfo.value.selType == -1) {
        toast({title: "请选择设备归属", type: 2});
        return;
      } else if (applyInfo.value.selType == 1 && applyInfo.value.showTips) {
        toast({title: `${applyInfo.value.showTips}，请联系资产`, type: 2});
        return;
      } else if (!personInfo.value[1].empNumber) {
        toast({title: "请输入申请人", type: 2});
        return;
      } else if (!applyInfo.value.workerPwd) {
        toast({title: "请输入申请人登录密码", type: 2});
        return;
      } else if (applyInfo.value.selType == 1 && !applyInfo.value.pcTypeItem.value) {
        toast({title: "请选择电脑类别", type: 2});
        return;
      }
      let param = {
        pcId: netInfo.value.existPcId,
        type: applyInfo.value.selType == 1 ? applyInfo.value.pcTypeItem.value : applyInfo.value.privateTypeItem.value,
        pcName: applyInfo.value.pcName,
        applyEmpName: personInfo.value[1].empName,
        applyEmpNumber: personInfo.value[1].empNumber,
        applyDeptName: personInfo.value[1].deptName,
        applyDeptNumber: personInfo.value[1].deptNumber,
        applyPassword: MD5(applyInfo.value.workerPwd.toLowerCase()),
        applyPasswordV2: MD5(applyInfo.value.workerPwd),
        hdSerialNumber: netComputerInfo.value.SerialNumber,
        hdModelNumber: netComputerInfo.value.Model,
        computerId: netComputerInfo.value.ComputerId,
        macAddress: netComputerInfo.value.Mac[0],
        netIds: netComputerInfo.value.Mac[1] || netComputerInfo.value.Mac[0]
      }
      // 公司电脑加入资产编码
      if (param.type == 1 || param.type == 3) {
        param.assetNo = netInfo.value.assetNo;
      }

      if (param.type == 1) {
        // 分行电脑
        if (applyInfo.value.canInputAdlsNumber) {
          // 判断是否需要宽带
          if (!applyInfo.value.adlsAccount) {
            toast({title: "请输入宽带账号", type: 2});
            return;
          }
          if (!applyInfo.value.adlsPwd) {
            toast({title: "请输入宽带密码", type: 2});
            return;
          }
          param.adlsAccount = applyInfo.value.adlsAccount;
          param.adlsPwd = applyInfo.value.adlsPwd;
        }
      } else if (param.type == 4) {
        // 私人电脑
        if (applyInfo.value.timeFlag) {
          if (!applyInfo.value.timeItem.startTime) {
            toast({title: "请选择授权开始时间", type: 2});
            return;
          }
          if (!applyInfo.value.timeItem.endTime) {
            toast({title: "请选择授权结束时间", type: 2});
            return;
          }
          param.useStartTime = dateFormat(applyInfo.value.timeItem.startTime, "yyyy-MM-dd");
          param.useEndTime = dateFormat(applyInfo.value.timeItem.endTime, "yyyy-MM-dd");
        }
        if (!applyInfo.value.remark) {
          toast({title: "请输入申请原因", type: 2});
          return;
        }
        param.applyReason = applyInfo.value.remark;
      }
      loading();
      let secret = await store.dispatch("getSecretEnCrypt", {param: {}});
      param.computerInfo = secret;
      let res = await applyNewNetApi(param);
      loading().hide();
      if (res.success || res.data) {
        alert({
          content: res.errorMsg || res.data || "提交成功",
          showCancel: false,
          done: () => {
            winClose(true);
          }
        });
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 网络连接申请人搜索api,type-1申请人-2使用者-3证明人1-4证明人2
    async function getPcPerson(type) {
      let res = await searchUsersApi({
        msgBody: JSON.stringify({
          workerId: userInfo.workerId,
          name: personInfo.value[type].val,
          status: "1",//默认为全部1在职2为离职
          page: 1,
          rows: 50
        })
      });
      if (res?.data?.empList) {
        if (res.data.empList.length == 0) {
          personInfo.value[type].list = [{empName: "无数据"}];
        } else {
          personInfo.value[type].list = res.data.empList;
        }
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 网络连接电脑名api
    async function getPcName() {
      if (((applyInfo.value.selType == 1 && applyInfo.value.pcTypeItem.value) || applyInfo.value.selType == 2) && personInfo.value[1].empNumber) {
        if (personInfoFlag) {
          return;
        }
        personInfoFlag = true;
        let res = await getNewNetNameApi({
          "type": applyInfo.value.selType == 1 ? applyInfo.value.pcTypeItem.value : applyInfo.value.privateTypeItem.value,
          "pcUserNumber": personInfo.value[1].empNumber || "",
          "assetNo": netInfo.value.assetNo
        });
        personInfoFlag = false;
        if (res.success) {
          applyInfo.value.pcName = res.data?.pcName;
          applyInfo.value.canInputAdlsNumber = res.data?.canInputAdlsNumber;
          if (!applyInfo.value.showTips || applyInfo.value.showTips == "该设备归属部门不是您的部门") {
            if (res.data?.canSubmitApply === false) {
              applyInfo.value.showTips = "该设备归属部门不是您的部门";
            } else {
              applyInfo.value.showTips = "";
            }
            setWinWH(1);
          }
        } else {
          applyInfo.value.pcName = "";
          applyInfo.value.canInputAdlsNumber = false;
          toast({title: res.errorMsg || "系统错误", type: 2});
        }
      } else {
        applyInfo.value.pcName = "";
        applyInfo.value.canInputAdlsNumber = false;
      }
    }

    // 关闭窗口
    function winClose(flag) {
      if (flag || netInfo.value.status !== 0) {
        store.commit("setEmit", {type: "closeWindow", value: ""});
        store.commit("setWindowClose", currentWindow.cWindow.id);
      } else {
        alert({
          content: "确定取消申请公司电脑认证吗?",
          done: (type) => {
            if (type == 1) {
              store.commit("setEmit", {type: "closeWindow", value: ""});
              store.commit("setWindowClose", currentWindow.cWindow.id);
            } else {
              store.commit("setEmit", {type: "closeWindow", value: -1});
            }
          }
        })
      }
    }

    // 重新加载
    function winReload() {
      location.reload();
    }

    // 切换公司和私人电脑
    function selTypeItem(type) {
      if (applyInfo.value.disabledSel) {
        return;
      }
      applyInfo.value.selType = type;
      getPcName();
      nextTick(() => {
        setWinWH(type);
      });
    }

    // 显示日历,date为开始时间，type-1开始时间-2结束时间
    function showCalendar(e, date, type) {
      if (!date || !applyInfo.value.timeFlag) {
        calendarFlag.value = false;
        return;
      }
      let startTime = new Date(new Date(new Date().getTime() + store.getters.getDiffTime).toLocaleDateString());
      if (type == 1 && applyInfo.value.timeItem.startTime) {
        startTime = applyInfo.value.timeItem.startTime;
      } else if (type == 2 && applyInfo.value.timeItem.endTime) {
        startTime = applyInfo.value.timeItem.endTime;
      }
      let calendarParam = {date: startTime, minDate: startTime.getTime()};
      if (type == 1 && applyInfo.value.timeItem.endTime) {
        // 存在结束时间，开始时间不能超过1年
        calendarParam.minDate = applyInfo.value.timeItem.endTime - 365 * 24 * 60 * 60 * 1000;
        calendarParam.maxDate = applyInfo.value.timeItem.endTime;
      } else if (type == 2) {
        // 选择结束时间的当前时间点
        calendarParam.startTime = applyInfo.value.timeItem.endTime || applyInfo.value.timeItem.startTime;
        if (!calendarParam.startTime) {
          toast({title: "请选择开始时间", type: 2});
          return;
        }
        // 选择结束时间的最大时间不能超过1年
        calendarParam.maxDate = calendarParam.startTime.getTime() + 365 * 24 * 60 * 60 * 1000;
      }
      applyInfo.value.timeItem.type = type;
      calendarRef.value.getCalendar(calendarParam);
      // 设置日历位置
      let offset = getOffset(e.target);
      calendarBoxRef.value.style.top = "30px";
      calendarBoxRef.value.style.left = offset.left + e.target.clientWidth + 10 + "px";
      calendarFlag.value = true;
    }

    // 设置当前时间
    function setCurrentDay(res) {
      if (res.err == "minDate") {
        toast({title: applyInfo.value.timeItem.type == 1 ? (applyInfo.value.timeItem.endTime ? "使用时间不可以选择超过1年的时间" : "不能选择已经过去的时间") : "结束时间不能小于开始时间", type: 2});
      } else if (res.err == "maxDate") {
        toast({title: applyInfo.value.timeItem.type == 1 ? "开始时间不能大于结束时间" : "使用时间不可以选择超过1年的时间", type: 2});
      } else {
        if (applyInfo.value.timeItem.type == 1) {
          applyInfo.value.timeItem.startTime = res.date;
        } else if (applyInfo.value.timeItem.type == 2) {
          applyInfo.value.timeItem.endTime = res.date;
        }
        showCalendar();
      }
    }

    // 切换上下月
    function setChangeDay(res) {
      if (res.err == "minDate") {
        toast({title: applyInfo.value.timeItem.type == 1 ? (applyInfo.value.timeItem.endTime ? "使用时间不可以选择超过1年的时间" : "不能选择已经过去的时间") : "结束时间不能小于开始时间", type: 2});
      } else if (res.err == "maxDate") {
        toast({title: applyInfo.value.timeItem.type == 1 ? "开始时间不能大于结束时间" : "使用时间不可以选择超过1年的时间", type: 2});
      }
    }

    // 打开对应会话
    async function toChat(scene, to) {
      // 打开会话带入内容
      let content = `用户：${userInfo.name}\n发生时间：${dateFormat(Date.now(), "yyyy-MM-dd HH:mm")}\n系统型号: ${computerInfo.Name}\nOS 名称: ${computerInfo.Caption} ${computerInfo.OSArchitecture}\nMAC：${netComputerInfo.value.Mac[0]}`;
      loading();
      let res = await sendNoticeApi({
        noticeEmpNo: to,
        noticeContext: content,
      });
      loading().hide();
      if (!res.success) {
        alert({content: res.errorMsg, showCancel: false, okText: "我知道了",});
        return;
      }
      remote.store.dispatch("setCurrentSession", {id: `${scene}-${to}`, type: "open"});
    }

    // 设置网络连接宽高
    function setWinWH(type) {
      let bounds = currentWindow.getBounds();
      let width = bounds.width;
      let height = bounds.height;
      switch (type) {
        case 1:
          if (netInfo.value.status !== 0) {
            // 启动窗口大小
            width = 443;
            height = 144;
            let tipsElm = netRef.value.querySelector(".main-title");
            if (tipsElm?.clientHeight - 34 > 0) {
              height += tipsElm.clientHeight - 34;
            }
          } else {
            // 公司电脑
            width = 540;
            height = 249;
            if (applyInfo.value.canInputAdlsNumber) {
              height += 40;
            }
            if (applyInfo.value.showTips) {
              height += 29;
            }
          }
          break;
        case 2:
          //私人电脑
          width = 652;
          height = 362;
          break
      }
      store.commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: bounds.x, y: bounds.y, width: width, height: height, minW: width, minH: height, resizable: false});
    }

    // 设置显示状态
    function setStatus(status) {
      netInfo.value.status = status;
      setWinWH(1);
    }

    // 切换申请人登录密码乐聊密码显示
    function toggleShowPwd() {
      showPwdFlag.value = !showPwdFlag.value;
    }

    // 阻止点击穿透
    function stopPropagation() {}

    return {
      netRef,
      computerTypeList,
      computerTypeFlag,
      netInfo,
      personInfo,
      search1Ref,
      personIndex,
      applyInfo,
      netComputerInfo,
      calendarRef,
      calendarBoxRef,
      calendarFlag,
      showPwdFlag,

      selComputerType,
      toggleComputerTypeFlag,
      winClose,
      winReload,
      doSearch,
      blurSearch,
      selPerson,
      selIndex,
      doApply,
      selTypeItem,
      setCurrentDay,
      showCalendar,
      setChangeDay,
      toChat,
      stopPropagation,
      dateFormat,
      setStatus,
      toggleShowPwd,
    }
  }
}
</script>
<style scoped lang="scss">
.net-connect {
  width: 100%;
  height: 100%;
  overflow: hidden;

  ::placeholder {
    color: #999999;
  }

  .calendar {
    position: absolute;
    top: 0;
    left: 0;
    width: 244px;
    background: #FFFFFF;
    box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
    border-radius: 2px;
    border: 1px solid #CCCCCC;
    padding: 10px;
  }

  .btn-box {
    margin-top: 16px;
    display: flex;
    justify-content: center;

    .btn {
      height: 30px;
      padding: 0 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #FFFFFF;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 16px;

      &:last-child {
        margin-right: 0;
      }

      &.btn-close {
        color: #000000;
        border: 1px solid #E0E0E0;
      }

      &.btn-apply {
        background: $styleColor;
        color: #FFFFFF;
      }
    }
  }

  .net-content {
    margin: 0 auto;
    padding: 16px;

    .net-result {
      .main-title {
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        line-height: 17px;
        word-break: break-all;
      }
    }

    .net-apply {

      &.net-apply-2 {
        .input-box {
          width: 58% !important;

          &:nth-child(2n) {
            width: 42% !important;
          }
        }
      }

      label {
        width: 62px;
        height: 30px;
        line-height: 30px;
        padding-right: 10px;
        color: #666666;
        text-align: right;
        flex-shrink: 0;
      }

      .apply-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;

        .input-box {
          width: 50%;
          display: flex;
          line-height: 28px;

          &:nth-child(2n) {
            label {
              width: 98px;
            }
          }

          &.w100 {
            width: 100%;
          }

          .show-ul {
            position: absolute;
            top: 100%;
            left: -1px;
            width: 160px;
            max-height: 120px;
            line-height: 26px;
            overflow-y: auto;
            border: 1px solid #ccc;
            border-radius: 2px;
            margin-top: 1px;
            background: #FFFFFF;
            z-index: 10;

            li {
              padding: 0px 5px;

              &.curr,
              &:hover {
                background-color: #474752;
                color: #FFFFFF;
              }
            }
          }

          .input-search-box {
            width: 160px;
            height: 30px;
            line-height: 28px;
            background: #FFFFFF;
            border-radius: 4px;
            border: 1px solid #E0E0E0;
            position: relative;

            &.computer-name {
              width: 160px;
              height: 30px;
              line-height: 28px;
              padding-left: 10px;
              background: $styleBg1Hover;
              border-radius: 4px;
              border: 1px solid #CCCCCC;
              color: #999999;
            }

            &.input-search-box-pwd {
              padding-right: 10px;
            }

            .icon-pwd {
              width: 15px;
              height: 15px;
              background-image: url("/img/login/pwd_toggle.png");
              background-repeat: no-repeat;
              background-size: 60px 15px;
              cursor: pointer;

              &:hover {
                background-position: -15px 0;
              }

              &.show {
                background-position: -30px 0;

                &:hover {
                  background-position: -45px 0;
                }
              }
            }

            .show-pwd {
              position: absolute;
              top: 50%;
              right: 3px;
              transform: translateY(-50%);
              cursor: pointer;
            }
          }

          input {
            width: 100%;
            height: 100%;
            padding: 0 10px;
            border-radius: 4px;
          }

          .show-text {
            width: 160px;
            height: 30px;
            line-height: 28px;
            border: 1px solid #CCCCCC;
            position: relative;
            padding: 0 10px;
            border-radius: 4px;

            .default {
              color: #999999;

              &.sel {
                &:after {
                  border-width: 0 4px 4px 4px;
                  border-color: transparent transparent #000000 transparent;
                }
              }

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 10px;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 4px 0;
                border-style: solid;
                border-color: #000000 transparent transparent transparent;
              }
            }
          }
        }

        &.radio-box {
          justify-content: flex-start;
          margin: 0 0 12px;

          label {
            height: 17px;
            line-height: 17px;
          }

          .apply-radio {
            display: flex;
            align-items: center;

            li {
              width: 94px;
              padding-left: 18px;
              position: relative;
              cursor: pointer;

              &.disabled {
                &:before {
                  background: #CCCCCC;
                }
              }

              &:before {
                content: "";
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                width: 12px;
                height: 12px;
                border-radius: 50%;
                border: 1px solid #E0E0E0;
              }

              &.sel {
                &:before {
                  background: $styleColor;
                  border: 1px solid $styleColor;
                }

                &:after {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: 5px;
                  transform: translateY(-50%);
                  width: 4px;
                  height: 4px;
                  border-radius: 50%;
                  background: #FFFFFF;
                }
              }
            }
          }
        }

        &.contact-box {
          justify-content: flex-start;
          margin-top: 12px;
          color: #666666;
          line-height: 17px;

          .apply-contact {
            display: flex;
            align-items: center;

            .to-chat {
              color: $styleLink;
              margin: 0 3px;
              cursor: pointer;
            }
          }
        }

        &.time-box {
          .apply-time {
            display: flex;
            align-items: center;

            .time-line {
              margin: 0 4px;
              width: 8px;
              overflow: hidden;
              display: flex;
              justify-content: center;
              align-items: center;
            }

            .show-text {
              width: 93px;
              height: 30px;
              border-radius: 4px;
              color: #999999;

              &.sel {
                color: #000000;
              }

              &.disabled {
                background: $styleBg1Hover;
              }
            }

            .apply-time-sel {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-left: 12px;
              color: #333333;
              cursor: pointer;

              .sel-box-i {
                flex-shrink: 0;
                width: 14px;
                height: 14px;
                position: relative;
                border: 1px solid #DDDDDD;
                border-radius: 2px;
                margin-right: 4px;

                &.sel {
                  border: 1px solid $styleColor;
                  background: $styleColor;

                  &:after {
                    content: "";
                    width: 8px;
                    height: 3px;
                    border: 2px solid #FFFFFF;
                    border-top: transparent;
                    border-right: transparent;
                    position: absolute;
                    top: 2px;
                    left: 1px;
                    transform: rotate(-45deg);
                  }
                }
              }

            }
          }
        }

        &.area-box {
          justify-content: flex-start;
          align-items: flex-start;
          position: relative;

          textarea {
            padding: 7px 10px;
            min-height: 82px;
            border: 1px solid #ccc;
            border-radius: 4px;
            width: 100%;
            resize: none;
          }

          .area-num {
            color: #999999;
            line-height: 17px;
            position: absolute;
            bottom: 4px;
            right: 4px;
          }
        }

        &.intr-box {
          justify-content: flex-start;
          align-items: flex-start;

          label {
            height: 17px;
            line-height: 17px;
          }

          .apply-intr {
            color: #333333;
            line-height: 17px;
          }
        }
      }
    }
  }
}
</style>