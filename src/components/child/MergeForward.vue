<template>
  <div class="merge-forward child-win-box">
    <div class="child-win-nav win-drag">合并转发</div>
    <div class="merge-forward-back" v-if="historyList.length>1">
      <span class="back" @click="loadMsg(-1)">返回</span>
    </div>
    <div :class="['child-win-content',historyList.length>1?'child-win-content-back':'']">
      <ChatMsg v-if="msgList.length>0" :msgList="msgList" :sessionInfo="sessionInfo" msgType="5"></ChatMsg>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted} from "vue";
import {useStore} from "vuex";
import {useRouter} from 'vue-router';
import {findMsgByIdApi} from "@utils/net/api";
import {convertMsg} from "@utils"
import ChatMsg from "@comp/chat/ChatMsg";
import {alert, toast, loading} from "@comp/ui";

export default {
  name: "MergeForward",
  components: {ChatMsg},
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    // 消息列表
    let msgList = ref([]);
    // 当前会话
    let sessionInfo = ref({});
    // 打开的消息记录数组列表
    let historyList = ref([router.currentRoute.value.query]);

    watch(() => store.state.emit.loadForwardMsg,
      (newValue, oldValue) => {
        if (newValue) {
          let param = {};
          newValue.replace(/([^?&]+)=([^?&]+)/g, function (s, v, k) {
            param[v] = decodeURIComponent(k);
            return k + '=' + v;
          });
          loadMsg(1, param);
        }
      }, {
        deep: true
      }
    );

    loadMsg(0, historyList.value[0]);

    // 加载合并转发消息
    function loadMsg(type, param) {
      if (type == -1) {
        historyList.value.pop();
        param = historyList.value[historyList.value.length - 1];
      } else if (type == 1) {
        historyList.value.push(param);
      }
      findMsgById(param);
    }

    // 获取消息列表
    function findMsgById(param) {
      loading();
      msgList.value = [];
      findMsgByIdApi({
        msgIds: JSON.stringify((param.msgIds || "").split(",")),
        msgTimeList: JSON.stringify((param.msgTimes || "").split(",")),
        sessionId: param.sessionId,
        sessionType: param.sessionType
      }).then(res => {
        if (res.data && res.data.length > 0) {
          let thisMsgList = convertMsg(res.data);
          let p = [];
          thisMsgList.map(item => {
            p.push(store.dispatch("setMsgField", {item: item}));
          });
          Promise.all(p).then(res => {
            remote.store.dispatch("getPersons", {doneFlag: true, account: thisMsgList.map(item => {return item.from})}).then(personInfo => {
              msgList.value = thisMsgList;
              let sessionParam = {scene: msgList.value[0].scene, to: msgList.value[0].to, id: msgList.value[0].scene + "-" + msgList.value[0].to};
              if (msgList.value[0].scene == "p2p") {
                sessionParam.detailInfo = {workerName: remote.store.getters.getPersons(msgList.value[0].to).name}
              }
              sessionInfo.value = sessionParam;
              loading().hide();
            })
          });
        } else {
          loading().hide();
        }
      });
    }

    return {
      msgList,
      sessionInfo,
      historyList,

      loadMsg,
    }
  }
}
</script>
<style scoped lang="scss">
.merge-forward {
  background: $styleBg1Hover !important;

  .merge-forward-back {
    height: 36px;
    line-height: 36px;
    border-top: 1px solid #E7E7E7;
    border-bottom: 1px solid #E7E7E7;
    display: flex;
    font-size: 14px;

    .back {
      color: #4A90E2;
      padding: 0 10px 0 24px;
      cursor: pointer;
      position: relative;

      &:after {
        content: "";
        position: absolute;
        top: 50%;
        left: 10px;
        height: 8px;
        width: 8px;
        transform: rotate(45deg) translateY(-50%);
        border-width: 0 0 1px 1px;
        border-color: #4A90E2;
        border-style: solid;
      }
    }
  }

  .child-win-content-back {
    height: calc(100% - 36px - 36px);
  }
}
</style>