<template>
  <div class="collect child-win-box">
    <div class="child-win-nav win-drag">{{ sessionInfo.to ? "话术库" : "我的收藏" }}</div>
    <div class="child-win-content">
      <!--顶部操作-->
      <div class="collect-header">
        <div class="header-search-box" @click="showComponents({type:1,key:1})">请输入关键字搜索</div>
        <div class="header-btn-box">
          <span class="btn btn-note" @click="showComponents({type:2,key:1,editItem:{parentId:collectObj.groupId},editType:2,detailType:1})" v-show="collectView.key==2">笔记</span>
          <span class="btn btn-refresh" @click="resetData({page:1,groupId:collectObj.groupId,type:collectObj.type,init:true})">刷新</span>
        </div>
      </div>
      <!--内容-->
      <div class="collect-content">
        <!--收藏列表-->
        <ul class="collect-content-left">
          <li v-if="collectView.pur" :class="['left-li',collectView.key==0?'sel':'']" @click="changeKey(1,0)">公共话术</li>
          <li v-if="collectView.pur" :class="['left-li',collectView.key==1?'sel':'']" @click="changeKey(1,1)">我的话术</li>
          <li :class="['left-li',collectView.key==2?'sel':'']" @click="changeKey(1,2)">全部收藏</li>
        </ul>
        <div class="collect-content-right">
          <!--分组-->
          <div :class="['collect-classify-box',collectView.isClassifyShow?'show':'']" :style="{'height':collectView.classifyHeight+'px'}">
            <div class="classify-content">
              <span class="classify-intr">分类：</span>
              <div class="classify-ul-box" ref="classifyUlBoxRef">
                <ul class="classify-ul" ref="classifyUlRef">
                  <li :class="['classify',(collectView.key==2&&String(item.id)==String(collectObj.groupId))||(collectView.key!=2&&String(item.id)==String(collectStoryObj.gid))?'sel':'']"
                      v-for="(item,key) in collectView.groupList" :key="item.id" @click="changeKey(3,item.id)">{{ item.name }}({{ item.speechNum || item.amount || 0 }})
                  </li>
                  <li v-show="collectView.key!=0" class="classify classify-add" @click="showDialog(dialogObj.type,2)">+新建分组</li>
                </ul>
              </div>
            </div>
            <div class="classify-btn-box">
              <span v-show="collectView.key!=0" class="btn" @click="showDialog(1)">管理</span>
              <span v-show="collectView.isClassifyMore" :class="['btn','btn-more',collectView.isClassifyShow?'show':'']" @click="toggleClassify()">{{ collectView.isClassifyShow ? "收起" : "展开" }}</span>
            </div>
          </div>
          <!--类型-->
          <ul class="collect-type-box">
            <li :class="['type-li',(collectView.key==2&&item.key==collectObj.type)||(collectView.key!=2&&item.key==collectStoryObj.showType)?'sel':'']"
                v-for="(item,key) in collectView.typeList[collectView.key]" :key="key" @click="changeKey(2,item.key)">{{ item.name }}
            </li>
          </ul>
          <!--收藏详情-->
          <div class="collect-details-box" :style="{'height':'calc(100% - '+collectView.classifyHeight+'px - 30px)'}">
            <div :class="['collect-details-left',collectView.selCollectCount>0?'sel-more':'']">
              <CollectMsg type="1" :collectType="collectView.key" :collectStatus="collectView.loading" :collectList="collectView.collectList" :collectClassify="collectView.groupList"
                          :showMsgItem="showMsgItem" :removeContent="removeContent" :moveCollectContent="moveCollectContent" :selMore="true" :changeSelCollectList="changeSelCollectList"
                          :selCollectMap="collectView.selCollectMap" :loadMore="loadMore" :sessionInfo="sessionInfo" ref="collectMsgRef"
              ></CollectMsg>
            </div>
            <div v-if="collectView.key==2&&collectView.msgList.length>0" class="collect-details-right">
              <div class="collect-details-msg selAll">
                <ChatMsg :searchList="collectView.msgList" msgType="6"></ChatMsg>
              </div>
              <div class="collect-details-btn-box">
                <span class="btn" @click="removeContent(collectView.msgList)">删除</span>
                <span v-show="isShowEdit()" class="btn" @click="showComponents({type:2,key:1,editItem:collectView.msgList[0]})">编辑</span>
                <span class="btn" @click="forwardItem(0)">发送</span>
              </div>
            </div>
            <div v-if="collectView.selCollectCount>0" class="collect-details-sel-box">
              <div class="collect-details-sel-intr">
                <span>已选</span>
                <span class="highlight">{{ collectView.selCollectCount }}</span>
                <span>条内容</span>
              </div>
              <div class="collect-details-sel-btn-box">
                <span class="btn" @click="changeSelCollectList(2)">全选</span>
                <span class="btn" @click="changeSelCollectList(3)">取消勾选</span>
                <span v-show="collectView.key!=0" class="btn" @click="changeSelCollectList(4)">删除</span>
                <span v-show="collectView.key!=0" class="btn" @click="showDialog(2)">移动至</span>
                <span v-show="collectView.key!=0" class="btn" @click="saveItem(1)">另存为</span>
                <span class="btn" @click="forwardItem(1)">转发</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--管理分组弹窗-->
      <LyDialog newClass="dialog-manger-box" title="管理分组" :width="396" :height="400" :closeOnClickModal="true" :visible="dialogObj.type==1"
                @close="dialogOperate(1,1)" @confirm="dialogOperate(1,2)">
        <div class="main-dialog-box">
          <div class="add-classify-box">
            <span class="add-classify" @click="showDialog(dialogObj.type,2)">新建分组</span>
          </div>
          <ul class="sel-classify-ul">
            <li class="sel-classify-li" :class="{'move':item.id==dialogObj.moveItem.id,'move-status':dialogObj.moveItem.id}" v-for="(item,key) in dialogObj.moveList" :key="key"
                :draggable="true" @dragstart="classifyDrag($event,1,item)" @dragenter="classifyDrag($event,2,item)" @dragover="classifyDrag($event,3,item)" @dragend="classifyDrag($event,4,item)">
              <i v-if="item.id!=0" class="li-move-icon"></i>
              <span class="li-name textEls">{{ item.name }}</span>
              <i v-if="item.id!=0" class="li-edit-icon" @click="showDialog(dialogObj.type,1,item)"></i>
              <img v-if="item.id!=0" class="li-close-icon" src="/img/workbench/icon_close.png" @click="removeClassify(item)">
            </li>
          </ul>
        </div>
      </LyDialog>
      <!--移动至分组弹窗-->
      <LyDialog newClass="dialog-manger-box dialog-move-box" title="移动至" :width="396" :height="400" :closeOnClickModal="true" :visible="dialogObj.type==2"
                @close="dialogOperate(4,1)" @confirm="dialogOperate(4,2)">
        <div class="main-dialog-box">
          <div class="move-content-box">
            <ul v-if="dialogObj.moveFlag" class="ly-dialog-default-tab">
              <li v-if="collectView.key==2" :class="{'sel':dialogObj.moveKey==2}" @click="changeKey(4,2)">全部收藏</li>
              <li v-if="collectView.pur" :class="{'sel':dialogObj.moveKey==1}" @click="changeKey(4,1)">我的话术</li>
            </ul>
            <div v-else class="move-content-tips">内容包含语音/文件/链接/笔记/视频，只能批量移动到收藏分组</div>
            <div class="add-classify-box">
              <span class="add-classify" @click="showDialog(dialogObj.type,2)">新建分组</span>
            </div>
            <ul class="move-content-ul">
              <li v-for="(item,key) in dialogObj.moveList" :key="key" @click="dialogObj.moveSelItem=item">
                <span :class="['sel-box',String(dialogObj.moveSelItem.id)==String(item.id)?'sel':'']"></span>
                <span class="textEls">{{ item.name }}</span>
              </li>
            </ul>
          </div>
        </div>
      </LyDialog>
      <!--新建/重命名分组弹窗-->
      <LyDialog :title="dialogObj.type1==1?'重命名分组':'新建分组'" :width="383" :height="146" :closeOnClickModal="true" :visible="dialogObj.type1==1||dialogObj.type1==2"
                @close="dialogOperate(dialogObj.type1==2?2:3,1)" @confirm="dialogOperate(dialogObj.type1==2?2:3,2)">
        <div class="main-dialog-box">
          <div class="ly-dialog-default-box">
            <label class="ly-dialog-default-label">
              <span class="ly-dialog-default-tips">*</span>
              <span>分组名称：</span>
            </label>
            <div class="ly-dialog-default-detail">
              <input ref="lyDialogInputRef" class="ly-dialog-default-input" v-model.trim="dialogObj.classifyName" maxlength="30" placeholder="最多输入30字"/>
            </div>
          </div>
        </div>
      </LyDialog>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import {useRouter} from "vue-router";
import ChatMsg from "@comp/chat/ChatMsg";
import CollectMsg from "@comp/chat/CollectMsg";
import LyDialog from "@comp/ui/comps/LyDialog";
import {alert, loading, toast} from "@comp/ui";
import {convertCollectMsg, deepClone, debounce, elmToMsg, forwardCollectMsgs, getChildWin, dealFileField, setJJSEvent} from "@utils"
import {
  queryCollectContentApi, addCollectClassifyApi, editCollectClassifyApi, delCollectClassifyApi, moveCollectContentApi, removeCollectContentApi, sortCollectClassifyApi,
  toggleCollectContentApi, queryCollectStoryContentApi, editCollectStoryClassifyApi, delCollectStoryClassifyApi, sortCollectStoryClassifyApi, removeCollectStoryContentApi,
  moveCollectStoryContentApi
} from "@utils/net/api.js";

export default {
  name: "Collect",
  components: {ChatMsg, CollectMsg, LyDialog},
  setup(props, ctx) {
    const store = useStore();
    const router = useRouter();
    let config = store.getters.getConfig.config;
    let userInfo = store.getters.getUserInfo;
    let sessionInfo = ref({
      scene: router.currentRoute.value.query.scene,
      to: router.currentRoute.value.query.to,
      id: `${router.currentRoute.value.query.scene}-${router.currentRoute.value.query.to}`,
      name: router.currentRoute.value.query.userName,
      close: router.currentRoute.value.query.to ? true : false
    });
    // 分组元素
    let classifyUlBoxRef = ref();
    let classifyUlRef = ref();
    // 分组输入框元素
    let lyDialogInputRef = ref();
    // 收藏列表元素
    let collectMsgRef = ref();
    // 收藏内容
    let collectView = ref({
      key: -1,// 0公共话术-1我的话术-2全部收藏
      typeList: [
        [{name: "全部", key: ""}],
        [{name: "全部", key: ""}, {name: "图片", key: "1"}],
        [{name: "全部", key: ""}, {name: "图片", key: "image"}, {name: "语音", key: "audio"}, {name: "视频", key: "video"}, {name: "链接", key: "link"}, {name: "文件", key: "file"}, {name: "笔记", key: "note"}, {name: "文字", key: "text"}],
      ],
      groupList: [],// 显示分组列表
      groupOtherList: [],// 移动分组另外分组
      loading: false,// 加载状态
      originCollectList: [],// 收藏列表原始数据
      collectList: [],// 收藏列表
      selAllMap: {},// 选择列表缓存
      selCollectMap: {},// 选择的收藏列表
      selCollectCount: 0,// 选择的收藏列表数量
      msgList: [],// 消息列表
      isClassifyShow: false,// 是否显示3行分组标签
      isClassifyMore: false,// 是否显示更多分组按钮
      classifyHeight: 26,// 分组高度
      searchFlag: false,// 显示搜索组件
      editFlag: false,// 显示编辑组件
      editItem: {},// 编辑的消息体
      hasMore: false,// 是否还能加载更多
      pur: false,// 是否有话术权限
    });
    // 话术参数
    let collectStoryObj = ref({
      type: 0,// 0全部1公共2私有
      gid: "",// 分组id
      pageNumber: 1,// 查询页数
      pageSize: 20,// 查询页面数量
      keyWord: "",// 搜索关键词
      showType: "",// 1图片
    });
    // 收藏参数
    let collectObj = ref({
      empNo: userInfo.workerNo,// 当前登录人工号
      page: 1,// 查询页数
      rows: 20,// 查询页面数量
      groupId: "",// 分组id
      type: "",// 查询类型
      name: "",// 搜索关键词
    });
    // 弹窗参数
    let dialogObj = ref({
      type: -1,// 1管理分组,2移动至分组
      type1: -1,// 1重命名,2新建分组
      classifyName: "",// 新建分组名
      selItem: {},// 选择的对象
      moveFlag: true,// 是否能移动到我的话术
      moveContentList: [],// 移动的数据列表
      moveKey: 2,// 移动至分组2我的收藏1我的话术
      moveList: [],// 显示的分组列表
      moveSelItem: {},// 移动至选择的对象
      moveItem: {},// 移动分组对象
      tempKey: "",// 新增分组当前的类型
    });

    initCollect();

    // 初始化收藏
    async function initCollect() {
      loading();
      collectView.value.pur = await store.dispatch("getCollectStoryPur");
      loading().hide();
      if (collectView.value.pur && sessionInfo.value.to) {
        changeKey(1, 0);
      } else {
        changeKey(1, 2);
      }
      setJJSEvent("P66153472", JSON.stringify({
        type: sessionInfo.value.to ? "快捷回复话术库" : "乐聊收藏",
        workerId: userInfo.workerId,
        recommendID: userInfo.deptNumber
      }));
    }

    // 加载更多0公共话术1我的话术2我的收藏
    function loadMore(type) {
      if (collectView.value.hasMore) {
        switch (type) {
          case 0:
          case 1:
            collectStoryObj.value.pageNumber++;
            queryCollectStoryContent();
            break;
          case 2:
            collectObj.value.page++;
            getCollectList();
            break;
        }
      }
    }

    // 初始化数据
    function resetData(param = {}) {
      collectView.value.collectList = [];
      collectView.value.originCollectList = [];
      collectView.value.loading = true;
      switch (collectView.value.key) {
        case 0:
        case 1:
          collectStoryObj.value.showType = param.type || "";
          collectStoryObj.value.pageNumber = param.page || 1;
          collectStoryObj.value.gid = param.groupId;
          if (collectView.value.key == 0) {
            collectStoryObj.value.type = 1;
          } else {
            collectStoryObj.value.type = 2;
          }
          if (param.init) {
            // 获取公共话术分组
            queryCollectStoryClassify(collectView.value.key == 0 ? 1 : 2);
          } else {
            // 获取公共话术列表
            queryCollectStoryContent();
          }
          break;
        case 2:
          collectObj.value.page = param.page || 1;
          collectObj.value.groupId = param.groupId;
          collectObj.value.type = param.type;
          if (param.init) {
            // 获取收藏分组
            getCollectClassify();
          } else {
            // 获取收藏列表
            getCollectList();
          }
          break;
      }
    }

    // 处理分组列表
    function dealClassifyList(type, list) {
      // 全部分组内容数量
      let num = 0;
      let thisList = [];
      if (type == 2) {
        // 我的收藏
        list.map(item => {
          num += Number(item.amount);
          thisList.push(item);
        });
        thisList = [{id: "", name: "全部", amount: num}].concat(thisList);
      } else {
        // 公共/我的话术
        list.map(item => {
          // 设置通用字段
          num += Number(item.speechNum);
          item.id = item.gid;
          item.name = item.gname;
          thisList.push(item);
        });
        thisList = [{id: "", name: "全部", gid: "", gname: "全部", speechNum: num}].concat(thisList);
      }
      return thisList;
    }

    // 获取收藏分组
    async function getCollectClassify() {
      let res = await store.dispatch("getCollectClassify");
      if (res.success) {
        collectView.value.groupList = dealClassifyList(2, res.data.data);
        resetData({page: 1, groupId: collectView.value.groupList[0].id, type: ""});
        getClassifyMore();
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 获取收藏列表数据
    async function getCollectList() {
      if (collectObj.value.page == 1) {
        collectView.value.loading = true;
      }
      loading();
      let res = await queryCollectContentApi({
        msgBody: JSON.stringify(collectObj.value),
      });
      if (res.success) {
        try {
          if (collectObj.value.page < res.data.pages) {
            collectView.value.hasMore = true;
          } else {
            collectView.value.hasMore = false;
          }
          dealCollectList(2, res.data.data);
        } catch (e) {
          loading().hide();
          collectView.value.loading = false;
        }
      } else {
        loading().hide();
        collectView.value.loading = false;
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 查询话术分组-type0全部1公共2私有
    async function queryCollectStoryClassify(type) {
      let res = await store.dispatch("getCollectStoryClassify", type);
      if (res.success) {
        collectView.value.groupList = dealClassifyList(type == 2 ? 1 : 0, res.data.data);
        resetData({page: 1, groupId: collectView.value.groupList[0]?.id, type: ""});
        getClassifyMore();
      } else {
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 查询话术分组内容
    async function queryCollectStoryContent() {
      if (collectStoryObj.value.pageNumber == 1) {
        collectView.value.loading = true;
      }
      loading();
      let res = await queryCollectStoryContentApi({
        msgBody: JSON.stringify(collectStoryObj.value),
      });
      if (res.success) {
        try {
          if (collectStoryObj.value.pageNumber < res.data.data.pages) {
            collectView.value.hasMore = true;
          } else {
            collectView.value.hasMore = false;
          }
          dealCollectList(1, res.data.data.list);
        } catch (e) {
          loading().hide();
          collectView.value.loading = false;
        }
      } else {
        loading().hide();
        collectView.value.loading = false;
        toast({title: res.errorMsg || "系统错误", type: 2});
      }
    }

    // 处理收藏列表数据-type-1公共/我的话术-2我的收藏
    function dealCollectList(type, msgList, insertIndex) {
      if (type == 1) {
        insertCollectList(1, insertIndex, msgList);
        if (collectStoryObj.value.type == 1) {
          // 公共话术列表
          let thisMsgList = msgList.map(item => {
            return {collectId: item.id, ...item}
          });
          loading().hide();
          insertCollectList(2, insertIndex, thisMsgList);
          collectView.value.loading = false;
        } else {
          // 我的话术列表
          let thisMsgList = msgList.map(item => {
            return {collectId: item.id, parentId: item.parentId, from: userInfo.workerNo, type: "custom", content: {type: "multi", msgs: item.speechContent}}
          });
          let p = [];
          thisMsgList.map(item => {
            p.push(store.dispatch("setMsgField", {item: item}));
          });
          Promise.all(p).then(res => {
            remote.store.dispatch("getPersons", {doneFlag: true, account: thisMsgList.map(item => {return item.from})}).then(personInfo => {
              loading().hide();
              insertCollectList(2, insertIndex, thisMsgList);
              collectView.value.loading = false;
            })
          });
        }
      } else if (type == 2) {
        insertCollectList(1, insertIndex, msgList);
        let thisMsgList = convertCollectMsg(msgList);
        let p = [];
        thisMsgList.map(item => {
          p.push(store.dispatch("setMsgField", {item: item}));
        });
        Promise.all(p).then(res => {
          remote.store.dispatch("getPersons", {doneFlag: true, account: thisMsgList.map(item => {return item.from})}).then(personInfo => {
            loading().hide();
            insertCollectList(2, insertIndex, thisMsgList);
            collectView.value.loading = false;
            // 默认打开第一个
            if (collectObj.value.page == 1 && collectView.value.collectList[0]) {
              nextTick(() => {
                collectMsgRef.value.showMsg(collectView.value.collectList[0]);
              });
            }
          })
        });
      }
    }

    // 插入收藏列表 type-1插入原数据列表-2插入显示数据列表，insertIndex插入下标，insertList插入列表
    function insertCollectList(type, insertIndex, insertList) {
      if (insertIndex == -1) {
        // 新增
        if (type == 1) {
          collectView.value.originCollectList = insertList.concat(collectView.value.originCollectList);
        } else {
          collectView.value.collectList = insertList.concat(collectView.value.collectList);
          changeClassifyNum(insertList[0].parentId != null ? insertList[0].parentId : insertList[0].groupId, insertList.length);
        }
      } else if (!isNaN(insertIndex) && insertIndex > -1) {
        // 替换-替换后目前逻辑是需要排到最前面
        if (type == 1) {
          collectView.value.originCollectList.splice(insertIndex, 1);
          collectView.value.originCollectList = insertList.concat(collectView.value.originCollectList);
          // collectView.value.originCollectList.splice(insertIndex, 1, ...insertList);
        } else {
          collectView.value.collectList.splice(insertIndex, 1);
          collectView.value.collectList = insertList.concat(collectView.value.collectList);
          // collectView.value.collectList.splice(insertIndex, 1, ...insertList);
          // 替换显示的收藏内容
          if (insertList[0] && collectView.value.msgList[0] && insertList[0].collectId == collectView.value.msgList[0].collectId) {
            collectView.value.msgList[0] = insertList[0];
          }
        }
      } else {
        // 加载更多
        if (type == 1) {
          collectView.value.originCollectList = collectView.value.originCollectList.concat(insertList);
        } else {
          collectView.value.collectList = collectView.value.collectList.concat(insertList);
        }
      }
    }

    // 切换显示 type-1切换左侧tab-2切换分组-3切换类型-4切换移动至分组
    function changeKey(type, key) {
      collectView.value.msgList = [];
      if (type == 1) {
        // 切换左侧tab
        collectView.value.key = key;
        collectView.value.groupId = "";
        collectView.value.groupList = [];
        collectView.value.isClassifyShow = false;
        collectView.value.classifyHeight = 26;
        resetData({page: 1, groupId: "", type: "", init: true});
        changeSelCollectList(5);
        setJJSEvent("P25901824", JSON.stringify({
          type: key == 0 ? "公共话术" : key == 1 ? "我的话术" : "全部收藏",
          workerId: userInfo.workerId,
          recommendID: userInfo.deptNumber
        }));
      } else if (type == 2) {
        // 切换分组
        resetData({groupId: collectView.value.key == 2 ? collectObj.value.groupId : collectStoryObj.value.gid, type: key});
      } else if (type == 3) {
        // 切换类型
        resetData({groupId: key, type: collectView.value.key == 2 ? collectObj.value.type : collectStoryObj.value.showType});
      } else if (type == 4) {
        dialogObj.value.tempKey = key;
        dialogObj.value.moveKey = key;
        dialogObj.value.moveSelItem = {};
        if (key == collectView.value.key) {
          dialogObj.value.moveList = deepClone(collectView.value.groupList);
        } else {
          dialogObj.value.moveList = deepClone(collectView.value.groupOtherList);
        }
        if (dialogObj.value.moveList[0].id === "") {
          dialogObj.value.moveList.splice(0, 1);
        }
      }
    }

    // 当前窗口拖拽缩放
    watch(() => store.state.emit.resize,
      (newValue, oldValue) => {
        debounce({
          timerName: "getClassifyMore",
          time: 500,
          fnName: getClassifyMore
        })
      }, {
        deep: true
      }
    );

    // 更新我的话术/收藏数据
    watch(() => store.state.emit.saveCollectItem,
      (newValue, oldValue) => {
        if (newValue) {
          let collectType = collectView.value.key;
          if (collectType == 1 || collectType == 2) {
            let thisItem = deepClone(newValue);
            let collectIndex = collectView.value.collectList.findIndex(item => {return thisItem.id == item.collectId})
            if (collectType == 2 && collectIndex > -1) {
              // 我的收藏编辑字段不完整
              thisItem = deepClone(collectView.value.originCollectList[collectIndex]);
              thisItem.oHtml = newValue.oHtml;
              thisItem.oText = newValue.oText;
              thisItem.title = newValue.title;
              thisItem.custom = newValue.custom;
              thisItem.content = newValue.content;
              if (newValue.type == "note" && thisItem.type != newValue.type) {
                delete thisItem.msgJson;
              }
              thisItem.type = newValue.type;
              if (thisItem.type == "text") {
                delete thisItem.msgJson;
                delete collectView.value.originCollectList[collectIndex].msgJson;
              }
            }
            dealCollectList(collectType, [thisItem], collectIndex);
          }
        }
      }, {
        deep: true
      }
    );

    // 判断是否显示分组更多按钮
    function getClassifyMore() {
      nextTick(() => {
        // 存在滚动范围显示展开按钮
        if (classifyUlRef.value.clientHeight > classifyUlBoxRef.value.clientHeight) {
          collectView.value.isClassifyMore = true;
        } else {
          collectView.value.isClassifyMore = false;
        }
      });
    }

    // 切换显示更多分组
    function toggleClassify() {
      collectView.value.isClassifyShow = !collectView.value.isClassifyShow;
      calcClassifyHeight();
    }

    // 计算分组高度
    function calcClassifyHeight() {
      nextTick(() => {
        // 展开
        if (collectView.value.isClassifyShow) {
          // 获取分组高度 最多显示3个加半个
          if (classifyUlRef.value.clientHeight + 6 > 26 * 3 + 10) {
            collectView.value.classifyHeight = 26 * 3 + 10;
          } else {
            if (classifyUlRef.value.clientHeight == 26) {
              // 收起
              collectView.value.classifyHeight = 26;
              getClassifyMore();
            } else {
              collectView.value.classifyHeight = classifyUlRef.value.clientHeight + 6;
            }
          }
        } else {
          // 收起
          collectView.value.classifyHeight = 26;
          getClassifyMore();
        }
      });
    }

    // 显示消息详情
    async function showMsgItem(item) {
      if (item) {
        loading();
        let res = await forwardCollectMsgs([item], 2);
        loading().hide();
        collectView.value.msgList = [res.thisMsg];
      } else {
        collectView.value.msgList = [];
      }
    }

    // 显示弹窗-type-1管理分组2移动至分组 type1-1重命名,2新建分组 item-编辑的分组
    async function showDialog(type, type1, item) {
      if (type == 2) {
        // 获取分组列表
        loading();
        let p = [store.dispatch("getCollectStoryClassify", 2), store.dispatch("getCollectClassify")];
        let resList = await Promise.all(p);
        loading().hide();
        let collectStoryClassifyList = [];
        let collectClassifyList = [];
        for (let i = 0; i < resList.length; i++) {
          let thisRes = resList[i];
          if (!thisRes.success) {
            toast({title: thisRes.errorMsg || "系统错误", type: 2});
            return;
          }
          // 设置返回数据
          if (i == 0) {
            collectStoryClassifyList = dealClassifyList(1, thisRes.data.data);
          } else {
            collectClassifyList = dealClassifyList(2, thisRes.data.data);
          }
        }
        // 设置列表
        if (collectView.value.key == 1) {
          collectView.value.groupList = collectStoryClassifyList;
          collectView.value.groupOtherList = collectClassifyList;
        } else {
          collectView.value.groupList = collectClassifyList;
          collectView.value.groupOtherList = collectStoryClassifyList;
        }
      }
      dialogObj.value.classifyName = "";
      if (type == 1) {
        dialogObj.value.moveList = deepClone(collectView.value.groupList);
        if (dialogObj.value.moveList[0] === "") {
          dialogObj.value.moveList.splice(0, 1);
        }
      } else if (type == 2) {
        // 恢复移动至默认状态
        dialogObj.value.moveFlag = true;
        loading();
        let forwardRes = await forwardCollectMsgs(Object.values(collectView.value.selCollectMap), 2);
        loading().hide();
        if (!forwardRes.success) {
          toast({title: forwardRes.errorMsg, type: 2});
          loading().hide();
          return;
        }
        dialogObj.value.moveContentList = forwardRes.thisMsgs;
        // 文字、图片、图文才支持收藏到话术
        for (let i = 0; i < dialogObj.value.moveContentList.length; i++) {
          let thisItem = dialogObj.value.moveContentList[i];
          if (thisItem.type == "text" || thisItem.type == "image" || (thisItem.type == "custom" && thisItem.content && thisItem.content.type == "multi")) {
            if (thisItem.type == "custom") {
              // 只要图文才能收藏
              thisItem.content.msgs.map(item1 => {
                if (item1.type != "text" && item1.type != "image") {
                  dialogObj.value.moveFlag = false;
                }
              });
            }
          } else {
            dialogObj.value.moveFlag = false;
            break;
          }
        }
        if (dialogObj.value.type != type) {
          changeKey(4, collectView.value.key);
        }
      }
      dialogObj.value.type = type;
      dialogObj.value.type1 = type1;
      if (type1 == 1 || type1 == 2) {
        if (type1 == 1) {
          dialogObj.value.classifyName = item.name;
          dialogObj.value.selItem = item;
        }
        nextTick(() => {
          // 聚焦到输入框
          lyDialogInputRef.value.focus();
        });
      }
    }

    // 弹窗操作-type1管理分组2新建分组3重命名分组4移动至-key1关闭2确认
    async function dialogOperate(type, key) {
      switch (type) {
        // 管理分组
        case 1:
          if (key == 1) {
            dialogObj.value.type = -1;
          } else if (key == 2) {
            let sortParam = [];
            for (let i = 0; i < dialogObj.value.moveList.length; i++) {
              // 去除默认分组
              if (dialogObj.value.moveList[i].id) {
                if (collectView.value.key == 1) {
                  // 我的话术分组排序
                  sortParam.push({
                    groupId: dialogObj.value.moveList[i].id,
                    sortNum: i
                  });
                } else if (collectView.value.key == 2) {
                  // 我的收藏分组排序
                  sortParam.push({
                    appId: dialogObj.value.moveList[i].id,
                    newSortNum: i
                  });
                }
              }
            }
            let res = {};
            loading();
            if (collectView.value.key == 1) {
              // 我的话术分组排序
              res = await sortCollectStoryClassifyApi({
                msgBody: JSON.stringify({
                  sortList: sortParam
                })
              });
            } else if (collectView.value.key == 2) {
              // 我的收藏分组排序
              res = await sortCollectClassifyApi({
                msgBody: JSON.stringify(sortParam)
              });
            } else {
              loading().hide();
              return;
            }
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            // 分组排序
            collectView.value.groupList = deepClone(dialogObj.value.moveList);
            dialogObj.value.type = -1;
            calcClassifyHeight();
            toast({title: "分组排序成功", type: 1});
          }
          break;
        // 新建分组
        case 2:
          let tempKey = dialogObj.value.tempKey || collectView.value.key;
          if (key == 1) {
            dialogObj.value.type1 = -1;
          } else if (key == 2) {
            if (!dialogObj.value.classifyName) {
              toast({title: "请输入分组名称", type: 2});
              return;
            }
            let res = {};
            let thisItem = {};
            loading();
            if (tempKey == 1) {
              // 添加我的话术分组
              res = await editCollectStoryClassifyApi({
                msgBody: JSON.stringify({
                  groupName: dialogObj.value.classifyName
                })
              });
            } else if (tempKey == 2) {
              // 添加我的收藏分组
              res = await addCollectClassifyApi({
                msgBody: JSON.stringify({
                  empNo: userInfo.workerNo,
                  name: dialogObj.value.classifyName
                })
              });
            } else {
              loading().hide();
              return;
            }
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            if (tempKey == 1) {
              thisItem = {id: res.data.data, name: dialogObj.value.classifyName, speechNum: "0"};
            } else if (tempKey == 2) {
              thisItem = res.data.data;
            }
            // 新增分组数据添加到列表
            if (tempKey == collectView.value.key) {
              collectView.value.groupList.push(thisItem);
            }
            dialogObj.value.moveList.push(thisItem);
            dialogObj.value.type1 = -1;
            dialogObj.value.tempKey = "";
            calcClassifyHeight();
            toast({title: "新建分组成功", type: 1});
          }
          break;
        // 重命名分组
        case 3:
          if (key == 1) {
            dialogObj.value.type1 = -1;
          } else if (key == 2) {
            if (!dialogObj.value.classifyName) {
              toast({title: "请输入分组名称", type: 2});
              return;
            }
            let res = {};
            loading();
            // 重命名分组
            if (collectView.value.key == 1) {
              res = await editCollectStoryClassifyApi({
                msgBody: JSON.stringify({
                  groupId: dialogObj.value.selItem.id,
                  groupName: dialogObj.value.classifyName
                })
              });
            } else if (collectView.value.key == 2) {
              res = await editCollectClassifyApi({
                msgBody: JSON.stringify({
                  groupId: dialogObj.value.selItem.id,
                  name: dialogObj.value.classifyName
                })
              });
            } else {
              loading().hide();
              return;
            }
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            dialogObj.value.selItem.name = dialogObj.value.classifyName;
            // 重命名分组
            for (let i = 0; i < collectView.value.groupList.length; i++) {
              if (collectView.value.groupList[i].id == dialogObj.value.selItem.id) {
                collectView.value.groupList[i].name = dialogObj.value.classifyName;
                dialogObj.value.moveList[i].name = dialogObj.value.classifyName;
                break;
              }
            }
            dialogObj.value.type1 = -1;
            toast({title: "重命名分组成功", type: 1});
          }
          break;
        case 4:
          // 移动至分组
          if (key == 1) {
            dialogObj.value.type = -1;
          } else if (key == 2) {
            // 收藏和话术互相移动
            if (dialogObj.value.tempKey && dialogObj.value.tempKey != collectView.value.key) {
              toggleCollectContent(Object.values(collectView.value.selCollectMap), dialogObj.value.moveSelItem.id);
              return;
            }
            // 批量移动
            dialogObj.value.tempKey = "";
            moveCollectContent(Object.values(collectView.value.selCollectMap), dialogObj.value.moveSelItem.id);
          }
          break;
      }
    }

    // 移除分组
    function removeClassify(item) {
      alert({
        title: "提示",
        content: `${item.name}分组将被删除，分组下的内容将移至"默认分组"，确定删除？`,
        done: async confirmType => {
          if (confirmType == 1) {
            loading();
            let res = {};
            let groupId = item.id;
            if (collectView.value.key == 1) {
              // 删除我的话术分组
              res = await delCollectStoryClassifyApi({
                msgBody: groupId
              });
            } else if (collectView.value.key == 2) {
              // 删除我的收藏分组
              res = await delCollectClassifyApi({
                msgBody: JSON.stringify({
                  empNo: userInfo.workerNo,
                  groupId: groupId
                })
              });
            } else {
              loading().hide();
              return;
            }
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            // 删除分组
            let collectViewIndex = collectView.value.groupList.findIndex(item1 => {return String(groupId) == String(item1.id)});
            if (collectViewIndex > -1) {
              collectView.value.groupList.splice(collectViewIndex, 1);
            }
            // 删除移动列表分组
            let moveListIndex = dialogObj.value.moveList.findIndex(item1 => {return String(groupId) == String(item1.id)});
            if (moveListIndex > -1) {
              dialogObj.value.moveList.splice(moveListIndex, 1);
            }
            let defaultGroupId = collectView.value.groupList[0].id;
            changeClassifyNum(defaultGroupId, item.speechNum || item.amount || 0);
            // 选中的分组是当前分组则选择第一个分组
            if ((collectView.value.key == 2 && String(collectObj.value.groupId) == String(groupId)) || (collectView.value.key != 2 && String(collectStoryObj.value.gid) == String(groupId))) {
              changeKey(3, defaultGroupId);
            }
            calcClassifyHeight();
            toast({title: "删除分组成功", type: 1});
          }
        }
      });
    }

    // 移除内容
    function removeContent(removeList) {
      alert({
        title: "提示",
        content: `该收藏内容将被删除，是否确定删除？`,
        done: async confirmType => {
          if (confirmType == 1) {
            let collectIds = removeList.map(item => {
              return item.collectId;
            });
            loading();
            let res = {};
            if (collectView.value.key == 1) {
              res = await removeCollectStoryContentApi({
                msgBody: JSON.stringify({
                  ids: collectIds
                })
              });
            } else if (collectView.value.key == 2) {
              res = await removeCollectContentApi({
                msgBody: JSON.stringify({
                  collectIds: collectIds.join(",")
                })
              });
            } else {
              loading().hide();
              return;
            }
            loading().hide();
            if (!res.success) {
              toast({title: res.errorMsg || "系统错误", type: 2});
              return;
            }
            removeList.map(item => {
              // 删除分组数量
              changeClassifyNum(item.parentId, -1);
            });
            // 删除内容
            collectMsgRef.value.showMsg();
            collectIds.map(item => {
              for (let i = 0; i < collectView.value.collectList.length; i++) {
                if (collectView.value.collectList[i].collectId == item) {
                  collectView.value.collectList.splice(i, 1);
                  break;
                }
              }
            });
            // 取消多选
            changeSelCollectList(3);
            toast({title: "删除成功", type: 1});
          }
        }
      });
    }

    // 批量移动内容到分组
    async function moveCollectContent(moveList, groupId) {
      if (groupId == null) {
        toast({title: "请选择移动至分组", type: 2});
        return;
      }
      let collectIds = [];
      let parentsIds = [];
      moveList.map(item => {
        collectIds.push(item.collectId);
        parentsIds.push(item.parentId);
      });
      loading();
      let res = {};
      if (collectView.value.key == 1) {
        res = await moveCollectStoryContentApi({
          msgBody: JSON.stringify({
            sourceGroupIds: collectIds,
            targetGroupId: groupId,
          })
        });
      } else if (collectView.value.key == 2) {
        res = await moveCollectContentApi({
          msgBody: JSON.stringify({
            collectIds: collectIds.join(","),
            groupId: groupId,
          })
        });
      } else {
        loading().hide();
        return;
      }

      loading().hide();
      if (!res.success) {
        toast({title: res.errorMsg || "系统错误", type: 2});
        return;
      }
      moveList.map(item => {
        let currGroupId = item.parentId;
        // 删除移动分组数量
        changeClassifyNum(currGroupId, -1);
        // 增加移动后分组数量
        changeClassifyNum(groupId, 1);
      });
      // 移动到当前分组重新获取
      let currId = collectView.value.key == 2 ? String(collectObj.value.groupId) : String(collectStoryObj.value.gid);
      if (String(groupId) == String(currId)) {
        changeKey(3, groupId);
      } else if (String(currId) != "") {
        removeCurrentList(collectIds);
      }
      // 关闭移动弹窗
      dialogObj.value.type = -1;
      // 取消多选
      changeSelCollectList(3);
      toast({title: "移动成功", type: 1});
    }

    // 收藏/话术内容批量互相移动
    async function toggleCollectContent(moveList, groupId) {
      let thisMsgs = dialogObj.value.moveContentList;
      let speechIdList = [];
      let collectIdList = [];
      let param = {}
      if (dialogObj.value.tempKey == 2) {
        // 话术移动到收藏-收藏需要消息id暂时不支持
        param.type = 1;
        param.speechIds = "";
        param.collectBatch = [];
        thisMsgs.map(item => {
          speechIdList.push(item.collectId);
          param.collectBatch.push({
            groupId: groupId,
            id: item.collectId || "",
            type: "custom",
            empNo: userInfo.workerNo,
            sourceType: 4,
            sourceName: userInfo.name,
            sourceIcon: userInfo.headPic,
            sourceId: userInfo.workerNo,
            sourceClientType: "pc",
            text: store.getters.getPrimaryMsg({msg: item, primaryType: 4, notChange: true}) || "",
            custom: JSON.stringify(item.content),
            content: JSON.stringify(item.content)
          });
        });
        param.speechIds = speechIdList.join(",");
      } else {
        // 收藏移动到话术
        param.type = 2;
        param.speechBatch = [];
        thisMsgs.map(item => {
          collectIdList.push(item.collectId);
          let thisContent = [];
          let thisItem = item;
          switch (item.type) {
            case "text":
              thisContent.push({type: "text", text: item.text});
              break;
            case "image":
              dealFileField(thisItem);
              thisContent.push({type: "image", file: thisItem.file});
              break;
            case "custom":
              for (let i = 0; i < item.content.msgs.length; i++) {
                thisItem = item.content.msgs[i];
                dealFileField(thisItem);
                thisContent.push(thisItem);
              }
              break;
          }
          param.speechBatch.push({categoryId: groupId, contentJson: thisContent});
        });
        param.colloecIds = collectIdList.join(",");
      }
      loading();
      let res = await toggleCollectContentApi({
        msgBody: JSON.stringify(param)
      })
      loading().hide();
      if (res.success) {
        moveList.map(item => {
          let currGroupId = item.parentId;
          // 删除移动分组数量
          changeClassifyNum(currGroupId, -1);
          // 增加移动后分组数量
          changeClassifyNum(groupId, 1);
        });
        removeCurrentList(collectIdList.length > 0 ? collectIdList : speechIdList);
        // 关闭移动弹窗
        dialogObj.value.type = -1;
        // 取消多选
        changeSelCollectList(3);
        toast({title: "移动成功", type: 1});
      }
    }

    // 移除当前分组数据
    function removeCurrentList(collectIds) {
      // 移除当前分组内容
      collectIds.map(item => {
        // 移除显示内容-当前在全部里面不移除
        if (collectView.value.msgList[0] && item == collectView.value.msgList[0].collectId) {
          collectMsgRef.value.showMsg();
        }
        for (let i = 0; i < collectView.value.collectList.length; i++) {
          if (collectView.value.collectList[i].collectId == item) {
            collectView.value.collectList.splice(i, 1);
            break;
          }
        }
      });
    }

    // 改变分组数量
    function changeClassifyNum(groupId, num) {
      num = parseInt(num);
      let groupListIndex = collectView.value.groupList.findIndex(item => {return String(item.id) == String(groupId)});
      if (groupListIndex > -1) {
        if (collectView.value.groupList[groupListIndex].speechNum != null) {
          collectView.value.groupList[groupListIndex].speechNum = Number(collectView.value.groupList[groupListIndex].speechNum || 0) + num;
          collectView.value.groupList[0].speechNum = Number(collectView.value.groupList[0].speechNum || 0) + num;
        } else if (collectView.value.groupList[groupListIndex].amount != null) {
          collectView.value.groupList[groupListIndex].amount = Number(collectView.value.groupList[groupListIndex].amount) + num;
          collectView.value.groupList[0].amount = Number(collectView.value.groupList[0].amount || 0) + num;
        }
      }
    }

    // 改变选择的内容列表
    function changeSelCollectList(type, item) {
      let thisCollectList = [];
      switch (type) {
        case 1:
          // 切换选择
          if (collectView.value.selCollectMap[item.collectId]) {
            // 取消选择
            item.sel = false;
            delete collectView.value.selCollectMap[item.collectId];
          } else {
            // 选择
            item.sel = true;
            collectView.value.selCollectMap[item.collectId] = item;
          }
          break;
        case 2:
          // 全选
          thisCollectList = deepClone(collectView.value.collectList);
          let thisCollectMap = {};
          if (collectView.value.key == 0) {
            // 公共话术列表在第三层
            thisCollectList.map(item => {
              if (item.speechList) {
                item.speechList.map(item1 => {
                  if (item1.speechList) {
                    item1.speechList.map(item2 => {
                      item2.sel = true;
                      thisCollectMap[item2.id] = item2;
                    });
                  }
                });
              }
            });
          } else {
            thisCollectList.map(item => {
              item.sel = true;
              thisCollectMap[item.collectId] = item;
            });
          }
          collectView.value.collectList = thisCollectList;
          collectView.value.selCollectMap = thisCollectMap;
          break;
        case 3:
          // 取消全选
          thisCollectList = deepClone(collectView.value.collectList);
          thisCollectList.map(item => {item.sel = false});
          collectView.value.collectList = thisCollectList;
          collectView.value.selCollectMap = {};
          break;
        case 4:
          // 批量删除
          removeContent(Object.values(collectView.value.selCollectMap));
          break;
        case 5:
          // 设置原有勾选缓存
          collectView.value.selCollectMap = collectView.value.selAllMap[collectView.value.key] || {};
          break;
      }
      // 计算选择列表数量
      collectView.value.selCollectCount = Object.keys(collectView.value.selCollectMap).length;
      // 设置当前缓存-去除
      // collectView.value.selAllMap[collectView.value.key] = collectView.value.selCollectMap;
      store.commit("setEmit", {type: "selCollectCount", value: collectView.value.selCollectCount});
    }

    // 显示组件type-1搜索2编辑,key-1显示
    function showComponents(param) {
      let {type, key, editItem, detailType} = param;
      let thisSessionInfo = "";
      // 快捷回复打开的编辑窗口
      if (sessionInfo.value.to) {
        thisSessionInfo = sessionInfo.value;
      }
      if (type == 1) {
        store.commit("setEmit", {type: "showComponents", value: {type: 1, key: key, sessionInfo: thisSessionInfo}});
      } else if (type == 2) {
        store.commit("setEmit", {type: "showComponents", value: {type: 2, key: key, editItem: editItem || {}, editType: collectView.value.key, sessionInfo: thisSessionInfo, detailType: detailType}});
      }
    }

    // 移动分组 type-1开始2进入3过程4结束
    function classifyDrag(e, type, item) {
      switch (type) {
        case 1:
          if (!item.id || item.id == 0) {
            e.stopPropagation();
            e.preventDefault();
            return false;
          }
          dialogObj.value.moveItem = item;
          break;
        case 2:
          if (!item.id || item.id == 0) {
            e.stopPropagation();
            e.preventDefault();
            return false;
          }
          if (dialogObj.value.moveItem !== item) {
            let oldIndex = dialogObj.value.moveList.indexOf(dialogObj.value.moveItem);
            let newIndex = dialogObj.value.moveList.indexOf(item);
            let newItems = [...dialogObj.value.moveList];
            // 删除老节点
            newItems.splice(oldIndex, 1);
            // 添加新节点
            newItems.splice(newIndex, 0, dialogObj.value.moveItem);
            dialogObj.value.moveList = [...newItems];
          }
          e.preventDefault();
          break;
        case 3:
          // 去除禁止光标
          e.preventDefault();
          break;
        case 4:
          dialogObj.value.moveItem = {};
          break;
        default:
          break;
      }
    }

    // 判断当前消息是否显示编辑按钮
    function isShowEdit() {
      let flag = false;
      let thisItem = collectView.value.msgList[0];
      if (/^(text|custom|image|else|note)$/.test(thisItem.collectType)) {
        flag = true;
        if (thisItem.type == "custom" && thisItem.content && thisItem.content.type == "multi") {
          flag = true;
          // 只要图文才能收藏
          thisItem.content.msgs.map(item => {
            if (item.type != "text" && item.type != "image") {
              flag = false;
            }
          });
        } else if (thisItem.type == "custom" && !(thisItem.content && thisItem.content.type == "multi")) {
          flag = false;
        }
      }
      return flag;
    }

    // 转发消息
    async function forwardItem(type, list) {
      if (type == 0) {
        // 转发当前显示消息
        list = deepClone(collectView.value.msgList);
      } else if (type == 1) {
        if (collectView.value.selCollectCount > 20) {
          toast({title: "一次最多发送20条消息", type: 2});
          return;
        }
        // 转发批量选择
        list = Object.values(collectView.value.selCollectMap);
      }
      loading();
      let forwardRes = await forwardCollectMsgs(list, 3, sessionInfo.value);
      loading().hide();
      if (!forwardRes.success) {
        toast({title: forwardRes.errorMsg, type: 2});
        return;
      }
      changeSelCollectList(3);
      if (list.length == 1 && sessionInfo.value.to) {
        alert({
          title: "提示",
          content: `<div>确认将内容发送至 <b>乐有家网客户 | ${sessionInfo.value.name || sessionInfo.value.detailInfo.name}</b></div>`,
          done: async confirmType => {
            if (confirmType == 1) {
              remote.store.getters.getNim.forwardMsg({scene: sessionInfo.value.scene, to: sessionInfo.value.to, msg: forwardRes.thisMsg}).then(res => {
                remote.store.dispatch("sendMsgDone", res);
                // 关闭搜索弹窗
                if (sessionInfo.value.close && getChildWin("collect")) {
                  getChildWin("collect").close();
                  remote.store.dispatch("setChildWin", {type: "del", name: "collect"});
                }
              });
            }
          }
        });
      } else {
        if (sessionInfo.value.close) {
          // 关闭搜索弹窗
          if (getChildWin("collect")) {
            getChildWin("collect").close();
            remote.store.dispatch("setChildWin", {type: "del", name: "collect"});
          }
        }
      }
    }

    // 图片文件另存为
    async function saveItem(type, list) {
      if (type == 1) {
        // 转发另存为
        list = Object.values(collectView.value.selCollectMap);
      }
      let thisMsgs = [];
      let p = [];
      list.map(item => {
        p.push(new Promise(async resolve => {
          let thisItem = {
            type: item.type
          };
          switch (item.type) {
            case "image":
            case "video":
            case "file":
              if (item.file) {
                thisMsgs.push(item);
              }
              break;
            case "custom":
              if (item.content && item.content.msgs) {
                item.content.msgs.map(item1 => {
                  if (item1.type == "image") {
                    thisMsgs.push(item1);
                  }
                });
              }
              break;
            default:
              let node = document.createElement("div");
              node.innerHTML = item.oHtml;
              let res = await elmToMsg(2, node, 1);
              if (res.msgs.length > 0) {
                res.msgs.map(item1 => {
                  if (item1.type == "image") {
                    thisMsgs.push(item1);
                  }
                });
              }
              break;
          }
          resolve();
        }));
      });
      loading();
      await Promise.all(p);
      loading().hide();
      alert({
        content: `<div>只能保存图片/视频/文件到电脑,共选择 <i class="highlight">${thisMsgs.length}</i> 个文件</div>`,
        done: async type => {
          if (type == 1 && thisMsgs.length > 0) {
            changeSelCollectList(3);
            store.commit("setEmit", {
              type: "fileInput", value: {
                nwdirectory: true,
                done: files => {
                  toast({title: "文件保存中", type: 1});
                  thisMsgs.map((item, index) => {
                    let thisExt = item.file.ext && item.file.ext != "unknown" ? item.file.ext : "png";
                    let isImage = config.imgTypeReg.test(thisExt);
                    let param = {
                      filePath: files[0].path + "\\",
                      fileName: `乐聊收藏${isImage ? "图片" : "文件"}${new Date().getTime()}${index}.${thisExt}`,
                      fileExt: thisExt,
                      replaceFile: true,
                    }
                    store.dispatch("downloadFile", {item: item, param: param});
                  });
                }
              }
            });
          }
        }
      });
    }

    return {
      sessionInfo,
      classifyUlBoxRef,
      classifyUlRef,
      lyDialogInputRef,
      collectMsgRef,
      collectView,
      collectStoryObj,
      collectObj,
      dialogObj,

      changeKey,
      loadMore,
      resetData,
      toggleClassify,
      showMsgItem,
      dialogOperate,
      showDialog,
      removeClassify,
      removeContent,
      moveCollectContent,
      changeSelCollectList,
      showComponents,
      classifyDrag,
      isShowEdit,
      forwardItem,
      saveItem,
    }
  }
}
</script>
<style scoped lang="scss">
.collect {
  .collect-header {
    height: 54px;
    display: flex;
    align-items: center;
    padding: 0 16px;

    .header-search-box {
      flex: 1;
      height: 30px;
      line-height: 28px;
      padding-left: 26px;
      color: #999999;
      border-radius: 4px;
      border: 1px solid #E0E0E0;
      background: url("/img/schedule/search.png") no-repeat 8px 8px;
      background-size: 16px 16px;
      cursor: pointer;

      &:hover {
        border: 1px solid #333333;
      }
    }

    .header-btn-box {
      display: flex;
      align-items: center;

      .btn {
        height: 20px;
        line-height: 20px;
        margin-left: 20px;
        padding-left: 20px;
        color: #333333;
        background-size: 20px 20px;
        background-repeat: no-repeat;
        cursor: pointer;

        &.btn-note {
          background-image: url("/img/collect/icon_add.png");
        }

        &.btn-refresh {
          background-image: url("/img/collect/icon_refresh.png");
        }
      }
    }
  }

  .collect-content {
    display: flex;
    height: calc(100% - 54px);

    .collect-content-left {
      width: 108px;
      height: 100%;
      border-right: 1px solid #E0E0E0;
      flex-shrink: 0;

      .left-li {
        height: 30px;
        line-height: 30px;
        color: #333333;
        padding-left: 16px;
        cursor: pointer;

        &:hover {
          color: $styleColor;
          background: $styleBg2;
        }

        &.sel {
          font-weight: bold;
          color: $styleColor;
          background: $styleBg2;
        }
      }
    }

    .collect-content-right {
      width: calc(100% - 108px);
      height: 100%;

      .collect-classify-box {
        display: flex;
        height: 26px;
        padding: 0 16px;
        overflow: hidden;

        &.show {
          overflow: auto;
        }

        .classify-content {
          display: flex;
          flex: 1;

          .classify-intr {
            height: 20px;
            line-height: 20px;
            color: #666666;
            margin-right: 4px;
            flex-shrink: 0;
          }

          .classify-ul {
            display: flex;
            flex-wrap: wrap;
          }

          .classify {
            height: 20px;
            line-height: 18px;
            padding: 0 10px;
            color: #999999;
            border: 1px solid #E0E0E0;
            margin: 0 6px 6px 0;
            cursor: pointer;
            flex-shrink: 0;
            border-radius: 2px;

            &.sel {
              color: $styleColor;
              background: #FFF3F3;
              border: 1px solid $styleColor;
            }
          }
        }

        .classify-btn-box {
          display: flex;
          align-items: center;
          height: 20px;
          flex-shrink: 0;

          .btn {
            margin-left: 16px;
            cursor: pointer;

            &.btn-more {
              padding-right: 10px;
              position: relative;

              &.show {
                color: $styleColor;

                &:after {
                  border-width: 0 4px 4px 4px;
                  border-color: transparent transparent $styleColor transparent;
                }
              }

              &:after {
                content: "";
                position: absolute;
                top: 50%;
                right: 0;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-width: 4px 4px 0 4px;
                border-style: solid;
                border-color: #000000 transparent transparent transparent;
              }
            }

            &:hover {
              color: $styleColor;

              &:after {
                border-color: $styleColor transparent transparent transparent;
              }
            }
          }
        }
      }

      .collect-type-box {
        display: flex;
        height: 30px;
        line-height: 18px;
        padding: 0 8px;
        border-bottom: 1px solid #D8D8D8;

        .type-li {
          line-height: 30px;
          padding: 0 8px;
          cursor: pointer;
          color: #666666;

          &:hover {
            color: $styleColor;
          }

          &.sel {
            position: relative;
            color: $styleColor;
            font-weight: bold;

            &:after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 12px;
              height: 3px;
              background: $styleColor;
            }
          }
        }
      }

      .collect-details-box {
        display: flex;
        height: calc(100% - 26px - 30px);
        position: relative;

        .collect-details-left {
          flex: 1;
          height: 100%;
          overflow: auto;

          &.sel-more {
            height: calc(100% - 42px);
          }
        }

        .collect-details-right {
          width: calc(100% - 400px);
          height: 100%;
          border-left: 1px solid #E0E0E0;

          .collect-details-msg {
            height: calc(100% - 40px);
            overflow: scroll;
            padding: 12px 16px 0;
          }

          .collect-details-btn-box {
            height: 40px;
            padding: 0 16px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            border-top: 1px solid #E0E0E0;

            .btn {
              padding: 4px 12px;
              margin-left: 8px;
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              cursor: pointer;

              &:hover {
                border: 1px solid $styleColor;
                color: $styleColor;
              }
            }
          }
        }

        .collect-details-sel-box {
          position: absolute;
          left: 0;
          bottom: 0;
          width: 100%;
          height: 42px;
          padding: 0 16px;
          border-top: 1px solid #E5E5E5;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #FFFFFF;

          .highlight {
            color: $styleColor;
            margin: 0 2px;
          }

          .collect-details-sel-btn-box {
            display: flex;
            align-items: center;

            .btn {
              margin-left: 8px;
              padding: 5px 10px;
              border-radius: 4px;
              border: 1px solid #E0E0E0;
              cursor: pointer;

              &:hover {
                border: 1px solid $styleColor;
                color: $styleColor;
              }
            }
          }
        }
      }
    }
  }

  :deep(.dialog-manger-box) {
    .content {
      padding: 10px 0 0 !important;
    }

    footer {
      padding: 12px 0 !important;
      box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05), inset 0px 1px 0px 0px #DDDDDD;
    }
  }

  :deep(.dialog-move-box) {
    .content {
      padding: 0 !important;
    }
  }

  .main-dialog-box {
    .add-classify-box {
      margin: 0 16px;

      .add-classify {
        padding-left: 16px;
        color: $styleColor;
        background: url("/img/workbench/icon_add.png") no-repeat left center;
        background-size: 12px 12px;
        line-height: 16px;
        cursor: pointer;
      }
    }

    .sel-classify-ul {
      margin: 10px 0;
      height: 230px;
      overflow-y: auto;

      .sel-classify-li {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        padding: 6px 16px;
        position: relative;

        &:hover {
          background: $styleBg1Hover;
        }

        &.move {
          box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.18) !important;
        }

        &.move-status {
          &:hover {
            box-shadow: none;
            background: none;
          }
        }

        &.sel {
          .li-sel-icon {
            background-image: url("/img/mail/check_1.png");
          }
        }

        .li-sel-icon {
          width: 14px;
          height: 14px;
          background-image: url("/img/mail/check_0.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-right: 10px;
          flex-shrink: 0;
        }

        .li-img-box {
          width: 20px;
          height: 20px;
          overflow: hidden;
          margin-right: 8px;
          flex-shrink: 0;
          background: #F2F2F2;
          border: 1px solid #D7D9DA;
          border-radius: 2px;
          position: relative;

          img {
            width: calc(100% + 2px);
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }

        .li-name {
          flex: 1;
        }

        .li-move-icon {
          width: 16px;
          height: 16px;
          background-image: url("/img/workbench/icon_move.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .li-edit-icon {
          width: 16px;
          height: 16px;
          background-image: url("/img/workbench/icon_edit.png");
          background-repeat: no-repeat;
          background-size: 100%;
          margin-right: 10px;
          flex-shrink: 0;
          cursor: pointer;
        }

        .li-close-icon {
          width: 16px;
          cursor: pointer;
        }
      }
    }

    .ly-dialog-default-box {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;

      .ly-dialog-default-label {
        color: #666666;
        line-height: 30px;
        flex-shrink: 0;

        .ly-dialog-default-tips {
          color: #EE3939;
          margin-right: 5px;
        }
      }

      .ly-dialog-default-detail {
        width: 282px;

        .ly-dialog-default-input {
          width: 100%;
          line-height: 28px;
          padding-left: 10px;
          border: 1px solid #CCCCCC;
          border-radius: 4px;

          &::placeholder {
            color: #999999;
          }

          &:focus {
            border: 1px solid #333333;
          }
        }
      }
    }

    .move-content-box {
      .ly-dialog-default-tab {
        display: flex;
        padding: 0 6px;
        border-bottom: 1px solid #E0E0E0;
        margin-bottom: 10px;

        li {
          position: relative;
          padding: 10px;
          cursor: pointer;

          &.sel {
            color: $styleColor;
            font-weight: bold;

            &:after {
              content: "";
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              width: 12px;
              height: 3px;
              background: $styleColor;
            }
          }
        }
      }

      .move-content-tips {
        display: flex;
        align-items: center;
        height: 34px;
        padding-left: 36px;
        color: #666666;
        background: url("/img/collect/icon_tips.png") no-repeat 15px center;
        background-size: 14px 14px;
        border-bottom: 1px solid #E0E0E0;
        margin-bottom: 10px;
      }

      .move-content-ul {
        margin: 7px 0;
        height: 216px;
        overflow-y: auto;

        li {
          display: flex;
          align-items: center;
          height: 30px;
          padding: 0 16px;

          &:hover {
            background: $styleBg1Hover;
          }

          .sel-box {
            display: inline-block;
            width: 14px;
            height: 14px;
            position: relative;
            border: 1px solid #DDDDDD;
            border-radius: 50%;
            flex-shrink: 0;
            margin-right: 6px;

            &.sel {
              border: 1px solid $styleColor;
              background: $styleColor;

              &:after {
                content: "";
                width: 6px;
                height: 3px;
                border: 2px solid #FFFFFF;
                border-top: transparent;
                border-right: transparent;
                position: absolute;
                top: 3px;
                left: 2px;
                transform: rotate(-45deg);
              }
            }
          }
        }
      }
    }
  }
}
</style>