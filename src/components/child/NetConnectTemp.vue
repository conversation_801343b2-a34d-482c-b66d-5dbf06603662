<template>
  <div class="net-connect-temp" ref="netRef">
    <div class="child-win-nav win-drag">外部访问申请</div>
    <div class="qrcode-box">
      <img v-if="tempInfo.qrcode" class="qrcode" :src="tempInfo.qrcode" alt="">
      <div class="tips">{{ tempInfo.tips }}</div>
    </div>
  </div>
</template>
<script>
import {ref, reactive, watch, computed, onMounted, nextTick} from "vue";
import {useStore} from "vuex";
import {alert, loading} from "@comp/ui";
import {generateCodeApi} from "@utils/net/api";
import {createQrCode} from "@utils";

export default {
  name: "netConnectTemp",
  setup(props, ctx) {
    const store = useStore();
    let netRef = ref();
    let tempInfo = ref({});
    // 当前窗口
    let currentWindow = store.getters.getCurrentWindow();

    init();

    // 初始化数据
    async function init() {
      // 获取临时电脑授权二维码和提示
      loading();
      let secret = await store.dispatch("getSecretEnCrypt", {param: {}});
      let res = await generateCodeApi({msgBody: JSON.stringify({secret: secret})});
      loading().hide();
      if (res.success && res.data) {
        // 赋值显示
        let qrcode = await createQrCode({text: res.data.qrCode, width: 138});
        tempInfo.value = {
          qrcode: qrcode,
          tips: res.data.tip
        }
        // 计算窗口高度
        nextTick(() => {
          let bounds = currentWindow.getBounds();
          let width = 410;
          let height = 245;
          let tipsElm = netRef.value.querySelector(".tips");
          if (tipsElm?.clientHeight - 22 > 0) {
            height += tipsElm.clientHeight - 22;
          }
          store.commit("setWindowSizeInfo", {currentWindow: currentWindow.cWindow.id, x: bounds.x, y: bounds.y, width: width, height: height, minW: width, minH: height, resizable: false});
        });
      } else {
        alert({
          content: (res.errorMsg || "服务异常!") ,
          showCancel: false,
          done: () => {
            store.commit("setWindowClose", currentWindow.cWindow.id);
          }
        });
      }
    }

    return {
      netRef,
      tempInfo,
    }
  }
}
</script>
<style scoped lang="scss">
.net-connect-temp {
  width: 100%;
  height: 100%;

  .qrcode-box {
    width: 100%;
    height: calc(100% - 30px);
    padding: 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .qrcode {
      width: 138px;
      height: 138px;
      margin-bottom: 16px;
    }

    .tips {
      font-size: 15px;
      line-height: 22px;
      color: #333333;
      word-break: break-all;
    }
  }
}
</style>