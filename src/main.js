import {createApp} from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import imUi from "@comp/ui/util";

import '@static/css/reset.css'
import '@static/css/main.css'

// 初始化 electron 相关配置
const electron = window.require('electron');
const ipcRenderer = electron.ipcRenderer;
const remote = window.remote;

// 确保 Node.js 模块可用
if (!window.fs) {
  window.fs = window.require('fs');
}
if (!window.path) {
  window.path = window.require('path');
}
if (!window.os) {
  window.os = window.require('os');
}
if (!window.crypto) {
  window.crypto = window.require('crypto');
}

console.log('加载remote:', remote);
console.log('Node.js 模块已加载:', {
  fs: !!window.fs,
  path: !!window.path,
  os: !!window.os,
  crypto: !!window.crypto
});
let app = createApp(App);

// 全局注入 electron 对象
app.config.globalProperties.$electron = electron;
app.config.globalProperties.$ipcRenderer = ipcRenderer;
app.config.globalProperties.$remote = remote;

app.use(store).use(router).use(imUi)
app.mount('#app');
// 监听vue错误
app.config.errorHandler = (err, vm, info) => {
  console.log("vue错误", err.stack, info);
}
// 监听vue警告
app.config.warnHandler = function (err, vm, info) {
  console.log("vue警告", err?.stack || err, info)
}

// 监听 electron 事件
ipcRenderer.on('main-process-message', (_event, message) => {
  console.log('来自主进程的消息:', message);
});