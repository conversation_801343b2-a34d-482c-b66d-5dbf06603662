/* 边距 */

.mr4 {
    margin-right: 4px;
}

.mr5 {
    margin-right: 5px;
}

.mr6 {
    margin-right: 6px;
}


.mr10 {
    margin-right: 10px;
}

.mr15 {
    margin-right: 15px;
}

.mr20 {
    margin-right: 20px;
}


.mb10 {
    margin-bottom: 10px;
}

.mt10 {
    margin-top: 10px;
}

.mt20 {
    margin-top: 20px;
}

.ml6 {
    margin-left: 6px;
}

.ml8 {
    margin-left: 6px;
}

.ml10 {
    margin-left: 10px;
}

.ml20 {
    margin-left: 20px;
}


.relative {
    position: relative;
}

.fl {
    float: left;
}

.clearfix {
    zoom: 1;
}

.tc {
    text-align: center;
}

.clearfix:after {
    content: "";
    display: block;
    height: 0;
    overflow: hidden;
    clear: both;
}
.text-bold{
    font-weight: bold;
}
.flex{
    display: flex;
}
.flex-item{
  flex: 1;
}
.flex-bet{
    display: flex;
    justify-content: space-between;
}
.flex-center{
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex-vcenter{
    display: flex;
    align-items: center;
}
.flex-hcenter{
    display: flex;
    justify-content: center;
}