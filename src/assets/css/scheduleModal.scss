.rc-modal-overlay {
    opacity: 0;
    visibility: hidden;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 51;
    overflow-x: hidden;
    overflow-y: auto;
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -webkit-box-align: center;
    -webkit-box-pack: center;
    background: transparent;
    user-select: text;
}

.rc-modal-overlay.lay {
    background: rgba(0, 0, 0, .5);
}

.rc-modal-overlay.visible {
    opacity: 1;
    visibility: visible;
    -webkit-transition: opacity 0.3s 0s, visibility 0s 0s;
    -moz-transition: opacity 0.3s 0s, visibility 0s 0s;
    transition: opacity 0.3s 0s, visibility 0s 0s;
    touch-action: none;
}

.rc-modal-overlay * {
    transition: all ease 0.1s;
    word-break: break-all;
}

.rc-modal-overlay .font12 {
    font-size: 12px;
}

.rc-modal-overlay .font14 {
    font-size: 14px;
}

.rc-modal-overlay .bold {
    font-weight: bold;
}

.rc-modal-overlay .line-18 {
    line-height: 18px;
}

.rc-modal-overlay .rc-modal {
    width: 486px;
    background: #FFFFFF;
    box-shadow: 5px 5px 20px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    position: absolute;
    overflow: hidden;
}

.rc-modal-overlay .rc-modal .rc-head {
    padding-left: 16px;
    background: #F0F0F0;
    position: relative;
    border-radius: 4px 4px 0 0;
}

.rc-modal-overlay .rc-modal .rc-title {
    font-size: 13px;
    color: #000;
    line-height: 18px;
    padding: 7px 0;
    display: inline-block;
}

.rc-modal-overlay .rc-modal .rc-close {
    width: 12px;
    height: 12px;
    background-image: url("/img/schedule/delete.png");
    background-size: 12px 12px;
    background-repeat: no-repeat;
    position: absolute;
    right: 16px;
    top: 10px;
    cursor: pointer;
}

.rc-modal-overlay .rc-modal .rc-block {
    padding-left: 26px;
    position: relative;
}

.rc-modal-overlay .rc-modal .rc-body {
    padding: 20px 16px 6px;
    max-height: 350px;
    overflow-y: auto;
    box-sizing: border-box;
}

.rc-modal-overlay .rc-modal .rc-body::-webkit-scrollbar {
    width: 6px;
    height: auto;
}

.rc-modal-overlay .rc-modal .rc-body::-webkit-scrollbar-thumb {
    background: #D4D4D4;
}

.rc-modal-overlay .rc-modal .bloc-right {
    min-height: 30px;
    font-size: 0;
}

.rc-modal-overlay .rc-modal .bloc-right .add-alarm {
    color: $styleColor;
}

.rc-modal-overlay .rc-modal .bloc-right .edit-show {
    display: flex;
    align-items: center;
    /*min-height: 30px;*/
    padding-top: 6px;
}

.rc-modal-overlay .rc-modal .rc-block {
    margin-bottom: 10px;
    position: relative;
}

.rc-modal-overlay .rc-modal .rc-block .block-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    left: 0;
    top: 7px;
    background-repeat: no-repeat;
    background-size: 16px 16px;
}

.rc-modal-overlay .rc-modal .rc-block .count-num {
    position: absolute;
    right: 10px;
    top: 0;
    line-height: 30px;
    font-size: 12px;
    color: #999;
}

.rc-modal-overlay .rc-modal .rc-block .count-num.area {
    top: 0;
    right: 6px;
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.theme {
    background-image: url("/img/schedule/rc_theme.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.time {
    background-image: url("/img/schedule/rc_time.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.people {
    background-image: url("/img/schedule/rc_people.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.hy {
    background-image: url("/img/schedule/rc_hy.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.people {
    background-image: url("/img/schedule/rc_people.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.video {
    background-image: url("/img/schedule/rc_video.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.pen {
    background-image: url("/img/schedule/rc_pen.png");
}

.rc-modal-overlay .rc-modal .rc-block .block-icon.alarm {
    background-image: url("/img/schedule/rc_alarm.png");
}

.rc-modal-overlay .rc-modal .rc-block input,
.rc-modal-overlay .rc-modal .rc-block textarea {
    width: 428px;
    border-radius: 2px;
    border: 1px solid #E7E7E7;
    color: #000;
    height: 30px;
    font-size: 12px;
    line-height: 18px;
    padding: 5px 4px 5px 10px;
    overflow-x: hidden;
}

.rc-modal-overlay *::-webkit-scrollbar {
    width: 4px !important;
    height: auto !important;
}

.rc-modal-overlay *::-webkit-scrollbar-thumb {
    background: #D4D4D4;
}

.rc-modal-overlay .rc-modal .rc-block textarea {
    /*overflow-y: hidden;*/
}

.rc-modal-overlay .rc-modal .rc-block textarea.open {
    min-height: 80px;
    /*padding-bottom: 32px;*/
}

.rc-modal-overlay .rc-modal .rc-block textarea.open + .count-num {
    top: unset;
    background: transparent;
    /*width: calc(100% - 2px);*/
    bottom: -2px;
    /*right: 1px;*/
    /*text-align: right;*/
    /*padding-right: 6px;*/
    /*height: 22px;*/
    /*line-height: 14px;*/
    /*padding-top: 4px;*/
}

.rc-modal-overlay .rc-modal .rc-block input::selection,
.rc-modal-overlay .rc-modal .rc-block textarea::selection {
    background-color: #E6F7FF;
}

.rc-modal-overlay .rc-modal .rc-block input:focus,
.rc-modal-overlay .rc-modal .rc-block textarea:focus {
    border: 1px solid #000000;
}

.rc-modal-overlay .rc-modal .rc-block button:disabled,
.rc-modal-overlay .rc-modal .rc-block .rc-input:disabled {
    border: 1px solid #CCCCCC;
    background: $styleBg1Hover;
    color: #999;
}

.rc-modal-overlay .rc-modal .rc-block .rc-input.tit {
    font-size: 14px;
    font-weight: bold;
}

.rc-modal-overlay .rc-modal .h30 {
    height: 30px;
}

.rc-modal-overlay .rc-modal .line-30 {
    line-height: 30px;
}

.rc-modal-overlay .rc-modal .rc-block input::placeholder {
    color: #999;
}

.rc-modal-overlay .rc-modal .rc-block .pr6 {
    padding-right: 6px;
}

.rc-modal-overlay .mr6 {
    margin-right: 6px;
}

.rc-modal-overlay .mt2 {
    margin-top: 2px;
}

.rc-modal-overlay .mt6 {
    margin-top: 6px;
}

.rc-modal-overlay .mt10 {
    margin-top: 10px;
}

.rc-modal-overlay .mb0 {
    margin-bottom: 0 !important;
}

.rc-modal-overlay .c666 {
    color: #666;
}

.rc-modal-overlay .make-black-tip {
    cursor: pointer;
}

.rc-modal-overlay .rc-modal .rc-block .btn-blue {
    font-size: 12px;
    color: $styleColor;
    border-radius: 2px;
    border: 1px solid $styleColor;
    height: 30px;
    line-height: 16px;
    padding: 6px 10px 8px;
    background: #fff;
    cursor: pointer;
}

.rc-modal-overlay .rc-footer {
    padding-bottom: 16px;
    font-size: 0;
    border-radius: 0 0 4px 4px;
}

.rc-modal-overlay .rc-footer.shadow {
    padding-top: 16px;
    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05), 0px 1px 0px 0px #DDDDDD;
}

.rc-modal-overlay .rc-footer .btn-white {
    font-size: 12px;
    color: #333;
    border-radius: 4px;
    border: 1px solid #CCCCCC;
    height: 30px;
    line-height: 16px;
    padding: 6px 20px 8px;
    background: #fff;
    cursor: pointer;
}

.rc-modal-overlay .rc-footer .btn-default {
    font-size: 12px;
    color: #fff;
    font-weight: 500;
    border-radius: 4px;
    background: $styleColor;
    border: 1px solid $styleColor;
    height: 30px;
    line-height: 16px;
    padding: 6px 20px 8px;
    cursor: pointer;
}


.rc-modal-overlay .rc-modal .icheckbox_square {
    background-image: url("/img/schedule/un_checked.png");
    background-size: 14px 14px;
    background-repeat: no-repeat;
    background-position: left 10px top 8px;
    font-size: 12px;
    color: #000;
    padding-left: 28px;
    position: relative;
    cursor: pointer;
}

.rc-modal-overlay .rc-modal .icheckbox_square.disabled::after {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.rc-modal-overlay .rc-modal .icheckbox_square.checked {
    background-image: url("/img/schedule/checked.png");
}

.rc-modal-overlay .rc-modal .icheckbox_square.checked.disabled {
    background-image: url("/img/schedule/checked_gray.png");
}

.rc-modal-overlay .rc-modal .icheckbox_square input {
    width: 100% !important;
    height: 30px !important;
    opacity: 0;
    position: absolute;
    left: 0;
    top: 0;
    cursor: pointer;
}

.rc-modal-overlay a.blue {
    display: inline-block;
}

.rc-modal-overlay a.blue:hover {
    color: #2D91E6;
}

.rc-modal-overlay .red-tip {
    font-size: 10px;
    line-height: 30px;
    color: #EE3939;
    padding-left: 18px;
    background-image: url("/img/schedule/red_tip.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
    background-position: left 6px top 10px;
}

/* 日期时间 */
.sch-select-list {
    font-size: 12px;
    display: block;
    width: 426px;
    max-height: 180px;
    overflow-x: hidden;
    overflow-y: auto;
    white-space: nowrap;
    box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
    border-radius: 2px;
    border: 1px solid #CCCCCC;
    background-color: #fff;
}

.sch-select-list.time-list {
    width: 82px;
}

.sch-select-list::-webkit-scrollbar {
    width: 4px;
    height: auto;
}

.sch-select-list::-webkit-scrollbar-thumb {
    background: #D4D4D4;
}

.sch-select-list li {
    display: block;
    line-height: 30px;
    height: 30px;
    padding: 0 8px;
    cursor: pointer;
}

.sch-select-list li .light {
    color: $styleColor;
}

.sch-select-list li:not(.eg-tit):hover,
.sch-select-list li.curr {
    background-color: $styleBg1;
}

.sch-select-list li.eg-tit {
    background: $styleBg1;
    color: #000;
    font-weight: 700;
    line-height: 24px;
    height: 24px;
}

.sch-select-list li.empty-li {
    color: #666;
    height: 50px;
    line-height: 50px;
    text-align: center;
}

.dateDiv {
    font-size: 12px;
    width: 240px;
    padding: 7px 0 15px;
    box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
    border-radius: 2px;
    border: 1px solid #CCCCCC;
    background-color: #fff;
    overflow: hidden;
}

.date-table {
    font-size: 12px;
    color: #727578;
}

.date-table tr th {
    font-weight: 400;
    font-size: 14px;
    height: 34px;
    text-align: center;
}

.date-table tr.weekdate th {
    font-size: 12px;
}

.date-table tr th a {
    display: block;
    width: 24px;
    height: 24px;
    margin: 0 auto;
}

.date-table tr td {
    width: 34px;
    height: 34px;
    text-align: center;
    cursor: pointer;
}

.date-table tr td span {
    display: block;
    width: 22px;
    height: 22px;
    line-height: 20px;
    margin: 5px;
}

.date-table tr td.transparent {
    color: #ccc;
}

.date-table tr td.change span {
    color: #3888FF;
    background-color: transparent;
}

.date-table tr td.curr.change span,
.date-table tr td.curr span {
    color: #FFFFFF;
    background-color: #2D91E6;
    font-weight: bold;
    border-radius: 22px;
}

.date-table tr td.today span {
    background: #fff !important;
    color: #2D91E6 !important;
    font-weight: 700 !important;
    border-radius: 22px;
    border: 1px solid #3188E8;
}

.date-table tr td.curr.today span {
    background: #2D91E6 !important;
    color: #fff !important;
    font-weight: 700 !important;
    border-radius: 22px;
}

.date-table tr td:not(.today):not(.curr) span:hover {
    background: #E7E7E7 !important;
    color: #222 !important;
    font-weight: 700 !important;
    border-radius: 22px;
}

.date-table tr td.listCurr span {
    position: relative;
}

.date-table tr td.listCurr span:after {
    position: absolute;
    left: 50%;
    bottom: -7px;
    content: '';
    display: block;
    width: 4px;
    height: 4px;
    margin-left: -2px;
    background: #FEDA5D;
    border-radius: 4px;
}

.date-prev {
    background: url("/img/schedule/time_left.png") center no-repeat;
    background-size: 6px 10px;
}

.date-next {
    background: url("/img/schedule/time_right.png") center no-repeat;
    background-size: 6px 10px;
}


/* eg-selected-list s*/
.eg-selected-list {
    margin-top: 4px;
}

.eg-selected-list .emp-li {
    height: 30px;
}


.eg-selected-list .emp-li span {
    font-size: 12px;
    color: #000;
    line-height: 30px;
    float: left;
}

.eg-selected-list .emp-li span .emp-li-inner-deptName {
    color: #999;
}

.eg-selected-list .emp-li span .emp-li-inner-deptName span {
    color: #999;
}

.eg-selected-list .emp-li span span.light {
    float: none;
}

.eg-selected-list .org-icon {
    float: left;
    width: 10px;
    height: 10px;
    margin-top: 10px;
    margin-left: 6px;
    background-image: url("/img/schedule/organizer.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
}

.eg-selected-list .busy-icon {
    float: left;
    width: 10px;
    height: 10px;
    margin-top: 10px;
    margin-left: 6px;
    background-image: url("/img/schedule/busy.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
}

.eg-selected-list .extend-e-icon {
    float: right;
    width: 10px;
    height: 10px;
    margin-top: 10px;
    margin-right: 10px;
    background-image: url("/img/schedule/extend.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
}

.eg-selected-list .del-e-icon {
    float: right;
    width: 8px;
    height: 8px;
    margin-top: 11px;
    background-image: url("/img/schedule/delete.png");
    background-size: 8px 8px;
    background-repeat: no-repeat;
}

.eg-selected-list .group-li {
    min-height: 30px;
}

.eg-selected-list .group-li .group-head {
    min-height: 30px;
}

.eg-selected-list .group-li span.gro-name {
    font-size: 12px;
    color: #000;
    line-height: 30px;
    cursor: pointer;
    float: left;
}

.eg-selected-list .group-li .arr-icon {
    width: 20px;
    height: 20px;
    float: left;
    background-image: url("/img/schedule/arr_down.png");
    background-size: 20px 20px;
    background-repeat: no-repeat;
    margin-top: 5px;
    cursor: pointer;
}

.eg-selected-list .group-li.open .arr-icon {
    transform: rotate(180deg);
}

.eg-selected-list .group-li .child-list {
    display: none;
}

.eg-selected-list .group-li.open .child-list {
    display: block;
}

.eg-selected-list .group-li .emp-li {
    padding-left: 10px;
}

/* eg-selected-list e*/

/* add-hys S*/
.add-hys {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 3;
    background: #fff;
    border-radius: 4px 4px 0px 0px;
    display: none;
}

.add-hys .add-head {
    padding-left: 16px;
    height: 30px;
    background: #F0F0F0;
    border-radius: 4px 4px 0px 0px;
}

.add-hys .add-head .rc-title {
    font-size: 14px;
    color: #000;
    font-weight: 600;
    background-image: url("/img/schedule/time_left.png");
    background-size: 7px 11px;
    background-repeat: no-repeat;
    background-position: left 0 top 10px;
    padding-left: 16px;
    cursor: pointer;
}

.add-hys .hys-list {
    overflow-y: auto;
    height: 410px;
}

.add-hys .hys-list::-webkit-scrollbar {
    width: 6px;
    height: auto;
}

.add-hys .hys-list::-webkit-scrollbar-thumb {
    background: #D4D4D4;
}

.add-hys .hys-list .hys-li {
    line-height: 40px;
    padding: 0 16px;
    cursor: pointer;
    min-height: 40px;
}

.add-hys .hys-list .hys-li.busy {
    background: #F5F5F5;
}

.add-hys .hys-list .hys-li:not(.busy):not(.added):hover {
    background: $styleBg1;
}

.add-hys .hys-list .hys-li .hys-icon {
    width: 16px;
    height: 16px;
    background-image: url("/img/schedule/rc_hy.png");
    background-size: 16px 16px;
    background-repeat: no-repeat;
    float: left;
    margin-top: 12px;
}

.add-hys .hys-list .hys-li.busy .hys-icon {
    background-image: url("/img/schedule/rc_hy_gray.png");
}

.add-hys .hys-list .hys-li .room-name {
    font-size: 12px;
    color: #333;
    float: left;
    line-height: 40px;
    margin-left: 10px;
}

.add-hys .hys-list .hys-li.busy .room-name {
    color: #BFBFBF;
}

.add-hys .hys-list .hys-li .busy-status {
    font-size: 10px;
    color: #FF9100;
    float: right;
    background-image: url("/img/schedule/busy.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
    background-position: left 0 top 15px;
    padding-left: 16px;
    display: none;
}

.add-hys .hys-list .hys-li.busy .busy-status {
    display: block;
}

.add-hys .hys-list .hys-li .blue {
    font-size: 12px;
    line-height: 40px;
    color: $styleColor;
    float: right;
}

.add-hys .hys-list .hys-li .use {
    display: none;
}

.add-hys .hys-list .hys-li.added .use {
    display: block;
}

/* add-hys E*/

/* hys-sel-list S*/
.hys-sel-list {
    /*padding: 6px 0;*/
}

.hys-sel-list .hys-sel {
    font-size: 12px;
    color: $styleColor;
    line-height: 20px;
    height: 22px;
    border-radius: 1px;
    border: 1px solid $styleColor;
    margin-right: 6px;
    display: inline-block;
    padding-right: 4px;
    background: $styleBg2;
    margin-top: 6px;
}

.hys-sel-list .hys-sel:last-child {
    margin-bottom: 6px;
}

.hys-sel-list .hys-sel .blue-del {
    float: left;
    width: 20px;
    height: 20px;
    background-image: url("/img/schedule/del_red.png");
    background-size: 20px 20px;
    background-repeat: no-repeat;
    cursor: pointer;
}

.hys-block .cust-hys {
    display: none;
}

.hys-block.cust .cust-hys {
    display: block;
}

.hys-block.cust .hys-sel-list {
    display: none;
}

/* hys-sel-list E*/

.rc-modal-overlay .ala-line {
    margin-bottom: 6px;
}

.select-block {
    cursor: default;
}

.select-block .select-show {
    cursor: default;
    position: relative;
    z-index: 2;
    background: transparent;
}

.select-block .check-arrow {
    position: absolute;
    right: 1px;
    top: 1px;
    z-index: 1;
    text-align: center;
    font-size: 12px;
    width: 22px;
    height: 28px;
    color: #999999;
    background-color: #fff;
}

.select-block .check-arrow:before {
    content: " ";
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #4B4B4B;
    display: block;
    width: 0;
    height: 0;
    top: 0;
    right: 7px;
    bottom: 0;
    position: absolute;
    margin: auto 0;
}

.rc-block .alarm-del {
    width: 16px;
    height: 16px;
    background-size: 8px 8px;
    background-image: url("/img/schedule/delete.png");
    background-repeat: no-repeat;
    background-position: left 4px top 4px;
    margin-top: 7px;
    margin-left: 6px;
    float: left;
    cursor: pointer;
}

/* rc-det-modal S */
.rc-det-modal {
    width: 362px;
    background: #FFFFFF;
    box-shadow: 0px 2px 20px 0px rgba(165, 165, 165, 0.5);
    border-radius: 4px;
    border: 1px solid #CCCCCC;
    position: absolute;
}

.rc-det-modal .rc-det-head {
    padding: 12px 16px 16px;
    background-image: url("/img/schedule/bg_color_0.png");
    background-size: 100% auto;
    background-repeat: no-repeat;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.rc-det-modal .rc-det-head.color_0 {
    background-image: url("/img/schedule/bg_color_0.png");
}

.rc-det-modal .rc-det-head.color_1 {
    background-image: url("/img/schedule/bg_color_1.png");
}

.rc-det-modal .rc-det-head.color_2 {
    background-image: url("/img/schedule/bg_color_2.png");
}

.rc-det-modal .rc-det-head.color_3 {
    background-image: url("/img/schedule/bg_color_3.png");
}

.rc-det-modal .rc-det-head.color_4 {
    background-image: url("/img/schedule/bg_color_4.png");
}

.rc-det-modal .rc-det-head.color_5 {
    background-image: url("/img/schedule/bg_color_5.png");
}

.rc-det-modal .rc-det-head.color_6 {
    background-image: url("/img/schedule/bg_color_6.png");
}

.rc-det-modal .rc-det-head.color_7 {
    background-image: url("/img/schedule/bg_color_7.png");
}

.rc-det-modal .rc-det-head.color_8 {
    background-image: url("/img/schedule/bg_color_8.png");
}

.rc-det-modal .rc-det-head.color_9 {
    background-image: url("/img/schedule/bg_color_9.png");
}

.rc-det-modal .rc-det-head.color_10 {
    background-image: url("/img/schedule/bg_color_10.png");
}

.rc-det-modal .rc-acts-list {
    float: right;
}

.rc-det-modal .rc-acts-list .act-icon {
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    background-size: 16px 16px;
    background-position: left 2px top 2px;
    margin-left: 12px;
    float: left;
    cursor: pointer;
}

.rc-det-modal .rc-acts-list .edit-icon {
    background-image: url("/img/schedule/det_edit.png");
}

.rc-det-modal .rc-acts-list .bw-icon {
    background-image: url("/img/schedule/det_bw.png");
}

.rc-det-modal .rc-acts-list .share-icon {
    background-image: url("/img/schedule/det_share.png");
}

.rc-det-modal .rc-acts-list .del-icon {
    background-image: url("/img/schedule/det_del.png");
}

.rc-det-modal .rc-acts-list .more-icon {
    position: relative;
    background-image: url("/img/schedule/det_more.png");
}

.rc-det-modal .rc-acts-list .close-icon {
    background-size: 11px 11px;
    background-position: 4px 4px;
    background-image: url("/img/schedule/det_close.png");
}

.rc-det-modal .rc-acts-list .more-icon:hover .more-act-box {
    display: block;
}

.rc-det-modal .more-act-box {
    background: #fff;
    width: 110px;
    position: absolute;
    left: -4px;
    top: 20px;
    box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
    border-radius: 2px;
    border: 1px solid #CCCCCC;
    display: none;
}

.rc-det-modal .more-act-box .m-a-l {
    font-size: 12px;
    color: #000;
    padding-left: 28px;
    height: 30px;
    line-height: 30px;
    background-repeat: no-repeat;
    background-size: 12px 12px;
    background-position: left 10px top 10px;
}

.rc-det-modal .more-act-box .m-a-l:hover {
    background-color: $styleBg1;
}

.rc-det-modal .more-act-box .m-a-l.l-zr {
    background-image: url("/img/schedule/zr_gray.png");
}

.rc-det-modal .more-act-box .m-a-l.l-del {
    background-image: url("/img/schedule/del_gray.png");
}

.rc-det-modal .sche-title {
    font-size: 20px;
    color: #fff;
    font-weight: 600;
    line-height: 28px;
    margin-top: 10px;
}

.rc-det-modal .sche-time {
    font-size: 14px;
    color: #fff;
    line-height: 20px;
    margin-top: 2px;
}

.rc-det-modal .build-group {
    font-size: 12px;
    color: #fff;
    line-height: 28px;
    height: 28px;
    background-color: #F47578;
    border-radius: 14px;
    background-image: url("/img/schedule/chat_icon.png");
    background-repeat: no-repeat;
    background-size: 28px 28px;
    padding: 0 10px 0 34px;
    margin-top: 16px;
    display: inline-block;
    cursor: pointer;
}

.rc-det-modal .rc-det-main {
    padding: 16px 16px 0 16px;
    max-height: 320px;
    overflow-y: auto;
    box-sizing: border-box;
}

.rc-det-modal .rc-det-main::-webkit-scrollbar {
    width: 6px;
    height: auto;
}

.rc-det-modal .rc-det-main::-webkit-scrollbar-thumb {
    background: #D4D4D4;
}

.rc-det-modal .urgent-tip {
    font-size: 10px;
    color: #FF0000;
    height: 30px;
    line-height: 30px;
    background-color: #FFEEEE;
    background-image: url("/img/schedule/red_tip.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
    background-position: left 15px top 10px;
    padding-left: 29px;
}

.rc-det-main .rc-block {
    margin-bottom: 16px;
    position: relative;
    padding-left: 26px;
}

.rc-det-modal .rc-det-main.not-my .rc-block:not(.gsr-block) {
    display: none;
}

.rc-det-main .rc-block .block-icon {
    width: 16px;
    height: 16px;
    position: absolute;
    left: 0;
    top: 1px;
    background-repeat: no-repeat;
    background-size: 16px 16px;
}

.rc-det-main .rc-block .block-icon.hys {
    background-image: url("/img/schedule/rc_hy.png");
}

.rc-det-main .rc-block .block-icon.video {
    background-image: url("/img/schedule/rc_video.png");
}

.rc-det-main .rc-block .block-icon.man {
    background-image: url("/img/schedule/rc_man.png");
}

.rc-det-main .rc-block .block-icon.people {
    background-image: url("/img/schedule/rc_people.png");
}

.rc-det-main .rc-block .block-icon.pen {
    background-image: url("/img/schedule/rc_pen.png");
}

.rc-det-main .rc-block .block-icon.alarm {
    background-image: url("/img/schedule/rc_alarm.png");
}

.rc-det-main .rc-block .block-icon.cale {
    background-image: url("/img/schedule/rc_cale.png");
}

.rc-det-main .rc-block .block-icon.detail {
    background-image: url("/img/schedule/rc_detail.png");
}

.rc-det-main .rc-block .block-icon.time {
    background-image: url("/img/schedule/rc_time.png");
}

.rc-det-main .rc-block .peo-tag {
    font-size: 10px;
    color: #238DFD;
    height: 16px;
    line-height: 16px;
    background: #EEF6FF;
    padding: 0 4px;
    border-radius: 2px;
    margin-left: 9px;
}

.rc-det-main .rc-block .par-tner {
    display: inline-block;
    font-size: 12px;
    color: #000;
    cursor: pointer;
    padding-right: 10px;
}

.rc-det-main .rc-block .par-tner.link {
    background-image: url("/img/schedule/time_right.png");
    background-size: 5px 8px;
    background-repeat: no-repeat;
    background-position: right 0 top 5px;
}

.rc-det-main .rc-block .par-status {
    font-size: 10px;
    margin-top: 2px;
    line-height: 14px;
    color: #999;
}

.rc-det-main .rc-block .hys-na {
    padding-right: 14px;
    background-image: url("/img/schedule/vip.png");
    background-size: 10px 10px;
    background-repeat: no-repeat;
    background-position: right 0 top 4px;
    float: left;
    margin-right: 24px;

}

.rc-det-main .rc-block .par-show {
    margin-top: 4px;
    width: 310px;
}

.rc-det-main .rc-block .par-show .pant-one {
    font-size: 12px;
    color: #000;
    line-height: 16px;
    margin-right: 22px;
    float: left;
    margin-top: 6px;
}

.rc-det-main .rc-block .par-show .pant-one > span {
    cursor: pointer;
}

.rc-det-main .rc-block .par-show .pant-one .p-icon {
    width: 10px;
    height: 10px;
    background-size: 10px 10px;
    background-repeat: no-repeat;
    margin-top: 4px;
    float: left;
    margin-left: 4px;
}

.par-show .pant-one .p-icon.org {
    background-image: url("/img/schedule/organizer.png");
}

.par-show .pant-one .p-icon.agree {
    background-image: url("/img/schedule/agree.png");
}

.par-show .pant-one .p-icon.refuse {
    background-image: url("/img/schedule/refuse.png");
}

.par-show .pant-one .p-icon.wait {
    background-image: url("/img/schedule/wait.png");
}

.par-show .pant-one .p-icon.busy {
    background-image: url("/img/schedule/busy.png");
}

.par-show .pant-one .p-unanswer {
    font-size: 9px;
    color: #999;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    background: #F5F5F5;
    border-radius: 2px;
    float: left;
    margin-left: 4px;
}

.rc-det-btns {
    padding: 0 16px 16px 16px;
    border-radius: 0px 0px 3px 3px;
    display: flex;
    justify-content: space-between;
}

.rc-det-btns.shadow {
    padding-top: 16px;
    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
}

.rc-det-btns .det-btn {
    font-size: 12px;
    color: #333;
    height: 30px;
    line-height: 28px;
    padding-left: 46px;
    padding-right: 28px;
    border-radius: 2px;
    border: 1px solid #E7E7E7;
    background-size: 12px 12px;
    background-repeat: no-repeat;
    background-position: left 28px top 9px;
    transition: all ease 0.1s;
}

.rc-det-btns .det-btn:last-child {
    margin-right: 0;
}

.rc-det-btns .det-btn.agree {
    background-image: url("/img/schedule/agree_def.png");
}

.rc-det-btns .det-btn.refuse {
    background-image: url("/img/schedule/refuse_def.png");
}

.rc-det-btns .det-btn.wait {
    background-image: url("/img/schedule/wait_def.png");
}

.rc-det-btns .det-btn.agree:not(.checked):hover {
    /*color: #37990D;*/
    /*border-color: #37990D;*/
    color: #2D91E6;
    border-color: #2D91E6;
    background-image: url("/img/schedule/agree_hover.png");
}

.rc-det-btns .det-btn.refuse:not(.checked):hover {
    color: #EE3939;
    border-color: #EE3939;
    background-image: url("/img/schedule/refuse_hover.png");
}

.rc-det-btns .det-btn.wait:not(.checked):hover {
    color: #FF9100;
    border-color: #FF9100;
    background-image: url("/img/schedule/wait_hover.png");
}

.rc-det-btns .det-btn.agree.checked {
    color: #2D91E6;
    /*color: #37990D;*/
    border-color: #2D91E6;
    /*border-color: #37990D;*/
    /*background-color: #E4FFE8;*/
    background-image: url("/img/schedule/agree_checked.png");
}

.rc-det-btns .det-btn.refuse.checked {
    color: #EE3939;
    border-color: #EE3939;
    /*background-color: #FFEDED;*/
    background-image: url("/img/schedule/refuse_checked.png");
}

.rc-det-btns .det-btn.wait.checked {
    color: #FF9100;
    border-color: #FF9100;
    /*background-color: #FFF3E3;*/
    background-image: url("/img/schedule/wait_checked.png");
}

.rc-det-btns .det-btn.checked {
    padding-left: 42px;
    padding-right: 24px;
    background-position: left 24px top 9px;
}

.partner-shows {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    background: #FFFFFF;
    padding: 0 16px;
    box-sizing: border-box;
    overflow-y: auto;
}

.partner-shows::-webkit-scrollbar {
    width: 6px;
    height: auto;
}

.partner-shows::-webkit-scrollbar-thumb {
    background: #D4D4D4;
}

.partner-shows .return-head {
    height: 40px;
}

.partner-shows .p-ret-icon {
    width: 14px;
    height: 14px;
    background-image: url("/img/schedule/return.png");
    background-size: contain;
    background-repeat: no-repeat;
    margin-top: 13px;
    float: left;
}

.partner-shows .p-s-t {
    font-size: 16px;
    color: #333;
    font-weight: 700;
}

.partner-shows .p-s-sub {
    font-size: 10px;
    color: #999;
    margin-top: 6px;
}

.partner-shows .pa-ner-bo {
    margin-top: 10px;

}

.partner-shows .pant-one {
    font-size: 12px;
    color: #000;
    height: 30px;
    line-height: 30px;
    display: block;
}

.partner-shows .pant-one > span {
    cursor: pointer;
}

.partner-shows .pant-one .p-icon {
    width: 10px;
    height: 10px;
    background-size: 10px 10px;
    background-repeat: no-repeat;
    margin-top: 10px;
    float: left;
    margin-left: 6px;
}

.partner-shows .pant-one .p-icon.org {
    background-image: url("/img/schedule/organizer.png");
}

.partner-shows .pant-one .p-icon.agree {
    background-image: url("/img/schedule/agree.png");
}

.partner-shows .pant-one .p-icon.refuse {
    background-image: url("/img/schedule/refuse.png");
}

.partner-shows .pant-one .p-icon.wait {
    background-image: url("/img/schedule/wait.png");
}

.partner-shows .pant-one .p-icon.busy {
    background-image: url("/img/schedule/busy.png");
}

.partner-shows .pant-one .p-unanswer {
    font-size: 9px;
    color: #666;
    height: 16px;
    line-height: 16px;
    padding: 0 4px;
    background: #F5F5F5;
    border-radius: 2px;
    float: left;
    margin-left: 6px;
    margin-top: 7px;
}

/* rc-det-modal E */

/*.sch-refuse-content S*/
.sch-refuse-content {
    width: 100%;
}

.sch-refuse-content .area-cont {
    position: relative;
}

.sch-refuse-content.err textarea {
    border: 1px solid #FF0000;
    background: #FFEBE9;
}

.sch-refuse-content textarea {
    width: 100%;
    height: 88px;
    border-radius: 2px;
    border: 1px solid #E7E7E7;
    color: #000;
    font-size: 12px;
    line-height: 18px;
    padding: 5px 4px 5px 10px;
}

.sch-refuse-content .count-num {
    position: absolute;
    right: 10px;
    bottom: 10px;
    line-height: 18px;
    font-size: 12px;
    color: #999;
}

.sch-refuse-content textarea::placeholder {
    color: #999;
}

.sch-refuse-content textarea::selection {
    background-color: #E6F7FF;
}

.sch-refuse-content textarea:focus {
    border: 1px solid #333333;
    background-color: #fff;
}

.sch-refuse-content .red-tip {
    font-size: 10px;
    line-height: 30px;
    color: #EE3939;
    padding-left: 20px;
    background-image: url("/img/schedule/red_tip.png");
    background-size: 14px 14px;
    background-repeat: no-repeat;
    background-position: left 0 top 8px;
    display: none;
}

.sch-refuse-content.err .red-tip {
    display: block;
}

.rc-modal-overlay .over-black-tip {
    font-size: 12px;
    color: #fff;
    line-height: 14px;
    padding: 7px 12px;
    border-radius: 2px;
    background-color: rgba(0, 0, 0, .8);
    position: absolute;
    transform: translateX(-50%);
    z-index: 2;
}

/* schedule-modal-zr S */
.schedule-modal-zr {
    padding: 4px 0 34px;
}

.schedule-modal-zr input {
    border-radius: 2px;
    border: 1px solid #E7E7E7;
    color: #000;
    height: 30px;
    font-size: 12px;
    line-height: 18px;
    padding: 5px 4px 5px 10px;
}

.schedule-modal-zr input.err {
    border: 1px solid #FF0000;
    background: #FFEBE9;
}

.schedule-modal-zr input::selection {
    background-color: #E6F7FF;
}

.schedule-modal-zr input:focus {
    border: 1px solid #2D91E6;
}

/* schedule-modal-zr E */
