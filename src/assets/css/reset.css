/*css样式初始化*/
* {
  box-sizing: border-box;
  user-select: none;
}

/*富文本编辑器兼容 排除#editor-css-filter下的span字体样式*/
*:not(#editor-css-filter span) {
  font-family: "微软雅黑", "Microsoft YaHei", Helvetica, Arial, sans-serif;
}

img {
  image-rendering: -webkit-optimize-contrast;
}

a, img {
  -webkit-user-drag: none;
}

html, body {
  width: 100%;
  height: 100%;
  font-size: 12px;
}

input, textarea, button {
  font-size: 12px;
  resize: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

a {
  color: #00A2FF;
  display: inline-block;
  text-decoration: none;
  outline: none;
}

body, ul, ol, dl, dd, h1, h2, h3, h4, h5, h6, p, form, fieldset, legend, input, textarea, select, button, th, td {
  margin: 0;
  padding: 0;
  outline: none;
  border: 0;
}

i, em, b {
  font-style: normal;
}

ol, ul {
  list-style: none outside none;
}

::-webkit-scrollbar {
  width: 6px;
  height: 4px;
  border-radius: 5px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: #b8bcc2;
  border-radius: 5px;
  min-height: 50px;
  cursor: default;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #6e7174;
}

::-webkit-scrollbar-track {
  border-radius: 5px;
  background-color: transparent;
}

::-webkit-scrollbar-track:hover {
  background-color: #ddd;
}

.im-emoji {
  width: 24px;
  height: 24px;
  vertical-align: bottom;
}

img::selection {
  background: #cacdd3;
}

a.im-link, a.im-link:hover {
  color: #00A2FF;
  text-decoration: underline;
  display: inline;
}

@keyframes myLoading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.selAll,
.selAll * {
  user-select: text;
}

.selNone,
.selNone * {
  user-select: none;
}

.textEls {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.textEls2 {
  word-break: break-all;
  -webkit-box-orient: vertical;
  white-space: normal;
  display: -webkit-box !important;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.textEls3 {
  word-break: break-all;
  -webkit-box-orient: vertical;
  white-space: normal;
  display: -webkit-box !important;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
}

.textEls4 {
  word-break: break-all;
  -webkit-box-orient: vertical;
  white-space: normal;
  display: -webkit-box !important;
  -webkit-line-clamp: 4;
  overflow: hidden;
  text-overflow: ellipsis;
}

.im-image-hait,
.im-image-hait-new {
  vertical-align: bottom;
  margin-top: 2px;
  height: 20px;
}

.im-image-hait-new {
  cursor: pointer;
}

.hait-text {
  color: #00A2FF;
  white-space: nowrap;
  font-size: 13px;
}

.hait-text.hait-self {
  background: #FEB673;
  color: #000000;
}

.bold {
  font-weight: bold;
}

input {
  background: #FFFFFF;
}

input[type="checkbox"] {
  display: inline-block;
  text-align: center;
  margin-right: 10px;
  border-radius: 2px;
  vertical-align: middle;
  position: relative;
  outline: none;
  appearance: none;
  cursor: pointer;
  height: 12px;
  width: 12px;
}

input[type="checkbox"]::before {
  content: "";
  top: 0;
  left: 0;
  border: none;
  position: absolute;
  background-image: url("/img/mail/check_0.png");
  background-size: 100%;
  width: 100%;
  height: 100%;
}

input[type="checkbox"]:disabled::before {
  content: "";
  top: 0;
  left: 0;
  border: none;
  position: absolute;
  background-image: url("/img/mail/check_0_disable.png");
  background-size: 100%;
  width: 100%;
  height: 100%;
}

input[type="checkbox"]:checked::before {
  content: "";
  top: 0;
  left: 0;
  border: none;
  position: absolute;
  background-image: url("/img/mail/check_1.png");
  background-size: 100%;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  font-weight: bold;
}

input[type="checkbox"]:checked:disabled::before {
  content: "";
  top: 0;
  left: 0;
  border: none;
  position: absolute;
  background-image: url("/img/mail/check_1_disable.png");
  background-size: 100%;
  width: 100%;
  height: 100%;
}

#app {
  width: 100%;
  height: 100%;
}

#fileInput {
  display: none;
}

#copyDivElm,
#copyRenderElm {
  z-index: -1;
  position: fixed;
  left: -10000px;
  top: -10000px;
}

.show-text-hover {
  position: relative;
}

.show-text-hover:hover:after {
  position: absolute;
  top: 35px;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 12px;
  font-size: 12px;
  line-height: 16px;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.8);
  white-space: nowrap;
  border-radius: 2px;
  content: attr(content);
  z-index: 20;
}

.child-win-box {
  width: 100%;
  height: 100%;
}

.child-win-nav {
  padding-left: 16px;
  height: 30px;
  line-height: 30px;
  background: #F0F0F0;
  font-size: 13px;
  font-weight: bold;
}

.child-win-content {
  width: 100%;
  height: calc(100% - 30px);
  overflow: auto;
}

.user-avatar-box {
  width: 38px;
  height: 38px;
  position: relative;
  flex-shrink: 0;
  border-radius: 50%;
}

.user-avatar-box.border {
  border: 1px solid #E7E7E7;
  box-sizing: content-box;
}

.user-avatar-box .avatar-box {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
}

.user-avatar-box .avatar-box.border {
  border-radius: 50%;
}

.user-avatar-box .avatar-box .avatar {
  width: 100%;
}

.user-avatar-box .user-label {
  max-width: 46px;
  height: 12px;
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
}

.user-avatar-box .user-label-text {
  width: 46px;
  height: 12px;
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  line-height: 11px;
  font-size: 9px;
  color: #834107;
  white-space: nowrap;
  overflow: hidden;
}

.dataCopy {
  z-index: -1;
  width: 0;
  height: 0;
  overflow: hidden;
}

.badge {
  min-width: 15px;
  min-height: 15px;
  padding: 0 3px;
  font-size: 10px;
  line-height: 13px;
  text-align: center;
  white-space: nowrap;
  color: #FFFFFF;
  border-radius: 14px;
  background: #f74b32;
  box-shadow: 0px 2px 4px 0px rgb(0 0 0 / 30%);
  border: 1px solid #FFFFFF;
}

.pointer {
  cursor: pointer;
}

.show-arrow {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  vertical-align: middle;
}

.show-arrow:before,
.show-arrow:after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-width: 6px 0 6px 6px;
  border-style: solid;
  border-color: transparent transparent transparent #666666;
}

.show-arrow.arrow-right:before {
  transform: translate(-50%, -50%);
}

.show-arrow.arrow-right:after {
  transform: translate(calc(-50% - 2px), -50%);
  border-color: transparent transparent transparent #FFFFFF;
}

.show-arrow.arrow-left:before,
.show-arrow.arrow-left:after {
  transform: translate(-50%, -50%);
  border-width: 6px 6px 6px 0;
  border-color: transparent #666666 transparent transparent;
}

.show-arrow.arrow-left:after {
  transform: translate(calc(-50% + 2px), -50%);
  border-color: transparent #FFFFFF transparent transparent;
}

.show-arrow.arrow-top:before,
.show-arrow.arrow-top:after {
  transform: translate(-50%, -50%);
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent #666666 transparent;
}

.show-arrow.arrow-top:after {
  transform: translate(-50%, calc(-50% + 2px));
  border-color: transparent transparent #FFFFFF transparent;
}

.show-arrow.arrow-bottom:before,
.show-arrow.arrow-bottom:after {
  transform: translate(-50%, -50%);
  border-width: 6px 6px 0 6px;
  border-color: #666666 transparent transparent transparent;
}

.show-arrow.arrow-bottom:after {
  transform: translate(-50%, calc(-50% - 2px));
  border-color: #FFFFFF transparent transparent transparent;
}

#tipsBox {
  position: fixed;
  padding: 6px 8px;
  font-size: 12px;
  line-height: 16px;
  color: #FFFFFF;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  max-width: 50%;
  max-height: 50%;
  overflow: auto;
  word-break: break-all;
  z-index: -1;
  left: -10000px;
  top: -10000px;
}

.show-loading-modal {
  position: relative;
}

.show-loading-modal:before {
  content: attr(content);
  position: absolute;
  top: calc(50% + 32px);
  left: 50%;
  transform: translate(-50%, -50%);
  color: #DDDDDD;
  z-index: 10;
}

.show-loading-modal:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4) url("/img/waitting.gif") no-repeat center;
  background-size: 32px;
  z-index: 1;
}