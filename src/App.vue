<template>
  <router-view v-slot="{ Component }">
    <!--<keep-alive>-->
    <Component :is="Component" :selUserWorkerNo="selUserWorkerNo"/>
    <!--</keep-alive>-->
  </router-view>
  <!--悬浮提示框-->
  <div id="tipsBox" ref="tipsBoxRef" @mouseenter="tipsBoxHover(1)" @mouseleave="tipsBoxHover(2)"></div>
  <!--选择文件/文件夹区域-->
  <input id="fileInput" ref="fileInputRef" type="file" @change="changeFileInput">
  <!--音频播放-->
  <audio :src="audioObj.src" ref="audioRef"></audio>
  <!--特别关心强提醒弹窗-->
  <div class="chat-tips-box" v-show="concernMsg" @click="openChat(concernMsg.sessionId)">
    <div class="chat-tips-header">
      <div class="user-avatar-box">
        <div class="avatar-box border">
          <img class="avatar" :src="getPerson(concernMsg.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', concernMsg.from, '')">
        </div>
      </div>
      <span class="user-name">{{ getPerson(concernMsg.from).name }}</span>
    </div>
    <div class="chat-tips-content">
      <span class="chat-tips">[特别关心]</span>
      <span class="chat-tips-text" v-html="getPrimaryMsg(concernMsg)"></span>
    </div>
    <div class="chat-tips-footer" v-show="concernMsg.teamName">来自：{{ concernMsg.teamName }}</div>
  </div>
  <!--日程提醒/邀约弹窗/消息平台重要消息-->
  <div id="remindBox" class="selAll" v-show="remindMsg.length>0" ref="remindRef">
    <div class="remind-schedule-tips-box" v-for="(item,key) in remindMsg" :key="item.time" @mouseenter="hoverRemind(key)"
         :class="[item&&item.content?item.content.type:'',hoverRemindKey==key?'curr':'']">
      <div v-if="item&&item.content&&item.content.type">
        <!--日程提醒/邀约弹窗-->
        <div class="remind-schedule-tips-box-detail" v-if="item.content.type=='schedule_invite'||item.content.type=='schedule_remind'">
          <div class="remind-schedule-header">
            <div class="remind-schedule-info-box">
              <div class="user-avatar-box">
                <div class="avatar-box border">
                  <img class="avatar" :src="getPerson(item.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.from, '')">
                </div>
              </div>
              <span class="user-name">{{ item.content.type == "schedule_invite" ? (item.content.schedule.updType ? '日程更新通知' : '日程邀约') : '日程提醒' }}</span>
            </div>
            <div class="remind-schedule-btn-box">
            <span class="remind-schedule-btn remind-schedule-btn-detail"
                  @click="setScheduleInfo($event,item.content.schedule.scheduleId, item.idServer)">详情</span>
              <span class="remind-schedule-btn remind-schedule-btn-close" @click="closeRemind(item.idServer)">关闭</span>
            </div>
          </div>
          <div class="remind-schedule-content">
            <div class="smart-remind-content-box">
              <span class="smart-remind-intr">日程主题:</span>
              <span class="smart-remind-content">
              <span class="smart-remind-content-text"
                    :class="{'update':/1/.test(item.content.schedule.updType)}">{{ (item.content.schedule.title).trim() || "无主题" }}</span>
              <span class="smart-remind-update" v-if="/1/.test(item.content.schedule.updType)">更新</span>
            </span>
            </div>
            <div class="smart-remind-content-box">
              <span class="smart-remind-intr">日程时间:</span>
              <span class="smart-remind-content">
                <span>{{ getScheduleTime(item.content.schedule) }}</span>
              </span>
              <span class="smart-remind-update" v-if="/2/.test(item.content.schedule.updType)">更新</span>
            </div>
            <div v-if="item.content.type=='schedule_remind'&&(item.content.schedule.roomNames || item.content.schedule.customContent)" class="smart-remind-content-box">
              <span class="smart-remind-intr"><span>会</span><span>议</span><span>室:</span></span>
              <span class="smart-remind-content">
                <span v-html="strToHtml(item.content.schedule.roomNames||item.content.schedule.customContent)"></span>
              </span>
            </div>
          </div>
          <div class="remind-schedule-footer" v-if="item.content.type =='schedule_invite'">
            <div v-show="item.content.schedule.showStatus" class="smart-card-btn-box">
              <div class="smart-card-btn smart-card-btn-accept" :class="{'sel':item.content.schedule.status == 1}"
                   @click="setScheduleInfo($event,item.content.schedule.scheduleId,item.idServer,1)">
                <i></i>
                <span>接受</span>
              </div>
              <div class="smart-card-btn smart-card-btn-refuse" :class="{'sel':item.content.schedule.status == 4}"
                   @click="setScheduleInfo($event,item.content.schedule.scheduleId,item.idServer,4)">
                <i></i>
                <span>拒绝</span>
              </div>
              <div class="smart-card-btn smart-card-btn-upcoming" :class="{'sel':item.content.schedule.status == 2}"
                   @click="setScheduleInfo($event,item.content.schedule.scheduleId,item.idServer,2)">
                <i></i>
                <span>待定</span>
              </div>
            </div>
            <div v-show="!item.content.schedule.showStatus" class="schedule-invite-btn-add"
                 @click="setScheduleInfo($event,item.content.schedule.scheduleId,item.idServer,1,true)">加入日程
            </div>
          </div>
        </div>
        <!--消息平台重要消息-->
        <div class="msg-center-box" :class="'msg-center-box-'+item.msgCenterType" v-else-if="item.content.type=='msg-center'||item.content.type=='msg-center-link'">
          <div class="remind-schedule-header">
            <div class="remind-schedule-info-box">
              <div class="user-avatar-box">
                <div class="avatar-box border">
                  <img class="avatar" :src="getPerson(item.from).avatar" alt="" :onerror="avatarError.bind(this, 'p2p', item.from, '')">
                </div>
              </div>
              <span class="user-name">重要消息</span>
            </div>
            <div class="remind-schedule-btn-box">
              <span class="remind-schedule-btn remind-schedule-btn-close" @click="closeRemind(item.idServer)">关闭</span>
            </div>
          </div>
          <div class="remind-schedule-content">
            <div class="msg-center-title">{{ item.content.data.title }}</div>
            <div class="msg-center-content-box" v-for="(item1,key1) in item.content.data.content" :key="item1.type+item.time">
              <div class="msg-center-intr" v-if="item1.value&&item1.type=='text'" v-html="strToHtml(item1.value)"></div>
              <div class="msg-center-link-box" v-if="item1.value&&item1.type=='link'">
                <span>{{ item1.title }}</span>
                <span class="msg-center-link-detail" @click="toMsgCenterLink(item1)">{{ item1.value }}</span>
              </div>
              <div class="msg-center-img-box" v-if="item1.value&&item1.type=='img'">
                <img :src="item1.value" @dblclick="toViewer">
              </div>
              <div class="msg-center-btn-box" v-if="item1.type=='button'&&key1!=item.content.data.content.length-1">
                <div class="msg-center-btn" v-for="(item2,key2) in item1.value" :key="key2" @click="toMsgCenterLink(item2, false, item.idServer)">
                  {{ item2.value }}
                </div>
              </div>
            </div>
          </div>
          <div class="remind-schedule-footer" v-if="item.msgCenterType>0">
            <!--最后一行的按钮显示在底部-->
            <div class="msg-center-content-box" v-for="(item1,key1) in item.content.data.content" :key="item1.type+item.time">
              <div class="msg-center-btn-box" v-if="item1.type=='button'&&key1==item.content.data.content.length-1">
                <div class="msg-center-btn textEls" v-for="(item2,key2) in item1.value" :key="key2" @click="toMsgCenterLink(item2, false, item.idServer)">
                  {{ item2.value }}
                </div>
              </div>
            </div>
            <div class="msg-center-remind-box" v-if="item.content.data.laterRemind == 2">
              <span class="msg-center-remind-intr">稍后提醒:</span>
              <span class="msg-center-remind-btn" @click="remindLater(item, 10)">10分钟</span>
              <span class="msg-center-remind-btn" @click="remindLater(item, 30)">30分钟</span>
              <span class="msg-center-remind-btn" @click="remindLater(item, 60)">1个小时</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!--人员信息弹窗-->
  <ChatUserInfoBox :selUserWorkerNo="selUserWorkerNo" :showUserInfo="showUserInfo" :selUserElm="selUserElm" ref="chatUserInfoRef"
                   @mouseenter="mouseShowUserInfo('show')" @mouseleave="mouseShowUserInfo('hide')"></ChatUserInfoBox>
  <!--霸屏消息-->
  <div id="importantBox" class="selAll win-drag win-no-resize" v-for="(item,key) in importantMsg" :key="item.idServer" v-show="currRouter=='/index/chat'">
    <template v-if="item?.content?.type=='msg-center'||item?.content?.type=='msg-center-link'">
      <div class="important-msg-box" :class="'important-msg-box-'+(item.msgCenterType||0)">
        <div class="important-msg-header">紧急消息</div>
        <div class="important-msg-content win-no-drag">
          <div class="important-msg-title">{{ item.content.data.title }}</div>
          <div class="important-msg-time">
            <span>发布时间：{{ dateFormat(item.time, 'yyyy-MM-dd HH:mm:ss') }}</span>
            <span class="important-msg-time-person">发布人：{{ item.content.data.belongDeptName || "-" }}</span>
          </div>
          <div class="important-msg-content-box-parent">
            <div class="important-msg-content-box" v-for="(item1,key1) in item.content.data.content" :key="item1.type+item.time">
              <div class="important-msg-intr" v-if="item1.value&&item1.type=='text'" v-html="strToHtml(item1.value)"></div>
              <div class="important-msg-link-box" v-if="item1.value&&item1.type=='link'">
                <span>{{ item1.title }}</span>
                <span class="important-msg-link-detail" @click="toMsgCenterLink(item1)">{{ item1.value }}</span>
              </div>
              <div class="important-msg-img-box" v-if="item1.value&&item1.type=='img'">
                <img :src="item1.value" @dblclick="toViewer" @click="clickImg($event,item1)" :onerror="errorImage">
              </div>
              <div class="important-msg-btn-box" v-if="item1.type=='button'&&key1!=item.content.data.content.length-1">
                <div class="important-msg-btn" v-for="(item2,key2) in item.value" :key="key2" @click="toMsgCenterLink(item2, true, item.idServer)">
                  {{ item2.value }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="important-msg-footer win-no-drag">
          <!--最后一行的按钮显示在底部-->
          <div v-if="item.msgCenterType==2||item.msgCenterType==3">
            <div class="important-msg-content-box" v-for="(item1,key1) in item.content.data.content" :key="item1.type+item.time">
              <div class="important-msg-btn-box" v-if="item1.type=='button'&&key1==item.content.data.content.length-1">
                <div class="important-msg-btn textEls" :class="'important-msg-btn-'+item1.value.length" v-for="(item2,key2) in item1.value" :key="key2"
                     @click="toMsgCenterLink(item2, true, item.idServer)">{{ item2.value }}
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="important-msg-content-box">
              <div class="important-msg-btn-box">
                <div class="important-msg-btn" @click="toMsgCenterLink({}, true, item.idServer)">我知道了</div>
              </div>
            </div>
          </div>
          <div class="important-msg-remind-box" v-if="item.content.data.laterRemind == 2">
            <span class="important-msg-remind-intr">稍后提醒:</span>
            <span class="important-msg-remind-btn" @click="remindLater(item, 10, true, item.idServer)">10分钟</span>
            <span class="important-msg-remind-btn" @click="remindLater(item, 30, true, item.idServer)">30分钟</span>
            <span class="important-msg-remind-btn" @click="remindLater(item, 60, true, item.idServer)">1个小时</span>
          </div>
        </div>
      </div>
    </template>
    <template v-else-if="item?.content?.type=='msg-report'">
      <div class="important-msg-box" :class="'important-msg-box-'+(item.msgCenterType||0)">
        <MsgReport :item="item" :type="3"></MsgReport>
        <div class="important-msg-footer win-no-drag">
          <!--最后一行的按钮显示在底部-->
          <div v-if="item.msgCenterType==2||item.msgCenterType==3">
            <div class="important-msg-content-box" v-for="(item1,key1) in item.content.data.content" :key="item1.type+item.time">
              <div class="important-msg-btn-box" v-if="item1.type=='button'&&key1==item.content.data.content.length-1">
                <div class="important-msg-btn textEls" :class="'important-msg-btn-'+item1.value.length" v-for="(item2,key2) in item1.value" :key="key2"
                     @click="toMsgCenterLink(item2, true, item.idServer)">{{ item2.value }}
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="important-msg-content-box">
              <div class="important-msg-btn-box">
                <div class="important-msg-btn" @click="toMsgCenterLink({}, true, item.idServer)">我知道了</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
  <!--搜索弹窗-->
  <CollectCSearch type="2" :isShow="collectView.searchFlag" :sessionInfo="collectView.sessionInfo" :text="collectView.text" :showComponents="showComponents"></CollectCSearch>
  <!--编辑弹窗-->
  <CollectInput :isShow="collectView.editFlag" :sessionInfo="collectView.sessionInfo" :editItem="collectView.editItem" :showComponents="showComponents"
                :changeShow="changeShow" :editType="collectView.editType" :detailType="collectView.detailType"></CollectInput>
  <!--客户备注弹窗-->
  <LyDialog class="customer-dialog" title="备注" :width="334" :height="228" :closeOnClickModal="false" :visible="lyDialogObj.customerRemarkFlag" @confirm="dialogCheckConfirm(1)"
            @close="toggleLyDialog(1, false)">
    <div class="ly-dialog-default-box center">
      <label for="customerRemark" class="ly-dialog-default-label ly-dialog-default-label-width">
        <span class="ly-dialog-default-tips">*</span>
        <span>备注：</span>
      </label>
      <div class="ly-dialog-default-detail">
        <input class="ly-dialog-default-input" type="text" id="customerRemark" placeholder="最多输入20字" maxlength="20" v-model.trim="lyDialogObj.customerRemark">
      </div>
    </div>
  </LyDialog>
  <!--客户举报弹窗-->
  <LyDialog class="customer-dialog" title="举报" :width="364" :height="281" :closeOnClickModal="false" :visible="lyDialogObj.customerReportFlag" @confirm="dialogCheckConfirm(2)"
            @close="toggleLyDialog(2, false)" @click="toggleLyDialogSel(2,false)">
    <ul class="ly-dialog-default-ul">
      <li class="ly-dialog-default-box">
        <label class="ly-dialog-default-label">
          <span class="ly-dialog-default-tips">*</span>
          <span>类型：</span>
        </label>
        <div class="ly-dialog-default-detail">
          <div class="ly-dialog-default-sel-text" :class="{'show':lyDialogObj.customerReportType!=-1,'sel':lyDialogObj.customerReportTypeFlag}"
               @click.stop="toggleLyDialogSel(2)">
            {{ lyDialogObj.customerReportType == -1 ? "请选择" : lyDialogObj.customerReportTypeList[lyDialogObj.customerReportType] }}
          </div>
          <ul class="ly-dialog-default-sel-ul" v-show="lyDialogObj.customerReportTypeFlag">
            <li class="ly-dialog-default-sel-li" v-for="(item,key) in lyDialogObj.customerReportTypeList" :key="key"
                @click.stop="toggleLyDialogSel(2,false,key)">{{ item }}
            </li>
          </ul>
        </div>
      </li>
      <li class="ly-dialog-default-box">
        <label class="ly-dialog-default-label">
          <span class="ly-dialog-default-tips">*</span>
          <span>类型原因：</span>
        </label>
        <div class="ly-dialog-default-detail">
          <textarea class="ly-dialog-default-textarea" placeholder="描述一下为什么举报用户，5~30字" maxlength="30" v-model.trim="lyDialogObj.customerReportReason"></textarea>
          <span class="ly-dialog-default-textarea-text">{{ lyDialogObj.customerReportReason.length }}/30</span>
        </div>
      </li>
      <li class="ly-dialog-default-box">
        <label class="ly-dialog-default-label"></label>
        <div class="ly-dialog-default-detail">举报后，客户与您的<i class="red">聊天记录将会被删除</i>，该客户将不再显示在您的客户咨询及潜在客户列表。</div>
      </li>
    </ul>
  </LyDialog>
  <!--创建讨论组-->
  <InviteBox ref="inviteBoxRef"></InviteBox>
  <!--代理线路弹窗-->
  <Proxy :isShow="isProxyShow" :showComponents="showComponents"></Proxy>
  <!--提货验证码弹窗-->
  <NetCheck></NetCheck>
  <!--清理文件-->
  <Clear ref="clearRef"></Clear>
</template>
<script>
import {jQueryMethod} from "@comp/schedule/jquery";
import {nextTick, onMounted, onUnmounted, ref, watch} from "vue";
import {useStore} from "vuex"
import {useRouter} from "vue-router";
import {toast, loading} from "@comp/ui";
import {
  getAllComputerInfo, getBaseComputerInfo, debounce, avatarError, getScheduleLocalStatus, getScheduleTime, deepClone, dateFormat, openViewer, strToHtml,
  getNetConnectInfo, getBounding, clickImg,
} from "@utils";
import {fcwReportApi, delNimFriendApi, leaveLatentCustomerApi} from "@utils/net/api"
import ChatUserInfoBox from "@comp/chat/ChatUserInfoBox";
import CollectCSearch from "@comp/ui/comps/CollectSearch";
import CollectInput from "@comp/input/Collect";
import Proxy from "@comp/ui/comps/Proxy";
import NetCheck from "@comp/ui/comps/NetCheck";
import InviteBox from "@comp/ui/comps/InviteBox";
import LyDialog from "@comp/ui/comps/LyDialog";
import Clear from "@comp/update/Clear";
import MsgReport from "@comp/msg/MsgReport";
import '@static/css/scheduleModal.scss'

export default {
  components: {ChatUserInfoBox, CollectCSearch, CollectInput, Proxy, NetCheck, InviteBox, LyDialog, Clear, MsgReport},
  setup() {
    jQueryMethod();
    const store = useStore();
    const router = useRouter();
    // 初始化日志
    store.commit("setLogger");
    // 配置文件
    let config = store.getters.getConfig.config;
    // 设置水印
    onMounted(() => {
      store.commit("setWaterMark", {window: window});
      // 清除缓存标识
      if (window.global.clearAllFlag) {
        window.global.clearAllFlag = false;
        remote.store.commit("setEmit", {type: "clearBox", value: Date.now()});
      }
    });
    // 卸载去除监听
    onUnmounted(() => {
      // 全局按键监听
      document.removeEventListener("keydown", keydownEvent);
      // 全局点击监听
      document.removeEventListener("click", clickEvent);
      // 全局鼠标按下监听
      document.removeEventListener("mousedown", mousedownEvent);
      // 全局鼠标释放监听
      document.removeEventListener("mouseup", mouseupEvent);
      // 全局鼠标移动监听
      document.removeEventListener("mousemove", mousemoveEvent);
      // 全局鼠标滚轮监听
      document.removeEventListener("mousewheel", mousewheelEvent);
      // 全局双击监听
      document.removeEventListener("dblclick", dblclickEvent);
      // 全局右键监听
      document.removeEventListener("contextmenu", contextmenuEvent);
    })
    // 设置版本号
    store.commit("setConfigVersion");
    // 语音播放元素
    let audioRef = ref();
    // 语音播放对象
    let audioObj = ref({
      src: "",
      timer: "",
    });
    // 创建讨论组组件实例
    let inviteBoxRef = ref(null);
    // 选择查看的用户工号
    let selUserWorkerNo = ref("");
    // 选择查看的元素位置
    let selUserElm = ref({});
    // 隐藏弹窗定时器
    let showUserTimer = "";
    // 查看用户信息工号临时数据
    let tempAccount = "";
    // 显示用户信息标识
    let ShowUserInfoFlag = false;
    // 判断是否悬浮在用户信息内
    let hoverShowUserInfo = "";
    // 客户弹窗数据
    let lyDialogObj = ref({
      // 客户咨询id
      customerAccount: "",
      // 客户咨询备注
      customerRemarkFlag: false,
      customerRemark: "",
      // 客户咨询举报
      customerReportFlag: false,
      customerReportType: -1,
      customerReportTypeList: ["同事", "同行", "微商", "恶意骚扰", "其他"],
      customerReportTypeFlag: false,
      customerReportReason: "",
      customerTeamId: "",//举报的群id
    });
    // 提示框定时器
    let tipsBoxTimer = "";
    // 提示框元素
    let tipsBoxRef = ref();
    // 清理文件元素
    let clearRef = ref();
    // 当前页面路径
    let currRouter = ref(router.currentRoute.value.path);
    watch(() => router.currentRoute.value.path,
      (newValue, oldValue) => {
        currRouter.value = newValue;
      }, {
        deep: true
      }
    )
    // 打开文件选择框
    let fileInput = ref({
      nwsaveas: "",// 另存文件名
      multiple: false,// 是否多选
      accept: "*",// 选择文件类型
      nwdirectory: '',// 选择文件夹
      nwworkingdir: "",
    });
    // 打开文件元素
    let fileInputRef = ref();
    watch(() => store.state.emit.fileInput,
      (newValue, oldValue) => {
        // 选择类型
        if (newValue.accept) {
          fileInputRef.value.setAttribute("accept", newValue.accept);
        } else {
          fileInputRef.value.removeAttribute("accept");
        }
        // 另存为
        if (newValue.nwsaveas) {
          fileInputRef.value.setAttribute("nwsaveas", newValue.nwsaveas);
        } else {
          fileInputRef.value.removeAttribute("nwsaveas");
        }
        // 多选
        if (newValue.multiple) {
          fileInputRef.value.setAttribute("multiple", true);
        } else {
          fileInputRef.value.removeAttribute("multiple");
        }
        // 选择文件夹
        if (newValue.nwdirectory) {
          fileInputRef.value.setAttribute("nwdirectory", true);
        } else {
          fileInputRef.value.removeAttribute("nwdirectory");
        }
        nextTick(() => {
          fileInputRef.value.click();
        });
      }, {
        deep: true
      }
    );
    // 特别关心消息
    let concernMsg = ref("");
    // 强提醒选中下标
    let hoverRemindKey = ref(1);
    watch(() => store.state.emit.concernMsg,
      (newValue, oldValue) => {
        concernMsg.value = newValue;
        if (newValue) {
          // 3s后关闭强提醒
          debounce({
            timerName: "hideChatTipsBox",
            time: 3000,
            fnName: hideChatTipsBox,
          })
        }
      }, {
        deep: true
      }
    );
    // 离开群回调隐藏特别关心强提醒
    watch(() => store.state.emit.leaveTeam,
      (newValue, oldValue) => {
        if (newValue.id == concernMsg.value.sessionId) {
          hideChatTipsBox();
        }
      }, {
        deep: true
      }
    );
    // 强提醒消息
    let remindRef = ref();
    let remindMsg = ref([]);
    watch(() => store.state.emit.remindMsg,
      (newValue, oldValue) => {
        let list = deepClone(newValue);
        list.map(item => {
          // 智能日程消息获取本地最新状态
          if (item.content && item.content.type == "schedule_invite" && item.content.schedule) {
            item.content.schedule.msgTime = item.time;
            item.content.schedule = getScheduleLocalStatus(item.content.schedule);
          }
        });
        remindMsg.value = list;
        // 计算弹窗高度
        calcRemindBoxHeight();
      }, {
        deep: true
      }
    );
    // 霸屏消息
    let importantMsg = ref([]);
    watch(() => store.state.emit.importantMsg,
      (newValue, oldValue) => {
        importantMsg.value = newValue;
      }, {
        deep: true
      }
    );
    // 代理线路弹窗
    let isProxyShow = ref(false);
    // 搜藏弹窗对象
    let collectView = ref({
      searchFlag: false,// 显示搜索组件
      editFlag: false,// 显示编辑组件
      text: "",// 搜索内容
      sessionInfo: {},// 搜索用户信息
      editItem: {},// 编辑对象
      editType: "",// 1我的话术2我的收藏
      detailType: "",// 1新增笔记
    });
    watch(() => store.state.emit.showComponents,
      (newValue, oldValue) => {
        showComponents(newValue);
      }, {
        deep: true
      }
    );
    let currentWindow = store.getters.getCurrentWindow();
    // 重启后设置当前窗口在最前
    currentWindow.focus();
    // 全局按键监听
    document.addEventListener("keydown", keydownEvent);
    // 全局点击监听
    document.addEventListener("click", clickEvent);
    // 全局鼠标按下监听
    document.addEventListener("mousedown", mousedownEvent);
    // 全局鼠标释放监听
    document.addEventListener("mouseup", mouseupEvent);
    // 全局鼠标移动监听
    document.addEventListener("mousemove", mousemoveEvent);
    // 全局鼠标滚轮监听
    document.addEventListener("mousewheel", mousewheelEvent);
    // 全局双击监听
    document.addEventListener("dblclick", dblclickEvent);
    // 全局右键监听
    document.addEventListener("contextmenu", contextmenuEvent);

    // 全局按键事件
    function keydownEvent(e) {
      store.commit("setEmit", {type: "keydown", value: e});
      // 调试模式
      if (e.key == "Escape") {
        if (router.currentRoute.value.path == "/child/childChat") {
          // 聊天子窗口关闭
          store.commit("setWindowClose", currentWindow.cWindow.id);
        } else {
          store.commit("setWindowMin", currentWindow.cWindow.id);
        }
      } else if (e.ctrlKey && e.key.toLowerCase() == "f") {
        store.commit("setEmit", {type: "searchFocus", value: Date.now()});
      } else if (e.key == "F12") {
        // 屏蔽调试工具
        e.stopPropagation();
        e.preventDefault();
        return false;
      } else if (e.ctrlKey && e.key.toLowerCase() == "o") {
        // 发货验证码弹窗
        store.commit("setEmit", {type: "netCheck", value: Date.now()});
      } else if (e.altKey && e.shiftKey && e.ctrlKey && e.key == "P") {
        // 显示调试工具
        currentWindow.showDevTools();
        remote.store.dispatch("uploadZxp", {type: 4});
      }
      if (store.getters.getEmit.toggleSession && (e.key == "ArrowUp" || e.key == "ArrowDown")) {
        // 禁止列表滚动
        e.stopPropagation();
        e.preventDefault();
        return false;
      }
    }

    // 全局点击事件
    function clickEvent(e) {
      remote.store.commit("setEmit", {type: "imActivity", value: Date.now()});
      // 点击会话支持上下切换会话
      if (/chat-ul-scroll/.test(e.target.className) || e.path.some(elm => /chat-ul-scroll/.test(elm.className))) {
        store.commit("setEmit", {type: "toggleSession", value: true});
      } else {
        store.commit("setEmit", {type: "toggleSession", value: false});
      }
      if (!store.getters.getIsClick) {
        debounce({
          timerName: "setIsClick",
          time: 300,
          fnName: function () {
            store.commit("setIsClick", true);
          }
        });
        return;
      }
      if (e.target.nodeName != "INPUT") {
        store.commit("setEmit", {type: "click", value: e});
      }
      store.commit("setEmit", {type: "allClick", value: e});
      // 隐藏人员信息弹窗
      let showUserInfoEmit = store.getters.getEmit["showUserInfo"];
      if (showUserInfoEmit && showUserInfoEmit.selUserWorkerNo) {
        if (ShowUserInfoFlag) {
          store.commit("setEmit", {type: "showUserInfo", value: {selUserWorkerNo: "", selUserElm: ""}});
        } else {
          ShowUserInfoFlag = true;
        }
      }
    }

    // 全局鼠标按下事件
    function mousedownEvent(e) {
      store.commit("setEmit", {type: "mousedown", value: Date.now()});
      if (e.path.some(elm => /win-drag/.test(elm.className)) && !e.path.some(elm => /win-no-drag/.test(elm.className))) {
        let bounds = currentWindow.getBounds();
        remote.global.dragObj = {x: e.x, y: e.y, width: bounds.width, height: bounds.height};
      }
    }

    // 全局鼠标释放事件
    function mouseupEvent(e) {
      store.commit("setEmit", {type: "mouseup", value: Date.now()});
      remote.global.dragObj = "";
    }

    // 全局鼠标移动事件
    function mousemoveEvent(e) {
      remote.store.commit("setEmit", {type: "imActivity", value: Date.now()});
      store.commit("setEmit", {type: "mousemove", value: e});
      // 移动窗口
      if (remote.global.dragObj && (Math.abs(e.screenX - remote.global.dragObj.x) > 5 || Math.abs(e.screenY - remote.global.dragObj.y) > 5)) {
        if (!e.which) {
          remote.global.dragObj = "";
        }
        if (currentWindow.isFullscreen) {
          store.commit("setWindowCancelMax", currentWindow.cWindow.id);
        }
        currentWindow.moveTo(e.screenX - remote.global.dragObj.x, e.screenY - remote.global.dragObj.y)
      }
    }

    // 全局鼠标滚轮事件
    function mousewheelEvent(e) {
      remote.store.commit("setEmit", {type: "imActivity", value: Date.now()});
      store.commit("setEmit", {type: "mousewheel", value: e});
    }

    // 全局双击事件
    function dblclickEvent(e) {
      if (e.path.some(elm => /win-drag/.test(elm.className)) && !e.path.some(elm => /win-no-drag/.test(elm.className)) && !e.path.some(elm => /win-no-resize/.test(elm.className))) {
        if (currentWindow.isFullscreen) {
          store.commit("setWindowCancelMax", currentWindow.cWindow.id);
        } else {
          store.commit("setWindowMax", currentWindow.cWindow.id);
        }
      }
    }

    // 全局右键事件
    function contextmenuEvent(e) {
      e.preventDefault();
      e.stopPropagation();
      return false;
    }

    // 设置全局定时器
    store.commit("setGlobalTimer");

    // 设置电脑基础信息
    store.commit("setBaseComputerInfo", getBaseComputerInfo());
    getAllComputerInfo().then(function (systemInfoData) {
      // 设置电脑基础信息
      store.commit("setComputerInfo", systemInfoData);
      store.commit("loadTjs", {window: window});
    });
    getNetConnectInfo().then(function (netComputerInfo) {
      // 设置电脑网络连接信息
      store.commit("setNetComputerInfo", netComputerInfo);
    });

    // 监听全局窗口大小变动
    watch(() => store.state.emit.resize,
      (newValue, oldValue) => {
        // 改变大小设置主窗口大小
        if (router?.currentRoute?.value?.matched?.[0]?.path == "/index") {
          let userInfo = store.getters.getUserInfo;
          if (!userInfo || !userInfo.workerNo) {
            return;
          }
          if (store.getters.getEmit.initWinSize) {
            store.commit("setEmit", {type: "initWinSize", value: false});
          } else {
            if (newValue.resizeFlag) {
              // 设置当前窗口大小
              store.commit("setWindowSizeType", {...store.getters.getWindowSizeType, width: currentWindow.getBounds().width, height: currentWindow.getBounds().height});
              // 拖拽结束5s后再同步到服务器
              debounce({
                timerName: "resizeWin",
                time: 5 * 1000,
                fnName: function () {
                  // 设置服务器
                  store.commit("setWindowSizeType", {...store.getters.getWindowSizeType, width: currentWindow.getBounds().width, height: currentWindow.getBounds().height});
                  store.dispatch("setModifySettings", {type: 1, key: config.settings.type12, value: store.getters.getWindowSizeType});
                }
              });
            }
          }
          // 通知更新消息
          store.commit("setEmit", {type: "reloadMsg", value: {id: store.getters.getCurrentSession.id, time: Date.now()}});
          calcRemindBoxHeight();
        }
      }, {
        deep: true
      }
    );

    // 监听全局查看用户详情
    watch(() => store.state.emit.showUserInfo,
      (newValue, oldValue) => {
        showUserInfo(newValue.selUserWorkerNo, newValue.selUserElm, newValue.showTop, newValue.showLeft);
        // 重置状态
        if (newValue.selUserWorkerNo) {
          ShowUserInfoFlag = false;
        }
      }, {
        deep: true
      }
    );

    // 创建讨论组
    watch(() => store.state.emit.inviteBox,
      (newValue, oldValue) => {
        inviteBoxRef.value.inviteBoxInit(newValue);
      }, {
        deep: true
      }
    );

    // 显示用户详情弹窗
    function showUserInfo(account, e, showTop, showLeft) {
      let showUserInfoEmit = store.getters.getEmit["showUserInfo"];
      if (showUserInfoEmit.hover) {
        mouseShowUserInfo(account, e, showTop, showLeft);
      } else {
        if (isNaN(account) && !new RegExp(config.ai).test(account) && !new RegExp(config.jm).test(account)) {
          account = "";
        }
        selUserWorkerNo.value = account;
        selUserElm.value = e;
      }
    }

    // 鼠标悬浮显示用户详情弹窗
    function mouseShowUserInfo(account, e, showTop, showLeft) {
      let showUserInfoEmit = store.getters.getEmit["showUserInfo"];
      if (showUserInfoEmit.hover) {
        if (account && (!isNaN(account) || new RegExp(config.ai).test(account) || new RegExp(config.jm).test(account) || account == "show")) {
          // 清除隐藏弹窗定时器
          clearTimeout(showUserTimer);
          showUserTimer = "";
          // 移入弹窗不做处理
          if (account == "show") {
            hoverShowUserInfo = true;
            return;
          }
          tempAccount = account;
          debounce({
            timerName: "showUserInfo",
            time: 100,
            fnName: function () {
              if (tempAccount) {
                selUserWorkerNo.value = tempAccount;
                selUserElm.value = {x: e.clientX, y: e.clientY, noPadding: true, showTop: showTop, showLeft: showLeft};
              }
            }
          });
        } else if (account == "hide") {
          hoverShowUserInfo = false;
          tempAccount = "";
          selUserWorkerNo.value = "";
          selUserElm.value = "";
        } else {
          // 隐藏弹窗
          showUserTimer = setTimeout(() => {
            if (!hoverShowUserInfo) {
              tempAccount = "";
              selUserWorkerNo.value = "";
              selUserElm.value = "";
            }
          }, 0);
        }
      }
    }

    // 隐藏特别关心强提醒弹窗
    function hideChatTipsBox() {
      store.commit("setEmit", {type: "concernMsg", value: ""});
    }

    // 获取用户信息
    function getPerson(account) {
      return store.getters.getPersons(account);
    }

    // 获取消息简要
    function getPrimaryMsg(msg) {
      return store.getters.getPrimaryMsg({msg: msg, primaryType: 2});
    }

    // 打开会话
    function openChat(id) {
      store.dispatch("setCurrentSession", {id: id, type: "open"});
      hideChatTipsBox();
    }

    // 设置日程参数
    function setScheduleInfo(e, id, idServer, changeStatus, join) {
      let param = {
        id: id,
        idServer: idServer,
        x: e.pageX,
        y: e.pageY,
        changeStatus: changeStatus,
        join: join,
      }
      showSchedule(param);
    }

    // 查看日程详情
    function showSchedule(param) {
      // 获取用户信息
      let userInfo = store.getters.getUserInfo;
      param.workerId = userInfo.workerId;
      param.workerName = userInfo.name;
      if (param.join) {
        param.addParticipateIds = userInfo.workerId;
      }
      param.type = 3;
      param.callback = function (result, status) {
        if (result == "feedback") {
          setScheduleStatus(param.id, status, param.idServer);
          if (param.idServer) {
            closeRemind(param.idServer)
          }
        }
      }
      try {
        new store.getters.getScheduleModal(param);
      } catch (e) {}
    }

    // 监听全局查看日程详情
    watch(() => store.state.emit.showScheduleInfo,
      (newValue, oldValue) => {
        showSchedule(deepClone(newValue));
      }, {
        deep: true
      }
    );

    // 设置日程状态
    function setScheduleStatus(id, status, idServer) {
      store.dispatch("setScheduleStatus", {id: id, status: status, idServer: idServer});
    }

    // 关闭强提醒
    function closeRemind(idServer) {
      store.dispatch("setRemindMsg", {type: "del", idServer: idServer});
    }

    // 强提醒选中
    function hoverRemind(key) {
      hoverRemindKey.value = key;
      calcRemindBoxHeight();
    }

    // 计算弹窗高度
    function calcRemindBoxHeight() {
      nextTick(() => {
        let currHeight = 0;
        let remindBoxList = remindRef.value.querySelectorAll(".remind-schedule-tips-box");
        for (let i = 0; i < remindBoxList.length; i++) {
          remindBoxList[i].style.maxHeight = "";
        }
        for (let i = 0; i < remindBoxList.length; i++) {
          if (/curr/.test(remindBoxList[i].className)) {
            currHeight = remindBoxList[i].clientHeight;
          }
        }
        let maxHeight = currentWindow.getBounds().height - 150 - 40 - 4 * remindBoxList.length - currHeight;
        for (let i = 0; i < remindBoxList.length; i++) {
          remindBoxList[i].style.maxHeight = maxHeight / (remindBoxList.length - 1) + "px";
        }
      });
    }

    // 监听全局语音播放
    watch(() => store.state.audioObj,
      (newValue, oldValue) => {
        let src = "";
        let dur = 0;
        if (newValue.src) {
          src = newValue.src;
          src += src.indexOf("?") > -1 ? "&" : "?";
          src += "audioTrans&type=mp3";
          dur = newValue.dur;
        }
        audioObj.value.src = src;
        nextTick(() => {
          if (audioRef.value.timer) {
            clearTimeout(audioRef.value.timer);
            audioRef.value.timer = "";
          }
          if (src) {
            audioRef.value.play();
            audioRef.value.timer = setTimeout(() => {
              audioRef.value.timer = "";
              // 停止播放
              remote.store.dispatch("setAudioObj", {audioEnd: true});
            }, dur);
          }
        });
      }, {
        deep: true
      }
    );

    // 监听打开弹窗
    watch(() => store.state.emit.customerDialog,
      (newValue, oldValue) => {
        toggleLyDialog(newValue.type, true, newValue);
      }, {
        deep: true
      }
    );

    // 监听悬浮提示框
    watch(() => store.state.emit.tipsBox,
      (newValue, oldValue) => {
        // 显示提示框
        if (newValue.content) {
          tipsBoxHover(1);
          // 设置提示框内容
          let elmBounds = getBounding(newValue.e.currentTarget);
          let defaultPadding = 6;
          let top = elmBounds.top + newValue.e.currentTarget.clientHeight + defaultPadding;
          tipsBoxRef.value.innerHTML = newValue.content;
          // 获取提示框宽度和设置渲染位置
          nextTick(() => {
            let elmLeft = elmBounds.left + newValue.e.currentTarget.clientWidth / 2;
            let nodeLeft = tipsBoxRef.value.clientWidth / 2;
            // 判断元素上边距
            if (newValue.pos == "top") {
              top = elmBounds.top - tipsBoxRef.value.clientHeight - defaultPadding;
            }
            if (top < 0) {
              top = defaultPadding;
            } else if (top + tipsBoxRef.value.clientHeight > window.document.body.clientHeight) {
              top = window.document.body.clientHeight - tipsBoxRef.value.clientHeight - defaultPadding;
            }
            tipsBoxRef.value.style.top = top + "px";
            // 判断元素左边距
            let left = elmLeft - nodeLeft;
            if (left < 0) {
              left = defaultPadding;
            } else if (left + tipsBoxRef.value.clientWidth > window.document.body.clientWidth) {
              left = window.document.body.clientWidth - tipsBoxRef.value.clientWidth - defaultPadding;
            }
            tipsBoxRef.value.style.left = left + "px";
            tipsBoxRef.value.style.zIndex = 99;
          });
        } else {
          // 移出隐藏提示框
          tipsBoxHover(2);
        }
      }, {
        deep: true
      }
    );

    // 监听清理文件弹窗
    watch(() => store.state.emit.clearBox,
      (newValue, oldValue) => {
        clearRef.value.clear();
      }, {
        deep: true
      }
    );


    // 跳转消息平台,isImportant-true霸屏弹窗、false强提醒
    function toMsgCenterLink(item, isImportant, idServer) {
      if (isImportant) {
        store.dispatch("setImportantMsg", {type: "del", idServer: idServer});
      } else {
        // 关闭对应强提醒
        closeRemind(idServer);
      }
      store.dispatch("toMsgCenterLink", {item: item});
    }

    // 跳转消息平台
    function remindLater(item, minute, isImportant, idServer) {
      if (isImportant) {
        store.dispatch("setImportantMsg", {type: "del", idServer: idServer});
      }
      store.dispatch("remindLater", {item: item, minute: minute});
    }

    // 图片加载失败
    function errorImage(e) {
      e.target.setAttribute("src", `/img/image_reload_min.png`);
    }

    // 打开查看大图/视频
    function toViewer(e) {
      let thisElm = e.target;
      if (thisElm.src == location.origin + "/img/image_reload_min.png") {
        return;
      }
      let imgList = [{src: thisElm.src, dataSrc: thisElm.src, w: thisElm.naturalWidth, h: thisElm.naturalHeight}];
      let thisIndex = 0;
      openViewer(imgList, thisIndex, thisElm.naturalWidth, thisElm.naturalHeight);
    }

    // 选择文件/文件夹
    function changeFileInput(e) {
      let emitFileInput = store.getters.getEmit["fileInput"];
      if (emitFileInput && emitFileInput.done) {
        emitFileInput.done(e.target.files)
      }
      e.target.value = "";
    }

    // 显示组件type-1搜索2编辑3代理线路,key-1显示
    function showComponents(param) {
      let {type, key, text, sessionInfo, editItem, editType, detailType} = param;
      if (type == 1) {
        collectView.value.searchFlag = key == 1 ? true : false;
        if (key != 1) {
          collectView.value.text = "";
        }
      } else if (type == 2) {
        collectView.value.editFlag = key == 1 ? true : false;
        if (collectView.value.editFlag) {
          collectView.value.editType = String(editType || "");
        }
      }
      if (text != undefined) {
        collectView.value.text = text;
      }
      if (sessionInfo) {
        collectView.value.sessionInfo = sessionInfo;
      }
      if (editItem) {
        collectView.value.editItem = editItem;
      } else if (type == 3) {
        isProxyShow.value = key == 1 ? true : false;
      }
      collectView.value.detailType = String(detailType || "");
    }

    // 改变编辑框显示
    function changeShow(flag) {
      collectView.value.editFlag = flag;
    }

    // 操作框确认
    async function dialogCheckConfirm(type) {
      if (type == 1) {
        // 备注
        let res = await store.dispatch("fcwRemark", {accid: lyDialogObj.value.customerAccount, remark: lyDialogObj.value.customerRemark});
        if (res?.success) {
          toggleLyDialog(type, false);
        } else {
          toast({title: res.errorMsg || "系统错误", type: 2});
        }
      } else if (type == 2) {
        // 举报
        if (lyDialogObj.value.customerReportType == -1) {
          toast({title: "请选择举报类型", type: 2});
          return;
        }
        if (lyDialogObj.value.customerReportReason.length < 5) {
          toast({title: "请输入举报类型原因5~30字", type: 2});
          return;
        }
        loading();
        let reason = `${lyDialogObj.value.customerReportTypeList[lyDialogObj.value.customerReportType]}(${lyDialogObj.value.customerReportReason})`
        let res = await fcwReportApi({
          msgBody: JSON.stringify({
            accid: lyDialogObj.value.customerAccount,
            reason: reason,
            type: 2
          })
        });
        loading().hide();
        if (res.success) {
          // 放弃跟进
          leaveLatentCustomerApi({
            tag: lyDialogObj.value.customerReportTypeList[lyDialogObj.value.customerReportType],
            type: lyDialogObj.value.customerTeamId ? 2 : 1,
            yxId: lyDialogObj.value.customerTeamId ? lyDialogObj.value.customerTeamId : lyDialogObj.value.customerAccount
          });
          if (lyDialogObj.value.customerTeamId) {
            // 举报进线客户讨论组-删除群会话
            store.dispatch("removeSession", `team-${lyDialogObj.value.customerTeamId}`);
          } else {
            // 举报客户-删除好友
            if (store.getters.getNimFriend({account: lyDialogObj.value.customerAccount})) {
              delNimFriendApi({
                msgBody: JSON.stringify({
                  accid: store.getters.getUserInfo.workerNo,
                  faccid: lyDialogObj.value.customerAccount,
                })
              });
            }
            // 举报后删除会话
            if (store.getters.getSessions({id: `p2p-${lyDialogObj.value.customerAccount}`})) {
              store.dispatch("removeSession", `p2p-${lyDialogObj.value.customerAccount}`);
            }
          }
          toggleLyDialog(type, false);
        }
        toast({title: res.success ? "举报成功" : (res.errorMsg || "系统错误"), type: res.success ? 1 : 2});
      }
    }

    // 切换显示弹窗-1备注，2举报
    function toggleLyDialog(type, flag, value) {
      if (type == 1 || type == 2) {
        if (value && value.account) {
          // 设置修改备注/举报的用户账号
          lyDialogObj.value.customerAccount = value.account;
        }
        if (value && value.teamId) {
          // 设置举报群的id
          lyDialogObj.value.customerTeamId = value.teamId;
        }
      }
      if (type == 1) {
        if (value) {
          lyDialogObj.value.customerRemark = value.name;
        }
        if (flag != null) {
          lyDialogObj.value.customerRemarkFlag = flag;
        } else {
          lyDialogObj.value.customerRemarkFlag = !lyDialogObj.value.customerRemarkFlag;
        }
      } else if (type == 2) {
        if (flag != null) {
          lyDialogObj.value.customerReportFlag = flag;
        } else {
          lyDialogObj.value.customerReportFlag = !lyDialogObj.value.customerReportFlag;
        }
        // 隐藏窗口重置数据
        if (!lyDialogObj.value.customerReportFlag) {
          lyDialogObj.value.customerReportType = -1;
          lyDialogObj.value.customerReportReason = "";
          lyDialogObj.value.customerTeamId = "";
        }
      }
    }

    // 切换显示弹窗选择类型
    function toggleLyDialogSel(type, flag, value) {
      if (type == 2) {
        if (value != null) {
          // 选择的类型
          lyDialogObj.value.customerReportType = value;
        }
        // 客户举报窗口选择类型
        if (flag != null) {
          lyDialogObj.value.customerReportTypeFlag = flag;
        } else {
          lyDialogObj.value.customerReportTypeFlag = !lyDialogObj.value.customerReportTypeFlag;
        }
      }
    }

    // 提示框悬浮 type1鼠标移入2移出
    function tipsBoxHover(type) {
      if (type == 1) {
        if (tipsBoxTimer) {
          clearTimeout(tipsBoxTimer);
        }
      } else {
        tipsBoxTimer = debounce({
          timerName: "tipsBox",
          time: 100,
          fnName: function () {
            tipsBoxRef.value.innerHTML = "";
            tipsBoxRef.value.style.zIndex = -1;
          }
        });
      }
    }

    return {
      audioRef,
      audioObj,
      inviteBoxRef,
      currRouter,
      concernMsg,
      remindRef,
      remindMsg,
      importantMsg,
      selUserWorkerNo,
      selUserElm,
      hoverRemindKey,
      fileInput,
      fileInputRef,
      collectView,
      isProxyShow,
      lyDialogObj,
      clearRef,
      tipsBoxRef,

      avatarError,
      getScheduleTime,
      hideChatTipsBox,
      getPerson,
      getPrimaryMsg,
      openChat,
      showUserInfo,
      mouseShowUserInfo,
      setScheduleInfo,
      closeRemind,
      setScheduleStatus,
      hoverRemind,
      toMsgCenterLink,
      remindLater,
      dateFormat,
      clickImg,
      errorImage,
      toViewer,
      strToHtml,
      changeFileInput,
      showComponents,
      changeShow,
      dialogCheckConfirm,
      toggleLyDialog,
      toggleLyDialogSel,
      tipsBoxHover,
    }
  }
};
</script>
<style lang="scss">
.highlight {
  color: $styleColor;
}
</style>
<style scoped lang="scss">
#app {
  audio {
    display: none;
  }

  .chat-tips-box {
    width: 330px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border: 1px solid #E7E7E7;
    position: fixed;
    top: 70px;
    right: 20px;
    font-size: 12px;
    padding: 10px 16px;
    box-sizing: border-box;
    cursor: pointer;

    .chat-tips-header,
    .chat-tips-content {
      display: flex;
      align-items: center;
    }

    .chat-tips-header {
      font-weight: 600;

      .user-avatar-box {
        width: 24px !important;
        height: 24px !important;
        margin-right: 8px;
        border-radius: 50% !important;
      }
    }

    .chat-tips-content {
      font-size: 14px;
      margin: 8px 0 4px;

      ::v-deep(.im-emoji) {
        width: 15px;
        height: 15px;
      }

      .chat-tips {
        color: #FE7801;
        margin-right: 2px;
        flex-shrink: 0;
      }
    }

    .chat-tips-footer {
      color: #999999;
    }

    .chat-tips-text {
      flex: 1;
      height: 19px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  /*强提醒弹窗*/
  #remindBox {
    position: fixed;
    top: 150px;
    right: 20px;
    display: flex;
    flex-direction: column;
    max-height: calc(100% - 150px - 40px);
    z-index: 18;

    .remind-schedule-tips-box {
      overflow-y: hidden;
      margin-bottom: 4px;
      box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.25);
      border: 1px solid #e7e7e7;
      position: relative;
      flex: none;

      &.curr {
        max-height: none !important;
      }

      .remind-schedule-tips-box-detail {
        width: 330px;
        background: #FFFFFF;
        padding: 12px;
        border-left: 2px solid rgba(236, 56, 42, 0.98);
      }

      .remind-schedule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .remind-schedule-info-box {
        display: flex;
        align-items: center;
      }

      .user-avatar-box {
        width: 24px !important;
        height: 24px !important;
        margin-right: 8px;
      }

      .user-name {
        font-weight: 800;
      }

      .remind-schedule-btn-box {
        display: flex;
        align-items: center;
      }

      .remind-schedule-btn {
        width: 44px;
        height: 24px;
        border-radius: 2px;
        border: 1px solid #E7E7E7;
        color: #666666;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-left: 8px;
        cursor: pointer;

        &:hover {
          background: #F6F6F6;
        }
      }

      .remind-schedule-content {
        margin: 12px 0;
      }

      &.schedule_remind {
        .remind-schedule-content {
          margin-bottom: 0;
        }
      }

      &.msg-center-link,
      &.msg-center {
        .remind-schedule-header {
          position: absolute;
          top: 0px;
          left: 0px;
          width: 100%;
          z-index: 1;
          background: #FFFFFF;
          padding: 12px;
        }

        .remind-schedule-content {
          margin: 0;
          max-height: 280px;
          overflow-y: auto;
          padding: 0 12px;

          &::-webkit-scrollbar {
            width: 6px;
          }
        }

        .remind-schedule-footer {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          background: #FFFFFF;
          border-top: 1px solid #E7E7E7;
          padding: 0 12px;

          .msg-center-btn-box {
            margin-bottom: 0;
            margin-top: 10px;
          }
        }
      }

      .smart-remind-content {
        display: flex;
        word-break: break-all;
      }

      .smart-remind-content-text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 238px;

        &.update {
          max-width: 201px;
        }
      }

      .smart-remind-content-box {
        display: flex;
        margin-bottom: 4px;
        font-size: 13px;
        line-height: normal;
      }

      .smart-remind-scroll {
        max-height: 34px;
        overflow-y: scroll;
        overflow-x: hidden;
        word-break: break-all;
      }

      .smart-remind-intr {
        min-width: 55px;
        text-align: right;
        color: #999999;
        margin-right: 10px;
        flex-shrink: 0;
        display: flex;
        justify-content: space-between;
      }

      .smart-remind-update {
        background: #FEF4F3;
        color: $styleColor;
        border-radius: 1px;
        padding: 1px 6px;
        line-height: 17px;
        font-size: 12px;
        margin-left: 2px;
        flex-shrink: 0;
      }

      .smart-card-btn {
        width: 94px;
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 6px;
        border-radius: 2px;
        border: 1px solid #E7E7E7;
        cursor: pointer;
        font-size: 13px;
        box-sizing: border-box;

        &:last-child {
          margin-right: 0;
        }

        i {
          width: 12px;
          height: 12px;
          margin-right: 6px;
          background-image: url("/img/schedule/smart-card-status.png");
          background-repeat: no-repeat;
          background-size: 108px 12px;
          background-position: 0 0;
        }

        &.smart-card-btn-accept {
          i {
            background-position: 0 0;
            display: none;
          }

          &.sel,
          &:hover {
            color: #2D91E6;
            border: 1px solid #2D91E6;
          }

          &.sel {
            i {
              background-image: url("/img/schedule/agree.png");
              display: inline;
              background-size: 100%;
            }
          }
        }

        &.smart-card-btn-refuse {
          i {
            background-position: -12px 0;
            display: none;
          }

          &.sel,
          &:hover {
            color: #EE3939;
            border: 1px solid #EE3939;
          }

          &.sel {
            i {
              display: inline;
              background-position: -84px 0;
            }
          }
        }

        &.smart-card-btn-upcoming {
          i {
            background-position: -24px 0;
            display: none;
          }

          &.sel,
          &:hover {
            color: #FF9100;
            border: 1px solid #FF9100;
          }

          &.sel {
            i {
              display: inline;
              background-position: -96px 0;
            }
          }
        }
      }

      .smart-card-btn-add {
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 2px;
        border: 1px solid #E7E7E7;
        width: 100%;
        height: 30px;
        margin: 0 auto;
        cursor: pointer;
      }

      .smart-card-btn-box {
        margin-top: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .schedule-invite-btn-add {
        border-radius: 2px;
        border: 1px solid #E7E7E7;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
      }
    }

    .msg-center-box {
      width: 330px;
      background: #FFFFFF;
      padding: 50px 0 4px 0;

      &.msg-center-box-1 {
        padding-bottom: 40px;
      }

      &.msg-center-box-2 {
        padding-bottom: 54px;
      }

      &.msg-center-box-3 {
        padding-bottom: 80px;
      }

      &.msg-center-box-2 {
        .remind-schedule-footer {
          .msg-center-content-box {
            &:last-child {
              margin-bottom: 10px;
            }
          }
        }
      }

      .msg-center-title {
        font-size: 15px;
        line-height: 21px;
        font-weight: bold;
        margin-bottom: 5px;
        word-break: break-all;
      }

      .msg-center-name {
        color: #999999;
        line-height: 20px;
        text-align: right;
      }

      .msg-center-content-box {
        border-bottom: 1px solid transparent;
      }

      .msg-center-intr {
        font-size: 13px;
        color: #333333;
        line-height: 20px;
        margin-bottom: 8px;
        white-space: pre-wrap;
        word-break: break-all;
        text-align: justify;
      }

      .msg-center-link-box {
        line-height: 20px;
        margin-bottom: 6px;
        font-size: 14px;

        .msg-center-link-detail {
          color: $styleLink;
          border-bottom: 1px solid $styleLink;
          cursor: pointer;
        }
      }

      .msg-center-img-box {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-bottom: 8px;

        img {
          max-width: 100%;
        }
      }

      .msg-center-btn-box {
        display: flex;
        margin-bottom: 8px;

        .msg-center-btn {
          flex: 1;
          height: 30px;
          line-height: 28px;
          text-align: center;
          border-radius: 2px;
          border: 1px solid $styleColor;
          margin-right: 10px;
          color: $styleColor;
          cursor: pointer;

          &:hover {
            background: #FFF3F3;
          }

          &:last-child {
            margin-right: 0px;
          }
        }
      }

      .msg-center-remind-box {
        height: 33px;
        line-height: 33px;

        .msg-center-remind-intr {
          color: #666666;
        }

        .msg-center-remind-btn {
          margin-left: 10px;
          cursor: pointer;

          &:hover {
            color: $styleColor;
          }
        }
      }
    }
  }

  /*霸屏消息*/
  #importantBox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 52;
    background: rgba(0, 0, 0, 0.7);

    .important-msg-box {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 640px;
      background: #FFFFFF;
      box-shadow: 5px 5px 20px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;

      &.important-msg-box-1,
      &.important-msg-box-3 {
        .important-msg-content-box-parent {
          max-height: calc(540px - 32px - 20px - 28px - 33px - 42px - 29px);
          overflow-y: auto;
        }

        ::v-deep(.msg-report-content) {
          max-height: calc(540px - 42px - 29px);
        }
      }

      &.important-msg-box-0,
      &.important-msg-box-2 {
        .important-msg-content-box-parent {
          max-height: calc(540px - 32px - 20px - 28px - 33px - 42px);
          overflow-y: auto;
        }

        ::v-deep(.msg-report-content) {
          max-height: calc(540px - 42px);
        }
      }

      &.important-msg-box-2,
      &.important-msg-box-3 {
        .important-msg-content {
          .important-msg-content-box {
            &:last-child {
              margin-top: 0;
            }
          }
        }
      }

      .important-msg-header {
        width: 100%;
        height: 32px;
        line-height: 32px;
        background: #F0F0F0;
        border-radius: 4px;
        font-size: 13px;
        color: #333333;
        padding: 0 16px;
      }

      .important-msg-content {
        padding-top: 20px;
        text-align: center;

        .important-msg-content-box-parent {
          .important-msg-content-box {
            margin-top: 10px;
          }
        }
      }

      .important-msg-footer {
        padding: 0 40px 12px;
      }

      .important-msg-title {
        font-size: 18px;
        font-weight: 600;
        line-height: 25px;
        margin-bottom: 8px;
        word-break: break-all;
        padding: 0 40px;
      }

      .important-msg-time {
        font-size: 12px;
        color: #999999;
        line-height: 17px;
        margin-bottom: 16px;
        padding: 0 40px;

        .important-msg-time-person {
          margin-left: 16px;
        }
      }

      .important-msg-content-box-parent {
        padding: 0 40px;
      }

      .important-msg-intr {
        font-size: 13px;
        color: #333333;
        line-height: 20px;
        white-space: pre-wrap;
        word-break: break-all;
        text-align: justify;
      }

      .important-msg-link-box {
        line-height: 20px;
        margin-bottom: 6px;
        font-size: 14px;
        text-align: left;

        .important-msg-link-detail {
          color: $styleLink;
          border-bottom: 1px solid $styleLink;
          cursor: pointer;
        }
      }

      .important-msg-img-box {
        width: 100%;
        display: flex;
        justify-content: center;

        img {
          max-width: 100%;
        }
      }

      .important-msg-btn-box {
        margin-top: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;

        .important-msg-btn {
          width: 300px;
          height: 30px;
          line-height: 28px;
          border-radius: 2px;
          border: 1px solid $styleColor;
          font-size: 14px;
          color: $styleColor;
          cursor: pointer;
          margin-right: 12px;

          &:hover {
            background: #FFF3F3;
          }

          &:last-child {
            margin-right: 0;
          }
        }

        .important-msg-btn-2 {
          width: 235px;
        }
      }

      .important-msg-remind-box {
        line-height: 17px;
        margin-top: 12px;

        .important-msg-remind-intr {
          color: #666666;
        }

        .important-msg-remind-btn {
          margin-left: 10px;
          cursor: pointer;

          &:hover {
            color: $styleColor;
          }
        }
      }
    }
  }

  :deep(.customer-dialog .content) {
    padding: 16px;
  }

  .ly-dialog-default-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 10px;

    &.center {
      align-items: center;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .ly-dialog-default-label {
      font-size: 13px;
      color: #666666;
      line-height: 30px;
      width: 80px;
      text-align: right;

      &.ly-dialog-default-label-width {
        width: auto;
      }

      .ly-dialog-default-tips {
        color: #EE3939;
        margin-right: 5px;
      }
    }

    .ly-dialog-default-detail {
      width: 252px;
      position: relative;

      .ly-dialog-default-sel-text,
      .ly-dialog-default-input {
        width: 100%;
        line-height: 28px;
        padding-left: 10px;
        border: 1px solid #E0E0E0;
        border-radius: 4px;

      }

      .ly-dialog-default-sel-text {
        color: #545454;

        &:after {
          content: "";
          position: absolute;
          top: 50%;
          right: 10px;
          transform: translateY(-50%);
          width: 0;
          height: 0;
          border-width: 4px 4px 0;
          border-style: solid;
          border-color: #000000 transparent transparent transparent;
        }

        &.show {
          color: #000000;
        }

        &.sel {
          &:after {
            border-width: 0 4px 4px 4px;
            border-color: transparent transparent #000000 transparent;
          }
        }
      }

      .ly-dialog-default-sel-ul {
        width: 100%;
        position: absolute;
        top: 30px;
        left: 0;
        box-shadow: 0px 2px 4px 0px rgba(165, 165, 165, 0.5);
        border-radius: 2px;
        border: 1px solid #CCCCCC;
        background: #FFFFFF;
        z-index: 1;

        .ly-dialog-default-sel-li {
          width: 100%;
          line-height: 30px;
          padding-left: 10px;

          &:hover {
            background: $styleBg1Hover;
          }
        }
      }

      .ly-dialog-default-textarea {
        width: 100%;
        height: 110px;
        background: #FFFFFF;
        border-radius: 2px;
        border: 1px solid #E0E0E0;
        padding: 10px;
      }

      .ly-dialog-default-textarea-text {
        position: absolute;
        right: 10px;
        bottom: 10px;
      }

      .red {
        color: $styleColor;
      }
    }
  }

  .ly-dialog-default-ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .ly-dialog-default-box {
      height: auto;
    }
  }
}
</style>