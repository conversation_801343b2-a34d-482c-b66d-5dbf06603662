import {dateFormat, getAppPath, getUserInfo, mkdirsSync, getLogFileInfo, emitMsg, dealMem, getCpus, deleteFile, encryptRsa, throttle} from "@utils";
import {alert} from "@comp/ui";

let os = remote.require("os");
let fs = remote.require("fs");
let path = remote.require("path");
let logger = {};
// 计时对象
let timeMap = {};

// 获取文件路径
function getStackTrace() {
  let obj = {};
  Error.captureStackTrace(obj, getStackTrace);
  return obj.stack;
};

// 写入文件
logger.writeLogFile = function (type, text) {
  if (type == 5 || type == 6) {
    // 暂不写入云信详细日志
    return;
  }
  // 不写入错误日志，改为普通日志加错误标识
  let logFileInfo = getLogFileInfo(type == 2 ? 1 : type, getUserInfo().workerNo || "", dateFormat(new Date(), "yyyy-MM-dd"));
  try {
    // 针对内容处理
    mkdirsSync(logFileInfo.path);
    let version = "unknown";
    try {
      if (window.remote && window.remote.store && window.remote.store.state && window.remote.store.state.config) {
        version = window.remote.store.state.config.version || "unknown";
      } else if (remote && remote.store && remote.store.state && remote.store.state.config) {
        version = remote.store.state.config.version || "unknown";
      }
    } catch (error) {
      console.warn('获取版本信息失败:', error);
    }
    text = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss") + " v-" + version + " - " + (type == 2 ? "错误日志" : "") + "-" + text;
    if (!remote.logFileMap) {
      remote.logFileMap = {};
    }
    if (!remote.logFileMap[type]) {
      remote.logFileMap[type] = [];
    }
    remote.logFileMap[type].push(text + "\r\n");
  } catch (e) {
    console.oldLog(e);
  }
}

// 写入日志定时器
logger.writeLogFileTimer = function () {
  for (let key in remote.logFileMap) {
    let writeText = "";
    let spliceLine = 100;
    if (remote.logFileMap[key].length > 0) {
      if (key == 5 || key == 6) {
        // 加密卡顿每次只写入10行
        spliceLine = 10;
      }
      writeText = remote.logFileMap[key].slice(0, spliceLine).join("");
      if (key == 5 || key == 6) {
        // writeText = encryptRsa(writeText, "log");
      }
      remote.logFileMap[key].splice(0, spliceLine);
    }
    if (writeText) {
      let logFileInfo = getLogFileInfo(key == 2 ? 1 : key, getUserInfo().workerNo || "", dateFormat(new Date(), "yyyy-MM-dd"));
      let stdout = fs.createWriteStream(path.join(logFileInfo.path, logFileInfo.name), {flags: "a", encoding: "utf8"});
      stdout.write(writeText, function () {
        // 结束流
        stdout.end();
      });
    }
  }
}


// 删除日志
function deleteLogFile() {
  deleteFile(getAppPath(`\\logs\\`), 7 * 24 * 60 * 60 * 1000);
}

// 打印参数
function consoleArg(args) {
  let str = "";
  for (let i = 0; i < args.length; i++) {
    let obj = args[i] || "";
    if (obj instanceof String) {
      str += obj + "";
    } else {
      try {
        let cache = [];
        str += JSON.stringify(obj, function (key, value) {
          if (typeof value === 'object' && value !== null) {
            // 去除无用类
            if (cache.indexOf(value) !== -1 || key == "clientRequest") {
              // 移除
              return;
            }
            // 收集所有的值
            cache.push(value);
          }
          return value;
        });
        cache = null; // 清空变量，便于垃圾回收机制回收
      } catch (e) {
        str += " <JSON.stringify unidentifiable> " + e;
      }
    }
  }
  return str;
}

export function initLogger() {
  deleteLogFile();
  // 重写console
  console.oldLog = console.log;
  // console.oldLog = function () {}
  console.log = function () {
    let stack = getStackTrace() || "";
    let matchResult = stack.match(/at(.*?\n)|(.*?$)/g) || [];
    let line = matchResult[1] || "--- 未找到url";
    let codePath = line.replace(/\n/g, "");
    // 过滤云信日志
    if (/NIM_Web_NIM/.test(codePath)) {
      writeNimLogFile(arguments);
      return;
    }
    console.oldLog(...arguments, codePath)
    logger.writeLogFile(1, consoleArg(arguments));
  }
  // 重写错误日志
  console.error = function () {
    let stack = getStackTrace() || "";
    let matchResult = stack.match(/at(.*?\n)|(.*?$)/g) || [];
    let codePath = matchResult.join(",").replace(/\n|\s*/g, "");
    // 过滤云信日志
    if (/NIM_Web_NIM/.test(codePath)) {
      writeNimLogFile(arguments);
      return;
    }
    console.oldLog("error", ...arguments, codePath);
    logger.writeLogFile(2, consoleArg(arguments));
    errorReload(...arguments);
  }
  console.info = function () {
    let stack = getStackTrace() || "";
    let matchResult = stack.match(/at(.*?\n)|(.*?$)/g) || [];
    let line = matchResult[1] || "--- 未找到url";
    let codePath = line.replace(/\n/g, "");
    // 过滤云信日志
    if (/NIM_Web_NIM/.test(codePath)) {
      writeNimLogFile(arguments);
      return;
    }
    console.oldLog("info", ...arguments, codePath);
    logger.writeLogFile(1, consoleArg(arguments));
  }
  console.warn = function () {
    let stack = getStackTrace() || "";
    let matchResult = stack.match(/at(.*?\n)|(.*?$)/g) || [];
    let line = matchResult[1] || "--- 未找到url";
    let codePath = line.replace(/\n/g, "");
    // 过滤云信日志
    if (/NIM_Web_NIM/.test(codePath)) {
      writeNimLogFile(arguments);
      return;
    }
    console.oldLog("warn", ...arguments, codePath);
    logger.writeLogFile(1, consoleArg(arguments));
  }
  // 重写计时日志
  console.time = function (label) {
    timeMap[label] = {
      time: Date.now(),
      ...getCpus()
    };
  }
  console.timeEnd = function (label, param) {
    if (timeMap[label] && Date.now() - timeMap[label].time > 0) {
      let cpus = os.cpus();
      let cpusTimes = getCpus();
      let usageInfo = remote.process.memoryUsage();
      let timeParam = {}
      // 收集当前系统数据
      timeParam[label + (param ? "-" + JSON.stringify(param) : "")] = `${Date.now() - timeMap[label].time}ms`;//计时
      timeParam["cpuNum"] = cpus.length;//cpu数量
      timeParam["cpuUsage"] = `${cpusTimes.total == timeMap[label].total ? 0 : ((1 - (cpusTimes.idle - timeMap[label].idle) / (cpusTimes.total - timeMap[label].total)) * 100.0).toFixed(2)}%`;//cpu占用
      timeParam["ramFree"] = dealMem(os.freemem());//内存空闲空间
      timeParam["ramTotal"] = dealMem(os.totalmem());//内存总空间
      timeParam["ramInfo"] = `rss:${dealMem(usageInfo.rss)},heapTotal:${dealMem(usageInfo.heapTotal)},heapUsed:${dealMem(usageInfo.heapUsed)},external:${dealMem(usageInfo.external)}`;//当前程序占用内存
      timeParam["cpuModel"] = cpus[0].model;//cpu信息
      timeParam["idle"] = timeMap[label].idle;
      timeParam["total"] = timeMap[label].total;

      setTimeout(() => {
        let cpusTimes = getCpus();
        timeParam["cpuUsageTime"] = `${cpusTimes.total == timeParam["total"] ? 0 : ((1 - (cpusTimes.idle - timeParam["idle"]) / (cpusTimes.total - timeParam["total"])) * 100.0).toFixed(2)}%`;//cpu占用
        delete timeParam["idle"];
        delete timeParam["total"];
        logger.writeLogFile(3, JSON.stringify(timeParam));
      }, 1000);
    }
    delete timeMap[label];
  }
  try {
    // 全局错误监听
    if (window && window.document) {
      function onerror(info, file, line, col, err) {
        if (info && info.target && info.target.nodeName == 'IMG' && !line) {
          return;
        }
        let logInfo = file + ' [' + line + ',' + col + ']' + info + info?.error?.stack + (info?.reason?.stack || info?.reason?.message || info?.reason?.event?.target?.error?.message || info?.reason);
        if (/Unable to add key to index/.test(logInfo)) {
          console.oldLog("windowError", logInfo);
          logger.writeLogFile(2, logInfo);
        } else {
          console.oldLog("windowError", logInfo);
          logger.writeLogFile(2, logInfo);
          remote.store.dispatch("uploadZxp", {type: 5, msg: logInfo});
        }
        errorReload(logInfo);
      }

      window.onerror = onerror;
      window.onunhandledrejection = e => {
        onerror(e);
        e.preventDefault();
      };
    }
  } catch (e) {}
  try {
    // 异常监听
    process.on("uncaughtException", err => {
      logger.writeLogFile(2, err);
    });
  } catch (e) {}
  return logger;
}

// 需要加载的异常
function errorReload(logInfo) {
  try {
    if (/A system error occurred|Internal error opening backing store for indexedDB.open|The database connection is closing|Cannot read property 'chatroom' of undefined|Cannot read properties of undefined \(reading 'chatroom'\)/.test(logInfo)) {
      let type = 1;
      if (/Internal error opening backing store for indexedDB.open/.test(logInfo)) {
        type = 2;
      }
      // 重新加载
      alert({
        content: "系统错误，需要重新启动乐聊",
        okText: "重新加载",
        showCancel: false,
        done: () => {
          if (location.hash == "#/") {
            remote.global.cmdRelunach = true;
          }
          emitMsg("msg", {type: "logout", logout: 1});
          if (type == 2) {
            emitMsg("msg", {type: "clear", clear: 2});
          }
          window.global.clearAllFlag = false;
        }
      });
    }
  } catch (e) {}
}

// 过滤云信日志敏感内容写入日志
function writeNimLogFile() {
  let str = "";
  for (let i = 0; i < arguments.length; i++) {
    let obj = arguments[i] || '';
    if (obj instanceof String) {
      str += obj + "";
    } else {
      try {
        str += JSON.stringify(obj) + ' ';
      } catch (e) {
        //console.error(e);
        str += " <JSON.stringify unidentifiable> " + e;
      }
    }
  }
  try {
    logger.writeLogFile(6, str);
  } catch (e) {}
  str = str.replace(/\\/g, "");// 过滤斜杆
  // 过滤具体聊天内容
  str = str.replace(/(?<="token": ").+(?="sdkVersion":)/, "");
  str = str.replace(/(?<=,"1000":").+(?=")/, "");
  str = str.replace(/(?<=,"9":").+(?=","11":)/, "");
  str = str.replace(/(?<=,"10":").+(?=","11":)/, "");
  str = str.replace(/(?<=,"15":").+(?=","16":)/, "");
  str = str.replace(/(?<=,"17":").+(?=","100":)/, "");
  str = str.replace(/(?<=,"17":").+(?=","26":)/, "");
  str = str.replace(/(?<=,"17":").+(?=","110":)/, "");
  str = str.replace(/(?<=,"17":").+(?="}])/, "");
  str = str.replace(/(?<="body": ).+(?="idClient":)/, "");
  str = str.replace(/(?<="body": ).+(?="pushContent":)/, "");
  str = str.replace(/(?<="pushContent": ).+(?="isHistoryable":)/, "");
  str = str.replace(/(?<="pushContent": ).+(?="status":)/, "");
  str = str.replace(/(?<="pushContent": ).+(?="needPushNick":)/, "");
  str = str.replace(/(?<="text": ).+(?="isHistoryable":)/, "");
  str = str.replace(/(?<="attach": ).+(?="idClient":)/, "");
  str = str.replace(/(?<="attach": ).+(?="userUpdateTime":)/, "");
  str = str.replace(/(?<="content": ).+(?="target":)/, "");
  str = str.replace(/(?<="announcement": ").+(?="joinMode":)/, "");
  str = str.replace(/(?<="file":).+(?="target":)/, "");
  str = str.replace(/(?<="avatar":).+(?="updateCustomMode":)/, "");
  str = str.replace(/(?<="ps":).+(?="scene":)/, "");
  logger.writeLogFile(7, str);
}

try {
  // 安全地检查和初始化 httRequestInfo
  let globalObj;
  try {
    if (window.remote && window.remote.global) {
      globalObj = window.remote.global;
    } else if (remote && remote.global) {
      globalObj = remote.global;
    }
  } catch (error) {
    console.warn('无法访问 remote.global:', error);
  }

  if (globalObj && !globalObj.httRequestInfo) {
    globalObj.httRequestInfo = {};
    //当请求即将发出时产生
    chrome.webRequest.onBeforeRequest.addListener(function (details) {
      try {
        if (details.type == "image" || details.type == "xmlhttprequest") {
          globalObj.httRequestInfo[details.requestId] = {
            onBeforeRequestTime: details.timeStamp
          }
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]});
    // 加入请求头信息到日志
    chrome.webRequest.onBeforeSendHeaders.addListener(function (details) {
      try {
        if (details.type == "xmlhttprequest") {
          for (var i = 0; i < details.requestHeaders.length; ++i) {
            if (details.requestHeaders[i].name == 'methodCode' || details.requestHeaders[i].name == 'serviceCode') {
              if (!remote.global.httRequestInfo[details.requestId].headerInfo) {
                remote.global.httRequestInfo[details.requestId].headerInfo = {};
              }
              remote.global.httRequestInfo[details.requestId].headerInfo[details.requestHeaders[i].name] = details.requestHeaders[i].value;
            }
          }
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]}, ["requestHeaders"]);
    //当所有应用已经修改完请求标头并且展现最终版本时产生
    chrome.webRequest.onSendHeaders.addListener(function (details) {
      try {
        if (details.type == "image" || details.type == "xmlhttprequest") {
          remote.global.httRequestInfo[details.requestId].onSendHeadersTime = details.timeStamp;
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]});
    //每当接收到 HTTP(S) 响应标头时产生
    chrome.webRequest.onHeadersReceived.addListener(function (details) {
      try {
        if (!remote.global.httRequestInfo[details.requestId]) {
          remote.global.httRequestInfo[details.requestId] = {};
        }
        if ((details.type == "image" || details.type == "xmlhttprequest") && !remote.global.httRequestInfo[details.requestId].onHeadersReceivedTime) {
          remote.global.httRequestInfo[details.requestId].onHeadersReceivedTime = details.timeStamp;
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]});
    //当接收到响应正文的第一个字节时产生
    chrome.webRequest.onResponseStarted.addListener(function (details) {
      try {
        if (details.type == "image" || details.type == "xmlhttprequest") {
          remote.global.httRequestInfo[details.requestId].onResponseStartedTime = details.timeStamp;
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]});
    //当请求成功处理后产生
    chrome.webRequest.onCompleted.addListener(function (details) {
      try {
        if (details.type == "image" || details.type == "xmlhttprequest") {
          remote.global.httRequestInfo[details.requestId].onCompletedTime = details.timeStamp;
          var param = remote.global.httRequestInfo[details.requestId];
          param.RequestSentTime = param.onSendHeadersTime - param.onBeforeRequestTime;
          param.WaitingTTFBTime = param.onHeadersReceivedTime - param.onSendHeadersTime;
          param.ContentDownloadTime = param.onCompletedTime - param.onResponseStartedTime;
          param.AllTime = param.onCompletedTime - param.onBeforeRequestTime;
          param.url = details.url;
          param.ip = details.ip;
          param.statusCode = details.statusCode;
          if (new RegExp("https:\/\/nim.nosdn.127.net\/|https:\/\/nim-nosdn.netease.im\/|https:\/\/nim.leyoujia.com\/|https:\/\/hknim.leyoujia.com\/|https:\/\/xjpnim.leyoujia.com\/").test(param.url)) {
            logger.writeLogFile(8, JSON.stringify(param));
          } else if (details.type == "xmlhttprequest" || details.type == "xmlhttprequest" && new RegExp(".leyoujia.com\/").test(param.url)) {
            logger.writeLogFile(9, JSON.stringify(param));
          }
          delete remote.global.httRequestInfo[details.requestId];
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]});
    //当请求不能成功处理时产生
    chrome.webRequest.onErrorOccurred.addListener(function (details) {
      try {
        if (details.type == "image" || details.type == "xmlhttprequest") {
          remote.global.httRequestInfo[details.requestId].onErrorOccurredTime = details.timeStamp;
          var param = remote.global.httRequestInfo[details.requestId];
          param.RequestSentTime = param.onSendHeadersTime - param.onBeforeRequestTime;
          param.WaitingTTFBTime = param.onHeadersReceivedTime - param.onSendHeadersTime;
          param.ContentDownloadErrorTime = param.onErrorOccurredTime - param.onResponseStartedTime;
          param.AllTime = param.onErrorOccurredTime - param.onBeforeRequestTime;
          param.url = details.url;
          param.ip = details.ip;
          param.statusCode = details.statusCode;
          if (new RegExp("https:\/\/nim.nosdn.127.net\/|https:\/\/nim-nosdn.netease.im\/|https:\/\/nim.leyoujia.com\/|https:\/\/hknim.leyoujia.com\/|https:\/\/xjpnim.leyoujia.com\/").test(param.url)) {
            logger.writeLogFile(8, JSON.stringify(param));
          } else if (details.type == "xmlhttprequest" || details.type == "xmlhttprequest" && new RegExp(".leyoujia.com\/").test(param.url)) {
            logger.writeLogFile(9, JSON.stringify(param));
          }
          delete remote.global.httRequestInfo[details.requestId];
        }
      } catch (e) {}
    }, {urls: ["<all_urls>"]});
  }
} catch (e) {
  console.log("devtoolsError", e);
}