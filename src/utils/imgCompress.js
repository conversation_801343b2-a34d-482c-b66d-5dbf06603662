// 将File（Blob）对象转变为一个dataURL字符串， 即base64格式
export function fileToDataURL(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onloadend = e => resolve(e.target.result);
    reader.readAsDataURL(file);
  });
}

// 将dataURL字符串转变为image对象，即base64转img对象
export function dataURLToImage(dataURL) {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => resolve("");
    img.src = dataURL;
  });
}

// 将一个canvas对象转变为一个File（Blob）对象
export function canvasToFile(canvas, type, quality) {
  return new Promise(resolve => {
    canvas.toBlob(blob => resolve(blob), type, quality);
  });
}

// 图片压缩 file为图片文件对象 quality为压缩质量
export function imgQuality(param) {
  return new Promise(async (resolve, reject) => {
    let {file, quality} = param;
    const fileSize = file.size / 1024;
    if (!quality) {
      // 原有的压缩比例方式
      if (fileSize) {
        if (fileSize <= 100) {
          quality = 0.75;
        } else if (fileSize > 100 && fileSize <= 300) {
          quality = 0.65;
        } else if (fileSize > 300 && fileSize <= 500) {
          quality = 0.55;
        } else if (fileSize > 500 && fileSize <= 1000) {
          quality = 0.5;
        } else if (fileSize > 1000) {
          quality = 0.4;
        }
      } else {
        quality = 0.8;
      }
    }
    // 将原图片转换成base64
    const base64 = await fileToDataURL(file);
    // gif不压缩
    if (/gif/.test(file.type)) {
      resolve({file: file, base64: base64});
      return;
    }

    // 缩放图片需要的canvas
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');

    const img = await dataURLToImage(base64);

    let originWidth = img.width;
    let originHeight = img.height;

    let radioW = originWidth / screen.width;
    let radioH = originHeight / screen.height;
    // 限制宽高重新设置
    if (radioW > 1 || radioH > 1) {
      if (radioW > radioH) {
        originHeight = screen.width / originWidth * originHeight;
        originWidth = screen.width;
      } else {
        originWidth = screen.height / originHeight * originWidth;
        originHeight = screen.height;
      }
    }

    canvas.width = originWidth;
    canvas.height = originHeight;
    context.clearRect(0, 0, originWidth, originHeight);
    try {
      context.drawImage(img, 0, 0, originWidth, originHeight);
    } catch (e) {}
    let compressBlob = await canvasToFile(canvas, "image/webp", quality);
    // 压缩失败返回原图
    if (compressBlob.size > file.size) {
      resolve({file: file, base64: base64});
      return;
    }
    const compressedBase64 = await fileToDataURL(compressBlob);
    const compressedFile = new File([compressBlob], file.name, {type: file.type});

    resolve({file: compressedFile, base64: compressedBase64});
  });
}