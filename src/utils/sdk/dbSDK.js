/**
 * 本地数据库缓存操作sdk
 * */
import <PERSON><PERSON> from 'dexie';
import {regReplace} from '@utils'

// 初始化文件缓存数据库
export function initFileDB(account) {
  let fileDB = new Dexie("file-" + account);
  fileDB.version(1).stores({
    fileInfo: "++,&md5,name,path,ext,state"
  }).upgrade(function () {
    console.log('upgrade');
  });

  fileDB.open().then(function (data) {
    console.log('open DB:' + data.name + ' succeed');
  }).catch(function (err) {
    console.log('Failed to open fileDB"' + data + ': ' + (err.stack || err));
  });

  // 新增文件记录
  fileDB.add = function (data) {
    try {
      fileDB.fileInfo.add(data);
    } catch (e) {}
  };
  // 查询文件记录
  fileDB.query = function (key, data) {
    let timeKey = `fileDB-${key}-${data}`;
    console.time(timeKey);
    let fileList = fileDB.fileInfo.where(key).equalsIgnoreCase(data).toArray();
    console.timeEnd(timeKey);
    return fileList;
  };
  // 删除文件记录
  fileDB.del = function (key, data) {
    try {
      fileDB.fileInfo.where(key).equalsIgnoreCase(data).delete();
    } catch (e) {}
  };

  return fileDB;
}

// 初始化人员数据库
export function initUserDB() {
  // 创建人员数据表
  let userDb = new Dexie("user");
  userDb.version(1).stores({
    user: "++,&workerNo"
  }).upgrade(function () {
    console.log('upgrade');
  });

  userDb.open().then(function (data) {
    console.log('open DB:' + data.name + ' succeed');
  }).catch(function (err) {
    console.log('Failed to open "userDb"' + data + ': ' + (err.stack || err));
  });

  // 查询单个人员信息
  userDb.query = function (data) {
    return userDb.user.get({"workerNo": data});
  };
  // 批量查询人员信息,data为账号数组
  userDb.search = function (data) {
    let list = [];
    data.map(item => {
      if (item) {
        list.push(item);
      }
    });
    return userDb.user.where("workerNo").anyOf(list).toArray();
  };
  // 批量获取人员信息主键
  userDb.primaryKeys = function (data) {
    return userDb.user.where("workerNo").anyOf(data).primaryKeys();
  };
  // 批量插入人员信息,data为人员数据
  userDb.bulkPut = function (data) {
    try {
      userDb.user.bulkPut(Object.values(data));
    } catch (e) {}
  };
  // 逐个插入人员信息
  userDb.put = function (data) {
    try {
      userDb.user.put(data);
    } catch (e) {}
  };
  // 逐个更新人员信息
  userDb.update = function (key, data) {
    try {
      return userDb.user.update(key, data);
    } catch (e) {}
  };
  // 模糊查询
  userDb.searchUser = function (param) {
    try {
      !param.page && (param.page = 1);
      !param.rows && (param.rows = 50);
      let userList = [];
      return new Promise(resolve => {
        userDb.user.offset((param.page - 1) * param.rows).each((item, cursor) => {
          if (new RegExp(regReplace(param.name), "i").test(item.name) || new RegExp(regReplace(param.name), "i").test(item.workerSpell)) {
            // 查询名字和简拼
            userList.push(item);
          }
          // 返回限制个数
          if (userList.length >= param.rows) {
            cursor.stop();
          }
        }).then(() => {
          resolve(userList);
        }).catch(e => {
          console.log("searchUserDBErr", e);
          resolve(userList);
        });
      });
    } catch (e) {}
  };
  return userDb;
}

// 初始化数据缓存数据库
export function initCacheDB() {
  let cacheDB = new Dexie("cache");
  cacheDB.version(1).stores({
    cache: "++,&name,value"
  }).upgrade(function () {
    console.log('upgrade');
  });

  cacheDB.open().then(function (data) {
    console.log('open DB:' + data.name + ' succeed');
  }).catch(function (err) {
    console.log('Failed to open cacheDB"' + data + ': ' + (err.stack || err));
  });

  // 查询缓存记录
  cacheDB.query = async function (data) {
    try {
      let result = await cacheDB.cache.where("name").equalsIgnoreCase(data).toArray();
      if (result.length > 0) {
        result = JSON.parse(result[0].value);
      } else {
        result = {};
      }
      return result;
    } catch (e) {}
  };
  // 新增/更新缓存
  cacheDB.update = async function (key, data) {
    try {
      let list = await cacheDB.cache.where("name").anyOf([key]).primaryKeys();
      let dbData = {name: key, value: data};
      if (list.length == 0) {
        await cacheDB.cache.put(dbData);
      } else {
        await cacheDB.cache.update(list[0], dbData);
      }
    } catch (e) {}
  };
  // 删除缓存记录
  cacheDB.del = async function (data) {
    try {
      return cacheDB.cache.where("name").equalsIgnoreCase(data).delete();
    } catch (e) {}
  };

  return cacheDB;
}

// 删除db数据 1只删除云信数据库 2删除所有file数据库
export function removeDB(workerNo, type) {
  if (type == 1) {
    Dexie.delete("nim-" + workerNo);
  } else if (type == "2") {
    indexedDB.databases().then(databases => {
      if (databases?.length > 0) {
        databases.map(item => {
          if (/file-/.test(item.name)) {
            Dexie.delete(item.name);
          }
        });
        if (workerNo) {
          remote.store.commit("setFileDB", workerNo);
        }
      }
    })
  } else {
    Dexie.delete("nim-" + workerNo);
    Dexie.delete("file-" + workerNo);
    Dexie.delete("user");
    Dexie.delete("cache");
  }
}