/**
 * 云信imSDK
 * */
import {alert, loading, toast} from "@comp/ui";
import {regAccidApi, unblockAccidApi, getEmpStatusApi, zxpApi} from "@utils/net/api.js";
import {
  dateFormat, emitMsg, deepClone, MD5, getPushContent, dataUrlToBlob, isNotificationRole, getLocalFile, removeReplace, setMsgString,
  addFileDB, getChildWin, userLocalStorage, setUserBaseInfo, dealMsgField, getFileCachedPath, getFileExt, getBase64Ext, saveImageLocal,
} from "@utils";
import {fileToDataURL} from "@utils/imgCompress"

let baseComputerInfo = remote.store.getters.getBaseComputerInfo;

let nim = {};// 云信对象
let nimSDK = {};//云信sdk
let timeMap = {};//计时器对象
let workerNo = "";//当前用户
let imei = baseComputerInfo.hostName;//本机mac
let asyncCount = 0;// 同步消息次数
let isAsyncDone = false;//是否同步完成
let onteamCount = 0;// 同步群次数
let config = remote.store.getters.getConfig.config;// 配置文件
let uniqueSignCount = 0;// 批量发送文件时间偶尔会一致，新增计数
let msgsLength = 0;// 收到的消息数量
const fs = remote.require("fs");
const path = remote.require("path");


export function initNim(userInfo) {
  let env = config[config.env];
  let account = userInfo.workerNo;
  let token = userInfo.imPwd;
  if (userInfo.env != config.env) {
    return;
  }
  workerNo = account;

  if (!remote.nim?.disconnect) {
    // 没有初始化过im
    timeMap.init = new Date().getTime();
    timeMap.initFlag = true;
    timeMap.initloadFlag = true;
    remote.store.commit("setNimInfo", {type: "info", infoType: 0, defaultProgress: 0});
    loadTimeout();
  }
  if (!nim.disconnect) {
    console.timeEnd("登录跳转");
    console.time("云信加载");
    remote.nim = nim = NIM.getInstance({
      transports: ["websocket"], // 链接方式只采取ws
      debug: true,
      appKey: env.nimAppKey,
      account: account,
      token: token,
      db: true,
      dbLog: false,
      onconnect: onconnect,//连接
      onwillreconnect: onwillreconnect,//重连
      ondisconnect: ondisconnect,//断连
      onerror: onerror,//云信错误
      onsyncdone: onsyncdone,//同步完成
      onloginportschange: onloginportschange,//多端登录变化

      syncSessionUnread: true,//同步会话未读数
      syncRoamingMsgs: true,//同步漫游消息
      syncMsgReceipts: true,//同步已读回执时间戳
      syncStickTopSessions: true,//同步置顶会话
      onsessions: onsessions,//同步会话
      onupdatesession: onupdatesession,//更新会话

      onmsg: onmsg.bind(this, 1),//在线消息
      onofflinemsgs: onmsg.bind(this, 2),//离线消息
      onroamingmsgs: onmsg.bind(this, 3),//漫游消息

      syncTeams: true,//同步群列表
      syncExtraTeamInfo: true,//同步额外的群信息
      syncSuperTeams: true,//同步超大群列表
      syncTeamMembers: false,//同步群成员
      onteams: onteams.bind(this, 1),//同步群
      onSuperTeams: onteams.bind(this, 2),//同步超大群

      /**群回调**/
      onCreateTeam: onCreateTeam.bind(this, 1),//创建群回调
      onsynccreateteam: onCreateTeam.bind(this, 2),//漫游创建群回调
      onCreateSuperTeam: onCreateTeam.bind(this, 3),//超大群--创建群回调
      onSyncCreateSuperTeam: onCreateTeam.bind(this, 4),//超大群--漫游创建群回调
      onUpdateTeam: onUpdateTeam.bind(this, 1),//更新群的回调
      onUpdateSuperTeam: onUpdateTeam.bind(this, 2),//超大群--更新群的回调
      // onAddTeamMembers: onAddTeamMembers.bind(this, 1),//有人进群回调
      onAddSuperTeamMembers: onAddTeamMembers.bind(this, 2),//超大群--有人进群回调
      // onRemoveTeamMembers: onRemoveTeamMembers.bind(this, 1),//有人出群回调
      onRemoveSuperTeamMembers: onRemoveTeamMembers.bind(this, 2),//超大群--有人出群回调
      // onUpdateTeamManagers: onUpdateTeamManagers.bind(this, 1),//更新管理员回调
      onUpdateSuperTeamManagers: onUpdateTeamManagers.bind(this, 2),//超大群--更新管理员回调
      // onDismissTeam: onDismissTeam.bind(this, 1),//解散群回调
      onDismissSuperTeam: onDismissTeam.bind(this, 2),//超大群--解散群回调
      // onTransferTeam: onTransferTeam.bind(this, 1),//移交群回调
      onTransferSuperTeam: onTransferTeam.bind(this, 2),//超大群--移交群回调
      // onUpdateTeamMembersMute: onUpdateTeamMembersMute.bind(this, 1),//群成员禁言回调
      onUpdateSuperTeamMembersMute: onUpdateTeamMembersMute.bind(this, 2),//超大群--群成员禁言回调

      syncRelations: true,//同步黑名单和静音列表
      syncFriends: true,//同步好友列表
      syncFriendUsers: true,//同步好友信息
      onblacklist: onblacklist.bind(this, 1),//同步黑名单列表
      onsyncmarkinblacklist: onblacklist.bind(this, 2),//多端同步黑名单列表
      onfriends: onfriends.bind(this, 1),//好友列表
      onsyncfriendaction: onfriends.bind(this, 2),//多端同步好友列表
      onmyinfo: onmyinfo.bind(this, 1),//个人信息
      onupdatemyinfo: onmyinfo.bind(this, 2),//多端同步个人信息

      onsysmsg: onsysmsg.bind(this, 1),//系统消息
      onofflinesysmsgs: onsysmsg.bind(this, 2),//离线系统消息
      onroamingsysmsgs: onsysmsg.bind(this, 3),//漫游系统消息
      onupdatesysmsg: onsysmsg.bind(this, 4),//更新系统消息
      oncustomsysmsg: oncustomsysmsg.bind(this, 1),//自定义系统消息
      onofflinecustomsysmsgs: oncustomsysmsg.bind(this, 2),//离线自定义系统消息

      shouldIgnoreNotification: shouldIgnoreNotification, //是否要忽略某条通知类消息
      shouldIgnoreMsg: shouldIgnoreMsg, //是否要忽略某条消息

      onpushevents: onpushevents,// 订阅事件推送
    });
  }
  return nimSDK;
}

// 连接
function onconnect(data) {
  console.log("连接成功onconnect:", data);
  isAsyncDone = false;
  timeMap.onconnect = new Date().getTime();
  //告诉其他端PC端登录了
  nimSDK.modifyMyInfo({
    "empNo": workerNo,
    "key": "im_behavior",
    "remark": "PC登录",
    "type": 1,    //1:PC端,2:安卓端,3:IOS端
    "value": JSON.stringify({"connectionId": data.connectionId}),
    "operation": "login",
    "imei": imei,
  });
  // 登录成功发送通知给主进程（改变任务栏状态）
  emitMsg("msg", {type: "tray", tray: 1});
  loadTimeout();
  // 设置本地时间和服务器时间差
  remote.store.dispatch("setDiffTime");
  // 首次登录获取本地会话先渲染
  if (asyncCount == 0) {
    nimSDK.getLocalSessions().then(res => {
      remote.store.commit("setNimInfo", {type: "info", infoType: 0, defaultProgress: 20});
      if (res.obj?.sessions?.length > 0) {
        remote.store.commit("setSessions", res.obj.sessions);
        let teamIds = [];
        res.obj.sessions.map(item => {
          if (item.scene == "team") {
            teamIds.push(item.to);
          }
        });
        if (teamIds.length > 0) {
          nimSDK.getLocalTeams({teamIds: teamIds}).then(res => {
            remote.store.commit("setNimInfo", {type: "info", infoType: 0, defaultProgress: 30});
            if (res.obj?.teams?.length > 0) {
              remote.store.commit("setTeams", {teamList: res.obj.teams, localTeamFlag: true});
            }
          });
        }
      } else {
        remote.store.commit("setNimInfo", {type: "info", infoType: 0, defaultProgress: 30});
      }
    });
  } else {
    // 断网重连成功未读数云信有可能不是最新的，获取本地会话渲染
    setTimeout(() => {
      nimSDK.getLocalSessions().then(res => {
        if (res.obj?.sessions?.length > 0) {
          remote.store.commit("setSessions", deepClone(res.obj.sessions));
        }
      });
    }, 100);
  }
  remote.store.commit("setNimInfo", {type: "info", infoType: 0, defaultProgress: 10, time: Date.now()});
}

// 重连
function onwillreconnect(data) {
  console.log("onwillreconnect", data);
  emitMsg("msg", {type: "tray", tray: 2});
  remote.store.commit("setNimInfo", {type: "info", infoType: 1, defaultProgress: 0, time: Date.now()});
}

// 失去连接
function ondisconnect(error) {
  console.log("ondisconnect", error);
  let errorMsg = error.message;
  if (error) {
    switch (error.code) {
      case 302:
        // 账号未注册，去注册一个账号，重新登录
        errorMsg = "登入IM失败,账号或者密码错误!";
        // 账号或者密码错误, 请跳转到登录页面并提示错误
        regAccidApi(workerNo).then(data => {
          console.log("ondisconnect-302", data);
        });
        break;
      case 422:
        // 账号被禁用，解禁去登录
        errorMsg = "您的账号被冻结，请对接总裁办进行解禁！";
        // 先去后台查询是否是正常的冻结，如果不是，在解禁
        unblockAccidApi(workerNo).then(data => {
          console.log("ondisconnect-422", data);
        });
        break;
      case "kicked":
        // 被踢, 请提示错误后跳转到登录页面
        errorMsg = `你的账号于${dateFormat(new Date(), "HH:mm")}被踢出下线，请确定账号信息安全!`
        break;
      case "logout":
        errorMsg = `${error.message}。已退出！`;
        break;
      case "Error_Connection_Socket_State_not_Match":
        errorMsg = "暂时连接不上第三方IM服务，请重新登录后再试！";
        break;
      default:
        break;
    }
    nim = {};
    remote.store.commit("setLogout", {type: 1, content: errorMsg});
  }
  emitMsg("msg", {type: "tray", tray: 3});
}

// 连接错误
function onerror(error) {
  console.log("onerror", error?.code, error);
  try {
    if (/IDBDatebase/.test(error)) {
      // 重新加载
      alert({
        content: "同步失败,消息可能丢失,需要重新登录乐聊-DB",
        okText: "重新登录",
        showCancel: false,
        done: () => {
          remote.store.commit("setRemoveDB", {workerNo: remote.store.getters.getUserInfo.workerNo, type: 1});
          setTimeout(() => {
            emitMsg("msg", {type: "logout", logout: 1});
          }, 1000);
        }
      });
    }
  } catch (e) {
    console.log("IDBDatebaseErr", e);
  }
}

// 同步完成
function onsyncdone() {
  try {
    console.log("消息同步完成onsyncdone");
    isAsyncDone = true;
    asyncCount++;
    timeMap.onsyncdone = new Date().getTime();
    console.log(`${timeMap.initFlag ? "初始化时间:" + (timeMap.onconnect - timeMap.init) : ""},同步完成时间:${timeMap.onsyncdone - timeMap.onconnect}`);
    uploadImTime();
    try {
      console.timeEnd("云信加载", {msgs: msgsLength, sessions: Object.values(remote.store.getters.getSessions()).length});
    } catch (e) {}
    remote.store.commit("setNimInfo", {type: "", time: ""});
    // 同步完成删除本地不存在的群会话
    remote.store.commit("setRemoveSessions");
  } catch (e) {
    console.log("onsyncdoneErr", e);
  }
}

// 多端登录变化
function onloginportschange(loginPorts) {
  console.log("当前登录账号在其它端的状态发生改变了", loginPorts);
  try {
    let userInfo = remote.store.getters.getUserInfo;
    getEmpStatusApi({
      msgBody: JSON.stringify({
        empNumber: userInfo.workerId,
        token: userInfo.token,
        appName: "pc-im"
      })
    });
  } catch (e) {
    console.log("getEmpStatusApiError", e);
  }
}

// 会话
function onsessions(sessions) {
  try {
    console.log("onsessions", sessions.map(item => {return {id: item.id, unread: item.unread, isTop: item.isTop, lastMsgIdServer: item.lastMsg ? item.lastMsg.idServer : "", updateTime: item.updateTime}}));
    remote.store.commit("setSessions", deepClone(sessions));
    // 初始化的时候获取自己会话的500条数据用于判断语音漫游
    remote.store.dispatch("initAudioTipMsg");
    // 判断清除@和特别关心消息
    remote.store.dispatch("setMsgTipsClear", {type: "sessionDB", clearAll: asyncCount == 0});
  } catch (e) {
    console.log("onsessionsErr", e);
  }
}

// 更新会话
function onupdatesession(session) {
  try {
    console.log("onupdatesession", `unread:${session.unread},id:${session.id},isTop:${session.isTop},lastMsgIdServer:${session.lastMsg?.idServer},lastMsgTime:${session.lastMsg?.time},updateTime:${session.updateTime},msgReceiptTime:${session.msgReceiptTime}`);
    // 打开 智能日程会话/消息平台会话 清空对应强提醒
    try {
      if (!session.unread && (session.to == config[config.env].scheduleNumber || session.to == config[config.env].msgCenterNumber)) {
        let remindMsg = [];
        // 关闭对应强提醒
        if (remote.store.getters.getRemindMsg.length > 0) {
          remote.store.getters.getRemindMsg.map(item => {
            // 打开智能日程/消息平台会话，设置还显示的强提醒
            if ((session.to == config[config.env].scheduleNumber && !(item.content.type == 'schedule_invite' || item.content.type == 'schedule_remind')) ||
              (session.to == config[config.env].msgCenterNumber && !(item.content.type == "msg-center" || item.content.type == "msg-report" || item.content.type == "msg-center-link"))) {
              remindMsg.push(item);
            }
          });
          remote.store.dispatch("setRemindMsg", {value: remindMsg});
        }
      }
    } catch (e) {}
    let thisSession = remote.store.getters.getSessions({id: session.id});
    // // 打开当前会话没更新不执行、对方发送消息回调2次不重复触发
    if (((thisSession.unread || 0) == session.unread && thisSession.updateTime == session.updateTime && thisSession.msgReceiptTime == session.msgReceiptTime)
      || (session.unread != 0 && thisSession.unread == session.unread && session.updateTime == session.msgReceiptTime)
    ) {
      return;
    }
    remote.store.commit("setSessions", session);
    remote.store.commit("setUpdateSessionId", session.id);
    let isChatChild = false;
    try {
      isChatChild = getChildWin("chat-" + session.id, "", true).isExit;
    } catch (e) {
      console.log("isChatChildErr", e);
    }
    if (!session.unread && !isChatChild) {
      // 打开会话才清空提示
      remote.store.dispatch("setMsgTipsClear", {type: "sessionDB", id: session.id});
    }
  } catch (e) {
    console.log("onupdatesessionErr", e);
  }
}

// 在线消息
function onmsg(type, obj) {
  console.log("onmsg-1on-2off-3roam:", type);
  let objInfo = {};
  try {
    if (type == 1) {
      objInfo.msgs = [obj];
    } else {
      objInfo = obj;
    }
    objInfo.id = obj.sessionId;
    // 标识从消息回调传递-首次登录离线漫游消息不同步
    objInfo.onmsg = type;
    objInfo.asyncCount = asyncCount;
    console.log("msgs", objInfo.msgs.map(item => {return {from: item.from, to: item.to, time: item.time, idServer: item.idServer, idClient: item.idClient}}));
    remote.store.dispatch("setMsgs", objInfo);
    msgsLength += objInfo.msgs.length;
  } catch (e) {
    console.log("onmsgErr", e, type, obj);
  }
}

// 群列表
function onteams(type, teams) {
  console.log("onteams-1team-2super:", type, teams);
  try {
    teams.map(item => {
      item.diyTeamType = type;
    });
  } catch (e) {
    console.log("onteamsErr", e);
  }
  let list = onteamCount == 0 ? [] : remote.store.getters.getTeams({sort: 1});
  onteamCount++;
  list = nim.mergeTeams(list, teams);
  list = nim.cutTeams(list, list.invalid);
  try {
    // 群全体禁言群主同步调用禁言api方法
    list.map(item => {
      if (item.custom && item.custom.isMute == 1 && item.owner == remote.store.getters.getUserInfo.workerNo && !item.mute) {
        nimSDK.updateTeamMute({mute: true, teamId: item.teamId});
      }
    });
  } catch (e) {
    console.log("onteamsErr", e);
  }
  remote.store.commit("setTeams", {teamList: list, initTeamFlag: true});
}

// 创建群回调
function onCreateTeam(type, info) {
  console.log("onCreateTeam", type, info);
  info.updateType = "create";
  remote.store.commit("setTeams", {teamList: [info]});
}

// 更新群回调
function onUpdateTeam(type, info) {
  console.log("onUpdateTeam", type, info);
  info.updateType = "update";
  info.scene = "team";
  if (type == 2) {
    info.scene = "superTeam";
  }
  remote.store.commit("updateTeam", info)
}

// 有人进群回调
function onAddTeamMembers(type, info) {
  console.log("onAddTeamMembers", type, info);
  info.team.diyTeamType = type;
  remote.store.commit("setTeams", {teamList: [info.team]});
  info.updateType = "add";
  remote.store.commit("updateTeamMembers", info);
}

// 有人出群回调
function onRemoveTeamMembers(type, info) {
  console.log("onRemoveTeamMembers", type, info);
  let scene = type == 1 ? "team" : "superTeam";
  info.updateType = "remove";
  remote.store.commit("updateTeamMembers", info);

  // 自己被移出群
  if (info.accounts.findIndex(item => {return item == workerNo}) != -1) {
    info.updateType = "remove";
    info.teamId = info.team.teamId
    let sessionId = scene + "-" + info.teamId;
    remote.store.commit("updateTeam", info);
    remote.store.dispatch("removeSession", sessionId);
    remote.store.dispatch("setNotification", {type: "del", session: {id: sessionId}});
    remote.store.commit("setEmit", {type: "leaveTeam", value: {id: sessionId}});
  }
}

//更新管理员回调
function onUpdateTeamManagers(type, info) {
  console.log("onUpdateTeamManagers", type, info);
  info.updateType = "update";
  remote.store.commit("updateTeamMembers", info);
}

//解散群回调
function onDismissTeam(type, info) {
  console.log("onDismissTeam", type, info);
  let scene = type == 1 ? "team" : "superTeam";
  let sessionId = scene + "-" + info.teamId;
  info.updateType = "dismiss";
  remote.store.commit("updateTeam", info);
  remote.store.dispatch("removeSession", sessionId);
  remote.store.commit("setEmit", {type: "leaveTeam", value: {id: sessionId}});
}

//移交群回调
function onTransferTeam(type, info) {
  console.log("onTransferTeam", type, info);
  info.teamId = info.team.teamId;
  info.updateType = "update";
  info.owner = info.team.owner;
  remote.store.commit("updateTeam", info);
  // 更新群成员类型
  info.members = [info.from, info.to];
  remote.store.commit("updateTeamMembers", info);
}

//群成员禁言回调
function onUpdateTeamMembersMute(type, info) {
  console.log("onUpdateTeamMembersMute", type, info);
  info.updateType = "update";
  remote.store.commit("updateTeamMembers", info);
}

// 同步好友列表
function onfriends(type, friends) {
  console.log("onfriends-1on-2off", type, friends)
  if (type == 1) {
    // 初始化好友列表
    remote.store.commit("setNimFriend", {type: "init", friends: friends});
  } else {
    // 漫游删除/添加/修改
    remote.store.commit("setNimFriend", friends);
  }
}

// 黑名单列表
function onblacklist(type, blacklist) {
  console.log("onblacklist-1on-2off:", type, blacklist);
  let list = [];
  if (type == 1) {
    list = remote.store.getters.getBlacklist({sort: 1});
    list = nim.mergeRelations(blacklist, list);
    list = nim.cutRelations(list, blacklist.invalid);
  } else if (type == 2) {
    // 多端同步为对象类型
    list = blacklist;
  }
  remote.store.dispatch("setBlacklist", list);
}

// 同步个人信息
function onmyinfo(type, data) {
  console.log("onmyinfo-1on-2off", type, data);
  try {
    if (data.custom) {
      let custom = JSON.parse(data.custom);
      if (custom) {
        if (custom.key === "group_setting") {
          // 加载漫游群分组配置
          remote.store.dispatch("setGroupSetting");
        } else {
          remote.store.commit("setSetting", custom);
        }
      }
    }
  } catch (e) {
    console.log("onmyinfoErr:", e);
  }
}

// 系统消息
function onsysmsg(type, obj) {
  console.log("onsysmsg-1on-2off-3roam-4update", type, obj);
  let objInfo = {
    onmsg: type,
    id: "",
    msgs: []
  };
  // 设置系统消息
  try {
    if (!Array.isArray(obj)) {
      obj = [obj];
    }
    for (let i = 0; i < obj.length; i++) {
      let objI = obj[i];
      let item = objI.msg;
      if (item) {
        // 撤回消息加入消息队列
        // 首次登录离线漫游消息不同步
        if (!(type != 1 && asyncCount == 0)) {
          if (obj[i].type == "deleteMsg") {
            item.type = "tip";
            item.tip = "撤回了一条消息";
            item.time = obj[i].time;
            item.deleteMsg = true;
            item.onmsg = type;
            objInfo.id = item.sessionId;
            objInfo.msgs.push(item);
            remote.store.state.updateSessionsTime = Date.now();
          }
        }
      } else {
        if (objI.attach) {
          item = objI.attach;
        }
        // 插入系统通知会话消息
        try {
          if (config.teamNotifyTypes.indexOf(objI.type) > -1) {
            remote.store.dispatch("setLocalSysMsgs", objI);
            remote.store.commit("getLocalSysMsgs");
          }
        } catch (e) {
          console.log("setLocalSysMsgsErr", e);
        }
      }
    }
    if (objInfo.msgs.length > 0) {
      remote.store.dispatch("setMsgs", objInfo);
    }
  } catch (e) {
    console.log("onsysmsgErr", e, type, obj);
  }
}

// 自定义系统消息
function oncustomsysmsg(type, obj) {
  console.log("oncustomsysmsg-1on-2off", type, obj);
  if (obj && obj.content) {
    let content = obj.content;
    try {
      if (typeof obj.content == "string") {
        content = JSON.parse(content);
      }
    } catch (e) {
    }
    if (content) {
      switch (content.type) {
        case "updateSchedule":
          // 收到智能日程操作多端同步 id-日程id idServer-消息id value-1关闭弹窗 status-参与状态1接受2待定3未回复4拒绝
          if (content.id && content.status) {
            // 设置本地日程状态
            remote.store.dispatch("setScheduleStatus", {id: content.id, idServer: content.idServer, status: content.status, customSysMsg: true, value: content.value})
          }
          break;
        case "updateDocument":
          // 收到文档操作多端同步-设置本地日程状态
          remote.store.dispatch("setDocStatus", content);
          break;
        case "updateSpecialConcernData":
          // 收到特别关心多端同步
          remote.store.dispatch("setAllConcernMap");
          break;
        case "refreshGroups":
          // 讨论组分组多端同步
          remote.store.dispatch("setGroupSetting");
          if (content.from != 1) {
            if (content.value) {
              let groupList = content.value.split(",");
              groupList.map(tid => {
                // 不存在会话则插入本地
                remote.store.dispatch("insertLocalSession", {scene: "team", to: tid});
              });
            }
            if (content.person) {
              let personList = content.person.split(",");
              personList.map(tid => {
                // 不存在会话则插入本地
                remote.store.dispatch("insertLocalSession", {scene: "p2p", to: tid});
              });
            }
          }
          break;
        case "refreshSetting":
          // 更新设置
          switch (content.key) {
            case config.settings.type13:
            case config.settings.type14:
            case config.settings.type16:
              // 13-更新分组模式排序
              // 14-客户进线讨论组静音开关
              // 16-更新分组模式排序（新）
              if (content.value) {
                remote.store.commit("setSetting", content);
              } else {
                // 暂时只有16
                remote.store.dispatch("updateAllSetting");
              }
              break;
          }
          break;
        case "updateAi":
          // 更新ai账号数据
          let updateObj = deepClone(content);
          delete updateObj.type;
          updateObj.headImg = updateObj.avatar;
          delete updateObj.avatar;
          let aiObj = remote.store.getters.getState("aiObj");
          let datas = [];
          if (updateObj.to == aiObj.workerNo) {
            remote.store.commit("setAiObj", updateObj);
            aiObj = deepClone(remote.store.getters.getState("aiObj"));
            // 设置本地缓存
            userLocalStorage({key: "aiObj", value: aiObj}, 1);
            datas = [setUserBaseInfo({
              workerNo: aiObj.workerNo,
              name: aiObj.name,
              selfIntro: aiObj.selfIntro,
              prologue: aiObj.prologue,
              headImg: aiObj.headImg,
            })];
          } else {
            updateObj.workerName = updateObj.name;
            updateObj = Object.assign(deepClone(remote.store.getters.getPersons(updateObj.to)), updateObj);
            datas = [setUserBaseInfo(updateObj)];
          }
          // 初始化更新本地数据库
          remote.store.dispatch("updatePersons", {account: updateObj.to, datas: datas});
          break;
      }
    }
  }
}

// 是否要忽略某条通知类消息
function shouldIgnoreNotification(msg) {
  // 超大群不触发onAddSuperTeamMembers，补偿处理-断网场景不触发群员更新，补偿处理
  try {
    let info = deepClone(msg.attach);
    if (!info.members && info.users) {
      info.members = info.users.map(item => {
        item.id = msg.to + "-" + item.account;
        return item
      });
    }
    if (msg.attach) {
      switch (msg.attach.type) {
        case "acceptSuperTeamInvite":
          onAddTeamMembers(2, info);
          break;
        case "acceptTeamInvite":
        case "passTeamApply":
        case "addTeamMembers":
          onAddTeamMembers(1, info);
          break;
        case "leaveTeam":
          info.accounts = info.members.map(item => {return item.account});
        case "removeTeamMembers":
          onRemoveTeamMembers(1, info);
          // 自己被移出群
          if (info.accounts.findIndex(item => {return item == workerNo}) != -1) {
            return true;
          }
          break;
        case "updateTeamMute":
          info.members = [{account: info.account, id: `${info.team.teamId}-${info.account}`, mute: info.mute, teamId: info.team.teamId, updateTime: info.team.memberUpdateTime}];
          onUpdateTeamMembersMute(1, info);
          break;
        case "transferTeam":
          info.from = {id: `${info.team.teamId}-${msg.from}`, type: "normal", updateTime: info.team.memberUpdateTime};
          info.to = {id: `${info.team.teamId}-${info.account}`, type: "owner", updateTime: info.team.memberUpdateTime};
          onTransferTeam(1, info);
          break;
        case "addTeamManagers":
        case "removeTeamManagers":
          let memberType = "normal";
          info.team = {teamId: msg.to, memberUpdateTime: msg.time};
          if (msg.attach.type == "addTeamManagers") {
            memberType = "manager";
          }
          info.members = info.accounts.map(item => {return {id: `${info.team.teamId}-${item}`, type: memberType, updateTime: info.team.memberUpdateTime}})
          onUpdateTeamManagers(1, info);
          break;
        case "dismissTeam":
          info.teamId = msg.to;
          onDismissTeam(1, info);
          return true;
          break;
      }
    }
  } catch (e) {
    console.log("shouldIgnoreNotificationErr1", e);
  }
  // 修改群信息都不更新会话列表
  try {
    if (msg && msg.attach && msg.scene != "p2p" && msg.type == 'notification') {
      // 获取当前成员在群信息
      let thisUserTeamInfo = remote.store.getters.getTeamMembersLocal({id: msg.to, account: workerNo});
      let isThisOwner = remote.store.getters.getTeams({id: msg.to}).owner == workerNo;
      let isTeamManager = thisUserTeamInfo.obj && /manager|owner/.test(thisUserTeamInfo.obj.type);
      let isNotify = isNotificationRole(msg, isThisOwner, isTeamManager);
      // 不存在通知解析删除该消息
      let hasText = remote.store.getters.getNotification(msg);
      if (isNotify && hasText) {
        return false;
      }
      return true;
    }
  } catch (e) {
    console.log("shouldIgnoreNotificationErr2", e);
  }
}

// 是否要忽略某条消息
function shouldIgnoreMsg(msg) {
  try {
    // 过滤语音消息已读通知
    if (msg && msg.scene == "p2p" && msg.from == msg.to && msg.type == 'tip' && msg.custom) {
      if (typeof msg.custom == "string") {
        try {
          msg.custom = JSON.parse(msg.custom);
        } catch (e) {
        }
      }
      if (msg.custom.type == "audio" || msg.custom.type == "hide") {
        remote.store.dispatch("setAudioTipMsg", [msg]);
        return true;
      }
    }
  } catch (e) {
    console.log("shouldIgnoreNotificationErr-audio", e);
  }
}

// 订阅事件推送
function onpushevents(param) {
  if (param?.msgEvents?.length > 0) {
    for (let i = 0; i < param.msgEvents.length; i++) {
      try {
        let item = param.msgEvents[i];
        if (item.type == 1) {
          // 设置fcw用户在线状态
          let serverConfig = JSON.parse(item.serverConfig);
          if (serverConfig.online.length > 0) {
            remote.store.commit("setFcwOnlineMap", {type: "add", key: item.account, value: serverConfig.online});
          } else {
            remote.store.commit("setFcwOnlineMap", {type: "del", key: item.account});
          }
          remote.store.commit("setUpdateSessionId", "p2p-" + item.account);
        }
      } catch (e) {
        console.log("onpusheventsErr", e);
      }
    }
  }
}

// 推送提醒内容
function getPushContentParam(param) {
  let contentParam = {userName: remote.store.getters.getUserInfo.workerName};
  if (param.scene != "p2p" && remote.store.getters.getTeams({id: param.to})) {
    contentParam.teamName = remote.store.getters.getTeams({id: param.to}).name;
  }
  return contentParam;
}

// 云信连接超时提示
function loadTimeout() {
  let time = Number(localStorage.getItem("loadImTime") || 5) * 60 * 1000;
  // 消息5分钟未同步成功提示
  if (timeMap.timer) {
    clearTimeout(timeMap.timer);
  }
  timeMap.timer = setTimeout(() => {
    if (!isAsyncDone) {
      remote.store.commit("setNimInfo", {type: "err", time: Date.now()});
      uploadImTime();
    }
  }, time);
}

// 上传用户连接云信时间
function uploadImTime() {
  // 上传用户初始化时间
  let zxpParam = {
    type: 3,
    load_time: timeMap.onsyncdone - timeMap.onconnect,
  }
  if (timeMap.initFlag || timeMap.initloadFlag) {
    timeMap.initFlag = false;
    timeMap.initloadFlag = false;
    zxpParam.init_time = timeMap.onconnect - timeMap.init
  }
  remote.store.dispatch("uploadZxp", zxpParam);
}

// 设置移动端要求字段pushPayload
function getPushPayload(obj = {}) {
  // 群会话不变，单聊会话改回当前发送人
  let userInfo = remote.store.getters.getUserInfo;
  if (obj.sessionType == "p2p" && obj.sessionId) {
    obj.sessionId = userInfo.workerNo;
  }
  obj.sessionType = obj.sessionType == "p2p" ? 0 : 1;
  obj.sessionImage = userInfo.headPic;
  let defaultObj = {
    vivoField: {"classification": 1},
    oppoField: {"channel_id": "leliaoIM_01"},
    hwField: {"importance": "NORMAL"},
    channel_id: "high_system",
    apsField: {"mutable-content": 1},
  }
  defaultObj = Object.assign(defaultObj, obj);
  return JSON.stringify(defaultObj);
}

/***以下为暴露给外部的云信sdk方法***/
// 退出登录
nimSDK.disconnect = function (param) {
  if (nim.disconnect) {
    remote.store.commit("setNim", {});
    nim.disconnect();
    nim = {};
  }
}

// 设置当前会话和重置当前会话 type-1设置当前会话-2重置会话
nimSDK.setCurrSessionState = function (param) {
  let {id, type} = param;
  if (param.type == 1) {
    nim.setCurrSession(id);
    if (id == "p2p-" + config.systemMessage) {
      nim.updateLocalSession({
        id: id,
        localCustom: {unread: 0, updateTime: remote.store.getters.getSessions({id: id}).updateTime}
      });
    }
  } else if (type == 2) {
    nim.resetCurrSession();
  }
}

// 设置会话已读
nimSDK.sendMsgReceipt = function (msg) {
  if (msg.status == "success" && !remote.store.getters.getNimInfo.type) {
    // 对应字段转为字符串
    msg = setMsgString(deepClone(msg));
    nim.sendMsgReceipt({
      msg: msg,
      done: (err, obj) => {
        if (err) {
          console.log("sendMsgReceiptErr:", err, obj);
        }
      }
    });
  }
}

// 删除会话
nimSDK.deleteLocalSession = function (param) {
  return new Promise((resolve) => {
    let initFlag = false;
    if (param.initFlag) {
      initFlag = true;
      delete param.initFlag;
    }
    nim.deleteLocalSession({
      ...param,
      done: (err, obj) => {
        console.log(`删除会话记录${initFlag ? "init" : ""}:`, err, obj);
        resolve({err: err, obj: obj})
      }
    })
  });
}

// 插入会话
nimSDK.insertLocalSession = function (param) {
  return new Promise((resolve) => {
    nim.insertLocalSession({
      ...param,
      done: (err, obj) => {
        console.log("打开会话记录:", err, obj);
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 获取历史记录
nimSDK.getHistoryMsgs = function (param) {
  return new Promise((resolve) => {
    nim.getHistoryMsgs({
      ...param,
      done: (err, obj) => {
        console.log("getHistoryMsgsDone", param);
        if (err) {
          console.log("getHistoryMsgsErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 更改个人信息
nimSDK.modifyMyInfo = async function (param) {
  return new Promise((resolve) => {
    nim.updateMyInfo({
      custom: JSON.stringify(param),
      done: (err, obj) => {
        if (err) {
          console.log("modifyMyInfoErr", err);
        }
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 发送文本消息
nimSDK.sendText = async function (param) {
  console.log("nim-sendText");
  param.text = remote.store.getters.getKeyText(param.text);
  param.pushContent = remote.store.getters.getKeyText(param.pushContent);
  // 房产网敏感词命中词
  remote.store.getters.getFcwKeyMsg(param);
  return new Promise((resolve) => {
    nim.sendText({
      needPushNick: false,
      pushPayload: getPushPayload({sessionType: param.scene, sessionId: param.to}),
      ...param,
      done: (err, obj) => {
        if (err) {
          console.log("sendTextErr", err);
        }
        obj.uniqueSign = param.uniqueSign;
        obj.fileMsgFlag = param.fileMsgFlag;
        obj.autoResendFlag = param.autoResendFlag;
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 发送文件消息-type默认为file可指定image
nimSDK.sendFile = async function (param) {
  console.log("nim-sendFile");
  let {thisMsg} = param;
  delete param.thisMsg;
  let blob = param.blob || {};
  delete param.blob;
  let dataURL = "";
  // 兼容重新发送
  if (blob.filePath || blob.path) {
    param.blob = await getLocalFile({filePath: blob.filePath || blob.path, blob: true});
    if (param.type == "image") {
      dataURL = await getLocalFile({filePath: blob.filePath || blob.path, base64: true});
    }
  } else {
    if (/data:image\//.test(thisMsg.file.url)) {
      dataURL = thisMsg.file.url;
    } else {
      dataURL = await fileToDataURL(thisMsg.file);
    }
    param.blob = dataUrlToBlob(dataURL);
  }
  let defaultName = "乐聊" + new Date().getTime() + uniqueSignCount + "." + (getFileExt(blob.name, 1) || getBase64Ext(param.blob.type, 1));
  param.blob.name = blob.name || defaultName;
  // nodejs生成的file对象没有path。额外设置了filePath
  param.blob.path = blob.path || blob.filePath;
  // 图片类型缓存到缓存目录
  let cachedPath = "";
  let cachedName = "";
  if ((param.type == "image" || /image\//.test(param.blob.type)) && param.blob.path && !removeReplace(param.blob.name, true)) {
    cachedPath = getFileCachedPath({type: 1, account: remote.store.getters.getUserInfo.workerNo});
    cachedName = defaultName;
    fs.copyFile(param.blob.path, path.join(cachedPath, defaultName), function () {});
  }
  return new Promise(async (resolve) => {
    // 图片检测是否能发送
    if (param.type == "image") {
      let canSend = await remote.store.dispatch("setSensitiveWords", (dataURL || "").replace("data:image/png;base64,", ""));
      if (!canSend) {
        thisMsg.status = "fail";
        remote.store.dispatch("setMsgs", {id: thisMsg.id, msgs: [thisMsg]});
        resolve({err: {message: "notTips"}, obj: thisMsg});
        return;
      }
    }
    console.log("nim-sendFile-api");
    nim.sendFile({
      needPushNick: false,
      pushPayload: getPushPayload({sessionType: param.scene, sessionId: param.to}),
      ...param,
      beginupload: upload => {
        // 开始上传
        console.log("beginupload", upload);
        remote.store.commit("setNimFileUpload", {key: thisMsg.uniqueSign, value: {notChange: true, upload: upload}});
        remote.store.dispatch("setMsgs", {id: thisMsg.id, msgs: [thisMsg]});
      },
      uploadprogress: data => {
        // 上传进度
        thisMsg.file.progress = data && data.percentage || 0;
        if (thisMsg.file.progress < 10 || thisMsg.file.progress > 90) {
          console.log("uploadprogress", data);
        }
        // 通知更新消息
        remote.store.commit("setEmit", {type: "reloadMsg", value: {id: thisMsg.sessionId, uniqueSign: thisMsg.uniqueSign, type: "update", time: Date.now()}});
      },
      uploaddone: (err, obj) => {
        // 上传完成
        console.log("uploaddone", err, obj);
        if (err) {
          if (err.code != "abort" || err.code == "Error_Connection_Socket_State_not_Match") {
            // 不是主动中断提示发送失败
            thisMsg.status = "fail";
            delete thisMsg.file.progress;
            remote.store.dispatch("setMsgs", {id: thisMsg.id, msgs: [thisMsg]});
          } else {
            // 主动中断删除该消息
            thisMsg.toDel = true;
            remote.store.dispatch("setMsgs", {id: thisMsg.id, msgs: [thisMsg]});
          }
        } else {
          // 加入本地缓存
          let thisUrl = obj.url;
          let thisPath = param.blob.path;
          if (thisPath && !removeReplace(param.blob.name, true)) {
            thisMsg.fileDBParam = {
              path: cachedPath ? cachedPath : thisPath.replace(param.blob.name, ""),
              name: cachedName ? cachedName : param.blob.name,
              total: param.blob.size,
              url: thisUrl
            };
            if (cachedPath) {
              saveImageLocal(thisMsg.fileDBParam);
            }
          }
          remote.store.commit("setEmit", {type: "reloadMsg", value: {id: thisMsg.sessionId, uniqueSign: thisMsg.uniqueSign, type: "updateDone", time: Date.now()}});
        }
        remote.store.commit("setNimFileUpload", {key: thisMsg.uniqueSign, value: "del"});
      },
      beforesend: msg => {
        // 开始发送
        console.log("beforesend", msg);
        msg.uniqueSign = param.uniqueSign;
        msg.fileMsgFlag = param.fileMsgFlag;
        msg.autoResendFlag = param.autoResendFlag;
        remote.store.dispatch("setMsgs", {id: msg.sessionId, msgs: [msg]});
      },
      done: (err, obj) => {
        if (err) {
          delete thisMsg.file.progress;
          console.log("sendFileErr", err);
          if (thisMsg.toDel) {
            err.message = "notTips";
          }
          thisMsg.status = "fail";
          obj = thisMsg;
        }
        obj.uniqueSign = param.uniqueSign;
        obj.fileMsgFlag = param.fileMsgFlag;
        obj.autoResendFlag = param.autoResendFlag;
        if (thisMsg.fileDBParam) {
          addFileDB(remote.store.getters.getFileDB, MD5(thisMsg.fileDBParam.url), thisMsg.fileDBParam).then(res => {
            resolve({err: err, obj: obj});
          });
        } else {
          resolve({err: err, obj: obj});
        }
      }
    });
  });
}

// 上传文件-最大100M
nimSDK.previewFile = async function (param) {
  console.log("nim-previewFile");
  // 图片检测是否能发送
  let canSend = await remote.store.dispatch("setSensitiveWords", (param.dataURL || "").replace("data:image/png;base64,", ""));
  return new Promise((resolve, reject) => {
    if (!canSend) {
      resolve({
        name: param.name,
        type: param.type,
        file: param
      });
      return;
    }
    console.log("nim-previewFile-api");
    nim.previewFile({
      ...param,
      uploadprogress: function (data) {
        let progress = data && data.percentage || 0;
        if (progress < 10 || progress > 90) {
          console.log("previewFile-uploadprogress", data);
        }
      },
      done: function (err, obj) {
        if (err) {
          console.log("previewFileErr", err, obj);
          reject({
            name: param.name,
            type: param.type,
            file: param,
            error: true,
            err: err
          });
        } else {
          resolve({
            name: param.name,
            type: param.type,
            file: obj
          });
        }
      }
    });
  })
}

// 发送自定义消息
nimSDK.sendCustomMsg = function (param) {
  console.log("nim-sendCustomMsg");
  let {thisMsg} = param;
  delete param.thisMsg;
  return new Promise((resolve, reject) => {
    nim.sendCustomMsg({
      needPushNick: false,
      pushPayload: getPushPayload({sessionType: param.scene, sessionId: param.to}),
      ...param,
      done: function (err, obj) {
        if (err) {
          console.log("sendCustomMsgErr", err);
          thisMsg.status = "fail";
          obj = thisMsg;
        }
        obj.uniqueSign = param.uniqueSign;
        obj.fileMsgFlag = param.fileMsgFlag;
        obj.autoResendFlag = param.autoResendFlag;
        resolve({err: err, obj: obj})
      }
    });
  })
}

// 发送自定义系统消息
nimSDK.sendCustomSysMsg = function (param) {
  return new Promise((resolve, reject) => {
    nim.sendCustomSysMsg({
      ...param,
      sendToOnlineUsersOnly: false,
      done: function (err, obj) {
        if (err) {
          console.log("sendCustomSysMsgErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 发送提示消息
nimSDK.sendTipMsg = function (param) {
  return new Promise((resolve, reject) => {
    nim.sendTipMsg({
      ...param,
      isPushable: false,
      done: function (err, obj) {
        if (err) {
          console.log("sendTipMsgErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 发送位置
nimSDK.sendGeoMsg = function (param) {
  return new Promise((resolve) => {
    nim.sendGeo({
      needPushNick: false,
      pushPayload: getPushPayload({sessionType: param.scene, sessionId: param.to}),
      ...param,
      done: (err, obj) => {
        if (err) {
          console.log("sendGeoErr", err);
        }
        obj.uniqueSign = param.uniqueSign;
        obj.fileMsgFlag = param.fileMsgFlag;
        obj.autoResendFlag = param.autoResendFlag;
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 转发消息
nimSDK.forwardMsg = function (param) {
  console.log("nim-forwardMsg");
  return new Promise(async (resolve, reject) => {
    // 重发消息处理
    uniqueSignCount++;
    let uniqueSign = param.msg.uniqueSign || MD5(`sign_${param.scene}_${param.to}_${Date.now()}_${uniqueSignCount}`);
    delete param.msg.idServer;
    delete param.msg.idClient;
    // 当前时间小于最后一条消息时间
    let thisMsgTime = Date.now() + remote.store.getters.getDiffTime;
    let sessionId = param.scene + "-" + param.to;
    let thisSessionMsgs = remote.store.getters.getMsgs({id: sessionId});
    if (thisSessionMsgs && thisSessionMsgs.length > 0 && thisSessionMsgs[thisSessionMsgs.length - 1].time > thisMsgTime) {
      thisMsgTime = thisSessionMsgs[thisSessionMsgs.length - 1].time + 100;
    }
    param.msg.uniqueSign = uniqueSign;
    param.msg.status = "toSending";
    param.msg.scene = param.scene;
    param.msg.from = workerNo;
    param.msg.to = param.to;
    param.msg.target = param.to;
    param.msg.sessionId = sessionId;
    param.msg.time = thisMsgTime;
    // 显示发送中
    remote.store.dispatch("setMsgs", {id: sessionId, msgs: [deepClone(param.msg)]});
    if (!param.pushContent) {
      // 推送提示
      let contentParam = getPushContentParam(param);
      contentParam.text = remote.store.getters.getPrimaryMsg({...param, primaryType: 1, notChange: true});
      param.pushContent = getPushContent(contentParam) || "";
    }
    // 图片检测是否能发送
    if (param.msg.type == "image") {
      let canSend = await remote.store.dispatch("setSensitiveWords", param.msg.file.url);
      if (!canSend) {
        param.msg.status = "fail";
        remote.store.dispatch("setMsgs", {id: sessionId, msgs: [param.msg]});
        resolve({err: {message: "notTips"}, obj: param.msg});
        return;
      }
    } else if (param.msg.type == "custom") {
      let msgContent = param.msg.content;
      try {
        msgContent = JSON.parse(param.msg.content);
      } catch (e) {}
      if (msgContent && msgContent.msgs) {
        for (let i = 0; i < msgContent.msgs.length; i++) {
          if (msgContent.msgs[i].type == "image") {
            let canSend = await remote.store.dispatch("setSensitiveWords", msgContent.msgs[i].file.url);
            if (!canSend) {
              param.msg.status = "fail";
              remote.store.dispatch("setMsgs", {id: sessionId, msgs: [param.msg]});
              resolve({err: {message: "notTips"}, obj: param.msg});
              return;
            }
          }
        }
      }
    }
    dealMsgField(param.msg);
    console.log("nim-forwardMsg-api");
    nim.forwardMsg({
      ...param,
      isPushable: false,
      done: function (err, obj) {
        if (err) {
          console.log("forwardMsgErr", err);
        }
        obj.uniqueSign = uniqueSign;
        obj.fileMsgFlag = param?.msg?.fileMsgFlag;
        obj.uniqueSignForward = param?.msg?.uniqueSignForward;
        obj.autoResendFlag = param?.msg?.autoResendFlag;
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 转发额外文字
nimSDK.forwardMsgText = function (param) {
  return new Promise((resolve, reject) => {
    let contentParam = getPushContentParam(param);
    contentParam.text = param.otherText;
    // 不存在发送唯一标识生成
    if (!param.uniqueSign) {
      uniqueSignCount++;
      param.uniqueSign = MD5(`sign_${param.scene}_${param.to}_${Date.now()}_${uniqueSignCount}`);
    }
    nimSDK.sendText({
      scene: param.scene,
      to: param.to,
      text: param.otherText,
      pushContent: getPushContent(contentParam)
    }).then(res => {
      res.obj.uniqueSign = param.uniqueSign;
      if (res.err) {
        console.log("forwardMsgTextErr", res.err);
      } else {
        res.obj.fileMsgFlag = param.fileMsgFlag;
        res.obj.autoResendFlag = param.autoResendFlag;
      }
      resolve(res);
    });
  });
}

// 消息重发
nimSDK.resendMsg = function (msg) {
  console.log("nim-resendMsg");
  return new Promise((resolve, reject) => {
    if (msg && msg.text) {
      msg.text = remote.store.getters.getKeyText(msg.text);
    }
    if (msg && msg.pushContent) {
      msg.pushContent = remote.store.getters.getKeyText(msg.pushContent);
    }
    // 房产网敏感词命中词
    remote.store.getters.getFcwKeyMsg(msg);
    // 删除对应重发消息
    msg.toDel = true;
    remote.store.dispatch("setMsgs", {id: msg.sessionId, msgs: [deepClone(msg)]}).then(res => {
      remote.store.dispatch("scrollBottom", {sessionId: msg.sessionId});
    });
    delete msg.toDel;
    // 显示发送中
    msg.status = "toSending";
    remote.store.dispatch("setMsgs", {id: msg.sessionId, msgs: [deepClone(msg)]}).then(res => {
      remote.store.dispatch("scrollBottom", {sessionId: msg.sessionId});
    });
    // 重发消息状态需要fail
    let sendMsg = deepClone(msg);
    sendMsg.status = "fail";
    console.log("nim-resendMsg-api");
    try {
      nim.resendMsg({
        msg: sendMsg,
        done: function (err, obj) {
          if (err) {
            console.log("resendMsgErr", err);
          }
          obj.uniqueSign = msg.uniqueSign;
          obj.uniqueSignForward = msg.uniqueSignForward;
          obj.autoResendFlag = msg.autoResendFlag;
          resolve({err: err, obj: obj})
        }
      });
    } catch (e) {
      msg.status = "fail";
      remote.store.dispatch("setMsgs", {id: msg.id, msgs: [deepClone(msg)]});
      console.log("resendMsg3Err", e)
    }
  });
}

// 获取服务器系统时间
nimSDK.getServerTime = function (param) {
  return new Promise((resolve, reject) => {
    if (remote.openExternalChild) {
      remote.openExternalChild = false;
      resolve({obj: Date.now() + remote.store.state.diffTime});
    } else {
      nim.getServerTime({
        done: function (err, obj) {
          if (err) {
            console.log("getServerTimeErr", err);
          }
          resolve({err: err, obj: obj})
        }
      });
    }
  });
}

// 设置会话置顶信息
nimSDK.updateStickTopSession = function (param) {
  return new Promise((resolve, reject) => {
    if (param.operation == "add") {//置顶
      nim.addStickTopSession({
        id: param.value,
        done: function (err, obj) {
          console.log("addStickTopSession", err, obj, param);
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.deleteStickTopSession({
        id: param.value,
        done: function (err, obj) {
          console.log("deleteStickTopSession", err, obj, param);
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

//同步群设置操作
nimSDK.updateInfoInTeam = function (param, detailType) {
  let muteNotiType = -1;//0表示接收提醒，1表示关闭提醒，2表示仅接收管理员提醒 ,-1不设置(保持现状)
  if ((param.key == config.settings.type2 && param.operation == 'add') || (param.key == config.settings.type1 && param.operation == 'add')) {
    muteNotiType = 1;
  } else if ((param.key == config.settings.type2 && param.operation == 'delete') || (param.key == config.settings.type1 && param.operation == 'delete')) {
    muteNotiType = 0;
  }
  if (muteNotiType == -1) {
    return;
  }
  return new Promise((resolve, reject) => {
    if (detailType == "superTeam") {
      nim.updateInfoInSuperTeam({
        teamId: param.value.split('-')[1],
        muteNotiType: muteNotiType,
        custom: '{}',
        done: function (err, obj) {
          console.log("updateInfoInSuperTeam", err, obj, param);
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.updateInfoInTeam({
        teamId: param.value.split('-')[1],
        muteNotiType: muteNotiType,
        done: function (err, obj) {
          console.log("updateInfoInTeam", err, obj, param);
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 撤回消息
nimSDK.deleteMsg = function (msg) {
  return new Promise((resolve, reject) => {
    nim.deleteMsg({
      msg: msg,
      done: function (err, obj) {
        if (err) {
          console.log("deleteMsgErr", err);
        } else {
          obj.msg.type = "tip";
          obj.msg.tip = "你撤回了一条消息";
          obj.msg.deleteMsg = true;
          obj.msg.onmsg = 1;
          let objInfo = {
            id: obj.msg.sessionId,
            msgs: [obj.msg]
          };
          // 撤回消息加入消息队列
          remote.store.dispatch("setMsgs", objInfo);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 解散讨论组-！！！禁止群调用(后台维护群数据)！！！
nimSDK.dismissTeam = function (teamId) {
  let team = remote.store.getters.getTeams({id: teamId});
  if (team && team.detailType != "group") {
    return;
  }
  return new Promise((resolve, reject) => {
    nim.dismissTeam({
      teamId: teamId,
      done: function (err, obj) {
        if (err) {
          console.log("dismissTeamErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 退出讨论组-！！！禁止群调用(后台维护群数据)！！！
nimSDK.leaveTeam = function (teamId) {
  let team = remote.store.getters.getTeams({id: teamId});
  if (team && team.detailType != "group") {
    return;
  }
  return new Promise((resolve, reject) => {
    nim.leaveTeam({
      teamId: teamId,
      done: function (err, obj) {
        if (err) {
          console.log("leaveTeamErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 申请入群
nimSDK.applyTeam = function (param) {
  return new Promise((resolve, reject) => {
    let {scene, teamId} = param;
    if (scene == "superTeam") {
      nim.applySuperTeam({
        teamId: teamId,
        done: function (err, obj) {
          if (err) {
            console.log("applySuperTeamErr", err);
          }
          resolve({err: err, obj: obj})
        }
      });
    } else {
      nim.applyTeam({
        teamId: teamId,
        done: function (err, obj) {
          if (err) {
            console.log("applyTeamErr", err);
          }
          resolve({err: err, obj: obj})
        }
      });
    }
  });
}

// 邀人入群
nimSDK.addTeamMembers = function (param) {
  let team = remote.store.getters.getTeams({id: param.teamId});
  if (team && team.detailType != "group") {
    return;
  }
  return new Promise((resolve, reject) => {
    nim.addTeamMembers({
      ...param,
      done: function (err, obj) {
        if (err) {
          console.log("addTeamMembersErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}
// 踢人出群
nimSDK.removeTeamMembers = function (param) {
  if (remote.store.getters.getTeams({id: param.teamId}).detailType != "group") {
    return;
  }
  return new Promise((resolve, reject) => {
    nim.removeTeamMembers({
      ...param,
      done: function (err, obj) {
        if (err) {
          console.log("removeTeamMembersErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 群成员禁言
nimSDK.updateMuteStateInTeam = function (param) {
  let teamInfo = remote.store.getters.getTeams({id: param.teamId});
  return new Promise((resolve, reject) => {
    if (teamInfo.detailType == "superTeam") {
      nim.updateSuperTeamMembersMute({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("updateSuperTeamMembersMuteErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    } else {
      let account = param.accounts[0];
      delete param.accounts;
      nim.updateMuteStateInTeam({
        ...param,
        account: account,
        done: function (err, obj) {
          if (err) {
            console.log("updateMuteStateInTeamErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 群禁言
nimSDK.updateTeamMute = function (param) {
  let teamInfo = remote.store.getters.getTeams({id: param.teamId});
  return new Promise((resolve, reject) => {
    if (teamInfo.detailType == "superTeam") {
      nim.updateSuperTeamMute({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("updateSuperTeamMuteErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.muteTeamAll({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("muteTeamAllErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 更新群信息
nimSDK.updateTeam = function (param) {
  let teamInfo = remote.store.getters.getTeams({id: param.teamId});
  return new Promise((resolve, reject) => {
    if (teamInfo.detailType == "superTeam") {
      nim.updateSuperTeam({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("updateSuperTeamErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.updateTeam({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("updateTeamErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 获取群禁言列表
nimSDK.getMutedTeamMembers = function (param) {
  let teamInfo = remote.store.getters.getTeams({id: param.teamId});
  return new Promise((resolve, reject) => {
    if (teamInfo.detailType == "superTeam") {
      nim.getMutedSuperTeamMembers({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("getMutedSuperTeamMembersErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.getMutedTeamMembers({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("getMutedTeamMembersErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 获取群成员在群内的信息
nimSDK.getTeamMemberByTeamIdAndAccount = function (param) {
  let teamInfo = remote.store.getters.getTeams({id: param.teamId});
  return new Promise((resolve, reject) => {
    if (teamInfo.detailType == "superTeam") {
      nim.getSuperTeamMembersByAccounts({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("getSuperTeamMembersByAccountsErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.getTeamMemberByTeamIdAndAccount({
        ...param,
        done: function (err, obj) {
          if (err) {
            console.log("getTeamMemberByTeamIdAndAccountErr", err);
          }
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 标记会话已读
nimSDK.resetSessionUnread = function (id) {
  nim.resetSessionUnread(id);
}

// 黑名单
nimSDK.markInBlacklist = function (param) {
  let {account, isAdd} = param;
  return new Promise((resolve, reject) => {
    nim.markInBlacklist({
      account: account,
      // true表示加入黑名单，false表示从黑名单移除
      isAdd: isAdd,
      done: function (err, obj) {
        if (err) {
          console.log("markInBlacklistErr", err);
        }
        resolve({err: err, obj: obj})
      }
    });
  });
}

// 获取本地会话列表
nimSDK.getLocalSessions = function () {
  return new Promise((resolve, reject) => {
    nim.getLocalSessions({
      done: function (err, obj) {
        if (err) {
          console.log("getLocalSessionsErr", err);
        } else {
          console.log('getLocalSessions', obj.sessions.map(item => {return {id: item.id, unread: item.unread, isTop: item.isTop, lastMsgIdServer: item.lastMsg ? item.lastMsg.idServer : ""}}));
        }
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 获取本地群信息
nimSDK.getLocalTeams = function (param) {
  return new Promise((resolve, reject) => {
    nim.getLocalTeams({
      ...param,
      done: function (err, obj) {
        if (err) {
          console.log("getLocalTeamsErr", err);
        }
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 更新本地会话
nimSDK.updateLocalSession = function (param) {
  return new Promise((resolve, reject) => {
    nim.updateLocalSession({
      ...param,
      done: function (err, obj) {
        if (err) {
          console.log("updateLocalSessionErr", err);
        }
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 获取群成员
nimSDK.getTeamMembers = function (param) {
  let {id} = param;
  return new Promise((resolve, reject) => {
    let teamInfo = remote.store.getters.getTeams({id: id});
    if (!teamInfo) {
      console.log("不存在群信息", id);
      return;
    }
    let members = "";
    if (teamInfo.detailType == "superTeam") {
      // 获取超大群成员
      remote.store.commit("setTeamMembers", {id: id, members: []});
      resolve({err: "", obj: []});
      // nim.getAllSuperTeamMembers({
      //   teamId: id,
      //   done: function (err, obj) {
      //     members = obj.members || [];
      //     if (err) {
      //       console.log("getAllSuperTeamMembersErr", err);
      //       remote.store.commit("setTeamMembers", {id: id, del: true});
      //     } else {
      //       // 去除多余字段
      //       delete members.invalid;
      //       remote.store.commit("setTeamMembers", {id: id, members: members});
      //     }
      //     resolve({err: err, obj: members});
      //   }
      // });
    } else {
      // 获取普通群成员
      nim.getTeamMembers({
        teamId: id,
        done: function (err, obj) {
          members = obj.members || [];
          // 802无权限
          if (err && err.code != 802) {
            console.log("getTeamMembersErr", err, obj);
            remote.store.commit("setTeamMembers", {id: id, del: true});
          } else {
            // 去除多余字段
            delete members.invalid;
            remote.store.commit("setTeamMembers", {id: id, members: members, notReqFlag: true});
          }
          resolve({err: err, obj: members});
        }
      });
    }
  });
}

// 获取本地通知信息
nimSDK.getLocalSysMsgs = function () {
  return new Promise((resolve, reject) => {
    nim.getLocalSysMsgs({
      done: function (err, obj) {
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 获取群信息，type="superTeam"||"team"
nimSDK.getTeam = function (params) {
  var account = params.account;
  var type = params.type;
  return new Promise((resolve, reject) => {
    if (type == "superTeam") {
      nim.getSuperTeam({
        teamId: account,
        sync: true,
        done: function (err, obj) {
          resolve({err: err, obj: obj});
        }
      });
    } else {
      nim.getTeam({
        teamId: account,
        done: function (err, obj) {
          resolve({err: err, obj: obj});
        }
      });
    }
  });
}

// 接受入群邀请
nimSDK.acceptTeamInvite = function (params) {
  var teamId = params.teamId;
  var from = params.from;
  var idServer = params.idServer;
  var type = params.type;
  return new Promise((resolve, reject) => {
    if (type == "superTeam") {
      nim.acceptSuperTeamInvite({
        teamId,
        from,
        idServer,
        done: function (err, obj) {
          let state = 'passed';
          if (err) state = 'error'
          remote.store.commit('updateLocalSysMsgs', {state, idServer})
          resolve({err: err, obj: obj});
        }
      })
    } else {
      nim.acceptTeamInvite({
        teamId,
        from,
        idServer,
        done: function (err, obj) {
          if (err) {
            if (err.code == 807) {
              err.message = "该邀请已失效";
            } else if (err.code == 809) {
              err.message = "该人员已在群里";
            }
          }
          resolve({err: err, obj: obj});
        }
      })
    }
  })
}

// 通过入群申请
nimSDK.passTeamApply = function (params) {
  var teamId = params.teamId;
  var from = params.from;
  var idServer = params.idServer;
  var type = params.type;
  return new Promise((resolve, reject) => {
    if (type == "superTeam") {
      nim.passSuperTeamApply({
        teamId,
        from,
        idServer,
        done: function (err, obj) {
          let state = 'passed';
          if (err) state = 'error'
          remote.store.commit('updateLocalSysMsgs', {state, idServer})
          resolve({err: err, obj: obj});
        }
      })
    } else {
      nim.passTeamApply({
        teamId,
        from,
        idServer,
        done: function (err, obj) {
          if (err) {
            if (err.code == 509) {
              err.message = "该申请已失效";
            } else if (err.code == 809) {
              err.message = "该人员已在群里";
            }
          }
          resolve({err: err, obj: obj});
        }
      })
    }
  })
}

// 拒绝入群申请
nimSDK.rejectTeamApply = function (params) {
  var teamId = params.teamId;
  var from = params.from;
  var idServer = params.idServer;
  var type = params.type;
  return new Promise((resolve, reject) => {
    if (type == "superTeam") {
      nim.rejectSuperTeamApply({
        teamId,
        from,
        idServer,
        done: function (err, obj) {
          let state = 'rejected';
          if (err) state = 'error'
          remote.store.commit('updateLocalSysMsgs', {state, idServer})
          resolve({err: err, obj: obj});
        }
      })
    } else {
      nim.rejectTeamApply({
        teamId,
        from,
        idServer,
        done: function (err, obj) {
          if (err) {
            if (err.code == 509) {
              err.message = "该申请已失效";
            } else if (err.code == 809) {
              err.message = "该人员已在群里";
            }
          }
          resolve({err: err, obj: obj});
        }
      })
    }
  })
}

// 拒绝入群邀请
nimSDK.rejectTeamInvite = function (params) {
  var teamId = params.teamId;
  var from = params.from;
  var idServer = params.idServer;
  var type = params.type;
  var ps = params.ps;
  return new Promise((resolve, reject) => {
    if (type == "superTeam") {
      nim.rejectSuperTeamInvite({
        teamId,
        from,
        idServer,
        ps,
        done: function (err, obj) {
          let state = 'rejected';
          if (err) state = 'error'
          remote.store.commit('updateLocalSysMsgs', {state, idServer})
          resolve({err: err, obj: obj});
        }
      })
    } else {
      nim.rejectTeamInvite({
        teamId,
        from,
        idServer,
        ps,
        done: function (err, obj) {
          resolve({err: err, obj: obj});
        }
      })
    }
  })
}

// 删除系统消息
nimSDK.deleteAllLocalSysMsgs = function () {
  return new Promise((resolve, reject) => {
    nim.deleteAllLocalSysMsgs({
      done: function (err, obj) {
        resolve({err: err, obj: obj});
      }
    })
  })
}

// 订阅事件
nimSDK.subscribeEvent = function (param) {
  return new Promise((resolve, reject) => {
    nim.subscribeEvent({
      type: 1,
      accounts: param.accounts,
      sync: true,
      subscribeTime: 24 * 60 * 60,
      vaildTime: 24 * 60 * 60,
      done: function (err, obj) {
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 查询订阅
nimSDK.querySubscribeEventsByAccounts = function (param) {
  return new Promise((resolve, reject) => {
    nim.querySubscribeEventsByAccounts({
      type: 1,
      accounts: param.accounts,
      done: function (err, obj) {
        resolve({err: err, obj: obj});
      }
    });
  });
}

// 删除聊天记录
nimSDK.clearServerHistoryMsgsWithSync = function (param) {
  return new Promise((resolve, reject) => {
    nim.clearServerHistoryMsgsWithSync({
      ...param,
      isSyncSelf: true,
      done: function (err, obj) {
        resolve({err: err, obj: obj});
      }
    });
  });
}