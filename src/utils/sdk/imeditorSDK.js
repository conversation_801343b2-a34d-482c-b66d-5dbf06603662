/**
 * 主页面聊天框sdk
 * */
let imEditor = "";

// 初始化编辑器
export function initEditor(param) {
  // 存在iframe则不需要重新初始化
  if (imEditor && document.getElementById(imEditor._id)) {
    return imEditor;
  }
  imEditor = new ImEditor(param.id, {
    bindings: {
      aenterA: {
        key: ImEditor.keys.ENTER,
        handler: param.send.bind(this, {key: ImEditor.keys.ENTER})
      },
      aenterB: {
        key: ImEditor.keys.ENTER,
        shortKey: true,// shiftKey-shift shortKey-ctrl
        handler: param.send.bind(this, {key: ImEditor.keys.ENTER, shortKey: true})
      },
      aEsc: {
        key: ImEditor.keys.ESCAPE,
        handler: param.handlerKey.bind(this, {key: "esc"})
      },
      aUp: {
        key: ImEditor.keys.UP,
        handler: param.handlerKey.bind(this, {key: "up"})
      },
      aDown: {
        key: ImEditor.keys.DOWN,
        handler: param.handlerKey.bind(this, {key: "down"})
      },
    },
    matchers: [
      // 添加元素到输入框过滤器
      [Node.ELEMENT_NODE, param.dropAndCopyFilter],
    ],
    // 处理图片
    dealImg: function (node) {
      return param.dealImg(node);
    },
    // 只能粘贴2M的图片
    isValidImg: function (blob) {
      return blob && blob.size <= 2 * 1024 * 1024;
    },
  });
  // 左键
  imEditor.on(ImEditor.events.click, function (e) {
    param.click(e);
  });
  // 鼠标释放
  imEditor.on(ImEditor.events.mouseup, function (e) {
    param.mouseup(e);
  });
  // 鼠标释放
  imEditor.on(ImEditor.events.mousemove, function (id, e) {
    param.mousemove(id, e);
  });
  // 右键
  imEditor.on(ImEditor.events.EDITOR_CONTEXT_MENU, function (id, contents, e) {
    param.menu(id, contents, e);
  });
  // 双击图片
  imEditor.on(ImEditor.events.IMG_DBLCLICK, function (id, contents) {
    param.dblImg(id, contents)
  });
  // 获取焦点
  imEditor.on(ImEditor.events.EDITOR_FOCUS, function (id) {
    param.focus(id);
  });
  // 失去焦点
  imEditor.on(ImEditor.events.EDITOR_BLUR, function (e) {
    param.blur(e);
  });
  // @触发
  imEditor.on(ImEditor.events.EDITOR_HAIT_KEYUP, function (id, range) {
    param.hait(id, range)
  });
  // #触发
  imEditor.on(ImEditor.events.EDITOR_CALL_KEYUP, function (id, range) {
    param.call(id, range)
  });
  // 文本变化
  imEditor.on(ImEditor.events.TEXT_CHANGE, function (id) {
    param.change(id);
  });
  // 拖拽文件
  imEditor.on(ImEditor.events.DROP_AND_COPY_FILE, function (id, files) {
    param.dropFile(id, files)
  });
  // 粘贴文件
  imEditor.on(ImEditor.events.PASTE_FILE, function (id, files) {
    param.pasteFile(id, files);
  });
  // 违规图片
  imEditor.on(ImEditor.events.DROP_AND_COPY_INVALID_IMAGE, function (id, files) {
    param.imageError(id, files);
  });
  // 输入框错误
  imEditor.on("error", function (e) {
    param.error(e)
  });
  // 按键监听
  imEditor.on(ImEditor.events.EDITOR_KEYDOWN, function (e) {
    param.keydown(e)
  });
  return imEditor;
}