const httpReq = remote.require("request");

export function request(param) {
  // 不验证证书
  param.rejectUnauthorized = false;
  return new Promise((resolve, reject) => {
    httpReq(param, (err, res, body) => {
      // 输出日志信息
      let consoleInfo = {key: "request", headerInfo: {methodCode: param?.headers?.methodCode, serviceCode: param?.headers?.serviceCode}, url: param?.url, proxy: param.proxy};
      if (err) {
        // 记录代理错误日志
        remote.store.getters.getLogger.writeLogFile(10, JSON.stringify({...consoleInfo, error: err}));
        reject({success: false, errorMsg: err, thisProxy: param.proxy});
        return;
      }
      // 记录请求日志
      res.timingPhases.ip = res.socket.remoteAddress;
      remote.store.getters.getLogger.writeLogFile(10, JSON.stringify({...consoleInfo, ...res.timingPhases}));
      res.data = body;
      res.request.responseURL = param.url;
      res.thisProxy = param.proxy;
      resolve(res);
    });
  });
}