import axios from "axios"
import qs from "qs"
import {emitMsg, deepClone, decrypt} from "@utils";
import {request} from "@utils/net/request";
import config from "/config.js";
// 被退出登录
let logout = false;
let env = config[config.env];

export default {
  // 返回通用状态
  checkStatus: function (response, data) {
    if (response?.data) {
      let thisHeaders = response?.config?.headers || response?.request?.headers;
      try {
        // 40002、applogin、im_message接口解密、通讯录、jjsImApi解密
        if (((thisHeaders.serviceCode == "40002" || thisHeaders.serviceCode == "applogin" || thisHeaders.serviceCode == "im_message") && thisHeaders.v >= 10) ||
          (thisHeaders.serviceCode == "40001" && thisHeaders.methodCode == "50025" && thisHeaders.v == 5) ||
          response.request.responseURL == config[config.env].jjsImApi) {
          response.data = decrypt(response.data, "ODcyYTUxNGM1N2M2", "", "aes-128-ecb");
          response.data = JSON.parse(response.data);
        } else if (thisHeaders.jjrplus) {
          response.data = decrypt(response.data, "kyapi_2020051620", "", "aes-128-ecb");
          response.data = JSON.parse(response.data);
        }
      } catch (e) {
        try {
          response.data = JSON.parse(response.data.replace(/(\d+):/g, "\"$1\":"));
        } catch (e) {}
      }
      // 在其他地方登录pc乐聊
      if (response.data && response.data.errorCode == "99999" &&
        (!remote.store.getters.getJJsProxy.loginSwitch || (remote.store.getters.getJJsProxy.loginSwitch && thisHeaders.serviceCode == "applogin" && thisHeaders.methodCode == "60001"))) {
        if (response.config &&
          (new RegExp(env.jjsHome + "\/kyapi\/").test(response.config.url) || /sensitive-words-api\/sensitiveWords\/contains/.test(response.config.url))) {
          // 客源接口、敏感词校验不确定是否有问题不退出
        } else {
          remote.store.commit("setLogout", {type: 1, content: response.data.errorMsg || "您的账号在另一台设备登录了乐聊,已被迫下线!"});
          logout = true;
          // 关闭所有窗口并清除缓存
          emitMsg("msg", {type: "clear", clear: 1});
        }
      }
      // 请求失败没有提示语，默认返回系统错误
      if (response.data?.success === false && !response.data?.errorMsg) {
        response.data.errorMsg = "系统错误";
      }
      // 账号特殊处理不显示红点
      if (remote.store.getters.getUserInfo?.workerNo == "346765") {
        if (/sensitive-words-api\/task\/getTaskStatusBy1/.test(response.config.url) && response.data?.data) {
          response.data.data = 0;
        }
      }
      return response.data;
    } else if (response.status == 200) {
      return {
        data: "",
        status: 200,
        errorMsg: ""
      }
    } else {
      console.log("checkStatusErr", response.err);
      return {
        data: data,
        status: -1,
        errorMsg: "系统响应超时"
      }
    }
  },
  // 默认请求头设置
  headers: function (data = {}, noUserInfo) {
    let baseComputerInfo = remote.store.getters.getBaseComputerInfo;
    let userInfo = remote.store.getters.getUserInfo;
    let param = {
      appName: "pc-im",
      imei: baseComputerInfo.hostName,
      version: config.version,
      ...data
    }
    if (!noUserInfo) {
      param.empNo = userInfo.workerNo || "";
      param.empNumber = userInfo.workerId || "";
      param.empName = encodeURI(encodeURI(userInfo.name)) || "";
      param.deptNumber = userInfo.deptNumber || "";
      param.deptName = encodeURI(encodeURI(userInfo.deptName)) || "";
      param.token = userInfo.token;
    }
    return param;
  },
  // get/post请求
  req: async function (data) {
    if (logout) {
      return this.checkStatus({});
    }
    try {
      let userInfo = remote.store.getters.getUserInfo;
      let axiosParam = {
        method: data.method || "post",
        headers: data.noHeader ? {} : this.headers(data.headers, data.noUserInfo),
        url: data.url,
        data: qs.stringify(data.data),
        timeout: data.headers && data.headers.timeout ? data.headers.timeout : 10000,
      }
      if (config.env == "dev" && /college_api/.test(axiosParam.url)) {
        // 乐学堂接口3.100环境需要url拼接empNumber
        axiosParam.url += "?empNumber=77785863";
      }
      if (data.json) {
        axiosParam.headers["Content-Type"] = "application/json;charset=utf-8";
      }
      if (axiosParam.headers["Content-Type"] == "application/json;charset=utf-8") {
        axiosParam.data = data.data;
      }
      if (data.data instanceof FormData) {
        axiosParam.data = data.data;
      }
      if (axiosParam.method.toLowerCase() == "get" && data.data) {
        axiosParam.params = data.data;
      }
      if (data.data?.setTimeout) {
        axiosParam.timeout = data.data.setTimeout;
      }
      // 兼容客源项目校验登录
      if (axiosParam.headers["OpId"]) {
        axiosParam.headers["OpId"] = axiosParam.headers["empNumber"];
        axiosParam.headers["App_name"] = "pc-im";
        axiosParam.headers["version"] = "1";
      }
      // 40002、applogin、im_message且版本号小于10的都改为10加密
      if (axiosParam.headers && (axiosParam.headers.serviceCode == "40002" || axiosParam.headers.serviceCode == "applogin" || axiosParam.headers.serviceCode == "im_message")
        && Number(axiosParam.headers.v || 0) < 10) {
        axiosParam.headers.v = 10;
      }
      let response = {};
      let proxyInfo = remote.store.getters.getJJsProxy;
      if (proxyInfo.proxyCurrent.url && new RegExp(proxyInfo.host.replace(/,/g, "|")).test(axiosParam.url)) {
        let requestParam = deepClone(axiosParam);
        requestParam.proxy = proxyInfo.proxyCurrent.url;
        requestParam.json = true;
        requestParam.time = true;
        requestParam.form = axiosParam.params || axiosParam.data;
        requestParam.qs = axiosParam.params || axiosParam.data;
        // 存在form会转换成application/x-www-form-urlencoded
        if (axiosParam.headers["Content-Type"]?.indexOf("application/json") > -1) {
          delete requestParam.form;
          requestParam.body = axiosParam.params || axiosParam.data;
        }
        try {
          // request包括服务器响应时间和超时时间2个时间
          requestParam.timeout /= 2;
          response = await request(requestParam);
        } catch (e) {
          // 代理超时/异常
          console.log("requestErr", e);
        }
      } else {
        let axiosCancel = new axios.CancelToken.source();
        axiosParam.cancelToken = axiosCancel.token;
        if (data?.data?.axiosCancel) {
          data.data.axiosCancel(axiosCancel);
        }
        response = await axios(axiosParam);
      }
      return this.checkStatus(response, data.data);
    } catch (err) {
      console.log(err);
      return this.checkStatus({err: err}, data.data);
    }
  },
}
