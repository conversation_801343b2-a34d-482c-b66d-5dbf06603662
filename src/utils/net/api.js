import http from './http';
import config from "/config.js"

let env = config[config.env];

// 查询人员
export const queryEmpApi = (data) => http.req({url: env.jjsImApi, data: data, noUserInfo: true});
// 登录
export const loginApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "login", serviceCode: "applogin", v: 3, empNo: data.empNo}, noUserInfo: true});
// pc登录设备认证验证
export const doCheckAuthApi = (data) => http.req({url: `${env.jjsHome}/jjslogin/doCheckAuth`, data: data, headers: {}, noUserInfo: true});
// 登录页账号密码登录新系统
export const doLoginNewSysApi = (data) => http.req({url: `${env.jjsHome}/jjslogin/doLoginByImV1`, data: data, headers: {}, noUserInfo: true});
// 获取服务器时间
export const getTimeApi = (data) => http.req({url: `https://data.leyoujia.com/time`, data: data, header: {}, noUserInfo: true});


// 批量查询人员信息
export const getUserBatchApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50011", serviceCode: "40002", v: 3}});

// 注册账号
export const regAccidApi = (workerNo) => http.req({url: env.jjsImHome + '/registerAccid?token=DFyMGktbgTGcpLMx&empNo=' + workerNo});
// 账号解禁
export const unblockAccidApi = (workerNo) => http.req({url: env.jjsImHome + '/unblockAccount?token=DFyMGktbgTGcpLMx&empNo=' + workerNo});

// 查询全部订阅号和服务号
export const searchSubAndSerApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50039", serviceCode: "40002", v: 3}});
// 查询所有配置信息
export const queryAllSettingApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50015", serviceCode: "40002", v: 11}});
// 查询所有分组
export const queryAllGroupSettingApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50018", serviceCode: "40002", v: 11}});
// 查询所有找对人办对事
export const getFindPersonsDataApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50005", serviceCode: "40002", v: 3}});

// 模糊查询用户
export const searchUsersApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50014", serviceCode: "40002", v: 11}});
// 模糊查询用户-普通讨论组
export const findIMEmpByAuthApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "findIMEmpByAuth", serviceCode: "40002", v: 12}});
// 批量更新im人员信息
export const updateUsersApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "updateUsers", serviceCode: "40002", v: 1}});
// 查询勋章列表
export const queryHonourDataApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_honour_data", serviceCode: "40002", v: 1}});
// 获取合作对象信息
export const queryCooperateApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_cooperate_by_emp_number", serviceCode: "40002", v: 1}});
// 查询勋章、标签列表
export const queryHonourDataListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_honour_data_list", serviceCode: "40002", v: 10}});
// 查询人事档案权限
export const queryHrAuthorityApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "40005", serviceCode: "40013", v: 1}});
// 语音转文字
export const speechToTextApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "speechToText", serviceCode: "40002", v: 1}});

// 获得人员状态
export const getEmpStatusApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "60001", serviceCode: "applogin", v: 3}});
// 获取登录新系统秘钥
export const getLoginSecretApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50012", serviceCode: "40002", v: 11, timeout: 3000}});
// 设置配置信息-用于置顶/群通知配置等
export const modifySettingsApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50016", serviceCode: "40002", v: 3}});
// 删除服务器消息
export const deleteMessageApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50017", serviceCode: "40002", v: 4}});
// 退出群（群）
export const leaveTeamApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "leave", serviceCode: "40002", v: 12}});
// 解散讨论组(群)
export const dismissGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50002", serviceCode: "40002", v: 12}});
// 创建讨论组(群)
export const createGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50001", serviceCode: "40002", v: 11}});
// 邀请入群/踢人出群(群)
export const comeInAndGoOutGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50023", serviceCode: "40002", v: 11}});
// 移交创建人（讨论组）
export const handOverCreatorApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50050", serviceCode: "40002", v: 1}});
// 退群确认消息
export const quitTeamMsgsApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50026", serviceCode: "40002", v: 3}});
// 删除所有退群确认消息
export const clearTransactionMsgApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50034", serviceCode: "40002", v: 3}});
// 处理退群消息
export const disposeTransactionMsg = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50025", serviceCode: "40002", v: 3}});
// 批量处理退群消息
export const disposeTransactionMsgs = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50036", serviceCode: "40002", v: 3}});
// 可创建的讨论组类型
export const searchGroupTypeListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "searchGroupTypeList", serviceCode: "40002", v: 11}});
// 根据讨论组类型获取系统拉人人员列表
export const getSystemPerpleEmpApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getSystemPerpleEmp", serviceCode: "40002", v: 11}});

// 查询特别关心
export const getAllSpecialDataApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getAllSpecialData", serviceCode: "40002", v: 1}});
// 添加特别关心
export const addSpecialConcernApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "addSpecialConcern", serviceCode: "40002", v: 1}});
// 取消特别关心
export const deleteSpecialConcernApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "deleteSpecialConcern", serviceCode: "40002", v: 1}});
// 添加云信好友
export const addNimFriendApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "addNimFriend", serviceCode: "40002", v: 1}});
// 删除云信好友
export const delNimFriendApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "delNimFriend", serviceCode: "40002", v: 1}});
// 常用表情
export const queryOftenEmojiApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_often_emoji", serviceCode: "40002", v: 1}});
// 小乐智能问答
export const answerXiaoLeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "answer", serviceCode: "40002", v: 11}});

// 查询im服务端同步云记录
export const queryCloudMessageApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50001M", serviceCode: "40002", timeout: 20000, v: 2}});
// 查询消息记录
export const queryCloudAllMessageApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50003M", serviceCode: "40002", timeout: 20000, v: 1}});
// 根据code提取消息记录
export const queryMessageByCodeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "queryMessageByCode", serviceCode: "40002", timeout: 20000, v: 1}});
// 生成消息记录code
export const messageToCodeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "messageToCode", serviceCode: "40002", timeout: 20000, v: 1}});
// 生成合并转发内容提取code
export const mergeMessageToCodeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "mergeMessageToCode", serviceCode: "40002", timeout: 20000, v: 1}});

// 查询文件夹分组
export const getResourceGroupDataApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getResourceGroupData", serviceCode: "40002", v: 1}});
// 新增文件夹分组
export const addResourceGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "addResourceGroup", serviceCode: "40002", v: 1}});
// 重命名分组
export const updateResourceGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "updateResourceGroup", serviceCode: "40002", v: 1}});
// 删除文件夹分组
export const deleteResourceGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "deleteResourceGroup", serviceCode: "40002", v: 1}});
// 移动文件夹分组
export const moveResourceGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "moveResourceGroup", serviceCode: "40002", v: 2}});


// 日程列表 http://172.16.16.2:3000/project/273/interface/api
export const smartQueryListApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule-emp/list`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 更改个人日程 http://172.16.16.2:3000/project/273/interface/api
export const smartUpdateApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule/update`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 添加日程 http://172.16.16.2:3000/project/273/interface/api
export const smartSaveApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule/save`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 日程详情 http://172.16.16.2:3000/project/273/interface/api
export const smartDetailApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule/detail`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 获取ycya任务 http://172.16.16.2:3000/project/273/interface/api
export const smartTaskListApi = (data) => http.req({url: `${env.jjsHome}/schedule/mainremote/task/list`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 删除ycya任务 http://172.16.16.2:3000/project/273/interface/api
export const smartTaskDelApi = (data) => http.req({url: `${env.jjsHome}/schedule/mainremote/task/del`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 获取待办列表 http://172.16.16.2:3000/project/273/interface/api
export const smartZnzlListApi = (data) => http.req({url: `${env.jjsHome}/schedule/mainremote/znzl/selectAiRemindList`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 添加关注 http://172.16.16.2:3000/project/273/interface/api
export const smartAttentionAddApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule-follow/save`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 取消关注 http://172.16.16.2:3000/project/273/interface/api
export const smartAttentionDelApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule-follow/delete`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 关注列表 http://172.16.16.2:3000/project/273/interface/api
export const smartAttentionListApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule-follow/list`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 转发与邀约 http://172.16.16.2:3000/project/273/interface/api
export const smartTransmitApi = (data) => http.req({url: `${env.jjsHome}/schedule/mainremote/im/transmit`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 添加提醒 http://172.16.16.2:3000/project/273/interface/api
export const smartRemindApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule-remind/save`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 日历列表 http://172.16.16.2:3000/project/273/interface/api
export const smartCalendarApi = (data) => http.req({url: `${env.jjsHome}/schedule/mainremote/erp/findSystemCalendar`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 日程删除
export const smartDeleteApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule/delete`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 日程转让
export const smartMakeOverApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule/makeOver`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 日程冲突人员
export const smartConflictEmpApi = (data) => http.req({url: `${env.jjsHome}/schedule/nim-schedule-emp/getConflictEmp`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 会议室列表
export const smartConferenceRoomListApi = (data) => http.req({url: `${env.jjsHome}/schedule/mainremote/erp/getConferenceRoomList`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 转备忘
export const smartAddMemoApi = (data) => http.req({url: `${env.jjsHome}/attm/pc-memo/addMemo`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});

// 搜素@文档 http://172.16.16.2:3000/project/279/interface/api/24407
export const searchDocApi = (data) => http.req({url: `${env.jjsHome}/document/dc-document/search`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 客户搜索@文档 http://172.16.16.2:3000/project/279/interface/api/2219941
export const searchDocCustomerApi = (data) => http.req({url: `${env.jjsHome}/document/dc-document/searchFormClient`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 批量查询文档信息 http://172.16.16.2:3000/project/279/interface/api/29099
export const detailsDocApi = (data) => http.req({url: `${env.jjsHome}/document/dc-document/details`, data: data, headers: {methodCode: "", serviceCode: "", v: 1}});
// 删除文档权限 http://172.16.16.2:3000/project/279/interface/api/28667
export const delPurDocApi = (data) => http.req({url: `${env.jjsHome}/document/dc-purview-detail/delPur`, data: data, headers: {methodCode: "", serviceCode: "", v: 1, "Content-Type": "application/json;charset=utf-8"}});
// 批量设置文档权限 http://172.16.16.2:3000/project/279/interface/api/29103
export const addPurDocListApi = (data) => http.req({url: `${env.jjsHome}/document/dc-purview-detail/addPurList`, data: data, headers: {methodCode: "", serviceCode: "", v: 1, "Content-Type": "application/json;charset=utf-8"}});

// 查询通讯录
export const getUsersApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50025", serviceCode: "40001", v: 5}});

// 创建标签-通讯录
export const addTagApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "modifyGroupSettinList", serviceCode: "40002", v: 1}});
// 创建标签的权限，判断是否能添加50人
export const addTagPowerApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50046", serviceCode: "40002", v: 1}});
// 移动内容|重命名-群分组
export const moveTagApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "modifyGroupSettingByEmpNo", serviceCode: "40002", v: 3}});
// 搜索讨论组列表
export const searchGroupFrontListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "searchGroupFrontList", serviceCode: "40002", v: 11}});
// 查询讨论组列表条件
export const initSeachDateApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "initSeachDate", serviceCode: "40002", v: 11}});
// 查询讨论组可切换状态
export const getEditMapForFrontApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getEditMapForFront", serviceCode: "40002", v: 11}});
// 批量修改讨论组状态
export const batchUpdateStatusForFrontApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "batchUpdateStatusForFront", serviceCode: "40002", v: 11}});
// 分组-新增/重命名
export const classifyUpdateApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "addGroupSetting", serviceCode: "40002", v: 11}});
// 分组-移动至
export const classifyMoveApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "moveSettingGroup", serviceCode: "40002", v: 11}});
// 分组-添加至
export const classifyMoveInApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "addSettingForGroup", serviceCode: "40002", v: 11}});
// 分组-移出至
export const classifyMoveOutApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "moveOutFromGroup", serviceCode: "40002", v: 10}});
// 分组-排序
export const groupSettingSortApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "groupSettingSort", serviceCode: "40002", v: 11}});
// 分组-删除
export const deleteGroupSettingApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "deleteGroupSetting", serviceCode: "40002", v: 10}});

// 查询群通知
export const chatNoticeListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50003", serviceCode: "40002", v: 3}});
// 新增/编辑/删除群通知
export const teamAnnouncementApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50004", serviceCode: "40002", v: 11}});
// 获取七牛token
export const get7NTokenApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50006", serviceCode: "40002", v: 3}});

//更多模块
//考勤登记
export const checkWorkApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50052", serviceCode: "applogin", v: 3}});
//操作记录
export const sysLogApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50001", serviceCode: "40013", v: 3}});
// 查询老系统mac
export const queryMacApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50003", serviceCode: "40013", v: 3}});
//考勤打卡
export const checkWorkSetApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50002", serviceCode: "40013", v: 3}});

//更新头像
export const refreshEmpHeadApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "refreshEmpHead", serviceCode: "40002", v: 1}});

// 收藏-分组列表
export const queryCollectClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50030", serviceCode: "40002", v: 3}});
// 收藏-查询分组内容
export const queryCollectContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50031", serviceCode: "40002", v: 3}});
// 收藏-新增分组
export const addCollectClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50027", serviceCode: "40002", v: 3}});
// 收藏-重命名分组
export const editCollectClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50032", serviceCode: "40002", v: 3}});
// 收藏-删除分组
export const delCollectClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50028", serviceCode: "40002", v: 3}});
// 收藏-新增/更新分组收藏内容
export const editCollectContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50029", serviceCode: "40002", v: 3}});
// 收藏-批量移动收藏内容到分组
export const moveCollectContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "move_collects_group", serviceCode: "40002", v: 1}});
// 收藏-批量删除收藏内容
export const removeCollectContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50049", serviceCode: "40002", v: 3}});
// 收藏-分组批量排序
export const sortCollectClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "sort_collect_group_all", serviceCode: "40002", v: 1}});
// 收藏/话术-批量互相移动
export const toggleCollectContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "batch_move", serviceCode: "40002", v: 1}});

// 话术-查询分类
export const queryCollectStoryClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_quick_reply_group", serviceCode: "40002", v: 1}});
// 话术-查询分组内容
export const queryCollectStoryContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_speech_list", serviceCode: "40002", v: 1}});
// 话术-新增/编辑分组
export const editCollectStoryClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "save_edit_worker_speech_group", serviceCode: "40002", v: 1}});
// 话术-删除分组
export const delCollectStoryClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "delete_worker_speech_group", serviceCode: "40002", v: 1}});
// 话术-分组排序
export const sortCollectStoryClassifyApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "worker_speech_group_sort_list", serviceCode: "40002", v: 1}});
// 话术-新增/编辑话术
export const editCollectStoryContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "save_worker_speech", serviceCode: "40002", v: 1}});
// 话术-批量删除话术
export const removeCollectStoryContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "delete_worker_speech", serviceCode: "40002", v: 1}});
// 话术-批量移动话术到分组
export const moveCollectStoryContentApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "move_worker_speech_group", serviceCode: "40002", v: 1}});

// 敏感词库
export const getKeyTextApi = () => http.req({url: `https://fang-community.leyoujia.com/keyword/keywordsfilter.txt?v=${new Date().getTime()}`, method: "GET", noHeader: true});
// 图片失败
export const sensitiveWordsApi = (data) => http.req({url: `${env.jjsHome}/sensitive-words-api/sensitiveWords/contains`, data: data, timeout: 5000, headers: {"OpId": true, "Content-Type": "application/json;charset=utf-8"}});
// 获取涉及敏感的msgIdService
export const querySensitiveApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "querySensitive", serviceCode: "40002", v: 1}});
// 讨论组群名敏感词库
export const getImSensitivesApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getImSensitives", serviceCode: "40002", v: 1}});

// 获取用户ip和地址详情1
export const getIpApi1 = () => http.req({url: "https://my.ip.cn/json/", method: "GET", noHeader: true});
// 获取用户ip和地址详情2
export const getIpApi2 = () => http.req({url: "https://myip.ipip.net", method: "GET", noHeader: true});
// 获取用户ip和地址详情3
export const getIpApi3 = () => http.req({url: "https://qifu-api.baidubce.com/ip/local/geo/v1/district", method: "GET", noHeader: true});
// 获取用户ip和地址详情4
export const getIpApi4 = () => http.req({url: "http://***********/api/zxp/getCurrentIp", method: "GET", noHeader: true});

// 获取代理信息
export const getProxyApi = () => http.req({url: `https://appdownload.leyoujia.com/leyoujia-proxy${config.env == "online" ? "" : "-uat"}.json?v=${new Date().getTime()}`, method: "GET", noHeader: true});
// 获取上传日志token
export const getLoggerTokenApi = () => http.req({url: `${env.jjsHome}/jjslogin/qiniu/getQiNiuToken`, data: {projName: "pc-im/log/", type: 2}, method: "GET", noHeader: true});
// 上传日志地址到服务器
export const uploadFilePathApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "monitorLog", serviceCode: "40002", v: 5}});
// 获取微信预警token
export const getWxMsgTokenApi = () => http.req({url: `https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=wx6810a1001ac73cec&corpsecret=OcPsQwhN8kt9ko3CA9tJeK6ErW3rWuo4vG857hg44qYgiRBp5_1JCjvl-N_lzOQT`, method: "GET", noHeader: true});
// 发送微信预警信息
export const sendWxMsgApi = (data) => http.req({url: `https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token=${data.token}`, data: data, method: "POST", noHeader: true, json: true});
// 上传网络检测结果
export const networkCheckApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "network_check", serviceCode: "40002", v: 5}});


// 获取网络连接情况
export const checkPcApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50050", serviceCode: "40005", v: 1}});
// 获取网络连接电脑类型
export const getPcTypeListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50035", serviceCode: "40005", v: 1}});
// 获取网络连接电脑部门
export const getDeptApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50036", serviceCode: "40005", v: 1}});
// 申请提交网络连接
export const applyNetApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50051", serviceCode: "40005", v: 1}});
// 获取网络连接电脑名
export const getPcNameApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50038", serviceCode: "40005", v: 1}});
// 获取网络连接申请人
export const getPcPersonApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50039", serviceCode: "40005", v: 1}});
// 获取电脑注册信息 allMac-本机的所有mac地址 applyEmpNumber-申请人编号8位数
export const getNetInfoApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50043", serviceCode: "40005", v: 1}});
// 根据注册类型获取本机注册名称 attachNumber-归属/挂靠部门编号 pcUserNumber-电脑使用者编号
export const getNetNameApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50045", serviceCode: "40005", v: 1}});
// 新申请提交网络连接
export const applyNetNewApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50046", serviceCode: "40005", v: 1}});

// 新版本网络连接-初始化检查
export const getNewNetCheckApi = (data) => http.req({url: `${env.jjsHome}/jg/pcCheck/initCheck`, data: data, headers: {}});
// 新版本网络连接-获取电脑注册名和其他相关信息
export const getNewNetNameApi = (data) => http.req({url: `${env.jjsHome}/jg/pcCheck/getPcNameInfos`, data: data, headers: {}});
// 新版本网络连接-提交网络申请
export const applyNewNetApi = (data) => http.req({url: `${env.jjsHome}/jg/pcCheck/commitPcCheck`, data: data, headers: {"Content-Type": "application/json;charset=utf-8"}});
// 新版网络连接-发货验证码上传电脑信息
export const uploadPcInfoApi = (data) => http.req({url: `${env.jjsHome}/jg/pcCheck/uploadPcInfo`, data: data, headers: {}});
// 新版网络连接-发送通知
export const sendNoticeApi = (data) => http.req({url: `${env.jjsHome}/jg/pcCheck/sendNotice`, data: data, headers: {}});
// 获取是否显示临时电脑授权入口
export const tempConIsShowApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "tempConIsShow", serviceCode: "applogin", v: 1}});
// 私人电脑授权临时连接
export const generateCodeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "generateCode", serviceCode: "applogin", v: 1}});
// 获取是否有vpn权限和内容
export const getProxyToolApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getProxyTool", serviceCode: "40002", v: 1}});
// 上传公钥
export const uploadPcKeyApi = (data) => http.req({url: `${env.jjsHome}/jg/pcCheck/uploadPcKey`, data: data, headers: {}});

// 房产网客户备注
export const fcwRemarkApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50048", serviceCode: "40002", v: 4}});
// 房产网客户举报
export const fcwReportApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "50020", serviceCode: "40002", v: 2}});
// 放弃跟进（客源）
export const leaveLatentCustomerApi = (data) => http.req({url: `${env.jjsHome}/kyapi/latent/leaveLatentCustomer`, data: data, headers: {"OpId": true}});
// 查询客户跳转信息
export const searchCustomerLinkApi = (data) => http.req({url: `${env.jjsHome}/kyapi/httpService/searchCustomerLink`, data: data, headers: {"OpId": true}});
// 房产网客户查询快捷回复
export const fcwQueryQuickReplyListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "queryQuickReplyList", serviceCode: "40002", v: 1}});
// 查询客户专属经纪人绑定
export const fcwBindApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "selectBind", serviceCode: "40002", v: 1}});
// 查询客户消息敏感词
export const fcwRulesApi = (data) => http.req({url: `${env.jjsHome}/kyapi/realTimeHit/getRules`, data: data, headers: {"OpId": true, jjrplus: true}});
// 查询房产网会话
export const updateActiveAccidsApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "updateActiveAccids", serviceCode: "40002", v: 10}});


// 稍后提醒
export const msgTipApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "msgTip", serviceCode: "40002", v: 1}});

// 获取验证码
export const sendEditPassCodeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "sendEditPassCode", serviceCode: "applogin", v: 1}});
// 忘记密码
export const editPasswordApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "editPassword", serviceCode: "applogin", v: 1}});
// 协议版本号
export const getPolicyVersionApi = () => http.req({url: `https://front.leyoujia.com/leyoujiaIm/policyVersion.json?v=${new Date().getTime()}`, method: "GET", noHeader: true});
// 获取最新版本号
export const getVersionApi = (data) => http.req({url: `${env.jjsHome}/im/version/getVersion`, data: data, method: "GET", noHeader: true});
// 发送更新失败消息
export const sendFailUpdateApi = (data) => http.req({url: `${env.jjsHome}/im/version/sendFailUpdate`, data: data, method: "GET", noHeader: true});
// 获取账号类型
export const checkAccountApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "checkAccount", serviceCode: "applogin", v: 1}});

// 查询应用
export const searchMenuByKeywordApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "searchMenuByKeyword", serviceCode: "40002", v: 1}});
// 查询应用（新）
export const getMenuListApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getMenuList", serviceCode: "40002", v: 1}});


// 转发集中销售消息
export const getPushForwardersApi = (data) => http.req({url: `${env.jjsHome}/ylyk-api/conSale/getPushForwarders`, data: data, headers: {"OpId": true}});

// 工作台-权限
export const getEditCommonPurviewApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getEditCommonPurview", serviceCode: "40002", v: 10}});
// 工作台-全部应用
export const queryWorkBenchApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "queryWorkBench", serviceCode: "40002", v: 10}});
// 工作台-常用应用
export const queryWorkBenchUsedApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "queryWorkBenchUsed", serviceCode: "40002", v: 10}});
// 工作台-应用分组
export const queryWorkBenchGroupApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "queryWorkBenchGroup", serviceCode: "40002", v: 10}});
// 工作台-添加编辑应用
export const editWorkBenchApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "editWorkBench", serviceCode: "40002", v: 10}});
// 工作台-添加编辑常用应用
export const editWorkBenchUsedApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "editWorkBenchUsed", serviceCode: "40002", v: 10}});
// 工作台-移除应用
export const deleteWorkBenchApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "deleteWorkBench", serviceCode: "40002", v: 10}});
// 工作台-移除常用应用
export const deleteWorkBenchUsedApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "deleteWorkBenchUsed", serviceCode: "40002", v: 10}});
// 工作台-应用排序
export const sortNimWorkBenchApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "sortNimWorkBench", serviceCode: "40002", v: 10}});
// 工作台-常用应用排序
export const sortWorkBenchUsedApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "sortWorkBenchUsed", serviceCode: "40002", v: 10}});
// 工作台-获取全部菜单（新）
export const getLoginMenuByEmpApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getLoginMenuByEmp", serviceCode: "40002", v: 10}});
// 工作台-应用排序（新）
export const resetIndexAppApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "resetIndexApp", serviceCode: "40002", v: 10}});

// 点击按钮请求
export const postApi = (data) => http.req({url: data.url, data: {}, headers: data.headers || {}, method: data.method ? data.method : "POST"});

// 直接下属
export const findSubordinateApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "findSubordinate", serviceCode: "40002", v: 1}});
// 转发转私聊权限
export const queryPurviewApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "query_purview", serviceCode: "40002", v: 1}});
// 获取云信文件转移时间
export const obsStoreEndTimeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "obsStoreEndTime", serviceCode: "40002", v: 10}});

// 合并转发
export const findMsgByIdApi = (data) => http.req({url: env.jjsImHome + "/message/findMsgById", data: data});

// 用户日志请求 0闪退 1登录 2新系统获取设备 3云信加载 4控制台 5错误日志
export const zxpApi = (data) => http.req({url: "http://***********/index.php?s=api/zxp/addCount", data: data, headers: {}});
// 获取uat密码
export const getUATPasswordApi = (data) => http.req({url: "http://172.16.3.233:12001/apis/back/oldSystem/PassGet", data: data, headers: {}});

// 查询房源卡片信息
export const queryEsfCardFieldsApi = (data) => http.req({url: env.jjsHome + "/jjrplus/detail/queryEsfCardFields", data: data, method: "GET", headers: {jjrplus: true}});
// 查询面试工单卡片信息
export const imMonadInfoByIdsApi = (data) => http.req({url: env.jjsHome + "/hr_new/lbg-api/xzMonad/ImMonadInfoByIds", method: "GET", data: data});

// 智能助理-获取个人助理信息
export const getAccountInfoApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getAccountInfo", data: data});
// 智能助理-获取数字人信息
export const getDigitalInfoApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getDigitalInfo", data: data});
// 智能助理-应用列表（含搜索分页
export const getAppListApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getAppList", data: data});
// 智能助理-回答反馈
export const feedbackApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/feedback", data: data});
// 智能助理-新话题
export const newTopicsApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/newTopics", data: data});
// 智能助理-停止回答
export const stopAnswerApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/stopAnswer/" + data.sid});
// 智能助理-停止流
export const closeAnswerApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/api/sseProxy/close/" + data.sid, method: "GET"});
// 智能助理-发起提问
export const initiateAskApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/initiateAsk", data: data, headers: {timeout: 30000}});
// 智能助理-判断应用是否有使用权限
export const getAppPurApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getAppInfoByIdAndPower", method: "GET", data: data});
// 智能助理-获取应用信息
export const getAppCardByAccIdsApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getAppCardByAccIds", data: data, json: true});
// 智能助理-获取热门ai列表
export const getHotAppListApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getHotAppList?releaseForm=2", data: data, json: true});
// 智能助理-ai陪练
export const aiSparringApi = (data) => http.req({url: env.jjsHome + "/college_api/ai/aiSparring", data: data});

// 获取权限
export const getButtonObjApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getButtonObj", serviceCode: "40002", v: 10}});
// 获取转发至特定人群
export const getForwardGroupTypeApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getForwardGroupType", serviceCode: "40002", v: 10}});
// 发送消息播报
export const sendBroadcastingApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "sendBroadcasting", serviceCode: "40002", v: 10}});
// 远程cmd指令
export const checkRemoteApi = () => http.req({url: `${env.jjsHome}/attend/js/checkRemote.json?v=${new Date().getTime()}`, method: "GET", noHeader: true});
// 清除聊天记录
export const clearMessageApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "clearMessage", serviceCode: "40002", timeout: 20000, v: 10}});
// 浏览器打开会话权限
export const havePermissionApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "havePermission", serviceCode: "40002", timeout: 5000, v: 10}});
// 获取阿波罗配置-是否显示同事分组
export const getNhrApolloApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getNhrApollo", serviceCode: "40002", timeout: 10000, v: 10}});

// 群ai-获取群对应关联的ai信息
export const getTeamAiInfoApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "getTeamAiInfo", serviceCode: "40002", v: 10}});
// 群ai-召唤群对应关联的ai信息
export const createTeamAiInfoApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "createTeamAiInfo", serviceCode: "40002", v: 10}});
// 群ai-编辑群ai简介
export const updateTeamAiInfoApi = (data) => http.req({url: env.aicpUrl, data: data, headers: {methodCode: "updateTeamAiInfo", serviceCode: "40002", v: 10}});
// 群ai-查询ai信息
export const getAppAndAccGroupInfoApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/aiApp/ask/getAppAndAccGroupInfo", method: "GET", data: data});
// 群ai-获取数据库知识库列表
export const searchDatasetDocumentListApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/dify/searchDatasetDocumentList", method: "POST", data: data, headers: {timeout: 30000}});
// 群ai-上传知识库
export const updateDocumentApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/dify/updateDocument", method: "POST", data: data, headers: {timeout: 30000}});
// 群ai-移除指定知识
export const removeDatasetDocumentByIdApi = (data) => http.req({url: env.jjsHome + "/ai-platform-api/dify/removeDatasetDocumentById", method: "POST", data: data});

// 快速行动-按业务端查询模板
export const searchTemplateByTypeApi = (data) => http.req({url: env.jjsHome + "/sensitive-words-api/taskTemplate/searchTemplateByType",headers: {"Content-Type": "application/json;charset=utf-8"}, method: "GET", data: data});
// 快速行动-新增任务
export const addTaskBatchApi = (data) => http.req({url: env.jjsHome + "/sensitive-words-api/task/addTaskBatch",headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST", data: data});
// 快速行动-获取日程中的任务列表
export const taskListApi = (data) => http.req({url: env.jjsHome + "/sensitive-words-api/task/taskList",headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST", data: data});
// 快速行动-获取群是否开启
export const quickActingInfo = (data) => http.req({url: env.jjsHome + "/taskpf/quick-acting-info/info",headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "GET", data: data});
// 快速行动-切换开启状态
export const quickActingUpStatus = (data) => http.req({url: env.jjsHome + "/taskpf/quick-acting-info/upStatus",headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "GET", data: data});
// 快速行动-查询进行填报
export const quickActingSearchFilling = (data) => http.req({url: env.jjsHome + `/sensitive-words-api/taskBatch/searchFilling?batchNo=${data.batchNo}&isLook=${data.isLook||1}`,headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST"});
// 快速行动-删除行动
export const quickActingDeleteBatchByNo = (data) => http.req({url: env.jjsHome + `/sensitive-words-api/taskBatch/deleteBatchByNo?batchNo=${data.batchNo}`,headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST"});
// 快速行动-获取日程中的任务列表(执行人+创建人=本人)
export const quickActingSearchPublicTaskListApi = (data) => http.req({url: env.jjsHome + "/sensitive-words-api/task/searchPublicTaskList",headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST", data: data});
// 快速行动-获取日程中的未完成点点
export const quickActingGetTaskStatusBy1 = (data) => http.req({url: env.jjsHome + "/sensitive-words-api/task/getTaskStatusBy1",headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST", data: data});
// 快速行动-查询卡片
export const quickActingGetBatchCard = (data) => http.req({url: env.jjsHome + `/sensitive-words-api/taskBatch/getBatchCard?batchNo=${data.batchNo}`,headers: {"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"}, method: "POST"});
