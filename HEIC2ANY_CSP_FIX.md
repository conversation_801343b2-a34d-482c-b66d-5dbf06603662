# HEIC2ANY CSP 修复方案

## 问题描述

在 Mac ARM 系统上运行 Electron 应用时，`heic2any` 模块因为 Content Security Policy (CSP) 限制而无法创建 Web Worker，导致以下错误：

```
Refused to create a worker from 'blob:http://localhost:8888/...' because it violates the following Content Security Policy directive: "script-src 'self' 'unsafe-inline' 'unsafe-eval'". Note that 'worker-src' was not explicitly set, so 'script-src' is used as a fallback.
```

## 修复方案

### 1. 更新 HTML CSP 指令

**文件**: `public/index.html`

**修改前**:
```html
<meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' 'unsafe-eval';">
```

**修改后**:
```html
<meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' 'unsafe-eval'; worker-src 'self' blob: data:; child-src 'self' blob: data:;">
```

**说明**: 添加了 `worker-src` 和 `child-src` 指令，允许从 blob 和 data URL 创建 Worker。

### 2. 增强 Electron 主进程的 CSP 处理

**文件**: `main.js`

**主要改进**:
- 在响应头中添加更宽松的 CSP 策略
- 增强 Worker 模拟机制，特别针对 heic2any 模块的需求
- 添加更完整的 Worker API 模拟

**关键变化**:
```javascript
// 为 heic2any 模块添加特殊的 CSP 头
responseHeaders['Content-Security-Policy'] = ['script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' blob: data:; worker-src \'self\' blob: data:; child-src \'self\' blob: data:;'];
```

### 3. 改进 heicToJpg 函数的错误处理

**文件**: `src/utils/index.js`

**主要改进**:
- 添加了针对 heic2any 失败的专门错误处理
- 实现了回退机制，当 heic2any 失败时使用原始数据
- 增加了详细的错误日志记录

**关键变化**:
```javascript
try {
  // 尝试使用 heic2any 进行转换
  let pngBlob = await heic2any({
    blob: dataUrlToBlob(base64),
    toType: 'image/jpeg',
    quality: 1
  });
  // ... 处理成功情况
} catch (heicError) {
  console.warn('heic2any 转换失败，可能是由于 CSP 限制:', heicError);
  // 实现回退方案
  // ...
}
```

## 测试方法

### 1. 使用提供的测试脚本

运行项目后，在开发者控制台中执行：

```javascript
// 加载测试脚本（如果尚未加载）
// 然后运行测试
testHeic2any.runAllTests();
```

### 2. 手动测试步骤

1. **启动应用**:
   ```bash
   npm run dev
   ```

2. **打开开发者控制台**，检查是否还有 CSP 相关的错误

3. **测试 HEIC 文件处理**:
   - 尝试上传或处理 HEIC 格式的图片
   - 观察控制台输出，确认没有 CSP 错误

4. **验证 Worker 创建**:
   ```javascript
   // 在控制台中测试
   try {
     const worker = new Worker('data:application/javascript,console.log("test");');
     console.log('Worker 创建成功');
     worker.terminate();
   } catch (e) {
     console.log('Worker 创建失败，但应该有模拟 Worker 可用:', e);
   }
   ```

## Mac ARM 系统特殊考虑

### 1. 性能优化

已在 `main.js` 中添加了 Apple Silicon 特定的优化：

```javascript
if (isAppleSilicon) {
  // 启用 GPU 加速
  app.commandLine.appendSwitch('enable-gpu-rasterization');
  app.commandLine.appendSwitch('enable-zero-copy');
  // 优化内存使用
  app.commandLine.appendSwitch('max_old_space_size', '4096');
}
```

### 2. 兼容性设置

确保以下设置适用于 Mac ARM：

```javascript
webPreferences: {
  nodeIntegration: true,
  nodeIntegrationInWorker: true,
  contextIsolation: false,
  webSecurity: false,
  experimentalFeatures: true,
  sandbox: false
}
```

## 预期结果

修复后应该看到：

1. **控制台中不再出现 CSP 相关的错误**
2. **heic2any 模块能够正常加载和初始化**
3. **HEIC 文件处理功能正常工作**
4. **应用在 Mac ARM 系统上稳定运行**

## 故障排除

如果问题仍然存在：

1. **检查 Electron 版本兼容性**
2. **确认所有文件修改都已保存**
3. **清除应用缓存并重新启动**
4. **检查是否有其他 CSP 策略覆盖了我们的设置**

## 注意事项

- 这些修改放宽了 CSP 限制，在生产环境中请根据安全需求进行调整
- 模拟 Worker 可能不能完全替代真实的 Worker 功能，但足以让 heic2any 模块正常工作
- 建议在不同的 Mac ARM 设备上进行测试以确保兼容性
