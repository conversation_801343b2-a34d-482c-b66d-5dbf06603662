const path = require("path")
const fs = require("fs")
const webpack = require("webpack");

let version = getArg("version");
let env = getArg("env");

// 获取参数
function getArg(key) {
  let argv = JSON.parse(process.env.npm_config_argv || "{}").remain || [];
  let index = argv.findIndex(item => new RegExp(key).test(item))
  if (index != -1) {
    return argv[index].split("=")[1]
  }
  return ""
}

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  outputDir: 'dist',
  assetsDir: '',
  productionSourceMap: false,
  devServer: {
    port: 8888,
    host: '0.0.0.0',
    hot: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization'
    },
    // 使用 before 钩子来禁用 CSP（兼容旧版本）
    before: function(app, server) {
      app.use((req, res, next) => {
        // 移除所有 CSP 相关的头
        res.removeHeader('Content-Security-Policy');
        res.removeHeader('Content-Security-Policy-Report-Only');
        res.removeHeader('X-Content-Security-Policy');
        res.removeHeader('X-WebKit-CSP');

        // 设置允许所有的 CSP
        res.setHeader('Content-Security-Policy', "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:; script-src * 'unsafe-inline' 'unsafe-eval' data: blob:; worker-src * 'unsafe-inline' 'unsafe-eval' data: blob:; child-src * 'unsafe-inline' 'unsafe-eval' data: blob:;");

        next();
      });
    }
  },
  css: {
    loaderOptions: {
      sass: {
        prependData: `@import "@static/css/public.scss";`
      }
    }
  },
  configureWebpack: {
    target: 'electron-renderer',
    node: {
      __dirname: false,
      __filename: false,
      global: true,
      process: true,
      Buffer: true
    },
    resolve: {
      fallback: {
        "events": require.resolve("events"),
        "path": require.resolve("path-browserify"),
        "os": require.resolve("os-browserify/browser"),
        "crypto": require.resolve("crypto-browserify"),
        "stream": require.resolve("stream-browserify"),
        "buffer": require.resolve("buffer"),
        "process": require.resolve("process/browser"),
        "util": require.resolve("util")
      }
    },
    plugins: [
      new webpack.ProvidePlugin({
        Buffer: ['buffer', 'Buffer'],
        process: 'process/browser',
      }),
      new webpack.DefinePlugin({
        __VUE_OPTIONS_API__: true,
        __VUE_PROD_DEVTOOLS__: false,
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
      })
    ],
    externals: {
      'electron': 'require("electron")'
    },
    optimization: {
      splitChunks: {
        chunks: "all",
        cacheGroups: {
          iconvLite: {
            test: /[\\/]node_modules[\\/](iconv-lite)[\\/]/,
            name: "chunk-iconv",
            priority: 2
          },
          dexie: {
            test: /[\\/]node_modules[\\/](dexie)[\\/]/,
            name: "chunk-dexie",
            priority: 2
          },
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            name: "chunk-vendors",
            priority: 1,
            reuseExistingChunk: true
          },
          common: {
            chunks: "all",
            name: "chunk-common",
            priority: 1,
            reuseExistingChunk: true
          }
        }
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
        '@store': path.resolve(__dirname, 'src/store'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@comp': path.resolve(__dirname, 'src/components'),
        '@view': path.resolve(__dirname, 'src/views'),
        '@static': path.resolve(__dirname, 'src/assets'),
        '@dire': path.resolve(__dirname, 'src/directives'),
        '@public': path.resolve(__dirname, 'public')
      }
    }
  },
  chainWebpack: config => {
    if (process.env.NODE_ENV === "production") {
      // 清除css，js版本号
      config.output.filename("[name].js").end();
      config.output.chunkFilename("[name].js").end();
      // 为生产环境修改配置...
      config.plugin("extract-css").tap(args => [{
        filename: `[name].css`,
        chunkFilename: `[name].css`
      }]);
    }
  },
  pages: {
    index: {
      template: "public/index.html",
      entry: "src/main.js",
      chunks: ["index", "chunk-index", "chunk-child", "chunk-vendors", "chunk-common", "chunk-iconv", "chunk-dexie"]
    },
    forward: {
      template: "public/forward.html",
      entry: "child/forward/main.js",
      chunks: ["forward", "chunk-common", "chunk-iconv"]
    },
    viewer: {
      template: "public/viewer.html",
      entry: "child/viewer/main.js",
      chunks: ["viewer", "chunk-common", "chunk-iconv"]
    },
    notify: {
      template: "public/notify.html",
      entry: "child/notify/main.js",
      chunks: ["notify", "chunk-common"]
    },
    shortcutConflict: {
      template: "public/shortcutConflict.html",
      entry: "child/shortcutConflict/main.js",
      chunks: ["shortcutConflict", "chunk-common"]
    },
    netDetect: {
      template: "public/netDetect.html",
      entry: "child/netDetect/main.js",
      chunks: ["netDetect", "chunk-common", "chunk-iconv"]
    }
  }
};
