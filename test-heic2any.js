/**
 * 测试 heic2any 模块在修复 CSP 问题后是否正常工作
 * 这个脚本可以在开发者控制台中运行来验证修复效果
 */

// 测试函数
async function testHeic2anyFunctionality() {
  console.log('开始测试 heic2any 功能...');
  
  try {
    // 检查 heic2any 模块是否可用
    let heic2any;
    if (typeof window !== 'undefined' && window.require) {
      heic2any = window.require("heic2any");
    } else if (window.remote) {
      heic2any = window.remote.require("heic2any");
    }
    
    if (!heic2any) {
      console.error('heic2any 模块不可用');
      return false;
    }
    
    console.log('heic2any 模块已加载');
    
    // 测试 Worker 创建
    try {
      const testWorker = new Worker('data:application/javascript,console.log("test worker");');
      console.log('Worker 创建成功');
      testWorker.terminate();
    } catch (workerError) {
      console.warn('Worker 创建失败，但这是预期的，将使用模拟 Worker:', workerError);
    }
    
    // 创建一个测试用的 HEIC 文件 blob（模拟数据）
    const testBlob = new Blob(['fake heic data'], { type: 'image/heic' });
    
    // 测试 heic2any 转换
    try {
      console.log('尝试使用 heic2any 进行转换...');
      const result = await heic2any({
        blob: testBlob,
        toType: 'image/jpeg',
        quality: 1
      });
      
      console.log('heic2any 转换成功:', result);
      return true;
    } catch (heicError) {
      console.warn('heic2any 转换失败（这可能是正常的，因为我们使用的是模拟数据）:', heicError);
      
      // 检查错误是否与 CSP 相关
      if (heicError.message && heicError.message.includes('Content Security Policy')) {
        console.error('CSP 问题仍然存在');
        return false;
      } else {
        console.log('错误不是 CSP 相关的，这表明 CSP 修复可能有效');
        return true;
      }
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
    return false;
  }
}

// 测试 heicToJpg 函数
async function testHeicToJpgFunction() {
  console.log('测试 heicToJpg 函数...');
  
  try {
    // 检查函数是否存在
    if (typeof window.heicToJpg === 'function') {
      console.log('heicToJpg 函数可用');
      
      // 创建测试数据
      const testParam = {
        base64: 'data:image/heic;base64,fake_heic_data',
        localSrc: '/tmp/test.heic',
        done: true
      };
      
      const result = await window.heicToJpg(testParam);
      console.log('heicToJpg 测试结果:', result);
      return true;
    } else {
      console.warn('heicToJpg 函数不可用');
      return false;
    }
  } catch (error) {
    console.error('heicToJpg 测试失败:', error);
    return false;
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('=== 开始 HEIC2ANY CSP 修复测试 ===');
  
  const test1 = await testHeic2anyFunctionality();
  const test2 = await testHeicToJpgFunction();
  
  console.log('=== 测试结果 ===');
  console.log('heic2any 模块测试:', test1 ? '通过' : '失败');
  console.log('heicToJpg 函数测试:', test2 ? '通过' : '失败');
  
  if (test1 && test2) {
    console.log('✅ 所有测试通过，CSP 修复可能有效');
  } else {
    console.log('❌ 部分测试失败，可能需要进一步调试');
  }
  
  return test1 && test2;
}

// 导出测试函数供外部使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testHeic2anyFunctionality,
    testHeicToJpgFunction,
    runAllTests
  };
}

// 如果在浏览器环境中，将函数添加到 window 对象
if (typeof window !== 'undefined') {
  window.testHeic2any = {
    testHeic2anyFunctionality,
    testHeicToJpgFunction,
    runAllTests
  };
}

console.log('HEIC2ANY 测试脚本已加载。运行 testHeic2any.runAllTests() 来执行测试。');
