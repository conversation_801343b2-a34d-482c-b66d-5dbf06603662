!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.ImEditor=t():e.ImEditor=t()}("undefined"!=typeof self?self:this,function(){return function(e){function t(o){if(r[o])return r[o].exports;var n=r[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,t),n.l=!0,n.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,o){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/sdk/imeditor/",t(t.s="./EditorManager/js/index.js")}({"../node_modules/babel-runtime/core-js/array/from.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/array/from.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/get-iterator.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/get-iterator.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/is-iterable.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/is-iterable.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/create.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/object/create.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/define-property.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/object/define-property.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/get-own-property-descriptor.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/object/get-own-property-descriptor.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/get-prototype-of.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/object/get-prototype-of.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/keys.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/object/keys.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/set-prototype-of.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/object/set-prototype-of.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/promise.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/promise.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/symbol.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/symbol/index.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/symbol/iterator.js":function(e,t,r){e.exports={default:r("../node_modules/core-js/library/fn/symbol/iterator.js"),__esModule:!0}},"../node_modules/babel-runtime/helpers/classCallCheck.js":function(e,t,r){"use strict";t.__esModule=!0,t.default=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},"../node_modules/babel-runtime/helpers/createClass.js":function(e,t,r){"use strict";t.__esModule=!0;var o=r("../node_modules/babel-runtime/core-js/object/define-property.js"),n=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),(0,n.default)(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}()},"../node_modules/babel-runtime/helpers/defineProperty.js":function(e,t,r){"use strict";t.__esModule=!0;var o=r("../node_modules/babel-runtime/core-js/object/define-property.js"),n=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e,t,r){return t in e?(0,n.default)(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}},"../node_modules/babel-runtime/helpers/get.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var n=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/core-js/object/get-own-property-descriptor.js"),l=o(s);t.default=function e(t,r,o){null===t&&(t=Function.prototype);var n=(0,l.default)(t,r);if(void 0===n){var s=(0,i.default)(t);return null===s?void 0:e(s,r,o)}if("value"in n)return n.value;var u=n.get;if(void 0!==u)return u.call(o)}},"../node_modules/babel-runtime/helpers/inherits.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var n=r("../node_modules/babel-runtime/core-js/object/set-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/core-js/object/create.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/typeof.js"),a=o(u);t.default=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+(void 0===t?"undefined":(0,a.default)(t)));e.prototype=(0,l.default)(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(i.default?(0,i.default)(e,t):e.__proto__=t)}},"../node_modules/babel-runtime/helpers/possibleConstructorReturn.js":function(e,t,r){"use strict";t.__esModule=!0;var o=r("../node_modules/babel-runtime/helpers/typeof.js"),n=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==(void 0===t?"undefined":(0,n.default)(t))&&"function"!=typeof t?e:t}},"../node_modules/babel-runtime/helpers/slicedToArray.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var n=r("../node_modules/babel-runtime/core-js/is-iterable.js"),i=o(n),s=r("../node_modules/babel-runtime/core-js/get-iterator.js"),l=o(s);t.default=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,u=(0,l.default)(e);!(o=(s=u.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&u.return&&u.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if((0,i.default)(Object(t)))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}()},"../node_modules/babel-runtime/helpers/toConsumableArray.js":function(e,t,r){"use strict";t.__esModule=!0;var o=r("../node_modules/babel-runtime/core-js/array/from.js"),n=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return(0,n.default)(e)}},"../node_modules/babel-runtime/helpers/typeof.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var n=r("../node_modules/babel-runtime/core-js/symbol/iterator.js"),i=o(n),s=r("../node_modules/babel-runtime/core-js/symbol.js"),l=o(s),u="function"==typeof l.default&&"symbol"==typeof i.default?function(e){return typeof e}:function(e){return e&&"function"==typeof l.default&&e.constructor===l.default&&e!==l.default.prototype?"symbol":typeof e};t.default="function"==typeof l.default&&"symbol"===u(i.default)?function(e){return void 0===e?"undefined":u(e)}:function(e){return e&&"function"==typeof l.default&&e.constructor===l.default&&e!==l.default.prototype?"symbol":void 0===e?"undefined":u(e)}},"../node_modules/base64-js/index.js":function(e,t,r){"use strict";function o(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function n(e){var t=o(e),r=t[0],n=t[1];return 3*(r+n)/4-n}function i(e,t,r){return 3*(t+r)/4-r}function s(e){var t,r,n=o(e),s=n[0],l=n[1],u=new f(i(e,s,l)),a=0,c=l>0?s-4:s;for(r=0;r<c;r+=4)t=d[e.charCodeAt(r)]<<18|d[e.charCodeAt(r+1)]<<12|d[e.charCodeAt(r+2)]<<6|d[e.charCodeAt(r+3)],u[a++]=t>>16&255,u[a++]=t>>8&255,u[a++]=255&t;return 2===l&&(t=d[e.charCodeAt(r)]<<2|d[e.charCodeAt(r+1)]>>4,u[a++]=255&t),1===l&&(t=d[e.charCodeAt(r)]<<10|d[e.charCodeAt(r+1)]<<4|d[e.charCodeAt(r+2)]>>2,u[a++]=t>>8&255,u[a++]=255&t),u}function l(e){return c[e>>18&63]+c[e>>12&63]+c[e>>6&63]+c[63&e]}function u(e,t,r){for(var o,n=[],i=t;i<r;i+=3)o=(e[i]<<16&16711680)+(e[i+1]<<8&65280)+(255&e[i+2]),n.push(l(o));return n.join("")}function a(e){for(var t,r=e.length,o=r%3,n=[],i=0,s=r-o;i<s;i+=16383)n.push(u(e,i,i+16383>s?s:i+16383));return 1===o?(t=e[r-1],n.push(c[t>>2]+c[t<<4&63]+"==")):2===o&&(t=(e[r-2]<<8)+e[r-1],n.push(c[t>>10]+c[t>>4&63]+c[t<<2&63]+"=")),n.join("")}t.byteLength=n,t.toByteArray=s,t.fromByteArray=a;for(var c=[],d=[],f="undefined"!=typeof Uint8Array?Uint8Array:Array,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=0,m=h.length;p<m;++p)c[p]=h[p],d[h.charCodeAt(p)]=p;d["-".charCodeAt(0)]=62,d["_".charCodeAt(0)]=63},"../node_modules/buffer/index.js":function(e,t,r){"use strict";(function(e){function o(){return i.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function n(e,t){if(o()<t)throw new RangeError("Invalid typed array length");return i.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=i.prototype):(null===e&&(e=new i(t)),e.length=t),e}function i(e,t,r){if(!(i.TYPED_ARRAY_SUPPORT||this instanceof i))return new i(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return a(this,e)}return s(this,e,t,r)}function s(e,t,r,o){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?f(e,t,r,o):"string"==typeof t?c(e,t,r):h(e,t)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function u(e,t,r,o){return l(t),t<=0?n(e,t):void 0!==r?"string"==typeof o?n(e,t).fill(r,o):n(e,t).fill(r):n(e,t)}function a(e,t){if(l(t),e=n(e,t<0?0:0|p(t)),!i.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function c(e,t,r){if("string"==typeof r&&""!==r||(r="utf8"),!i.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var o=0|y(t,r);e=n(e,o);var s=e.write(t,r);return s!==o&&(e=e.slice(0,s)),e}function d(e,t){var r=t.length<0?0:0|p(t.length);e=n(e,r);for(var o=0;o<r;o+=1)e[o]=255&t[o];return e}function f(e,t,r,o){if(r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(o||0))throw new RangeError("'length' is out of bounds");return t=void 0===r&&void 0===o?new Uint8Array(t):void 0===o?new Uint8Array(t,r):new Uint8Array(t,r,o),i.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=i.prototype):e=d(e,t),e}function h(e,t){if(i.isBuffer(t)){var r=0|p(t.length);return e=n(e,r),0===e.length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||X(t.length)?n(e,0):d(e,t);if("Buffer"===t.type&&Z(t.data))return d(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function p(e){if(e>=o())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o().toString(16)+" bytes");return 0|e}function m(e){return+e!=e&&(e=0),i.alloc(+e)}function y(e,t){if(i.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var o=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return Y(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return W(e).length;default:if(o)return Y(e).length;t=(""+t).toLowerCase(),o=!0}}function b(e,t,r){var o=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if(r>>>=0,t>>>=0,r<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return C(this,t,r);case"utf8":case"utf-8":return T(this,t,r);case"ascii":return S(this,t,r);case"latin1":case"binary":return P(this,t,r);case"base64":return N(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,t,r);default:if(o)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function _(e,t,r){var o=e[t];e[t]=e[r],e[r]=o}function v(e,t,r,o,n){if(0===e.length)return-1;if("string"==typeof r?(o=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=n?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(n)return-1;r=e.length-1}else if(r<0){if(!n)return-1;r=0}if("string"==typeof t&&(t=i.from(t,o)),i.isBuffer(t))return 0===t.length?-1:g(e,t,r,o,n);if("number"==typeof t)return t&=255,i.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,o,n);throw new TypeError("val must be string, number or Buffer")}function g(e,t,r,o,n){function i(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}var s=1,l=e.length,u=t.length;if(void 0!==o&&("ucs2"===(o=String(o).toLowerCase())||"ucs-2"===o||"utf16le"===o||"utf-16le"===o)){if(e.length<2||t.length<2)return-1;s=2,l/=2,u/=2,r/=2}var a;if(n){var c=-1;for(a=r;a<l;a++)if(i(e,a)===i(t,-1===c?0:a-c)){if(-1===c&&(c=a),a-c+1===u)return c*s}else-1!==c&&(a-=a-c),c=-1}else for(r+u>l&&(r=l-u),a=r;a>=0;a--){for(var d=!0,f=0;f<u;f++)if(i(e,a+f)!==i(t,f)){d=!1;break}if(d)return a}return-1}function j(e,t,r,o){r=Number(r)||0;var n=e.length-r;o?(o=Number(o))>n&&(o=n):o=n;var i=t.length;if(i%2!=0)throw new TypeError("Invalid hex string");o>i/2&&(o=i/2);for(var s=0;s<o;++s){var l=parseInt(t.substr(2*s,2),16);if(isNaN(l))return s;e[r+s]=l}return s}function E(e,t,r,o){return V(Y(t,e.length-r),e,r,o)}function O(e,t,r,o){return V(z(t),e,r,o)}function w(e,t,r,o){return O(e,t,r,o)}function x(e,t,r,o){return V(W(t),e,r,o)}function A(e,t,r,o){return V(G(t,e.length-r),e,r,o)}function N(e,t,r){return $.fromByteArray(0===t&&r===e.length?e:e.slice(t,r))}function T(e,t,r){r=Math.min(e.length,r);for(var o=[],n=t;n<r;){var i=e[n],s=null,l=i>239?4:i>223?3:i>191?2:1;if(n+l<=r){var u,a,c,d;switch(l){case 1:i<128&&(s=i);break;case 2:u=e[n+1],128==(192&u)&&(d=(31&i)<<6|63&u)>127&&(s=d);break;case 3:u=e[n+1],a=e[n+2],128==(192&u)&&128==(192&a)&&(d=(15&i)<<12|(63&u)<<6|63&a)>2047&&(d<55296||d>57343)&&(s=d);break;case 4:u=e[n+1],a=e[n+2],c=e[n+3],128==(192&u)&&128==(192&a)&&128==(192&c)&&(d=(15&i)<<18|(63&u)<<12|(63&a)<<6|63&c)>65535&&d<1114112&&(s=d)}}null===s?(s=65533,l=1):s>65535&&(s-=65536,o.push(s>>>10&1023|55296),s=56320|1023&s),o.push(s),n+=l}return k(o)}function k(e){var t=e.length;if(t<=Q)return String.fromCharCode.apply(String,e);for(var r="",o=0;o<t;)r+=String.fromCharCode.apply(String,e.slice(o,o+=Q));return r}function S(e,t,r){var o="";r=Math.min(e.length,r);for(var n=t;n<r;++n)o+=String.fromCharCode(127&e[n]);return o}function P(e,t,r){var o="";r=Math.min(e.length,r);for(var n=t;n<r;++n)o+=String.fromCharCode(e[n]);return o}function C(e,t,r){var o=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>o)&&(r=o);for(var n="",i=t;i<r;++i)n+=K(e[i]);return n}function M(e,t,r){for(var o=e.slice(t,r),n="",i=0;i<o.length;i+=2)n+=String.fromCharCode(o[i]+256*o[i+1]);return n}function L(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function I(e,t,r,o,n,s){if(!i.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<s)throw new RangeError('"value" argument is out of bounds');if(r+o>e.length)throw new RangeError("Index out of range")}function R(e,t,r,o){t<0&&(t=65535+t+1);for(var n=0,i=Math.min(e.length-r,2);n<i;++n)e[r+n]=(t&255<<8*(o?n:1-n))>>>8*(o?n:1-n)}function q(e,t,r,o){t<0&&(t=4294967295+t+1);for(var n=0,i=Math.min(e.length-r,4);n<i;++n)e[r+n]=t>>>8*(o?n:3-n)&255}function B(e,t,r,o,n,i){if(r+o>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function D(e,t,r,o,n){return n||B(e,t,r,4,3.4028234663852886e38,-3.4028234663852886e38),J.write(e,t,r,o,23,4),r+4}function U(e,t,r,o,n){return n||B(e,t,r,8,1.7976931348623157e308,-1.7976931348623157e308),J.write(e,t,r,o,52,8),r+8}function F(e){if(e=H(e).replace(ee,""),e.length<2)return"";for(;e.length%4!=0;)e+="=";return e}function H(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function K(e){return e<16?"0"+e.toString(16):e.toString(16)}function Y(e,t){t=t||1/0;for(var r,o=e.length,n=null,i=[],s=0;s<o;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!n){if(r>56319){(t-=3)>-1&&i.push(239,191,189);continue}if(s+1===o){(t-=3)>-1&&i.push(239,191,189);continue}n=r;continue}if(r<56320){(t-=3)>-1&&i.push(239,191,189),n=r;continue}r=65536+(n-55296<<10|r-56320)}else n&&(t-=3)>-1&&i.push(239,191,189);if(n=null,r<128){if((t-=1)<0)break;i.push(r)}else if(r<2048){if((t-=2)<0)break;i.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;i.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;i.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return i}function z(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function G(e,t){for(var r,o,n,i=[],s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),o=r>>8,n=r%256,i.push(n),i.push(o);return i}function W(e){return $.toByteArray(F(e))}function V(e,t,r,o){for(var n=0;n<o&&!(n+r>=t.length||n>=e.length);++n)t[n+r]=e[n];return n}function X(e){return e!==e}var $=r("../node_modules/base64-js/index.js"),J=r("../node_modules/ieee754/index.js"),Z=r("../node_modules/isarray/index.js");t.Buffer=i,t.SlowBuffer=m,t.INSPECT_MAX_BYTES=50,i.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),t.kMaxLength=o(),i.poolSize=8192,i._augment=function(e){return e.__proto__=i.prototype,e},i.from=function(e,t,r){return s(null,e,t,r)},i.TYPED_ARRAY_SUPPORT&&(i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&i[Symbol.species]===i&&Object.defineProperty(i,Symbol.species,{value:null,configurable:!0})),i.alloc=function(e,t,r){return u(null,e,t,r)},i.allocUnsafe=function(e){return a(null,e)},i.allocUnsafeSlow=function(e){return a(null,e)},i.isBuffer=function(e){return!(null==e||!e._isBuffer)},i.compare=function(e,t){if(!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,o=t.length,n=0,s=Math.min(r,o);n<s;++n)if(e[n]!==t[n]){r=e[n],o=t[n];break}return r<o?-1:o<r?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!Z(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return i.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var o=i.allocUnsafe(t),n=0;for(r=0;r<e.length;++r){var s=e[r];if(!i.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(o,n),n+=s.length}return o},i.byteLength=y,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)_(this,t,t+1);return this},i.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)_(this,t,t+3),_(this,t+1,t+2);return this},i.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)_(this,t,t+7),_(this,t+1,t+6),_(this,t+2,t+5),_(this,t+3,t+4);return this},i.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?T(this,0,e):b.apply(this,arguments)},i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===i.compare(this,e)},i.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"},i.prototype.compare=function(e,t,r,o,n){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===o&&(o=0),void 0===n&&(n=this.length),t<0||r>e.length||o<0||n>this.length)throw new RangeError("out of range index");if(o>=n&&t>=r)return 0;if(o>=n)return-1;if(t>=r)return 1;if(t>>>=0,r>>>=0,o>>>=0,n>>>=0,this===e)return 0;for(var s=n-o,l=r-t,u=Math.min(s,l),a=this.slice(o,n),c=e.slice(t,r),d=0;d<u;++d)if(a[d]!==c[d]){s=a[d],l=c[d];break}return s<l?-1:l<s?1:0},i.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},i.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},i.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)},i.prototype.write=function(e,t,r,o){if(void 0===t)o="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)o=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===o&&(o="utf8")):(o=r,r=void 0)}var n=this.length-t;if((void 0===r||r>n)&&(r=n),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");for(var i=!1;;)switch(o){case"hex":return j(this,e,t,r);case"utf8":case"utf-8":return E(this,e,t,r);case"ascii":return O(this,e,t,r);case"latin1":case"binary":return w(this,e,t,r);case"base64":return x(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return A(this,e,t,r);default:if(i)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),i=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Q=4096;i.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var o;if(i.TYPED_ARRAY_SUPPORT)o=this.subarray(e,t),o.__proto__=i.prototype;else{var n=t-e;o=new i(n,void 0);for(var s=0;s<n;++s)o[s]=this[s+e]}return o},i.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||L(e,t,this.length);for(var o=this[e],n=1,i=0;++i<t&&(n*=256);)o+=this[e+i]*n;return o},i.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||L(e,t,this.length);for(var o=this[e+--t],n=1;t>0&&(n*=256);)o+=this[e+--t]*n;return o},i.prototype.readUInt8=function(e,t){return t||L(e,1,this.length),this[e]},i.prototype.readUInt16LE=function(e,t){return t||L(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUInt16BE=function(e,t){return t||L(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUInt32LE=function(e,t){return t||L(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUInt32BE=function(e,t){return t||L(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||L(e,t,this.length);for(var o=this[e],n=1,i=0;++i<t&&(n*=256);)o+=this[e+i]*n;return n*=128,o>=n&&(o-=Math.pow(2,8*t)),o},i.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||L(e,t,this.length);for(var o=t,n=1,i=this[e+--o];o>0&&(n*=256);)i+=this[e+--o]*n;return n*=128,i>=n&&(i-=Math.pow(2,8*t)),i},i.prototype.readInt8=function(e,t){return t||L(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){t||L(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},i.prototype.readInt16BE=function(e,t){t||L(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},i.prototype.readInt32LE=function(e,t){return t||L(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return t||L(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readFloatLE=function(e,t){return t||L(e,4,this.length),J.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,t){return t||L(e,4,this.length),J.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,t){return t||L(e,8,this.length),J.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,t){return t||L(e,8,this.length),J.read(this,e,!1,52,8)},i.prototype.writeUIntLE=function(e,t,r,o){if(e=+e,t|=0,r|=0,!o){I(this,e,t,r,Math.pow(2,8*r)-1,0)}var n=1,i=0;for(this[t]=255&e;++i<r&&(n*=256);)this[t+i]=e/n&255;return t+r},i.prototype.writeUIntBE=function(e,t,r,o){if(e=+e,t|=0,r|=0,!o){I(this,e,t,r,Math.pow(2,8*r)-1,0)}var n=r-1,i=1;for(this[t+n]=255&e;--n>=0&&(i*=256);)this[t+n]=e/i&255;return t+r},i.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,1,255,0),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},i.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):R(this,e,t,!0),t+2},i.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,2,65535,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):R(this,e,t,!1),t+2},i.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):q(this,e,t,!0),t+4},i.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,4,4294967295,0),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):q(this,e,t,!1),t+4},i.prototype.writeIntLE=function(e,t,r,o){if(e=+e,t|=0,!o){var n=Math.pow(2,8*r-1);I(this,e,t,r,n-1,-n)}var i=0,s=1,l=0;for(this[t]=255&e;++i<r&&(s*=256);)e<0&&0===l&&0!==this[t+i-1]&&(l=1),this[t+i]=(e/s>>0)-l&255;return t+r},i.prototype.writeIntBE=function(e,t,r,o){if(e=+e,t|=0,!o){var n=Math.pow(2,8*r-1);I(this,e,t,r,n-1,-n)}var i=r-1,s=1,l=0;for(this[t+i]=255&e;--i>=0&&(s*=256);)e<0&&0===l&&0!==this[t+i+1]&&(l=1),this[t+i]=(e/s>>0)-l&255;return t+r},i.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,1,127,-128),i.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},i.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):R(this,e,t,!0),t+2},i.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,2,32767,-32768),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):R(this,e,t,!1),t+2},i.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,4,2147483647,-2147483648),i.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):q(this,e,t,!0),t+4},i.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||I(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),i.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):q(this,e,t,!1),t+4},i.prototype.writeFloatLE=function(e,t,r){return D(this,e,t,!0,r)},i.prototype.writeFloatBE=function(e,t,r){return D(this,e,t,!1,r)},i.prototype.writeDoubleLE=function(e,t,r){return U(this,e,t,!0,r)},i.prototype.writeDoubleBE=function(e,t,r){return U(this,e,t,!1,r)},i.prototype.copy=function(e,t,r,o){if(r||(r=0),o||0===o||(o=this.length),t>=e.length&&(t=e.length),t||(t=0),o>0&&o<r&&(o=r),o===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),e.length-t<o-r&&(o=e.length-t+r);var n,s=o-r;if(this===e&&r<t&&t<o)for(n=s-1;n>=0;--n)e[n+t]=this[n+r];else if(s<1e3||!i.TYPED_ARRAY_SUPPORT)for(n=0;n<s;++n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+s),t);return s},i.prototype.fill=function(e,t,r,o){if("string"==typeof e){if("string"==typeof t?(o=t,t=0,r=this.length):"string"==typeof r&&(o=r,r=this.length),1===e.length){var n=e.charCodeAt(0);n<256&&(e=n)}if(void 0!==o&&"string"!=typeof o)throw new TypeError("encoding must be a string");if("string"==typeof o&&!i.isEncoding(o))throw new TypeError("Unknown encoding: "+o)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0);var s;if("number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{var l=i.isBuffer(e)?e:Y(new i(e,o).toString()),u=l.length;for(s=0;s<r-t;++s)this[s+t]=l[s%u]}return this};var ee=/[^+\/0-9A-Za-z-_]/g}).call(t,r("../node_modules/webpack/buildin/global.js"))},"../node_modules/core-js/library/fn/array/from.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.string.iterator.js"),r("../node_modules/core-js/library/modules/es6.array.from.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Array.from},"../node_modules/core-js/library/fn/get-iterator.js":function(e,t,r){r("../node_modules/core-js/library/modules/web.dom.iterable.js"),r("../node_modules/core-js/library/modules/es6.string.iterator.js"),e.exports=r("../node_modules/core-js/library/modules/core.get-iterator.js")},"../node_modules/core-js/library/fn/is-iterable.js":function(e,t,r){r("../node_modules/core-js/library/modules/web.dom.iterable.js"),r("../node_modules/core-js/library/modules/es6.string.iterator.js"),e.exports=r("../node_modules/core-js/library/modules/core.is-iterable.js")},"../node_modules/core-js/library/fn/object/create.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.create.js");var o=r("../node_modules/core-js/library/modules/_core.js").Object;e.exports=function(e,t){return o.create(e,t)}},"../node_modules/core-js/library/fn/object/define-property.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.define-property.js");var o=r("../node_modules/core-js/library/modules/_core.js").Object;e.exports=function(e,t,r){return o.defineProperty(e,t,r)}},"../node_modules/core-js/library/fn/object/get-own-property-descriptor.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.get-own-property-descriptor.js");var o=r("../node_modules/core-js/library/modules/_core.js").Object;e.exports=function(e,t){return o.getOwnPropertyDescriptor(e,t)}},"../node_modules/core-js/library/fn/object/get-prototype-of.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.get-prototype-of.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Object.getPrototypeOf},"../node_modules/core-js/library/fn/object/keys.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.keys.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Object.keys},"../node_modules/core-js/library/fn/object/set-prototype-of.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.set-prototype-of.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Object.setPrototypeOf},"../node_modules/core-js/library/fn/promise.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.object.to-string.js"),r("../node_modules/core-js/library/modules/es6.string.iterator.js"),r("../node_modules/core-js/library/modules/web.dom.iterable.js"),r("../node_modules/core-js/library/modules/es6.promise.js"),r("../node_modules/core-js/library/modules/es7.promise.finally.js"),r("../node_modules/core-js/library/modules/es7.promise.try.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Promise},"../node_modules/core-js/library/fn/symbol/index.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.symbol.js"),r("../node_modules/core-js/library/modules/es6.object.to-string.js"),r("../node_modules/core-js/library/modules/es7.symbol.async-iterator.js"),r("../node_modules/core-js/library/modules/es7.symbol.observable.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Symbol},"../node_modules/core-js/library/fn/symbol/iterator.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.string.iterator.js"),r("../node_modules/core-js/library/modules/web.dom.iterable.js"),e.exports=r("../node_modules/core-js/library/modules/_wks-ext.js").f("iterator")},"../node_modules/core-js/library/modules/_a-function.js":function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},"../node_modules/core-js/library/modules/_add-to-unscopables.js":function(e,t){e.exports=function(){}},"../node_modules/core-js/library/modules/_an-instance.js":function(e,t){e.exports=function(e,t,r,o){if(!(e instanceof t)||void 0!==o&&o in e)throw TypeError(r+": incorrect invocation!");return e}},"../node_modules/core-js/library/modules/_an-object.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_is-object.js");e.exports=function(e){if(!o(e))throw TypeError(e+" is not an object!");return e}},"../node_modules/core-js/library/modules/_array-includes.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-iobject.js"),n=r("../node_modules/core-js/library/modules/_to-length.js"),i=r("../node_modules/core-js/library/modules/_to-absolute-index.js");e.exports=function(e){return function(t,r,s){var l,u=o(t),a=n(u.length),c=i(s,a);if(e&&r!=r){for(;a>c;)if((l=u[c++])!=l)return!0}else for(;a>c;c++)if((e||c in u)&&u[c]===r)return e||c||0;return!e&&-1}}},"../node_modules/core-js/library/modules/_classof.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_cof.js"),n=r("../node_modules/core-js/library/modules/_wks.js")("toStringTag"),i="Arguments"==o(function(){return arguments}()),s=function(e,t){try{return e[t]}catch(e){}};e.exports=function(e){var t,r,l;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(r=s(t=Object(e),n))?r:i?o(t):"Object"==(l=o(t))&&"function"==typeof t.callee?"Arguments":l}},"../node_modules/core-js/library/modules/_cof.js":function(e,t){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},"../node_modules/core-js/library/modules/_core.js":function(e,t){var r=e.exports={version:"2.6.10"};"number"==typeof __e&&(__e=r)},"../node_modules/core-js/library/modules/_create-property.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_object-dp.js"),n=r("../node_modules/core-js/library/modules/_property-desc.js");e.exports=function(e,t,r){t in e?o.f(e,t,n(0,r)):e[t]=r}},"../node_modules/core-js/library/modules/_ctx.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_a-function.js");e.exports=function(e,t,r){if(o(e),void 0===t)return e;switch(r){case 1:return function(r){return e.call(t,r)};case 2:return function(r,o){return e.call(t,r,o)};case 3:return function(r,o,n){return e.call(t,r,o,n)}}return function(){return e.apply(t,arguments)}}},"../node_modules/core-js/library/modules/_defined.js":function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},"../node_modules/core-js/library/modules/_descriptors.js":function(e,t,r){e.exports=!r("../node_modules/core-js/library/modules/_fails.js")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},"../node_modules/core-js/library/modules/_dom-create.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_is-object.js"),n=r("../node_modules/core-js/library/modules/_global.js").document,i=o(n)&&o(n.createElement);e.exports=function(e){return i?n.createElement(e):{}}},"../node_modules/core-js/library/modules/_enum-bug-keys.js":function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},"../node_modules/core-js/library/modules/_enum-keys.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-keys.js"),n=r("../node_modules/core-js/library/modules/_object-gops.js"),i=r("../node_modules/core-js/library/modules/_object-pie.js");e.exports=function(e){var t=o(e),r=n.f;if(r)for(var s,l=r(e),u=i.f,a=0;l.length>a;)u.call(e,s=l[a++])&&t.push(s);return t}},"../node_modules/core-js/library/modules/_export.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_global.js"),n=r("../node_modules/core-js/library/modules/_core.js"),i=r("../node_modules/core-js/library/modules/_ctx.js"),s=r("../node_modules/core-js/library/modules/_hide.js"),l=r("../node_modules/core-js/library/modules/_has.js"),u=function(e,t,r){var a,c,d,f=e&u.F,h=e&u.G,p=e&u.S,m=e&u.P,y=e&u.B,b=e&u.W,_=h?n:n[t]||(n[t]={}),v=_.prototype,g=h?o:p?o[t]:(o[t]||{}).prototype;h&&(r=t);for(a in r)(c=!f&&g&&void 0!==g[a])&&l(_,a)||(d=c?g[a]:r[a],_[a]=h&&"function"!=typeof g[a]?r[a]:y&&c?i(d,o):b&&g[a]==d?function(e){var t=function(t,r,o){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,r)}return new e(t,r,o)}return e.apply(this,arguments)};return t.prototype=e.prototype,t}(d):m&&"function"==typeof d?i(Function.call,d):d,m&&((_.virtual||(_.virtual={}))[a]=d,e&u.R&&v&&!v[a]&&s(v,a,d)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},"../node_modules/core-js/library/modules/_fails.js":function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},"../node_modules/core-js/library/modules/_for-of.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_ctx.js"),n=r("../node_modules/core-js/library/modules/_iter-call.js"),i=r("../node_modules/core-js/library/modules/_is-array-iter.js"),s=r("../node_modules/core-js/library/modules/_an-object.js"),l=r("../node_modules/core-js/library/modules/_to-length.js"),u=r("../node_modules/core-js/library/modules/core.get-iterator-method.js"),a={},c={},t=e.exports=function(e,t,r,d,f){var h,p,m,y,b=f?function(){return e}:u(e),_=o(r,d,t?2:1),v=0;if("function"!=typeof b)throw TypeError(e+" is not iterable!");if(i(b)){for(h=l(e.length);h>v;v++)if((y=t?_(s(p=e[v])[0],p[1]):_(e[v]))===a||y===c)return y}else for(m=b.call(e);!(p=m.next()).done;)if((y=n(m,_,p.value,t))===a||y===c)return y};t.BREAK=a,t.RETURN=c},"../node_modules/core-js/library/modules/_global.js":function(e,t){var r=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},"../node_modules/core-js/library/modules/_has.js":function(e,t){var r={}.hasOwnProperty;e.exports=function(e,t){return r.call(e,t)}},"../node_modules/core-js/library/modules/_hide.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-dp.js"),n=r("../node_modules/core-js/library/modules/_property-desc.js");e.exports=r("../node_modules/core-js/library/modules/_descriptors.js")?function(e,t,r){return o.f(e,t,n(1,r))}:function(e,t,r){return e[t]=r,e}},"../node_modules/core-js/library/modules/_html.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_global.js").document;e.exports=o&&o.documentElement},"../node_modules/core-js/library/modules/_ie8-dom-define.js":function(e,t,r){e.exports=!r("../node_modules/core-js/library/modules/_descriptors.js")&&!r("../node_modules/core-js/library/modules/_fails.js")(function(){return 7!=Object.defineProperty(r("../node_modules/core-js/library/modules/_dom-create.js")("div"),"a",{get:function(){return 7}}).a})},"../node_modules/core-js/library/modules/_invoke.js":function(e,t){e.exports=function(e,t,r){var o=void 0===r;switch(t.length){case 0:return o?e():e.call(r);case 1:return o?e(t[0]):e.call(r,t[0]);case 2:return o?e(t[0],t[1]):e.call(r,t[0],t[1]);case 3:return o?e(t[0],t[1],t[2]):e.call(r,t[0],t[1],t[2]);case 4:return o?e(t[0],t[1],t[2],t[3]):e.call(r,t[0],t[1],t[2],t[3])}return e.apply(r,t)}},"../node_modules/core-js/library/modules/_iobject.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_cof.js");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==o(e)?e.split(""):Object(e)}},"../node_modules/core-js/library/modules/_is-array-iter.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_iterators.js"),n=r("../node_modules/core-js/library/modules/_wks.js")("iterator"),i=Array.prototype;e.exports=function(e){return void 0!==e&&(o.Array===e||i[n]===e)}},"../node_modules/core-js/library/modules/_is-array.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_cof.js");e.exports=Array.isArray||function(e){return"Array"==o(e)}},"../node_modules/core-js/library/modules/_is-object.js":function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},"../node_modules/core-js/library/modules/_iter-call.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_an-object.js");e.exports=function(e,t,r,n){try{return n?t(o(r)[0],r[1]):t(r)}catch(t){var i=e.return;throw void 0!==i&&o(i.call(e)),t}}},"../node_modules/core-js/library/modules/_iter-create.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_object-create.js"),n=r("../node_modules/core-js/library/modules/_property-desc.js"),i=r("../node_modules/core-js/library/modules/_set-to-string-tag.js"),s={};r("../node_modules/core-js/library/modules/_hide.js")(s,r("../node_modules/core-js/library/modules/_wks.js")("iterator"),function(){return this}),e.exports=function(e,t,r){e.prototype=o(s,{next:n(1,r)}),i(e,t+" Iterator")}},"../node_modules/core-js/library/modules/_iter-define.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_library.js"),n=r("../node_modules/core-js/library/modules/_export.js"),i=r("../node_modules/core-js/library/modules/_redefine.js"),s=r("../node_modules/core-js/library/modules/_hide.js"),l=r("../node_modules/core-js/library/modules/_iterators.js"),u=r("../node_modules/core-js/library/modules/_iter-create.js"),a=r("../node_modules/core-js/library/modules/_set-to-string-tag.js"),c=r("../node_modules/core-js/library/modules/_object-gpo.js"),d=r("../node_modules/core-js/library/modules/_wks.js")("iterator"),f=!([].keys&&"next"in[].keys()),h=function(){return this};e.exports=function(e,t,r,p,m,y,b){u(r,t,p);var _,v,g,j=function(e){if(!f&&e in x)return x[e];switch(e){case"keys":case"values":return function(){return new r(this,e)}}return function(){return new r(this,e)}},E=t+" Iterator",O="values"==m,w=!1,x=e.prototype,A=x[d]||x["@@iterator"]||m&&x[m],N=A||j(m),T=m?O?j("entries"):N:void 0,k="Array"==t?x.entries||A:A;if(k&&(g=c(k.call(new e)))!==Object.prototype&&g.next&&(a(g,E,!0),o||"function"==typeof g[d]||s(g,d,h)),O&&A&&"values"!==A.name&&(w=!0,N=function(){return A.call(this)}),o&&!b||!f&&!w&&x[d]||s(x,d,N),l[t]=N,l[E]=h,m)if(_={values:O?N:j("values"),keys:y?N:j("keys"),entries:T},b)for(v in _)v in x||i(x,v,_[v]);else n(n.P+n.F*(f||w),t,_);return _}},"../node_modules/core-js/library/modules/_iter-detect.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_wks.js")("iterator"),n=!1;try{var i=[7][o]();i.return=function(){n=!0},Array.from(i,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!n)return!1;var r=!1;try{var i=[7],s=i[o]();s.next=function(){return{done:r=!0}},i[o]=function(){return s},e(i)}catch(e){}return r}},"../node_modules/core-js/library/modules/_iter-step.js":function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},"../node_modules/core-js/library/modules/_iterators.js":function(e,t){e.exports={}},"../node_modules/core-js/library/modules/_library.js":function(e,t){e.exports=!0},"../node_modules/core-js/library/modules/_meta.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_uid.js")("meta"),n=r("../node_modules/core-js/library/modules/_is-object.js"),i=r("../node_modules/core-js/library/modules/_has.js"),s=r("../node_modules/core-js/library/modules/_object-dp.js").f,l=0,u=Object.isExtensible||function(){return!0},a=!r("../node_modules/core-js/library/modules/_fails.js")(function(){return u(Object.preventExtensions({}))}),c=function(e){s(e,o,{value:{i:"O"+ ++l,w:{}}})},d=function(e,t){if(!n(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!i(e,o)){if(!u(e))return"F";if(!t)return"E";c(e)}return e[o].i},f=function(e,t){if(!i(e,o)){if(!u(e))return!0;if(!t)return!1;c(e)}return e[o].w},h=function(e){return a&&p.NEED&&u(e)&&!i(e,o)&&c(e),e},p=e.exports={KEY:o,NEED:!1,fastKey:d,getWeak:f,onFreeze:h}},"../node_modules/core-js/library/modules/_microtask.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_global.js"),n=r("../node_modules/core-js/library/modules/_task.js").set,i=o.MutationObserver||o.WebKitMutationObserver,s=o.process,l=o.Promise,u="process"==r("../node_modules/core-js/library/modules/_cof.js")(s);e.exports=function(){var e,t,r,a=function(){var o,n;for(u&&(o=s.domain)&&o.exit();e;){n=e.fn,e=e.next;try{n()}catch(o){throw e?r():t=void 0,o}}t=void 0,o&&o.enter()};if(u)r=function(){s.nextTick(a)};else if(!i||o.navigator&&o.navigator.standalone)if(l&&l.resolve){var c=l.resolve(void 0);r=function(){c.then(a)}}else r=function(){n.call(o,a)};else{var d=!0,f=document.createTextNode("");new i(a).observe(f,{characterData:!0}),r=function(){f.data=d=!d}}return function(o){var n={fn:o,next:void 0};t&&(t.next=n),e||(e=n,r()),t=n}}},"../node_modules/core-js/library/modules/_new-promise-capability.js":function(e,t,r){"use strict";function o(e){var t,r;this.promise=new e(function(e,o){if(void 0!==t||void 0!==r)throw TypeError("Bad Promise constructor");t=e,r=o}),this.resolve=n(t),this.reject=n(r)}var n=r("../node_modules/core-js/library/modules/_a-function.js");e.exports.f=function(e){return new o(e)}},"../node_modules/core-js/library/modules/_object-create.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_an-object.js"),n=r("../node_modules/core-js/library/modules/_object-dps.js"),i=r("../node_modules/core-js/library/modules/_enum-bug-keys.js"),s=r("../node_modules/core-js/library/modules/_shared-key.js")("IE_PROTO"),l=function(){},u=function(){var e,t=r("../node_modules/core-js/library/modules/_dom-create.js")("iframe"),o=i.length;for(t.style.display="none",r("../node_modules/core-js/library/modules/_html.js").appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;o--;)delete u.prototype[i[o]];return u()};e.exports=Object.create||function(e,t){var r;return null!==e?(l.prototype=o(e),r=new l,l.prototype=null,r[s]=e):r=u(),void 0===t?r:n(r,t)}},"../node_modules/core-js/library/modules/_object-dp.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_an-object.js"),n=r("../node_modules/core-js/library/modules/_ie8-dom-define.js"),i=r("../node_modules/core-js/library/modules/_to-primitive.js"),s=Object.defineProperty;t.f=r("../node_modules/core-js/library/modules/_descriptors.js")?Object.defineProperty:function(e,t,r){if(o(e),t=i(t,!0),o(r),n)try{return s(e,t,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[t]=r.value),e}},"../node_modules/core-js/library/modules/_object-dps.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-dp.js"),n=r("../node_modules/core-js/library/modules/_an-object.js"),i=r("../node_modules/core-js/library/modules/_object-keys.js");e.exports=r("../node_modules/core-js/library/modules/_descriptors.js")?Object.defineProperties:function(e,t){n(e);for(var r,s=i(t),l=s.length,u=0;l>u;)o.f(e,r=s[u++],t[r]);return e}},"../node_modules/core-js/library/modules/_object-gopd.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-pie.js"),n=r("../node_modules/core-js/library/modules/_property-desc.js"),i=r("../node_modules/core-js/library/modules/_to-iobject.js"),s=r("../node_modules/core-js/library/modules/_to-primitive.js"),l=r("../node_modules/core-js/library/modules/_has.js"),u=r("../node_modules/core-js/library/modules/_ie8-dom-define.js"),a=Object.getOwnPropertyDescriptor;t.f=r("../node_modules/core-js/library/modules/_descriptors.js")?a:function(e,t){if(e=i(e),t=s(t,!0),u)try{return a(e,t)}catch(e){}if(l(e,t))return n(!o.f.call(e,t),e[t])}},"../node_modules/core-js/library/modules/_object-gopn-ext.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-iobject.js"),n=r("../node_modules/core-js/library/modules/_object-gopn.js").f,i={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],l=function(e){try{return n(e)}catch(e){return s.slice()}};e.exports.f=function(e){return s&&"[object Window]"==i.call(e)?l(e):n(o(e))}},"../node_modules/core-js/library/modules/_object-gopn.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-keys-internal.js"),n=r("../node_modules/core-js/library/modules/_enum-bug-keys.js").concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return o(e,n)}},"../node_modules/core-js/library/modules/_object-gops.js":function(e,t){t.f=Object.getOwnPropertySymbols},"../node_modules/core-js/library/modules/_object-gpo.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_has.js"),n=r("../node_modules/core-js/library/modules/_to-object.js"),i=r("../node_modules/core-js/library/modules/_shared-key.js")("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=n(e),o(e,i)?e[i]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},"../node_modules/core-js/library/modules/_object-keys-internal.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_has.js"),n=r("../node_modules/core-js/library/modules/_to-iobject.js"),i=r("../node_modules/core-js/library/modules/_array-includes.js")(!1),s=r("../node_modules/core-js/library/modules/_shared-key.js")("IE_PROTO");e.exports=function(e,t){var r,l=n(e),u=0,a=[];for(r in l)r!=s&&o(l,r)&&a.push(r);for(;t.length>u;)o(l,r=t[u++])&&(~i(a,r)||a.push(r));return a}},"../node_modules/core-js/library/modules/_object-keys.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-keys-internal.js"),n=r("../node_modules/core-js/library/modules/_enum-bug-keys.js");e.exports=Object.keys||function(e){return o(e,n)}},"../node_modules/core-js/library/modules/_object-pie.js":function(e,t){t.f={}.propertyIsEnumerable},"../node_modules/core-js/library/modules/_object-sap.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_export.js"),n=r("../node_modules/core-js/library/modules/_core.js"),i=r("../node_modules/core-js/library/modules/_fails.js");e.exports=function(e,t){var r=(n.Object||{})[e]||Object[e],s={};s[e]=t(r),o(o.S+o.F*i(function(){r(1)}),"Object",s)}},"../node_modules/core-js/library/modules/_perform.js":function(e,t){e.exports=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}}},"../node_modules/core-js/library/modules/_promise-resolve.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_an-object.js"),n=r("../node_modules/core-js/library/modules/_is-object.js"),i=r("../node_modules/core-js/library/modules/_new-promise-capability.js");e.exports=function(e,t){if(o(e),n(t)&&t.constructor===e)return t;var r=i.f(e);return(0,r.resolve)(t),r.promise}},"../node_modules/core-js/library/modules/_property-desc.js":function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},"../node_modules/core-js/library/modules/_redefine-all.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_hide.js");e.exports=function(e,t,r){for(var n in t)r&&e[n]?e[n]=t[n]:o(e,n,t[n]);return e}},"../node_modules/core-js/library/modules/_redefine.js":function(e,t,r){e.exports=r("../node_modules/core-js/library/modules/_hide.js")},"../node_modules/core-js/library/modules/_set-proto.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_is-object.js"),n=r("../node_modules/core-js/library/modules/_an-object.js"),i=function(e,t){if(n(e),!o(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,o){try{o=r("../node_modules/core-js/library/modules/_ctx.js")(Function.call,r("../node_modules/core-js/library/modules/_object-gopd.js").f(Object.prototype,"__proto__").set,2),o(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return i(e,r),t?e.__proto__=r:o(e,r),e}}({},!1):void 0),check:i}},"../node_modules/core-js/library/modules/_set-species.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_global.js"),n=r("../node_modules/core-js/library/modules/_core.js"),i=r("../node_modules/core-js/library/modules/_object-dp.js"),s=r("../node_modules/core-js/library/modules/_descriptors.js"),l=r("../node_modules/core-js/library/modules/_wks.js")("species");e.exports=function(e){var t="function"==typeof n[e]?n[e]:o[e];s&&t&&!t[l]&&i.f(t,l,{configurable:!0,get:function(){return this}})}},"../node_modules/core-js/library/modules/_set-to-string-tag.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_object-dp.js").f,n=r("../node_modules/core-js/library/modules/_has.js"),i=r("../node_modules/core-js/library/modules/_wks.js")("toStringTag");e.exports=function(e,t,r){e&&!n(e=r?e:e.prototype,i)&&o(e,i,{configurable:!0,value:t})}},"../node_modules/core-js/library/modules/_shared-key.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_shared.js")("keys"),n=r("../node_modules/core-js/library/modules/_uid.js");e.exports=function(e){return o[e]||(o[e]=n(e))}},"../node_modules/core-js/library/modules/_shared.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_core.js"),n=r("../node_modules/core-js/library/modules/_global.js"),i=n["__core-js_shared__"]||(n["__core-js_shared__"]={});(e.exports=function(e,t){return i[e]||(i[e]=void 0!==t?t:{})})("versions",[]).push({version:o.version,mode:r("../node_modules/core-js/library/modules/_library.js")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"../node_modules/core-js/library/modules/_species-constructor.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_an-object.js"),n=r("../node_modules/core-js/library/modules/_a-function.js"),i=r("../node_modules/core-js/library/modules/_wks.js")("species");e.exports=function(e,t){var r,s=o(e).constructor;return void 0===s||void 0==(r=o(s)[i])?t:n(r)}},"../node_modules/core-js/library/modules/_string-at.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-integer.js"),n=r("../node_modules/core-js/library/modules/_defined.js");e.exports=function(e){return function(t,r){var i,s,l=String(n(t)),u=o(r),a=l.length;return u<0||u>=a?e?"":void 0:(i=l.charCodeAt(u),i<55296||i>56319||u+1===a||(s=l.charCodeAt(u+1))<56320||s>57343?e?l.charAt(u):i:e?l.slice(u,u+2):s-56320+(i-55296<<10)+65536)}}},"../node_modules/core-js/library/modules/_task.js":function(e,t,r){var o,n,i,s=r("../node_modules/core-js/library/modules/_ctx.js"),l=r("../node_modules/core-js/library/modules/_invoke.js"),u=r("../node_modules/core-js/library/modules/_html.js"),a=r("../node_modules/core-js/library/modules/_dom-create.js"),c=r("../node_modules/core-js/library/modules/_global.js"),d=c.process,f=c.setImmediate,h=c.clearImmediate,p=c.MessageChannel,m=c.Dispatch,y=0,b={},_=function(){var e=+this;if(b.hasOwnProperty(e)){var t=b[e];delete b[e],t()}},v=function(e){_.call(e.data)};f&&h||(f=function(e){for(var t=[],r=1;arguments.length>r;)t.push(arguments[r++]);return b[++y]=function(){l("function"==typeof e?e:Function(e),t)},o(y),y},h=function(e){delete b[e]},"process"==r("../node_modules/core-js/library/modules/_cof.js")(d)?o=function(e){d.nextTick(s(_,e,1))}:m&&m.now?o=function(e){m.now(s(_,e,1))}:p?(n=new p,i=n.port2,n.port1.onmessage=v,o=s(i.postMessage,i,1)):c.addEventListener&&"function"==typeof postMessage&&!c.importScripts?(o=function(e){c.postMessage(e+"","*")},c.addEventListener("message",v,!1)):o="onreadystatechange"in a("script")?function(e){u.appendChild(a("script")).onreadystatechange=function(){u.removeChild(this),_.call(e)}}:function(e){setTimeout(s(_,e,1),0)}),e.exports={set:f,clear:h}},"../node_modules/core-js/library/modules/_to-absolute-index.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-integer.js"),n=Math.max,i=Math.min;e.exports=function(e,t){return e=o(e),e<0?n(e+t,0):i(e,t)}},"../node_modules/core-js/library/modules/_to-integer.js":function(e,t){var r=Math.ceil,o=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?o:r)(e)}},"../node_modules/core-js/library/modules/_to-iobject.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_iobject.js"),n=r("../node_modules/core-js/library/modules/_defined.js");e.exports=function(e){return o(n(e))}},"../node_modules/core-js/library/modules/_to-length.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-integer.js"),n=Math.min;e.exports=function(e){return e>0?n(o(e),9007199254740991):0}},"../node_modules/core-js/library/modules/_to-object.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_defined.js");e.exports=function(e){return Object(o(e))}},"../node_modules/core-js/library/modules/_to-primitive.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_is-object.js");e.exports=function(e,t){if(!o(e))return e;var r,n;if(t&&"function"==typeof(r=e.toString)&&!o(n=r.call(e)))return n;if("function"==typeof(r=e.valueOf)&&!o(n=r.call(e)))return n;if(!t&&"function"==typeof(r=e.toString)&&!o(n=r.call(e)))return n;throw TypeError("Can't convert object to primitive value")}},"../node_modules/core-js/library/modules/_uid.js":function(e,t){var r=0,o=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++r+o).toString(36))}},"../node_modules/core-js/library/modules/_user-agent.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_global.js"),n=o.navigator;e.exports=n&&n.userAgent||""},"../node_modules/core-js/library/modules/_wks-define.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_global.js"),n=r("../node_modules/core-js/library/modules/_core.js"),i=r("../node_modules/core-js/library/modules/_library.js"),s=r("../node_modules/core-js/library/modules/_wks-ext.js"),l=r("../node_modules/core-js/library/modules/_object-dp.js").f;e.exports=function(e){var t=n.Symbol||(n.Symbol=i?{}:o.Symbol||{});"_"==e.charAt(0)||e in t||l(t,e,{value:s.f(e)})}},"../node_modules/core-js/library/modules/_wks-ext.js":function(e,t,r){t.f=r("../node_modules/core-js/library/modules/_wks.js")},"../node_modules/core-js/library/modules/_wks.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_shared.js")("wks"),n=r("../node_modules/core-js/library/modules/_uid.js"),i=r("../node_modules/core-js/library/modules/_global.js").Symbol,s="function"==typeof i;(e.exports=function(e){return o[e]||(o[e]=s&&i[e]||(s?i:n)("Symbol."+e))}).store=o},"../node_modules/core-js/library/modules/core.get-iterator-method.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_classof.js"),n=r("../node_modules/core-js/library/modules/_wks.js")("iterator"),i=r("../node_modules/core-js/library/modules/_iterators.js");e.exports=r("../node_modules/core-js/library/modules/_core.js").getIteratorMethod=function(e){if(void 0!=e)return e[n]||e["@@iterator"]||i[o(e)]}},"../node_modules/core-js/library/modules/core.get-iterator.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_an-object.js"),n=r("../node_modules/core-js/library/modules/core.get-iterator-method.js");e.exports=r("../node_modules/core-js/library/modules/_core.js").getIterator=function(e){var t=n(e);if("function"!=typeof t)throw TypeError(e+" is not iterable!");return o(t.call(e))}},"../node_modules/core-js/library/modules/core.is-iterable.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_classof.js"),n=r("../node_modules/core-js/library/modules/_wks.js")("iterator"),i=r("../node_modules/core-js/library/modules/_iterators.js");e.exports=r("../node_modules/core-js/library/modules/_core.js").isIterable=function(e){var t=Object(e);return void 0!==t[n]||"@@iterator"in t||i.hasOwnProperty(o(t))}},"../node_modules/core-js/library/modules/es6.array.from.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_ctx.js"),n=r("../node_modules/core-js/library/modules/_export.js"),i=r("../node_modules/core-js/library/modules/_to-object.js"),s=r("../node_modules/core-js/library/modules/_iter-call.js"),l=r("../node_modules/core-js/library/modules/_is-array-iter.js"),u=r("../node_modules/core-js/library/modules/_to-length.js"),a=r("../node_modules/core-js/library/modules/_create-property.js"),c=r("../node_modules/core-js/library/modules/core.get-iterator-method.js");n(n.S+n.F*!r("../node_modules/core-js/library/modules/_iter-detect.js")(function(e){Array.from(e)}),"Array",{from:function(e){var t,r,n,d,f=i(e),h="function"==typeof this?this:Array,p=arguments.length,m=p>1?arguments[1]:void 0,y=void 0!==m,b=0,_=c(f);if(y&&(m=o(m,p>2?arguments[2]:void 0,2)),void 0==_||h==Array&&l(_))for(t=u(f.length),r=new h(t);t>b;b++)a(r,b,y?m(f[b],b):f[b]);else for(d=_.call(f),r=new h;!(n=d.next()).done;b++)a(r,b,y?s(d,m,[n.value,b],!0):n.value);return r.length=b,r}})},"../node_modules/core-js/library/modules/es6.array.iterator.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_add-to-unscopables.js"),n=r("../node_modules/core-js/library/modules/_iter-step.js"),i=r("../node_modules/core-js/library/modules/_iterators.js"),s=r("../node_modules/core-js/library/modules/_to-iobject.js");e.exports=r("../node_modules/core-js/library/modules/_iter-define.js")(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,n(1)):"keys"==t?n(0,r):"values"==t?n(0,e[r]):n(0,[r,e[r]])},"values"),i.Arguments=i.Array,o("keys"),o("values"),o("entries")},"../node_modules/core-js/library/modules/es6.object.create.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_export.js");o(o.S,"Object",{create:r("../node_modules/core-js/library/modules/_object-create.js")})},"../node_modules/core-js/library/modules/es6.object.define-property.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_export.js");o(o.S+o.F*!r("../node_modules/core-js/library/modules/_descriptors.js"),"Object",{defineProperty:r("../node_modules/core-js/library/modules/_object-dp.js").f})},"../node_modules/core-js/library/modules/es6.object.get-own-property-descriptor.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-iobject.js"),n=r("../node_modules/core-js/library/modules/_object-gopd.js").f;r("../node_modules/core-js/library/modules/_object-sap.js")("getOwnPropertyDescriptor",function(){return function(e,t){return n(o(e),t)}})},"../node_modules/core-js/library/modules/es6.object.get-prototype-of.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-object.js"),n=r("../node_modules/core-js/library/modules/_object-gpo.js");r("../node_modules/core-js/library/modules/_object-sap.js")("getPrototypeOf",function(){return function(e){return n(o(e))}})},"../node_modules/core-js/library/modules/es6.object.keys.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_to-object.js"),n=r("../node_modules/core-js/library/modules/_object-keys.js");r("../node_modules/core-js/library/modules/_object-sap.js")("keys",function(){return function(e){return n(o(e))}})},"../node_modules/core-js/library/modules/es6.object.set-prototype-of.js":function(e,t,r){var o=r("../node_modules/core-js/library/modules/_export.js");o(o.S,"Object",{setPrototypeOf:r("../node_modules/core-js/library/modules/_set-proto.js").set})},"../node_modules/core-js/library/modules/es6.object.to-string.js":function(e,t){},"../node_modules/core-js/library/modules/es6.promise.js":function(e,t,r){"use strict";var o,n,i,s,l=r("../node_modules/core-js/library/modules/_library.js"),u=r("../node_modules/core-js/library/modules/_global.js"),a=r("../node_modules/core-js/library/modules/_ctx.js"),c=r("../node_modules/core-js/library/modules/_classof.js"),d=r("../node_modules/core-js/library/modules/_export.js"),f=r("../node_modules/core-js/library/modules/_is-object.js"),h=r("../node_modules/core-js/library/modules/_a-function.js"),p=r("../node_modules/core-js/library/modules/_an-instance.js"),m=r("../node_modules/core-js/library/modules/_for-of.js"),y=r("../node_modules/core-js/library/modules/_species-constructor.js"),b=r("../node_modules/core-js/library/modules/_task.js").set,_=r("../node_modules/core-js/library/modules/_microtask.js")(),v=r("../node_modules/core-js/library/modules/_new-promise-capability.js"),g=r("../node_modules/core-js/library/modules/_perform.js"),j=r("../node_modules/core-js/library/modules/_user-agent.js"),E=r("../node_modules/core-js/library/modules/_promise-resolve.js"),O=u.TypeError,w=u.process,x=w&&w.versions,A=x&&x.v8||"",N=u.Promise,T="process"==c(w),k=function(){},S=n=v.f,P=!!function(){try{var e=N.resolve(1),t=(e.constructor={})[r("../node_modules/core-js/library/modules/_wks.js")("species")]=function(e){e(k,k)};return(T||"function"==typeof PromiseRejectionEvent)&&e.then(k)instanceof t&&0!==A.indexOf("6.6")&&-1===j.indexOf("Chrome/66")}catch(e){}}(),C=function(e){var t;return!(!f(e)||"function"!=typeof(t=e.then))&&t},M=function(e,t){if(!e._n){e._n=!0;var r=e._c;_(function(){for(var o=e._v,n=1==e._s,i=0;r.length>i;)!function(t){var r,i,s,l=n?t.ok:t.fail,u=t.resolve,a=t.reject,c=t.domain;try{l?(n||(2==e._h&&R(e),e._h=1),!0===l?r=o:(c&&c.enter(),r=l(o),c&&(c.exit(),s=!0)),r===t.promise?a(O("Promise-chain cycle")):(i=C(r))?i.call(r,u,a):u(r)):a(o)}catch(e){c&&!s&&c.exit(),a(e)}}(r[i++]);e._c=[],e._n=!1,t&&!e._h&&L(e)})}},L=function(e){b.call(u,function(){var t,r,o,n=e._v,i=I(e);if(i&&(t=g(function(){T?w.emit("unhandledRejection",n,e):(r=u.onunhandledrejection)?r({promise:e,reason:n}):(o=u.console)&&o.error&&o.error("Unhandled promise rejection",n)}),e._h=T||I(e)?2:1),e._a=void 0,i&&t.e)throw t.v})},I=function(e){return 1!==e._h&&0===(e._a||e._c).length},R=function(e){b.call(u,function(){var t;T?w.emit("rejectionHandled",e):(t=u.onrejectionhandled)&&t({promise:e,reason:e._v})})},q=function(e){var t=this;t._d||(t._d=!0,t=t._w||t,t._v=e,t._s=2,t._a||(t._a=t._c.slice()),M(t,!0))},B=function(e){var t,r=this;if(!r._d){r._d=!0,r=r._w||r;try{if(r===e)throw O("Promise can't be resolved itself");(t=C(e))?_(function(){var o={_w:r,_d:!1};try{t.call(e,a(B,o,1),a(q,o,1))}catch(e){q.call(o,e)}}):(r._v=e,r._s=1,M(r,!1))}catch(e){q.call({_w:r,_d:!1},e)}}};P||(N=function(e){p(this,N,"Promise","_h"),h(e),o.call(this);try{e(a(B,this,1),a(q,this,1))}catch(e){q.call(this,e)}},o=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1},o.prototype=r("../node_modules/core-js/library/modules/_redefine-all.js")(N.prototype,{then:function(e,t){var r=S(y(this,N));return r.ok="function"!=typeof e||e,r.fail="function"==typeof t&&t,r.domain=T?w.domain:void 0,this._c.push(r),this._a&&this._a.push(r),this._s&&M(this,!1),r.promise},catch:function(e){return this.then(void 0,e)}}),i=function(){var e=new o;this.promise=e,this.resolve=a(B,e,1),this.reject=a(q,e,1)},v.f=S=function(e){return e===N||e===s?new i(e):n(e)}),d(d.G+d.W+d.F*!P,{Promise:N}),r("../node_modules/core-js/library/modules/_set-to-string-tag.js")(N,"Promise"),r("../node_modules/core-js/library/modules/_set-species.js")("Promise"),s=r("../node_modules/core-js/library/modules/_core.js").Promise,d(d.S+d.F*!P,"Promise",{reject:function(e){var t=S(this);return(0,t.reject)(e),t.promise}}),d(d.S+d.F*(l||!P),"Promise",{resolve:function(e){return E(l&&this===s?N:this,e)}}),d(d.S+d.F*!(P&&r("../node_modules/core-js/library/modules/_iter-detect.js")(function(e){N.all(e).catch(k)})),"Promise",{all:function(e){var t=this,r=S(t),o=r.resolve,n=r.reject,i=g(function(){var r=[],i=0,s=1;m(e,!1,function(e){var l=i++,u=!1;r.push(void 0),s++,t.resolve(e).then(function(e){u||(u=!0,r[l]=e,--s||o(r))},n)}),--s||o(r)});return i.e&&n(i.v),r.promise},race:function(e){var t=this,r=S(t),o=r.reject,n=g(function(){m(e,!1,function(e){t.resolve(e).then(r.resolve,o)})});return n.e&&o(n.v),r.promise}})},"../node_modules/core-js/library/modules/es6.string.iterator.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_string-at.js")(!0);r("../node_modules/core-js/library/modules/_iter-define.js")(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,r=this._i;return r>=t.length?{value:void 0,done:!0}:(e=o(t,r),this._i+=e.length,{value:e,done:!1})})},"../node_modules/core-js/library/modules/es6.symbol.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_global.js"),n=r("../node_modules/core-js/library/modules/_has.js"),i=r("../node_modules/core-js/library/modules/_descriptors.js"),s=r("../node_modules/core-js/library/modules/_export.js"),l=r("../node_modules/core-js/library/modules/_redefine.js"),u=r("../node_modules/core-js/library/modules/_meta.js").KEY,a=r("../node_modules/core-js/library/modules/_fails.js"),c=r("../node_modules/core-js/library/modules/_shared.js"),d=r("../node_modules/core-js/library/modules/_set-to-string-tag.js"),f=r("../node_modules/core-js/library/modules/_uid.js"),h=r("../node_modules/core-js/library/modules/_wks.js"),p=r("../node_modules/core-js/library/modules/_wks-ext.js"),m=r("../node_modules/core-js/library/modules/_wks-define.js"),y=r("../node_modules/core-js/library/modules/_enum-keys.js"),b=r("../node_modules/core-js/library/modules/_is-array.js"),_=r("../node_modules/core-js/library/modules/_an-object.js"),v=r("../node_modules/core-js/library/modules/_is-object.js"),g=r("../node_modules/core-js/library/modules/_to-object.js"),j=r("../node_modules/core-js/library/modules/_to-iobject.js"),E=r("../node_modules/core-js/library/modules/_to-primitive.js"),O=r("../node_modules/core-js/library/modules/_property-desc.js"),w=r("../node_modules/core-js/library/modules/_object-create.js"),x=r("../node_modules/core-js/library/modules/_object-gopn-ext.js"),A=r("../node_modules/core-js/library/modules/_object-gopd.js"),N=r("../node_modules/core-js/library/modules/_object-gops.js"),T=r("../node_modules/core-js/library/modules/_object-dp.js"),k=r("../node_modules/core-js/library/modules/_object-keys.js"),S=A.f,P=T.f,C=x.f,M=o.Symbol,L=o.JSON,I=L&&L.stringify,R=h("_hidden"),q=h("toPrimitive"),B={}.propertyIsEnumerable,D=c("symbol-registry"),U=c("symbols"),F=c("op-symbols"),H=Object.prototype,K="function"==typeof M&&!!N.f,Y=o.QObject,z=!Y||!Y.prototype||!Y.prototype.findChild,G=i&&a(function(){return 7!=w(P({},"a",{get:function(){return P(this,"a",{value:7}).a}})).a})?function(e,t,r){var o=S(H,t);o&&delete H[t],P(e,t,r),o&&e!==H&&P(H,t,o)}:P,W=function(e){var t=U[e]=w(M.prototype);return t._k=e,t},V=K&&"symbol"==typeof M.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof M},X=function(e,t,r){return e===H&&X(F,t,r),_(e),t=E(t,!0),_(r),n(U,t)?(r.enumerable?(n(e,R)&&e[R][t]&&(e[R][t]=!1),r=w(r,{enumerable:O(0,!1)})):(n(e,R)||P(e,R,O(1,{})),e[R][t]=!0),G(e,t,r)):P(e,t,r)},$=function(e,t){_(e);for(var r,o=y(t=j(t)),n=0,i=o.length;i>n;)X(e,r=o[n++],t[r]);return e},J=function(e,t){return void 0===t?w(e):$(w(e),t)},Z=function(e){var t=B.call(this,e=E(e,!0));return!(this===H&&n(U,e)&&!n(F,e))&&(!(t||!n(this,e)||!n(U,e)||n(this,R)&&this[R][e])||t)},Q=function(e,t){if(e=j(e),t=E(t,!0),e!==H||!n(U,t)||n(F,t)){var r=S(e,t);return!r||!n(U,t)||n(e,R)&&e[R][t]||(r.enumerable=!0),r}},ee=function(e){for(var t,r=C(j(e)),o=[],i=0;r.length>i;)n(U,t=r[i++])||t==R||t==u||o.push(t);return o},te=function(e){for(var t,r=e===H,o=C(r?F:j(e)),i=[],s=0;o.length>s;)!n(U,t=o[s++])||r&&!n(H,t)||i.push(U[t]);return i};K||(M=function(){if(this instanceof M)throw TypeError("Symbol is not a constructor!");var e=f(arguments.length>0?arguments[0]:void 0),t=function(r){this===H&&t.call(F,r),n(this,R)&&n(this[R],e)&&(this[R][e]=!1),G(this,e,O(1,r))};return i&&z&&G(H,e,{configurable:!0,set:t}),W(e)},l(M.prototype,"toString",function(){return this._k}),A.f=Q,T.f=X,r("../node_modules/core-js/library/modules/_object-gopn.js").f=x.f=ee,r("../node_modules/core-js/library/modules/_object-pie.js").f=Z,N.f=te,i&&!r("../node_modules/core-js/library/modules/_library.js")&&l(H,"propertyIsEnumerable",Z,!0),p.f=function(e){return W(h(e))}),s(s.G+s.W+s.F*!K,{Symbol:M});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),oe=0;re.length>oe;)h(re[oe++]);for(var ne=k(h.store),ie=0;ne.length>ie;)m(ne[ie++]);s(s.S+s.F*!K,"Symbol",{for:function(e){return n(D,e+="")?D[e]:D[e]=M(e)},keyFor:function(e){if(!V(e))throw TypeError(e+" is not a symbol!");for(var t in D)if(D[t]===e)return t},useSetter:function(){z=!0},useSimple:function(){z=!1}}),s(s.S+s.F*!K,"Object",{create:J,defineProperty:X,defineProperties:$,getOwnPropertyDescriptor:Q,getOwnPropertyNames:ee,getOwnPropertySymbols:te}),s(s.S+s.F*a(function(){N.f(1)}),"Object",{getOwnPropertySymbols:function(e){return N.f(g(e))}}),L&&s(s.S+s.F*(!K||a(function(){var e=M();return"[null]"!=I([e])||"{}"!=I({a:e})||"{}"!=I(Object(e))})),"JSON",{stringify:function(e){for(var t,r,o=[e],n=1;arguments.length>n;)o.push(arguments[n++]);if(r=t=o[1],(v(t)||void 0!==e)&&!V(e))return b(t)||(t=function(e,t){if("function"==typeof r&&(t=r.call(this,e,t)),!V(t))return t}),o[1]=t,I.apply(L,o)}}),M.prototype[q]||r("../node_modules/core-js/library/modules/_hide.js")(M.prototype,q,M.prototype.valueOf),d(M,"Symbol"),d(Math,"Math",!0),d(o.JSON,"JSON",!0)},"../node_modules/core-js/library/modules/es7.promise.finally.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_export.js"),n=r("../node_modules/core-js/library/modules/_core.js"),i=r("../node_modules/core-js/library/modules/_global.js"),s=r("../node_modules/core-js/library/modules/_species-constructor.js"),l=r("../node_modules/core-js/library/modules/_promise-resolve.js");o(o.P+o.R,"Promise",{finally:function(e){var t=s(this,n.Promise||i.Promise),r="function"==typeof e;return this.then(r?function(r){return l(t,e()).then(function(){return r})}:e,r?function(r){return l(t,e()).then(function(){throw r})}:e)}})},"../node_modules/core-js/library/modules/es7.promise.try.js":function(e,t,r){"use strict";var o=r("../node_modules/core-js/library/modules/_export.js"),n=r("../node_modules/core-js/library/modules/_new-promise-capability.js"),i=r("../node_modules/core-js/library/modules/_perform.js");o(o.S,"Promise",{try:function(e){var t=n.f(this),r=i(e);return(r.e?t.reject:t.resolve)(r.v),t.promise}})},"../node_modules/core-js/library/modules/es7.symbol.async-iterator.js":function(e,t,r){r("../node_modules/core-js/library/modules/_wks-define.js")("asyncIterator")},"../node_modules/core-js/library/modules/es7.symbol.observable.js":function(e,t,r){r("../node_modules/core-js/library/modules/_wks-define.js")("observable")},"../node_modules/core-js/library/modules/web.dom.iterable.js":function(e,t,r){r("../node_modules/core-js/library/modules/es6.array.iterator.js");for(var o=r("../node_modules/core-js/library/modules/_global.js"),n=r("../node_modules/core-js/library/modules/_hide.js"),i=r("../node_modules/core-js/library/modules/_iterators.js"),s=r("../node_modules/core-js/library/modules/_wks.js")("toStringTag"),l="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),u=0;u<l.length;u++){var a=l[u],c=o[a],d=c&&c.prototype;d&&!d[s]&&n(d,s,a),i[a]=i.Array}},"../node_modules/extend/index.js":function(e,t,r){"use strict";var o=Object.prototype.hasOwnProperty,n=Object.prototype.toString,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},u=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var t=o.call(e,"constructor"),r=e.constructor&&e.constructor.prototype&&o.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!t&&!r)return!1;var i;for(i in e);return void 0===i||o.call(e,i)},a=function(e,t){i&&"__proto__"===t.name?i(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},c=function(e,t){if("__proto__"===t){if(!o.call(e,t))return;if(s)return s(e,t).value}return e[t]};e.exports=function e(){var t,r,o,n,i,s,d=arguments[0],f=1,h=arguments.length,p=!1;for("boolean"==typeof d&&(p=d,d=arguments[1]||{},f=2),(null==d||"object"!=typeof d&&"function"!=typeof d)&&(d={});f<h;++f)if(null!=(t=arguments[f]))for(r in t)o=c(d,r),n=c(t,r),d!==n&&(p&&n&&(u(n)||(i=l(n)))?(i?(i=!1,s=o&&l(o)?o:[]):s=o&&u(o)?o:{},a(d,{name:r,newValue:e(p,s,n)})):void 0!==n&&a(d,{name:r,newValue:n}));return d}},"../node_modules/ieee754/index.js":function(e,t){t.read=function(e,t,r,o,n){var i,s,l=8*n-o-1,u=(1<<l)-1,a=u>>1,c=-7,d=r?n-1:0,f=r?-1:1,h=e[t+d];for(d+=f,i=h&(1<<-c)-1,h>>=-c,c+=l;c>0;i=256*i+e[t+d],d+=f,c-=8);for(s=i&(1<<-c)-1,i>>=-c,c+=o;c>0;s=256*s+e[t+d],d+=f,c-=8);if(0===i)i=1-a;else{if(i===u)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,o),i-=a}return(h?-1:1)*s*Math.pow(2,i-o)},t.write=function(e,t,r,o,n,i){var s,l,u,a=8*i-n-1,c=(1<<a)-1,d=c>>1,f=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,h=o?0:i-1,p=o?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(l=isNaN(t)?1:0,s=c):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+d>=1?f/u:f*Math.pow(2,1-d),t*u>=2&&(s++,u/=2),s+d>=c?(l=0,s=c):s+d>=1?(l=(t*u-1)*Math.pow(2,n),s+=d):(l=t*Math.pow(2,d-1)*Math.pow(2,n),s=0));n>=8;e[r+h]=255&l,h+=p,l/=256,n-=8);for(s=s<<n|l,a+=n;a>0;e[r+h]=255&s,h+=p,s/=256,a-=8);e[r+h-p]|=128*m}},"../node_modules/isarray/index.js":function(e,t){var r={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==r.call(e)}},"../node_modules/quill/dist/quill.core.css":function(e,t){},"../node_modules/quill/dist/quill.core.js":function(e,t,r){(function(t){!function(t,r){e.exports=r()}("undefined"!=typeof self&&self,function(){return function(e){function t(o){if(r[o])return r[o].exports;var n=r[o]={i:o,l:!1,exports:{}};return e[o].call(n.exports,n,n.exports,t),n.l=!0,n.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,o){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=110)}([function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=r(17),n=r(18),i=r(19),s=r(45),l=r(46),u=r(47),a=r(48),c=r(49),d=r(12),f=r(32),h=r(33),p=r(31),m=r(1);t.default={Scope:m.Scope,create:m.create,find:m.find,query:m.query,register:m.register,Container:o.default,Format:n.default,Leaf:i.default,Embed:a.default,Scroll:s.default,Block:u.default,Inline:l.default,Text:c.default,Attributor:{Attribute:d.default,Class:f.default,Style:h.default,Store:p.default}}},function(e,t,r){"use strict";function o(e,t){var r=i(e);if(null==r)throw new u("Unable to create "+e+" blot");var o=r;return new o(e instanceof Node||e.nodeType===Node.TEXT_NODE?e:o.create(t),t)}function n(e,r){return void 0===r&&(r=!1),null==e?null:null!=e[t.DATA_KEY]?e[t.DATA_KEY].blot:r?n(e.parentNode,r):null}function i(e,t){void 0===t&&(t=h.ANY);var r;if("string"==typeof e)r=f[e]||a[e];else if(e instanceof Text||e.nodeType===Node.TEXT_NODE)r=f.text;else if("number"==typeof e)e&h.LEVEL&h.BLOCK?r=f.block:e&h.LEVEL&h.INLINE&&(r=f.inline);else if(e instanceof HTMLElement){var o=(e.getAttribute("class")||"").split(/\s+/);for(var n in o)if(r=c[o[n]])break;r=r||d[e.tagName]}return null==r?null:t&h.LEVEL&r.scope&&t&h.TYPE&r.scope?r:null}function s(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];if(e.length>1)return e.map(function(e){return s(e)});var r=e[0];if("string"!=typeof r.blotName&&"string"!=typeof r.attrName)throw new u("Invalid definition");if("abstract"===r.blotName)throw new u("Cannot register abstract class");if(f[r.blotName||r.attrName]=r,"string"==typeof r.keyName)a[r.keyName]=r;else if(null!=r.className&&(c[r.className]=r),null!=r.tagName){r.tagName=Array.isArray(r.tagName)?r.tagName.map(function(e){return e.toUpperCase()}):r.tagName.toUpperCase();var o=Array.isArray(r.tagName)?r.tagName:[r.tagName];o.forEach(function(e){null!=d[e]&&null!=r.className||(d[e]=r)})}return r}var l=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var u=function(e){function t(t){var r=this;return t="[Parchment] "+t,r=e.call(this,t)||this,r.message=t,r.name=r.constructor.name,r}return l(t,e),t}(Error);t.ParchmentError=u;var a={},c={},d={},f={};t.DATA_KEY="__blot";var h;!function(e){e[e.TYPE=3]="TYPE",e[e.LEVEL=12]="LEVEL",e[e.ATTRIBUTE=13]="ATTRIBUTE",e[e.BLOT=14]="BLOT",e[e.INLINE=7]="INLINE",e[e.BLOCK=11]="BLOCK",e[e.BLOCK_BLOT=10]="BLOCK_BLOT",e[e.INLINE_BLOT=6]="INLINE_BLOT",e[e.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",e[e.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",e[e.ANY=15]="ANY"}(h=t.Scope||(t.Scope={})),t.create=o,t.find=n,t.query=i,t.register=s},function(e,t,r){var o=r(51),n=r(11),i=r(3),s=r(20),l=String.fromCharCode(0),u=function(e){this.ops=Array.isArray(e)?e:null!=e&&Array.isArray(e.ops)?e.ops:[]};u.prototype.insert=function(e,t){var r={};return 0===e.length?this:(r.insert=e,null!=t&&"object"==typeof t&&Object.keys(t).length>0&&(r.attributes=t),this.push(r))},u.prototype.delete=function(e){return e<=0?this:this.push({delete:e})},u.prototype.retain=function(e,t){if(e<=0)return this;var r={retain:e};return null!=t&&"object"==typeof t&&Object.keys(t).length>0&&(r.attributes=t),this.push(r)},u.prototype.push=function(e){var t=this.ops.length,r=this.ops[t-1];if(e=i(!0,{},e),"object"==typeof r){if("number"==typeof e.delete&&"number"==typeof r.delete)return this.ops[t-1]={delete:r.delete+e.delete},this;if("number"==typeof r.delete&&null!=e.insert&&(t-=1,"object"!=typeof(r=this.ops[t-1])))return this.ops.unshift(e),this;if(n(e.attributes,r.attributes)){if("string"==typeof e.insert&&"string"==typeof r.insert)return this.ops[t-1]={insert:r.insert+e.insert},"object"==typeof e.attributes&&(this.ops[t-1].attributes=e.attributes),this;if("number"==typeof e.retain&&"number"==typeof r.retain)return this.ops[t-1]={retain:r.retain+e.retain},"object"==typeof e.attributes&&(this.ops[t-1].attributes=e.attributes),this}}return t===this.ops.length?this.ops.push(e):this.ops.splice(t,0,e),this},u.prototype.chop=function(){var e=this.ops[this.ops.length-1];return e&&e.retain&&!e.attributes&&this.ops.pop(),this},u.prototype.filter=function(e){return this.ops.filter(e)},u.prototype.forEach=function(e){this.ops.forEach(e)},u.prototype.map=function(e){return this.ops.map(e)},u.prototype.partition=function(e){var t=[],r=[];return this.forEach(function(o){(e(o)?t:r).push(o)}),[t,r]},u.prototype.reduce=function(e,t){return this.ops.reduce(e,t)},u.prototype.changeLength=function(){return this.reduce(function(e,t){return t.insert?e+s.length(t):t.delete?e-t.delete:e},0)},u.prototype.length=function(){return this.reduce(function(e,t){return e+s.length(t)},0)},u.prototype.slice=function(e,t){e=e||0,"number"!=typeof t&&(t=1/0);for(var r=[],o=s.iterator(this.ops),n=0;n<t&&o.hasNext();){var i;n<e?i=o.next(e-n):(i=o.next(t-n),r.push(i)),n+=s.length(i)}return new u(r)},u.prototype.compose=function(e){for(var t=s.iterator(this.ops),r=s.iterator(e.ops),o=new u;t.hasNext()||r.hasNext();)if("insert"===r.peekType())o.push(r.next());else if("delete"===t.peekType())o.push(t.next());else{var n=Math.min(t.peekLength(),r.peekLength()),i=t.next(n),l=r.next(n);if("number"==typeof l.retain){var a={};"number"==typeof i.retain?a.retain=n:a.insert=i.insert;var c=s.attributes.compose(i.attributes,l.attributes,"number"==typeof i.retain);c&&(a.attributes=c),o.push(a)}else"number"==typeof l.delete&&"number"==typeof i.retain&&o.push(l)}return o.chop()},u.prototype.concat=function(e){var t=new u(this.ops.slice());return e.ops.length>0&&(t.push(e.ops[0]),t.ops=t.ops.concat(e.ops.slice(1))),t},u.prototype.diff=function(e,t){if(this.ops===e.ops)return new u;var r=[this,e].map(function(t){return t.map(function(r){if(null!=r.insert)return"string"==typeof r.insert?r.insert:l;var o=t===e?"on":"with";throw new Error("diff() called "+o+" non-document")}).join("")}),i=new u,a=o(r[0],r[1],t),c=s.iterator(this.ops),d=s.iterator(e.ops);return a.forEach(function(e){for(var t=e[1].length;t>0;){var r=0;switch(e[0]){case o.INSERT:r=Math.min(d.peekLength(),t),i.push(d.next(r));break;case o.DELETE:r=Math.min(t,c.peekLength()),c.next(r),i.delete(r);break;case o.EQUAL:r=Math.min(c.peekLength(),d.peekLength(),t);var l=c.next(r),u=d.next(r);n(l.insert,u.insert)?i.retain(r,s.attributes.diff(l.attributes,u.attributes)):i.push(u).delete(r)}t-=r}}),i.chop()},u.prototype.eachLine=function(e,t){t=t||"\n";for(var r=s.iterator(this.ops),o=new u,n=0;r.hasNext();){if("insert"!==r.peekType())return;var i=r.peek(),l=s.length(i)-r.peekLength(),a="string"==typeof i.insert?i.insert.indexOf(t,l)-l:-1;if(a<0)o.push(r.next());else if(a>0)o.push(r.next(a));else{if(!1===e(o,r.next(1).attributes||{},n))return;n+=1,o=new u}}o.length()>0&&e(o,{},n)},u.prototype.transform=function(e,t){if(t=!!t,"number"==typeof e)return this.transformPosition(e,t);for(var r=s.iterator(this.ops),o=s.iterator(e.ops),n=new u;r.hasNext()||o.hasNext();)if("insert"!==r.peekType()||!t&&"insert"===o.peekType())if("insert"===o.peekType())n.push(o.next());else{var i=Math.min(r.peekLength(),o.peekLength()),l=r.next(i),a=o.next(i);if(l.delete)continue;a.delete?n.push(a):n.retain(i,s.attributes.transform(l.attributes,a.attributes,t))}else n.retain(s.length(r.next()));return n.chop()},u.prototype.transformPosition=function(e,t){t=!!t;for(var r=s.iterator(this.ops),o=0;r.hasNext()&&o<=e;){var n=r.peekLength(),i=r.peekType();r.next(),"delete"!==i?("insert"===i&&(o<e||!t)&&(e+=n),o+=n):e-=Math.min(n,e-o)}return e},e.exports=u},function(e,t){"use strict";var r=Object.prototype.hasOwnProperty,o=Object.prototype.toString,n=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===o.call(e)},i=function(e){if(!e||"[object Object]"!==o.call(e))return!1;var t=r.call(e,"constructor"),n=e.constructor&&e.constructor.prototype&&r.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!t&&!n)return!1;var i;for(i in e);return void 0===i||r.call(e,i)};e.exports=function e(){var t,r,o,s,l,u,a=arguments[0],c=1,d=arguments.length,f=!1;for("boolean"==typeof a&&(f=a,a=arguments[1]||{},c=2),(null==a||"object"!=typeof a&&"function"!=typeof a)&&(a={});c<d;++c)if(null!=(t=arguments[c]))for(r in t)o=a[r],s=t[r],a!==s&&(f&&s&&(i(s)||(l=n(s)))?(l?(l=!1,u=o&&n(o)?o:[]):u=o&&i(o)?o:{},a[r]=e(f,u,s)):void 0!==s&&(a[r]=s));return a}},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return null==e?t:("function"==typeof e.formats&&(t=(0,d.default)(t,e.formats())),null==e.parent||"scroll"==e.parent.blotName||e.parent.statics.scope!==e.statics.scope?t:l(e.parent,t))}Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.BlockEmbed=t.bubbleFormats=void 0;var u=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),a=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},c=r(3),d=o(c),f=r(2),h=o(f),p=r(0),m=o(p),y=r(16),b=o(y),_=r(6),v=o(_),g=r(7),j=o(g),E=function(e){function t(){return n(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return s(t,e),u(t,[{key:"attach",value:function(){a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"attach",this).call(this),this.attributes=new m.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new h.default).insert(this.value(),(0,d.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(e,t){var r=m.default.query(e,m.default.Scope.BLOCK_ATTRIBUTE);null!=r&&this.attributes.attribute(r,t)}},{key:"formatAt",value:function(e,t,r,o){this.format(r,o)}},{key:"insertAt",value:function(e,r,o){if("string"==typeof r&&r.endsWith("\n")){var n=m.default.create(O.blotName);this.parent.insertBefore(n,0===e?this:this.next),n.insertAt(0,r.slice(0,-1))}else a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertAt",this).call(this,e,r,o)}}]),t}(m.default.Embed);E.scope=m.default.Scope.BLOCK_BLOT;var O=function(e){function t(e){n(this,t);var r=i(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return r.cache={},r}return s(t,e),u(t,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=this.descendants(m.default.Leaf).reduce(function(e,t){return 0===t.length()?e:e.insert(t.value(),l(t))},new h.default).insert("\n",l(this))),this.cache.delta}},{key:"deleteAt",value:function(e,r){a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"deleteAt",this).call(this,e,r),this.cache={}}},{key:"formatAt",value:function(e,r,o,n){r<=0||(m.default.query(o,m.default.Scope.BLOCK)?e+r===this.length()&&this.format(o,n):a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"formatAt",this).call(this,e,Math.min(r,this.length()-e-1),o,n),this.cache={})}},{key:"insertAt",value:function(e,r,o){if(null!=o)return a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertAt",this).call(this,e,r,o);if(0!==r.length){var n=r.split("\n"),i=n.shift();i.length>0&&(e<this.length()-1||null==this.children.tail?a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertAt",this).call(this,Math.min(e,this.length()-1),i):this.children.tail.insertAt(this.children.tail.length(),i),this.cache={});var s=this;n.reduce(function(e,t){return s=s.split(e,!0),s.insertAt(0,t),t.length},e+i.length)}}},{key:"insertBefore",value:function(e,r){var o=this.children.head;a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertBefore",this).call(this,e,r),o instanceof b.default&&o.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(e,r){a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"moveChildren",this).call(this,e,r),this.cache={}}},{key:"optimize",value:function(e){a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"optimize",this).call(this,e),this.cache={}}},{key:"path",value:function(e){return a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"path",this).call(this,e,!0)}},{key:"removeChild",value:function(e){a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"removeChild",this).call(this,e),this.cache={}}},{key:"split",value:function(e){var r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(r&&(0===e||e>=this.length()-1)){var o=this.clone();return 0===e?(this.parent.insertBefore(o,this),this):(this.parent.insertBefore(o,this.next),o)}var n=a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"split",this).call(this,e,r);return this.cache={},n}}]),t}(m.default.Block);O.blotName="block",O.tagName="P",O.defaultChild="break",O.allowedChildren=[v.default,m.default.Embed,j.default],t.bubbleFormats=l,t.BlockEmbed=E,t.default=O},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(t=(0,A.default)(!0,{container:e,modules:{clipboard:!0,keyboard:!0,history:!0}},t),t.theme&&t.theme!==C.DEFAULTS.theme){if(null==(t.theme=C.import("themes/"+t.theme)))throw new Error("Invalid theme "+t.theme+". Did you register it?")}else t.theme=S.default;var r=(0,A.default)(!0,{},t.theme.DEFAULTS);[r,t].forEach(function(e){e.modules=e.modules||{},Object.keys(e.modules).forEach(function(t){!0===e.modules[t]&&(e.modules[t]={})})});var o=Object.keys(r.modules).concat(Object.keys(t.modules)),n=o.reduce(function(e,t){var r=C.import("modules/"+t);return null==r?P.error("Cannot load "+t+" module. Are you sure you registered it?"):e[t]=r.DEFAULTS||{},e},{});return null!=t.modules&&t.modules.toolbar&&t.modules.toolbar.constructor!==Object&&(t.modules.toolbar={container:t.modules.toolbar}),t=(0,A.default)(!0,{},C.DEFAULTS,{modules:n},r,t),["bounds","container","scrollingContainer"].forEach(function(e){"string"==typeof t[e]&&(t[e]=document.querySelector(t[e]))}),t.modules=Object.keys(t.modules).reduce(function(e,r){return t.modules[r]&&(e[r]=t.modules[r]),e},{}),t}function l(e,t,r,o){if(this.options.strict&&!this.isEnabled()&&t===_.default.sources.USER)return new p.default;var n=null==r?null:this.getSelection(),i=this.editor.delta,s=e();if(null!=n&&(!0===r&&(r=n.index),null==o?n=a(n,s,t):0!==o&&(n=a(n,r,o,t)),this.setSelection(n,_.default.sources.SILENT)),s.length()>0){var l,u=[_.default.events.TEXT_CHANGE,s,i,t];if((l=this.emitter).emit.apply(l,[_.default.events.EDITOR_CHANGE].concat(u)),t!==_.default.sources.SILENT){var c;(c=this.emitter).emit.apply(c,u)}}return s}function u(e,t,r,o,n){var i={};return"number"==typeof e.index&&"number"==typeof e.length?"number"!=typeof t?(n=o,o=r,r=t,t=e.length,e=e.index):(t=e.length,e=e.index):"number"!=typeof t&&(n=o,o=r,r=t,t=0),"object"===(void 0===r?"undefined":c(r))?(i=r,n=o):"string"==typeof r&&(null!=o?i[r]=o:n=r),n=n||_.default.sources.API,[e,t,i,n]}function a(e,t,r,o){if(null==e)return null;var n=void 0,i=void 0;if(t instanceof p.default){var s=[e.index,e.index+e.length].map(function(e){return t.transformPosition(e,o!==_.default.sources.USER)}),l=d(s,2);n=l[0],i=l[1]}else{var u=[e.index,e.index+e.length].map(function(e){return e<t||e===t&&o===_.default.sources.USER?e:r>=0?e+r:Math.max(t,e+r)}),a=d(u,2);n=a[0],i=a[1]}return new O.Range(n,i-n)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.overload=t.expandConfig=void 0;var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},d=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),f=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}();r(50);var h=r(2),p=o(h),m=r(14),y=o(m),b=r(8),_=o(b),v=r(9),g=o(v),j=r(0),E=o(j),O=r(15),w=o(O),x=r(3),A=o(x),N=r(10),T=o(N),k=r(34),S=o(k),P=(0,T.default)("quill"),C=function(){function e(t){var r=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(i(this,e),this.options=s(t,o),null==(this.container=this.options.container))return P.error("Invalid Quill container",t);this.options.debug&&e.debug(this.options.debug);var n=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new _.default,this.scroll=E.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new y.default(this.scroll),this.selection=new w.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(_.default.events.EDITOR_CHANGE,function(e){e===_.default.events.TEXT_CHANGE&&r.root.classList.toggle("ql-blank",r.editor.isBlank())}),this.emitter.on(_.default.events.SCROLL_UPDATE,function(e,t){var o=r.selection.lastRange,n=o&&0===o.length?o.index:void 0;l.call(r,function(){return r.editor.update(null,t,n)},e)}),this.setContents(this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+n+"<p><br></p></div>")),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return f(e,null,[{key:"debug",value:function(e){!0===e&&(e="log"),T.default.level(e)}},{key:"find",value:function(e){return e.__quill||E.default.find(e)}},{key:"import",value:function(e){return null==this.imports[e]&&P.error("Cannot import "+e+". Are you sure it was registered?"),this.imports[e]}},{key:"register",value:function(e,t){var r=this,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!=typeof e){var n=e.attrName||e.blotName;"string"==typeof n?this.register("formats/"+n,e,t):Object.keys(e).forEach(function(o){r.register(o,e[o],t)})}else null==this.imports[e]||o||P.warn("Overwriting "+e+" with",t),this.imports[e]=t,(e.startsWith("blots/")||e.startsWith("formats/"))&&"abstract"!==t.blotName?E.default.register(t):e.startsWith("modules")&&"function"==typeof t.register&&t.register()}}]),f(e,[{key:"addContainer",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e){var r=e;e=document.createElement("div"),e.classList.add(r)}return this.container.insertBefore(e,t),e}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(e,t,r){var o=this,n=u(e,t,r),i=d(n,4);return e=i[0],t=i[1],r=i[3],l.call(this,function(){return o.editor.deleteText(e,t)},r,e,-1*t)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(e),this.container.classList.toggle("ql-disabled",!e)}},{key:"focus",value:function(){var e=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=e,this.scrollIntoView()}},{key:"format",value:function(e,t){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:_.default.sources.API;return l.call(this,function(){var o=r.getSelection(!0),i=new p.default;if(null==o)return i;if(E.default.query(e,E.default.Scope.BLOCK))i=r.editor.formatLine(o.index,o.length,n({},e,t));else{if(0===o.length)return r.selection.format(e,t),i;i=r.editor.formatText(o.index,o.length,n({},e,t))}return r.setSelection(o,_.default.sources.SILENT),i},o)}},{key:"formatLine",value:function(e,t,r,o,n){var i=this,s=void 0,a=u(e,t,r,o,n),c=d(a,4);return e=c[0],t=c[1],s=c[2],n=c[3],l.call(this,function(){return i.editor.formatLine(e,t,s)},n,e,0)}},{key:"formatText",value:function(e,t,r,o,n){var i=this,s=void 0,a=u(e,t,r,o,n),c=d(a,4);return e=c[0],t=c[1],s=c[2],n=c[3],l.call(this,function(){return i.editor.formatText(e,t,s)},n,e,0)}},{key:"getBounds",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=void 0;r="number"==typeof e?this.selection.getBounds(e,t):this.selection.getBounds(e.index,e.length);var o=this.container.getBoundingClientRect();return{bottom:r.bottom-o.top,height:r.height,left:r.left-o.left,right:r.right-o.left,top:r.top-o.top,width:r.width}}},{key:"getContents",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-e,r=u(e,t),o=d(r,2);return e=o[0],t=o[1],this.editor.getContents(e,t)}},{key:"getFormat",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"==typeof e?this.editor.getFormat(e,t):this.editor.getFormat(e.index,e.length)}},{key:"getIndex",value:function(e){return e.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(e){return this.scroll.leaf(e)}},{key:"getLine",value:function(e){return this.scroll.line(e)}},{key:"getLines",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof e?this.scroll.lines(e.index,e.length):this.scroll.lines(e,t)}},{key:"getModule",value:function(e){return this.theme.modules[e]}},{key:"getSelection",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-e,r=u(e,t),o=d(r,2);return e=o[0],t=o[1],this.editor.getText(e,t)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(t,r,o){var n=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.sources.API;return l.call(this,function(){return n.editor.insertEmbed(t,r,o)},i,t)}},{key:"insertText",value:function(e,t,r,o,n){var i=this,s=void 0,a=u(e,0,r,o,n),c=d(a,4);return e=c[0],s=c[2],n=c[3],l.call(this,function(){return i.editor.insertText(e,t,s)},n,e,t.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(e,t,r){this.clipboard.dangerouslyPasteHTML(e,t,r)}},{key:"removeFormat",value:function(e,t,r){var o=this,n=u(e,t,r),i=d(n,4);return e=i[0],t=i[1],r=i[3],l.call(this,function(){return o.editor.removeFormat(e,t)},r,e)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_.default.sources.API;return l.call(this,function(){e=new p.default(e);var r=t.getLength(),o=t.editor.deleteText(0,r),n=t.editor.applyDelta(e),i=n.ops[n.ops.length-1];return null!=i&&"string"==typeof i.insert&&"\n"===i.insert[i.insert.length-1]&&(t.editor.deleteText(t.getLength()-1,1),n.delete(1)),o.compose(n)},r)}},{key:"setSelection",value:function(t,r,o){if(null==t)this.selection.setRange(null,r||e.sources.API);else{var n=u(t,r,o),i=d(n,4);t=i[0],r=i[1],o=i[3],this.selection.setRange(new O.Range(t,r),o),o!==_.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_.default.sources.API;return this.setContents((new p.default).insert(e),t)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:_.default.sources.USER,t=this.scroll.update(e);return this.selection.update(e),t}},{key:"updateContents",value:function(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_.default.sources.API;return l.call(this,function(){return e=new p.default(e),t.editor.applyDelta(e,r)},r,!0)}}]),e}();C.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},C.events=_.default.events,C.sources=_.default.sources,C.version="1.3.6",C.imports={delta:p.default,parchment:E.default,"core/module":g.default,"core/theme":S.default},t.expandConfig=s,t.overload=u,t.default=C},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),u=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},a=r(7),c=o(a),d=r(0),f=o(d),h=function(e){function t(){return n(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return s(t,e),l(t,[{key:"formatAt",value:function(e,r,o,n){if(t.compare(this.statics.blotName,o)<0&&f.default.query(o,f.default.Scope.BLOT)){var i=this.isolate(e,r);n&&i.wrap(o,n)}else u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"formatAt",this).call(this,e,r,o,n)}},{key:"optimize",value:function(e){if(u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"optimize",this).call(this,e),this.parent instanceof t&&t.compare(this.statics.blotName,this.parent.statics.blotName)>0){var r=this.parent.isolate(this.offset(),this.length());this.moveChildren(r),r.wrap(this)}}}],[{key:"compare",value:function(e,r){var o=t.order.indexOf(e),n=t.order.indexOf(r);return o>=0||n>=0?o-n:e===r?0:e<r?-1:1}}]),t}(f.default.Inline);h.allowedChildren=[h,f.default.Embed,c.default],h.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],t.default=h},function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var s=r(0),l=function(e){return e&&e.__esModule?e:{default:e}}(s);t.default=function(e){function t(){return o(this,t),n(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return i(t,e),t}(l.default.Text)},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),u=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},a=r(54),c=o(a),d=r(10),f=o(d),h=(0,f.default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach(function(e){document.addEventListener(e,function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(e){if(e.__quill&&e.__quill.emitter){var r;(r=e.__quill.emitter).handleDOM.apply(r,t)}})})});var p=function(e){function t(){n(this,t);var e=i(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return e.listeners={},e.on("error",h.error),e}return s(t,e),l(t,[{key:"emit",value:function(){h.log.apply(h,arguments),u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),o=1;o<t;o++)r[o-1]=arguments[o];(this.listeners[e.type]||[]).forEach(function(t){var o=t.node,n=t.handler;(e.target===o||o.contains(e.target))&&n.apply(void 0,[e].concat(r))})}},{key:"listenDOM",value:function(e,t,r){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push({node:t,handler:r})}}]),t}(c.default);p.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},p.sources={API:"api",SILENT:"silent",USER:"user"},t.default=p},function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};o(this,e),this.quill=t,this.options=r};n.DEFAULTS={},t.default=n},function(e,t,r){"use strict";function o(e){if(i.indexOf(e)<=i.indexOf(s)){for(var t,r=arguments.length,o=Array(r>1?r-1:0),n=1;n<r;n++)o[n-1]=arguments[n];(t=console)[e].apply(t,o)}}function n(e){return i.reduce(function(t,r){return t[r]=o.bind(console,r,e),t},{})}Object.defineProperty(t,"__esModule",{value:!0});var i=["error","warn","log","info"],s="warn";o.level=n.level=function(e){s=e},t.default=n},function(e,t,r){function o(e){return null===e||void 0===e}function n(e){return!(!e||"object"!=typeof e||"number"!=typeof e.length)&&("function"==typeof e.copy&&"function"==typeof e.slice&&!(e.length>0&&"number"!=typeof e[0]))}function i(e,t,r){var i,c;if(o(e)||o(t))return!1;if(e.prototype!==t.prototype)return!1;if(u(e))return!!u(t)&&(e=s.call(e),t=s.call(t),a(e,t,r));if(n(e)){if(!n(t))return!1;if(e.length!==t.length)return!1;for(i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}try{var d=l(e),f=l(t)}catch(e){return!1}if(d.length!=f.length)return!1;for(d.sort(),f.sort(),i=d.length-1;i>=0;i--)if(d[i]!=f[i])return!1;for(i=d.length-1;i>=0;i--)if(c=d[i],!a(e[c],t[c],r))return!1;return typeof e==typeof t}var s=Array.prototype.slice,l=r(52),u=r(53),a=e.exports=function(e,t,r){return r||(r={}),e===t||(e instanceof Date&&t instanceof Date?e.getTime()===t.getTime():!e||!t||"object"!=typeof e&&"object"!=typeof t?r.strict?e===t:e==t:i(e,t,r))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=r(1);t.default=function(){function e(e,t,r){void 0===r&&(r={}),this.attrName=e,this.keyName=t;var n=o.Scope.TYPE&o.Scope.ATTRIBUTE;this.scope=null!=r.scope?r.scope&o.Scope.LEVEL|n:o.Scope.ATTRIBUTE,null!=r.whitelist&&(this.whitelist=r.whitelist)}return e.keys=function(e){return[].map.call(e.attributes,function(e){return e.name})},e.prototype.add=function(e,t){return!!this.canAdd(e,t)&&(e.setAttribute(this.keyName,t),!0)},e.prototype.canAdd=function(e,t){return null!=o.query(e,o.Scope.BLOT&(this.scope|o.Scope.TYPE))&&(null==this.whitelist||("string"==typeof t?this.whitelist.indexOf(t.replace(/["']/g,""))>-1:this.whitelist.indexOf(t)>-1))},e.prototype.remove=function(e){e.removeAttribute(this.keyName)},e.prototype.value=function(e){var t=e.getAttribute(this.keyName);return this.canAdd(e,t)&&t?t:""},e}()},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Code=void 0;var l=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),u=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),a=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},c=r(2),d=o(c),f=r(0),h=o(f),p=r(4),m=o(p),y=r(6),b=o(y),_=r(7),v=o(_),g=function(e){function t(){return n(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return s(t,e),t}(b.default);g.blotName="code",g.tagName="CODE";var j=function(e){function t(){return n(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return s(t,e),u(t,[{key:"delta",value:function(){var e=this,t=this.domNode.textContent;return t.endsWith("\n")&&(t=t.slice(0,-1)),t.split("\n").reduce(function(t,r){return t.insert(r).insert("\n",e.formats())},new d.default)}},{key:"format",value:function(e,r){if(e!==this.statics.blotName||!r){var o=this.descendant(v.default,this.length()-1),n=l(o,1),i=n[0];null!=i&&i.deleteAt(i.length()-1,1),a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"format",this).call(this,e,r)}}},{key:"formatAt",value:function(e,r,o,n){if(0!==r&&null!=h.default.query(o,h.default.Scope.BLOCK)&&(o!==this.statics.blotName||n!==this.statics.formats(this.domNode))){var i=this.newlineIndex(e);if(!(i<0||i>=e+r)){var s=this.newlineIndex(e,!0)+1,l=i-s+1,u=this.isolate(s,l),a=u.next;u.format(o,n),a instanceof t&&a.formatAt(0,e-s+r-l,o,n)}}}},{key:"insertAt",value:function(e,t,r){if(null==r){var o=this.descendant(v.default,e),n=l(o,2);n[0].insertAt(n[1],t)}}},{key:"length",value:function(){var e=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?e:e+1}},{key:"newlineIndex",value:function(e){if(arguments.length>1&&void 0!==arguments[1]&&arguments[1])return this.domNode.textContent.slice(0,e).lastIndexOf("\n");var t=this.domNode.textContent.slice(e).indexOf("\n");return t>-1?e+t:-1}},{key:"optimize",value:function(e){this.domNode.textContent.endsWith("\n")||this.appendChild(h.default.create("text","\n")),a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"optimize",this).call(this,e);var r=this.next;null!=r&&r.prev===this&&r.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===r.statics.formats(r.domNode)&&(r.optimize(e),r.moveChildren(this),r.remove())}},{key:"replace",value:function(e){a(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"replace",this).call(this,e),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(e){var t=h.default.find(e);null==t?e.parentNode.removeChild(e):t instanceof h.default.Embed?t.remove():t.unwrap()})}}],[{key:"create",value:function(e){var r=a(t.__proto__||Object.getPrototypeOf(t),"create",this).call(this,e);return r.setAttribute("spellcheck",!1),r}},{key:"formats",value:function(){return!0}}]),t}(m.default);j.blotName="code-block",j.tagName="PRE",j.TAB="  ",t.Code=g,t.default=j},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){return Object.keys(t).reduce(function(r,o){return null==e[o]?r:(t[o]===e[o]?r[o]=t[o]:Array.isArray(t[o])?t[o].indexOf(e[o])<0&&(r[o]=t[o].concat([e[o]])):r[o]=[t[o],e[o]],r)},{})}function l(e){return e.reduce(function(e,t){if(1===t.insert){var r=(0,A.default)(t.attributes);return delete r.image,e.insert({image:t.attributes.image},r)}if(null==t.attributes||!0!==t.attributes.list&&!0!==t.attributes.bullet||(t=(0,A.default)(t),t.attributes.list?t.attributes.list="ordered":(t.attributes.list="bullet",delete t.attributes.bullet)),"string"==typeof t.insert){return e.insert(t.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),t.attributes)}return e.push(t)},new f.default)}Object.defineProperty(t,"__esModule",{value:!0});var u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),c=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),d=r(2),f=o(d),h=r(20),p=o(h),m=r(0),y=o(m),b=r(13),_=o(b),v=r(24),g=o(v),j=r(4),E=o(j),O=r(16),w=o(O),x=r(21),A=o(x),N=r(11),T=o(N),k=r(3),S=o(k),P=/^[ -~]*$/;t.default=function(){function e(t){i(this,e),this.scroll=t,this.delta=this.getDelta()}return c(e,[{key:"applyDelta",value:function(e){var t=this,r=!1;this.scroll.update();var o=this.scroll.length();return this.scroll.batchStart(),e=l(e),e.reduce(function(e,n){var i=n.retain||n.delete||n.insert.length||1,s=n.attributes||{};if(null!=n.insert){if("string"==typeof n.insert){var l=n.insert;l.endsWith("\n")&&r&&(r=!1,l=l.slice(0,-1)),e>=o&&!l.endsWith("\n")&&(r=!0),t.scroll.insertAt(e,l);var c=t.scroll.line(e),d=a(c,2),f=d[0],h=d[1],m=(0,S.default)({},(0,j.bubbleFormats)(f));if(f instanceof E.default){var b=f.descendant(y.default.Leaf,h),_=a(b,1);m=(0,S.default)(m,(0,j.bubbleFormats)(_[0]))}s=p.default.attributes.diff(m,s)||{}}else if("object"===u(n.insert)){var v=Object.keys(n.insert)[0];if(null==v)return e;t.scroll.insertAt(e,v,n.insert[v])}o+=i}return Object.keys(s).forEach(function(r){t.scroll.formatAt(e,i,r,s[r])}),e+i},0),e.reduce(function(e,r){return"number"==typeof r.delete?(t.scroll.deleteAt(e,r.delete),e):e+(r.retain||r.insert.length||1)},0),this.scroll.batchEnd(),this.update(e)}},{key:"deleteText",value:function(e,t){return this.scroll.deleteAt(e,t),this.update((new f.default).retain(e).delete(t))}},{key:"formatLine",value:function(e,t){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.scroll.update(),Object.keys(o).forEach(function(n){if(null==r.scroll.whitelist||r.scroll.whitelist[n]){var i=r.scroll.lines(e,Math.max(t,1)),s=t;i.forEach(function(t){var i=t.length();if(t instanceof _.default){var l=e-t.offset(r.scroll);t.formatAt(l,t.newlineIndex(l+s)-l+1,n,o[n])}else t.format(n,o[n]);s-=i})}}),this.scroll.optimize(),this.update((new f.default).retain(e).retain(t,(0,A.default)(o)))}},{key:"formatText",value:function(e,t){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Object.keys(o).forEach(function(n){r.scroll.formatAt(e,t,n,o[n])}),this.update((new f.default).retain(e).retain(t,(0,A.default)(o)))}},{key:"getContents",value:function(e,t){return this.delta.slice(e,e+t)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(e,t){return e.concat(t.delta())},new f.default)}},{key:"getFormat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[],o=[];0===t?this.scroll.path(e).forEach(function(e){var t=a(e,1),n=t[0];n instanceof E.default?r.push(n):n instanceof y.default.Leaf&&o.push(n)}):(r=this.scroll.lines(e,t),o=this.scroll.descendants(y.default.Leaf,e,t));var n=[r,o].map(function(e){if(0===e.length)return{};for(var t=(0,j.bubbleFormats)(e.shift());Object.keys(t).length>0;){var r=e.shift();if(null==r)return t;t=s((0,j.bubbleFormats)(r),t)}return t});return S.default.apply(S.default,n)}},{key:"getText",value:function(e,t){return this.getContents(e,t).filter(function(e){return"string"==typeof e.insert}).map(function(e){return e.insert}).join("")}},{key:"insertEmbed",value:function(e,t,r){return this.scroll.insertAt(e,t,r),this.update((new f.default).retain(e).insert(n({},t,r)))}},{key:"insertText",value:function(e,t){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t=t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(e,t),Object.keys(o).forEach(function(n){r.scroll.formatAt(e,t.length,n,o[n])}),this.update((new f.default).retain(e).insert(t,(0,A.default)(o)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var e=this.scroll.children.head;return e.statics.blotName===E.default.blotName&&(!(e.children.length>1)&&e.children.head instanceof w.default)}},{key:"removeFormat",value:function(e,t){var r=this.getText(e,t),o=this.scroll.line(e+t),n=a(o,2),i=n[0],s=n[1],l=0,u=new f.default;null!=i&&(l=i instanceof _.default?i.newlineIndex(s)-s+1:i.length()-s,u=i.delta().slice(s,s+l-1).insert("\n"));var c=this.getContents(e,t+l),d=c.diff((new f.default).insert(r).concat(u));return this.applyDelta((new f.default).retain(e).concat(d))}},{key:"update",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,o=this.delta;if(1===t.length&&"characterData"===t[0].type&&t[0].target.data.match(P)&&y.default.find(t[0].target)){var n=y.default.find(t[0].target),i=(0,j.bubbleFormats)(n),s=n.offset(this.scroll),l=t[0].oldValue.replace(g.default.CONTENTS,""),u=(new f.default).insert(l),a=(new f.default).insert(n.value());e=(new f.default).retain(s).concat(u.diff(a,r)).reduce(function(e,t){return t.insert?e.insert(t.insert,i):e.push(t)},new f.default),this.delta=o.compose(e)}else this.delta=this.getDelta(),e&&(0,T.default)(o.compose(e),this.delta)||(e=o.diff(this.delta,r));return e}}]),e}()},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e){if(Array.isArray(e)){for(var t=0,r=Array(e.length);t<e.length;t++)r[t]=e[t];return r}return Array.from(e)}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){return t instanceof Text&&(t=t.parentNode),e.contains(t)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.Range=void 0;var l=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),u=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),a=r(0),c=o(a),d=r(21),f=o(d),h=r(11),p=o(h),m=r(8),y=o(m),b=r(10),_=o(b),v=(0,_.default)("quill:selection"),g=function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;i(this,e),this.index=t,this.length=r},j=function(){function e(t,r){var o=this;i(this,e),this.emitter=r,this.scroll=t,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=c.default.create("cursor",this),this.lastRange=this.savedRange=new g(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){o.mouseDown||setTimeout(o.update.bind(o,y.default.sources.USER),1)}),this.emitter.on(y.default.events.EDITOR_CHANGE,function(e,t){e===y.default.events.TEXT_CHANGE&&t.length()>0&&o.update(y.default.sources.SILENT)}),this.emitter.on(y.default.events.SCROLL_BEFORE_UPDATE,function(){if(o.hasFocus()){var e=o.getNativeRange();null!=e&&e.start.node!==o.cursor.textNode&&o.emitter.once(y.default.events.SCROLL_UPDATE,function(){})}}),this.emitter.on(y.default.events.SCROLL_OPTIMIZE,function(e,t){if(t.range){var r=t.range;o.setNativeRange(r.startNode,r.startOffset,r.endNode,r.endOffset)}}),this.update(y.default.sources.SILENT)}return u(e,[{key:"handleComposition",value:function(){var e=this;this.root.addEventListener("compositionstart",function(){e.composing=!0}),this.root.addEventListener("compositionend",function(){if(e.composing=!1,e.cursor.parent){var t=e.cursor.restore();if(!t)return;setTimeout(function(){e.setNativeRange(t.startNode,t.startOffset,t.endNode,t.endOffset)},1)}})}},{key:"handleDragging",value:function(){var e=this;this.emitter.listenDOM("mousedown",document.body,function(t){e.mouseDown=!0,setTimeout(function(){var e=document.getSelection();if(!(null==e||e.rangeCount<=0)){var r=e.getRangeAt(0),o="";if(r&&r.startContainer&&(r.startContainer.lastChild||r.startContainer.nextSibling)&&!(o=r.startContainer.lastChild)&&r.startContainer.parentNode&&r.startContainer.parentNode.lastChild&&(o=r.startContainer.parentNode.lastChild),o&&"IMG"==o.nodeName&&o.offsetLeft+o.clientWidth<t.x&&o.offsetTop+o.clientHeight<t.y){var n=document.createRange();n.selectNodeContents(t.target),n.collapse(!1),e.removeAllRanges(),e.addRange(n)}}},100)}),this.emitter.listenDOM("mouseup",document.body,function(){e.mouseDown=!1,e.update(y.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(e,t){if(null==this.scroll.whitelist||this.scroll.whitelist[e]){this.scroll.update();var r=this.getNativeRange();if(null!=r&&r.native.collapsed&&!c.default.query(e,c.default.Scope.BLOCK)){if(r.start.node!==this.cursor.textNode){var o=c.default.find(r.start.node,!1);if(null==o)return;if(o instanceof c.default.Leaf){o.parent.insertBefore(this.cursor,o.split(r.start.offset))}else o.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(e,t),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=this.scroll.length();e=Math.min(e,r-1),t=Math.min(e+t,r-1)-e;var o=void 0,n=this.scroll.leaf(e),i=l(n,2),s=i[0],u=i[1];if(null==s)return null;var a=s.position(u,!0),c=l(a,2);o=c[0],u=c[1];var d=document.createRange();if(t>0){d.setStart(o,u);var f=this.scroll.leaf(e+t),h=l(f,2);if(s=h[0],u=h[1],null==s)return null;var p=s.position(u,!0),m=l(p,2);return o=m[0],u=m[1],d.setEnd(o,u),d.getBoundingClientRect()}var y="left",b=void 0;return o instanceof Text?(u<o.data.length?(d.setStart(o,u),d.setEnd(o,u+1)):(d.setStart(o,u-1),d.setEnd(o,u),y="right"),b=d.getBoundingClientRect()):(b=s.domNode.getBoundingClientRect(),u>0&&(y="right")),{bottom:b.top+b.height,height:b.height,left:b[y],right:b[y],top:b.top,width:0}}},{key:"getNativeRange",value:function(){var e=document.getSelection();if(null==e||e.rangeCount<=0)return null;var t=e.getRangeAt(0);if(null==t)return null;var r=this.normalizeNative(t);return v.info("getNativeRange",r),r}},{key:"getRange",value:function(){var e=this.getNativeRange();return null==e?[null,null]:[this.normalizedToRange(e),e]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(e){var t=this,r=[[e.start.node,e.start.offset]];e.native.collapsed||r.push([e.end.node,e.end.offset]);var o=r.map(function(e){var r=l(e,2),o=r[0],n=r[1],i=c.default.find(o,!0),s=i.offset(t.scroll);return 0===n?s:i instanceof c.default.Container?s+i.length():s+i.index(o,n)}),i=Math.min(Math.max.apply(Math,n(o)),this.scroll.length()-1),s=Math.min.apply(Math,[i].concat(n(o)));return new g(s,i-s)}},{key:"normalizeNative",value:function(e){if(!s(this.root,e.startContainer)||!e.collapsed&&!s(this.root,e.endContainer))return null;var t={start:{node:e.startContainer,offset:e.startOffset},end:{node:e.endContainer,offset:e.endOffset},native:e};return[t.start,t.end].forEach(function(e){for(var t=e.node,r=e.offset;!(t instanceof Text)&&t.childNodes.length>0;)if(t.childNodes.length>r)t=t.childNodes[r],r=0;else{if(t.childNodes.length!==r)break;t=t.lastChild,r=t instanceof Text?t.data.length:t.childNodes.length+1}e.node=t,e.offset=r}),t}},{key:"rangeToNative",value:function(e){var t=this,r=e.collapsed?[e.index]:[e.index,e.index+e.length],o=[],n=this.scroll.length();return r.forEach(function(e,r){e=Math.min(n-1,e);var i=void 0,s=t.scroll.leaf(e),u=l(s,2),a=u[0],c=u[1],d=a.position(c,0!==r),f=l(d,2);i=f[0],c=f[1],o.push(i,c)}),o.length<2&&(o=o.concat(o)),o}},{key:"scrollIntoView",value:function(e){var t=this.lastRange;if(null!=t){var r=this.getBounds(t.index,t.length);if(null!=r){var o=this.scroll.length()-1,n=this.scroll.line(Math.min(t.index,o)),i=l(n,1),s=i[0],u=s;if(t.length>0){var a=this.scroll.line(Math.min(t.index+t.length,o));u=l(a,1)[0]}if(null!=s&&null!=u){var c=e.getBoundingClientRect();r.top<c.top?e.scrollTop-=c.top-r.top:r.bottom>c.bottom&&(e.scrollTop+=r.bottom-c.bottom)}}}}},{key:"setNativeRange",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t,n=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(v.info("setNativeRange",e,t,r,o),null==e||null!=this.root.parentNode&&null!=e.parentNode&&null!=r.parentNode){var i=document.getSelection();if(null!=i)if(null!=e){this.hasFocus()||this.root.focus();var s=(this.getNativeRange()||{}).native;if(null==s||n||e!==s.startContainer||t!==s.startOffset||r!==s.endContainer||o!==s.endOffset){"BR"==e.tagName&&(t=[].indexOf.call(e.parentNode.childNodes,e),e=e.parentNode),"BR"==r.tagName&&(o=[].indexOf.call(r.parentNode.childNodes,r),r=r.parentNode);var l=document.createRange();l.setStart(e,t),l.setEnd(r,o),i.removeAllRanges(),i.addRange(l)}}else i.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:y.default.sources.API;if("string"==typeof t&&(r=t,t=!1),v.info("setRange",e),null!=e){this.setNativeRange.apply(this,n(this.rangeToNative(e)).concat([t]))}else this.setNativeRange(null);this.update(r)}},{key:"update",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:y.default.sources.USER,t=this.lastRange,r=this.getRange(),o=l(r,2),n=o[0],i=o[1];if(this.lastRange=n,null!=this.lastRange&&(this.savedRange=this.lastRange),!(0,p.default)(t,this.lastRange)){var s;!this.composing&&null!=i&&i.native.collapsed&&i.start.node!==this.cursor.textNode&&this.cursor.restore();var u=[y.default.events.SELECTION_CHANGE,(0,f.default)(this.lastRange),(0,f.default)(t),e];if((s=this.emitter).emit.apply(s,[y.default.events.EDITOR_CHANGE].concat(u)),e!==y.default.sources.SILENT){var a;(a=this.emitter).emit.apply(a,u)}}}}]),e}();t.Range=g,t.default=j},function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var s=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),l=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},u=r(0),a=function(e){return e&&e.__esModule?e:{default:e}}(u),c=function(e){function t(){return o(this,t),n(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return i(t,e),s(t,[{key:"insertInto",value:function(e,r){0===e.children.length?l(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertInto",this).call(this,e,r):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),t}(a.default.Embed);c.blotName="break",c.tagName="BR",t.default=c},function(e,t,r){"use strict";function o(e){var t=l.find(e);if(null==t)try{t=l.create(e)}catch(r){t=l.create(l.Scope.INLINE),[].slice.call(e.childNodes).forEach(function(e){t.domNode.appendChild(e)}),e.parentNode&&e.parentNode.replaceChild(t.domNode,e),t.attach()}return t}var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=r(44),s=r(30),l=r(1);t.default=function(e){function t(t){var r=e.call(this,t)||this;return r.build(),r}return n(t,e),t.prototype.appendChild=function(e){this.insertBefore(e)},t.prototype.attach=function(){e.prototype.attach.call(this),this.children.forEach(function(e){e.attach()})},t.prototype.build=function(){var e=this;this.children=new i.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(t){try{var r=o(t);e.insertBefore(r,e.children.head||void 0)}catch(e){if(e instanceof l.ParchmentError)return;throw e}})},t.prototype.deleteAt=function(e,t){if(0===e&&t===this.length())return this.remove();this.children.forEachAt(e,t,function(e,t,r){e.deleteAt(t,r)})},t.prototype.descendant=function(e,r){var o=this.children.find(r),n=o[0],i=o[1];return null==e.blotName&&e(n)||null!=e.blotName&&n instanceof e?[n,i]:n instanceof t?n.descendant(e,i):[null,-1]},t.prototype.descendants=function(e,r,o){void 0===r&&(r=0),void 0===o&&(o=Number.MAX_VALUE);var n=[],i=o;return this.children.forEachAt(r,o,function(r,o,s){(null==e.blotName&&e(r)||null!=e.blotName&&r instanceof e)&&n.push(r),r instanceof t&&(n=n.concat(r.descendants(e,o,i))),i-=s}),n},t.prototype.detach=function(){this.children.forEach(function(e){e.detach()}),e.prototype.detach.call(this)},t.prototype.formatAt=function(e,t,r,o){this.children.forEachAt(e,t,function(e,t,n){e.formatAt(t,n,r,o)})},t.prototype.insertAt=function(e,t,r){var o=this.children.find(e),n=o[0],i=o[1];if(n)n.insertAt(i,t,r);else{this.appendChild(null==r?l.create("text",t):l.create(t,r))}},t.prototype.insertBefore=function(e,t){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some(function(t){return e instanceof t}))throw new l.ParchmentError("Cannot insert "+e.statics.blotName+" into "+this.statics.blotName);e.insertInto(this,t)},t.prototype.length=function(){return this.children.reduce(function(e,t){return e+t.length()},0)},t.prototype.moveChildren=function(e,t){this.children.forEach(function(r){e.insertBefore(r,t)})},t.prototype.optimize=function(t){if(e.prototype.optimize.call(this,t),0===this.children.length)if(null!=this.statics.defaultChild){var r=l.create(this.statics.defaultChild);this.appendChild(r),r.optimize(t)}else this.remove()},t.prototype.path=function(e,r){void 0===r&&(r=!1);var o=this.children.find(e,r),n=o[0],i=o[1],s=[[this,e]];return n instanceof t?s.concat(n.path(i,r)):(null!=n&&s.push([n,i]),s)},t.prototype.removeChild=function(e){this.children.remove(e)},t.prototype.replace=function(r){r instanceof t&&r.moveChildren(this),e.prototype.replace.call(this,r)},t.prototype.split=function(e,t){if(void 0===t&&(t=!1),!t){if(0===e)return this;if(e===this.length())return this.next}var r=this.clone();return this.parent.insertBefore(r,this.next),this.children.forEachAt(e,this.length(),function(e,o,n){e=e.split(o,t),r.appendChild(e)}),r},t.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},t.prototype.update=function(e,t){var r=this,n=[],i=[];e.forEach(function(e){e.target===r.domNode&&"childList"===e.type&&(n.push.apply(n,e.addedNodes),i.push.apply(i,e.removedNodes))}),i.forEach(function(e){if(!(null!=e.parentNode&&"IFRAME"!==e.tagName&&document.body.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var t=l.find(e);null!=t&&(null!=t.domNode.parentNode&&t.domNode.parentNode!==r.domNode||t.detach())}}),n.filter(function(e){return e.parentNode==r.domNode}).sort(function(e,t){return e===t?0:e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(e){var t=null;null!=e.nextSibling&&(t=l.find(e.nextSibling));var n=o(e);n.next==t&&null!=n.next||(null!=n.parent&&n.parent.removeChild(r),r.insertBefore(n,t||void 0))})},t}(s.default)},function(e,t,r){"use strict";var o=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var n=r(12),i=r(31),s=r(17),l=r(1);t.default=function(e){function t(t){var r=e.call(this,t)||this;return r.attributes=new i.default(r.domNode),r}return o(t,e),t.formats=function(e){return"string"==typeof this.tagName||(Array.isArray(this.tagName)?e.tagName.toLowerCase():void 0)},t.prototype.format=function(e,t){var r=l.query(e);r instanceof n.default?this.attributes.attribute(r,t):t&&(null==r||e===this.statics.blotName&&this.formats()[e]===t||this.replaceWith(e,t))},t.prototype.formats=function(){var e=this.attributes.values(),t=this.statics.formats(this.domNode);return null!=t&&(e[this.statics.blotName]=t),e},t.prototype.replaceWith=function(t,r){var o=e.prototype.replaceWith.call(this,t,r);return this.attributes.copy(o),o},t.prototype.update=function(t,r){var o=this;e.prototype.update.call(this,t,r),t.some(function(e){return e.target===o.domNode&&"attributes"===e.type})&&this.attributes.build()},t.prototype.wrap=function(r,o){var n=e.prototype.wrap.call(this,r,o);return n instanceof t&&n.statics.scope===this.statics.scope&&this.attributes.move(n),n},t}(s.default)},function(e,t,r){"use strict";var o=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var n=r(30),i=r(1);t.default=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.value=function(e){return!0},t.prototype.index=function(e,t){return this.domNode===e||this.domNode.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(t,1):-1},t.prototype.position=function(e,t){var r=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return e>0&&(r+=1),[this.parent.domNode,r]},t.prototype.value=function(){return e={},e[this.statics.blotName]=this.statics.value(this.domNode)||!0,e;var e},t.scope=i.Scope.INLINE_BLOT,t}(n.default)},function(e,t,r){function o(e){this.ops=e,this.index=0,this.offset=0}var n=r(11),i=r(3),s={attributes:{compose:function(e,t,r){"object"!=typeof e&&(e={}),"object"!=typeof t&&(t={});var o=i(!0,{},t);r||(o=Object.keys(o).reduce(function(e,t){return null!=o[t]&&(e[t]=o[t]),e},{}));for(var n in e)void 0!==e[n]&&void 0===t[n]&&(o[n]=e[n]);return Object.keys(o).length>0?o:void 0},diff:function(e,t){"object"!=typeof e&&(e={}),"object"!=typeof t&&(t={});var r=Object.keys(e).concat(Object.keys(t)).reduce(function(r,o){return n(e[o],t[o])||(r[o]=void 0===t[o]?null:t[o]),r},{});return Object.keys(r).length>0?r:void 0},transform:function(e,t,r){if("object"!=typeof e)return t;if("object"==typeof t){if(!r)return t;var o=Object.keys(t).reduce(function(r,o){return void 0===e[o]&&(r[o]=t[o]),r},{});return Object.keys(o).length>0?o:void 0}}},iterator:function(e){return new o(e)},length:function(e){return"number"==typeof e.delete?e.delete:"number"==typeof e.retain?e.retain:"string"==typeof e.insert?e.insert.length:1}};o.prototype.hasNext=function(){return this.peekLength()<1/0},o.prototype.next=function(e){e||(e=1/0);var t=this.ops[this.index];if(t){var r=this.offset,o=s.length(t);if(e>=o-r?(e=o-r,this.index+=1,this.offset=0):this.offset+=e,"number"==typeof t.delete)return{delete:e};var n={};return t.attributes&&(n.attributes=t.attributes),"number"==typeof t.retain?n.retain=e:n.insert="string"==typeof t.insert?t.insert.substr(r,e):t.insert,n}return{retain:1/0}},o.prototype.peek=function(){return this.ops[this.index]},o.prototype.peekLength=function(){return this.ops[this.index]?s.length(this.ops[this.index])-this.offset:1/0},o.prototype.peekType=function(){return this.ops[this.index]?"number"==typeof this.ops[this.index].delete?"delete":"number"==typeof this.ops[this.index].retain?"retain":"insert":"retain"},e.exports=s},function(e,r){var o=function(){"use strict";function e(e,t){return null!=t&&e instanceof t}function r(o,n,i,s,d){function f(o,i){if(null===o)return null;if(0===i)return o;var y,b;if("object"!=typeof o)return o;if(e(o,u))y=new u;else if(e(o,a))y=new a;else if(e(o,c))y=new c(function(e,t){o.then(function(t){e(f(t,i-1))},function(e){t(f(e,i-1))})});else if(r.__isArray(o))y=[];else if(r.__isRegExp(o))y=new RegExp(o.source,l(o)),o.lastIndex&&(y.lastIndex=o.lastIndex);else if(r.__isDate(o))y=new Date(o.getTime());else{if(m&&t.isBuffer(o))return y=new t(o.length),o.copy(y),y;e(o,Error)?y=Object.create(o):void 0===s?(b=Object.getPrototypeOf(o),y=Object.create(b)):(y=Object.create(s),b=s)}if(n){var _=h.indexOf(o);if(-1!=_)return p[_];h.push(o),p.push(y)}e(o,u)&&o.forEach(function(e,t){var r=f(t,i-1),o=f(e,i-1);y.set(r,o)}),e(o,a)&&o.forEach(function(e){var t=f(e,i-1);y.add(t)});for(var v in o){var g;b&&(g=Object.getOwnPropertyDescriptor(b,v)),g&&null==g.set||(y[v]=f(o[v],i-1))}if(Object.getOwnPropertySymbols)for(var j=Object.getOwnPropertySymbols(o),v=0;v<j.length;v++){var E=j[v],O=Object.getOwnPropertyDescriptor(o,E);(!O||O.enumerable||d)&&(y[E]=f(o[E],i-1),O.enumerable||Object.defineProperty(y,E,{enumerable:!1}))}if(d)for(var w=Object.getOwnPropertyNames(o),v=0;v<w.length;v++){var x=w[v],O=Object.getOwnPropertyDescriptor(o,x);O&&O.enumerable||(y[x]=f(o[x],i-1),Object.defineProperty(y,x,{enumerable:!1}))}return y}"object"==typeof n&&(i=n.depth,s=n.prototype,d=n.includeNonEnumerable,n=n.circular);var h=[],p=[],m=void 0!==t;return void 0===n&&(n=!0),void 0===i&&(i=1/0),f(o,i)}function o(e){return Object.prototype.toString.call(e)}function n(e){return"object"==typeof e&&"[object Date]"===o(e)}function i(e){return"object"==typeof e&&"[object Array]"===o(e)}function s(e){return"object"==typeof e&&"[object RegExp]"===o(e)}function l(e){var t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),t}var u;try{u=Map}catch(e){u=function(){}}var a;try{a=Set}catch(e){a=function(){}}var c;try{c=Promise}catch(e){c=function(){}}return r.clonePrototype=function(e){if(null===e)return null;var t=function(){};return t.prototype=e,new t},r.__objToStr=o,r.__isDate=n,r.__isArray=i,r.__isRegExp=s,r.__getRegExpFlags=l,r}();"object"==typeof e&&e.exports&&(e.exports=o)},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function l(e){return e instanceof y.default||e instanceof m.BlockEmbed}Object.defineProperty(t,"__esModule",{value:!0});var u=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),c=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},d=r(0),f=o(d),h=r(8),p=o(h),m=r(4),y=o(m),b=r(16),_=o(b),v=r(13),g=o(v),j=r(25),E=o(j),O=function(e){function t(e,r){n(this,t);var o=i(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return o.emitter=r.emitter,Array.isArray(r.whitelist)&&(o.whitelist=r.whitelist.reduce(function(e,t){return e[t]=!0,e},{})),o.domNode.addEventListener("DOMAttrModified",function(){}),o.optimize(),o.enable(),o}return s(t,e),a(t,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(e,r){var o=this.line(e),n=u(o,2),i=n[0],s=n[1],l=this.line(e+r),a=u(l,1),d=a[0];if(c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"deleteAt",this).call(this,e,r),null!=d&&i!==d&&s>0){if(i instanceof m.BlockEmbed||d instanceof m.BlockEmbed)return void this.optimize();if(i instanceof g.default){var f=i.newlineIndex(i.length(),!0);if(f>-1&&(i=i.split(f+1))===d)return void this.optimize()}else if(d instanceof g.default){var h=d.newlineIndex(0);h>-1&&d.split(h+1)}i.moveChildren(d,d.children.head instanceof _.default?null:d.children.head),i.remove()}this.optimize()}},{key:"enable",value:function(){this.domNode.setAttribute("contenteditable",!(arguments.length>0&&void 0!==arguments[0])||arguments[0])}},{key:"formatAt",value:function(e,r,o,n){(null==this.whitelist||this.whitelist[o])&&(c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"formatAt",this).call(this,e,r,o,n),this.optimize())}},{key:"insertAt",value:function(e,r,o){if(null==o||null==this.whitelist||this.whitelist[r]){if(e>=this.length())if(null==o||null==f.default.query(r,f.default.Scope.BLOCK)){var n=f.default.create(this.statics.defaultChild);this.appendChild(n),null==o&&r.endsWith("\n")&&(r=r.slice(0,-1)),n.insertAt(0,r,o)}else{var i=f.default.create(r,o);this.appendChild(i)}else c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertAt",this).call(this,e,r,o);this.optimize()}}},{key:"insertBefore",value:function(e,r){if(e.statics.scope===f.default.Scope.INLINE_BLOT){var o=f.default.create(this.statics.defaultChild);o.appendChild(e),e=o}c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"insertBefore",this).call(this,e,r)}},{key:"leaf",value:function(e){return this.path(e).pop()||[null,-1]}},{key:"line",value:function(e){return e===this.length()?this.line(e-1):this.descendant(l,e)}},{key:"lines",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return function e(t,r,o){var n=[],i=o;return t.children.forEachAt(r,o,function(t,r,o){l(t)?n.push(t):t instanceof f.default.Container&&(n=n.concat(e(t,r,i))),i-=o}),n}(this,e,t)}},{key:"optimize",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"optimize",this).call(this,e,r),e.length>0&&this.emitter.emit(p.default.events.SCROLL_OPTIMIZE,e,r))}},{key:"path",value:function(e){return c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"path",this).call(this,e).slice(1)}},{key:"update",value:function(e){if(!0!==this.batch){var r=p.default.sources.USER;"string"==typeof e&&(r=e),Array.isArray(e)||(e=this.observer.takeRecords()),e.length>0&&this.emitter.emit(p.default.events.SCROLL_BEFORE_UPDATE,r,e),c(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"update",this).call(this,e.concat([])),e.length>0&&this.emitter.emit(p.default.events.SCROLL_UPDATE,r,e)}}}]),t}(f.default.Scroll);O.blotName="scroll",O.className="ql-editor",O.tagName="DIV",O.defaultChild="block",O.allowedChildren=[y.default,m.BlockEmbed,E.default],t.default=O},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function u(e,t){var r,o=e===D.keys.LEFT?"prefix":"suffix";return r={key:e,shiftKey:t,altKey:null},n(r,o,/^$/),n(r,"handler",function(r){var o=r.index;e===D.keys.RIGHT&&(o+=r.length+1);var n=this.quill.getLeaf(o);return!(b(n,1)[0]instanceof S.default.Embed)||(e===D.keys.LEFT?t?this.quill.setSelection(r.index-1,r.length+1,C.default.sources.USER):this.quill.setSelection(r.index-1,C.default.sources.USER):t?this.quill.setSelection(r.index,r.length+1,C.default.sources.USER):this.quill.setSelection(r.index+r.length+1,C.default.sources.USER),!1)}),r}function a(e,t){if(0===e.index||this.quill.getLength()<=1)return void(0===e.index&&this.quill.getLength()<=1&&this.quill.deleteText(0,1));var r=this.quill.getLine(e.index),o=b(r,1),n=o[0],i={};if(0===t.offset){var s=this.quill.getLine(e.index-1),l=b(s,1),u=l[0];if(null!=u&&u.length()>1){var a=n.formats(),c=this.quill.getFormat(e.index-1,1);i=T.default.attributes.diff(a,c)||{}}}var d=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(t.prefix)?2:1;this.quill.deleteText(e.index-d,d,C.default.sources.USER),Object.keys(i).length>0&&this.quill.formatLine(e.index-d,d,i,C.default.sources.USER),this.quill.focus()}function c(e,t){var r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(t.suffix)?2:1;if(!(e.index>=this.quill.getLength()-r)){var o={},n=0,i=this.quill.getLine(e.index),s=b(i,1),l=s[0];if(t.offset>=l.length()-1){var u=this.quill.getLine(e.index+1),a=b(u,1),c=a[0];if(c){var d=l.formats(),f=this.quill.getFormat(e.index,1);o=T.default.attributes.diff(d,f)||{},n=c.length()}}this.quill.deleteText(e.index,r,C.default.sources.USER),Object.keys(o).length>0&&this.quill.formatLine(e.index+n-1,r,o,C.default.sources.USER)}}function d(e){var t=this.quill.getLines(e),r={};if(t.length>1){var o=t[0].formats(),n=t[t.length-1].formats();r=T.default.attributes.diff(n,o)||{}}this.quill.deleteText(e,C.default.sources.USER),Object.keys(r).length>0&&this.quill.formatLine(e.index,1,r,C.default.sources.USER),this.quill.setSelection(e.index,C.default.sources.SILENT),this.quill.focus()}function f(e,t){var r=this;e.length>0&&this.quill.scroll.deleteAt(e.index,e.length);var o=Object.keys(t.format).reduce(function(e,r){return S.default.query(r,S.default.Scope.BLOCK)&&!Array.isArray(t.format[r])&&(e[r]=t.format[r]),e},{});this.quill.insertText(e.index,"\n",o,C.default.sources.USER),this.quill.setSelection(e.index+1,C.default.sources.SILENT),this.quill.focus(),Object.keys(t.format).forEach(function(e){null==o[e]&&(Array.isArray(t.format[e])||"link"!==e&&r.quill.format(e,t.format[e],C.default.sources.USER))})}function h(e){return{key:D.keys.TAB,shiftKey:!e,format:{"code-block":!0},handler:function(t){var r=S.default.query("code-block"),o=t.index,n=t.length,i=this.quill.scroll.descendant(r,o),s=b(i,2),l=s[0],u=s[1];if(null!=l){var a=this.quill.getIndex(l),c=l.newlineIndex(u,!0)+1,d=l.newlineIndex(a+u+n),f=l.domNode.textContent.slice(c,d).split("\n");u=0,f.forEach(function(t,i){e?(l.insertAt(c+u,r.TAB),u+=r.TAB.length,0===i?o+=r.TAB.length:n+=r.TAB.length):t.startsWith(r.TAB)&&(l.deleteAt(c+u,r.TAB.length),u-=r.TAB.length,0===i?o-=r.TAB.length:n-=r.TAB.length),u+=t.length+1}),this.quill.update(C.default.sources.USER),this.quill.setSelection(o,n,C.default.sources.SILENT)}}}}function p(e){return{key:e[0].toUpperCase(),shortKey:!0,handler:function(t,r){this.quill.format(e,!r.format[e],C.default.sources.USER)}}}function m(e){if("string"==typeof e||"number"==typeof e)return m({key:e});if("object"===(void 0===e?"undefined":y(e))&&(e=(0,g.default)(e,!1)),"string"==typeof e.key)if(null!=D.keys[e.key.toUpperCase()])e.key=D.keys[e.key.toUpperCase()];else{if(1!==e.key.length)return null;e.key=e.key.toUpperCase().charCodeAt(0)}return e.shortKey&&(e[B]=e.shortKey,delete e.shortKey),e}Object.defineProperty(t,"__esModule",{value:!0}),t.SHORTKEY=t.default=void 0;var y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},b=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),_=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),v=r(21),g=o(v),j=r(11),E=o(j),O=r(3),w=o(O),x=r(2),A=o(x),N=r(20),T=o(N),k=r(0),S=o(k),P=r(5),C=o(P),M=r(10),L=o(M),I=r(9),R=o(I),q=(0,L.default)("quill:keyboard"),B=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",D=function(e){function t(e,r){i(this,t);var o=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return o.bindings={},Object.keys(o.options.bindings).forEach(function(t){("list autofill"!==t||null==e.scroll.whitelist||e.scroll.whitelist.list)&&o.options.bindings[t]&&o.addBinding(o.options.bindings[t])}),o.addBinding({key:t.keys.ENTER,shiftKey:null},f),o.addBinding({key:t.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(o.addBinding({key:t.keys.BACKSPACE},{collapsed:!0},a),o.addBinding({key:t.keys.DELETE},{collapsed:!0},c)):(o.addBinding({key:t.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},a),o.addBinding({key:t.keys.DELETE},{collapsed:!0,suffix:/^.?$/},c)),o.addBinding({key:t.keys.BACKSPACE},{collapsed:!1},d),o.addBinding({key:t.keys.DELETE},{collapsed:!1},d),o.addBinding({key:t.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},a),o.listen(),o}return l(t,e),_(t,null,[{key:"match",value:function(e,t){return t=m(t),!["altKey","ctrlKey","metaKey","shiftKey"].some(function(r){return!!t[r]!==e[r]&&null!==t[r]})&&t.key===(e.which||e.keyCode)}}]),_(t,[{key:"addBinding",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=m(e);if(null==o||null==o.key)return q.warn("Attempted to add invalid keyboard binding",o);"function"==typeof t&&(t={handler:t}),"function"==typeof r&&(r={handler:r}),o=(0,w.default)(o,t,r),this.bindings[o.key]=this.bindings[o.key]||[],this.bindings[o.key].push(o)}},{key:"listen",value:function(){var e=this;this.quill.root.addEventListener("keydown",function(r){if(!r.defaultPrevented){var o=r.which||r.keyCode,n=(e.bindings[o]||[]).filter(function(e){return t.match(r,e)});if(0!==n.length){var i=e.quill.getSelection();if(null!=i&&e.quill.hasFocus()){var s=e.quill.getLine(i.index),l=b(s,2),u=l[0],a=l[1],c=e.quill.getLeaf(i.index),d=b(c,2),f=d[0],h=d[1],p=0===i.length?[f,h]:e.quill.getLeaf(i.index+i.length),m=b(p,2),_=m[0],v=m[1],g=f instanceof S.default.Text?f.value().slice(0,h):"",j=_ instanceof S.default.Text?_.value().slice(v):"",O={collapsed:0===i.length,empty:0===i.length&&u.length()<=1,format:e.quill.getFormat(i),offset:a,prefix:g,suffix:j};n.some(function(t){if(null!=t.collapsed&&t.collapsed!==O.collapsed)return!1;if(null!=t.empty&&t.empty!==O.empty)return!1;if(null!=t.offset&&t.offset!==O.offset)return!1;if(Array.isArray(t.format)){if(t.format.every(function(e){return null==O.format[e]}))return!1}else if("object"===y(t.format)&&!Object.keys(t.format).every(function(e){return!0===t.format[e]?null!=O.format[e]:!1===t.format[e]?null==O.format[e]:(0,E.default)(t.format[e],O.format[e])}))return!1;return!(null!=t.prefix&&!t.prefix.test(O.prefix))&&(!(null!=t.suffix&&!t.suffix.test(O.suffix))&&!0!==t.handler.call(e,i,O))})&&r.preventDefault()}}}})}}]),t}(R.default);D.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},D.DEFAULTS={bindings:{bold:p("bold"),italic:p("italic"),underline:p("underline"),indent:{key:D.keys.TAB,format:["blockquote","indent","list"],handler:function(e,t){if(t.collapsed&&0!==t.offset)return!0;this.quill.format("indent","+1",C.default.sources.USER)}},outdent:{key:D.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(e,t){if(t.collapsed&&0!==t.offset)return!0;this.quill.format("indent","-1",C.default.sources.USER)}},"outdent backspace":{key:D.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(e,t){null!=t.format.indent?this.quill.format("indent","-1",C.default.sources.USER):null!=t.format.list&&this.quill.format("list",!1,C.default.sources.USER)}},"indent code-block":h(!0),"outdent code-block":h(!1),"remove tab":{key:D.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(e){this.quill.deleteText(e.index-1,1,C.default.sources.USER)}},tab:{key:D.keys.TAB,handler:function(e){this.quill.history.cutoff(),this.quill.updateContents((new A.default).retain(e.index).delete(e.length).insert("\t"),C.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(e.index+1,C.default.sources.SILENT)}},"list empty enter":{key:D.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(e,t){this.quill.format("list",!1,C.default.sources.USER),t.format.indent&&this.quill.format("indent",!1,C.default.sources.USER)}},"checklist enter":{key:D.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(e){var t=this.quill.getLine(e.index),r=b(t,2),o=r[0],n=r[1],i=(0,w.default)({},o.formats(),{list:"checked"});this.quill.updateContents((new A.default).retain(e.index).insert("\n",i).retain(o.length()-n-1).retain(1,{list:"unchecked"}),C.default.sources.USER),this.quill.setSelection(e.index+1,C.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:D.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(e,t){var r=this.quill.getLine(e.index),o=b(r,2),n=o[0],i=o[1];this.quill.updateContents((new A.default).retain(e.index).insert("\n",t.format).retain(n.length()-i-1).retain(1,{header:null}),C.default.sources.USER),this.quill.setSelection(e.index+1,C.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(e,t){var r=t.prefix.length,o=this.quill.getLine(e.index),n=b(o,2),i=n[0],s=n[1];if(s>r)return!0;var l=void 0;switch(t.prefix.trim()){case"[]":case"[ ]":l="unchecked";break;case"[x]":l="checked";break;case"-":case"*":l="bullet";break;default:l="ordered"}this.quill.insertText(e.index," ",C.default.sources.USER),this.quill.history.cutoff(),this.quill.updateContents((new A.default).retain(e.index-s).retain(i.length()-2-s).retain(1,{list:l}),C.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(e.index+(r-1),C.default.sources.SILENT)}},"code exit":{key:D.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(e){var t=this.quill.getLine(e.index),r=b(t,2),o=r[0],n=r[1];this.quill.updateContents((new A.default).retain(e.index+o.length()-n-2).retain(1,{"code-block":null}).delete(1),C.default.sources.USER)}},"embed left":u(D.keys.LEFT,!1),"embed left shift":u(D.keys.LEFT,!0),"embed right":u(D.keys.RIGHT,!1),"embed right shift":u(D.keys.RIGHT,!0)}},t.default=D,t.SHORTKEY=B},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),u=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},a=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),c=r(0),d=o(c),f=r(7),h=o(f),p=function(e){function t(e,r){n(this,t);var o=i(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return o.selection=r,o.textNode=document.createTextNode(t.CONTENTS),o.domNode.appendChild(o.textNode),o._length=0,o}return s(t,e),a(t,null,[{key:"value",value:function(){}}]),a(t,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(e,r){if(0!==this._length)return u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"format",this).call(this,e,r);for(var o=this,n=0;null!=o&&o.statics.scope!==d.default.Scope.BLOCK_BLOT;)n+=o.offset(o.parent),o=o.parent;null!=o&&(this._length=t.CONTENTS.length,o.optimize(),o.formatAt(n,t.CONTENTS.length,e,r),this._length=0)}},{key:"index",value:function(e,r){return e===this.textNode?0:u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"index",this).call(this,e,r)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var e=this.textNode,r=this.selection.getNativeRange(),o=void 0,n=void 0,i=void 0;if(null!=r&&r.start.node===e&&r.end.node===e){var s=[e,r.start.offset,r.end.offset];o=s[0],n=s[1],i=s[2]}for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==t.CONTENTS){var u=this.textNode.data.split(t.CONTENTS).join("");this.next instanceof h.default?(o=this.next.domNode,this.next.insertAt(0,u),this.textNode.data=t.CONTENTS):(this.textNode.data=u,this.parent.insertBefore(d.default.create(this.textNode),this),this.textNode=document.createTextNode(t.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),null!=n){var a=[n,i].map(function(e){return Math.max(0,Math.min(o.data.length,e-1))}),c=l(a,2);return n=c[0],i=c[1],{startNode:o,startOffset:n,endNode:o,endOffset:i}}}}},{key:"update",value:function(e,t){var r=this;if(e.some(function(e){return"characterData"===e.type&&e.target===r.textNode})){var o=this.restore();o&&(t.range=o)}}},{key:"value",value:function(){return""}}]),t}(d.default.Embed);p.blotName="cursor",p.className="ql-cursor",p.tagName="span",p.CONTENTS="\ufeff",t.default=p},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=r(0),u=o(l),a=r(4),c=o(a),d=function(e){function t(){return n(this,t),i(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return s(t,e),t}(u.default.Container);d.allowedChildren=[c.default,a.BlockEmbed,d],t.default=d},function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0}),t.ColorStyle=t.ColorClass=t.ColorAttributor=void 0;var s=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),l=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},u=r(0),a=function(e){return e&&e.__esModule?e:{default:e}}(u),c=function(e){function t(){return o(this,t),n(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return i(t,e),s(t,[{key:"value",value:function(e){var r=l(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"value",this).call(this,e);return r.startsWith("rgb(")?(r=r.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+r.split(",").map(function(e){return("00"+parseInt(e).toString(16)).slice(-2)}).join("")):r}}]),t}(a.default.Attributor.Style),d=new a.default.Attributor.Class("color","ql-color",{scope:a.default.Scope.INLINE}),f=new c("color","color",{scope:a.default.Scope.INLINE});t.ColorAttributor=c,t.ColorClass=d,t.ColorStyle=f},,,function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r(0),i=o(n),s=r(5),l=o(s),u=r(4),a=o(u),c=r(16),d=o(c),f=r(25),h=o(f),p=r(24),m=o(p),y=r(35),b=o(y),_=r(6),v=o(_),g=r(22),j=o(g),E=r(7),O=o(E);l.default.register({"blots/block":a.default,"blots/block/embed":u.BlockEmbed,"blots/break":d.default,"blots/container":h.default,"blots/cursor":m.default,"blots/embed":b.default,"blots/inline":v.default,"blots/scroll":j.default,"blots/text":O.default,"modules/clipboard":o(r(55)).default,"modules/history":o(r(42)).default,"modules/keyboard":o(r(23)).default}),i.default.register(a.default,d.default,m.default,v.default,j.default,O.default),t.default=l.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=r(1);t.default=function(){function e(e){this.domNode=e,this.domNode[o.DATA_KEY]={blot:this}}return Object.defineProperty(e.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),e.create=function(e){if(null==this.tagName)throw new o.ParchmentError("Blot definition missing tagName");var t;return Array.isArray(this.tagName)?("string"==typeof e&&(e=e.toUpperCase(),parseInt(e).toString()===e&&(e=parseInt(e))),t="number"==typeof e?document.createElement(this.tagName[e-1]):this.tagName.indexOf(e)>-1?document.createElement(e):document.createElement(this.tagName[0])):t=document.createElement(this.tagName),this.className&&t.classList.add(this.className),t},e.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)},e.prototype.clone=function(){var e=this.domNode.cloneNode(!1);return o.create(e)},e.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),delete this.domNode[o.DATA_KEY]},e.prototype.deleteAt=function(e,t){this.isolate(e,t).remove()},e.prototype.formatAt=function(e,t,r,n){var i=this.isolate(e,t);if(null!=o.query(r,o.Scope.BLOT)&&n)i.wrap(r,n);else if(null!=o.query(r,o.Scope.ATTRIBUTE)){var s=o.create(this.statics.scope);i.wrap(s),s.format(r,n)}},e.prototype.insertAt=function(e,t,r){this.parent.insertBefore(null==r?o.create("text",t):o.create(t,r),this.split(e))},e.prototype.insertInto=function(e,t){void 0===t&&(t=null),null!=this.parent&&this.parent.children.remove(this);var r=null;e.children.insertBefore(this,t),null!=t&&(r=t.domNode),this.domNode.parentNode==e.domNode&&this.domNode.nextSibling==r||e.domNode.insertBefore(this.domNode,r),this.parent=e,this.attach()},e.prototype.isolate=function(e,t){var r=this.split(e);return r.split(t),r},e.prototype.length=function(){return 1},e.prototype.offset=function(e){return void 0===e&&(e=this.parent),null==this.parent||this==e?0:this.parent.children.offset(this)+this.parent.offset(e)},e.prototype.optimize=function(e){null!=this.domNode[o.DATA_KEY]&&delete this.domNode[o.DATA_KEY].mutations},e.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},e.prototype.replace=function(e){null!=e.parent&&(e.parent.insertBefore(this,e.next),e.remove())},e.prototype.replaceWith=function(e,t){var r="string"==typeof e?o.create(e,t):e;return r.replace(this),r},e.prototype.split=function(e,t){return 0===e?this:this.next},e.prototype.update=function(e,t){},e.prototype.wrap=function(e,t){var r="string"==typeof e?o.create(e,t):e;return null!=this.parent&&this.parent.insertBefore(r,this.next),r.appendChild(this),r},e.blotName="abstract",e}()},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=r(12),n=r(32),i=r(33),s=r(1);t.default=function(){function e(e){this.attributes={},this.domNode=e,this.build()}return e.prototype.attribute=function(e,t){t?e.add(this.domNode,t)&&(null!=e.value(this.domNode)?this.attributes[e.attrName]=e:delete this.attributes[e.attrName]):(e.remove(this.domNode),delete this.attributes[e.attrName])},e.prototype.build=function(){var e=this;this.attributes={};var t=o.default.keys(this.domNode),r=n.default.keys(this.domNode),l=i.default.keys(this.domNode);t.concat(r).concat(l).forEach(function(t){var r=s.query(t,s.Scope.ATTRIBUTE);r instanceof o.default&&(e.attributes[r.attrName]=r)})},e.prototype.copy=function(e){var t=this;Object.keys(this.attributes).forEach(function(r){var o=t.attributes[r].value(t.domNode);e.format(r,o)})},e.prototype.move=function(e){var t=this;this.copy(e),Object.keys(this.attributes).forEach(function(e){t.attributes[e].remove(t.domNode)}),this.attributes={}},e.prototype.values=function(){var e=this;return Object.keys(this.attributes).reduce(function(t,r){return t[r]=e.attributes[r].value(e.domNode),t},{})},e}()},function(e,t,r){"use strict";function o(e,t){return(e.getAttribute("class")||"").split(/\s+/).filter(function(e){return 0===e.indexOf(t+"-")})}var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=r(12);t.default=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.keys=function(e){return(e.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})},t.prototype.add=function(e,t){return!!this.canAdd(e,t)&&(this.remove(e),e.classList.add(this.keyName+"-"+t),!0)},t.prototype.remove=function(e){o(e,this.keyName).forEach(function(t){e.classList.remove(t)}),0===e.classList.length&&e.removeAttribute("class")},t.prototype.value=function(e){var t=o(e,this.keyName)[0]||"",r=t.slice(this.keyName.length+1);return this.canAdd(e,r)?r:""},t}(i.default)},function(e,t,r){"use strict";function o(e){var t=e.split("-");return t[0]+t.slice(1).map(function(e){return e[0].toUpperCase()+e.slice(1)}).join("")}var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=r(12);t.default=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.keys=function(e){return(e.getAttribute("style")||"").split(";").map(function(e){return e.split(":")[0].trim()})},t.prototype.add=function(e,t){return!!this.canAdd(e,t)&&(e.style[o(this.keyName)]=t,!0)},t.prototype.remove=function(e){e.style[o(this.keyName)]="",e.getAttribute("style")||e.removeAttribute("style")},t.prototype.value=function(e){var t=e.style[o(this.keyName)];return this.canAdd(e,t)?t:""},t}(i.default)},function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),i=function(){function e(t,r){o(this,e),this.quill=t,this.options=r,this.modules={}}return n(e,[{key:"init",value:function(){var e=this;Object.keys(this.options.modules).forEach(function(t){null==e.modules[t]&&e.addModule(t)})}},{key:"addModule",value:function(e){return this.modules[e]=new(this.quill.constructor.import("modules/"+e))(this.quill,this.options.modules[e]||{})}}]),e}();i.DEFAULTS={modules:{}},i.themes={default:i},t.default=i},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0});var l=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),u=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},a=r(0),c=o(a),d=r(7),f=o(d),h="\ufeff";t.default=function(e){function t(e){n(this,t);var r=i(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return r.contentNode=document.createElement("span"),r.contentNode.setAttribute("contenteditable",!1),[].slice.call(r.domNode.childNodes).forEach(function(e){r.contentNode.appendChild(e)}),r.leftGuard=document.createTextNode(h),r.rightGuard=document.createTextNode(h),r.domNode.appendChild(r.leftGuard),r.domNode.appendChild(r.contentNode),r.domNode.appendChild(r.rightGuard),r}return s(t,e),l(t,[{key:"index",value:function(e,r){return e===this.leftGuard?0:e===this.rightGuard?1:u(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"index",this).call(this,e,r)}},{key:"restore",value:function(e){var t=void 0,r=void 0,o=e.data.split(h).join("");if(e===this.leftGuard)if(this.prev instanceof f.default){var n=this.prev.length();this.prev.insertAt(n,o),t={startNode:this.prev.domNode,startOffset:n+o.length}}else r=document.createTextNode(o),this.parent.insertBefore(c.default.create(r),this),t={startNode:r,startOffset:o.length};else e===this.rightGuard&&(this.next instanceof f.default?(this.next.insertAt(0,o),t={startNode:this.next.domNode,startOffset:o.length}):(r=document.createTextNode(o),this.parent.insertBefore(c.default.create(r),this.next),t={startNode:r,startOffset:o.length}));return e.data=h,t}},{key:"update",value:function(e,t){var r=this;e.forEach(function(e){if("characterData"===e.type&&(e.target===r.leftGuard||e.target===r.rightGuard)){var o=r.restore(e.target);o&&(t.range=o)}})}}]),t}(c.default.Embed)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AlignStyle=t.AlignClass=t.AlignAttribute=void 0;var o=r(0),n=function(e){return e&&e.__esModule?e:{default:e}}(o),i={scope:n.default.Scope.BLOCK,whitelist:["right","center","justify"]},s=new n.default.Attributor.Attribute("align","align",i),l=new n.default.Attributor.Class("align","ql-align",i),u=new n.default.Attributor.Style("align","text-align",i);t.AlignAttribute=s,t.AlignClass=l,t.AlignStyle=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BackgroundStyle=t.BackgroundClass=void 0;var o=r(0),n=function(e){return e&&e.__esModule?e:{default:e}}(o),i=r(26),s=new n.default.Attributor.Class("background","ql-bg",{scope:n.default.Scope.INLINE}),l=new i.ColorAttributor("background","background-color",{scope:n.default.Scope.INLINE});t.BackgroundClass=s,t.BackgroundStyle=l},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DirectionStyle=t.DirectionClass=t.DirectionAttribute=void 0;var o=r(0),n=function(e){return e&&e.__esModule?e:{default:e}}(o),i={scope:n.default.Scope.BLOCK,whitelist:["rtl"]},s=new n.default.Attributor.Attribute("direction","dir",i),l=new n.default.Attributor.Class("direction","ql-direction",i),u=new n.default.Attributor.Style("direction","direction",i);t.DirectionAttribute=s,t.DirectionClass=l,t.DirectionStyle=u},function(e,t,r){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}Object.defineProperty(t,"__esModule",{value:!0}),t.FontClass=t.FontStyle=void 0;var s=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),l=function e(t,r,o){null===t&&(t=Function.prototype);var n=Object.getOwnPropertyDescriptor(t,r);if(void 0===n){var i=Object.getPrototypeOf(t);return null===i?void 0:e(i,r,o)}if("value"in n)return n.value;var s=n.get;if(void 0!==s)return s.call(o)},u=r(0),a=function(e){return e&&e.__esModule?e:{default:e}}(u),c={scope:a.default.Scope.INLINE,whitelist:["serif","monospace"]},d=new a.default.Attributor.Class("font","ql-font",c);t.FontStyle=new(function(e){function t(){return o(this,t),n(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return i(t,e),s(t,[{key:"value",value:function(e){return l(t.prototype.__proto__||Object.getPrototypeOf(t.prototype),"value",this).call(this,e).replace(/["']/g,"")}}]),t}(a.default.Attributor.Style))("font","font-family",c),t.FontClass=d},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SizeStyle=t.SizeClass=void 0;var o=r(0),n=function(e){return e&&e.__esModule?e:{default:e}}(o),i=new n.default.Attributor.Class("size","ql-size",{scope:n.default.Scope.INLINE,whitelist:["small","large","huge"]}),s=new n.default.Attributor.Style("size","font-size",{scope:n.default.Scope.INLINE,whitelist:["10px","18px","32px"]});t.SizeClass=i,t.SizeStyle=s},,function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function s(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function l(e){var t=e.ops[e.ops.length-1];return null!=t&&(null!=t.insert?"string"==typeof t.insert&&t.insert.endsWith("\n"):null!=t.attributes&&Object.keys(t.attributes).some(function(e){return null!=d.default.query(e,d.default.Scope.BLOCK)}))}function u(e){var t=e.reduce(function(e,t){return e+=t.delete||0},0),r=e.length()-t;return l(e)&&(r-=1),r}Object.defineProperty(t,"__esModule",{value:!0}),t.getLastChangeIndex=t.default=void 0;var a=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),c=r(0),d=o(c),f=r(5),h=o(f),p=r(9),m=o(p),y=function(e){function t(e,r){n(this,t);var o=i(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return o.lastRecorded=0,o.ignoreChange=!1,o.clear(),o.quill.on(h.default.events.EDITOR_CHANGE,function(e,t,r,n){e!==h.default.events.TEXT_CHANGE||o.ignoreChange||(o.options.userOnly&&n!==h.default.sources.USER?o.transform(t):o.record(t,r))}),o.quill.keyboard.addBinding({key:"Z",shortKey:!0},o.undo.bind(o)),o.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},o.redo.bind(o)),/Win/i.test(navigator.platform)&&o.quill.keyboard.addBinding({key:"Y",shortKey:!0},o.redo.bind(o)),o}return s(t,e),a(t,[{key:"change",value:function(e,t){if(0!==this.stack[e].length){var r=this.stack[e].pop();this.stack[t].push(r),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r[e],h.default.sources.USER),this.ignoreChange=!1;this.quill.setSelection(u(r[e]))}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(e,t){if(0!==e.ops.length){this.stack.redo=[];var r=this.quill.getContents().diff(t),o=Date.now();if(this.lastRecorded+this.options.delay>o&&this.stack.undo.length>0){var n=this.stack.undo.pop();r=r.compose(n.undo),e=n.redo.compose(e)}else this.lastRecorded=o;this.stack.undo.push({redo:e,undo:r}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(e){this.stack.undo.forEach(function(t){t.undo=e.transform(t.undo,!0),t.redo=e.transform(t.redo,!0)}),this.stack.redo.forEach(function(t){t.undo=e.transform(t.undo,!0),t.redo=e.transform(t.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),t}(m.default);y.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},t.default=y,t.getLastChangeIndex=u},,function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){function e(){this.head=this.tail=null,this.length=0}return e.prototype.append=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.insertBefore(e[0],null),e.length>1&&this.append.apply(this,e.slice(1))},e.prototype.contains=function(e){for(var t,r=this.iterator();t=r();)if(t===e)return!0;return!1},e.prototype.insertBefore=function(e,t){e&&(e.next=t,null!=t?(e.prev=t.prev,null!=t.prev&&(t.prev.next=e),t.prev=e,t===this.head&&(this.head=e)):null!=this.tail?(this.tail.next=e,e.prev=this.tail,this.tail=e):(e.prev=null,this.head=this.tail=e),this.length+=1)},e.prototype.offset=function(e){for(var t=0,r=this.head;null!=r;){if(r===e)return t;t+=r.length(),r=r.next}return-1},e.prototype.remove=function(e){this.contains(e)&&(null!=e.prev&&(e.prev.next=e.next),null!=e.next&&(e.next.prev=e.prev),e===this.head&&(this.head=e.next),e===this.tail&&(this.tail=e.prev),this.length-=1)},e.prototype.iterator=function(e){return void 0===e&&(e=this.head),function(){var t=e;return null!=e&&(e=e.next),t}},e.prototype.find=function(e,t){void 0===t&&(t=!1);for(var r,o=this.iterator();r=o();){var n=r.length();if(e<n||t&&e===n&&(null==r.next||0!==r.next.length()))return[r,e];e-=n}return[null,0]},e.prototype.forEach=function(e){for(var t,r=this.iterator();t=r();)e(t)},e.prototype.forEachAt=function(e,t,r){if(!(t<=0))for(var o,n=this.find(e),i=n[0],s=n[1],l=e-s,u=this.iterator(i);(o=u())&&l<e+t;){var a=o.length();e>l?r(o,e-l,Math.min(t,l+a-e)):r(o,0,Math.min(a,e+t-l)),l+=a}},e.prototype.map=function(e){return this.reduce(function(t,r){return t.push(e(r)),t},[])},e.prototype.reduce=function(e,t){for(var r,o=this.iterator();r=o();)t=e(t,r);return t},e}()},function(e,t,r){"use strict";var o=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var n=r(17),i=r(1),s={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0};t.default=function(e){function t(t){var r=e.call(this,t)||this;return r.scroll=r,r.observer=new MutationObserver(function(e){r.update(e)}),r.observer.observe(r.domNode,s),r.attach(),r}return o(t,e),t.prototype.detach=function(){e.prototype.detach.call(this),this.observer.disconnect()},t.prototype.deleteAt=function(t,r){this.update(),0===t&&r===this.length()?this.children.forEach(function(e){e.remove()}):e.prototype.deleteAt.call(this,t,r)},t.prototype.formatAt=function(t,r,o,n){this.update(),e.prototype.formatAt.call(this,t,r,o,n)},t.prototype.insertAt=function(t,r,o){this.update(),e.prototype.insertAt.call(this,t,r,o)},t.prototype.optimize=function(t,r){var o=this;void 0===t&&(t=[]),void 0===r&&(r={}),e.prototype.optimize.call(this,r);for(var s=[].slice.call(this.observer.takeRecords());s.length>0;)t.push(s.pop());for(var l=function(e,t){void 0===t&&(t=!0),null!=e&&e!==o&&null!=e.domNode.parentNode&&(null==e.domNode[i.DATA_KEY].mutations&&(e.domNode[i.DATA_KEY].mutations=[]),t&&l(e.parent))},u=function(e){null!=e.domNode[i.DATA_KEY]&&null!=e.domNode[i.DATA_KEY].mutations&&(e instanceof n.default&&e.children.forEach(u),e.optimize(r))},a=t,c=0;a.length>0;c+=1){if(c>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(a.forEach(function(e){var t=i.find(e.target,!0);null!=t&&(t.domNode===e.target&&("childList"===e.type?(l(i.find(e.previousSibling,!1)),[].forEach.call(e.addedNodes,function(e){var t=i.find(e,!1);l(t,!1),t instanceof n.default&&t.children.forEach(function(e){l(e,!1)})})):"attributes"===e.type&&l(t.prev)),l(t))}),this.children.forEach(u),a=[].slice.call(this.observer.takeRecords()),s=a.slice();s.length>0;)t.push(s.pop())}},t.prototype.update=function(t,r){var o=this;void 0===r&&(r={}),t=t||this.observer.takeRecords(),t.map(function(e){var t=i.find(e.target,!0);return null==t?null:null==t.domNode[i.DATA_KEY].mutations?(t.domNode[i.DATA_KEY].mutations=[e],t):(t.domNode[i.DATA_KEY].mutations.push(e),null)}).forEach(function(e){null!=e&&e!==o&&null!=e.domNode[i.DATA_KEY]&&e.update(e.domNode[i.DATA_KEY].mutations||[],r)}),null!=this.domNode[i.DATA_KEY].mutations&&e.prototype.update.call(this,this.domNode[i.DATA_KEY].mutations,r),this.optimize(t,r)},t.blotName="scroll",t.defaultChild="block",t.scope=i.Scope.BLOCK_BLOT,t.tagName="DIV",t}(n.default)},function(e,t,r){"use strict";function o(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(var r in e)if(e[r]!==t[r])return!1;return!0}var n=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var i=r(18),s=r(1);t.default=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return n(t,e),t.formats=function(r){if(r.tagName!==t.tagName)return e.formats.call(this,r)},t.prototype.format=function(r,o){var n=this;r!==this.statics.blotName||o?e.prototype.format.call(this,r,o):(this.children.forEach(function(e){e instanceof i.default||(e=e.wrap(t.blotName,!0)),n.attributes.copy(e)}),this.unwrap())},t.prototype.formatAt=function(t,r,o,n){if(null!=this.formats()[o]||s.query(o,s.Scope.ATTRIBUTE)){this.isolate(t,r).format(o,n)}else e.prototype.formatAt.call(this,t,r,o,n)},t.prototype.optimize=function(r){e.prototype.optimize.call(this,r);var n=this.formats();if(0===Object.keys(n).length)return this.unwrap();var i=this.next;i instanceof t&&i.prev===this&&o(n,i.formats())&&(i.moveChildren(this),i.remove())},t.blotName="inline",t.scope=s.Scope.INLINE_BLOT,t.tagName="SPAN",t}(i.default)},function(e,t,r){"use strict";var o=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var n=r(18),i=r(1);t.default=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.formats=function(r){if(r.tagName!==i.query(t.blotName).tagName)return e.formats.call(this,r)},t.prototype.format=function(r,o){null!=i.query(r,i.Scope.BLOCK)&&(r!==this.statics.blotName||o?e.prototype.format.call(this,r,o):this.replaceWith(t.blotName))},t.prototype.formatAt=function(t,r,o,n){null!=i.query(o,i.Scope.BLOCK)?this.format(o,n):e.prototype.formatAt.call(this,t,r,o,n)},t.prototype.insertAt=function(t,r,o){if(null==o||null!=i.query(r,i.Scope.INLINE))e.prototype.insertAt.call(this,t,r,o);else{var n=this.split(t);n.parent.insertBefore(i.create(r,o),n)}},t.prototype.update=function(t,r){navigator.userAgent.match(/Trident/)?this.build():e.prototype.update.call(this,t,r)},t.blotName="block",t.scope=i.Scope.BLOCK_BLOT,t.tagName="P",t}(n.default)},function(e,t,r){"use strict";var o=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var n=r(19);t.default=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return o(t,e),t.formats=function(e){},t.prototype.format=function(t,r){e.prototype.formatAt.call(this,0,this.length(),t,r)},t.prototype.formatAt=function(t,r,o,n){0===t&&r===this.length()?this.format(o,n):e.prototype.formatAt.call(this,t,r,o,n)},t.prototype.formats=function(){return this.statics.formats(this.domNode)},t}(n.default)},function(e,t,r){"use strict";var o=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])};return function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}}();Object.defineProperty(t,"__esModule",{value:!0});var n=r(19),i=r(1);t.default=function(e){function t(t){var r=e.call(this,t)||this;return r.text=r.statics.value(r.domNode),r}return o(t,e),t.create=function(e){return document.createTextNode(e)},t.value=function(e){var t=e.data;return t.normalize&&(t=t.normalize()),t},t.prototype.deleteAt=function(e,t){this.domNode.data=this.text=this.text.slice(0,e)+this.text.slice(e+t)},t.prototype.index=function(e,t){return this.domNode===e?t:-1},t.prototype.insertAt=function(t,r,o){null==o?(this.text=this.text.slice(0,t)+r+this.text.slice(t),this.domNode.data=this.text):e.prototype.insertAt.call(this,t,r,o)},t.prototype.length=function(){return this.text.length},t.prototype.optimize=function(t){e.prototype.optimize.call(this,t),this.text=this.statics.value(this.domNode),0===this.text.length&&this.remove()},t.prototype.position=function(e,t){return void 0===t&&(t=!1),[this.domNode,e]},t.prototype.split=function(e,t){if(void 0===t&&(t=!1),!t){if(0===e)return this;if(e===this.length())return this.next}var r=i.create(this.domNode.splitText(e));return this.parent.insertBefore(r,this.next),this.text=this.statics.value(this.domNode),r},t.prototype.update=function(e,t){var r=this;e.some(function(e){return"characterData"===e.type&&e.target===r.domNode})&&(this.text=this.statics.value(this.domNode))},t.prototype.value=function(){return this.text},t.blotName="text",t.scope=i.Scope.INLINE_BLOT,t}(n.default)},function(e,t,r){"use strict";var o=document.createElement("div");if(o.classList.toggle("test-class",!1),o.classList.contains("test-class")){var n=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(e,t){return arguments.length>1&&!this.contains(e)==!t?t:n.call(this,e)}}String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.substr(t,e.length)===e}),String.prototype.endsWith||(String.prototype.endsWith=function(e,t){var r=this.toString();("number"!=typeof t||!isFinite(t)||Math.floor(t)!==t||t>r.length)&&(t=r.length),t-=e.length;var o=r.indexOf(e,t);return-1!==o&&o===t}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(e){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!=typeof e)throw new TypeError("predicate must be a function");for(var t,r=Object(this),o=r.length>>>0,n=arguments[1],i=0;i<o;i++)if(t=r[i],e.call(n,t,i,r))return t}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(e,t){function r(e,t,r){if(e==t)return e?[[y,e]]:[];(r<0||e.length<r)&&(r=null);var n=s(e,t),i=e.substring(0,n);e=e.substring(n),t=t.substring(n),n=l(e,t);var u=e.substring(e.length-n);e=e.substring(0,e.length-n),t=t.substring(0,t.length-n);var c=o(e,t);return i&&c.unshift([y,i]),u&&c.push([y,u]),a(c),null!=r&&(c=d(c,r)),c=f(c)}function o(e,t){var o;if(!e)return[[m,t]];if(!t)return[[p,e]];var i=e.length>t.length?e:t,s=e.length>t.length?t:e,l=i.indexOf(s);if(-1!=l)return o=[[m,i.substring(0,l)],[y,s],[m,i.substring(l+s.length)]],e.length>t.length&&(o[0][0]=o[2][0]=p),o;if(1==s.length)return[[p,e],[m,t]];var a=u(e,t);if(a){var c=a[0],d=a[1],f=a[2],h=a[3],b=a[4],_=r(c,f),v=r(d,h);return _.concat([[y,b]],v)}return n(e,t)}function n(e,t){for(var r=e.length,o=t.length,n=Math.ceil((r+o)/2),s=n,l=2*n,u=new Array(l),a=new Array(l),c=0;c<l;c++)u[c]=-1,a[c]=-1;u[s+1]=0,a[s+1]=0;for(var d=r-o,f=d%2!=0,h=0,y=0,b=0,_=0,v=0;v<n;v++){for(var g=-v+h;g<=v-y;g+=2){var j,E=s+g;j=g==-v||g!=v&&u[E-1]<u[E+1]?u[E+1]:u[E-1]+1;for(var O=j-g;j<r&&O<o&&e.charAt(j)==t.charAt(O);)j++,O++;if(u[E]=j,j>r)y+=2;else if(O>o)h+=2;else if(f){var w=s+d-g;if(w>=0&&w<l&&-1!=a[w]){var x=r-a[w];if(j>=x)return i(e,t,j,O)}}}for(var A=-v+b;A<=v-_;A+=2){var x,w=s+A;x=A==-v||A!=v&&a[w-1]<a[w+1]?a[w+1]:a[w-1]+1;for(var N=x-A;x<r&&N<o&&e.charAt(r-x-1)==t.charAt(o-N-1);)x++,N++;if(a[w]=x,x>r)_+=2;else if(N>o)b+=2;else if(!f){var E=s+d-A;if(E>=0&&E<l&&-1!=u[E]){var j=u[E],O=s+j-E;if(x=r-x,j>=x)return i(e,t,j,O)}}}}return[[p,e],[m,t]]}function i(e,t,o,n){var i=e.substring(0,o),s=t.substring(0,n),l=e.substring(o),u=t.substring(n);return r(i,s).concat(r(l,u))}function s(e,t){if(!e||!t||e.charAt(0)!=t.charAt(0))return 0;for(var r=0,o=Math.min(e.length,t.length),n=o,i=0;r<n;)e.substring(i,n)==t.substring(i,n)?(r=n,i=r):o=n,n=Math.floor((o-r)/2+r);return n}function l(e,t){if(!e||!t||e.charAt(e.length-1)!=t.charAt(t.length-1))return 0;for(var r=0,o=Math.min(e.length,t.length),n=o,i=0;r<n;)e.substring(e.length-n,e.length-i)==t.substring(t.length-n,t.length-i)?(r=n,i=r):o=n,n=Math.floor((o-r)/2+r);return n}function u(e,t){function r(e,t,r){for(var o,n,i,u,a=e.substring(r,r+Math.floor(e.length/4)),c=-1,d="";-1!=(c=t.indexOf(a,c+1));){var f=s(e.substring(r),t.substring(c)),h=l(e.substring(0,r),t.substring(0,c));d.length<h+f&&(d=t.substring(c-h,c)+t.substring(c,c+f),o=e.substring(0,r-h),n=e.substring(r+f),i=t.substring(0,c-h),u=t.substring(c+f))}return 2*d.length>=e.length?[o,n,i,u,d]:null}var o=e.length>t.length?e:t,n=e.length>t.length?t:e;if(o.length<4||2*n.length<o.length)return null;var i,u=r(o,n,Math.ceil(o.length/4)),a=r(o,n,Math.ceil(o.length/2));if(!u&&!a)return null;i=a?u&&u[4].length>a[4].length?u:a:u;var c,d,f,h;return e.length>t.length?(c=i[0],d=i[1],f=i[2],h=i[3]):(f=i[0],h=i[1],c=i[2],d=i[3]),[c,d,f,h,i[4]]}function a(e){e.push([y,""]);for(var t,r=0,o=0,n=0,i="",u="";r<e.length;)switch(e[r][0]){case m:n++,u+=e[r][1],r++;break;case p:o++,i+=e[r][1],r++;break;case y:o+n>1?(0!==o&&0!==n&&(t=s(u,i),0!==t&&(r-o-n>0&&e[r-o-n-1][0]==y?e[r-o-n-1][1]+=u.substring(0,t):(e.splice(0,0,[y,u.substring(0,t)]),r++),u=u.substring(t),i=i.substring(t)),0!==(t=l(u,i))&&(e[r][1]=u.substring(u.length-t)+e[r][1],u=u.substring(0,u.length-t),i=i.substring(0,i.length-t))),0===o?e.splice(r-n,o+n,[m,u]):0===n?e.splice(r-o,o+n,[p,i]):e.splice(r-o-n,o+n,[p,i],[m,u]),r=r-o-n+(o?1:0)+(n?1:0)+1):0!==r&&e[r-1][0]==y?(e[r-1][1]+=e[r][1],e.splice(r,1)):r++,n=0,o=0,i="",u=""}""===e[e.length-1][1]&&e.pop();var c=!1;for(r=1;r<e.length-1;)e[r-1][0]==y&&e[r+1][0]==y&&(e[r][1].substring(e[r][1].length-e[r-1][1].length)==e[r-1][1]?(e[r][1]=e[r-1][1]+e[r][1].substring(0,e[r][1].length-e[r-1][1].length),e[r+1][1]=e[r-1][1]+e[r+1][1],e.splice(r-1,1),c=!0):e[r][1].substring(0,e[r+1][1].length)==e[r+1][1]&&(e[r-1][1]+=e[r+1][1],e[r][1]=e[r][1].substring(e[r+1][1].length)+e[r+1][1],e.splice(r+1,1),c=!0)),r++;c&&a(e)}function c(e,t){if(0===t)return[y,e];for(var r=0,o=0;o<e.length;o++){var n=e[o];if(n[0]===p||n[0]===y){var i=r+n[1].length;if(t===i)return[o+1,e];if(t<i){e=e.slice();var s=t-r;return e.splice(o,1,[n[0],n[1].slice(0,s)],[n[0],n[1].slice(s)]),[o+1,e]}r=i}}throw new Error("cursor_pos is out of bounds!")}function d(e,t){var r=c(e,t),o=r[1],n=r[0],i=o[n],s=o[n+1];if(null==i)return e;if(i[0]!==y)return e;if(null!=s&&i[1]+s[1]===s[1]+i[1])return o.splice(n,2,s,i),h(o,n,2);if(null!=s&&0===s[1].indexOf(i[1])){o.splice(n,2,[s[0],i[1]],[0,i[1]]);var l=s[1].slice(i[1].length);return l.length>0&&o.splice(n+2,0,[s[0],l]),h(o,n,3)}return e}function f(e){for(var t=!1,r=function(e){return e.charCodeAt(0)>=56320&&e.charCodeAt(0)<=57343},o=2;o<e.length;o+=1)e[o-2][0]===y&&function(e){return e.charCodeAt(e.length-1)>=55296&&e.charCodeAt(e.length-1)<=56319}(e[o-2][1])&&e[o-1][0]===p&&r(e[o-1][1])&&e[o][0]===m&&r(e[o][1])&&(t=!0,e[o-1][1]=e[o-2][1].slice(-1)+e[o-1][1],e[o][1]=e[o-2][1].slice(-1)+e[o][1],e[o-2][1]=e[o-2][1].slice(0,-1));if(!t)return e;for(var n=[],o=0;o<e.length;o+=1)e[o][1].length>0&&n.push(e[o]);return n}function h(e,t,r){for(var o=t+r-1;o>=0&&o>=t-1;o--)if(o+1<e.length){var n=e[o],i=e[o+1];n[0]===i[1]&&e.splice(o,2,[n[0],n[1]+i[1]])}return e}var p=-1,m=1,y=0,b=r;b.INSERT=m,b.DELETE=p,b.EQUAL=y,e.exports=b},function(e,t){function r(e){var t=[];for(var r in e)t.push(r);return t}t=e.exports="function"==typeof Object.keys?Object.keys:r,t.shim=r},function(e,t){function r(e){return"[object Arguments]"==Object.prototype.toString.call(e)}function o(e){return e&&"object"==typeof e&&"number"==typeof e.length&&Object.prototype.hasOwnProperty.call(e,"callee")&&!Object.prototype.propertyIsEnumerable.call(e,"callee")||!1}t=e.exports="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}()?r:o,t.supported=r,t.unsupported=o},function(e,t){"use strict";function r(){}function o(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function n(){this._events=new r,this._eventsCount=0}var i=Object.prototype.hasOwnProperty,s="~";Object.create&&(r.prototype=Object.create(null),(new r).__proto__||(s=!1)),n.prototype.eventNames=function(){var e,t,r=[];if(0===this._eventsCount)return r;for(t in e=this._events)i.call(e,t)&&r.push(s?t.slice(1):t);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},n.prototype.listeners=function(e,t){var r=s?s+e:e,o=this._events[r];if(t)return!!o;if(!o)return[];if(o.fn)return[o.fn];for(var n=0,i=o.length,l=new Array(i);n<i;n++)l[n]=o[n].fn;return l},n.prototype.emit=function(e,t,r,o,n,i){var l=s?s+e:e;if(!this._events[l])return!1;var u,a,c=this._events[l],d=arguments.length;if(c.fn){switch(c.once&&this.removeListener(e,c.fn,void 0,!0),d){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,t),!0;case 3:return c.fn.call(c.context,t,r),!0;case 4:return c.fn.call(c.context,t,r,o),!0;case 5:return c.fn.call(c.context,t,r,o,n),!0;case 6:return c.fn.call(c.context,t,r,o,n,i),!0}for(a=1,u=new Array(d-1);a<d;a++)u[a-1]=arguments[a];c.fn.apply(c.context,u)}else{var f,h=c.length;for(a=0;a<h;a++)switch(c[a].once&&this.removeListener(e,c[a].fn,void 0,!0),d){case 1:c[a].fn.call(c[a].context);break;case 2:c[a].fn.call(c[a].context,t);break;case 3:c[a].fn.call(c[a].context,t,r);break;case 4:c[a].fn.call(c[a].context,t,r,o);break;default:if(!u)for(f=1,u=new Array(d-1);f<d;f++)u[f-1]=arguments[f];c[a].fn.apply(c[a].context,u)}}return!0},n.prototype.on=function(e,t,r){var n=new o(t,r||this),i=s?s+e:e;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],n]:this._events[i].push(n):(this._events[i]=n,this._eventsCount++),this},n.prototype.once=function(e,t,r){var n=new o(t,r||this,!0),i=s?s+e:e;return this._events[i]?this._events[i].fn?this._events[i]=[this._events[i],n]:this._events[i].push(n):(this._events[i]=n,this._eventsCount++),this},n.prototype.removeListener=function(e,t,o,n){var i=s?s+e:e;if(!this._events[i])return this;if(!t)return 0==--this._eventsCount?this._events=new r:delete this._events[i],this;var l=this._events[i];if(l.fn)l.fn!==t||n&&!l.once||o&&l.context!==o||(0==--this._eventsCount?this._events=new r:delete this._events[i]);else{for(var u=0,a=[],c=l.length;u<c;u++)(l[u].fn!==t||n&&!l[u].once||o&&l[u].context!==o)&&a.push(l[u]);a.length?this._events[i]=1===a.length?a[0]:a:0==--this._eventsCount?this._events=new r:delete this._events[i]}return this},n.prototype.removeAllListeners=function(e){var t;return e?(t=s?s+e:e,this._events[t]&&(0==--this._eventsCount?this._events=new r:delete this._events[t])):(this._events=new r,this._eventsCount=0),this},n.prototype.off=n.prototype.removeListener,n.prototype.addListener=n.prototype.on,n.prototype.setMaxListeners=function(){return this},n.prefixed=s,n.EventEmitter=n,void 0!==e&&(e.exports=n)},function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function u(e,t,r){return"object"===(void 0===t?"undefined":O(t))?Object.keys(t).reduce(function(e,r){return u(e,r,t[r])},e):e.reduce(function(e,o){return o.attributes&&o.attributes[t]?e.push(o):e.insert(o.insert,(0,N.default)({},n({},t,r),o.attributes))},new k.default)}function a(e){if(e.nodeType!==Node.ELEMENT_NODE)return{};return e["__ql-computed-style"]||(e["__ql-computed-style"]=window.getComputedStyle(e))}function c(e,t){for(var r="",o=e.ops.length-1;o>=0&&r.length<t.length;--o){var n=e.ops[o];if("string"!=typeof n.insert)break;r=n.insert+r}return r.slice(-1*t.length)===t}function d(e){return 0!==e.childNodes.length&&["block","list-item"].indexOf(a(e).display)>-1}function f(e,t,r){return e.nodeType===e.TEXT_NODE?r.reduce(function(t,r){return r(e,t)},new k.default):e.nodeType===e.ELEMENT_NODE?[].reduce.call(e.childNodes||[],function(o,n){var i=f(n,t,r);return n.nodeType===e.ELEMENT_NODE&&(i=t.reduce(function(e,t){return t(n,e)},i),i=(n[W]||[]).reduce(function(e,t){return t(n,e)},i)),o.concat(i)},new k.default):new k.default}function h(e,t,r){return u(r,e,!0)}function p(e,t){var r=P.default.Attributor.Attribute.keys(e),o=P.default.Attributor.Class.keys(e),n=P.default.Attributor.Style.keys(e),i={};return r.concat(o).concat(n).forEach(function(t){var r=P.default.query(t,P.default.Scope.ATTRIBUTE);null!=r&&(i[r.attrName]=r.value(e))||(r=X[t],null==r||r.attrName!==t&&r.keyName!==t||(i[r.attrName]=r.value(e)||void 0),null==(r=$[t])||r.attrName!==t&&r.keyName!==t||(r=$[t],i[r.attrName]=r.value(e)||void 0))}),Object.keys(i).length>0&&(t=u(t,i)),t}function m(e,t){var r=P.default.query(e);if(null==r)return t;if(r.prototype instanceof P.default.Embed){var o={},n=r.value(e);null!=n&&(o[r.blotName]=n,t=(new k.default).insert(o,r.formats(e)))}else"function"==typeof r.formats&&(t=u(t,r.blotName,r.formats(e)));return t}function y(e,t){return c(t,"\n")||t.insert("\n"),t}function b(){return new k.default}function _(e,t){var r=P.default.query(e);if(null==r||"list-item"!==r.blotName||!c(t,"\n"))return t;for(var o=-1,n=e.parentNode;!n.classList.contains("ql-clipboard");)"list"===(P.default.query(n)||{}).blotName&&(o+=1),n=n.parentNode;return o<=0?t:t.compose((new k.default).retain(t.length()-1).retain(1,{indent:o}))}function v(e,t){return c(t,"\n")||(d(e)||t.length()>0&&e.nextSibling&&d(e.nextSibling))&&t.insert("\n"),t}function g(e,t){if(d(e)&&null!=e.nextElementSibling&&!c(t,"\n\n")){e.nextElementSibling.offsetTop>e.offsetTop*****(e.offsetHeight+parseFloat(a(e).marginTop)+parseFloat(a(e).marginBottom))&&t.insert("\n")}return t}function j(e,t){var r={},o=e.style||{};return o.fontStyle&&"italic"===a(e).fontStyle&&(r.italic=!0),o.fontWeight&&(a(e).fontWeight.startsWith("bold")||parseInt(a(e).fontWeight)>=700)&&(r.bold=!0),Object.keys(r).length>0&&(t=u(t,r)),parseFloat(o.textIndent||0)>0&&(t=(new k.default).insert("\t").concat(t)),t}function E(e,t){var r=e.data;if("O:P"===e.parentNode.tagName)return t.insert(r.trim());if(0===r.trim().length&&e.parentNode.classList.contains("ql-clipboard"))return t;if(!a(e.parentNode).whiteSpace.startsWith("pre")){var o=function(e,t){return t=t.replace(/[^\u00a0]/g,""),t.length<1&&e?" ":t};r=r.replace(/\r\n/g," ").replace(/\n/g," "),r=r.replace(/\s\s+/g,o.bind(o,!0)),(null==e.previousSibling&&d(e.parentNode)||null!=e.previousSibling&&d(e.previousSibling))&&(r=r.replace(/^\s+/,o.bind(o,!1))),(null==e.nextSibling&&d(e.parentNode)||null!=e.nextSibling&&d(e.nextSibling))&&(r=r.replace(/\s+$/,o.bind(o,!1)))}return t.insert(r)}Object.defineProperty(t,"__esModule",{value:!0}),t.matchText=t.matchSpacing=t.matchNewline=t.matchBlot=t.matchAttributor=t.default=void 0;var O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},w=function(){function e(e,t){var r=[],o=!0,n=!1,i=void 0;try{for(var s,l=e[Symbol.iterator]();!(o=(s=l.next()).done)&&(r.push(s.value),!t||r.length!==t);o=!0);}catch(e){n=!0,i=e}finally{try{!o&&l.return&&l.return()}finally{if(n)throw i}}return r}return function(t,r){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,r);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),x=function(){function e(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,r,o){return r&&e(t.prototype,r),o&&e(t,o),t}}(),A=r(3),N=o(A),T=r(2),k=o(T),S=r(0),P=o(S),C=r(5),M=o(C),L=r(10),I=o(L),R=r(9),q=o(R),B=r(36),D=r(37),U=r(13),F=o(U),H=r(26),K=r(38),Y=r(39),z=r(40),G=(0,I.default)("quill:clipboard"),W="__ql-matcher",V=[[Node.TEXT_NODE,E],[Node.TEXT_NODE,v],["br",y],[Node.ELEMENT_NODE,v],[Node.ELEMENT_NODE,m],[Node.ELEMENT_NODE,g],[Node.ELEMENT_NODE,p],[Node.ELEMENT_NODE,j],["li",_],["b",h.bind(h,"bold")],["i",h.bind(h,"italic")],["style",b]],X=[B.AlignAttribute,K.DirectionAttribute].reduce(function(e,t){return e[t.keyName]=t,e},{}),$=[B.AlignStyle,D.BackgroundStyle,H.ColorStyle,K.DirectionStyle,Y.FontStyle,z.SizeStyle].reduce(function(e,t){return e[t.keyName]=t,e},{}),J=function(e){function t(e,r){i(this,t);var o=s(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,r));return o.quill.root.addEventListener("paste",o.onPaste.bind(o)),o.container=o.quill.addContainer("ql-clipboard"),o.container.setAttribute("contenteditable",!0),o.container.setAttribute("tabindex",-1),o.matchers=[],V.concat(o.options.matchers).forEach(function(e){var t=w(e,2),n=t[0],i=t[1];(r.matchVisual||i!==g)&&o.addMatcher(n,i)}),o}return l(t,e),x(t,[{key:"addMatcher",value:function(e,t){this.matchers.push([e,t])}},{key:"convert",value:function(e){if("string"==typeof e)return this.container.innerHTML=e.replace(/\>\r?\n +\</g,"><"),this.convert();var t=this.quill.getFormat(this.quill.selection.savedRange.index);if(t[F.default.blotName]){var r=this.container.innerText;return this.container.innerHTML="",(new k.default).insert(r,n({},F.default.blotName,t[F.default.blotName]))}var o=this.prepareMatching(),i=w(o,2),s=i[0],l=i[1],u=f(this.container,s,l);return c(u,"\n")&&null==u.ops[u.ops.length-1].attributes&&(u=u.compose((new k.default).retain(u.length()-1).delete(1))),G.log("convert",this.container.innerHTML,u),this.container.innerHTML="",u}},{key:"dangerouslyPasteHTML",value:function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:M.default.sources.API;if("string"==typeof e)this.quill.setContents(this.convert(e),t),this.quill.setSelection(0,M.default.sources.SILENT);else{var o=this.convert(t);this.quill.updateContents((new k.default).retain(e).concat(o),r),this.quill.setSelection(e+o.length(),M.default.sources.SILENT)}}},{key:"onPaste",value:function(e){var t=this;if(!e.defaultPrevented&&this.quill.isEnabled()){var r=this.quill.getSelection(),o=(new k.default).retain(r.index),n=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(M.default.sources.SILENT),setTimeout(function(){o=o.concat(t.convert()).delete(r.length),t.quill.updateContents(o,M.default.sources.USER),t.quill.setSelection(o.length()-r.length,M.default.sources.SILENT),t.quill.scrollingContainer.scrollTop=n,t.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var e=this,t=[],r=[];return this.matchers.forEach(function(o){var n=w(o,2),i=n[0],s=n[1];switch(i){case Node.TEXT_NODE:r.push(s);break;case Node.ELEMENT_NODE:t.push(s);break;default:[].forEach.call(e.container.querySelectorAll(i),function(e){e[W]=e[W]||[],e[W].push(s)})}}),[t,r]}}]),t}(q.default);J.DEFAULTS={matchers:[],matchVisual:!0},t.default=J,t.matchAttributor=p,t.matchBlot=m,t.matchNewline=v,t.matchSpacing=g,t.matchText=E},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,r){e.exports=r(29)}]).default})}).call(t,r("../node_modules/buffer/index.js").Buffer)},"../node_modules/webpack/buildin/global.js":function(e,t){var r;r=function(){return this}();try{r=r||Function("return this")()||(0,eval)("this")}catch(e){"object"==typeof window&&(r=window)}e.exports=r},"./EditorManager/css/editor.css":function(e,t){},"./EditorManager/js/blot/EmojiBlot.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/createClass.js"),a=o(u),c=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/get.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/inherits.js"),m=o(p),y=r("../node_modules/quill/dist/quill.core.js"),b=o(y),_=b.default.import("blots/embed"),v=function(e){function t(){return(0,l.default)(this,t),(0,d.default)(this,(t.__proto__||(0,i.default)(t)).apply(this,arguments))}return(0,m.default)(t,e),(0,a.default)(t,[{key:"format",value:function(e,r){t.ATTRIBUTES.indexOf(e)>-1?r?this.domNode.setAttribute(e,r):this.domNode.removeAttribute(e):(0,h.default)(t.prototype.__proto__||(0,i.default)(t.prototype),"format",this).call(this,e,r)}}],[{key:"create",value:function(e){var r=(0,h.default)(t.__proto__||(0,i.default)(t),"create",this).call(this,e);return r.setAttribute("data-text",e.text),r.setAttribute("alt",e.text),r.setAttribute("src",e.img),r.setAttribute("width",e.width||24),r.setAttribute("height",e.height||24),r}},{key:"formats",value:function(e){return t.ATTRIBUTES.reduce(function(t,r){return e.hasAttribute(r)&&(t[r]=e.getAttribute(r)),t},{})}},{key:"value",value:function(e){return{img:e.getAttribute("src"),text:e.getAttribute("data-text")}}}]),t}(_);v.blotName="imEmoji",v.className="im-emoji",v.src="img",v.out="img",v.tagName="img",v.ATTRIBUTES=["alt","height","width"],t.default=v,e.exports=t.default},"./EditorManager/js/blot/HaitImageBlot.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/createClass.js"),a=o(u),c=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/get.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/inherits.js"),m=o(p),y=r("../node_modules/quill/dist/quill.core.js"),b=o(y),_=b.default.import("blots/embed"),v=function(e){function t(){return(0,l.default)(this,t),(0,d.default)(this,(t.__proto__||(0,i.default)(t)).apply(this,arguments))}return(0,m.default)(t,e),(0,a.default)(t,null,[{key:"create",value:function(e){var r=(0,h.default)(t.__proto__||(0,i.default)(t),"create",this).call(this,e);return r.setAttribute("data-hait-text",e.text),r.setAttribute("data-hait-account",e.account),r.setAttribute("src",e.img),r}},{key:"value",value:function(e){return{img:e.getAttribute("src"),text:e.getAttribute("data-hait-text"),account:e.getAttribute("data-hait-account")}}}]),t}(_);v.blotName="imHaitImage",v.className="im-image-hait",v.src="img",v.out="img",v.tagName="img",t.default=v,e.exports=t.default},"./EditorManager/js/blot/HaitImageBlotNew.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/createClass.js"),a=o(u),c=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/get.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/inherits.js"),m=o(p),y=r("../node_modules/quill/dist/quill.core.js"),b=o(y),_=b.default.import("blots/embed"),v=function(e){function t(){return(0,l.default)(this,t),(0,d.default)(this,(t.__proto__||(0,i.default)(t)).apply(this,arguments))}return(0,m.default)(t,e),(0,a.default)(t,null,[{key:"create",value:function(e){var r=(0,h.default)(t.__proto__||(0,i.default)(t),"create",this).call(this,e);return r.setAttribute("data-hait-key",e.key),r.setAttribute("data-hait-value",e.value),e.name&&r.setAttribute("data-hait-name",e.name),e.ext&&r.setAttribute("data-hait-ext",e.ext),r.setAttribute("src",e.img),r.setAttribute("data-hait-empnumber",e.empNumber),r.setAttribute("data-hait-docid",e.docId),r.setAttribute("data-hait-docimg",e.docImg),r.setAttribute("data-hait-property",e.property),r}},{key:"value",value:function(e){return{img:e.getAttribute("src"),key:e.getAttribute("data-hait-key"),value:e.getAttribute("data-hait-value"),name:e.getAttribute("data-hait-name"),ext:e.getAttribute("data-hait-ext"),empNumber:e.getAttribute("data-hait-empnumber"),docId:e.getAttribute("data-hait-docid"),docImg:e.getAttribute("data-hait-docimg"),property:e.getAttribute("data-hait-property")}}}]),t}(_);v.blotName="imHaitImageNew",v.className="im-image-hait-new",v.src="img",v.out="img",v.tagName="img",t.default=v,e.exports=t.default},"./EditorManager/js/blot/HaitSpanBlot.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/createClass.js"),a=o(u),c=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/get.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/inherits.js"),m=o(p),y=r("../node_modules/quill/dist/quill.core.js"),b=o(y),_=b.default.import("blots/embed"),v=function(e){function t(e){(0,l.default)(this,t);var r=(0,d.default)(this,(t.__proto__||(0,i.default)(t)).call(this,e)),o=r.domNode.getAttribute("data-hait-color"),n=r.domNode.querySelector("span[contenteditable]");return n&&(n.style.color=o||"#24aeef"),r}return(0,m.default)(t,e),(0,a.default)(t,null,[{key:"create",value:function(e){var r=(0,h.default)(t.__proto__||(0,i.default)(t),"create",this).call(this,e);return r.setAttribute("data-hait-text",e.text),r.setAttribute("data-hait-account",e.account),r.setAttribute("data-hait-color",e.color||"#24aeef"),r.innerText=e.text,r}},{key:"value",value:function(e){return{text:e.getAttribute("data-hait-text"),account:e.getAttribute("data-hait-account")}}}]),t}(_);v.blotName="imHaitSpan",v.className="im-span-hait",v.tagName="span",t.default=v,e.exports=t.default},"./EditorManager/js/blot/ImageBlot.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/helpers/defineProperty.js"),i=o(n),s=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),a=o(u),c=r("../node_modules/babel-runtime/helpers/createClass.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/get.js"),m=o(p),y=r("../node_modules/babel-runtime/helpers/inherits.js"),b=o(y),_=r("../node_modules/quill/dist/quill.core.js"),v=o(_),g=v.default.import("blots/embed"),j="maxHeight",E="maxWidth",O="[图片]",w=function(e){function t(){return(0,a.default)(this,t),(0,h.default)(this,(t.__proto__||(0,l.default)(t)).apply(this,arguments))}return(0,b.default)(t,e),(0,d.default)(t,[{key:"format",value:function(e,r){e!==j&&e!==E||(this.domNode.style[e]=r?r+"px":""),t.ATTRIBUTES.indexOf(e)>-1?r?this.domNode.setAttribute(e,r):this.domNode.removeAttribute(e):(0,m.default)(t.prototype.__proto__||(0,l.default)(t.prototype),"format",this).call(this,e,r)}}],[{key:"create",value:function(e){var r=(0,m.default)(t.__proto__||(0,l.default)(t),"create",this).call(this,e);return r.setAttribute("data-text",e.text||O),r.setAttribute("alt",e.text||O),r.setAttribute("src",e.img),e.base64&&r.setAttribute("data-base64",e.base64),r.style[j]=e[j]?e[j]+"px":"70px",r.style[E]=e[E]?e[E]+"px":"97%",r.setAttribute(j,r.style[j]),r.setAttribute(E,r.style[E]),r}},{key:"formats",value:function(e){return t.ATTRIBUTES.reduce(function(t,r){return e.hasAttribute(r)&&(t[r]=e.getAttribute(r),t[r]=t[r]||"alt"!==r?t[r]:O),t},{})}},{key:"value",value:function(e){return{node:e,img:e.getAttribute("src"),text:e.getAttribute("data-text")||O,base64:e.getAttribute("data-base64")||O}}},{key:"valueDelta",value:function(e){return"string"==typeof e?[(0,i.default)({},t.blotName,{img:e,text:O}),(0,i.default)({alt:O},j,70)]:[(0,i.default)({},t.blotName,{img:e.src||"",base64:e.base64||"",text:O}),(0,i.default)({alt:O},j,70)]}}]),t}(g);w.blotName="imImage",w.tagName="img",w.src="img",w.out="node",w.ATTRIBUTES=["alt",j,E],t.default=w,e.exports=t.default},"./EditorManager/js/blot/defaultImageBlot.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=o(s),u=r("../node_modules/babel-runtime/helpers/createClass.js"),a=o(u),c=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/get.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/inherits.js"),m=o(p),y=r("../node_modules/quill/dist/quill.core.js"),b=o(y),_=b.default.import("blots/embed"),v=function(e){function t(){return(0,l.default)(this,t),(0,d.default)(this,(t.__proto__||(0,i.default)(t)).apply(this,arguments))}return(0,m.default)(t,e),(0,a.default)(t,null,[{key:"create",value:function(e){var r=(0,h.default)(t.__proto__||(0,i.default)(t),"create",this).call(this,e);return r.setAttribute("data-key",e.key),r.setAttribute("data-value",e.value),r.setAttribute("src",e.img),r}},{key:"value",value:function(e){return{img:e.getAttribute("src"),key:e.getAttribute("data-key"),value:e.getAttribute("data-value")}}}]),t}(_);v.blotName="imDefaultImage",v.className="im-image-default",v.src="img",v.out="img",v.tagName="img",t.default=v,e.exports=t.default},"./EditorManager/js/index.js":function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=r("../node_modules/quill/dist/quill.core.js"),n=function(e){return e&&e.__esModule?e:{default:e}}(o),i=r("./EditorManager/js/main/main.js"),s=n.default.import("delta");window.ImManager={hasEditor:i.hasEditor,createEditor:i.createEditor,activeEditor:i.activeEditor,getBounds:i.getBounds,getSelection:i.getSelection,clear:i.clear,focusEditor:i.focusEditor,isActive:i.isActive,insertHaitSpan:i.insertHaitSpan,insertHaitImage:i.insertHaitImage,insertHaitImageNew:i.insertHaitImageNew,insertDefaultImageBlot:i.insertDefaultImageBlot,insertImage:i.insertImage,insertEmoji:i.insertEmoji,insertText:i.insertText,setContents:i.setContents,getContents:i.getContents,Delta:s,doDrop:i.doDrop,update:i.update,blurEditor:i.blurEditor,safeBlurEditor:i.safeBlurEditor,dangerouslyPasteHTML:i.dangerouslyPasteHTML,pasteFile:i.pasteFile,deleteText:i.deleteText,deleteLastOneText:i.deleteLastOneText,getText:i.getText,insertTextByIndex:i.insertTextByIndex,dangerouslyPasteHTMLByIndex:i.dangerouslyPasteHTMLByIndex,setSelection:i.setSelection,setPlaceholder:i.setPlaceholder},t.default=void 0,e.exports=t.default},"./EditorManager/js/main/ImEditorRegister.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/quill/dist/quill.core.js"),i=o(n),s=r("./EditorManager/js/blot/EmojiBlot.js"),l=o(s),u=r("./EditorManager/js/blot/ImageBlot.js"),a=o(u),c=r("./EditorManager/js/blot/HaitImageBlot.js"),d=o(c),f=r("./EditorManager/js/blot/HaitImageBlotNew.js"),h=o(f),p=r("./EditorManager/js/blot/HaitSpanBlot.js"),m=o(p),y=r("./EditorManager/js/module/RootAttrModule.js"),b=o(y),_=r("./EditorManager/js/module/MouseModule.js"),v=o(_),g=r("./EditorManager/js/module/DropAndClipModule.js"),j=o(g),E=r("./EditorManager/js/module/HaitEventModule.js"),O=o(E),w=r("./EditorManager/js/blot/defaultImageBlot.js"),x=o(w);i.default.register(l.default),i.default.register(d.default),i.default.register(h.default),i.default.register(a.default),i.default.register(x.default),i.default.register(m.default);i.default.register("modules/rootAttr",b.default,!0),i.default.register("modules/mouse",v.default,!0),i.default.register("modules/haitEvent",O.default,!0),i.default.register("modules/clipboard",j.default,!0),t.default={IM_FORMAT_EMOJI:l.default.blotName,IM_FORMAT_IMAGE:a.default.blotName,IM_FORMAT_HAIT_IMAGE:d.default.blotName,IM_FORMAT_HAIT_IMAGE_New:h.default.blotName,IM_FORMAT_DEFAULT_IMAGE:x.default.blotName,IM_FORMAT_HAIT_SPAN:m.default.blotName,IM_MODULE_ROOT_ATTR:"modules/rootAttr",IM_MODULE_MOUSE:"modules/mouse",IM_MODULE_HAIT_EVENT:"modules/haitEvent"},e.exports=t.default},"./EditorManager/js/main/editor.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new M.default("#"+e,{debug:"warn",modules:{haitEvent:!0,rootAttr:{spellcheck:"false"},mouse:!0,keyboard:{bindings:t.bindings||{}},clipboard:{matchers:t.matchers||[],dealImg:t.dealImg||void 0,isValidImg:t.isValidImg||void 0}}})}function i(e){e.focus()}function s(e){var t=e.getSelection();if(0===t.index)return!0;var r=e.getContents(t.index-1,1);return!!(r.ops&&r.ops.length>0)&&"\n"===r.ops[0].insert}function l(e,t,r){e.hasFocus()||e.focus();var o=e.getSelection();o.length&&e.deleteText(o.index,o.length),t.insertRange?(e.insertEmbed(t.insertRange.index,r,t),e.setSelection(o.index+1,0)):(o=e.getSelection(),e.insertEmbed(o.index,r,t),e.setSelection(o.index+1,0))}function u(e,t){l(e,t,I.default.IM_FORMAT_EMOJI)}function a(e,t){l(e,t,I.default.IM_FORMAT_IMAGE)}function c(e,t,r){r&&e.deleteText(r.index,r.length),l(e,t,I.default.IM_FORMAT_HAIT_IMAGE)}function d(e,t){t.range&&e.deleteText(t.range.index,t.range.length),l(e,t,I.default.IM_FORMAT_HAIT_IMAGE_New)}function f(e,t){t.range&&e.deleteText(t.range.index,t.range.length),l(e,t,I.default.IM_FORMAT_DEFAULT_IMAGE)}function h(e,t){l(e,t,I.default.IM_FORMAT_HAIT_SPAN)}function p(e,t){e.hasFocus()||e.focus(),e.insertText(e.getSelection().index,t)}function m(e,t,r){e.hasFocus()||e.focus();e.getSelection();e.insertText(t,r)}function y(e){e.hasFocus()||e.focus(),e.deleteText(e.getSelection().index-1,1)}function b(e,t,r){e.hasFocus()||e.focus(),e.deleteText(t,r)}function _(e,t,r){return e.getText(t,r)}function v(e,t,r){return e.getContents(t,r)}function g(e){e.setContents(arguments.length>1&&void 0!==arguments[1]?arguments[1]:new R,M.default.sources.USER)}function j(e){e.setContents(new R),e.setSelection(0,0),e.history.clear()}function E(e,t){return e.getSelection(t)}function O(e,t){return e.setSelection(t)}function w(e,t,r){return e.getBounds(t,r)}function x(e,t){t.clipboard.onDrop(e)}function A(e,t){e.clipboard.dangerouslyPasteHTML(t)}function N(e,t,r){e.clipboard.dangerouslyPasteHTMLByIndex(t,r)}function T(e,t){t.clipboard.pasteFile(e)}function k(e){return e.update()}function S(e){return e.blur()}function P(e,t){var r=e.container.querySelector(".ql-editor.ql-blank");r&&r.setAttribute("data-placeholder",t)}Object.defineProperty(t,"__esModule",{value:!0}),t.createEditor=n,t.focusEditor=i,t.isLineFirst=s,t.insertEmbed=l,t.insertEmoji=u,t.insertImage=a,t.insertHaitImage=c,t.insertHaitImageNew=d,t.insertDefaultImageBlot=f,t.insertHaitSpan=h,t.insertText=p,t.insertTextByIndex=m,t.deleteLastOneText=y,t.deleteText=b,t.getText=_,t.getContents=v,t.setContents=g,t.clear=j,t.getSelection=E,t.setSelection=O,t.getBounds=w,t.doDrop=x,t.dangerouslyPasteHTML=A,t.dangerouslyPasteHTMLByIndex=N,t.pasteFile=T,t.update=k,t.blur=S,t.setPlaceholder=P;var C=r("../node_modules/quill/dist/quill.core.js"),M=o(C),L=r("./EditorManager/js/main/ImEditorRegister.js"),I=o(L);window.Quill=M.default;var R=M.default.import("delta")},"./EditorManager/js/main/main.js":function(e,t,r){"use strict";function o(e){return!!R[e]}function n(e,t){var r=document.createElement("div");r.id=e,r.classList.add(q),document.body.appendChild(r);var o=(0,M.createEditor)(e,t);return R[e]={$container:r,quill:o},o}function i(e){var t=R[e].quill,r=document.getElementById(I);return r&&r.classList.add(q),I=e,r=document.getElementById(I),r&&r.classList.remove(q),(0,M.focusEditor)(t),t}function s(e,t,r){return(0,M.getBounds)(R[e].quill,t,r)}function l(e){return(0,M.getSelection)(R[e].quill,arguments.length>1&&void 0!==arguments[1]&&arguments[1])}function u(e,t){return(0,M.setSelection)(R[e].quill,t)}function a(e,t){return(0,M.setPlaceholder)(R[e].quill,t)}function c(e){return(0,M.clear)(R[e].quill)}function d(e){return(0,M.focusEditor)(R[e].quill)}function f(e){return I===e}function h(e,t){return(0,M.insertHaitSpan)(R[e].quill,t)}function p(e,t,r){return(0,M.insertHaitImage)(R[e].quill,t,r)}function m(e){return(0,M.insertHaitImageNew)(R[e.id].quill,e)}function y(e){return(0,M.insertDefaultImageBlot)(R[e.id].quill,e)}function b(e,t){return(0,M.insertImage)(R[e].quill,t)}function _(e,t){return(0,M.insertEmoji)(R[e].quill,t)}function v(e,t){return(0,M.insertText)(R[e].quill,t)}function g(e,t,r){return(0,M.insertTextByIndex)(R[e].quill,t,r)}function j(e,t,r){return(0,M.getText)(R[e].quill,t,r)}function E(e,t,r){return(0,M.deleteText)(R[e].quill,t,r)}function O(e){return(0,M.deleteLastOneText)(R[e].quill)}function w(e,t){return(0,M.setContents)(R[e].quill,t)}function x(e,t,r){return(0,M.getContents)(R[e].quill,t,r)}function A(e){return(0,M.doDrop)(e,R[I].quill)}function N(e){return(0,M.update)(R[e].quill)}function T(e,t){return(0,M.dangerouslyPasteHTML)(R[e].quill,t)}function k(e,t,r){return(0,M.dangerouslyPasteHTMLByIndex)(R[e].quill,t,r)}function S(e){return(0,M.pasteFile)(e,R[I].quill)}function P(e){return(0,M.blur)(R[e].quill)}function C(e){var t=R[e].quill;t&&t.selection&&t.selection&&t.selection.savedRange&&0===t.selection.savedRange.length&&(0,M.blur)(t)}Object.defineProperty(t,"__esModule",{value:!0}),t.hasEditor=o,t.createEditor=n,t.activeEditor=i,t.getBounds=s,t.getSelection=l,t.setSelection=u,t.setPlaceholder=a,t.clear=c,t.focusEditor=d,t.isActive=f,t.insertHaitSpan=h,t.insertHaitImage=p,t.insertHaitImageNew=m,t.insertDefaultImageBlot=y,t.insertImage=b,t.insertEmoji=_,t.insertText=v,t.insertTextByIndex=g,t.getText=j,t.deleteText=E,t.deleteLastOneText=O,t.setContents=w,t.getContents=x,t.doDrop=A,t.update=N,t.dangerouslyPasteHTML=T,t.dangerouslyPasteHTMLByIndex=k,t.pasteFile=S,t.blurEditor=P,t.safeBlurEditor=C,r("../node_modules/quill/dist/quill.core.css");var M=r("./EditorManager/js/main/editor.js");r("./EditorManager/css/editor.css");var L=document.createElement("link");L.rel="stylesheet",L.href="./editor.styles.css",document.head.appendChild(L);var I=void 0,R={},q="hidden"},"./EditorManager/js/module/DropAndClipModule.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/helpers/toConsumableArray.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/defineProperty.js"),l=o(s),u=r("../node_modules/babel-runtime/core-js/promise.js"),a=o(u),c=r("../node_modules/babel-runtime/core-js/object/keys.js"),d=o(c),f=r("../node_modules/babel-runtime/helpers/typeof.js"),h=o(f),p=r("../node_modules/babel-runtime/helpers/slicedToArray.js"),m=o(p),y=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),b=o(y),_=r("../node_modules/babel-runtime/helpers/createClass.js"),v=o(_),g=r("../node_modules/quill/dist/quill.core.js"),j=o(g),E=r("./EditorManager/js/uitls.js"),O=r("./EditorManager/js/blot/ImageBlot.js"),w=o(O),x=j.default.import("delta"),A=j.default.import("parchment");j.default.events.DROP_AND_COPY_FILE="drop_and_copy_file",j.default.events.DROP_AND_COPY_INVALID_IMAGE="drop_and_copy_invalid_image",j.default.events.PASTE_FILE="paste_file";var N=[[Node.TEXT_NODE,E.matchText],[Node.TEXT_NODE,E.matchNewline],["br",E.matchBreak],[Node.ELEMENT_NODE,E.matchNewline],[Node.ELEMENT_NODE,E.matchBlot],[Node.ELEMENT_NODE,E.matchSpacing],[Node.ELEMENT_NODE,E.matchStyles],["li",E.matchIndent],["style",E.matchIgnore]],T=function(){function e(t,r){var o=this;(0,b.default)(this,e),this.quill=t,this.options=r,this.quill.root.addEventListener("drop",this.onDrop.bind(this)),this.quill.root.addEventListener("dragstart",this.onDragstart.bind(this)),this.quill.root.addEventListener("paste",this.onPaste.bind(this)),this.container=this.quill.addContainer("ql-clipboard"),this.container.setAttribute("contenteditable","true"),this.container.setAttribute("tabindex","-1"),this.matchers=[],N.concat(this.options.matchers).forEach(function(e){var t=(0,m.default)(e,2),n=t[0],i=t[1];(r.matchVisual||i!==E.matchSpacing)&&o.addMatcher(n,i)})}return(0,v.default)(e,[{key:"addMatcher",value:function(e,t){this.matchers.push([e,t])}},{key:"convert",value:function(e,t){var r=this;"string"!=typeof e||t||(this.container.innerHTML=e.replace(/>\r?\n +</g,"><")),t&&-1!=e.indexOf("&#65279;")&&(this.container.innerHTML=e);var o=this.prepareMatching(),n=(0,m.default)(o,2),i=n[0],s=n[1],l=(0,E.traverse)(this.container,i,s);(0,E.deltaEndsWith)(l,"\n")&&null==l.ops[l.ops.length-1].attributes&&(l=l.compose((new x).retain(l.length()-1).delete(1))),this.container.innerHTML="";var u=[],c={},f={},p={};return l&&l.ops&&l.ops.length>0&&l.ops.forEach(function(e,t){if("object"===(0,h.default)(e.insert)){var o=(0,d.default)(e.insert)[0],n=A.query(o);n&&n.src&&"imHaitImage"!=o&&"imHaitImageNew"!=o&&"imEmoji"!=o&&(u.push(r.options.dealImg(e.insert[o][n.src])),f[u.length-1]=e.insert,p[u.length-1]=t,c[u.length-1]=n)}}),u.length>0?a.default.all(u).then(function(e){for(var t=0;t<e.length;t++){var r=c[t];e[t]?"string"==typeof e[t]?f[t][r.blotName][r.src]=e[t]:(f[t][r.blotName][r.src]=e[t].src,f[t][r.blotName].text=e[t].text,e[t].base64&&(f[t][r.blotName].base64=e[t].base64)):delete l.ops[p[t]]}for(var o=0;o<l.ops.length;o++)l.ops[o]||(l.ops.splice(o,1),o--);return l}):a.default.resolve(l)}},{key:"dangerouslyPasteHTML",value:function(e){var t=this;this.quill.hasFocus()||this.quill.focus();var r=this.quill.getSelection(),o=(new x).retain(r.index),n=this.quill.scrollingContainer.scrollTop;this.convert(e).then(function(e){o=o.concat(e).delete(r.length),t.quill.updateContents(o,j.default.sources.USER),t.quill.setSelection(o.length()-r.length,j.default.sources.USER),t.quill.scrollingContainer.scrollTop=n,t.quill.focus()}).catch(function(e){return t.quill.emitter.emit("error",e)})}},{key:"dangerouslyPasteHTMLByIndex",value:function(e,t){var r=this;if("string"==typeof e)this.quill.setContents(this.convertOld(e),t),this.quill.setSelection(0,j.default.sources.SILENT);else{var o=(new x).retain(e);this.convert(t,!0).then(function(t){o=o.concat(t),r.quill.updateContents(o,j.default.sources.USER),r.quill.setSelection(e+t.length(),j.default.sources.SILENT),r.quill.focus()})}}},{key:"convertOld",value:function(e){if("string"==typeof e)return this.container.innerHTML=e.replace(/\>\r?\n +\</g,"><"),this.convert();var t=this.quill.getFormat(this.quill.selection.savedRange.index);if(t[CodeBlock.blotName]){var r=this.container.innerText;return this.container.innerHTML="",(new x).insert(r,(0,l.default)({},CodeBlock.blotName,t[CodeBlock.blotName]))}var o=this.prepareMatching(),n=(0,m.default)(o,2),i=n[0],s=n[1],u=(0,E.traverse)(this.container,i,s);return(0,E.deltaEndsWith)(u,"\n")&&null==u.ops[u.ops.length-1].attributes&&(u=u.compose((new x).retain(u.length()-1).delete(1))),debug.log("convert",this.container.innerHTML,u),this.container.innerHTML="",u}},{key:"onPaste",value:function(e){var t=this;if(!e.defaultPrevented&&this.quill.isEnabled()){if(this.quill.hasFocus()||this.quill.focus(),this.dropAndPaste(e))return void e.preventDefault();var r=this.quill.getSelection(),o=(new x).retain(r.index),n=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(j.default.sources.SILENT),setTimeout(function(){t.convert().then(function(i){i&&i.ops&&0==i.ops.length&&t.pasteFile(e),o=o.concat(i).delete(r.length),t.quill.updateContents(o,j.default.sources.USER),t.quill.setSelection(o.length()-r.length,j.default.sources.USER),t.quill.scrollingContainer.scrollTop=n,t.quill.focus()}).catch(function(e){return t.quill.emitter.emit("error",e)})},1)}}},{key:"pasteFile",value:function(e){this.quill.emitter.emit("paste_file",e)}},{key:"onDragstart",value:function(e){e.dataTransfer.setData("im.drag.input",!0)}},{key:"onDrop",value:function(e){var t=this;if(!e.defaultPrevented&&this.quill.isEnabled()){if(e.dataTransfer.getData("im.drag.input"))return void setTimeout(function(){return t.quill.selection.update(j.default.sources.USER)},1);if(e.stopPropagation(),e.preventDefault(),this.quill.hasFocus()||this.quill.focus(),!this.dropAndPaste(e)){var r=e.dataTransfer.getData("text/html")||e.dataTransfer.getData("text/plain"),o=this.quill.getSelection(),n=(new x).retain(o.index),i=this.quill.scrollingContainer.scrollTop;this.convert(r).then(function(e){n=n.concat(e).delete(o.length),t.quill.updateContents(n,j.default.sources.USER),t.quill.setSelection(n.length()-o.length,j.default.sources.USER),t.quill.scrollingContainer.scrollTop=i,t.quill.focus()}).catch(function(e){return t.quill.emitter.emit("error",e)})}}}},{key:"dropAndPaste",value:function(e){var t=this,r=e.clipboardData||e.dataTransfer,o=r&&r.items,n=[],s=[],l=[];if(2==o.length&&"string"==o[0].kind&&"string"==o[1].kind){var u=r.getData("text/html"),c=r.getData("text/plain"),d=document.createElement("div");return d.innerHTML=u,!(!d.firstElementChild||"A"!=d.firstElementChild.tagName||d.firstElementChild.innerHTML==c||d.firstElementChild!=d.lastElementChild)&&(this.dangerouslyPasteHTML(c.replace(/&/g,"&amp;")),!0)}if(o&&o.length>0)for(var f=((new Date).getTime(),-1),h=0;h<o.length;h++){var p=o[h].getAsFile();if(p){var m=p.path?1:2;if(-1==f&&"file"===o[h].kind&&(f=p.path?1:2),f!=m)continue}"file"===o[h].kind&&o[h].type.indexOf("image/")>-1&&p&&p.size>0?this.options.isValidImg(p)?n.push(p):l.push(p):"file"===o[h].kind&&p&&p.size>0&&s.push(p)}var y=!1;if(s.length>0&&(this.quill.emitter.emit("drop_and_copy_file",[].concat(s,l,n)),y=!0),0===s.length&&l.length>0&&(this.quill.emitter.emit("drop_and_copy_invalid_image",l),y=!0),0===s.length&&n.length>0){y=!0;var b=this.quill.getSelection(),_=(new x).retain(b.index),v=this.quill.scrollingContainer.scrollTop;a.default.all(n.map(function(e){return(0,E.readBlobAsDataURL)(e)})).then(function(e){var r=[];e.reduce(function(e,o){r.push(new a.default(function(r){o?t.options.dealImg(o).then(function(e){var t;r((t=new x).insert.apply(t,(0,i.default)(w.default.valueDelta(e))))}):r(e)}))},new x);a.default.all(r).then(function(e){var r=new x;e.map(function(e){r=r.concat(e)}),_=_.concat(r).delete(b.length),t.quill.updateContents(_,j.default.sources.USER),t.quill.setSelection(_.length()-b.length,j.default.sources.SILENT),t.quill.scrollingContainer.scrollTop=v,t.quill.focus()})}).catch(function(e){return t.quill.emitter.emit("error",e)})}return y}},{key:"prepareMatching",value:function(){var e=this,t=[],r=[];return this.matchers.forEach(function(o){var n=(0,m.default)(o,2),i=n[0],s=n[1];switch(i){case Node.TEXT_NODE:r.push(s);break;case Node.ELEMENT_NODE:t.push(s);break;default:[].forEach.call(e.container.querySelectorAll(i),function(e){e["__ql-matcher"]=e["__ql-matcher"]||[],e["__ql-matcher"].push(s)})}}),[t,r]}}]),e}();T.DEFAULTS={matchers:[],matchVisual:!0,dealImg:function(e){return new a.default("string"==typeof e?function(t){return setTimeout(t(e),1)}:function(t){return setTimeout(t(e.getAttribute("src")),1)})},isValidImg:function(e){return e.size<2097152}},t.default=T,e.exports=t.default},"./EditorManager/js/module/HaitEventModule.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),i=o(n),s=r("../node_modules/quill/dist/quill.core.js"),l=o(s);l.default.events.EDITOR_HAIT_KEYUP="editor_hait_keyup";l.default.events.EDITOR_CALL_KEYUP="editor_call_keyup",t.default=function e(t,r){var o=this;(0,i.default)(this,e),this.quill=t,this.emitter=t.emitter,this.root=t.root,this.scroll=t.scroll,this.selection=t.selection,this.options=r,this.root.addEventListener("keyup",function(e){var t=!1,r=null;if("@"===e.key||"＠"===e.key)t=!0,r=o.quill.getSelection();else if(e.shiftKey&&50===e.keyCode)t=!0,r=o.quill.getSelection();else if(e.shiftKey&&229===e.keyCode||50===e.keyCode){r=o.quill.getSelection();var n=o.quill.getContents(r.index-1,1);n&&n.ops&&1===n.ops.length&&n.ops[0]&&("@"===n.ops[0].insert||"＠"===n.ops[0].insert)&&(t=!0)}else if(51==e.keyCode&&!e.altKey&&!e.ctrlKey){r=o.quill.getSelection();var i=o.quill.getContents(r.index-1,1);(e.shiftKey||!e.shiftKey&&i&&i.ops&&1===i.ops.length&&i.ops[0]&&"#"==i.ops[0].insert)&&(r.index-=1,o.emitter.emit(l.default.events.EDITOR_CALL_KEYUP,r))}t&&(r.index-=1,o.emitter.emit(l.default.events.EDITOR_HAIT_KEYUP,r))},!1)},e.exports=t.default},"./EditorManager/js/module/MouseModule.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/createClass.js"),l=o(s),u=r("../node_modules/quill/dist/quill.core.js"),a=o(u),c="editor_context_menu",d="img_dblclick";a.default.events.EDITOR_CONTEXT_MENU=c,a.default.events.IMG_DBLCLICK=d;var f=a.default.import("parchment");t.default=function(){function e(t,r){var o=this;(0,i.default)(this,e),this.quill=t,this.emitter=t.emitter,this.root=t.root,this.scroll=t.scroll,this.selection=t.selection,this.options=r,this.root.addEventListener("mouseup",function(e){o.emitter.emit("mouseup",e)},!1),this.root.addEventListener("mousemove",function(e){o.emitter.emit("mousemove",e)},!1),this.root.addEventListener("mousedown",function(e){if(2===e.button)return e.preventDefault(),void e.stopPropagation();"IMG"===e.target.nodeName.toUpperCase()&&o.selectEmbed(e)},!1),this.root.addEventListener("contextmenu",function(e){e.stopPropagation(),e.preventDefault();var t=o.quill.getSelection(!0);if(t){var r=t&&t.length>0,n="IMG"===e.target.nodeName.toUpperCase();if(r||n)if(r||n)if(r&&!n);else{var i=f.find(e.target,!0),s=i.offset(o.scroll);s>=t.index&&s<t.index+t.length||(o.selectEmbed(e),t=o.quill.getSelection())}else o.selectEmbed(e),t=o.quill.getSelection();else;o.emitter.emit(c,o.quill.getContents(t.index,t.length),e)}},!1),this.root.addEventListener("dblclick",function(e){if("IMG"===e.target.nodeName.toUpperCase()){e.preventDefault();for(var t=e.target,r=o.quill.root.querySelectorAll("img"),n=-1,i=0;i<r.length;i++)t===r[i]&&(n=i);-1!==n&&o.emitter.emit(d,{index:n,imgs:r})}},!1)}return(0,l.default)(e,[{key:"selectEmbed",value:function(e){var t=f.find(e.target,!0),r=document.querySelector(".ql-embed-selected");if(r&&r.classList.remove("ql-embed-selected"),t instanceof f.Embed){t.domNode.classList.add("ql-embed-selected");this.selection.setRange({index:t.offset(this.scroll),length:t.length()},a.default.sources.USER),e.stopPropagation()}}}]),e}(),e.exports=t.default},"./EditorManager/js/module/RootAttrModule.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var n=r("../node_modules/babel-runtime/core-js/object/keys.js"),i=o(n),s=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=o(s);t.default=function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};(0,l.default)(this,e),(0,i.default)(r).forEach(function(e){t.root.setAttribute(e,r[e])})},e.exports=t.default},"./EditorManager/js/uitls.js":function(e,t,r){"use strict";function o(e){return e&&e.__esModule?e:{default:e}}function n(e,t,r){return"object"===(void 0===t?"undefined":(0,A.default)(t))?(0,w.default)(t).reduce(function(e,r){return n(e,r,t[r])},e):e.reduce(function(e,o){return o.attributes&&o.attributes[t]?e.push(o):e.insert(o.insert,(0,S.default)({},(0,E.default)({},t,r),o.attributes))},new P)}function i(e){return e.nodeType!==Node.ELEMENT_NODE?{}:e["__ql-computed-style"]=e["__ql-computed-style"]||window.getComputedStyle(e)}function s(e,t){for(var r="",o=e.ops.length-1;o>=0&&r.length<t.length;--o){var n=e.ops[o];if("string"!=typeof n.insert)break;r=n.insert+r}return r.slice(-1*t.length)===t}function l(e){return 0!==e.childNodes.length&&["block","list-item"].indexOf(i(e).display)>-1}function u(e,t,r){return e.nodeType===e.TEXT_NODE?r.reduce(function(t,r){return r(e,t)},new P):e.nodeType===e.ELEMENT_NODE?[].reduce.call(e.childNodes||[],function(o,n){var i=u(n,t,r);return n.nodeType===e.ELEMENT_NODE&&(i=t.reduce(function(e,t){return t(n,e)},i),i=(n[M]||[]).reduce(function(e,t){return t(n,e)},i)),o.concat(i)},new P):new P}function a(e,t,r){return n(r,e,!0)}function c(e,t){var r=C.query(e);if(null==r)return t;if(r.prototype instanceof C.Embed){var o={},i=r.value(e);null!=i&&(o[r.blotName]=i,t=(new P).insert(o,r.formats(e)))}else"function"==typeof r.formats&&(t=n(t,r.blotName,r.formats(e)));return t}function d(e,t){return s(t,"\n")||t.insert("\n"),t}function f(){return new P}function h(e,t){var r=C.query(e);if(null==r||"list-item"!==r.blotName||!s(t,"\n"))return t;for(var o=-1,n=e.parentNode;!n.classList.contains("ql-clipboard");)"list"===(C.query(n)||{}).blotName&&(o+=1),n=n.parentNode;return o<=0?t:t.compose((new P).retain(t.length()-1).retain(1,{indent:o}))}function p(e,t){return s(t,"\n")||(l(e)||t.length()>0&&e.nextSibling&&l(e.nextSibling))&&t.insert("\n"),t}function m(e,t){if(l(e)&&null!=e.nextElementSibling&&!s(t,"\n\n")){e.nextElementSibling.offsetTop>e.offsetTop*****(e.offsetHeight+parseFloat(i(e).marginTop)+parseFloat(i(e).marginBottom))&&t.insert("\n")}return t}function y(e,t){var r={},o=e.style||{};return o.fontStyle&&"italic"===i(e).fontStyle&&(r.italic=!0),o.fontWeight&&(i(e).fontWeight.startsWith("bold")||parseInt(i(e).fontWeight,10)>=700)&&(r.bold=!0),(0,w.default)(r).length>0&&(t=n(t,r)),parseFloat(o.textIndent||0)>0&&(t=(new P).insert("\t").concat(t)),t}function b(e,t){var r=e.data;if("O:P"===e.parentNode.tagName)return t.insert(r.trim());if(0===r.trim().length&&e.parentNode.classList.contains("ql-clipboard"))return t;if(!i(e.parentNode).whiteSpace.startsWith("pre")){r=r.replace(/\r\n/g," ").replace(/\n/g," ").replace(/\t/g,"    ")}return t.insert(r)}function _(e){return 0===e.size?g.default.resolve(null):new g.default(function(t){var r=new FileReader;r.onload=function(e){t(e.target.result)},r.onerror=function(e){console.warn("readBlobAsDataURL err.",e),t(null)},r.readAsDataURL(e)})}Object.defineProperty(t,"__esModule",{value:!0});var v=r("../node_modules/babel-runtime/core-js/promise.js"),g=o(v),j=r("../node_modules/babel-runtime/helpers/defineProperty.js"),E=o(j),O=r("../node_modules/babel-runtime/core-js/object/keys.js"),w=o(O),x=r("../node_modules/babel-runtime/helpers/typeof.js"),A=o(x);t.applyFormat=n,t.computeStyle=i,t.deltaEndsWith=s,t.isLine=l,t.traverse=u,t.matchAlias=a,t.matchBlot=c,t.matchBreak=d,t.matchIgnore=f,t.matchIndent=h,t.matchNewline=p,t.matchSpacing=m,t.matchStyles=y,t.matchText=b,t.readBlobAsDataURL=_;var N=r("../node_modules/quill/dist/quill.core.js"),T=o(N),k=r("../node_modules/extend/index.js"),S=o(k),P=T.default.import("delta"),C=T.default.import("parchment"),M="__ql-matcher"}})});