!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define([],o):"object"==typeof exports?exports.ImEditor=o():e.ImEditor=o()}("undefined"!=typeof self?self:this,function(){return function(e){function o(t){if(r[t])return r[t].exports;var s=r[t]={i:t,l:!1,exports:{}};return e[t].call(s.exports,s,s.exports,o),s.l=!0,s.exports}var r={};return o.m=e,o.c=r,o.d=function(e,r,t){o.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:t})},o.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(r,"a",r),r},o.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},o.p="/sdk/imeditor/",o(o.s="./ImEditor/js/index.js")}({"../node_modules/babel-runtime/core-js/object/create.js":function(e,o,r){e.exports={default:r("../node_modules/core-js/library/fn/object/create.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/define-property.js":function(e,o,r){e.exports={default:r("../node_modules/core-js/library/fn/object/define-property.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/get-prototype-of.js":function(e,o,r){e.exports={default:r("../node_modules/core-js/library/fn/object/get-prototype-of.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/object/set-prototype-of.js":function(e,o,r){e.exports={default:r("../node_modules/core-js/library/fn/object/set-prototype-of.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/symbol.js":function(e,o,r){e.exports={default:r("../node_modules/core-js/library/fn/symbol/index.js"),__esModule:!0}},"../node_modules/babel-runtime/core-js/symbol/iterator.js":function(e,o,r){e.exports={default:r("../node_modules/core-js/library/fn/symbol/iterator.js"),__esModule:!0}},"../node_modules/babel-runtime/helpers/classCallCheck.js":function(e,o,r){"use strict";o.__esModule=!0,o.default=function(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}},"../node_modules/babel-runtime/helpers/createClass.js":function(e,o,r){"use strict";o.__esModule=!0;var t=r("../node_modules/babel-runtime/core-js/object/define-property.js"),s=function(e){return e&&e.__esModule?e:{default:e}}(t);o.default=function(){function e(e,o){for(var r=0;r<o.length;r++){var t=o[r];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),(0,s.default)(e,t.key,t)}}return function(o,r,t){return r&&e(o.prototype,r),t&&e(o,t),o}}()},"../node_modules/babel-runtime/helpers/inherits.js":function(e,o,r){"use strict";function t(e){return e&&e.__esModule?e:{default:e}}o.__esModule=!0;var s=r("../node_modules/babel-runtime/core-js/object/set-prototype-of.js"),n=t(s),i=r("../node_modules/babel-runtime/core-js/object/create.js"),l=t(i),u=r("../node_modules/babel-runtime/helpers/typeof.js"),d=t(u);o.default=function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Super expression must either be null or a function, not "+(void 0===o?"undefined":(0,d.default)(o)));e.prototype=(0,l.default)(o&&o.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),o&&(n.default?(0,n.default)(e,o):e.__proto__=o)}},"../node_modules/babel-runtime/helpers/possibleConstructorReturn.js":function(e,o,r){"use strict";o.__esModule=!0;var t=r("../node_modules/babel-runtime/helpers/typeof.js"),s=function(e){return e&&e.__esModule?e:{default:e}}(t);o.default=function(e,o){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!o||"object"!==(void 0===o?"undefined":(0,s.default)(o))&&"function"!=typeof o?e:o}},"../node_modules/babel-runtime/helpers/typeof.js":function(e,o,r){"use strict";function t(e){return e&&e.__esModule?e:{default:e}}o.__esModule=!0;var s=r("../node_modules/babel-runtime/core-js/symbol/iterator.js"),n=t(s),i=r("../node_modules/babel-runtime/core-js/symbol.js"),l=t(i),u="function"==typeof l.default&&"symbol"==typeof n.default?function(e){return typeof e}:function(e){return e&&"function"==typeof l.default&&e.constructor===l.default&&e!==l.default.prototype?"symbol":typeof e};o.default="function"==typeof l.default&&"symbol"===u(n.default)?function(e){return void 0===e?"undefined":u(e)}:function(e){return e&&"function"==typeof l.default&&e.constructor===l.default&&e!==l.default.prototype?"symbol":void 0===e?"undefined":u(e)}},"../node_modules/core-js/library/fn/object/create.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.object.create.js");var t=r("../node_modules/core-js/library/modules/_core.js").Object;e.exports=function(e,o){return t.create(e,o)}},"../node_modules/core-js/library/fn/object/define-property.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.object.define-property.js");var t=r("../node_modules/core-js/library/modules/_core.js").Object;e.exports=function(e,o,r){return t.defineProperty(e,o,r)}},"../node_modules/core-js/library/fn/object/get-prototype-of.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.object.get-prototype-of.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Object.getPrototypeOf},"../node_modules/core-js/library/fn/object/set-prototype-of.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.object.set-prototype-of.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Object.setPrototypeOf},"../node_modules/core-js/library/fn/symbol/index.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.symbol.js"),r("../node_modules/core-js/library/modules/es6.object.to-string.js"),r("../node_modules/core-js/library/modules/es7.symbol.async-iterator.js"),r("../node_modules/core-js/library/modules/es7.symbol.observable.js"),e.exports=r("../node_modules/core-js/library/modules/_core.js").Symbol},"../node_modules/core-js/library/fn/symbol/iterator.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.string.iterator.js"),r("../node_modules/core-js/library/modules/web.dom.iterable.js"),e.exports=r("../node_modules/core-js/library/modules/_wks-ext.js").f("iterator")},"../node_modules/core-js/library/modules/_a-function.js":function(e,o){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},"../node_modules/core-js/library/modules/_add-to-unscopables.js":function(e,o){e.exports=function(){}},"../node_modules/core-js/library/modules/_an-object.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_is-object.js");e.exports=function(e){if(!t(e))throw TypeError(e+" is not an object!");return e}},"../node_modules/core-js/library/modules/_array-includes.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_to-iobject.js"),s=r("../node_modules/core-js/library/modules/_to-length.js"),n=r("../node_modules/core-js/library/modules/_to-absolute-index.js");e.exports=function(e){return function(o,r,i){var l,u=t(o),d=s(u.length),a=n(i,d);if(e&&r!=r){for(;d>a;)if((l=u[a++])!=l)return!0}else for(;d>a;a++)if((e||a in u)&&u[a]===r)return e||a||0;return!e&&-1}}},"../node_modules/core-js/library/modules/_cof.js":function(e,o){var r={}.toString;e.exports=function(e){return r.call(e).slice(8,-1)}},"../node_modules/core-js/library/modules/_core.js":function(e,o){var r=e.exports={version:"2.6.10"};"number"==typeof __e&&(__e=r)},"../node_modules/core-js/library/modules/_ctx.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_a-function.js");e.exports=function(e,o,r){if(t(e),void 0===o)return e;switch(r){case 1:return function(r){return e.call(o,r)};case 2:return function(r,t){return e.call(o,r,t)};case 3:return function(r,t,s){return e.call(o,r,t,s)}}return function(){return e.apply(o,arguments)}}},"../node_modules/core-js/library/modules/_defined.js":function(e,o){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},"../node_modules/core-js/library/modules/_descriptors.js":function(e,o,r){e.exports=!r("../node_modules/core-js/library/modules/_fails.js")(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},"../node_modules/core-js/library/modules/_dom-create.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_is-object.js"),s=r("../node_modules/core-js/library/modules/_global.js").document,n=t(s)&&t(s.createElement);e.exports=function(e){return n?s.createElement(e):{}}},"../node_modules/core-js/library/modules/_enum-bug-keys.js":function(e,o){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},"../node_modules/core-js/library/modules/_enum-keys.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-keys.js"),s=r("../node_modules/core-js/library/modules/_object-gops.js"),n=r("../node_modules/core-js/library/modules/_object-pie.js");e.exports=function(e){var o=t(e),r=s.f;if(r)for(var i,l=r(e),u=n.f,d=0;l.length>d;)u.call(e,i=l[d++])&&o.push(i);return o}},"../node_modules/core-js/library/modules/_export.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_global.js"),s=r("../node_modules/core-js/library/modules/_core.js"),n=r("../node_modules/core-js/library/modules/_ctx.js"),i=r("../node_modules/core-js/library/modules/_hide.js"),l=r("../node_modules/core-js/library/modules/_has.js"),u=function(e,o,r){var d,a,c,m=e&u.F,_=e&u.G,j=e&u.S,f=e&u.P,y=e&u.B,b=e&u.W,p=_?s:s[o]||(s[o]={}),h=p.prototype,v=_?t:j?t[o]:(t[o]||{}).prototype;_&&(r=o);for(d in r)(a=!m&&v&&void 0!==v[d])&&l(p,d)||(c=a?v[d]:r[d],p[d]=_&&"function"!=typeof v[d]?r[d]:y&&a?n(c,t):b&&v[d]==c?function(e){var o=function(o,r,t){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(o);case 2:return new e(o,r)}return new e(o,r,t)}return e.apply(this,arguments)};return o.prototype=e.prototype,o}(c):f&&"function"==typeof c?n(Function.call,c):c,f&&((p.virtual||(p.virtual={}))[d]=c,e&u.R&&h&&!h[d]&&i(h,d,c)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},"../node_modules/core-js/library/modules/_fails.js":function(e,o){e.exports=function(e){try{return!!e()}catch(e){return!0}}},"../node_modules/core-js/library/modules/_global.js":function(e,o){var r=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=r)},"../node_modules/core-js/library/modules/_has.js":function(e,o){var r={}.hasOwnProperty;e.exports=function(e,o){return r.call(e,o)}},"../node_modules/core-js/library/modules/_hide.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-dp.js"),s=r("../node_modules/core-js/library/modules/_property-desc.js");e.exports=r("../node_modules/core-js/library/modules/_descriptors.js")?function(e,o,r){return t.f(e,o,s(1,r))}:function(e,o,r){return e[o]=r,e}},"../node_modules/core-js/library/modules/_html.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_global.js").document;e.exports=t&&t.documentElement},"../node_modules/core-js/library/modules/_ie8-dom-define.js":function(e,o,r){e.exports=!r("../node_modules/core-js/library/modules/_descriptors.js")&&!r("../node_modules/core-js/library/modules/_fails.js")(function(){return 7!=Object.defineProperty(r("../node_modules/core-js/library/modules/_dom-create.js")("div"),"a",{get:function(){return 7}}).a})},"../node_modules/core-js/library/modules/_iobject.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_cof.js");e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==t(e)?e.split(""):Object(e)}},"../node_modules/core-js/library/modules/_is-array.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_cof.js");e.exports=Array.isArray||function(e){return"Array"==t(e)}},"../node_modules/core-js/library/modules/_is-object.js":function(e,o){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},"../node_modules/core-js/library/modules/_iter-create.js":function(e,o,r){"use strict";var t=r("../node_modules/core-js/library/modules/_object-create.js"),s=r("../node_modules/core-js/library/modules/_property-desc.js"),n=r("../node_modules/core-js/library/modules/_set-to-string-tag.js"),i={};r("../node_modules/core-js/library/modules/_hide.js")(i,r("../node_modules/core-js/library/modules/_wks.js")("iterator"),function(){return this}),e.exports=function(e,o,r){e.prototype=t(i,{next:s(1,r)}),n(e,o+" Iterator")}},"../node_modules/core-js/library/modules/_iter-define.js":function(e,o,r){"use strict";var t=r("../node_modules/core-js/library/modules/_library.js"),s=r("../node_modules/core-js/library/modules/_export.js"),n=r("../node_modules/core-js/library/modules/_redefine.js"),i=r("../node_modules/core-js/library/modules/_hide.js"),l=r("../node_modules/core-js/library/modules/_iterators.js"),u=r("../node_modules/core-js/library/modules/_iter-create.js"),d=r("../node_modules/core-js/library/modules/_set-to-string-tag.js"),a=r("../node_modules/core-js/library/modules/_object-gpo.js"),c=r("../node_modules/core-js/library/modules/_wks.js")("iterator"),m=!([].keys&&"next"in[].keys()),_=function(){return this};e.exports=function(e,o,r,j,f,y,b){u(r,o,j);var p,h,v,g=function(e){if(!m&&e in w)return w[e];switch(e){case"keys":case"values":return function(){return new r(this,e)}}return function(){return new r(this,e)}},E=o+" Iterator",x="values"==f,O=!1,w=e.prototype,k=w[c]||w["@@iterator"]||f&&w[f],I=k||g(f),S=f?x?g("entries"):I:void 0,T="Array"==o?w.entries||k:k;if(T&&(v=a(T.call(new e)))!==Object.prototype&&v.next&&(d(v,E,!0),t||"function"==typeof v[c]||i(v,c,_)),x&&k&&"values"!==k.name&&(O=!0,I=function(){return k.call(this)}),t&&!b||!m&&!O&&w[c]||i(w,c,I),l[o]=I,l[E]=_,f)if(p={values:x?I:g("values"),keys:y?I:g("keys"),entries:S},b)for(h in p)h in w||n(w,h,p[h]);else s(s.P+s.F*(m||O),o,p);return p}},"../node_modules/core-js/library/modules/_iter-step.js":function(e,o){e.exports=function(e,o){return{value:o,done:!!e}}},"../node_modules/core-js/library/modules/_iterators.js":function(e,o){e.exports={}},"../node_modules/core-js/library/modules/_library.js":function(e,o){e.exports=!0},"../node_modules/core-js/library/modules/_meta.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_uid.js")("meta"),s=r("../node_modules/core-js/library/modules/_is-object.js"),n=r("../node_modules/core-js/library/modules/_has.js"),i=r("../node_modules/core-js/library/modules/_object-dp.js").f,l=0,u=Object.isExtensible||function(){return!0},d=!r("../node_modules/core-js/library/modules/_fails.js")(function(){return u(Object.preventExtensions({}))}),a=function(e){i(e,t,{value:{i:"O"+ ++l,w:{}}})},c=function(e,o){if(!s(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!n(e,t)){if(!u(e))return"F";if(!o)return"E";a(e)}return e[t].i},m=function(e,o){if(!n(e,t)){if(!u(e))return!0;if(!o)return!1;a(e)}return e[t].w},_=function(e){return d&&j.NEED&&u(e)&&!n(e,t)&&a(e),e},j=e.exports={KEY:t,NEED:!1,fastKey:c,getWeak:m,onFreeze:_}},"../node_modules/core-js/library/modules/_object-create.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_an-object.js"),s=r("../node_modules/core-js/library/modules/_object-dps.js"),n=r("../node_modules/core-js/library/modules/_enum-bug-keys.js"),i=r("../node_modules/core-js/library/modules/_shared-key.js")("IE_PROTO"),l=function(){},u=function(){var e,o=r("../node_modules/core-js/library/modules/_dom-create.js")("iframe"),t=n.length;for(o.style.display="none",r("../node_modules/core-js/library/modules/_html.js").appendChild(o),o.src="javascript:",e=o.contentWindow.document,e.open(),e.write("<script>document.F=Object<\/script>"),e.close(),u=e.F;t--;)delete u.prototype[n[t]];return u()};e.exports=Object.create||function(e,o){var r;return null!==e?(l.prototype=t(e),r=new l,l.prototype=null,r[i]=e):r=u(),void 0===o?r:s(r,o)}},"../node_modules/core-js/library/modules/_object-dp.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_an-object.js"),s=r("../node_modules/core-js/library/modules/_ie8-dom-define.js"),n=r("../node_modules/core-js/library/modules/_to-primitive.js"),i=Object.defineProperty;o.f=r("../node_modules/core-js/library/modules/_descriptors.js")?Object.defineProperty:function(e,o,r){if(t(e),o=n(o,!0),t(r),s)try{return i(e,o,r)}catch(e){}if("get"in r||"set"in r)throw TypeError("Accessors not supported!");return"value"in r&&(e[o]=r.value),e}},"../node_modules/core-js/library/modules/_object-dps.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-dp.js"),s=r("../node_modules/core-js/library/modules/_an-object.js"),n=r("../node_modules/core-js/library/modules/_object-keys.js");e.exports=r("../node_modules/core-js/library/modules/_descriptors.js")?Object.defineProperties:function(e,o){s(e);for(var r,i=n(o),l=i.length,u=0;l>u;)t.f(e,r=i[u++],o[r]);return e}},"../node_modules/core-js/library/modules/_object-gopd.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-pie.js"),s=r("../node_modules/core-js/library/modules/_property-desc.js"),n=r("../node_modules/core-js/library/modules/_to-iobject.js"),i=r("../node_modules/core-js/library/modules/_to-primitive.js"),l=r("../node_modules/core-js/library/modules/_has.js"),u=r("../node_modules/core-js/library/modules/_ie8-dom-define.js"),d=Object.getOwnPropertyDescriptor;o.f=r("../node_modules/core-js/library/modules/_descriptors.js")?d:function(e,o){if(e=n(e),o=i(o,!0),u)try{return d(e,o)}catch(e){}if(l(e,o))return s(!t.f.call(e,o),e[o])}},"../node_modules/core-js/library/modules/_object-gopn-ext.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_to-iobject.js"),s=r("../node_modules/core-js/library/modules/_object-gopn.js").f,n={}.toString,i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],l=function(e){try{return s(e)}catch(e){return i.slice()}};e.exports.f=function(e){return i&&"[object Window]"==n.call(e)?l(e):s(t(e))}},"../node_modules/core-js/library/modules/_object-gopn.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-keys-internal.js"),s=r("../node_modules/core-js/library/modules/_enum-bug-keys.js").concat("length","prototype");o.f=Object.getOwnPropertyNames||function(e){return t(e,s)}},"../node_modules/core-js/library/modules/_object-gops.js":function(e,o){o.f=Object.getOwnPropertySymbols},"../node_modules/core-js/library/modules/_object-gpo.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_has.js"),s=r("../node_modules/core-js/library/modules/_to-object.js"),n=r("../node_modules/core-js/library/modules/_shared-key.js")("IE_PROTO"),i=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=s(e),t(e,n)?e[n]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?i:null}},"../node_modules/core-js/library/modules/_object-keys-internal.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_has.js"),s=r("../node_modules/core-js/library/modules/_to-iobject.js"),n=r("../node_modules/core-js/library/modules/_array-includes.js")(!1),i=r("../node_modules/core-js/library/modules/_shared-key.js")("IE_PROTO");e.exports=function(e,o){var r,l=s(e),u=0,d=[];for(r in l)r!=i&&t(l,r)&&d.push(r);for(;o.length>u;)t(l,r=o[u++])&&(~n(d,r)||d.push(r));return d}},"../node_modules/core-js/library/modules/_object-keys.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-keys-internal.js"),s=r("../node_modules/core-js/library/modules/_enum-bug-keys.js");e.exports=Object.keys||function(e){return t(e,s)}},"../node_modules/core-js/library/modules/_object-pie.js":function(e,o){o.f={}.propertyIsEnumerable},"../node_modules/core-js/library/modules/_object-sap.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_export.js"),s=r("../node_modules/core-js/library/modules/_core.js"),n=r("../node_modules/core-js/library/modules/_fails.js");e.exports=function(e,o){var r=(s.Object||{})[e]||Object[e],i={};i[e]=o(r),t(t.S+t.F*n(function(){r(1)}),"Object",i)}},"../node_modules/core-js/library/modules/_property-desc.js":function(e,o){e.exports=function(e,o){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:o}}},"../node_modules/core-js/library/modules/_redefine.js":function(e,o,r){e.exports=r("../node_modules/core-js/library/modules/_hide.js")},"../node_modules/core-js/library/modules/_set-proto.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_is-object.js"),s=r("../node_modules/core-js/library/modules/_an-object.js"),n=function(e,o){if(s(e),!t(o)&&null!==o)throw TypeError(o+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,o,t){try{t=r("../node_modules/core-js/library/modules/_ctx.js")(Function.call,r("../node_modules/core-js/library/modules/_object-gopd.js").f(Object.prototype,"__proto__").set,2),t(e,[]),o=!(e instanceof Array)}catch(e){o=!0}return function(e,r){return n(e,r),o?e.__proto__=r:t(e,r),e}}({},!1):void 0),check:n}},"../node_modules/core-js/library/modules/_set-to-string-tag.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_object-dp.js").f,s=r("../node_modules/core-js/library/modules/_has.js"),n=r("../node_modules/core-js/library/modules/_wks.js")("toStringTag");e.exports=function(e,o,r){e&&!s(e=r?e:e.prototype,n)&&t(e,n,{configurable:!0,value:o})}},"../node_modules/core-js/library/modules/_shared-key.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_shared.js")("keys"),s=r("../node_modules/core-js/library/modules/_uid.js");e.exports=function(e){return t[e]||(t[e]=s(e))}},"../node_modules/core-js/library/modules/_shared.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_core.js"),s=r("../node_modules/core-js/library/modules/_global.js"),n=s["__core-js_shared__"]||(s["__core-js_shared__"]={});(e.exports=function(e,o){return n[e]||(n[e]=void 0!==o?o:{})})("versions",[]).push({version:t.version,mode:r("../node_modules/core-js/library/modules/_library.js")?"pure":"global",copyright:"© 2019 Denis Pushkarev (zloirock.ru)"})},"../node_modules/core-js/library/modules/_string-at.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_to-integer.js"),s=r("../node_modules/core-js/library/modules/_defined.js");e.exports=function(e){return function(o,r){var n,i,l=String(s(o)),u=t(r),d=l.length;return u<0||u>=d?e?"":void 0:(n=l.charCodeAt(u),n<55296||n>56319||u+1===d||(i=l.charCodeAt(u+1))<56320||i>57343?e?l.charAt(u):n:e?l.slice(u,u+2):i-56320+(n-55296<<10)+65536)}}},"../node_modules/core-js/library/modules/_to-absolute-index.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_to-integer.js"),s=Math.max,n=Math.min;e.exports=function(e,o){return e=t(e),e<0?s(e+o,0):n(e,o)}},"../node_modules/core-js/library/modules/_to-integer.js":function(e,o){var r=Math.ceil,t=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?t:r)(e)}},"../node_modules/core-js/library/modules/_to-iobject.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_iobject.js"),s=r("../node_modules/core-js/library/modules/_defined.js");e.exports=function(e){return t(s(e))}},"../node_modules/core-js/library/modules/_to-length.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_to-integer.js"),s=Math.min;e.exports=function(e){return e>0?s(t(e),9007199254740991):0}},"../node_modules/core-js/library/modules/_to-object.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_defined.js");e.exports=function(e){return Object(t(e))}},"../node_modules/core-js/library/modules/_to-primitive.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_is-object.js");e.exports=function(e,o){if(!t(e))return e;var r,s;if(o&&"function"==typeof(r=e.toString)&&!t(s=r.call(e)))return s;if("function"==typeof(r=e.valueOf)&&!t(s=r.call(e)))return s;if(!o&&"function"==typeof(r=e.toString)&&!t(s=r.call(e)))return s;throw TypeError("Can't convert object to primitive value")}},"../node_modules/core-js/library/modules/_uid.js":function(e,o){var r=0,t=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++r+t).toString(36))}},"../node_modules/core-js/library/modules/_wks-define.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_global.js"),s=r("../node_modules/core-js/library/modules/_core.js"),n=r("../node_modules/core-js/library/modules/_library.js"),i=r("../node_modules/core-js/library/modules/_wks-ext.js"),l=r("../node_modules/core-js/library/modules/_object-dp.js").f;e.exports=function(e){var o=s.Symbol||(s.Symbol=n?{}:t.Symbol||{});"_"==e.charAt(0)||e in o||l(o,e,{value:i.f(e)})}},"../node_modules/core-js/library/modules/_wks-ext.js":function(e,o,r){o.f=r("../node_modules/core-js/library/modules/_wks.js")},"../node_modules/core-js/library/modules/_wks.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_shared.js")("wks"),s=r("../node_modules/core-js/library/modules/_uid.js"),n=r("../node_modules/core-js/library/modules/_global.js").Symbol,i="function"==typeof n;(e.exports=function(e){return t[e]||(t[e]=i&&n[e]||(i?n:s)("Symbol."+e))}).store=t},"../node_modules/core-js/library/modules/es6.array.iterator.js":function(e,o,r){"use strict";var t=r("../node_modules/core-js/library/modules/_add-to-unscopables.js"),s=r("../node_modules/core-js/library/modules/_iter-step.js"),n=r("../node_modules/core-js/library/modules/_iterators.js"),i=r("../node_modules/core-js/library/modules/_to-iobject.js");e.exports=r("../node_modules/core-js/library/modules/_iter-define.js")(Array,"Array",function(e,o){this._t=i(e),this._i=0,this._k=o},function(){var e=this._t,o=this._k,r=this._i++;return!e||r>=e.length?(this._t=void 0,s(1)):"keys"==o?s(0,r):"values"==o?s(0,e[r]):s(0,[r,e[r]])},"values"),n.Arguments=n.Array,t("keys"),t("values"),t("entries")},"../node_modules/core-js/library/modules/es6.object.create.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_export.js");t(t.S,"Object",{create:r("../node_modules/core-js/library/modules/_object-create.js")})},"../node_modules/core-js/library/modules/es6.object.define-property.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_export.js");t(t.S+t.F*!r("../node_modules/core-js/library/modules/_descriptors.js"),"Object",{defineProperty:r("../node_modules/core-js/library/modules/_object-dp.js").f})},"../node_modules/core-js/library/modules/es6.object.get-prototype-of.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_to-object.js"),s=r("../node_modules/core-js/library/modules/_object-gpo.js");r("../node_modules/core-js/library/modules/_object-sap.js")("getPrototypeOf",function(){return function(e){return s(t(e))}})},"../node_modules/core-js/library/modules/es6.object.set-prototype-of.js":function(e,o,r){var t=r("../node_modules/core-js/library/modules/_export.js");t(t.S,"Object",{setPrototypeOf:r("../node_modules/core-js/library/modules/_set-proto.js").set})},"../node_modules/core-js/library/modules/es6.object.to-string.js":function(e,o){},"../node_modules/core-js/library/modules/es6.string.iterator.js":function(e,o,r){"use strict";var t=r("../node_modules/core-js/library/modules/_string-at.js")(!0);r("../node_modules/core-js/library/modules/_iter-define.js")(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,o=this._t,r=this._i;return r>=o.length?{value:void 0,done:!0}:(e=t(o,r),this._i+=e.length,{value:e,done:!1})})},"../node_modules/core-js/library/modules/es6.symbol.js":function(e,o,r){"use strict";var t=r("../node_modules/core-js/library/modules/_global.js"),s=r("../node_modules/core-js/library/modules/_has.js"),n=r("../node_modules/core-js/library/modules/_descriptors.js"),i=r("../node_modules/core-js/library/modules/_export.js"),l=r("../node_modules/core-js/library/modules/_redefine.js"),u=r("../node_modules/core-js/library/modules/_meta.js").KEY,d=r("../node_modules/core-js/library/modules/_fails.js"),a=r("../node_modules/core-js/library/modules/_shared.js"),c=r("../node_modules/core-js/library/modules/_set-to-string-tag.js"),m=r("../node_modules/core-js/library/modules/_uid.js"),_=r("../node_modules/core-js/library/modules/_wks.js"),j=r("../node_modules/core-js/library/modules/_wks-ext.js"),f=r("../node_modules/core-js/library/modules/_wks-define.js"),y=r("../node_modules/core-js/library/modules/_enum-keys.js"),b=r("../node_modules/core-js/library/modules/_is-array.js"),p=r("../node_modules/core-js/library/modules/_an-object.js"),h=r("../node_modules/core-js/library/modules/_is-object.js"),v=r("../node_modules/core-js/library/modules/_to-object.js"),g=r("../node_modules/core-js/library/modules/_to-iobject.js"),E=r("../node_modules/core-js/library/modules/_to-primitive.js"),x=r("../node_modules/core-js/library/modules/_property-desc.js"),O=r("../node_modules/core-js/library/modules/_object-create.js"),w=r("../node_modules/core-js/library/modules/_object-gopn-ext.js"),k=r("../node_modules/core-js/library/modules/_object-gopd.js"),I=r("../node_modules/core-js/library/modules/_object-gops.js"),S=r("../node_modules/core-js/library/modules/_object-dp.js"),T=r("../node_modules/core-js/library/modules/_object-keys.js"),P=k.f,L=S.f,A=w.f,C=t.Symbol,D=t.JSON,M=D&&D.stringify,N=_("_hidden"),R=_("toPrimitive"),F={}.propertyIsEnumerable,H=a("symbol-registry"),G=a("symbols"),B=a("op-symbols"),U=Object.prototype,W="function"==typeof C&&!!I.f,Y=t.QObject,K=!Y||!Y.prototype||!Y.prototype.findChild,V=n&&d(function(){return 7!=O(L({},"a",{get:function(){return L(this,"a",{value:7}).a}})).a})?function(e,o,r){var t=P(U,o);t&&delete U[o],L(e,o,r),t&&e!==U&&L(U,o,t)}:L,X=function(e){var o=G[e]=O(C.prototype);return o._k=e,o},z=W&&"symbol"==typeof C.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof C},J=function(e,o,r){return e===U&&J(B,o,r),p(e),o=E(o,!0),p(r),s(G,o)?(r.enumerable?(s(e,N)&&e[N][o]&&(e[N][o]=!1),r=O(r,{enumerable:x(0,!1)})):(s(e,N)||L(e,N,x(1,{})),e[N][o]=!0),V(e,o,r)):L(e,o,r)},q=function(e,o){p(e);for(var r,t=y(o=g(o)),s=0,n=t.length;n>s;)J(e,r=t[s++],o[r]);return e},Q=function(e,o){return void 0===o?O(e):q(O(e),o)},Z=function(e){var o=F.call(this,e=E(e,!0));return!(this===U&&s(G,e)&&!s(B,e))&&(!(o||!s(this,e)||!s(G,e)||s(this,N)&&this[N][e])||o)},$=function(e,o){if(e=g(e),o=E(o,!0),e!==U||!s(G,o)||s(B,o)){var r=P(e,o);return!r||!s(G,o)||s(e,N)&&e[N][o]||(r.enumerable=!0),r}},ee=function(e){for(var o,r=A(g(e)),t=[],n=0;r.length>n;)s(G,o=r[n++])||o==N||o==u||t.push(o);return t},oe=function(e){for(var o,r=e===U,t=A(r?B:g(e)),n=[],i=0;t.length>i;)!s(G,o=t[i++])||r&&!s(U,o)||n.push(G[o]);return n};W||(C=function(){if(this instanceof C)throw TypeError("Symbol is not a constructor!");var e=m(arguments.length>0?arguments[0]:void 0),o=function(r){this===U&&o.call(B,r),s(this,N)&&s(this[N],e)&&(this[N][e]=!1),V(this,e,x(1,r))};return n&&K&&V(U,e,{configurable:!0,set:o}),X(e)},l(C.prototype,"toString",function(){return this._k}),k.f=$,S.f=J,r("../node_modules/core-js/library/modules/_object-gopn.js").f=w.f=ee,r("../node_modules/core-js/library/modules/_object-pie.js").f=Z,I.f=oe,n&&!r("../node_modules/core-js/library/modules/_library.js")&&l(U,"propertyIsEnumerable",Z,!0),j.f=function(e){return X(_(e))}),i(i.G+i.W+i.F*!W,{Symbol:C});for(var re="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),te=0;re.length>te;)_(re[te++]);for(var se=T(_.store),ne=0;se.length>ne;)f(se[ne++]);i(i.S+i.F*!W,"Symbol",{for:function(e){return s(H,e+="")?H[e]:H[e]=C(e)},keyFor:function(e){if(!z(e))throw TypeError(e+" is not a symbol!");for(var o in H)if(H[o]===e)return o},useSetter:function(){K=!0},useSimple:function(){K=!1}}),i(i.S+i.F*!W,"Object",{create:Q,defineProperty:J,defineProperties:q,getOwnPropertyDescriptor:$,getOwnPropertyNames:ee,getOwnPropertySymbols:oe}),i(i.S+i.F*d(function(){I.f(1)}),"Object",{getOwnPropertySymbols:function(e){return I.f(v(e))}}),D&&i(i.S+i.F*(!W||d(function(){var e=C();return"[null]"!=M([e])||"{}"!=M({a:e})||"{}"!=M(Object(e))})),"JSON",{stringify:function(e){for(var o,r,t=[e],s=1;arguments.length>s;)t.push(arguments[s++]);if(r=o=t[1],(h(o)||void 0!==e)&&!z(e))return b(o)||(o=function(e,o){if("function"==typeof r&&(o=r.call(this,e,o)),!z(o))return o}),t[1]=o,M.apply(D,t)}}),C.prototype[R]||r("../node_modules/core-js/library/modules/_hide.js")(C.prototype,R,C.prototype.valueOf),c(C,"Symbol"),c(Math,"Math",!0),c(t.JSON,"JSON",!0)},"../node_modules/core-js/library/modules/es7.symbol.async-iterator.js":function(e,o,r){r("../node_modules/core-js/library/modules/_wks-define.js")("asyncIterator")},"../node_modules/core-js/library/modules/es7.symbol.observable.js":function(e,o,r){r("../node_modules/core-js/library/modules/_wks-define.js")("observable")},"../node_modules/core-js/library/modules/web.dom.iterable.js":function(e,o,r){r("../node_modules/core-js/library/modules/es6.array.iterator.js");for(var t=r("../node_modules/core-js/library/modules/_global.js"),s=r("../node_modules/core-js/library/modules/_hide.js"),n=r("../node_modules/core-js/library/modules/_iterators.js"),i=r("../node_modules/core-js/library/modules/_wks.js")("toStringTag"),l="CSSRuleList,CSSStyleDeclaration,CSSValueList,ClientRectList,DOMRectList,DOMStringList,DOMTokenList,DataTransferItemList,FileList,HTMLAllCollection,HTMLCollection,HTMLFormElement,HTMLSelectElement,MediaList,MimeTypeArray,NamedNodeMap,NodeList,PaintRequestList,Plugin,PluginArray,SVGLengthList,SVGNumberList,SVGPathSegList,SVGPointList,SVGStringList,SVGTransformList,SourceBufferList,StyleSheetList,TextTrackCueList,TextTrackList,TouchList".split(","),u=0;u<l.length;u++){var d=l[u],a=t[d],c=a&&a.prototype;c&&!c[i]&&s(c,i,d),n[d]=n.Array}},"../node_modules/eventemitter3/index.js":function(e,o,r){"use strict";function t(){}function s(e,o,r){this.fn=e,this.context=o,this.once=r||!1}function n(){this._events=new t,this._eventsCount=0}var i=Object.prototype.hasOwnProperty,l="~";Object.create&&(t.prototype=Object.create(null),(new t).__proto__||(l=!1)),n.prototype.eventNames=function(){var e,o,r=[];if(0===this._eventsCount)return r;for(o in e=this._events)i.call(e,o)&&r.push(l?o.slice(1):o);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(e)):r},n.prototype.listeners=function(e,o){var r=l?l+e:e,t=this._events[r];if(o)return!!t;if(!t)return[];if(t.fn)return[t.fn];for(var s=0,n=t.length,i=new Array(n);s<n;s++)i[s]=t[s].fn;return i},n.prototype.emit=function(e,o,r,t,s,n){var i=l?l+e:e;if(!this._events[i])return!1;var u,d,a=this._events[i],c=arguments.length;if(a.fn){switch(a.once&&this.removeListener(e,a.fn,void 0,!0),c){case 1:return a.fn.call(a.context),!0;case 2:return a.fn.call(a.context,o),!0;case 3:return a.fn.call(a.context,o,r),!0;case 4:return a.fn.call(a.context,o,r,t),!0;case 5:return a.fn.call(a.context,o,r,t,s),!0;case 6:return a.fn.call(a.context,o,r,t,s,n),!0}for(d=1,u=new Array(c-1);d<c;d++)u[d-1]=arguments[d];a.fn.apply(a.context,u)}else{var m,_=a.length;for(d=0;d<_;d++)switch(a[d].once&&this.removeListener(e,a[d].fn,void 0,!0),c){case 1:a[d].fn.call(a[d].context);break;case 2:a[d].fn.call(a[d].context,o);break;case 3:a[d].fn.call(a[d].context,o,r);break;case 4:a[d].fn.call(a[d].context,o,r,t);break;default:if(!u)for(m=1,u=new Array(c-1);m<c;m++)u[m-1]=arguments[m];a[d].fn.apply(a[d].context,u)}}return!0},n.prototype.on=function(e,o,r){var t=new s(o,r||this),n=l?l+e:e;return this._events[n]?this._events[n].fn?this._events[n]=[this._events[n],t]:this._events[n].push(t):(this._events[n]=t,this._eventsCount++),this},n.prototype.once=function(e,o,r){var t=new s(o,r||this,!0),n=l?l+e:e;return this._events[n]?this._events[n].fn?this._events[n]=[this._events[n],t]:this._events[n].push(t):(this._events[n]=t,this._eventsCount++),this},n.prototype.removeListener=function(e,o,r,s){var n=l?l+e:e;if(!this._events[n])return this;if(!o)return 0==--this._eventsCount?this._events=new t:delete this._events[n],this;var i=this._events[n];if(i.fn)i.fn!==o||s&&!i.once||r&&i.context!==r||(0==--this._eventsCount?this._events=new t:delete this._events[n]);else{for(var u=0,d=[],a=i.length;u<a;u++)(i[u].fn!==o||s&&!i[u].once||r&&i[u].context!==r)&&d.push(i[u]);d.length?this._events[n]=1===d.length?d[0]:d:0==--this._eventsCount?this._events=new t:delete this._events[n]}return this},n.prototype.removeAllListeners=function(e){var o;return e?(o=l?l+e:e,this._events[o]&&(0==--this._eventsCount?this._events=new t:delete this._events[o])):(this._events=new t,this._eventsCount=0),this},n.prototype.off=n.prototype.removeListener,n.prototype.addListener=n.prototype.on,n.prototype.setMaxListeners=function(){return this},n.prefixed=l,n.EventEmitter=n,e.exports=n},"./EditorManager/html/editor.html":function(e,o,r){e.exports=r.p+"editor.html"},"./EditorManager/js/contants.js":function(e,o,r){"use strict";Object.defineProperty(o,"__esModule",{value:!0});o.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},o.events={DROP_AND_COPY_FILE:"drop_and_copy_file",DROP_AND_COPY_INVALID_IMAGE:"drop_and_copy_invalid_image",EDITOR_CHANGE:"editor-change",EDITOR_CONTEXT_MENU:"editor_context_menu",EDITOR_HAIT_KEYUP:"editor_hait_keyup",EDITOR_CALL_KEYUP:"editor_call_keyup",IMG_DBLCLICK:"img_dblclick",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",click:"click",EDITOR_BLUR:"editor-blur",EDITOR_FOCUS:"editor-focus",EDITOR_KEYDOWN:"editor-keydown",focus:"focus",mouseup:"mouseup",mousemove:"mousemove",PASTE_FILE:"paste_file"}},"./ImEditor/js/index.js":function(e,o,r){"use strict";function t(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(o,"__esModule",{value:!0});var s=r("../node_modules/babel-runtime/core-js/object/get-prototype-of.js"),n=t(s),i=r("../node_modules/babel-runtime/helpers/classCallCheck.js"),l=t(i),u=r("../node_modules/babel-runtime/helpers/createClass.js"),d=t(u),a=r("../node_modules/babel-runtime/helpers/possibleConstructorReturn.js"),c=t(a),m=r("../node_modules/babel-runtime/helpers/inherits.js"),_=t(m),j=r("../node_modules/eventemitter3/index.js"),f=t(j),y=r("./EditorManager/html/editor.html"),b=t(y),p=r("./EditorManager/js/contants.js"),h="im-editor",v=function(e){function o(e,r){(0,l.default)(this,o);var t=(0,c.default)(this,(o.__proto__||(0,n.default)(o)).call(this,e));return t._container="string"==typeof e?document.getElementById(e):e,t._options=r,t._id=h+"-"+(new Date).getTime(),t._iframe=document.createElement("iframe"),t._iframe.id=t._id,t._iframe.allowfullscreen="false",t._iframe.frameBorder="0",t._iframe.marginheight="0",t._iframe.marginwidth="0",t._iframe.width="100%",t._iframe.height="100%",t._iframe.scrolling="no",t._iframe.src=b.default,t._iframe.addEventListener("load",function(){t._iframeWindow=t._iframe.contentWindow,t._iframeDocument=t._iframeWindow.document,t._manager=t._iframeWindow.ImManager,o.Delta=t._manager.Delta,t._isInited=!0,t._currId&&t.activeEditor(t._currId),t._iframeWindow.addEventListener("blur",function(){t.emit(p.events.EDITOR_BLUR,t._currId)}),t._iframeWindow.addEventListener("focus",function(){t.emit(p.events.EDITOR_FOCUS,t._currId)}),t._iframeWindow.addEventListener("keydown",function(e){if("F12"==e.key)return e.stopPropagation(),e.preventDefault(),!1;t.emit(p.events.EDITOR_KEYDOWN,e)}),t._iframeDocument.addEventListener("click",function(e){t.emit(p.events.click,e)},!1),t._options.dropEl&&(document.addEventListener("drop",function(e){e.composedPath().some(function(e){return e.id==t._options.dropEl})&&(t._manager.doDrop(e),e.preventDefault(),e.stopPropagation())},!1),document.addEventListener("paste",function(e){e.composedPath().some(function(e){return e.id==t._options.dropEl})&&(t._manager.pasteFile(e.clipboardData.files),e.preventDefault(),e.stopPropagation())},!1))},!1),t._container.appendChild(t._iframe),t}return(0,_.default)(o,e),(0,d.default)(o,[{key:"hasContents",value:function(e){if(!this.hasEditor(e))return!1;var o=this._manager.getContents(e);return!(!o||!o.ops)&&(o.ops.length>1||"\n"!==o.ops[0].insert)}},{key:"getContents",value:function(e,o,r){if(this.hasEditor(e))return this._manager.getContents(e,o,r)}},{key:"setContents",value:function(e,o){this.isActive(e)&&this._manager.setContents(e,o)}},{key:"dangerouslyPasteHTML",value:function(e,o){this.hasEditor(e)&&this._manager.dangerouslyPasteHTML(e,o)}},{key:"dangerouslyPasteHTMLByIndex",value:function(e,o,r){this.hasEditor(e)&&this._manager.dangerouslyPasteHTMLByIndex(e,o,r)}},{key:"insertText",value:function(e,o){this.hasEditor(e)&&this._manager.insertText(e,o)}},{key:"insertTextByIndex",value:function(e,o,r){this.hasEditor(e)&&this._manager.insertTextByIndex(e,o,r)}},{key:"insertEmoji",value:function(e,o,r){this.hasEditor(r)&&this._manager.insertEmoji(r,{text:e,img:o})}},{key:"insertImage",value:function(e,o){this.hasEditor(o)&&this._manager.insertImage(o,{text:"[图片]",img:e})}},{key:"insertHaitImage",value:function(e,o,r,t,s){this.hasEditor(t)&&this._manager.insertHaitImage(t,{account:e,text:o,img:r},s)}},{key:"insertHaitImageNew",value:function(e){this.hasEditor(e.id)&&this._manager.insertHaitImageNew(e)}},{key:"insertDefaultImageBlot",value:function(e){this.hasEditor(e.id)&&this._manager.insertDefaultImageBlot(e)}},{key:"insertHaitSpan",value:function(e,o,r){this.hasEditor(r)&&this._manager.insertHaitSpan(r,{account:e,text:o})}},{key:"pasteFile",value:function(e){this._manager.pasteFile(e)}},{key:"doDrop",value:function(e){this._manager.doDrop(e)}},{key:"createEditor",value:function(e){var o=this;if(this.isInited(e)){var r=this._manager.createEditor(e,this._options);r.on(p.events.SELECTION_CHANGE,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.SELECTION_CHANGE,o._currId].concat(r))}),r.on(p.events.TEXT_CHANGE,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.TEXT_CHANGE,o._currId].concat(r))}),r.on(p.events.EDITOR_CHANGE,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.EDITOR_CHANGE,o._currId].concat(r))}),r.on(p.events.EDITOR_CONTEXT_MENU,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.EDITOR_CONTEXT_MENU,o._currId].concat(r))}),r.on(p.events.IMG_DBLCLICK,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.IMG_DBLCLICK,o._currId].concat(r))}),r.on(p.events.DROP_AND_COPY_FILE,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.DROP_AND_COPY_FILE,o._currId].concat(r))}),r.on(p.events.PASTE_FILE,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.PASTE_FILE,o._currId].concat(r))}),r.on(p.events.DROP_AND_COPY_INVALID_IMAGE,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.DROP_AND_COPY_INVALID_IMAGE,o._currId].concat(r))}),r.on(p.events.EDITOR_HAIT_KEYUP,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.EDITOR_HAIT_KEYUP,o._currId].concat(r))}),r.on(p.events.EDITOR_CALL_KEYUP,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.EDITOR_CALL_KEYUP,o._currId].concat(r))}),r.on(p.events.focus,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.focus,o._currId].concat(r))}),r.on(p.events.mouseup,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.mouseup,o._currId].concat(r))}),r.on(p.events.mousemove,function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,[p.events.mousemove,o._currId].concat(r))}),r.on("error",function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];o.emit.apply(o,["error",o._currId].concat(r))})}}},{key:"activeEditor",value:function(e){this.safeBlurEditor(e),this._currId=e,this.isInited(e)&&(this.hasEditor(e)||this.createEditor(e),this._manager.activeEditor(e))}},{key:"blurEditor",value:function(e){this.hasEditor(e)&&this._manager.blurEditor(e)}},{key:"safeBlurEditor",value:function(e){this.hasEditor(e)&&this._manager.safeBlurEditor(e)}},{key:"isInited",value:function(e){return!(!this._isInited||!e)}},{key:"hasEditor",value:function(e){return this._manager.hasEditor(e)}},{key:"isActive",value:function(e){return this.isInited(e)&&this.hasEditor(e)&&this._manager.isActive(e)}},{key:"clear",value:function(e){var o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this._manager.hasEditor(e)&&(this._manager.clear(e),o&&this._manager.focusEditor(e))}},{key:"getSelection",value:function(e){var o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return this.isActive(e)?this._manager.getSelection(e,o):null}},{key:"setSelection",value:function(e,o){return this.isActive(e)?this._manager.setSelection(e,o):null}},{key:"getBounds",value:function(e,o,r){return this.isActive(e)?this._manager.getBounds(e,o,r):null}},{key:"getText",value:function(e,o,r){return this._manager.getText(e,o,r)}},{key:"update",value:function(e){this.isActive(e)&&this._manager.update(e)}},{key:"deleteText",value:function(e,o,r){this._manager.deleteText(e,o,r)}},{key:"deleteLastOneText",value:function(e){this._manager.deleteLastOneText(e)}},{key:"setPlaceholder",value:function(e,o){this._manager.setPlaceholder(e,o)}}]),o}(f.default);v.keys=p.keys,v.events=p.events,o.default=v,e.exports=o.default}})});