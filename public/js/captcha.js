// ===================== 工具类 =======================
// base64
var Base64 = {
  _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
  encode: function (e) {
    var t = "";
    var n, r, i, s, o, u, a;
    var f = 0;
    e = Base64._utf8_encode(e);
    while (f < e.length) {
      n = e.charCodeAt(f++);
      r = e.charCodeAt(f++);
      i = e.charCodeAt(f++);
      s = n >> 2;
      o = (n & 3) << 4 | r >> 4;
      u = (r & 15) << 2 | i >> 6;
      a = i & 63;
      if (isNaN(r)) {
        u = a = 64
      } else if (isNaN(i)) {
        a = 64
      }
      t = t + this._keyStr.charAt(s) + this._keyStr.charAt(o) + this._keyStr.charAt(u) + this._keyStr.charAt(a)
    }
    return t
  },
  decode: function (e) {
    var t = "";
    var n, r, i;
    var s, o, u, a;
    var f = 0;
    e = e.replace(/[^A-Za-z0-9+/=]/g, "");
    while (f < e.length) {
      s = this._keyStr.indexOf(e.charAt(f++));
      o = this._keyStr.indexOf(e.charAt(f++));
      u = this._keyStr.indexOf(e.charAt(f++));
      a = this._keyStr.indexOf(e.charAt(f++));
      n = s << 2 | o >> 4;
      r = (o & 15) << 4 | u >> 2;
      i = (u & 3) << 6 | a;
      t = t + String.fromCharCode(n);
      if (u != 64) {
        t = t + String.fromCharCode(r)
      }
      if (a != 64) {
        t = t + String.fromCharCode(i)
      }
    }
    t = Base64._utf8_decode(t);
    return t
  },
  _utf8_encode: function (e) {
    e = e.replace(/rn/g, "n");
    var t = "";
    for (var n = 0; n < e.length; n++) {
      var r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r)
      } else if (r > 127 && r < 2048) {
        t += String.fromCharCode(r >> 6 | 192);
        t += String.fromCharCode(r & 63 | 128)
      } else {
        t += String.fromCharCode(r >> 12 | 224);
        t += String.fromCharCode(r >> 6 & 63 | 128);
        t += String.fromCharCode(r & 63 | 128)
      }
    }
    return t
  },
  _utf8_decode: function (e) {
    var t = "";
    var n = 0;
    var r = 0;
    var c1 = 0;
    var c2 = 0;
    var c3 = 0;
    while (n < e.length) {
      r = e.charCodeAt(n);
      if (r < 128) {
        t += String.fromCharCode(r);
        n++
      } else if (r > 191 && r < 224) {
        c2 = e.charCodeAt(n + 1);
        t += String.fromCharCode((r & 31) << 6 | c2 & 63);
        n += 2
      } else {
        c2 = e.charCodeAt(n + 1);
        c3 = e.charCodeAt(n + 2);
        t += String.fromCharCode((r & 15) << 12 | (c2 & 63) << 6 | c3 & 63);
        n += 3
      }
    }
    return t
  }
}

/**
 * 清除默认事件
 * @param event event
 */
function clearPreventDefault(event) {
  if (event.preventDefault) {
    event.preventDefault();
  }
}

/**
 * 阻止某div默认事件
 * @param $div
 */
function clearAllPreventDefault($div) {
  $div.each(function (index, el) {
    el.addEventListener('touchmove', clearPreventDefault, {passive: false});
  });
}

function reductionAllPreventDefault($div) {
  $div.each(function (index, el) {
    el.removeEventListener('touchmove', clearPreventDefault);
  });
}

/** 是否打印日志 */
var isPrintLog = false;

/** 语序点选(模板). */
var wordOrderImageClickDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-slider tianai-captcha-word-click">
    <div id="tianai-captcha-bg-img"></div>
    <div class="click-tip">
        <span id="tianai-captcha-click-track-font">请按语序依次点击文字:</span>
    </div>
    <div class="content">
        <div class="bg-img-div">
            <img id="tianai-captcha-slider-bg-img" src="" alt/>
            <canvas id="tianai-captcha-slider-bg-canvas"></canvas>
            <div id="bg-img-click-mask"></div>
        </div>
         <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;

/** 文字点选/图标点选. */
var wordImageClickDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-slider tianai-captcha-word-click">
    <div id="tianai-captcha-bg-img"></div>
    <div class="click-tip">
        <span id="tianai-captcha-click-track-font">请依次点击:</span>
        <img src="" id="tianai-captcha-tip-img" class="tip-img">
    </div>
    <div class="content">
        <div class="bg-img-div">
            <img id="tianai-captcha-slider-bg-img" src="" alt/>
            <canvas id="tianai-captcha-slider-bg-canvas"></canvas>
            <div id="bg-img-click-mask"></div>
        </div>
         <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;

/** 滑块验证码. */
var sliderCaptchaDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-slider">
    <div id="tianai-captcha-bg-img"></div>
<!--    <div class="slider-tip">-->
<!--        <span id="tianai-captcha-slider-move-track-font">拖动滑块完成拼图</span>-->
<!--    </div>-->
    <div class="content">
        <div class="bg-img-div">
            <img id="tianai-captcha-slider-bg-img" src="" alt/>
            <canvas id="tianai-captcha-slider-bg-canvas"></canvas>
        </div>
        <div class="slider-img-div" id="tianai-captcha-slider-img-div">
            <img id="tianai-captcha-slider-move-img" src="" alt/>
        </div>
        <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-move">
        <div class="slider-move-track">
            <div id="tianai-captcha-slider-move-track-mask"></div>
            <div class="slider-move-shadow"></div>
            拖动滑块完成拼图
        </div>
        <div class="slider-move-btn" id="tianai-captcha-slider-move-btn">
        </div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;
/** 旋转验证码. */
var rotateCaptchaDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-slider tianai-captcha-rotate">
    <div id="tianai-captcha-bg-img"></div>
<!--    <div class="slider-tip">-->
<!--        <span id="tianai-captcha-slider-move-track-font">拖动滑块完成拼图</span>-->
<!--    </div>-->
    <div class="content">
        <div class="bg-img-div">
            <img id="tianai-captcha-slider-bg-img" src="" alt/>
            <canvas id="tianai-captcha-slider-bg-canvas"></canvas>
        </div>
        <div class="rotate-img-div" id="tianai-captcha-slider-img-div">
            <img id="tianai-captcha-slider-move-img" src="" alt/>
        </div>
         <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-move">
        <div class="slider-move-track">
            <div id="tianai-captcha-slider-move-track-mask"></div>
            <div class="slider-move-shadow"></div>
            拖动滑块完成拼图
        </div>
        <div class="slider-move-btn" id="tianai-captcha-slider-move-btn">
        </div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;
/** 拼接验证码. */
var concatCaptchaDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-slider tianai-captcha-concat">
    <div id="tianai-captcha-bg-img"></div>
<!--    <div class="slider-tip">-->
<!--        <span id="tianai-captcha-slider-move-track-font">拖动滑块完成拼图</span>-->
<!--    </div>-->
    <div class="content">
        <div class="tianai-captcha-slider-concat-img-div" id="tianai-captcha-slider-concat-img-div">
            <img id="tianai-captcha-slider-concat-slider-img" src="" alt/>
        </div>
        <div class="tianai-captcha-slider-concat-bg-img"></div>
         <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-move">
        <div class="slider-move-track">
            <div id="tianai-captcha-slider-move-track-mask"></div>
            <div class="slider-move-shadow"></div>
            拖动滑块完成拼图
        </div>
        <div class="slider-move-btn" id="tianai-captcha-slider-move-btn">
        </div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;
/** 旋转验证码-2. */
var rotateDegreeCaptchaDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-rotate2">
    <div id="tianai-captcha-bg-img" ></div>
    <div class="slider-tip">
        <span id="tianai-captcha-slider-move-track-font">拖动滑块，使图片角度为正</span>
    </div>
    <div class="content">
        <div class="mask"></div>
        <div class="bg-img-div">
            <img id="tianai-captcha-slider-bg-img" src="" alt/>
            <canvas id="tianai-captcha-slider-bg-degree-canvas"></canvas>
            <img class="tianai-captcha-slider-bg-img-mask" />
        </div>
         <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-move">
        <div class="slider-move-track">
            <div id="tianai-captcha-slider-move-track-mask"></div>
            <div class="slider-move-shadow"></div>
        </div>
        <div class="slider-move-btn" id="tianai-captcha-slider-move-btn">
        </div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;


/** 刮刮卡. */
var scrapeCaptchaDivTemplate = `
<div id="tianai-captcha" class="tianai-captcha-slider tianai-captcha-scrape">
    <div id="tianai-captcha-bg-img"></div>
    <div class="slider-tip">
        <span id="tianai-captcha-slider-move-track-font">拖动滑块出现完整的
            <img id="tianai-captcha-scrape-tip-img" src="" alt/>
        后就松开</span>
    </div>
    <div class="content">
        <div class="bg-img-div">
            <img id="tianai-captcha-slider-bg-img" src="" alt/>
            <canvas id="tianai-captcha-slider-bg-canvas"></canvas>
            <div class="slider-img-div" id="tianai-captcha-slider-img-div">
        </div>
        </div>
        <div class="tianai-captcha-tips" id="tianai-captcha-tips">验证失败，请重新尝试</div>
    </div>
    <div class="slider-move">
        <div class="slider-move-track">
            <div id="tianai-captcha-slider-move-track-mask"></div>
            <div class="slider-move-shadow"></div>
        </div>
        <div class="slider-move-btn" id="tianai-captcha-slider-move-btn">
        </div>
    </div>
    <div class="slider-bottom">
        <i class="captcha-logo"></i>
        <div class="close-btn" id="tianai-captcha-slider-close-btn"></div>
        <div class="refresh-btn" id="tianai-captcha-slider-refresh-btn"></div>
    </div>
</div>
`;

/** 当前验证码 */
var currentCaptcha;

function printLog(params) {
  if (isPrintLog) {
    console.log(JSON.stringify(params));
  }
}

function wrapCaptchaConfig(captchaConfig) {
  // 判断有没有生成验证码的方法， 没有使用默认的
  if (!captchaConfig.genCaptchaFun) {
    captchaConfig.genCaptchaFun = (param, context) => {
      return new Promise((resolve) => {
        param.url = window.location.href;
        $.get(captchaConfig.genCaptchaUrl, param, function (data) {
          let convertData = data;
          if (typeof (convertData) == "string") {
            try {
              convertData = JSON.parse(data);
            } catch (e) {
              captchaConfig?.genCaptFail();
            }
          }
          return resolve(convertData);
        }).fail(err => {
          captchaConfig?.genCaptFail();
        });
      })
    }
  }
  // 判断有没有校验验证码的方法， 没有使用默认的
  if (!captchaConfig.validCaptchaFun) {
    captchaConfig.validCaptchaFun = (data, currentCaptchaData, context) => {
      const currentCaptchaId = currentCaptchaData.currentCaptchaId
      const encData = Base64.encode(JSON.stringify(data));
      return new Promise(((resolve, reject) => {
        $.post(captchaConfig.validUrl, {
          id: currentCaptchaId,
          data: encData,
          url: window.location.href
        }, function (res) {
          if (res.code === 200) {
            const useTimes = (data.endSlidingTime - data.startSlidingTime) / 1000;
            showTips(context.el, `验证成功,耗时${useTimes}秒`, 1, () => {
              resolve(res);
            });
          } else {
            let tipMsg = "验证失败，请重新尝试!";
            if (res.code) {
              switch (res.code) {
                case 4001:
                  tipMsg = "验证失败，请重新尝试!";
                  break;
                default:
                  tipMsg = "验证码被黑洞吸走了！"
                  break;
              }
            }
            showTips(context.el, tipMsg, 0, () => {
              reject(res);
            });
          }
        }).fail(err => {
          showTips(context.el, "系统响应超时", 0, () => {
            reject({success: false, msg: "系统响应超时"});
          });
        });
      }))
    }
  }
  if (!captchaConfig.validSuccessCallback) {
    captchaConfig.validSuccessCallback = () => {
      alert("验证成功，在配置项中重写 [validSuccessCallback] 方法，用于支持自定义逻辑")
    }
  }

  return captchaConfig;
}

/**
 * 创建滑动验证码
 * @param divId 绑定的div的id
 * @param captchaConfig 验证码相关配置
 * @param styleConfig 样式相关配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function createSliderCaptcha(divId, captchaConfig, styleConfig) {
  captchaConfig = wrapCaptchaConfig(captchaConfig);
  return {
    type: "slider",
    divId: divId,
    el: $(divId),
    styleConfig: styleConfig,
    captchaConfig: captchaConfig,
    currentCaptchaData: {},
    loadWindow: function () {
      let sliderImg = "data:image/png;base64,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";
      let bgImg = "";
      let moveTrackMaskBorderColor = "#00f4ab";
      let moveTrackMaskBgColor = "#a9ffe5";
      if (styleConfig) {
        if (styleConfig.btnUrl) {
          sliderImg = this.styleConfig.btnUrl;
        }
        moveTrackMaskBgColor = styleConfig.moveTrackMaskBgColor;
        moveTrackMaskBorderColor = styleConfig.moveTrackMaskBorderColor;
        if (styleConfig.bgImg) {
          bgImg = this.styleConfig.bgImg;
        }
      }
      if (bgImg != "") {
        this.el.find("#tianai-captcha-bg-img").css("background-image", "url(" + bgImg + ")");
      }
      this.el.find(".slider-move .slider-move-btn").css("background-image", "url(" + sliderImg + ")");
      // this.el.find("#tianai-captcha-slider-move-track-font").text(title);
      this.el.find("#tianai-captcha-slider-move-track-mask").css("border-color", moveTrackMaskBorderColor);
      this.el.find("#tianai-captcha-slider-move-track-mask").css("background-color", moveTrackMaskBgColor);
    },
    destoryWindow: function () {
      this.hideWindow();
      const existsCaptchaEl = this.el.children("#tianai-captcha");
      if (existsCaptchaEl) {
        existsCaptchaEl.remove();
      }
      this.captchaConfig.hideCallback();
    },
    showWindow: function () {
      showSliderWindow(this.el);
    },
    hideWindow: function () {
      this.reset();
      hideSliderWindow(this.el);
    },
    loadCaptcha: function () {
      const that = this;
      that.showWindow();
      this.captchaConfig.genCaptchaFun({}, this).then(data => {
        that.loadCaptchaForData(that, data);
      })
    },
    loadCaptchaForData: function (that, data) {
      that.showWindow();
      that.reset();
      const bgImg = that.el.find("#tianai-captcha-slider-bg-img");
      const sliderImg = that.el.find("#tianai-captcha-slider-move-img");
      bgImg.attr("src", data.captcha.backgroundImage);
      sliderImg.attr("src", data.captcha.templateImage);
      bgImg.load(() => {
        that.currentCaptchaData = initConfig(bgImg.width(), bgImg.height(), sliderImg.width(), sliderImg.height(), 295 - 63 + 5);
        that.currentCaptchaData.currentCaptchaId = data.id;
        // 重组
        drawBgImage(data.captcha, "tianai-captcha-slider-bg-canvas", "tianai-captcha-slider-bg-img", 50);
      });
    },
    reset: function () {
      this.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(0px, 0px)")
      this.el.find("#tianai-captcha-slider-img-div").css("transform", "translate(0px, 0px)")
      this.el.find("#tianai-captcha-slider-move-track-mask").css("width", 0)
      const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
      const sliderImg = this.el.find("#tianai-captcha-slider-move-img");
      bgImg.attr("src", "");
      sliderImg.attr("src", "");
      this.currentCaptchaData.currentCaptchaId = null;
    },
    valid: function () {
      const that = this;
      const currentCaptchaData = this.currentCaptchaData;
      const data = {
        bgImageWidth: currentCaptchaData.bgImageWidth,
        bgImageHeight: currentCaptchaData.bgImageHeight,
        sliderImageWidth: currentCaptchaData.sliderImageWidth,
        sliderImageHeight: currentCaptchaData.sliderImageHeight,
        startSlidingTime: currentCaptchaData.startTime,
        endSlidingTime: currentCaptchaData.stopTime,
        trackList: currentCaptchaData.trackArr
      };
      this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
        that.captchaConfig.validSuccessCallback(res, that);
      }).catch(res => {
        that.loadCaptcha();
        that.captchaConfig.validFailCallback(res, that);
      });
    }
  }
}

function closeTips(el, callback) {
  const tipEl = $(el.find("#tianai-captcha-tips"));
  tipEl.removeClass("tianai-captcha-tips-on")
  tipEl.removeClass("tianai-captcha-tips-success")
  tipEl.removeClass("tianai-captcha-tips-error")
  // 延时
  setTimeout(callback, .35);
}

function showTips(el, msg, type, callback) {
  const tipEl = $(el.find("#tianai-captcha-tips"));
  tipEl.text(msg);
  if (type === 1) {
    // 成功
    tipEl.removeClass("tianai-captcha-tips-error")
    tipEl.addClass("tianai-captcha-tips-success")
  } else {
    // 失败
    tipEl.removeClass("tianai-captcha-tips-success")
    tipEl.addClass("tianai-captcha-tips-error")
  }
  tipEl.addClass("tianai-captcha-tips-on");
  // 延时
  setTimeout(callback, 1000);
}

/**
 * 对于乱序背景图进行重组(暂时还有bug)
 * @param data 图片数据
 * @param canvasId canvas
 * @param imgId 对应的图片id
 * @param delay 延时
 */
function drawBgImage(data, canvasId, imgId, delay) {
  if (!data.data || !data.data.shuffle) {
    return;
  }
  var img = document.getElementById(imgId);
  if (img.width == 0 || img.height == 0) {
    setTimeout(drawBgImage(data, canvasId, imgId, delay), 50);
  }

  var c = document.getElementById(canvasId);
  var ctx = c.getContext("2d");
  var img = document.getElementById(imgId);
  c.width = img.width;
  c.height = img.height;

  const shuffle = data.data.shuffle;
  const sourceImageWidth = data.backgroundImageWidth;
  const sourceImageHeight = data.backgroundImageHeight;
  const canvasImageWidth = c.width;
  const canvasImageHeight = c.height;
  const xNum = shuffle.x;
  const yNum = shuffle.y;
  const pos = shuffle.pos;

  const sourceBlockX = sourceImageWidth / xNum;
  const sourceBlockY = sourceImageHeight / yNum;
  const blockX = canvasImageWidth / xNum;
  const blockY = canvasImageHeight / yNum;
  const sourceImageBlocks = [];
  const imageBlocks = [];
  for (let i = 0; i < yNum; i++) {
    for (let o = 0; o < xNum; o++) {
      sourceImageBlocks.push({
        startX: Math.floor(o * sourceBlockX),
        startY: Math.floor(i * sourceBlockY)
      });
      imageBlocks.push({
        startX: Math.round(o * blockX),
        startY: Math.round(i * blockY)
      });
    }
  }
  const evalFuns = []
  for (let i = 0; i < pos.length; i++) {
    const p = pos[i]
    const sourceBlock = sourceImageBlocks[p];
    const block = imageBlocks[i];
    evalFuns.push(() => {
      ctx.drawImage(img, sourceBlock.startX, sourceBlock.startY, sourceBlockX, sourceBlockY, block.startX, block.startY, blockX, blockY);
    });
  }
  evalFuns.sort((a, b) => {
    return Math.random() > .5 ? -1 : 1;
  });
  for (let i = 0; i < evalFuns.length; i++) {
    let fun = evalFuns[i]
    if (delay > 0) {
      setTimeout(fun, (i + 1) * delay);
    } else {
      fun();
    }
  }
}


function initWordImageClick(divId, captchaConfig, styleConfig) {
  captchaConfig = wrapCaptchaConfig(captchaConfig);
  const captcha = {
    type: "wordImageClick",
    divId: divId,
    el: $(divId),
    styleConfig: styleConfig,
    captchaConfig: captchaConfig,
    currentCaptchaData: {},
    loadWindow: function () {
      let bgImg = "";
      if (styleConfig) {
        if (this.styleConfig.bgUrl) {
          bgImg = this.styleConfig.bgUrl;
        }
      }
      this.el.find("#tianai-captcha-bg-img").css("background-image", "url(" + bgImg + ")");
    },
    showWindow: function () {
      showSliderWindow(this.el);
    },
    hideWindow: function () {
      this.reset();
      hideSliderWindow(this.el);
    },
    loadCaptcha: function () {
      const that = this;
      that.showWindow();
      this.captchaConfig.genCaptchaFun({}, this).then(data => {
        that.loadCaptchaForData(that, data);
      })
    },
    loadCaptchaForData: function (that, data) {
      that.showWindow();
      that.reset();
      const bgImg = that.el.find("#tianai-captcha-slider-bg-img");
      const tipImg = that.el.find("#tianai-captcha-tip-img");
      bgImg.load(() => {
        that.currentCaptchaData = initConfig(bgImg.width(), bgImg.height(), tipImg.width(), tipImg.height(), 295 - 63 + 5);
        that.currentCaptchaData.currentCaptchaId = data.id;
        drawBgImage(data.captcha, "tianai-captcha-slider-bg-canvas", "tianai-captcha-slider-bg-img", 50);
      })
      bgImg.attr("src", data.captcha.backgroundImage);
      tipImg.attr("src", data.captcha.templateImage);
    },
    destoryWindow: function () {
      this.hideWindow();
      const existsCaptchaEl = this.el.children("#tianai-captcha");
      if (existsCaptchaEl) {
        existsCaptchaEl.remove();
      }
    },
    reset: function () {
      const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
      const sliderImg = this.el.find("#tianai-captcha-tip-img");
      this.el.find("#bg-img-click-mask span").remove();
      bgImg.attr("src", "");
      sliderImg.attr("src", "");
      this.currentCaptchaData.currentCaptchaId = null;
    },
    valid: function () {
      const that = this;
      const currentCaptchaData = this.currentCaptchaData;
      const data = {
        bgImageWidth: currentCaptchaData.bgImageWidth,
        bgImageHeight: currentCaptchaData.bgImageHeight,
        sliderImageWidth: currentCaptchaData.sliderImageWidth,
        sliderImageHeight: currentCaptchaData.sliderImageHeight,
        startSlidingTime: currentCaptchaData.startTime,
        endSlidingTime: currentCaptchaData.stopTime,
        trackList: currentCaptchaData.trackArr
      };
      this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
        that.captchaConfig.validSuccessCallback(res, that);
      }).catch(res => {
        that.loadCaptcha();
      });
    }
  }
  currentCaptcha = captcha;
  captcha.destoryWindow();
  captcha.el.append(wordImageClickDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  captcha.el.find("#bg-img-click-mask").click(function (event) {
    currentCaptcha.currentCaptchaData.clickCount++;
    const trackArr = currentCaptcha.currentCaptchaData.trackArr;
    const startTime = currentCaptcha.currentCaptchaData.startTime;
    if (currentCaptcha.currentCaptchaData.clickCount === 1) {
      // move 轨迹
      window.addEventListener("mousemove", move);
      currentCaptcha.currentCaptchaData.startX = event.offsetX;
      currentCaptcha.currentCaptchaData.startY = event.offsetY;
    }
    trackArr.push({
      x: Math.round(event.offsetX),
      y: Math.round(event.offsetY),
      type: "click",
      t: (new Date().getTime() - startTime.getTime())
    });
    const left = event.offsetX - 10;
    const top = event.offsetY - 10;
    captcha.el.find("#bg-img-click-mask").append("<span class='click-span' style='left:" + left + "px;top: " + top + "px'>" + currentCaptcha.currentCaptchaData.clickCount + "</span>")
    if (currentCaptcha.currentCaptchaData.clickCount === 4) {
      // 校验
      currentCaptcha.currentCaptchaData.stopTime = new Date();
      window.removeEventListener("mousemove", move);
      currentCaptcha.valid();
    }
  });


  clearAllPreventDefault(captcha.el);
  return captcha;
}


function initWordOrderImageClick(divId, captchaConfig, styleConfig) {
  captchaConfig = wrapCaptchaConfig(captchaConfig);
  const captcha = {
    type: "wordOrderImageClick",
    divId: divId,
    el: $(divId),
    styleConfig: styleConfig,
    captchaConfig: captchaConfig,
    currentCaptchaData: {},
    loadWindow: function () {
      let bgImg = "";
      if (styleConfig) {
        if (this.styleConfig.bgUrl) {
          bgImg = this.styleConfig.bgUrl;
        }
      }
      this.el.find("#tianai-captcha-bg-img").css("background-image", "url(" + bgImg + ")");
    },
    showWindow: function () {
      showSliderWindow(this.el);
    },
    hideWindow: function () {
      this.reset();
      hideSliderWindow(this.el);
    },
    loadCaptcha: function () {
      const that = this;
      that.showWindow();
      this.captchaConfig.genCaptchaFun({}, this).then(data => {
        that.loadCaptchaForData(that, data);
      })
    },
    destoryWindow: function () {
      this.hideWindow();
      const existsCaptchaEl = this.el.children("#tianai-captcha");
      if (existsCaptchaEl) {
        existsCaptchaEl.remove();
      }
    },
    loadCaptchaForData: function (that, data) {
      that.showWindow();
      that.reset();
      const bgImg = that.el.find("#tianai-captcha-slider-bg-img");
      bgImg.load(() => {
        that.currentCaptchaData = initConfig(bgImg.width(), bgImg.height(), 0, 0, 295 - 63 + 5);
        that.currentCaptchaData.currentCaptchaId = data.id;
        drawBgImage(data.captcha, "tianai-captcha-slider-bg-canvas", "tianai-captcha-slider-bg-img", 50);
      })
      bgImg.attr("src", data.captcha.backgroundImage);

      // 重组
      // setTimeout(() => {
      // }, 300);
    },
    reset: function () {
      const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
      this.el.find("#bg-img-click-mask span").remove();
      bgImg.attr("src", "");
      this.currentCaptchaData.currentCaptchaId = null;
    },
    valid: function () {
      const that = this;
      const currentCaptchaData = this.currentCaptchaData;
      const data = {
        bgImageWidth: currentCaptchaData.bgImageWidth,
        bgImageHeight: currentCaptchaData.bgImageHeight,
        sliderImageWidth: currentCaptchaData.sliderImageWidth,
        sliderImageHeight: currentCaptchaData.sliderImageHeight,
        startSlidingTime: currentCaptchaData.startTime,
        endSlidingTime: currentCaptchaData.stopTime,
        trackList: currentCaptchaData.trackArr
      };
      this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
        that.captchaConfig.validSuccessCallback(res, that);
      }).catch(res => {
        that.loadCaptcha();
      });
    }
  }
  currentCaptcha = captcha;
  captcha.destoryWindow();
  captcha.el.append(wordOrderImageClickDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  captcha.el.find("#bg-img-click-mask").click(function (event) {
    currentCaptcha.currentCaptchaData.clickCount++;
    const trackArr = currentCaptcha.currentCaptchaData.trackArr;
    const startTime = currentCaptcha.currentCaptchaData.startTime;
    if (currentCaptcha.currentCaptchaData.clickCount === 1) {
      // move 轨迹
      window.addEventListener("mousemove", move);
      currentCaptcha.currentCaptchaData.startX = event.offsetX;
      currentCaptcha.currentCaptchaData.startY = event.offsetY;
    }
    trackArr.push({
      x: Math.round(event.offsetX),
      y: Math.round(event.offsetY),
      type: "click",
      t: (new Date().getTime() - startTime.getTime())
    });
    const left = event.offsetX - 10;
    const top = event.offsetY - 10;
    captcha.el.find("#bg-img-click-mask").append("<span class='click-span' style='left:" + left + "px;top: " + top + "px'>" + currentCaptcha.currentCaptchaData.clickCount + "</span>")
    if (currentCaptcha.currentCaptchaData.clickCount === 4) {
      // 校验
      currentCaptcha.currentCaptchaData.stopTime = new Date();
      window.removeEventListener("mousemove", move);
      currentCaptcha.valid();
    }
  });

  clearAllPreventDefault(captcha.el);
  return captcha;
}

/**
 * 初始化滑动拼图验证码
 * @param divId 绑定的div
 * @param captchaConfig 验证码配置
 * @param styleConfig 样式配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function initSliderCaptcha(divId, captchaConfig, styleConfig) {
  const captcha = createSliderCaptcha(divId, captchaConfig, styleConfig);
  // 全局
  currentCaptcha = captcha;
  // 隐藏窗口
  // captcha.hideWindow();
  // 载入div块
  const existsCaptchaEl = captcha.el.children("#tianai-captcha");
  if (existsCaptchaEl) {
    existsCaptchaEl.remove();
  }
  captcha.el.append(sliderCaptchaDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 滑动按钮绑定事件
  captcha.el.find("#tianai-captcha-slider-move-btn").mousedown(down);
  captcha.el.find("#tianai-captcha-slider-move-btn").on("touchstart", down);
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  clearAllPreventDefault(captcha.el);
  return captcha;
}

/**
 * 初始化旋转验证码
 * @param divId 对应的divId
 * @param captchaConfig 验证码配置
 * @param styleConfig 样式配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function initRotateCaptcha(divId, captchaConfig, styleConfig) {
  const captcha = createSliderCaptcha(divId, captchaConfig, styleConfig);
  captcha.type = "rotate";
  captcha.reset = function () {
    this.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(0px, 0px)")
    this.el.find("#tianai-captcha-slider-move-img").css("transform", "rotate(0deg)")
    this.el.find("#tianai-captcha-slider-move-track-mask").css("width", 0)
    const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
    const sliderImg = this.el.find("#tianai-captcha-slider-move-img");
    bgImg.attr("src", "");
    sliderImg.attr("src", "");
    this.currentCaptchaData.currentCaptchaId = null;
  }
  captcha.valid = function () {
    let data = {
      bgImageWidth: this.currentCaptchaData.end,
      bgImageHeight: this.currentCaptchaData.bgImageHeight,
      sliderImageWidth: this.currentCaptchaData.sliderImageWidth,
      sliderImageHeight: this.currentCaptchaData.sliderImageHeight,
      startSlidingTime: this.currentCaptchaData.startTime,
      endSlidingTime: this.currentCaptchaData.stopTime,
      trackList: this.currentCaptchaData.trackArr
    };
    const that = this;
    this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
      that.captchaConfig.validSuccessCallback(res, that);
    }).catch(res => {
      that.loadCaptcha();
    });
  }
  // 全局
  currentCaptcha = captcha;
  // 载入div块
  const existsCaptchaEl = captcha.el.children("#tianai-captcha");
  if (existsCaptchaEl) {
    existsCaptchaEl.remove();
  }
  captcha.el.append(rotateCaptchaDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 滑动按钮绑定事件
  captcha.el.find("#tianai-captcha-slider-move-btn").mousedown(down);
  captcha.el.find("#tianai-captcha-slider-move-btn").on("touchstart", down);
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  clearAllPreventDefault(captcha.el);
  return captcha;

}

/**
 * 初始化类百度旋转验证码
 * @param divId divId
 * @param captchaConfig 验证码配置
 * @param styleConfig 样式配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function initRotateCaptchaDegree(divId, captchaConfig, styleConfig) {
  const captcha = createSliderCaptcha(divId, captchaConfig, styleConfig);
  captcha.type = "rotate_degree";
  captcha.reset = function () {
    this.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(0px, 0px)")
    this.el.find("#tianai-captcha-slider-bg-img").css("transform", "rotate(0deg)")
    this.el.find("#tianai-captcha-slider-bg-degree-canvas").css("transform", "rotate(0deg)")
    this.el.find("#tianai-captcha-slider-move-track-mask").css("width", 0)
    const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
    bgImg.attr("src", "");
    this.currentCaptchaData.currentCaptchaId = null;
  }
  captcha.loadCaptchaForData = function (that, data) {
    that.showWindow();
    that.reset();
    that.el.find("#tianai-captcha-slider-bg-img").attr("src", data.captcha.backgroundImage);
    that.el.find("#tianai-captcha-slider-bg-img").load(() => {
      const bgImg = that.el.find(".bg-img-div");
      that.currentCaptchaData = initConfig(bgImg.width(), bgImg.height(), 0, 0, 248 - 63 + 5);
      that.currentCaptchaData.currentCaptchaId = data.id;
      // 重组
      // setTimeout(() => {
      drawBgImage(data.captcha, "tianai-captcha-slider-bg-degree-canvas", "tianai-captcha-slider-bg-img", 30);
      // }, 300);
    });

  }
  captcha.valid = function () {
    let data = {
      bgImageWidth: this.currentCaptchaData.end,
      bgImageHeight: this.currentCaptchaData.bgImageHeight,
      sliderImageWidth: this.currentCaptchaData.sliderImageWidth,
      sliderImageHeight: this.currentCaptchaData.sliderImageHeight,
      startSlidingTime: this.currentCaptchaData.startTime,
      endSlidingTime: this.currentCaptchaData.stopTime,
      trackList: this.currentCaptchaData.trackArr
    };
    const that = this;
    this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
      that.captchaConfig.validSuccessCallback(res, that);
    }).catch(res => {
      that.loadCaptcha();
    });
  }
  // 全局
  currentCaptcha = captcha;
  // 载入div块
  const existsCaptchaEl = captcha.el.children("#tianai-captcha");
  if (existsCaptchaEl) {
    existsCaptchaEl.remove();
  }
  captcha.el.append(rotateDegreeCaptchaDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 滑动按钮绑定事件
  captcha.el.find("#tianai-captcha-slider-move-btn").mousedown(down);
  captcha.el.find("#tianai-captcha-slider-move-btn").on("touchstart", down);
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  clearAllPreventDefault(captcha.el);
  return captcha;

}


/**
 * 初始化刮刮可验证码
 * @param divId 绑定的div
 * @param captchaConfig 验证码配置
 * @param styleConfig 样式配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function initScrapeCaptcha(divId, captchaConfig, styleConfig) {
  const captcha = createSliderCaptcha(divId, captchaConfig, styleConfig);
  captcha.type = "scrape";
  captcha.reset = function () {
    let left = 0;
    if (captcha.left) {
      left = captcha.left;
    }
    this.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(" + left + "px, 0px)")
    this.el.find("#tianai-captcha-slider-move-img").css("transform", "rotate(0deg)")
    this.el.find("#tianai-captcha-slider-move-track-mask").css("width", 0)
    const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
    const sliderImg = this.el.find("#tianai-captcha-slider-move-img");
    bgImg.attr("src", "");
    sliderImg.attr("src", "");
    this.currentCaptchaData.currentCaptchaId = null;
  }
  captcha.loadCaptchaForData = function (that, data) {
    that.showWindow();
    that.reset();
    that.el.find("#tianai-captcha-slider-bg-img").attr("src", data.captcha.backgroundImage);
    that.el.find("#tianai-captcha-scrape-tip-img").attr("src", data.captcha.templateImage);
    const left = data.captcha.data.blockWidth;
    const zoom = data.captcha.backgroundImageWidth / that.el.find("#tianai-captcha-slider-img-div").width();
    that.left = left / zoom;
    that.el.find("#tianai-captcha-slider-img-div").css("transform", "translate(" + that.left + "px, 0px)")
    that.el.find("#tianai-captcha-slider-bg-img").load(() => {
      const bgImg = that.el.find(".bg-img-div");
      that.currentCaptchaData = initConfig(bgImg.width(), bgImg.height(), 0, 0, 300 - 55);
      that.currentCaptchaData.currentCaptchaId = data.id;
      // 重组
      // setTimeout(() => {
      drawBgImage(data.captcha, "tianai-captcha-slider-bg-canvas", "tianai-captcha-slider-bg-img", 0);
      // }, 300);
    });

  }
  captcha.valid = function () {
    const that = this;
    const currentCaptchaData = this.currentCaptchaData;
    const data = {
      bgImageWidth: currentCaptchaData.bgImageWidth,
      bgImageHeight: currentCaptchaData.bgImageHeight,
      sliderImageWidth: currentCaptchaData.sliderImageWidth,
      sliderImageHeight: currentCaptchaData.sliderImageHeight,
      startSlidingTime: currentCaptchaData.startTime,
      endSlidingTime: currentCaptchaData.stopTime,
      trackList: currentCaptchaData.trackArr
    };
    this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
      that.captchaConfig.validSuccessCallback(res, that);
    }).catch(res => {
      that.loadCaptcha();
    });
  }
  // 全局
  currentCaptcha = captcha;
  // 载入div块
  const existsCaptchaEl = captcha.el.children("#tianai-captcha");
  if (existsCaptchaEl) {
    existsCaptchaEl.remove();
  }
  captcha.el.append(scrapeCaptchaDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 滑动按钮绑定事件
  captcha.el.find("#tianai-captcha-slider-move-btn").mousedown(down);
  captcha.el.find("#tianai-captcha-slider-move-btn").on("touchstart", down);
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  clearAllPreventDefault(captcha.el);
  return captcha;
}


/**
 * 根据验证码类型生成对应的验证码样式
 * @param divId divId
 * @param captchaConfig 验证码配置
 * @param styleConfig 样式配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function initRandomCaptcha(divId, captchaConfig, styleConfig) {
  let captcha = createSliderCaptcha(divId, captchaConfig, styleConfig);
  captcha.loadCaptcha = function () {
    const that = this;
    that.showWindow();
    this.captchaConfig.genCaptchaFun({}, this).then(data => {
      const type = data.captcha.type;
      let newCaptcha;
      if (type === 'SLIDER') {
        newCaptcha = initSliderCaptcha(that.divId, that.captchaConfig, that.styleConfig);
      } else if (type === 'CONCAT') {
        newCaptcha = initConcatCaptcha(that.divId, that.captchaConfig, that.styleConfig);
      } else if (type === 'ROTATE') {
        newCaptcha = initRotateCaptcha(that.divId, that.captchaConfig, that.styleConfig);
      } else if (type === 'ROTATE_DEGREE') {
        newCaptcha = initRotateCaptchaDegree(that.divId, that.captchaConfig, that.styleConfig);
      } else if (type === 'WORD_IMAGE_CLICK') {
        newCaptcha = initWordImageClick(that.divId, that.captchaConfig, that.styleConfig)
      } else if (type === 'IMAGE_CLICK') {
        newCaptcha = initWordImageClick(that.divId, that.captchaConfig, that.styleConfig)
        newCaptcha.type = "imageClick";
      } else if (type === 'WORD_ORDER_IMAGE_CLICK') {
        newCaptcha = initWordOrderImageClick(that.divId, that.captchaConfig, that.styleConfig)
      } else if (type === 'SCRAPE') {
        newCaptcha = initScrapeCaptcha(that.divId, that.captchaConfig, that.styleConfig);
      } else {
        throw new Error("不支持的类型[" + type + "]");
      }
      newCaptcha.loadCaptchaForData(newCaptcha, data);
      newCaptcha.hideWindow = captcha.hideWindow;
      newCaptcha.showWindow = captcha.showWindow;
      newCaptcha.destoryWindow = captcha.destoryWindow;
      newCaptcha.loadCaptcha = captcha.loadCaptcha;
    })
  }
  // 全局
  return captcha;
}

/**
 * 初始化拼接验证码
 * @param divId divId
 * @param captchaConfig 验证码配置
 * @param styleConfig 样式配置
 * @returns {{el: (*|jQuery|HTMLElement), styleConfig, currentCaptchaData: {}, loadCaptcha: loadCaptcha, type: string, showWindow: showWindow, loadWindow: loadWindow, valid: valid, hideWindow: hideWindow, successCallback: successCallback, reset: reset, loadCaptchaForData: loadCaptchaForData, divId, captchaConfig}}
 */
function initConcatCaptcha(divId, captchaConfig, styleConfig) {
  const captcha = createSliderCaptcha(divId, captchaConfig, styleConfig);
  captcha.type = "concat";
  captcha.reset = function () {
    this.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(0px, 0px)")
    this.el.find("#tianai-captcha-slider-img-div").css("transform", "rotate(0deg)")
    this.el.find("#tianai-captcha-slider-move-track-mask").css("width", 0)
    const bgImg = this.el.find("#tianai-captcha-slider-bg-img");
    const sliderImg = this.el.find("#tianai-captcha-slider-move-img");
    bgImg.attr("src", "");
    sliderImg.attr("src", "");
    this.currentCaptchaData.currentCaptchaId = null;
  }
  captcha.valid = function () {
    let data = {
      bgImageWidth: this.currentCaptchaData.bgImageWidth,
      bgImageHeight: this.currentCaptchaData.bgImageHeight,
      sliderImageWidth: this.currentCaptchaData.sliderImageWidth,
      sliderImageHeight: this.currentCaptchaData.sliderImageHeight,
      startSlidingTime: this.currentCaptchaData.startTime,
      endSlidingTime: this.currentCaptchaData.stopTime,
      trackList: this.currentCaptchaData.trackArr
    };
    const that = this;
    // console.log("valid", captchaConfig, data);
    this.captchaConfig.validCaptchaFun(data, this.currentCaptchaData, this).then(res => {
      that.captchaConfig.validSuccessCallback(res, that);
    }).catch(res => {
      that.loadCaptcha();
    });
  }
  captcha.loadCaptchaForData = function (that, data) {
    that.showWindow();
    that.reset();
    const bgImg = that.el.find(".tianai-captcha-slider-concat-bg-img");
    const sliderImg = that.el.find("#tianai-captcha-slider-concat-img-div");
    bgImg.css("background-image", "url(" + data.captcha.backgroundImage + ")");
    sliderImg.css("background-image", "url(" + data.captcha.backgroundImage + ")");
    sliderImg.css("background-position", "0px 0px");
    that.currentCaptchaData = initConfig(bgImg.width(), bgImg.height(), sliderImg.width(), sliderImg.height(), 295 - 63 + 5);
    var backgroundImageHeight = data.captcha.backgroundImageHeight;
    var height = ((backgroundImageHeight - data.captcha.data.randomY) / backgroundImageHeight) * 180;
    sliderImg.css("height", height)
    that.currentCaptchaData.currentCaptchaId = data.id;
  }
  // 全局
  currentCaptcha = captcha;
  // 载入div块
  const existsCaptchaEl = captcha.el.children("#tianai-captcha");
  if (existsCaptchaEl) {
    existsCaptchaEl.remove();
  }
  captcha.el.append(concatCaptchaDivTemplate);
  // 加载样式
  captcha.loadWindow();
  // 滑动按钮绑定事件
  captcha.el.find("#tianai-captcha-slider-move-btn").mousedown(down);
  captcha.el.find("#tianai-captcha-slider-move-btn").on("touchstart", down);
  // 刷新按钮绑定样式
  captcha.el.find("#tianai-captcha-slider-refresh-btn").click(function () {
    captcha.loadCaptcha();
  })
  captcha.el.find("#tianai-captcha-slider-close-btn").click(function () {
    captcha.destoryWindow();
  });
  clearAllPreventDefault(captcha.el);
  return captcha;

}


function initConfig(bgImageWidth, bgImageHeight, sliderImageWidth, sliderImageHeight, end) {
  const currentCaptchaConfig = {
    startTime: new Date(),
    trackArr: [],
    movePercent: 0,
    clickCount: 0,
    bgImageWidth: bgImageWidth,
    bgImageHeight: bgImageHeight,
    sliderImageWidth: sliderImageWidth,
    sliderImageHeight: sliderImageHeight,
    end: end
  }
  printLog(["init", currentCaptchaConfig]);
  return currentCaptchaConfig;
}

function down(event) {
  const coordinate = getCurrentCoordinate(event);
  let startX = coordinate.x;
  let startY = coordinate.y;
  currentCaptcha.currentCaptchaData.startX = startX;
  currentCaptcha.currentCaptchaData.startY = startY;

  const pageX = currentCaptcha.currentCaptchaData.startX;
  const pageY = currentCaptcha.currentCaptchaData.startY;
  const startTime = currentCaptcha.currentCaptchaData.startTime;
  const trackArr = currentCaptcha.currentCaptchaData.trackArr;
  trackArr.push({
    x: pageX - startX,
    y: pageY - startY,
    type: "down",
    t: (new Date().getTime() - startTime.getTime())
  });
  printLog(["start", startX, startY])
  // pc
  window.addEventListener("mousemove", move);
  window.addEventListener("mouseup", up);
  // 手机端
  window.addEventListener("touchmove", move, false);
  window.addEventListener("touchend", up, false);
  doDown();
}

function doDown() {
  // $("#slider-move-btn").css("background-position", "-5px 31.0092%")
  if (currentCaptcha.type === 'rotate_degree') {
    currentCaptcha.el.find(".tianai-captcha-slider-bg-img-mask").css("display", "block")
  }
}


function move(event) {
  if (event.touches && event.touches.length > 0) {
    event = event.touches[0];
  }
  const coordinate = getCurrentCoordinate(event);
  let pageX = coordinate.x;
  let pageY = coordinate.y;
  const startX = currentCaptcha.currentCaptchaData.startX;
  const startY = currentCaptcha.currentCaptchaData.startY;
  const startTime = currentCaptcha.currentCaptchaData.startTime;
  const end = currentCaptcha.currentCaptchaData.end;
  const bgImageWidth = currentCaptcha.currentCaptchaData.bgImageWidth;
  const trackArr = currentCaptcha.currentCaptchaData.trackArr;
  let moveX = pageX - startX;
  const track = {
    x: pageX - startX,
    y: pageY - startY,
    type: "move",
    t: (new Date().getTime() - startTime.getTime())
  };
  trackArr.push(track);
  if (moveX < 0) {
    moveX = 0;
  } else if (moveX > end) {
    moveX = end;
  }
  currentCaptcha.currentCaptchaData.moveX = moveX;
  currentCaptcha.currentCaptchaData.movePercent = moveX / bgImageWidth;
  doMove();
  printLog(["move", track])
}

function doMove() {
  const moveX = currentCaptcha.currentCaptchaData.moveX;
  if (currentCaptcha.type === 'slider') {
    // 滑动验证
    currentCaptcha.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(" + moveX + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-img-div").css("transform", "translate(" + moveX + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-move-track-mask").css("width", moveX + "px")
  } else if (currentCaptcha.type === 'rotate') {
    currentCaptcha.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(" + moveX + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-move-img").css("transform", "rotate(" + (moveX / (currentCaptcha.currentCaptchaData.end / 360)) + "deg)")
    currentCaptcha.el.find("#tianai-captcha-slider-move-track-mask").css("width", moveX + "px")
  } else if (currentCaptcha.type === 'concat') {
    currentCaptcha.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(" + moveX + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-concat-img-div").css("background-position-x", moveX + "px");
    currentCaptcha.el.find("#tianai-captcha-slider-move-track-mask").css("width", moveX + "px")
  } else if (currentCaptcha.type === 'rotate_degree') {
    currentCaptcha.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(" + moveX + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-bg-img").css("transform", "rotate(" + (moveX / (currentCaptcha.currentCaptchaData.end / 360)) + "deg)")
    currentCaptcha.el.find("#tianai-captcha-slider-bg-degree-canvas").css("transform", "rotate(" + (moveX / (currentCaptcha.currentCaptchaData.end / 360)) + "deg)")
    currentCaptcha.el.find("#tianai-captcha-slider-move-track-mask").css("width", moveX + "px")
  } else if (currentCaptcha.type === 'scrape') {
    currentCaptcha.el.find("#tianai-captcha-slider-move-btn").css("transform", "translate(" + moveX + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-img-div").css("transform", "translate(" + (currentCaptcha.left + moveX) + "px, 0px)")
    currentCaptcha.el.find("#tianai-captcha-slider-move-track-mask").css("width", moveX + "px")
  }
}

function up(event) {
  window.removeEventListener("mousemove", move);
  window.removeEventListener("mouseup", up);
  window.removeEventListener("touchmove", move);
  window.removeEventListener("touchend", up);
  const coordinate = getCurrentCoordinate(event);
  currentCaptcha.currentCaptchaData.stopTime = new Date();
  let pageX = coordinate.x;
  let pageY = coordinate.y;
  const startX = currentCaptcha.currentCaptchaData.startX;
  const startY = currentCaptcha.currentCaptchaData.startY;
  const startTime = currentCaptcha.currentCaptchaData.startTime;
  const trackArr = currentCaptcha.currentCaptchaData.trackArr;

  const track = {
    x: pageX - startX,
    y: pageY - startY,
    type: "up",
    t: (new Date().getTime() - startTime.getTime())
  }

  trackArr.push(track);
  printLog(["up", track])
  doUp();
  currentCaptcha.valid();
}

function doUp() {
  if (currentCaptcha.type === 'rotate_degree') {
    currentCaptcha.el.find(".tianai-captcha-slider-bg-img-mask").css("display", "none")
  }
}


/**
 * 获取当前坐标
 * @param event 事件
 * @returns {{x: number, y: number}}
 */
function getCurrentCoordinate(event) {
  let startX, startY;
  if (event.pageX) {
    startX = event.pageX;
    startY = event.pageY;
  }
  let targetTouches;
  if (event.changedTouches) {
    // 抬起事件
    targetTouches = event.changedTouches;
  } else if (event.targetTouches) {
    // pc 按下事件
    targetTouches = event.targetTouches;
  } else if (event.originalEvent && event.originalEvent.targetTouches) {
    // 鼠标触摸事件
    targetTouches = event.originalEvent.targetTouches;
  }
  if (!startX && targetTouches[0].pageX) {
    startX = targetTouches[0].pageX;
    startY = targetTouches[0].pageY;
  }
  if (!startX && targetTouches[0].clientX) {
    startX = targetTouches[0].clientX;
    startY = targetTouches[0].clientY;
  }
  if (startX && startY) {
    startX = Math.round(startX);
    startY = Math.round(startY);
  }
  return {
    x: startX,
    y: startY
  }
}


function showSliderWindow(el) {
  el.css("display", "block")
}

function hideSliderWindow(el) {
  el.css("display", "none")
}

// 导出（使用模块化引用时需要导出对应的方法）
// export {initRandomCaptcha, initSliderCaptcha, initRotateCaptcha,initRotateCaptchaDegree,initWordImageClick,initWordOrderImageClick,initConcatCaptcha};
