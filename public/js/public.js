/**全局复制监听**/
document.addEventListener("copy", copyEvent);
/**全局点击监听**/
document.addEventListener("mousedown", mousedownGlobalEvent);

let publicCopyFlag = false;

// 全局复制事件
function copyEvent(e) {
  if (publicCopyFlag) {
    publicCopyFlag = false;
    return true;
  }
  publicCopyFlag = true;
  // 记录原来选中的range
  let oldRange = window.getSelection().getRangeAt(0);
  // 不执行处理的复制
  if (oldRange && oldRange.startContainer && oldRange.startContainer.children) {
    let isInputOrTextarea = false;
    Array.prototype.find.call(oldRange.startContainer.children, item => {
      if ((item.nodeName == "INPUT" || item.nodeName == "TEXTAREA") && item.getAttribute("id") != "fileInput") {
        isInputOrTextarea = true;
      }
    });
    if (isInputOrTextarea) {
      return true;
    }
  }
  let node = getRangContent(1);
  document.getElementById("copyDivElm").appendChild(node);
  // 创建一个选择框
  let range = new Range();
  // 选中更改过的对象
  setTimeout(function () {
    range.selectNode(document.getElementById("copyDivElm"));
    window.getSelection().removeAllRanges();
    window.getSelection().addRange(range);
    document.execCommand("copy");
    setTimeout(function () {
      // 移除复制的内容
      document.getElementById("copyDivElmNode").remove();
      // 移除所有的选区
      window.getSelection().removeAllRanges();
      // 选中原来的值
      window.getSelection().addRange(oldRange);
    }, 0);
  }, 0);
}

// 全局点击事件
function mousedownGlobalEvent(e) {
  try {
    let clickParam = {x: e.pageX, y: e.pageY, which: e.which};
    // 获取窗口名
    let winId = nw.Window.get().cWindow.id;
    let childWin = remote.store.state.childWin;
    for (let key in childWin) {
      if (childWin[key] == winId) {
        clickParam.win = key;
        break;
      }
    }
    let pathList = [];
    // 获取操作路径
    for (let i = 0; i < e.path.length; i++) {
      let item = e.path[i];
      if (item.nodeName == "BODY") {
        break;
      }
      let className = item.className.split(/\s+/).join(".");
      pathList.push(item.nodeName + (item.id ? "#" + item.id : className ? "." + className : ""));
    }
    clickParam.path = pathList.reverse().join(">");
    remote.store.state.logger.writeLogFile(4, JSON.stringify(clickParam));
  } catch (e) {}
}

// 获取选中区域内容 type-1复制-2转需求
function getRangContent(type) {
  let node = document.createElement("div");
  try {
    node.setAttribute("id", "copyDivElmNode");
    node.appendChild(window.getSelection().getRangeAt(0).cloneContents());
    // 复制dataCopy内容
    let dataCopy = node.querySelectorAll(".dataCopy");
    for (let i = 0; i < dataCopy.length; i++) {
      // 转需求不复制头像和名字区域
      if (type == 2 && (dataCopy[i].classList.contains("avatar") || dataCopy[i].classList.contains("user-name-box") || dataCopy[i].classList.contains("user-name"))) {
        dataCopy[i].parentNode.removeChild(dataCopy[i]);
        continue;
      }
      let copyNode = document.createElement("div");
      copyNode.innerHTML = dataCopy[i].innerText;
      dataCopy[i].parentNode.insertBefore(copyNode, dataCopy[i]);
      dataCopy[i].parentNode.removeChild(dataCopy[i]);
    }
    // 删除禁止复制元素/通知消息元素
    let noCopyElm = node.querySelectorAll(".notCopy,.msg-li-notification,.msg-li-tip");
    for (let i = 0; i < noCopyElm.length; i++) {
      noCopyElm[i].remove();
    }
    // 替换图片内容
    let imgs = node.querySelectorAll("img");
    if (imgs && imgs.length > 0) {
      for (let i = 0; i < imgs.length; i++) {
        if (imgs[i].classList.contains("im-image-hait")) {
          imgs[i].outerHTML = `<span>${imgs[i].dataset.haitText}</span>`;
          continue;
        }
        let thisSrc = imgs[i].getAttribute("src");
        // 处理小乐表情
        if (/img\/emoji\//.test(thisSrc)) {
          imgs[i].outerHTML = `<span>${getEmojiText(thisSrc.slice(thisSrc.lastIndexOf("/") + 1))}</span>`;
          continue
        }
        if (type == 1) {
          // 处理本地小图
          let thumReg = /\\images\\thum\\/;
          if (thumReg.test(thisSrc)) {
            thisSrc = thisSrc.replace(thumReg, `/images/`).replace(/\\/g, "//");
          }
          // 处理云信小图
          let thisSrcIndex = thisSrc.lastIndexOf('?');
          if (thisSrcIndex > -1) {
            thisSrc = thisSrc.slice(0, thisSrcIndex);
          }
          imgs[i].setAttribute("src", thisSrc);
        } else if (type == 2) {
          if (!/https?/.test(thisSrc)) {
            thisSrc = imgs[i].getAttribute("data-src");
          }
          imgs[i].outerHTML = `<img src="${thisSrc}">`;
        }
      }
    }
    // 替换空格和制表符
    let msgTexts = node.querySelectorAll(".msg-text");
    if (msgTexts && msgTexts.length > 0) {
      for (let i = 0; i < msgTexts.length; i++) {
        let thisHtml = "";
        for (let j = 0; j < msgTexts[i].childNodes.length; j++) {
          if (msgTexts[i].childNodes[j].nodeType == 3) {
            // 文本节点
            thisHtml += ((msgTexts[i].childNodes[j].textContent) || "").replace(/&/g, "&amp;").replace(/[<]/g, "&lt;").replace(/[>]/g, "&gt;").replace(/ /g, "&nbsp;");
          } else {
            thisHtml += msgTexts[i].childNodes[j].outerHTML;
          }
        }
        msgTexts[i].innerHTML = thisHtml;
      }
    }
    // 去除注释
    node.innerHTML = node.innerHTML.replace(/<!--(.|\s)*?-->/g, "");
  } catch (e) {
    console.log("getRangContent", e);
  }
  return node;
}

/**乐聊表情**/
let publicJjEmoji = {
  "[乐乐微笑]": "jjs1.png",
  "[乐乐大笑]": "jjs2.png",
  "[乐乐噢耶]": "jjs3.png",
  "[乐乐色]": "jjs4.png",
  "[乐乐亲亲]": "jjs5.png",
  "[乐乐爱慕]": "jjs6.png",
  "[乐乐偷笑]": "jjs7.png",
  "[乐乐鼓掌]": "jjs8.png",
  "[乐乐可爱]": "jjs9.png",
  "[乐乐调皮]": "jjs10.png",
  "[乐乐好吃]": "jjs11.png",
  "[乐乐表白]": "jjs12.png",
  "[乐乐害羞]": "jjs13.png",
  "[乐乐奋斗]": "jjs14.png",
  "[乐乐收到]": "jjs15.png",
  "[乐乐耍酷]": "jjs16.png",
  "[乐乐坏笑]": "jjs17.png",
  "[乐乐奸笑]": "jjs18.png",
  "[乐乐机智]": "jjs19.png",
  "[乐乐嘿哈]": "jjs20.png",
  "[乐乐鬼脸]": "jjs21.png",
  "[乐乐傲慢]": "jjs22.png",
  "[乐乐嚎哭]": "jjs23.png",
  "[乐乐流泪]": "jjs24.png",
  "[乐乐捂脸]": "jjs25.png",
  "[乐乐敲打]": "jjs26.png",
  "[乐乐吐血]": "jjs27.png",
  "[乐乐恐惧]": "jjs28.png",
  "[乐乐呕吐]": "jjs29.png",
  "[乐乐衰]": "jjs30.png",
  "[乐乐挖鼻]": "jjs31.png",
  "[乐乐哈欠]": "jjs32.png",
  "[乐乐睡着]": "jjs33.png",
  "[乐乐感冒]": "jjs34.png",
  "[乐乐思考]": "jjs35.png",
  "[乐乐尴尬]": "jjs36.png",
  "[乐乐惊吓]": "jjs37.png",
  "[乐乐发怒]": "jjs38.png",
  "[乐乐崩溃]": "jjs39.png",
  "[乐乐难受]": "jjs40.png",
  "[乐乐笑哭]": "jjs41.png",
  "[乐乐流汗]": "jjs42.png",
  "[乐乐擦汗]": "jjs43.png",
  "[乐乐盲目]": "jjs44.png",
  "[乐乐心累]": "jjs45.png",
  "[乐乐晕了]": "jjs46.png",
  "[乐乐无视]": "jjs47.png",
  "[乐乐委屈]": "jjs48.png",
  "[乐乐鄙视]": "jjs49.png",
  "[乐乐托腮]": "jjs50.png",
  "[乐乐无奈]": "jjs51.png",
  "[乐乐纠结]": "jjs52.png",
  "[乐乐安慰]": "jjs53.png",
  "[乐乐难过]": "jjs54.png",
  "[乐乐疑问]": "jjs55.png",
  "[乐乐惊奇]": "jjs56.png",
  "[乐乐闭嘴]": "jjs57.png",
  "[乐乐呆萌]": "jjs58.png",
  "[乐乐白眼]": "jjs59.png",
  "[乐乐可怜]": "jjs60.png",
  "[乐乐再见]": "jjs61.png",
  "[乐乐抱抱]": "jjs62.png",
  "[乐乐炮竹]": "jjs63.png",
  "[乐乐炸弹]": "jjs64.png",
  "[乐乐生日]": "jjs65.png",
  "[乐乐礼物]": "jjs66.png",
  "[乐乐情书]": "jjs67.png",
  "[乐乐玫瑰]": "jjs68.png",
  "[乐乐凋谢]": "jjs69.png",
  "[乐乐心碎]": "jjs70.png",
  "[乐乐爱心]": "jjs71.png",
  "[乐乐吻]": "jjs72.png",
  "[乐乐爆筋]": "jjs73.png",
  "[乐乐闪电]": "jjs74.png",
  "[乐乐便便]": "jjs75.png",
  "[乐乐菜刀]": "jjs76.png",
  "[乐乐太阳]": "jjs77.png",
  "[乐乐拳头]": "jjs78.png",
  "[乐乐超赞]": "jjs79.png",
  "[乐乐胜利]": "jjs80.png",
  "[乐乐差劲]": "jjs81.png",
  "[乐乐勾引]": "jjs82.png",
  "[乐乐握手]": "jjs83.png",
  "[乐乐OK]": "jjs84.png",
  "[乐乐不]": "jjs85.png",
  "[乐乐爱你]": "jjs86.png",
  "[乐乐抱拳]": "jjs87.png",
  "[乐乐吃瓜]": "jjs88.png",
  "[乐乐帥]": "jjs89.png",
  "[乐乐微微笑]": "jjs90.png",
  "[乐乐囍]": "jjs91.png",
  "[乐乐下跪]": "jjs92.png",
  "[乐乐嘘嘘]": "jjs93.png",
  "[新乐乐OK]": "jjs94.png",
  "[乐乐知错]": "jjs95.png",
  "[乐乐羡慕]": "jjs96.png",
  "[乐乐鼓励]": "jjs97.png",
  "[乐乐瞌睡]": "jjs98.png",
  "[乐乐承让]": "jjs99.png",
  "[乐乐惊讶]": "jjs100.png",
  "[乐乐注视]": "jjs101.png",
  "[乐乐庆祝]": "jjs102.png",
  "[乐乐迷惑]": "jjs103.png",
  "[乐乐奖杯]": "jjs104.png",
  "[乐乐真诚]": "jjs105.png",
  "[乐乐表演]": "jjs106.png",
  "[乐乐举手]": "jjs107.png",
  "[真诚]": "jjs108.png",
  "[问好]": "jjs109.png",
  "[不客气]": "jjs110.png",
  "[比爱心]": "jjs111.png",
  "[学区房]": "jjs112.png",
  "[别墅]": "jjs113.png",
  "[住宅]": "jjs114.png",
  "[新乐乐白眼]": "jjs115.png",
  "[乐乐666]": "jjs116.png",
  "[乐乐看看]": "jjs117.png",
  "[乐乐叹气]": "jjs118.png",
  "[乐乐苦涩]": "jjs119.png",
  "[乐乐裂开]": "jjs120.png"
}
let publicEmoji = {
  "[大笑]": "emoji_0.png",
  "[可爱]": "emoji_01.png",
  "[色]": "emoji_02.png",
  "[嘘]": "emoji_03.png",
  "[亲]": "emoji_04.png",
  "[呆]": "emoji_05.png",
  "[口水]": "emoji_06.png",
  "[汗]": "emoji_145.png",
  "[呲牙]": "emoji_07.png",
  "[鬼脸]": "emoji_08.png",
  "[害羞]": "emoji_09.png",
  "[偷笑]": "emoji_10.png",
  "[调皮]": "emoji_11.png",
  "[可怜]": "emoji_12.png",
  "[敲]": "emoji_13.png",
  "[惊讶]": "emoji_14.png",
  "[流感]": "emoji_15.png",
  "[委屈]": "emoji_16.png",
  "[流泪]": "emoji_17.png",
  "[嚎哭]": "emoji_18.png",
  "[惊恐]": "emoji_19.png",
  "[怒]": "emoji_20.png",
  "[酷]": "emoji_21.png",
  "[不说]": "emoji_22.png",
  "[鄙视]": "emoji_23.png",
  "[阿弥陀佛]": "emoji_24.png",
  "[奸笑]": "emoji_25.png",
  "[睡着]": "emoji_26.png",
  "[口罩]": "emoji_27.png",
  "[努力]": "emoji_28.png",
  "[抠鼻孔]": "emoji_29.png",
  "[疑问]": "emoji_30.png",
  "[怒骂]": "emoji_31.png",
  "[晕]": "emoji_32.png",
  "[呕吐]": "emoji_33.png",
  "[拜一拜]": "emoji_160.png",
  "[惊喜]": "emoji_161.png",
  "[流汗]": "emoji_162.png",
  "[卖萌]": "emoji_163.png",
  "[默契眨眼]": "emoji_164.png",
  "[烧香拜佛]": "emoji_165.png",
  "[晚安]": "emoji_166.png",
  "[强]": "emoji_34.png",
  "[弱]": "emoji_35.png",
  "[OK]": "emoji_36.png",
  "[拳头]": "emoji_37.png",
  "[胜利]": "emoji_38.png",
  "[鼓掌]": "emoji_39.png",
  "[握手]": "emoji_200.png",
  "[发怒]": "emoji_40.png",
  "[骷髅]": "emoji_41.png",
  "[便便]": "emoji_42.png",
  "[火]": "emoji_43.png",
  "[溜]": "emoji_44.png",
  "[爱心]": "emoji_45.png",
  "[心碎]": "emoji_46.png",
  "[钟情]": "emoji_47.png",
  "[唇]": "emoji_48.png",
  "[戒指]": "emoji_49.png",
  "[钻石]": "emoji_50.png",
  "[太阳]": "emoji_51.png",
  "[有时晴]": "emoji_52.png",
  "[多云]": "emoji_53.png",
  "[雷]": "emoji_54.png",
  "[雨]": "emoji_55.png",
  "[雪花]": "emoji_56.png",
  "[爱人]": "emoji_57.png",
  "[帽子]": "emoji_58.png",
  "[皇冠]": "emoji_59.png",
  "[篮球]": "emoji_60.png",
  "[足球]": "emoji_61.png",
  "[垒球]": "emoji_62.png",
  "[网球]": "emoji_63.png",
  "[台球]": "emoji_64.png",
  "[咖啡]": "emoji_65.png",
  "[啤酒]": "emoji_66.png",
  "[干杯]": "emoji_67.png",
  "[柠檬汁]": "emoji_68.png",
  "[餐具]": "emoji_69.png",
  "[汉堡]": "emoji_70.png",
  "[鸡腿]": "emoji_71.png",
  "[面条]": "emoji_72.png",
  "[冰淇淋]": "emoji_73.png",
  "[沙冰]": "emoji_74.png",
  "[生日蛋糕]": "emoji_75.png",
  "[蛋糕]": "emoji_76.png",
  "[糖果]": "emoji_77.png",
  "[葡萄]": "emoji_78.png",
  "[西瓜]": "emoji_79.png",
  "[光碟]": "emoji_80.png",
  "[手机]": "emoji_81.png",
  "[电话]": "emoji_82.png",
  "[电视]": "emoji_83.png",
  "[声音开启]": "emoji_84.png",
  "[声音关闭]": "emoji_85.png",
  "[铃铛]": "emoji_86.png",
  "[锁头]": "emoji_87.png",
  "[放大镜]": "emoji_88.png",
  "[灯泡]": "emoji_89.png",
  "[锤头]": "emoji_90.png",
  "[烟]": "emoji_91.png",
  "[炸弹]": "emoji_92.png",
  "[枪]": "emoji_93.png",
  "[刀]": "emoji_94.png",
  "[药]": "emoji_95.png",
  "[打针]": "emoji_96.png",
  "[钱袋]": "emoji_97.png",
  "[钞票]": "emoji_98.png",
  "[银行卡]": "emoji_99.png",
  "[手柄]": "emoji_100.png",
  "[麻将]": "emoji_101.png",
  "[调色板]": "emoji_102.png",
  "[电影]": "emoji_103.png",
  "[麦克风]": "emoji_104.png",
  "[耳机]": "emoji_105.png",
  "[音乐]": "emoji_106.png",
  "[吉他]": "emoji_107.png",
  "[火箭]": "emoji_108.png",
  "[飞机]": "emoji_109.png",
  "[火车]": "emoji_110.png",
  "[公交]": "emoji_111.png",
  "[轿车]": "emoji_112.png",
  "[出租车]": "emoji_113.png",
  "[警车]": "emoji_114.png",
  "[自行车]": "emoji_115.png"
}
let publicEmojiList = {
  jjs_emoji: [],
  emoji: [],
  le: [],
  xxy: [],
  lt: [],
  xl: []
};

// 设置表情列表
for (let key in publicEmojiList) {
  if (key == "jjs_emoji") {
    for (let key1 in publicJjEmoji) {
      let emojiName = publicJjEmoji[key1].split(".")[0];
      publicEmojiList.jjs_emoji.push({key: key1, value: emojiName, ext: ".png"});
    }
  } else if (key == "emoji") {
    for (let key1 in publicEmoji) {
      let emojiName = publicEmoji[key1].split(".")[0];
      publicEmojiList.emoji.push({key: key1, value: emojiName, ext: ".png"});
    }
  } else {
    // 默认为jjs的表情个数
    let emojiIndex = 142;
    let emojiExt = ".png";
    if (key == "jjs") {
      emojiExt = ".gif";
    } else if (key == "le") {
      emojiIndex = 42;
    } else if (key == "ajmd") {
      emojiIndex = 48;
    } else if (key == "xxy") {
      emojiIndex = 40;
    } else if (key == "lt") {
      emojiIndex = 20;
    } else if (key == "xl") {
      emojiIndex = 20;
      emojiExt = ".gif";
    }
    for (let i = 1; i <= emojiIndex; i++) {
      let emojiName = key;
      if (key == "le") {
        emojiName += "_";
      }
      if (key == "xl") {
        emojiName += i;
      } else {
        if (i < 10) {
          emojiName += "00" + i;
        } else if (i < 100) {
          emojiName += "0" + i;
        } else {
          emojiName += i;
        }
      }
      publicEmojiList[key].push({key: "[表情]", value: emojiName, ext: emojiExt});
    }
  }
}

// 解析表情
function buildEmoji(text) {
  text = (text || "").replace(/\[([^\]\[]*)\]/g, (item) => {
    item = getOtherEmojiName(item);
    if (publicJjEmoji[item] || publicEmoji[item]) {
      let pathName = "jjs_emoji";
      if (publicEmoji[item]) {
        pathName = "emoji";
      }
      return `<img class="im-emoji" src="/img/emoji/${pathName}/${publicJjEmoji[item] || publicEmoji[item]}" data-text="${item}" alt="">`;
    } else {
      return item;
    }
  });
  return text;
}

// 获取表情
function getEmojiList(type) {
  if (type == 1) {
    // 收藏只需要前两个表情
    return {
      jjs_emoji: publicEmojiList.jjs_emoji,
      emoji: publicEmojiList.emoji,
    }
  }
  return publicEmojiList;
}

// 表情地址获取表情名
function getEmojiText(name) {
  let result = "";
  if (/jjs/.test(name)) {
    for (let key in publicJjEmoji) {
      if (publicJjEmoji[key] == name) {
        result = key;
        break;
      }
    }
  } else if (/emoji_/.test(name)) {
    for (let key in publicEmoji) {
      if (publicEmoji[key] == name) {
        result = key;
        break;
      }
    }
  }
  return result;
}

// 表情名获取表情文件名
function getEmojiName(name) {
  name = getOtherEmojiName(name);
  return publicJjEmoji[name] || publicEmoji[name];
}

// 表情改名兼容
function getOtherEmojiName(name) {
  switch (name) {
    case "[乐乐敲你]":
      name = "[乐乐敲打]"
      break;
  }
  return name;
}