<!DOCTYPE html>
<html lang="">
<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" href="./favicon.ico">
  <title>乐聊</title>
</head>
<body>
<div id="app"></div>
<% if (process.env.NODE_ENV == 'development') { %>
<script src="./js/public.js"></script>
<% } %>
<script>
  window.ondragover = function (e) {
    e.preventDefault();
    return false;
  }
  window.ondrop = function (e) {
    e.preventDefault();
    return false;
  }
  window.mainNW = window.global.mainNW;
  window.remote = window.global.mainWin.window.nw;
  if (nw.process.env.NODE_ENV != "development") {
    nw.Window.get().evalNWBin(null, "js/public.bin");

    nw.Window.get().evalNWBin(null, "netDetect.bin");
    nw.Window.get().evalNWBin(null, "chunk-common.bin");
    nw.Window.get().evalNWBin(null, "chunk-iconv.bin");
  }
</script>
</body>
</html>
